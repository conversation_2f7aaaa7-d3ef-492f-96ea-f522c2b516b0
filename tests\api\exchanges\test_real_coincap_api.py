#!/usr/bin/env python3
"""
Test Real CoinCap API Integration
This script tests the actual CoinCap API connection using the MCP server
"""

import asyncio
import logging
import requests
import json

# CoinCap API base URL
COINCAP_BASE_URL = "https://api.coincap.io/v2"

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_crypto_price(symbol: str):
    """Get cryptocurrency price from CoinCap API"""
    # First try to get all assets to find the correct ID
    if symbol.lower() == "bitcoin":
        asset_id = "bitcoin"
    elif symbol.lower() == "ethereum":
        asset_id = "ethereum"
    else:
        asset_id = symbol.lower()
    
    # Try with various symbol formats
    possible_ids = [asset_id, symbol.upper(), symbol.lower()]
    
    for test_id in possible_ids:
        try:
            response = requests.get(f"{COINCAP_BASE_URL}/assets/{test_id}")
            if response.status_code == 200:
                return response.json()['data']
        except:
            continue
    
    # If direct lookup fails, search all assets
    response = requests.get(f"{COINCAP_BASE_URL}/assets")
    response.raise_for_status()
    assets = response.json()['data']
    
    for asset in assets:
        if (asset['symbol'].lower() == symbol.lower() or 
            asset['name'].lower() == symbol.lower() or
            asset['id'].lower() == symbol.lower()):
            return asset
    
    raise ValueError(f"Asset {symbol} not found")

def get_crypto_markets(symbol: str):
    """Get cryptocurrency market data from CoinCap API"""
    response = requests.get(f"{COINCAP_BASE_URL}/assets/{symbol}/markets")
    response.raise_for_status()
    return response.json()['data']

def get_crypto_history(symbol: str, interval: str = "h1"):
    """Get cryptocurrency historical data from CoinCap API"""
    response = requests.get(f"{COINCAP_BASE_URL}/assets/{symbol}/history?interval={interval}")
    response.raise_for_status()
    return response.json()['data']

async def test_real_coincap_api():
    """Test real CoinCap API connection and data retrieval"""
    print("=" * 60)
    print("TESTING REAL COINCAP API CONNECTION")
    print("=" * 60)
    
    try:
        # Test 1: Get current price for Bitcoin
        print("\n--- Test 1: Bitcoin Current Price ---")
        btc_price = get_crypto_price("bitcoin")
        print(f"✓ Bitcoin price data retrieved")
        print(f"  Current price: ${float(btc_price.get('priceUsd', 0)):,.2f}")
        print(f"  24h change: {float(btc_price.get('changePercent24Hr', 0)):.2f}%")
        print(f"  Market cap: ${float(btc_price.get('marketCapUsd', 0)):,.0f}")
        print(f"  Volume 24h: ${float(btc_price.get('volumeUsd24Hr', 0)):,.0f}")
        
        # Test 2: Get current price for Ethereum  
        print("\n--- Test 2: Ethereum Current Price ---")
        eth_price = get_crypto_price("ethereum")
        print(f"✓ Ethereum price data retrieved")
        print(f"  Current price: ${float(eth_price.get('priceUsd', 0)):,.2f}")
        print(f"  24h change: {float(eth_price.get('changePercent24Hr', 0)):.2f}%")
        print(f"  Market cap: ${float(eth_price.get('marketCapUsd', 0)):,.0f}")
        
        # Test 3: Get market analysis for Bitcoin
        print("\n--- Test 3: Bitcoin Market Analysis ---")
        btc_markets = get_crypto_markets("bitcoin")
        print(f"✓ Bitcoin market analysis retrieved")
        if btc_markets:
            exchanges = btc_markets[:3]  # Show top 3 exchanges
            print(f"  Top exchanges ({len(btc_markets)} total):")
            for market in exchanges:
                volume = float(market.get('volumeUsd24Hr', 0))
                print(f"    {market.get('exchangeId', 'Unknown')}: ${volume:,.0f}")
        
        # Test 4: Get historical analysis for Bitcoin
        print("\n--- Test 4: Bitcoin Historical Analysis ---")
        btc_history = get_crypto_history("bitcoin", "h1")
        print(f"✓ Bitcoin historical analysis retrieved")
        if btc_history:
            print(f"  Historical data points: {len(btc_history)}")
            if btc_history:
                latest = btc_history[-1]
                earliest = btc_history[0]
                latest_price = float(latest.get('priceUsd', 0))
                earliest_price = float(earliest.get('priceUsd', 0))
                change = ((latest_price - earliest_price) / earliest_price) * 100
                print(f"  Recent price change: {change:.2f}%")
                print(f"  Start: ${earliest_price:,.2f}")
                print(f"  End: ${latest_price:,.2f}")
        
        # Test 5: Test multiple cryptocurrency prices
        print("\n--- Test 5: Multiple Cryptocurrency Prices ---")
        crypto_symbols = ["bitcoin", "ethereum", "cardano", "solana", "avalanche-2"]
        crypto_data = {}
        
        for symbol in crypto_symbols:
            try:
                data = get_crypto_price(symbol)
                crypto_data[symbol] = data
                price = float(data.get('priceUsd', 0))
                change = float(data.get('changePercent24Hr', 0))
                name = data.get('name', symbol.title())
                print(f"  {name}: ${price:,.4f} ({change:+.2f}%)")
            except Exception as e:
                print(f"  {symbol}: Error - {e}")
        
        print(f"✓ Retrieved data for {len(crypto_data)} cryptocurrencies")
        
        print("\n" + "=" * 60)
        print("✅ ALL COINCAP API TESTS PASSED")
        print("Real CoinCap API is working correctly!")
        print("CoinCap provides excellent cryptocurrency market data.")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ CoinCap API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_real_coincap_api())
    exit(0 if success else 1)