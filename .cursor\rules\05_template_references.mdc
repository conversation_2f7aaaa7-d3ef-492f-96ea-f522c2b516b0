---
description: 
globs: 
alwaysApply: true
---

# <PERSON> AI Assistant - Standard Templates Reference
# Version 2.0

This document lists the standard templates used for project documentation, context management, and planning. All templates are located in the `C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/` directory (or their corresponding location in the staging directory during setup).

## I. Memory Bank & Context Templates:

1.  **`product_context_template.md`**:
    *   **Purpose**: To capture essential information about the product being built, its goals, target users, and key features.
    *   **Location**: `.cursor/templates/product_context_template.md`

2.  **`system_patterns_template.md`**:
    *   **Purpose**: To document established system-wide design patterns, architectural choices, and common solutions used within the project.
    *   **Location**: `.cursor/templates/system_patterns_template.md`

3.  **`tech_context_template.md`**:
    *   **Purpose**: To outline the technology stack, development environment, tools, libraries, and versions used in the project.
    *   **Location**: `.cursor/templates/tech_context_template.md`

4.  **`active_context_template.md`**:
    *   **Purpose**: To maintain a focused, short-term snapshot of the current task, objectives, recent findings, and immediate next steps.
    *   **Location**: `.cursor/templates/active_context_template.md`

5.  **`progress_template.md`**:
    *   **Purpose**: To track overall project progress, completed milestones, ongoing tasks, and future roadmap items.
    *   **Location**: `.cursor/templates/progress_template.md`

6.  **`ai_context_snapshot_template.md`**:
    *   **Purpose**: A template for Cascade to structure its understanding of the current project state for long-term memory or session handoff.
    *   **Location**: `.cursor/templates/ai_context_snapshot_template.md`

7.  **`personal_memory_template.md`**:
    *   **Purpose**: A template for the USER to create personal notes or reminders for Cascade that don't fit into other structured documents.
    *   **Location**: `.cursor/templates/personal_memory_template.md`

## II. Planning & Execution Templates:

8.  **`implementation_checklist_template.md`**:
    *   **Purpose**: A standard format for the detailed, sequential checklist generated in RIPER-5 PLAN mode before entering EXECUTE mode.
    *   **Location**: `.cursor/templates/implementation_checklist_template.md`

# (Refer to these templates when creating or updating project documentation.)
