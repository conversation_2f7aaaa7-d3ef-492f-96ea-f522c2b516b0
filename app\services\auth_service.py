from passlib.context import <PERSON><PERSON><PERSON>ontex<PERSON>
from pydantic import BaseModel, ConfigD<PERSON>
from typing import Optional, List

# Minimal User models defined here since app/models was removed
class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class UserInDB(User):
    hashed_password: str
    roles: Optional[List[str]] = []

# Create a password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# In-memory user database for testing (would be replaced with actual database)
fake_users_db = {
    "admin": {
        "username": "admin",
        "full_name": "Admin User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("admin123"),
        "is_active": True,
        "roles": ["admin"]
    },
    "user": {
        "username": "user",
        "full_name": "Test User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("user123"),
        "is_active": True,
        "roles": ["user"]
    }
}

def verify_password(plain_password, hashed_password):
    """
    Verify a password against a hash
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    """
    Generate a hash from a password
    """
    return pwd_context.hash(password)

def get_user(db, username: str):
    """
    Get a user from the database by username
    """
    if username in db:
        user_dict = db[username]
        return UserInDB(**user_dict)

def authenticate_user(username: str, password: str):
    """
    Authenticate a user by username and password
    """
    user = get_user(fake_users_db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user