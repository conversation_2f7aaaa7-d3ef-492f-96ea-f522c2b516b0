# Epics, Stories, and Tickets: Crypto_App_V2

## Epics

### Epic 1: Modularize and Refactor Core Trading Logic
- Modularize large files (e.g., execution_service.py, strategy_selector.py, binance_client.py)
- Ensure maintainability and code clarity

### Epic 2: Enhance ML Weight Optimization and Integration
- Integrate ML weight optimizer with strategy selector
- Implement automated ML model training and hyperparameter optimization
- Add ML controls to dashboard

### Epic 3: Improve Testing and Error Handling
- Expand unit and integration tests (real Binance Testnet data)
- Address concurrency and orphaned order edge cases
- Improve error handling and logging

### Epic 4: Frontend Real-Time Enhancements
- Enhance dashboard with real-time trade state, prices, and trade visualization
- Improve UI/UX and error handling

### Epic 5: MCP Server Integration and Automation
- Ensure all required MCP servers are installed, configured, and started via scripts
- Integrate Playwright MCP for browser/E2E tests
- Integrate Vega-Lite MCP for visualization

---

## Stories

#### Story 1.1: As a developer, I want execution_service.py modularized so that the codebase is easier to maintain and extend.
#### Story 1.2: As a developer, I want strategy_selector.py and binance_client.py modularized for clarity and testability.
#### Story 2.1: As a user, I want ML weight optimization to dynamically adjust strategy selection for better trading performance.
#### Story 2.2: As a user, I want to control ML features from the dashboard.
#### Story 3.1: As a developer, I want comprehensive tests for SL/TP monitoring and reconciliation.
#### Story 3.2: As a developer, I want robust error handling for API, concurrency, and orphaned orders.
#### Story 4.1: As a user, I want the dashboard to show real-time trade state and prices.
#### Story 4.2: As a user, I want a user-friendly and error-resilient UI.
#### Story 5.1: As a developer, I want all MCP servers to be managed via scripts and integrated into the workflow.
#### Story 5.2: As a developer, I want Playwright and Vega-Lite MCPs tested and documented.

---

## Tickets

- [ ] Refactor execution_service.py into smaller modules
- [ ] Modularize strategy_selector.py and binance_client.py
- [ ] Integrate ML weight optimizer with strategy selector
- [ ] Implement automated ML model training (interval-based)
- [ ] Add ML controls to dashboard UI
- [ ] Expand unit/integration tests for SL/TP monitoring
- [ ] Address concurrency and orphaned order edge cases
- [ ] Improve error handling and logging throughout codebase
- [ ] Enhance dashboard with real-time trade state and price updates
- [ ] Improve frontend UI/UX and error handling
- [ ] Ensure all required MCP servers are installed and configured
- [ ] Update start_mcp_servers.ps1 to manage all MCPs
- [ ] Integrate Playwright MCP for browser/E2E tests
- [ ] Integrate Vega-Lite MCP for visualization tests
- [ ] Remove unused code/configs (e.g., app_config.py, config_loader.py)
- [ ] Keep all files under 500 lines and modularize as needed
- [ ] Document MCP server integration and usage 