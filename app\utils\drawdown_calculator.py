"""
Advanced Drawdown Calculator for Real-time Drawdown Monitoring
Implements comprehensive drawdown analysis, underwater curves, and recovery tracking
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
import redis.asyncio as redis
import logging

logger = logging.getLogger(__name__)

@dataclass
class DrawdownMetrics:
    """Comprehensive drawdown metrics"""
    current_drawdown: float
    max_drawdown: float
    max_drawdown_duration: int  # days
    current_drawdown_duration: int  # days
    recovery_factor: float
    avg_drawdown: float
    drawdown_frequency: float
    time_underwater: float  # percentage of time in drawdown
    ulcer_index: float
    pain_index: float
    sterling_ratio: float
    burke_ratio: float
    max_drawdown_start: Optional[datetime]
    max_drawdown_end: Optional[datetime]
    current_drawdown_start: Optional[datetime]
    timestamp: datetime

@dataclass
class DrawdownPeriod:
    """Individual drawdown period"""
    start_date: datetime
    end_date: Optional[datetime]
    start_value: float
    trough_value: float
    end_value: Optional[float]
    peak_to_trough: float
    duration_days: int
    recovery_days: Optional[int]
    is_active: bool

@dataclass
class UnderwaterCurve:
    """Underwater curve data for visualization"""
    timestamps: List[str]
    drawdown_values: List[float]
    cumulative_returns: List[float]
    running_peaks: List[float]
    recovery_periods: List[bool]

class DrawdownCalculator:
    """
    Advanced drawdown calculator with real-time monitoring and analysis
    """
    
    def __init__(
        self,
        redis_client: redis.Redis,
        drawdown_threshold: float = 0.01,  # 1% minimum drawdown to track
        recovery_threshold: float = 0.99   # 99% recovery to consider drawdown ended
    ):
        self.redis = redis_client
        self.drawdown_threshold = drawdown_threshold
        self.recovery_threshold = recovery_threshold
        
        # Cache keys
        self.DRAWDOWN_CACHE = "drawdown:current_metrics"
        self.HISTORY_CACHE = "drawdown:history"
        self.PERIODS_CACHE = "drawdown:periods"
        self.UNDERWATER_CACHE = "drawdown:underwater_curve"
        
    async def calculate_drawdown_metrics(
        self,
        returns: List[float],
        timestamps: Optional[List[datetime]] = None
    ) -> DrawdownMetrics:
        """
        Calculate comprehensive drawdown metrics
        """
        try:
            # Check cache first
            cache_key = f"{self.DRAWDOWN_CACHE}:{hash(str(returns[-10:]))}"
            cached_metrics = await self.redis.get(cache_key)
            
            if cached_metrics:
                cached_data = json.loads(cached_metrics)
                cache_time = datetime.fromisoformat(cached_data['timestamp'])
                
                # Use cached if less than 5 minutes old
                if datetime.now() - cache_time < timedelta(minutes=5):
                    return self._deserialize_drawdown_metrics(cached_data)
            
            if len(returns) < 2:
                return self._get_default_drawdown_metrics()
            
            # Generate timestamps if not provided
            if timestamps is None:
                timestamps = [datetime.now() - timedelta(days=len(returns)-i-1) for i in range(len(returns))]
            
            returns_array = np.array(returns)
            
            # Calculate cumulative returns and running maximum
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            
            # Calculate drawdowns
            drawdown_series = (cumulative_returns - running_max) / running_max
            
            # Current drawdown
            current_drawdown = drawdown_series[-1]
            
            # Maximum drawdown
            max_drawdown = np.min(drawdown_series)
            
            # Find max drawdown period
            max_dd_idx = np.argmin(drawdown_series)
            max_dd_start_idx = self._find_drawdown_start(drawdown_series, max_dd_idx)
            max_dd_end_idx = self._find_drawdown_end(drawdown_series, max_dd_idx, running_max)
            
            max_drawdown_duration = max_dd_end_idx - max_dd_start_idx if max_dd_end_idx else len(drawdown_series) - max_dd_start_idx
            
            # Current drawdown duration
            current_dd_start_idx = self._find_current_drawdown_start(drawdown_series)
            current_drawdown_duration = len(drawdown_series) - current_dd_start_idx if current_dd_start_idx is not None else 0
            
            # Recovery factor
            total_return = cumulative_returns[-1] - 1
            recovery_factor = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Average drawdown and frequency
            drawdown_periods = self._identify_drawdown_periods(drawdown_series, timestamps)
            avg_drawdown = np.mean([dd.peak_to_trough for dd in drawdown_periods]) if drawdown_periods else 0
            drawdown_frequency = len(drawdown_periods) / len(returns) * 252 if returns else 0  # Annualized
            
            # Time underwater (percentage of time in drawdown)
            underwater_days = np.sum(drawdown_series < -self.drawdown_threshold)
            time_underwater = underwater_days / len(drawdown_series) if drawdown_series.size > 0 else 0
            
            # Ulcer Index (downside risk measure)
            ulcer_index = np.sqrt(np.mean(drawdown_series ** 2)) * 100
            
            # Pain Index (average drawdown magnitude)
            pain_index = np.mean(np.abs(drawdown_series)) * 100
            
            # Sterling Ratio (return / max drawdown)
            annualized_return = (cumulative_returns[-1] ** (252 / len(returns))) - 1 if len(returns) > 0 else 0
            sterling_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Burke Ratio (return / sqrt(sum of squared drawdowns))
            burke_ratio = annualized_return / np.sqrt(np.sum(drawdown_series ** 2)) if np.sum(drawdown_series ** 2) > 0 else 0
            
            # Create timestamp objects
            max_dd_start = timestamps[max_dd_start_idx] if max_dd_start_idx < len(timestamps) else None
            max_dd_end = timestamps[max_dd_end_idx] if max_dd_end_idx and max_dd_end_idx < len(timestamps) else None
            current_dd_start = timestamps[current_dd_start_idx] if current_dd_start_idx is not None and current_dd_start_idx < len(timestamps) else None
            
            drawdown_metrics = DrawdownMetrics(
                current_drawdown=current_drawdown,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_drawdown_duration,
                current_drawdown_duration=current_drawdown_duration,
                recovery_factor=recovery_factor,
                avg_drawdown=avg_drawdown,
                drawdown_frequency=drawdown_frequency,
                time_underwater=time_underwater,
                ulcer_index=ulcer_index,
                pain_index=pain_index,
                sterling_ratio=sterling_ratio,
                burke_ratio=burke_ratio,
                max_drawdown_start=max_dd_start,
                max_drawdown_end=max_dd_end,
                current_drawdown_start=current_dd_start,
                timestamp=datetime.now()
            )
            
            # Cache the results
            await self._cache_drawdown_metrics(cache_key, drawdown_metrics)
            
            return drawdown_metrics
            
        except Exception as e:
            logger.error(f"Error calculating drawdown metrics: {e}")
            return self._get_default_drawdown_metrics()
    
    async def get_underwater_curve(
        self,
        returns: List[float],
        timestamps: Optional[List[datetime]] = None
    ) -> UnderwaterCurve:
        """
        Generate underwater curve data for visualization
        """
        try:
            if len(returns) < 2:
                return UnderwaterCurve([], [], [], [], [])
            
            # Generate timestamps if not provided
            if timestamps is None:
                timestamps = [datetime.now() - timedelta(days=len(returns)-i-1) for i in range(len(returns))]
            
            returns_array = np.array(returns)
            
            # Calculate cumulative returns and running maximum
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            
            # Calculate drawdowns
            drawdown_values = (cumulative_returns - running_max) / running_max
            
            # Identify recovery periods
            recovery_periods = []
            in_drawdown = False
            
            for i, dd in enumerate(drawdown_values):
                if dd < -self.drawdown_threshold:
                    in_drawdown = True
                    recovery_periods.append(False)
                elif in_drawdown and dd > -self.drawdown_threshold * self.recovery_threshold:
                    in_drawdown = False
                    recovery_periods.append(True)
                else:
                    recovery_periods.append(in_drawdown)
            
            return UnderwaterCurve(
                timestamps=[ts.isoformat() for ts in timestamps],
                drawdown_values=drawdown_values.tolist(),
                cumulative_returns=cumulative_returns.tolist(),
                running_peaks=running_max.tolist(),
                recovery_periods=recovery_periods
            )
            
        except Exception as e:
            logger.error(f"Error generating underwater curve: {e}")
            return UnderwaterCurve([], [], [], [], [])
    
    async def monitor_real_time_drawdown(
        self,
        new_return: float,
        portfolio_value: float
    ) -> Dict[str, any]:
        """
        Monitor real-time drawdown and trigger alerts if necessary
        """
        try:
            # Get current drawdown state
            current_state = await self.redis.get("drawdown:real_time_state")
            
            if current_state:
                state = json.loads(current_state)
                running_peak = state.get("running_peak", portfolio_value)
                current_dd_start = state.get("current_drawdown_start")
                alerts = state.get("alerts", [])
            else:
                running_peak = portfolio_value
                current_dd_start = None
                alerts = []
            
            # Update running peak
            if portfolio_value > running_peak:
                running_peak = portfolio_value
                current_dd_start = None  # End of drawdown
            
            # Calculate current drawdown
            current_drawdown = (portfolio_value - running_peak) / running_peak if running_peak > 0 else 0
            
            # Check if we're starting a new drawdown
            if current_drawdown < -self.drawdown_threshold and current_dd_start is None:
                current_dd_start = datetime.now().isoformat()
            
            # Generate alerts
            new_alerts = []
            
            if current_drawdown < -0.05:  # 5% drawdown alert
                new_alerts.append({
                    "level": "warning",
                    "message": f"Portfolio drawdown reached {current_drawdown:.2%}",
                    "timestamp": datetime.now().isoformat()
                })
            
            if current_drawdown < -0.10:  # 10% drawdown alert
                new_alerts.append({
                    "level": "critical",
                    "message": f"Significant drawdown: {current_drawdown:.2%}",
                    "timestamp": datetime.now().isoformat()
                })
            
            # Update state
            new_state = {
                "running_peak": running_peak,
                "current_drawdown": current_drawdown,
                "current_drawdown_start": current_dd_start,
                "portfolio_value": portfolio_value,
                "alerts": alerts + new_alerts,
                "last_updated": datetime.now().isoformat()
            }
            
            await self.redis.setex("drawdown:real_time_state", 3600, json.dumps(new_state))
            
            return {
                "current_drawdown": current_drawdown,
                "running_peak": running_peak,
                "in_drawdown": current_dd_start is not None,
                "drawdown_duration_hours": (datetime.now() - datetime.fromisoformat(current_dd_start)).total_seconds() / 3600 if current_dd_start else 0,
                "new_alerts": new_alerts,
                "risk_level": self._assess_risk_level(current_drawdown),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error monitoring real-time drawdown: {e}")
            return {
                "current_drawdown": 0.0,
                "running_peak": portfolio_value,
                "in_drawdown": False,
                "drawdown_duration_hours": 0,
                "new_alerts": [],
                "risk_level": "low",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def _identify_drawdown_periods(
        self,
        drawdown_series: np.ndarray,
        timestamps: List[datetime]
    ) -> List[DrawdownPeriod]:
        """
        Identify individual drawdown periods
        """
        periods = []
        in_drawdown = False
        start_idx = None
        trough_idx = None
        trough_value = 0
        
        for i, dd in enumerate(drawdown_series):
            if not in_drawdown and dd < -self.drawdown_threshold:
                # Start of new drawdown
                in_drawdown = True
                start_idx = i
                trough_idx = i
                trough_value = dd
            elif in_drawdown:
                # Update trough if deeper
                if dd < trough_value:
                    trough_idx = i
                    trough_value = dd
                
                # Check for end of drawdown
                if dd > -self.drawdown_threshold * self.recovery_threshold:
                    # End of drawdown
                    end_idx = i
                    
                    period = DrawdownPeriod(
                        start_date=timestamps[start_idx],
                        end_date=timestamps[end_idx],
                        start_value=1.0,  # Normalized
                        trough_value=1 + trough_value,
                        end_value=1 + dd,
                        peak_to_trough=abs(trough_value),
                        duration_days=end_idx - start_idx,
                        recovery_days=end_idx - trough_idx,
                        is_active=False
                    )
                    
                    periods.append(period)
                    in_drawdown = False
        
        # Handle ongoing drawdown
        if in_drawdown and start_idx is not None:
            period = DrawdownPeriod(
                start_date=timestamps[start_idx],
                end_date=None,
                start_value=1.0,
                trough_value=1 + trough_value,
                end_value=None,
                peak_to_trough=abs(trough_value),
                duration_days=len(drawdown_series) - start_idx,
                recovery_days=None,
                is_active=True
            )
            periods.append(period)
        
        return periods
    
    def _find_drawdown_start(self, drawdown_series: np.ndarray, trough_idx: int) -> int:
        """Find the start of a drawdown period"""
        for i in range(trough_idx, -1, -1):
            if drawdown_series[i] >= 0:
                return i + 1
        return 0
    
    def _find_drawdown_end(self, drawdown_series: np.ndarray, trough_idx: int, running_max: np.ndarray) -> Optional[int]:
        """Find the end of a drawdown period"""
        for i in range(trough_idx, len(drawdown_series)):
            if drawdown_series[i] >= -self.drawdown_threshold * self.recovery_threshold:
                return i
        return None
    
    def _find_current_drawdown_start(self, drawdown_series: np.ndarray) -> Optional[int]:
        """Find the start of the current drawdown"""
        if drawdown_series[-1] >= -self.drawdown_threshold:
            return None
        
        for i in range(len(drawdown_series) - 1, -1, -1):
            if drawdown_series[i] >= 0:
                return i + 1
        return 0
    
    def _assess_risk_level(self, current_drawdown: float) -> str:
        """Assess risk level based on current drawdown"""
        if current_drawdown >= -0.02:  # Less than 2%
            return "low"
        elif current_drawdown >= -0.05:  # 2-5%
            return "moderate"
        elif current_drawdown >= -0.10:  # 5-10%
            return "high"
        else:  # More than 10%
            return "critical"
    
    async def _cache_drawdown_metrics(self, cache_key: str, metrics: DrawdownMetrics):
        """Cache drawdown metrics"""
        try:
            cache_data = self._serialize_drawdown_metrics(metrics)
            await self.redis.setex(cache_key, 300, json.dumps(cache_data))  # 5 minutes
            
        except Exception as e:
            logger.error(f"Error caching drawdown metrics: {e}")
    
    def _serialize_drawdown_metrics(self, metrics: DrawdownMetrics) -> Dict:
        """Serialize drawdown metrics for caching"""
        data = asdict(metrics)
        
        # Convert datetime objects to ISO strings
        if data['max_drawdown_start']:
            data['max_drawdown_start'] = data['max_drawdown_start'].isoformat()
        if data['max_drawdown_end']:
            data['max_drawdown_end'] = data['max_drawdown_end'].isoformat()
        if data['current_drawdown_start']:
            data['current_drawdown_start'] = data['current_drawdown_start'].isoformat()
        
        data['timestamp'] = data['timestamp'].isoformat()
        
        return data
    
    def _deserialize_drawdown_metrics(self, data: Dict) -> DrawdownMetrics:
        """Deserialize drawdown metrics from cache"""
        # Convert ISO strings back to datetime objects
        if data['max_drawdown_start']:
            data['max_drawdown_start'] = datetime.fromisoformat(data['max_drawdown_start'])
        if data['max_drawdown_end']:
            data['max_drawdown_end'] = datetime.fromisoformat(data['max_drawdown_end'])
        if data['current_drawdown_start']:
            data['current_drawdown_start'] = datetime.fromisoformat(data['current_drawdown_start'])
        
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return DrawdownMetrics(**data)
    
    def _get_default_drawdown_metrics(self) -> DrawdownMetrics:
        """Return default drawdown metrics when calculation fails"""
        return DrawdownMetrics(
            current_drawdown=0.0,
            max_drawdown=0.0,
            max_drawdown_duration=0,
            current_drawdown_duration=0,
            recovery_factor=0.0,
            avg_drawdown=0.0,
            drawdown_frequency=0.0,
            time_underwater=0.0,
            ulcer_index=0.0,
            pain_index=0.0,
            sterling_ratio=0.0,
            burke_ratio=0.0,
            max_drawdown_start=None,
            max_drawdown_end=None,
            current_drawdown_start=None,
            timestamp=datetime.now()
        )
    
    async def generate_drawdown_report(
        self,
        returns: List[float],
        timestamps: Optional[List[datetime]] = None
    ) -> Dict[str, any]:
        """
        Generate comprehensive drawdown analysis report
        """
        try:
            # Calculate main drawdown metrics
            drawdown_metrics = await self.calculate_drawdown_metrics(returns, timestamps)
            
            # Generate underwater curve
            underwater_curve = await self.get_underwater_curve(returns, timestamps)
            
            # Identify all drawdown periods
            if timestamps is None:
                timestamps = [datetime.now() - timedelta(days=len(returns)-i-1) for i in range(len(returns))]
            
            returns_array = np.array(returns)
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown_series = (cumulative_returns - running_max) / running_max
            
            drawdown_periods = self._identify_drawdown_periods(drawdown_series, timestamps)
            
            # Create comprehensive report
            report = {
                "drawdown_summary": asdict(drawdown_metrics),
                "underwater_curve": asdict(underwater_curve),
                "drawdown_periods": [
                    {
                        "start_date": period.start_date.isoformat(),
                        "end_date": period.end_date.isoformat() if period.end_date else None,
                        "peak_to_trough": f"{period.peak_to_trough:.2%}",
                        "duration_days": period.duration_days,
                        "recovery_days": period.recovery_days,
                        "is_active": period.is_active
                    }
                    for period in drawdown_periods
                ],
                "risk_analysis": {
                    "current_risk_level": self._assess_risk_level(drawdown_metrics.current_drawdown),
                    "max_drawdown": f"{drawdown_metrics.max_drawdown:.2%}",
                    "time_underwater": f"{drawdown_metrics.time_underwater:.2%}",
                    "ulcer_index": f"{drawdown_metrics.ulcer_index:.2f}",
                    "pain_index": f"{drawdown_metrics.pain_index:.2f}"
                },
                "recovery_analysis": {
                    "recovery_factor": f"{drawdown_metrics.recovery_factor:.2f}",
                    "sterling_ratio": f"{drawdown_metrics.sterling_ratio:.2f}",
                    "burke_ratio": f"{drawdown_metrics.burke_ratio:.2f}",
                    "avg_drawdown": f"{drawdown_metrics.avg_drawdown:.2%}",
                    "drawdown_frequency": f"{drawdown_metrics.drawdown_frequency:.1f} per year"
                },
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "data_points": len(returns),
                    "analysis_period_days": len(returns),
                    "total_drawdown_periods": len(drawdown_periods),
                    "active_drawdowns": sum(1 for p in drawdown_periods if p.is_active)
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating drawdown report: {e}")
            return {
                "error": str(e),
                "generated_at": datetime.now().isoformat()
            }