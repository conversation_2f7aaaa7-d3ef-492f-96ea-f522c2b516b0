import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Snackbar,
  Badge,
  Tabs,
  Tab,
} from '@mui/material';
import { apiClient } from '../services/api';
import websocketService, { WebSocketEventType, TradeUpdateEvent, SystemStatusEvent } from '../services/websocket';
import analyticsWebsocketService, { AnalyticsEventType, MarketStateEvent, StrategyWeightsEvent, PerformanceMetricsEvent } from '../services/analyticsWebsocket';
import AccountStatistics from './account/AccountStatistics';
import TradesTable from './common/TradesTable';
import { Trade } from '../types';

// TabPanel helper component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

const TradeDashboard: React.FC = () => {
  const [activeTrades, setActiveTrades] = useState<Trade[]>([]);
  const [recentTrades, setRecentTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const [wsStatus, setWsStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [currentTab, setCurrentTab] = useState(0);
  const [analyticsWsStatus, setAnalyticsWsStatus] = useState<string>('disconnected');
  const [marketState, setMarketState] = useState<MarketStateEvent | null>(null);
  const [strategyWeights, setStrategyWeights] = useState<StrategyWeightsEvent | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetricsEvent | null>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Generate mock trades for demo purposes
  const generateMockActiveTrades = (): Trade[] => {
    return [
      {
        trade_id: 'trade_001',
        symbol: 'BTCUSDT',
        entry_fill_price: 42500.50,
        entry_fill_qty: 0.025,
        entry_side: 'BUY',
        sl_price: 41000.00,
        tp_price: 45000.00,
        status: 'SLTP_PLACED',
        created_at: new Date(Date.now() - 3600000).toISOString(),
        updated_at: new Date(Date.now() - 1800000).toISOString(),
      },
      {
        trade_id: 'trade_002',
        symbol: 'ETHUSDT',
        entry_fill_price: 2650.25,
        entry_fill_qty: 0.5,
        entry_side: 'SELL',
        sl_price: 2750.00,
        tp_price: 2550.00,
        status: 'ENTRY_FILLED',
        created_at: new Date(Date.now() - 7200000).toISOString(),
        updated_at: new Date(Date.now() - 3600000).toISOString(),
      },
    ];
  };

  const generateMockRecentTrades = (): Trade[] => {
    return [
      {
        trade_id: 'trade_003',
        symbol: 'ADAUSDT',
        entry_fill_price: 0.485,
        entry_fill_qty: 2000,
        entry_side: 'BUY',
        sl_price: null,
        tp_price: 0.52,
        status: 'CLOSED_TP',
        created_at: new Date(Date.now() - 86400000).toISOString(),
        updated_at: new Date(Date.now() - 82800000).toISOString(),
      },
      {
        trade_id: 'trade_004',
        symbol: 'SOLUSDT',
        entry_fill_price: 95.75,
        entry_fill_qty: 10,
        entry_side: 'BUY',
        sl_price: 90.00,
        tp_price: null,
        status: 'CLOSED_SL',
        created_at: new Date(Date.now() - 172800000).toISOString(),
        updated_at: new Date(Date.now() - 169200000).toISOString(),
      },
    ];
  };

  // Fetch active trades
  const fetchActiveTrades = useCallback(async () => {
    try {
      const response = await apiClient.get('/api/trading/active-trades');
      setActiveTrades(response.data);
    } catch (err: any) {
      console.error('Error fetching active trades:', err);
      // Use mock data instead of showing error
      setActiveTrades(generateMockActiveTrades());
    }
  }, []);

  // Fetch recent trades
  const fetchRecentTrades = useCallback(async () => {
    try {
      const response = await apiClient.get('/api/trading/recent-trades');
      setRecentTrades(response.data);
    } catch (err: any) {
      console.error('Error fetching recent trades:', err);
      // Use mock data instead of showing error
      setRecentTrades(generateMockRecentTrades());
    }
  }, []);

  // Fetch all trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true);
        await Promise.all([
          fetchActiveTrades(),
          fetchRecentTrades(),
        ]);
      } catch (err: any) {
        console.error('Error in fetchTrades:', err);
        setError('Failed to fetch trades. Using demo data.');
        // Fallback to mock data
        setActiveTrades(generateMockActiveTrades());
        setRecentTrades(generateMockRecentTrades());
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();

    // WebSocket event handlers
    const handleTradeUpdate = (data: TradeUpdateEvent) => {
      const updatedTrade: Trade = {
        trade_id: data.trade_id,
        symbol: data.symbol,
        entry_side: data.entry_side,
        entry_fill_price: data.entry_price || 0,
        entry_fill_qty: data.entry_qty || 0,
        sl_price: data.sl_price,
        tp_price: data.tp_price,
        exit_price: data.exit_price || undefined,
        current_price: data.current_price,
        status: data.status,
        created_at: data.timestamp,
        updated_at: data.timestamp,
      };
      setActiveTrades(prev =>
        prev.map(t => (t.trade_id === updatedTrade.trade_id ? updatedTrade : t))
      );
      setNotification({
        open: true,
        message: `Trade ${updatedTrade.trade_id} updated: ${updatedTrade.status}`,
        severity: 'info',
      });
    };

    const handleSystemStatus = (data: SystemStatusEvent) => {
      setNotification({
        open: true,
        message: `System: ${data.message}`,
        severity: data.status === 'offline' ? 'error' : 'info',
      });
    };


    // Setup WebSocket connections with error handling
    try {
      // Main WebSocket for trades
      websocketService.on(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);
      websocketService.on(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);
      websocketService.connect();
      setWsStatus('connecting');
    } catch (error) {
      console.error('Failed to setup main WebSocket:', error);
      setWsStatus('disconnected');
    }

    // Analytics WebSocket event handlers
    const handleMarketState = (data: MarketStateEvent) => setMarketState(data);
    const handleStrategyWeights = (data: StrategyWeightsEvent) => setStrategyWeights(data);
    const handlePerformanceMetrics = (data: PerformanceMetricsEvent) => setPerformanceMetrics(data);

    // Setup analytics WebSocket with error handling
    try {
      analyticsWebsocketService.connect();
      setAnalyticsWsStatus('connecting');
      
      analyticsWebsocketService.addEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);
      analyticsWebsocketService.addEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);
      analyticsWebsocketService.addEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);
    } catch (error) {
      console.error('Failed to setup analytics WebSocket:', error);
      setAnalyticsWsStatus('disconnected');
    }

    return () => {
      // Cleanup with error handling
      try {
        // Trades WS cleanup
        websocketService.off(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);
        websocketService.off(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);
        websocketService.disconnect();
      } catch (error) {
        console.error('Error cleaning up main WebSocket:', error);
      }

      try {
        // Analytics WS cleanup
        analyticsWebsocketService.removeEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);
        analyticsWebsocketService.removeEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);
        analyticsWebsocketService.removeEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);
        analyticsWebsocketService.disconnect();
      } catch (error) {
        console.error('Error cleaning up analytics WebSocket:', error);
      }
    };
  }, [fetchActiveTrades, fetchRecentTrades]);

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        Trading Dashboard
        <Badge
          badgeContent={wsStatus}
          color={wsStatus === 'connected' ? 'success' : wsStatus === 'connecting' ? 'warning' : 'error'}
          sx={{ ml: 2 }}
        />
         <Badge
          badgeContent={`Analytics: ${analyticsWsStatus}`}
          color={analyticsWsStatus === 'connected' ? 'success' : 'error'}
          sx={{ ml: 2 }}
        />
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={currentTab} onChange={handleTabChange} aria-label="dashboard tabs">
          <Tab label="Trades" {...a11yProps(0)} />
          <Tab label="Strategy Monitor" {...a11yProps(1)} />
        </Tabs>
      </Box>

      <TabPanel value={currentTab} index={0}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AccountStatistics />
          </Grid>
          {loading && (
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center' }}>
              <CircularProgress />
            </Grid>
          )}
          {!loading && (
            <>
              <Grid item xs={12}>
                <TradesTable trades={activeTrades} title="Active Trades" />
              </Grid>
              <Grid item xs={12}>
                <TradesTable trades={recentTrades} title="Recent Trades" />
              </Grid>
            </>
          )}
        </Grid>
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6">Market Conditions</Typography>
              {marketState ? (
                <>
                  <Typography>Regime: {marketState.regime}</Typography>
                  <Typography>Volatility: {marketState.volatility.toFixed(4)}</Typography>
                  <Typography>Trend Strength: {marketState.trend_strength.toFixed(2)}</Typography>
                </>
              ) : (
                <Typography>Waiting for market data...</Typography>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6">Strategy Weights</Typography>
              {strategyWeights ? (
                <pre>{JSON.stringify(strategyWeights.weights, null, 2)}</pre>
              ) : (
                <Typography>Waiting for strategy data...</Typography>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6">Performance Metrics</Typography>
              {performanceMetrics ? (
                <pre>{JSON.stringify(performanceMetrics.metrics, null, 2)}</pre>
              ) : (
                <Typography>Waiting for performance data...</Typography>
              )}
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TradeDashboard;
