---
description: 
globs: 
alwaysApply: true
---

# Cascade AI Assistant - Guiding Principles and Project Context
# Version 2.1

## I. Centralized Rule System

This project utilizes the `.cursor/rules/` directory as the central, authoritative source for all operational rules and guidelines for the AI assistant. All `.md` and `.mdc` files within this directory are loaded automatically to provide consistent, version-controlled instructions.

## II. Core Directives and High-Level Context

The file `00_core_directives.md` serves as the primary entry point for high-level project goals, core methodologies (like TDD), and essential protocols. It acts as the "executive summary" of the project's rules, containing the most critical, must-follow directives and pointers to more detailed rule files.

Key principles that would have previously been in a root `.windsurfrules` file should now be integrated into `00_core_directives.md` or a new, dedicated rule file within the `.cursor/rules/` directory.

## III. How the AI Uses the Rules
- **Comprehensive Context**: The AI assistant reads and adheres to all rules in the `.cursor/rules/` directory at the start of each session.
- **Hierarchical Structure**: While all rules are loaded, `00_core_directives.md` provides the foundational context, with other files offering detailed guidance on specific topics (e.g., memory management, development workflow).
- **Evolutionary System**: The USER is encouraged to update existing rules or add new ones as the project evolves and new key insights or directives emerge.

## IV. Relationship to Memory Bank
- The `.cursor/rules/` directory contains *explicit, persistent directives* and established project protocols.
- The `@memory-bank` is used for more granular, evolving context, including session notes, specific task details, code snippets, and broader knowledge accumulation.
- Both systems are crucial for providing the AI with a complete understanding of the project.

# (This document explains how the .cursor/rules/ directory and 00_core_directives.md function as the central guidance system for the AI.)
