#!/usr/bin/env python3
"""
Comprehensive Real API Integration Test
This script tests all working real API connections: Redis, Supabase, W&B
"""

import asyncio
import logging
import json
import time
from datetime import datetime
from app.services.mcp.real_redis_service import RealRedisService
import aiohttp
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env')
load_dotenv('.env.mcp')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_comprehensive_real_apis():
    """Test all real API integrations comprehensively"""
    print("=" * 80)
    print("COMPREHENSIVE REAL API INTEGRATION TEST")
    print("Testing: Redis, Supabase (existing tables), W&B")
    print("=" * 80)
    
    all_tests_passed = True
    
    try:
        # ===== REDIS TESTING =====
        print("\n🔴 REDIS API TESTING")
        print("-" * 50)
        
        redis_service = RealRedisService()
        await redis_service.connect()
        print("✓ Redis connected successfully")
        
        # Test Redis operations
        test_key = f"real_api_test_{int(time.time())}"
        test_data = {
            "strategy": "ensemble",
            "confidence": 0.87,
            "timestamp": datetime.now().isoformat(),
            "test": True
        }
        
        # Store data
        await redis_service.setex(test_key, 300, json.dumps(test_data))
        print(f"✓ Stored test data in Redis: {test_key}")
        
        # Retrieve data
        retrieved = await redis_service.get(test_key)
        if retrieved:
            parsed_data = json.loads(retrieved)
            print(f"✓ Retrieved and parsed data: strategy={parsed_data['strategy']}")
        
        # Get Redis performance stats
        redis_stats = redis_service.get_performance_stats()
        print(f"✓ Redis performance: {redis_stats['avg_latency']:.2f}ms avg latency")
        
        await redis_service.disconnect()
        print("✓ Redis connection closed")
        
        # ===== SUPABASE TESTING =====
        print("\n🟦 SUPABASE API TESTING (EXISTING TABLES)")
        print("-" * 50)
        
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_KEY')
        
        if not supabase_url or not supabase_key:
            print("✗ Supabase credentials not found")
            all_tests_passed = False
        else:
            api_url = f"{supabase_url}/rest/v1"
            headers = {
                'apikey': supabase_key,
                'Authorization': f'Bearer {supabase_key}',
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
            }
            
            async with aiohttp.ClientSession() as session:
                # Test 1: Store in portfolio_metrics table
                portfolio_data = {
                    'total_pnl': 5250.75,
                    'sharpe_ratio': 1.85,
                    'max_drawdown': -0.023,
                    'win_rate': 0.689,
                    'strategy_contributions': {
                        'technical_analysis': 0.25,
                        'grid_strategy': 0.25,
                        'ml_strategy': 0.25,
                        'ensemble': 0.25
                    },
                    'correlation_matrix': {
                        'ta_grid': 0.65,
                        'ta_ml': 0.72,
                        'grid_ml': 0.58
                    }
                }
                
                async with session.post(
                    f"{api_url}/portfolio_metrics",
                    headers=headers,
                    json=portfolio_data
                ) as response:
                    if response.status in [200, 201]:
                        result = await response.json()
                        print(f"✓ Portfolio metrics stored: PnL=${portfolio_data['total_pnl']:,.2f}")
                    else:
                        error_text = await response.text()
                        print(f"✗ Portfolio metrics failed: {response.status} - {error_text}")
                        all_tests_passed = False
                
                # Test 2: Store in strategy_performance table
                strategy_data = {
                    'strategy_name': 'real_api_ensemble_test',
                    'pnl': 1250.25,
                    'return_pct': 0.052,
                    'trades_count': 45,
                    'win_rate': 0.689,
                    'confidence_score': 0.87
                }
                
                async with session.post(
                    f"{api_url}/strategy_performance",
                    headers=headers,
                    json=strategy_data
                ) as response:
                    if response.status in [200, 201]:
                        print(f"✓ Strategy performance stored: {strategy_data['strategy_name']}")
                    else:
                        error_text = await response.text()
                        print(f"✗ Strategy performance failed: {response.status} - {error_text}")
                        all_tests_passed = False
                
                # Test 3: Store in trades table
                trade_data = {
                    'strategy_name': 'real_api_test',
                    'symbol': 'BTCUSDT',
                    'action': 'BUY',
                    'quantity': 0.001,
                    'price': 104900.0,
                    'pnl': 52.45,
                    'return_pct': 0.05,
                    'fees': 1.25,
                    'confidence': 0.87,
                    'weight_used': 0.25,
                    'position_size': 104.90,
                    'market_conditions': {
                        'volatility': 0.025,
                        'trend': 'bullish',
                        'volume': 'high'
                    }
                }
                
                async with session.post(
                    f"{api_url}/trades",
                    headers=headers,
                    json=trade_data
                ) as response:
                    if response.status in [200, 201]:
                        print(f"✓ Trade stored: {trade_data['action']} {trade_data['quantity']} {trade_data['symbol']}")
                    else:
                        error_text = await response.text()
                        print(f"✗ Trade storage failed: {response.status} - {error_text}")
                        all_tests_passed = False
                
                # Test 4: Query recent data
                async with session.get(
                    f"{api_url}/trades?select=*&limit=3&order=created_at.desc",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        trades = await response.json()
                        print(f"✓ Retrieved {len(trades)} recent trades")
                    else:
                        print(f"✗ Trade query failed: {response.status}")
                        all_tests_passed = False
        
        # ===== W&B TESTING =====
        print("\n🟡 WEIGHTS & BIASES API TESTING")
        print("-" * 50)
        
        wandb_api_key = os.getenv('WANDB_API_KEY')
        if not wandb_api_key:
            print("✗ W&B API key not found")
            all_tests_passed = False
        else:
            import wandb
            
            # Initialize W&B run
            run = wandb.init(
                project="real-api-integration-test",
                entity="samadeptc-elohim-tech-dynamics",
                name=f"comprehensive-test-{int(time.time())}",
                tags=["real-api", "comprehensive", "integration"],
                config={
                    "test_type": "comprehensive_real_api",
                    "services": ["redis", "supabase", "wandb"],
                    "timestamp": datetime.now().isoformat()
                }
            )
            print(f"✓ W&B run initialized: {run.name}")
            
            # Log comprehensive metrics
            comprehensive_metrics = {
                # Trading metrics
                "portfolio_value": 105250.75,
                "total_return": 5.25,
                "sharpe_ratio": 1.85,
                "max_drawdown": -2.3,
                "win_rate": 0.689,
                
                # Strategy metrics
                "ensemble_confidence": 0.87,
                "strategy_agreement": 0.73,
                "signal_strength": 0.92,
                "execution_time_ms": 23.5,
                
                # System metrics
                "redis_latency_ms": redis_stats['avg_latency'],
                "supabase_connected": True,
                "total_api_integrations": 3,
                
                # Performance metrics
                "api_test_success_rate": 1.0 if all_tests_passed else 0.8,
                "total_trades_today": 45,
                "active_strategies": 4
            }
            
            wandb.log(comprehensive_metrics)
            print(f"✓ Logged {len(comprehensive_metrics)} comprehensive metrics")
            
            # Log strategy comparison table
            strategy_comparison = [
                ["Redis", "Connected", f"{redis_stats['avg_latency']:.2f}ms", "✓"],
                ["Supabase", "Connected", "Real tables", "✓"],
                ["W&B", "Connected", "Logging active", "✓"],
                ["Binance", "Testnet", "API ready", "⚠️"]
            ]
            
            comparison_table = wandb.Table(
                columns=["Service", "Status", "Performance", "Integration"],
                data=strategy_comparison
            )
            wandb.log({"api_integration_status": comparison_table})
            print("✓ Logged API integration status table")
            
            # Update run summary
            run.summary.update({
                "total_apis_tested": 3,
                "successful_integrations": 3 if all_tests_passed else 2,
                "redis_performance": f"{redis_stats['avg_latency']:.2f}ms",
                "supabase_tables_working": True,
                "wandb_logging_active": True,
                "integration_success": all_tests_passed
            })
            
            wandb.finish()
            print("✓ W&B run completed and synced")
        
        # ===== FINAL RESULTS =====
        print("\n" + "=" * 80)
        if all_tests_passed:
            print("🎉 ALL COMPREHENSIVE API TESTS PASSED!")
            print("✅ Redis: Real-time caching operational")
            print("✅ Supabase: Database storage operational (existing tables)")
            print("✅ W&B: Experiment tracking and logging operational")
            print("\nThe strategy ensemble system can now use real APIs for:")
            print("  - Real-time data caching and retrieval")
            print("  - Persistent storage of trades and metrics")
            print("  - ML experiment tracking and visualization")
        else:
            print("⚠️  SOME API TESTS FAILED")
            print("Check the error messages above for details")
        print("=" * 80)
        
        return all_tests_passed
        
    except Exception as e:
        print(f"\n❌ Comprehensive API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_comprehensive_real_apis())
    exit(0 if success else 1)