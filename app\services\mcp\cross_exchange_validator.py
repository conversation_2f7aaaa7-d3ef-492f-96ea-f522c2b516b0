#!/usr/bin/env python3
"""
Cross-Exchange Validation Service for Task 1.3.1
Implements multi-source data quality checks and cross-exchange price validation.
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging
from statistics import median, stdev
import requests

from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

@dataclass
class MarketData:
    """Market data structure for cross-exchange validation"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'price': self.price,
            'volume': self.volume,
            'timestamp': self.timestamp.isoformat()
        }

logger = logging.getLogger(__name__)

@dataclass
class ExchangeDataPoint:
    """Single exchange data point for cross-validation"""
    source: str
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    exchange_id: str = ""
    reliability_score: float = 1.0
    latency_ms: float = 0.0

@dataclass
class CrossExchangeValidation:
    """Cross-exchange validation result"""
    symbol: str
    consensus_price: float
    price_variance: float
    data_quality_score: float
    source_count: int
    outlier_sources: List[str]
    validation_timestamp: datetime
    individual_sources: List[ExchangeDataPoint]
    price_spread_pct: float
    volume_weighted_price: float

@dataclass
class DataQualityMetrics:
    """Data quality assessment metrics"""
    source_reliability: Dict[str, float]
    latency_metrics: Dict[str, float]
    price_consistency: float
    volume_consistency: float
    temporal_consistency: float
    overall_quality_score: float

class CrossExchangeValidator:
    """
    Cross-exchange validation service for enhanced data quality.
    
    Features:
    - Multi-source price validation
    - Data quality scoring
    - Outlier detection and filtering
    - Source reliability weighting
    - Redis caching for performance
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.config = config or self._default_config()
        
        # Cache keys
        self.VALIDATION_CACHE_KEY = "cross_exchange:validation"
        self.QUALITY_METRICS_KEY = "cross_exchange:quality_metrics"
        self.SOURCE_RELIABILITY_KEY = "cross_exchange:source_reliability"
        
        # Data sources configuration
        self.data_sources = {
            "binance": {
                "url": "https://api.binance.com/api/v3/ticker/24hr",
                "reliability_weight": 0.9,
                "timeout": 5
            },
            "coinbase": {
                "url": "https://api.exchange.coinbase.com/products",
                "reliability_weight": 0.85,
                "timeout": 5
            },
            "kraken": {
                "url": "https://api.kraken.com/0/public/Ticker",
                "reliability_weight": 0.8,
                "timeout": 5
            },
            "coincap": {
                "url": "https://api.coincap.io/v2/assets",
                "reliability_weight": 0.75,
                "timeout": 5
            }
        }
        
        # Source reliability tracking
        self.source_reliability = {}
        
        logger.info("Cross-exchange validation service initialized")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for cross-exchange validation"""
        return {
            "max_price_deviation_pct": 5.0,      # 5% max deviation from consensus
            "min_sources_required": 2,            # Minimum sources for validation
            "quality_threshold": 0.7,             # Minimum quality score
            "cache_ttl_validation": 30,           # 30 seconds
            "cache_ttl_metrics": 300,             # 5 minutes
            "outlier_z_score_threshold": 2.0,    # Z-score for outlier detection
            "enable_volume_weighting": True,      # Use volume-weighted consensus
            "enable_reliability_weighting": True, # Use source reliability weighting
            "max_latency_ms": 10000,              # 10 second max latency
            "enable_temporal_validation": True,   # Check for stale data
            "max_data_age_seconds": 60           # Maximum age for fresh data
        }
    
    async def validate_cross_exchange_data(
        self,
        symbol: str,
        current_price: Optional[float] = None
    ) -> CrossExchangeValidation:
        """
        Perform cross-exchange validation for a given symbol.
        """
        try:
            # Check cache first
            cache_key = f"{self.VALIDATION_CACHE_KEY}:{symbol}"
            cached_validation = await self.redis_service.get(cache_key)
            
            if cached_validation:
                validation_data = json.loads(cached_validation)
                cache_time = datetime.fromisoformat(validation_data['validation_timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_validation"]):
                    return CrossExchangeValidation(**validation_data)
            
            # Fetch data from multiple sources concurrently
            source_data = await self._fetch_multi_source_data(symbol)
            
            if len(source_data) < self.config["min_sources_required"]:
                raise ValueError(f"Insufficient data sources: {len(source_data)} < {self.config['min_sources_required']}")
            
            # Perform cross-validation analysis
            validation_result = await self._analyze_cross_exchange_data(
                symbol, source_data, current_price
            )
            
            # Cache the validation result
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_validation"],
                json.dumps(asdict(validation_result), default=str)
            )
            
            # Store validation history in Supabase
            if self.supabase_service:
                await self._store_validation_history(validation_result)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Cross-exchange validation failed for {symbol}: {e}")
            return self._create_fallback_validation(symbol, current_price)
    
    async def _fetch_multi_source_data(self, symbol: str) -> List[ExchangeDataPoint]:
        """Fetch data from multiple sources concurrently"""
        tasks = []
        
        for source_name, source_config in self.data_sources.items():
            task = self._fetch_source_data(source_name, symbol, source_config)
            tasks.append(task)
        
        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and collect valid data points
        valid_data = []
        for i, result in enumerate(results):
            source_name = list(self.data_sources.keys())[i]
            
            if isinstance(result, Exception):
                logger.warning(f"Failed to fetch data from {source_name}: {result}")
                await self._update_source_reliability(source_name, False)
            elif result:
                valid_data.append(result)
                await self._update_source_reliability(source_name, True)
        
        return valid_data
    
    async def _fetch_source_data(
        self,
        source_name: str,
        symbol: str,
        source_config: Dict
    ) -> Optional[ExchangeDataPoint]:
        """Fetch data from a specific source"""
        start_time = datetime.now()
        
        try:
            if source_name == "binance":
                return await self._fetch_binance_data(symbol, start_time)
            elif source_name == "coinbase":
                return await self._fetch_coinbase_data(symbol, start_time)
            elif source_name == "kraken":
                return await self._fetch_kraken_data(symbol, start_time)
            elif source_name == "coincap":
                return await self._fetch_coincap_data(symbol, start_time)
            else:
                logger.warning(f"Unknown data source: {source_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching data from {source_name}: {e}")
            return None
    
    async def _fetch_binance_data(self, symbol: str, start_time: datetime) -> Optional[ExchangeDataPoint]:
        """Fetch data from Binance API"""
        try:
            # Convert symbol format (e.g., BTC -> BTCUSDT)
            binance_symbol = f"{symbol}USDT" if symbol != "USDT" else "BTCUSDT"
            
            url = f"https://api.binance.com/api/v3/ticker/24hr?symbol={binance_symbol}"
            
            # Use asyncio to run the synchronous request
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.get(url, timeout=self.data_sources["binance"]["timeout"])
            )
            
            if response.status_code == 200:
                data = response.json()
                latency = (datetime.now() - start_time).total_seconds() * 1000
                
                return ExchangeDataPoint(
                    source="binance",
                    symbol=symbol,
                    price=float(data["lastPrice"]),
                    volume=float(data["volume"]),
                    timestamp=datetime.now(),
                    exchange_id="binance",
                    reliability_score=self.data_sources["binance"]["reliability_weight"],
                    latency_ms=latency
                )
            else:
                logger.warning(f"Binance API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Binance data fetch error: {e}")
            return None
    
    async def _fetch_coinbase_data(self, symbol: str, start_time: datetime) -> Optional[ExchangeDataPoint]:
        """Fetch data from Coinbase API"""
        try:
            # Convert symbol format (e.g., BTC -> BTC-USD)
            coinbase_symbol = f"{symbol}-USD"
            
            url = f"https://api.exchange.coinbase.com/products/{coinbase_symbol}/ticker"
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.get(url, timeout=self.data_sources["coinbase"]["timeout"])
            )
            
            if response.status_code == 200:
                data = response.json()
                latency = (datetime.now() - start_time).total_seconds() * 1000
                
                return ExchangeDataPoint(
                    source="coinbase",
                    symbol=symbol,
                    price=float(data["price"]),
                    volume=float(data["volume"]),
                    timestamp=datetime.now(),
                    exchange_id="coinbase-pro",
                    reliability_score=self.data_sources["coinbase"]["reliability_weight"],
                    latency_ms=latency
                )
            else:
                logger.warning(f"Coinbase API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Coinbase data fetch error: {e}")
            return None
    
    async def _fetch_kraken_data(self, symbol: str, start_time: datetime) -> Optional[ExchangeDataPoint]:
        """Fetch data from Kraken API"""
        try:
            # Convert symbol format (e.g., BTC -> XXBTZUSD)
            kraken_symbol = f"X{symbol}ZUSD" if len(symbol) == 3 else f"{symbol}USD"
            
            url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.get(url, timeout=self.data_sources["kraken"]["timeout"])
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("error"):
                    logger.warning(f"Kraken API error: {data['error']}")
                    return None
                
                # Kraken returns data with variable key names
                result_data = data.get("result", {})
                if not result_data:
                    return None
                
                # Get the first (and usually only) result
                ticker_data = list(result_data.values())[0]
                latency = (datetime.now() - start_time).total_seconds() * 1000
                
                return ExchangeDataPoint(
                    source="kraken",
                    symbol=symbol,
                    price=float(ticker_data["c"][0]),  # Last trade price
                    volume=float(ticker_data["v"][1]),  # 24h volume
                    timestamp=datetime.now(),
                    exchange_id="kraken",
                    reliability_score=self.data_sources["kraken"]["reliability_weight"],
                    latency_ms=latency
                )
            else:
                logger.warning(f"Kraken API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Kraken data fetch error: {e}")
            return None
    
    async def _fetch_coincap_data(self, symbol: str, start_time: datetime) -> Optional[ExchangeDataPoint]:
        """Fetch data from CoinCap API"""
        try:
            # CoinCap uses lowercase symbols
            coincap_symbol = symbol.lower()
            
            url = f"https://api.coincap.io/v2/assets/{coincap_symbol}"
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.get(url, timeout=self.data_sources["coincap"]["timeout"])
            )
            
            if response.status_code == 200:
                data = response.json()
                asset_data = data.get("data")
                
                if not asset_data:
                    return None
                
                latency = (datetime.now() - start_time).total_seconds() * 1000
                
                return ExchangeDataPoint(
                    source="coincap",
                    symbol=symbol,
                    price=float(asset_data["priceUsd"]),
                    volume=float(asset_data["volumeUsd24Hr"]),
                    timestamp=datetime.now(),
                    exchange_id="coincap-aggregate",
                    reliability_score=self.data_sources["coincap"]["reliability_weight"],
                    latency_ms=latency
                )
            else:
                logger.warning(f"CoinCap API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"CoinCap data fetch error: {e}")
            return None
    
    async def _analyze_cross_exchange_data(
        self,
        symbol: str,
        source_data: List[ExchangeDataPoint],
        current_price: Optional[float] = None
    ) -> CrossExchangeValidation:
        """Analyze cross-exchange data and generate validation result"""
        
        # Extract prices and volumes
        prices = [dp.price for dp in source_data]
        volumes = [dp.volume for dp in source_data]
        
        # Calculate basic statistics
        consensus_price = median(prices)
        price_variance = stdev(prices) if len(prices) > 1 else 0.0
        price_spread_pct = ((max(prices) - min(prices)) / consensus_price) * 100 if consensus_price > 0 else 0.0
        
        # Detect outliers using Z-score
        outlier_sources = []
        if len(prices) > 2:
            mean_price = np.mean(prices)
            std_price = np.std(prices)
            
            for i, dp in enumerate(source_data):
                if std_price > 0:
                    z_score = abs((dp.price - mean_price) / std_price)
                    if z_score > self.config["outlier_z_score_threshold"]:
                        outlier_sources.append(dp.source)
        
        # Calculate volume-weighted price
        total_volume = sum(volumes)
        if total_volume > 0 and self.config["enable_volume_weighting"]:
            volume_weighted_price = sum(dp.price * dp.volume for dp in source_data) / total_volume
        else:
            volume_weighted_price = consensus_price
        
        # Calculate reliability-weighted price
        if self.config["enable_reliability_weighting"]:
            total_weight = sum(dp.reliability_score for dp in source_data)
            if total_weight > 0:
                reliability_weighted_price = sum(
                    dp.price * dp.reliability_score for dp in source_data
                ) / total_weight
                # Use the more conservative of volume-weighted or reliability-weighted
                consensus_price = (volume_weighted_price + reliability_weighted_price) / 2
        
        # Calculate data quality score
        data_quality_score = await self._calculate_data_quality_score(
            source_data, price_variance, price_spread_pct
        )
        
        return CrossExchangeValidation(
            symbol=symbol,
            consensus_price=consensus_price,
            price_variance=price_variance,
            data_quality_score=data_quality_score,
            source_count=len(source_data),
            outlier_sources=outlier_sources,
            validation_timestamp=datetime.now(),
            individual_sources=source_data,
            price_spread_pct=price_spread_pct,
            volume_weighted_price=volume_weighted_price
        )
    
    async def _calculate_data_quality_score(
        self,
        source_data: List[ExchangeDataPoint],
        price_variance: float,
        price_spread_pct: float
    ) -> float:
        """Calculate overall data quality score"""
        
        # Base score starts at 1.0
        quality_score = 1.0
        
        # Penalize high price variance
        variance_penalty = min(price_variance / 1000, 0.3)  # Max 30% penalty
        quality_score -= variance_penalty
        
        # Penalize high price spread
        spread_penalty = min(price_spread_pct / 100, 0.2)  # Max 20% penalty  
        quality_score -= spread_penalty
        
        # Penalize high latency
        avg_latency = np.mean([dp.latency_ms for dp in source_data])
        latency_penalty = min(avg_latency / self.config["max_latency_ms"], 0.2)
        quality_score -= latency_penalty
        
        # Boost for more sources
        source_bonus = min(len(source_data) / 10, 0.1)  # Max 10% bonus
        quality_score += source_bonus
        
        # Boost for high reliability sources
        avg_reliability = np.mean([dp.reliability_score for dp in source_data])
        reliability_bonus = (avg_reliability - 0.5) * 0.2  # Up to 10% bonus
        quality_score += reliability_bonus
        
        return max(0.0, min(1.0, quality_score))
    
    async def _update_source_reliability(self, source_name: str, success: bool) -> None:
        """Update source reliability based on success/failure"""
        try:
            cache_key = f"{self.SOURCE_RELIABILITY_KEY}:{source_name}"
            cached_reliability = await self.redis_service.get(cache_key)
            
            if cached_reliability:
                reliability_data = json.loads(cached_reliability)
                current_score = reliability_data["score"]
                total_requests = reliability_data["total_requests"]
                successful_requests = reliability_data["successful_requests"]
            else:
                current_score = 0.8
                total_requests = 0
                successful_requests = 0
            
            # Update counts
            total_requests += 1
            if success:
                successful_requests += 1
            
            # Calculate new score with exponential moving average
            new_score = successful_requests / total_requests
            alpha = 0.1
            updated_score = alpha * new_score + (1 - alpha) * current_score
            
            # Cache updated reliability
            reliability_data = {
                "score": updated_score,
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "last_updated": datetime.now().isoformat()
            }
            
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_metrics"],
                json.dumps(reliability_data)
            )
            
            # Update local reliability score
            if source_name in self.data_sources:
                self.data_sources[source_name]["reliability_weight"] = updated_score
            
        except Exception as e:
            logger.error(f"Failed to update source reliability for {source_name}: {e}")
    
    async def _store_validation_history(self, validation: CrossExchangeValidation) -> None:
        """Store validation history in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            validation_data = {
                'symbol': validation.symbol,
                'consensus_price': validation.consensus_price,
                'price_variance': validation.price_variance,
                'data_quality_score': validation.data_quality_score,
                'source_count': validation.source_count,
                'outlier_sources': validation.outlier_sources,
                'price_spread_pct': validation.price_spread_pct,
                'volume_weighted_price': validation.volume_weighted_price,
                'validation_timestamp': validation.validation_timestamp.isoformat(),
                'individual_sources': [asdict(source) for source in validation.individual_sources]
            }
            
            await self.supabase_service.store_trade_execution({
                'strategy_name': 'cross_exchange_validator',
                'symbol': validation.symbol,
                'action': 'VALIDATE',
                'quantity': 0,
                'price': validation.consensus_price,
                'timestamp': validation.validation_timestamp.isoformat(),
                'metadata': validation_data
            })
            
        except Exception as e:
            logger.error(f"Failed to store validation history: {e}")
    
    def _create_fallback_validation(
        self,
        symbol: str,
        current_price: Optional[float]
    ) -> CrossExchangeValidation:
        """Create fallback validation when cross-exchange validation fails"""
        
        fallback_price = current_price or 0.0
        
        return CrossExchangeValidation(
            symbol=symbol,
            consensus_price=fallback_price,
            price_variance=0.0,
            data_quality_score=0.0,
            source_count=0,
            outlier_sources=[],
            validation_timestamp=datetime.now(),
            individual_sources=[],
            price_spread_pct=0.0,
            volume_weighted_price=fallback_price
        )
    
    async def get_data_quality_metrics(self, symbol: str) -> DataQualityMetrics:
        """Get comprehensive data quality metrics"""
        try:
            cache_key = f"{self.QUALITY_METRICS_KEY}:{symbol}"
            cached_metrics = await self.redis_service.get(cache_key)
            
            if cached_metrics:
                metrics_data = json.loads(cached_metrics)
                cache_time = datetime.fromisoformat(metrics_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_metrics"]):
                    del metrics_data['timestamp']  # Remove timestamp before creating dataclass
                    return DataQualityMetrics(**metrics_data)
            
            # Calculate fresh metrics
            validation = await self.validate_cross_exchange_data(symbol)
            
            # Gather source reliability scores
            source_reliability = {}
            for source_name in self.data_sources.keys():
                reliability_key = f"{self.SOURCE_RELIABILITY_KEY}:{source_name}"
                reliability_data = await self.redis_service.get(reliability_key)
                if reliability_data:
                    reliability_info = json.loads(reliability_data)
                    source_reliability[source_name] = reliability_info["score"]
                else:
                    source_reliability[source_name] = self.data_sources[source_name]["reliability_weight"]
            
            # Calculate latency metrics
            latency_metrics = {}
            for source in validation.individual_sources:
                latency_metrics[source.source] = source.latency_ms
            
            # Calculate consistency metrics
            prices = [s.price for s in validation.individual_sources]
            volumes = [s.volume for s in validation.individual_sources]
            
            price_consistency = 1.0 - (validation.price_variance / validation.consensus_price) if validation.consensus_price > 0 else 0.0
            volume_consistency = 1.0 - (stdev(volumes) / np.mean(volumes)) if len(volumes) > 1 and np.mean(volumes) > 0 else 1.0
            temporal_consistency = 1.0  # Simplified - would check data freshness in production
            
            overall_quality_score = validation.data_quality_score
            
            metrics = DataQualityMetrics(
                source_reliability=source_reliability,
                latency_metrics=latency_metrics,
                price_consistency=max(0.0, min(1.0, price_consistency)),
                volume_consistency=max(0.0, min(1.0, volume_consistency)),
                temporal_consistency=temporal_consistency,
                overall_quality_score=overall_quality_score
            )
            
            # Cache metrics
            metrics_data = asdict(metrics)
            metrics_data['timestamp'] = datetime.now().isoformat()
            
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_metrics"],
                json.dumps(metrics_data)
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get data quality metrics: {e}")
            return DataQualityMetrics(
                source_reliability={},
                latency_metrics={},
                price_consistency=0.0,
                volume_consistency=0.0,
                temporal_consistency=0.0,
                overall_quality_score=0.0
            )

# Utility functions for integration

async def create_cross_exchange_validator(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[Dict] = None
) -> CrossExchangeValidator:
    """Factory function to create cross-exchange validator"""
    
    redis_service = RedisService(redis_url)
    
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return CrossExchangeValidator(
        redis_service=redis_service,
        supabase_service=supabase_service,
        config=config
    )

# Example usage
if __name__ == "__main__":
    # This would be used for testing the cross-exchange validator
    pass