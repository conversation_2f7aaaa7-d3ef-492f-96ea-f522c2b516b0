#!/usr/bin/env python3
"""
Optimized Database Connection Pool Service
Provides connection pooling and performance optimizations for Supabase operations.
Auto-generated on: June 16, 2025
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv('.env')
logger = logging.getLogger(__name__)

class OptimizedSupabaseService:
    """Optimized Supabase service with connection pooling"""
    
    def __init__(self, pool_size: int = 20, max_per_host: int = 10):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")
        
        self.api_url = f"{self.supabase_url}/rest/v1"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
        
        # Connection pool configuration
        self.connector = aiohttp.TCPConnector(
            limit=pool_size,
            limit_per_host=max_per_host,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        self.timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = None
        
        # Performance tracking
        self.metrics = {
            'requests_total': 0,
            'requests_successful': 0,
            'average_response_time': 0,
            'response_times': []
        }
    
    async def connect(self):
        """Initialize connection pool"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=self.timeout,
                headers=self.headers
            )
        logger.info("✓ Optimized Supabase connection pool initialized")
    
    async def disconnect(self):
        """Close connection pool"""
        if self.session:
            await self.session.close()
        logger.info("✓ Connection pool closed")
    
    async def _execute_request(self, method: str, endpoint: str, data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute HTTP request with performance tracking"""
        if not self.session:
            await self.connect()
        
        start_time = time.time()
        self.metrics['requests_total'] += 1
        
        try:
            url = f"{self.api_url}/{endpoint}"
            
            if method.upper() == 'GET':
                async with self.session.get(url, params=params) as response:
                    result = await self._process_response(response, start_time)
            elif method.upper() == 'POST':
                async with self.session.post(url, json=data, params=params) as response:
                    result = await self._process_response(response, start_time)
            elif method.upper() == 'DELETE':
                async with self.session.delete(url, params=params) as response:
                    result = await self._process_response(response, start_time)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            self.metrics['requests_successful'] += 1
            return result
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            self.metrics['response_times'].append(response_time)
            logger.error(f"Request failed after {response_time:.1f}ms: {e}")
            raise
    
    async def _process_response(self, response, start_time: float) -> Dict[str, Any]:
        """Process HTTP response"""
        response_time = (time.time() - start_time) * 1000
        self.metrics['response_times'].append(response_time)
        
        # Update average response time
        if self.metrics['response_times']:
            self.metrics['average_response_time'] = sum(self.metrics['response_times']) / len(self.metrics['response_times'])
        
        if response.status in [200, 201, 204]:
            if response.status == 204:
                return {"success": True}
            else:
                return await response.json()
        else:
            error_text = await response.text()
            raise Exception(f"HTTP {response.status}: {error_text}")
    
    async def store_trade_execution_optimized(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store trade execution with optimizations"""
        return await self._execute_request('POST', 'trade_executions', data=trade_data)
    
    async def store_portfolio_metrics_optimized(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store portfolio metrics with optimizations"""
        return await self._execute_request('POST', 'portfolio_metrics', data=metrics_data)
    
    async def query_with_timeout(self, table: str, params: Optional[Dict] = None, timeout: float = 5.0) -> List[Dict[str, Any]]:
        """Query with configurable timeout"""
        try:
            result = await asyncio.wait_for(
                self._execute_request('GET', table, params=params),
                timeout=timeout
            )
            return result if isinstance(result, list) else [result]
        except asyncio.TimeoutError:
            logger.warning(f"Query to {table} timed out after {timeout}s")
            return []
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        success_rate = (self.metrics['requests_successful'] / max(1, self.metrics['requests_total'])) * 100
        
        return {
            'total_requests': self.metrics['requests_total'],
            'successful_requests': self.metrics['requests_successful'],
            'success_rate_percent': success_rate,
            'average_response_time_ms': self.metrics['average_response_time'],
            'connection_pool_active': self.session is not None
        }

# Global instance for connection pooling
_optimized_service = None

async def get_optimized_supabase_service() -> OptimizedSupabaseService:
    """Get global optimized Supabase service instance"""
    global _optimized_service
    if _optimized_service is None:
        _optimized_service = OptimizedSupabaseService()
        await _optimized_service.connect()
    return _optimized_service

async def close_optimized_service():
    """Close global service"""
    global _optimized_service
    if _optimized_service:
        await _optimized_service.disconnect()
        _optimized_service = None
