{"name": "frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:8000", "dependencies": {"@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "axios": "^1.8.3", "http-proxy-middleware": "^3.0.3", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "recharts": "^2.15.4", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-react-app": "^7.0.1"}}