#!/usr/bin/env python3
"""Test external APIs directly"""

import requests
import json

def test_external_apis():
    results = {}
    
    # Test CoinCap API directly
    try:
        response = requests.get('https://api.coincap.io/v2/assets/bitcoin', timeout=10)
        if response.status_code == 200:
            data = response.json()
            results['coincap'] = {
                'status': 'success',
                'price': float(data["data"]["priceUsd"])
            }
            print('✅ CoinCap API - Direct connection successful')
            print(f'   BTC Price: ${float(data["data"]["priceUsd"]):,.2f}')
        else:
            results['coincap'] = {'status': 'failed', 'code': response.status_code}
            print(f'❌ CoinCap API - Failed with status {response.status_code}')
    except Exception as e:
        results['coincap'] = {'status': 'error', 'error': str(e)}
        print(f'❌ CoinCap API - Error: {e}')

    # Test Binance API directly
    try:
        response = requests.get('https://fapi.binance.com/fapi/v1/ping', timeout=10)
        if response.status_code == 200:
            results['binance'] = {'status': 'success'}
            print('✅ Binance Futures API - Direct connection successful')
        else:
            results['binance'] = {'status': 'failed', 'code': response.status_code}
            print(f'❌ Binance Futures API - Failed with status {response.status_code}')
    except Exception as e:
        results['binance'] = {'status': 'error', 'error': str(e)}
        print(f'❌ Binance Futures API - Error: {e}')
    
    # Test Supabase API directly
    try:
        import os
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_KEY')
        
        if supabase_url and supabase_key:
            headers = {
                'apikey': supabase_key,
                'Authorization': f'Bearer {supabase_key}'
            }
            response = requests.get(f'{supabase_url}/rest/v1/trades?limit=1', headers=headers, timeout=10)
            if response.status_code == 200:
                results['supabase'] = {'status': 'success', 'response_code': response.status_code}
                print('✅ Supabase API - Direct connection successful')
            else:
                results['supabase'] = {'status': 'failed', 'code': response.status_code}
                print(f'❌ Supabase API - Failed with status {response.status_code}')
        else:
            results['supabase'] = {'status': 'error', 'error': 'Missing credentials'}
            print('❌ Supabase API - Missing credentials')
    except Exception as e:
        results['supabase'] = {'status': 'error', 'error': str(e)}
        print(f'❌ Supabase API - Error: {e}')
    
    return results

if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    
    print("🧪 Testing External APIs...")
    results = test_external_apis()
    
    print("\n📊 Summary:")
    for service, result in results.items():
        status = result['status']
        icon = '✅' if status == 'success' else '❌'
        print(f"{icon} {service.title()}: {status}")