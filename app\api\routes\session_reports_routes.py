"""
Trading Session Reports API Routes - Enhanced reporting and analysis endpoints.
Provides comprehensive session analysis, reports, and comparison features.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
import json
import numpy as np
from pydantic import BaseModel, Field

from app.services.auto_trading_controller import AutoTradingController
from app.dependencies import get_current_user

logger = logging.getLogger(__name__)

# Router setup
router = APIRouter(prefix="/api/reports", tags=["session-reports"])

# Response Models
class SessionReportResponse(BaseModel):
    """Comprehensive session report response"""
    session_id: str
    session_summary: Dict[str, Any]
    performance_analysis: Dict[str, Any]
    trade_analysis: Dict[str, Any]
    strategy_analysis: Dict[str, Any]
    risk_analysis: Dict[str, Any]
    time_series_data: Dict[str, Any]
    alerts_analysis: Dict[str, Any]
    recommendations: List[str]

class SessionComparisonResponse(BaseModel):
    """Session comparison analysis response"""
    comparison_summary: Dict[str, Any]
    performance_comparison: Dict[str, Any]
    strategy_comparison: Dict[str, Any]
    risk_comparison: Dict[str, Any]
    improvement_analysis: Dict[str, Any]

class SessionAnalyticsResponse(BaseModel):
    """Advanced session analytics response"""
    session_id: str
    correlation_analysis: Dict[str, Any]
    volatility_analysis: Dict[str, Any]
    drawdown_analysis: Dict[str, Any]
    execution_analysis: Dict[str, Any]
    ml_performance_analysis: Dict[str, Any]
    market_conditions_impact: Dict[str, Any]

# Global controller dependency
def get_trading_controller() -> AutoTradingController:
    """Dependency to get trading controller instance"""
    # This should be injected properly in production
    pass

@router.get("/session/{session_id}/report", response_model=SessionReportResponse)
async def get_session_comprehensive_report(
    session_id: str,
    include_charts: bool = Query(True, description="Include chart data"),
    include_trade_details: bool = Query(True, description="Include detailed trade analysis"),
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Generate comprehensive session report with analysis and visualizations.
    
    Provides detailed analysis including:
    - Performance summary and metrics
    - Strategy attribution and effectiveness
    - Risk analysis and drawdown periods
    - Trade execution analysis
    - Time series performance data
    - Alert and event analysis
    - Actionable recommendations
    """
    try:
        # Get session details
        session_details = await controller.get_session_details(session_id)
        session = session_details["session"]
        
        # Generate comprehensive analysis
        report = SessionReportResponse(
            session_id=session_id,
            session_summary=await _generate_session_summary(session),
            performance_analysis=await _generate_performance_analysis(session),
            trade_analysis=await _generate_trade_analysis(session, include_trade_details),
            strategy_analysis=await _generate_strategy_analysis(session),
            risk_analysis=await _generate_risk_analysis(session),
            time_series_data=await _generate_time_series_data(session, include_charts),
            alerts_analysis=await _generate_alerts_analysis(session),
            recommendations=await _generate_recommendations(session)
        )
        
        return report
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to generate session report: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate session report: {str(e)}"
        )

@router.get("/session/{session_id}/analytics", response_model=SessionAnalyticsResponse)
async def get_session_advanced_analytics(
    session_id: str,
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get advanced analytics for a trading session.
    
    Provides deep analysis including:
    - Correlation analysis between strategies
    - Volatility patterns and regime changes
    - Drawdown attribution and recovery analysis
    - Execution quality metrics
    - ML model performance impact
    - Market conditions correlation
    """
    try:
        session_details = await controller.get_session_details(session_id)
        session = session_details["session"]
        
        analytics = SessionAnalyticsResponse(
            session_id=session_id,
            correlation_analysis=await _generate_strategy_correlation_analysis(session),
            volatility_analysis=await _generate_volatility_analysis(session),
            drawdown_analysis=await _generate_drawdown_analysis(session),
            execution_analysis=await _generate_execution_analysis(session),
            ml_performance_analysis=await _generate_ml_performance_analysis(session),
            market_conditions_impact=await _generate_market_conditions_analysis(session)
        )
        
        return analytics
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to generate session analytics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate session analytics: {str(e)}"
        )

@router.post("/sessions/compare", response_model=SessionComparisonResponse)
async def compare_trading_sessions(
    session_ids: List[str] = Field(..., description="List of session IDs to compare"),
    baseline_session_id: Optional[str] = Field(None, description="Baseline session for comparison"),
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Compare multiple trading sessions for performance analysis.
    
    Provides comparative analysis including:
    - Performance metrics comparison
    - Strategy effectiveness comparison
    - Risk profile comparison
    - Improvement recommendations
    - Best practices identification
    """
    try:
        if len(session_ids) < 2:
            raise ValueError("At least 2 sessions required for comparison")
        
        # Get session data for all sessions
        sessions_data = []
        for session_id in session_ids:
            session_details = await controller.get_session_details(session_id)
            sessions_data.append(session_details["session"])
        
        # Generate comparison analysis
        comparison = SessionComparisonResponse(
            comparison_summary=await _generate_comparison_summary(sessions_data, baseline_session_id),
            performance_comparison=await _generate_performance_comparison(sessions_data),
            strategy_comparison=await _generate_strategy_comparison(sessions_data),
            risk_comparison=await _generate_risk_comparison(sessions_data),
            improvement_analysis=await _generate_improvement_analysis(sessions_data, baseline_session_id)
        )
        
        return comparison
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to compare sessions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to compare sessions: {str(e)}"
        )

@router.get("/session/{session_id}/export")
async def export_session_report(
    session_id: str,
    format: str = Query("json", regex="^(json|csv|pdf)$", description="Export format"),
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Export session report in various formats.
    
    Supports:
    - JSON: Complete session data and analysis
    - CSV: Trade data and performance metrics
    - PDF: Formatted report with charts (future)
    """
    try:
        session_details = await controller.get_session_details(session_id)
        
        if format == "json":
            return session_details
        elif format == "csv":
            # Generate CSV export
            csv_data = await _generate_csv_export(session_details)
            return {"csv_data": csv_data}
        elif format == "pdf":
            # PDF export (placeholder)
            return {"message": "PDF export not yet implemented"}
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to export session report: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to export session report: {str(e)}"
        )

@router.get("/sessions/summary")
async def get_sessions_summary(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    status_filter: Optional[str] = Query(None, description="Filter by session status"),
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get summary statistics for trading sessions.
    
    Provides aggregate analysis including:
    - Overall performance statistics
    - Strategy effectiveness over time
    - Success/failure patterns
    - Trend analysis
    """
    try:
        # Get sessions within date range
        sessions_data = await controller.list_sessions(
            limit=1000,  # Large limit to get comprehensive data
            offset=0,
            status_filter=status_filter
        )
        
        # Filter by date range
        cutoff_date = datetime.now() - timedelta(days=days)
        filtered_sessions = []
        
        for session_summary in sessions_data["sessions"]:
            session_start = datetime.fromisoformat(session_summary["start_time"])
            if session_start >= cutoff_date:
                filtered_sessions.append(session_summary)
        
        # Generate summary statistics
        summary = await _generate_sessions_summary(filtered_sessions, days)
        
        return {
            "period_days": days,
            "total_sessions": len(filtered_sessions),
            "status_filter": status_filter,
            "summary_statistics": summary,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to generate sessions summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate sessions summary: {str(e)}"
        )

@router.get("/live-session/report")
async def get_live_session_report(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get real-time report for the currently active session.
    
    Provides live analysis of the active session including:
    - Real-time performance metrics
    - Current strategy status
    - Live risk monitoring
    - Active alerts and warnings
    """
    try:
        status = await controller.get_session_status()
        
        if not status.get("session_active"):
            return {
                "active": False,
                "message": "No active trading session"
            }
        
        session_id = status["session_id"]
        
        # Generate live report
        live_report = {
            "active": True,
            "session_id": session_id,
            "live_metrics": await _generate_live_metrics(status),
            "current_positions": await _get_current_positions(status),
            "active_alerts": await _get_active_alerts(status),
            "real_time_performance": await _generate_real_time_performance(status),
            "strategy_status": await _get_strategy_status(status),
            "risk_monitoring": await _generate_risk_monitoring(status),
            "last_updated": datetime.now().isoformat()
        }
        
        return live_report
        
    except Exception as e:
        logger.error(f"Failed to generate live session report: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate live session report: {str(e)}"
        )

# Helper functions for report generation

async def _generate_session_summary(session: Dict) -> Dict:
    """Generate session summary with key metrics"""
    try:
        start_time = datetime.fromisoformat(session["start_time"])
        end_time = datetime.fromisoformat(session["end_time"]) if session.get("end_time") else datetime.now()
        duration = end_time - start_time
        
        performance = session.get("performance", {})
        
        return {
            "session_id": session["id"],
            "status": session["status"],
            "duration": {
                "total_seconds": duration.total_seconds(),
                "formatted": _format_duration(duration),
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat() if session.get("end_time") else None
            },
            "key_metrics": {
                "total_pnl": performance.get("total_pnl", 0),
                "total_return": performance.get("total_return", 0),
                "win_rate": performance.get("win_rate", 0),
                "sharpe_ratio": performance.get("sharpe_ratio", 0),
                "max_drawdown": performance.get("max_drawdown", 0),
                "total_trades": performance.get("total_trades", 0)
            },
            "symbols_traded": session.get("parameters", {}).get("symbols", []),
            "strategies_enabled": _get_enabled_strategies(session.get("parameters", {}))
        }
    except Exception as e:
        logger.error(f"Error generating session summary: {e}")
        return {"error": str(e)}

async def _generate_performance_analysis(session: Dict) -> Dict:
    """Generate detailed performance analysis"""
    try:
        performance = session.get("performance", {})
        trades = session.get("trades", [])
        
        # Calculate additional metrics
        winning_trades = [t for t in trades if t.get("pnl", 0) > 0]
        losing_trades = [t for t in trades if t.get("pnl", 0) < 0]
        
        return {
            "financial_metrics": {
                "total_pnl": performance.get("total_pnl", 0),
                "realized_pnl": performance.get("realized_pnl", 0),
                "unrealized_pnl": performance.get("unrealized_pnl", 0),
                "total_return": performance.get("total_return", 0),
                "roi_percentage": performance.get("total_return", 0) * 100,
                "compound_annual_growth_rate": _calculate_cagr(session),
                "profit_factor": performance.get("profit_factor", 0)
            },
            "risk_metrics": {
                "sharpe_ratio": performance.get("sharpe_ratio", 0),
                "max_drawdown": performance.get("max_drawdown", 0),
                "current_drawdown": performance.get("current_drawdown", 0),
                "volatility": performance.get("volatility", 0),
                "var_95": performance.get("var_95", 0),
                "beta": _calculate_beta(trades),
                "alpha": _calculate_alpha(trades)
            },
            "trading_metrics": {
                "total_trades": len(trades),
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": len(winning_trades) / len(trades) if trades else 0,
                "avg_win": sum(t["pnl"] for t in winning_trades) / len(winning_trades) if winning_trades else 0,
                "avg_loss": sum(t["pnl"] for t in losing_trades) / len(losing_trades) if losing_trades else 0,
                "largest_win": max((t["pnl"] for t in winning_trades), default=0),
                "largest_loss": min((t["pnl"] for t in losing_trades), default=0),
                "avg_holding_time": _calculate_avg_holding_time(trades)
            },
            "efficiency_metrics": {
                "trades_per_day": _calculate_trades_per_day(session, trades),
                "avg_execution_time_ms": performance.get("avg_execution_time_ms", 0),
                "slippage_cost": performance.get("slippage_cost", 0),
                "commission_cost": performance.get("commission_cost", 0),
                "total_costs": performance.get("slippage_cost", 0) + performance.get("commission_cost", 0)
            }
        }
    except Exception as e:
        logger.error(f"Error generating performance analysis: {e}")
        return {"error": str(e)}

async def _generate_trade_analysis(session: Dict, include_details: bool) -> Dict:
    """Generate detailed trade analysis"""
    try:
        trades = session.get("trades", [])
        
        if not trades:
            return {"message": "No trades found"}
        
        # Trade distribution analysis
        trade_sizes = [t.get("value", 0) for t in trades]
        trade_pnls = [t.get("pnl", 0) for t in trades]
        
        analysis = {
            "trade_statistics": {
                "total_trades": len(trades),
                "avg_trade_size": sum(trade_sizes) / len(trade_sizes) if trade_sizes else 0,
                "median_trade_size": _median(trade_sizes),
                "largest_trade": max(trade_sizes) if trade_sizes else 0,
                "smallest_trade": min(trade_sizes) if trade_sizes else 0,
                "trade_size_std": _std_dev(trade_sizes)
            },
            "pnl_distribution": {
                "avg_pnl": sum(trade_pnls) / len(trade_pnls) if trade_pnls else 0,
                "median_pnl": _median(trade_pnls),
                "pnl_std": _std_dev(trade_pnls),
                "positive_trades": len([p for p in trade_pnls if p > 0]),
                "negative_trades": len([p for p in trade_pnls if p < 0]),
                "breakeven_trades": len([p for p in trade_pnls if p == 0])
            },
            "temporal_analysis": {
                "trades_by_hour": _analyze_trades_by_hour(trades),
                "trades_by_day": _analyze_trades_by_day(trades),
                "trade_frequency": _calculate_trade_frequency(trades)
            },
            "strategy_attribution": _analyze_strategy_attribution(trades)
        }
        
        if include_details:
            analysis["recent_trades"] = trades[-20:]  # Last 20 trades
            analysis["best_trades"] = sorted(trades, key=lambda x: x.get("pnl", 0), reverse=True)[:10]
            analysis["worst_trades"] = sorted(trades, key=lambda x: x.get("pnl", 0))[:10]
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error generating trade analysis: {e}")
        return {"error": str(e)}

async def _generate_strategy_analysis(session: Dict) -> Dict:
    """Generate strategy performance analysis"""
    try:
        performance = session.get("performance", {})
        strategy_performance = performance.get("strategy_performance", {})
        strategy_weights = performance.get("strategy_weights", {})
        
        return {
            "strategy_performance": strategy_performance,
            "current_weights": strategy_weights,
            "weight_evolution": await _analyze_weight_evolution(session),
            "strategy_effectiveness": await _calculate_strategy_effectiveness(strategy_performance),
            "correlation_matrix": await _calculate_strategy_correlations(session),
            "contribution_analysis": await _analyze_strategy_contributions(strategy_performance),
            "optimization_suggestions": await _generate_strategy_optimization_suggestions(
                strategy_performance, strategy_weights
            )
        }
    except Exception as e:
        logger.error(f"Error generating strategy analysis: {e}")
        return {"error": str(e)}

# Additional helper functions
def _format_duration(duration: timedelta) -> str:
    """Format duration as human-readable string"""
    total_seconds = int(duration.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    if hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"

def _get_enabled_strategies(parameters: Dict) -> List[str]:
    """Get list of enabled strategies"""
    enabled = []
    if parameters.get("grid_strategy_enabled"):
        enabled.append("Grid Strategy")
    if parameters.get("ta_strategy_enabled"):
        enabled.append("Technical Analysis")
    if parameters.get("trend_strategy_enabled"):
        enabled.append("Trend Following")
    return enabled

def _calculate_cagr(session: Dict) -> float:
    """Calculate Compound Annual Growth Rate"""
    try:
        performance = session.get("performance", {})
        total_return = performance.get("total_return", 0)
        
        start_time = datetime.fromisoformat(session["start_time"])
        end_time = datetime.fromisoformat(session["end_time"]) if session.get("end_time") else datetime.now()
        duration_years = (end_time - start_time).days / 365.25
        
        if duration_years > 0 and total_return > -1:
            return ((1 + total_return) ** (1 / duration_years)) - 1
        return 0
    except:
        return 0

def _median(values: List[float]) -> float:
    """Calculate median of a list"""
    if not values:
        return 0
    sorted_values = sorted(values)
    n = len(sorted_values)
    if n % 2 == 0:
        return (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
    return sorted_values[n//2]

def _std_dev(values: List[float]) -> float:
    """Calculate standard deviation"""
    if not values:
        return 0
    mean = sum(values) / len(values)
    variance = sum((x - mean) ** 2 for x in values) / len(values)
    return variance ** 0.5

# Placeholder implementations for complex analysis functions
async def _generate_risk_analysis(session: Dict) -> Dict:
    """Generate risk analysis"""
    return {"placeholder": "Risk analysis implementation"}

async def _generate_time_series_data(session: Dict, include_charts: bool) -> Dict:
    """Generate time series data for charts"""
    return {"placeholder": "Time series data implementation"}

async def _generate_alerts_analysis(session: Dict) -> Dict:
    """Generate alerts analysis"""
    return {"placeholder": "Alerts analysis implementation"}

async def _generate_recommendations(session: Dict) -> List[str]:
    """Generate actionable recommendations"""
    return ["Recommendation 1", "Recommendation 2", "Recommendation 3"]

# Additional placeholder implementations for other analysis functions
async def _generate_strategy_correlation_analysis(session: Dict) -> Dict:
    """Generate comprehensive strategy correlation analysis"""
    try:
        from app.utils.ml_analytics import MLAnalyticsEngine
        
        # Initialize ML analytics engine
        ml_engine = MLAnalyticsEngine()
        
        # Perform strategy correlation analysis
        correlation_analysis = await ml_engine.analyze_strategy_correlation(session)
        
        # Extract strategy performance data
        performance = session.get("performance", {})
        strategy_performance = performance.get("strategy_performance", {})
        strategy_weights = performance.get("strategy_weights", {})
        trades = session.get("trades", [])
        
        # Calculate basic strategy statistics
        strategy_stats = {}
        for strategy_name, perf_value in strategy_performance.items():
            strategy_trades = [t for t in trades if t.get("strategy") == strategy_name]
            strategy_pnls = [t.get("pnl", 0) for t in strategy_trades]
            
            if strategy_pnls:
                strategy_stats[strategy_name] = {
                    "total_return": perf_value,
                    "total_trades": len(strategy_trades),
                    "avg_pnl": sum(strategy_pnls) / len(strategy_pnls),
                    "win_rate": len([p for p in strategy_pnls if p > 0]) / len(strategy_pnls),
                    "current_weight": strategy_weights.get(strategy_name, 0.0),
                    "total_pnl": sum(strategy_pnls),
                    "volatility": _std_dev(strategy_pnls) if len(strategy_pnls) > 1 else 0.0
                }
        
        # Identify ML vs Traditional strategies
        ml_strategies = {k: v for k, v in strategy_stats.items() if "ml" in k.lower() or "machine" in k.lower() or "ai" in k.lower()}
        traditional_strategies = {k: v for k, v in strategy_stats.items() if k not in ml_strategies}
        
        # Calculate correlation matrix between strategies
        strategy_correlations = await _calculate_basic_strategy_correlations(trades, list(strategy_performance.keys()))
        
        # Performance comparison
        ml_total_return = sum(s["total_return"] for s in ml_strategies.values())
        traditional_total_return = sum(s["total_return"] for s in traditional_strategies.values())
        
        return {
            "strategy_statistics": strategy_stats,
            "ml_vs_traditional": {
                "ml_strategies": list(ml_strategies.keys()),
                "traditional_strategies": list(traditional_strategies.keys()),
                "ml_total_return": ml_total_return,
                "traditional_total_return": traditional_total_return,
                "ml_avg_weight": sum(s["current_weight"] for s in ml_strategies.values()) / len(ml_strategies) if ml_strategies else 0.0,
                "traditional_avg_weight": sum(s["current_weight"] for s in traditional_strategies.values()) / len(traditional_strategies) if traditional_strategies else 0.0
            },
            "correlation_matrix": strategy_correlations,
            "diversification_metrics": {
                "strategy_count": len(strategy_stats),
                "weight_concentration": max(strategy_weights.values()) if strategy_weights else 0.0,
                "effective_strategies": len([w for w in strategy_weights.values() if w > 0.05])  # Strategies with >5% weight
            },
            "performance_ranking": sorted(strategy_stats.items(), key=lambda x: x[1]["total_return"], reverse=True),
            "risk_contribution": await _calculate_strategy_risk_contribution(strategy_stats),
            "detailed_analysis": correlation_analysis,
            "optimization_suggestions": await _generate_strategy_optimization_recommendations(strategy_stats, strategy_weights),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating strategy correlation analysis: {e}")
        return {
            "error": str(e),
            "basic_stats": {
                "total_strategies": len(session.get("performance", {}).get("strategy_performance", {})),
                "active_strategies": len([w for w in session.get("performance", {}).get("strategy_weights", {}).values() if w > 0])
            },
            "analysis_timestamp": datetime.now().isoformat()
        }

async def _generate_volatility_analysis(session: Dict) -> Dict:
    return {"placeholder": "Volatility analysis"}

async def _generate_drawdown_analysis(session: Dict) -> Dict:
    return {"placeholder": "Drawdown analysis"}

async def _generate_execution_analysis(session: Dict) -> Dict:
    return {"placeholder": "Execution analysis"}

async def _generate_ml_performance_analysis(session: Dict) -> Dict:
    """Generate comprehensive ML performance analysis"""
    try:
        from app.utils.ml_analytics import MLAnalyticsEngine
        
        # Initialize ML analytics engine
        ml_engine = MLAnalyticsEngine()
        
        # Perform comprehensive ML analysis
        ml_analysis = await ml_engine.analyze_ml_performance(session)
        
        # Extract key ML metrics from session performance
        performance = session.get("performance", {})
        
        # Enhanced ML performance summary
        ml_summary = {
            "model_performance": {
                "accuracy": performance.get("ml_model_accuracy", 0.0),
                "confidence": performance.get("ml_model_confidence", 0.0),
                "version": performance.get("ml_model_version", "unknown"),
                "state": performance.get("current_model_state", "unknown"),
                "drift_score": performance.get("ml_drift_score", 0.0),
                "prediction_accuracy": performance.get("ml_prediction_accuracy", 0.0)
            },
            "decision_metrics": {
                "total_decisions": performance.get("ml_decisions_count", 0),
                "correct_decisions": performance.get("ml_decisions_correct", 0),
                "profitable_decisions": performance.get("ml_decisions_profitable", 0),
                "decision_accuracy_rate": performance.get("ml_decisions_correct", 0) / max(performance.get("ml_decisions_count", 1), 1),
                "profitability_rate": performance.get("ml_decisions_profitable", 0) / max(performance.get("ml_decisions_count", 1), 1)
            },
            "cost_metrics": {
                "training_cost": performance.get("ml_training_cost", 0.0),
                "inference_cost": performance.get("ml_inference_cost", 0.0),
                "total_cost": performance.get("ml_total_cost", 0.0),
                "cost_per_prediction": performance.get("cost_per_prediction", 0.0),
                "roi": performance.get("ml_roi", 0.0)
            },
            "performance_comparison": performance.get("ml_vs_traditional_performance", {"ml": 0.0, "traditional": 0.0, "combined": 0.0}),
            "feature_analysis": {
                "top_features": performance.get("top_features", [])[:10],
                "feature_importance": performance.get("feature_importance_current", {}),
                "feature_drift": performance.get("feature_importance_drift", {})
            },
            "confidence_distribution": performance.get("ml_confidence_buckets", {"high": 0, "medium": 0, "low": 0}),
            "system_metrics": {
                "prediction_latency_ms": performance.get("prediction_latency_ms", 0.0),
                "memory_usage_mb": performance.get("model_memory_usage_mb", 0.0),
                "last_update": performance.get("model_last_update", datetime.now()).isoformat() if isinstance(performance.get("model_last_update"), datetime) else str(performance.get("model_last_update", "unknown"))
            }
        }
        
        # Combine with detailed analysis
        return {
            **ml_summary,
            "detailed_analysis": ml_analysis,
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating ML performance analysis: {e}")
        return {
            "error": str(e),
            "basic_metrics": {
                "ml_accuracy": session.get("performance", {}).get("ml_model_accuracy", 0.0),
                "ml_confidence": session.get("performance", {}).get("ml_model_confidence", 0.0),
                "ml_decisions": session.get("performance", {}).get("ml_decisions_count", 0)
            },
            "analysis_timestamp": datetime.now().isoformat()
        }

async def _generate_market_impact_analysis(session: Dict) -> Dict:
    """Generate comprehensive market impact analysis"""
    try:
        from app.utils.ml_analytics import MLAnalyticsEngine
        
        # Initialize ML analytics engine
        ml_engine = MLAnalyticsEngine()
        
        # Perform market impact analysis
        market_analysis = await ml_engine.analyze_market_impact(session)
        
        # Extract session data
        trades = session.get("trades", [])
        performance = session.get("performance", {})
        
        # Analyze performance by time periods
        hourly_performance = await _analyze_hourly_performance(trades)
        daily_performance = await _analyze_daily_performance(trades)
        
        # Analyze performance by market volatility
        volatility_buckets = await _analyze_volatility_impact(trades)
        
        # Market conditions impact on ML performance
        ml_market_performance = {
            "high_volatility_accuracy": await _calculate_ml_accuracy_by_volatility(trades, "high"),
            "medium_volatility_accuracy": await _calculate_ml_accuracy_by_volatility(trades, "medium"),
            "low_volatility_accuracy": await _calculate_ml_accuracy_by_volatility(trades, "low"),
            "trend_following_performance": await _analyze_trend_performance(trades),
            "range_bound_performance": await _analyze_range_performance(trades)
        }
        
        # Calculate market regime performance
        regime_performance = {
            "bull_market": await _calculate_regime_performance(trades, "bull"),
            "bear_market": await _calculate_regime_performance(trades, "bear"),
            "sideways_market": await _calculate_regime_performance(trades, "sideways")
        }
        
        # Risk-adjusted performance by market conditions
        risk_adjusted_analysis = {
            "high_vol_sharpe": await _calculate_conditional_sharpe(trades, "high_volatility"),
            "low_vol_sharpe": await _calculate_conditional_sharpe(trades, "low_volatility"),
            "trending_sharpe": await _calculate_conditional_sharpe(trades, "trending"),
            "ranging_sharpe": await _calculate_conditional_sharpe(trades, "ranging")
        }
        
        return {
            "temporal_analysis": {
                "hourly_performance": hourly_performance,
                "daily_performance": daily_performance,
                "weekend_vs_weekday": await _compare_weekend_weekday_performance(trades)
            },
            "volatility_analysis": {
                "volatility_buckets": volatility_buckets,
                "volatility_correlation": await _calculate_volatility_correlation(trades, performance),
                "optimal_volatility_range": await _find_optimal_volatility_range(trades)
            },
            "ml_market_performance": ml_market_performance,
            "regime_performance": regime_performance,
            "risk_adjusted_analysis": risk_adjusted_analysis,
            "market_adaptation": {
                "adaptation_speed": await _calculate_adaptation_speed(trades),
                "performance_stability": await _calculate_performance_stability(trades),
                "drawdown_recovery": await _analyze_drawdown_recovery(trades)
            },
            "detailed_analysis": market_analysis,
            "market_recommendations": await _generate_market_recommendations(regime_performance, volatility_buckets),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating market impact analysis: {e}")
        return {
            "error": str(e),
            "basic_market_metrics": {
                "total_trades": len(session.get("trades", [])),
                "session_duration": await _calculate_session_duration(session),
                "avg_trade_frequency": await _calculate_trade_frequency(session.get("trades", []))
            },
            "analysis_timestamp": datetime.now().isoformat()
        }

async def _generate_market_conditions_analysis(session: Dict) -> Dict:
    """Alias for market impact analysis - maintains compatibility"""
    return await _generate_market_impact_analysis(session)

async def _generate_comparison_summary(sessions: List[Dict], baseline_id: Optional[str]) -> Dict:
    return {"placeholder": "Comparison summary"}

async def _generate_performance_comparison(sessions: List[Dict]) -> Dict:
    return {"placeholder": "Performance comparison"}

async def _generate_strategy_comparison(sessions: List[Dict]) -> Dict:
    return {"placeholder": "Strategy comparison"}

async def _generate_risk_comparison(sessions: List[Dict]) -> Dict:
    return {"placeholder": "Risk comparison"}

async def _generate_cost_benefit_analysis(session: Dict) -> Dict:
    """Generate comprehensive cost-benefit analysis"""
    try:
        from app.utils.ml_analytics import MLAnalyticsEngine
        
        # Initialize ML analytics engine
        ml_engine = MLAnalyticsEngine()
        
        # Perform cost-benefit analysis
        cost_benefit_analysis = await ml_engine.analyze_cost_benefit(session)
        
        # Extract performance and cost data
        performance = session.get("performance", {})
        trades = session.get("trades", [])
        
        # ML costs breakdown
        ml_costs = {
            "training_cost": performance.get("ml_training_cost", 0.0),
            "inference_cost": performance.get("ml_inference_cost", 0.0),
            "total_cost": performance.get("ml_total_cost", 0.0),
            "cost_per_prediction": performance.get("cost_per_prediction", 0.0),
            "cost_per_trade": performance.get("ml_total_cost", 0.0) / len(trades) if trades else 0.0
        }
        
        # Performance benefits
        ml_vs_traditional = performance.get("ml_vs_traditional_performance", {"ml": 0.0, "traditional": 0.0, "combined": 0.0})
        
        benefits = {
            "ml_return": ml_vs_traditional.get("ml", 0.0),
            "traditional_return": ml_vs_traditional.get("traditional", 0.0),
            "combined_return": ml_vs_traditional.get("combined", 0.0),
            "excess_return": ml_vs_traditional.get("ml", 0.0) - ml_vs_traditional.get("traditional", 0.0),
            "ml_roi": performance.get("ml_roi", 0.0)
        }
        
        # Calculate ROI metrics
        roi_metrics = {
            "cost_to_benefit_ratio": ml_costs["total_cost"] / max(benefits["ml_return"], 0.001),
            "net_benefit": benefits["ml_return"] - ml_costs["total_cost"],
            "payback_period_days": await _calculate_payback_period(ml_costs["total_cost"], benefits["ml_return"]),
            "return_multiple": benefits["ml_return"] / max(ml_costs["total_cost"], 0.001),
            "break_even_threshold": ml_costs["total_cost"]
        }
        
        # Efficiency analysis
        efficiency_metrics = {
            "cost_per_unit_return": ml_costs["total_cost"] / max(benefits["ml_return"], 0.001),
            "prediction_efficiency": benefits["ml_return"] / max(ml_costs["cost_per_prediction"] * performance.get("ml_decisions_count", 1), 0.001),
            "training_efficiency": benefits["ml_return"] / max(ml_costs["training_cost"], 0.001),
            "inference_efficiency": benefits["ml_return"] / max(ml_costs["inference_cost"], 0.001)
        }
        
        # Cost optimization opportunities
        optimization_opportunities = await _identify_cost_optimization_opportunities(ml_costs, benefits, performance)
        
        # Risk-adjusted cost analysis
        risk_adjusted_analysis = {
            "sharpe_ratio": performance.get("sharpe_ratio", 0.0),
            "risk_adjusted_return": benefits["ml_return"] / max(performance.get("volatility", 0.01), 0.01),
            "cost_adjusted_sharpe": (benefits["ml_return"] - ml_costs["total_cost"]) / max(performance.get("volatility", 0.01), 0.01),
            "maximum_drawdown_cost": performance.get("max_drawdown", 0.0) * ml_costs["total_cost"]
        }
        
        # Value attribution
        value_attribution = await _calculate_ml_value_attribution(trades, performance)
        
        return {
            "cost_breakdown": ml_costs,
            "benefit_summary": benefits,
            "roi_metrics": roi_metrics,
            "efficiency_metrics": efficiency_metrics,
            "risk_adjusted_analysis": risk_adjusted_analysis,
            "value_attribution": value_attribution,
            "optimization_opportunities": optimization_opportunities,
            "cost_projections": await _project_future_costs(ml_costs, session),
            "benefit_projections": await _project_future_benefits(benefits, session),
            "detailed_analysis": cost_benefit_analysis,
            "recommendations": await _generate_cost_benefit_recommendations(roi_metrics, efficiency_metrics),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating cost-benefit analysis: {e}")
        return {
            "error": str(e),
            "basic_cost_metrics": {
                "total_cost": session.get("performance", {}).get("ml_total_cost", 0.0),
                "total_return": session.get("performance", {}).get("total_return", 0.0),
                "simple_roi": session.get("performance", {}).get("ml_roi", 0.0)
            },
            "analysis_timestamp": datetime.now().isoformat()
        }

async def _generate_improvement_analysis(sessions: List[Dict], baseline_id: Optional[str]) -> Dict:
    return {"placeholder": "Improvement analysis"}

async def _generate_csv_export(session_details: Dict) -> str:
    return "CSV export placeholder"

async def _generate_sessions_summary(sessions: List[Dict], days: int) -> Dict:
    return {"placeholder": "Sessions summary"}

async def _generate_live_metrics(status: Dict) -> Dict:
    return {"placeholder": "Live metrics"}

async def _get_current_positions(status: Dict) -> Dict:
    return {"placeholder": "Current positions"}

async def _get_active_alerts(status: Dict) -> List[Dict]:
    return [{"placeholder": "Active alerts"}]

async def _generate_real_time_performance(status: Dict) -> Dict:
    return {"placeholder": "Real-time performance"}

async def _get_strategy_status(status: Dict) -> Dict:
    return {"placeholder": "Strategy status"}

async def _generate_risk_monitoring(status: Dict) -> Dict:
    return {"placeholder": "Risk monitoring"}

# Placeholder implementations for trade analysis helpers
def _analyze_trades_by_hour(trades: List[Dict]) -> Dict:
    return {"placeholder": "Trades by hour"}

def _analyze_trades_by_day(trades: List[Dict]) -> Dict:
    return {"placeholder": "Trades by day"}

def _calculate_trade_frequency(trades: List[Dict]) -> float:
    return 0.0

def _analyze_strategy_attribution(trades: List[Dict]) -> Dict:
    return {"placeholder": "Strategy attribution"}

def _calculate_trades_per_day(session: Dict, trades: List[Dict]) -> float:
    return 0.0

def _calculate_avg_holding_time(trades: List[Dict]) -> float:
    return 0.0

def _calculate_beta(trades: List[Dict]) -> float:
    return 1.0

def _calculate_alpha(trades: List[Dict]) -> float:
    return 0.0

# Strategy analysis helpers
async def _analyze_weight_evolution(session: Dict) -> Dict:
    return {"placeholder": "Weight evolution"}

async def _calculate_strategy_effectiveness(strategy_performance: Dict) -> Dict:
    return {"placeholder": "Strategy effectiveness"}

async def _calculate_strategy_correlations(session: Dict) -> Dict:
    return {"placeholder": "Strategy correlations"}

async def _analyze_strategy_contributions(strategy_performance: Dict) -> Dict:
    return {"placeholder": "Strategy contributions"}

async def _generate_strategy_optimization_suggestions(
    strategy_performance: Dict, 
    strategy_weights: Dict
) -> List[str]:
    return ["Strategy optimization suggestion"]

# Enhanced helper functions for ML analytics

async def _calculate_basic_strategy_correlations(trades: List[Dict], strategies: List[str]) -> Dict:
    """Calculate basic correlation matrix between strategies"""
    try:
        strategy_returns = {}
        for strategy in strategies:
            strategy_trades = [t for t in trades if t.get("strategy") == strategy]
            returns = [t.get("pnl", 0) for t in strategy_trades]
            strategy_returns[strategy] = returns
        
        correlations = {}
        for i, strategy1 in enumerate(strategies):
            correlations[strategy1] = {}
            for j, strategy2 in enumerate(strategies):
                if i == j:
                    correlations[strategy1][strategy2] = 1.0
                elif len(strategy_returns[strategy1]) > 1 and len(strategy_returns[strategy2]) > 1:
                    # Calculate correlation if both strategies have sufficient data
                    try:
                        corr = np.corrcoef(strategy_returns[strategy1], strategy_returns[strategy2])[0,1]
                        correlations[strategy1][strategy2] = float(corr) if not np.isnan(corr) else 0.0
                    except:
                        correlations[strategy1][strategy2] = 0.0
                else:
                    correlations[strategy1][strategy2] = 0.0
        
        return correlations
    except Exception as e:
        logger.error(f"Error calculating strategy correlations: {e}")
        return {}

async def _calculate_strategy_risk_contribution(strategy_stats: Dict) -> Dict:
    """Calculate risk contribution of each strategy"""
    try:
        total_risk = sum(s.get("volatility", 0.0) * s.get("current_weight", 0.0) for s in strategy_stats.values())
        
        risk_contributions = {}
        for strategy, stats in strategy_stats.items():
            strategy_risk = stats.get("volatility", 0.0) * stats.get("current_weight", 0.0)
            risk_contributions[strategy] = strategy_risk / max(total_risk, 0.001)
        
        return risk_contributions
    except Exception as e:
        logger.error(f"Error calculating risk contribution: {e}")
        return {}

async def _analyze_hourly_performance(trades: List[Dict]) -> Dict:
    """Analyze performance by hour of day"""
    try:
        hourly_pnl = {}
        for trade in trades:
            timestamp = trade.get("timestamp")
            if timestamp:
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                hour = timestamp.hour
                if hour not in hourly_pnl:
                    hourly_pnl[hour] = []
                hourly_pnl[hour].append(trade.get("pnl", 0))
        
        hourly_stats = {}
        for hour, pnls in hourly_pnl.items():
            hourly_stats[hour] = {
                "avg_pnl": sum(pnls) / len(pnls),
                "total_pnl": sum(pnls),
                "trade_count": len(pnls),
                "win_rate": len([p for p in pnls if p > 0]) / len(pnls) if pnls else 0
            }
        
        return hourly_stats
    except Exception as e:
        logger.error(f"Error analyzing hourly performance: {e}")
        return {}

async def _analyze_daily_performance(trades: List[Dict]) -> Dict:
    """Analyze performance by day of week"""
    try:
        daily_pnl = {}
        for trade in trades:
            timestamp = trade.get("timestamp")
            if timestamp:
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                day = timestamp.weekday()  # 0=Monday, 6=Sunday
                if day not in daily_pnl:
                    daily_pnl[day] = []
                daily_pnl[day].append(trade.get("pnl", 0))
        
        day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        daily_stats = {}
        for day, pnls in daily_pnl.items():
            daily_stats[day_names[day]] = {
                "avg_pnl": sum(pnls) / len(pnls),
                "total_pnl": sum(pnls),
                "trade_count": len(pnls),
                "win_rate": len([p for p in pnls if p > 0]) / len(pnls) if pnls else 0
            }
        
        return daily_stats
    except Exception as e:
        logger.error(f"Error analyzing daily performance: {e}")
        return {}

async def _analyze_volatility_impact(trades: List[Dict]) -> Dict:
    """Analyze performance impact of volatility"""
    try:
        # Categorize trades by estimated volatility (simplified approach)
        high_vol_trades = []
        medium_vol_trades = []
        low_vol_trades = []
        
        for trade in trades:
            # Simple volatility estimation based on price movement
            price = trade.get("price", 0)
            value = trade.get("value", 0)
            
            # Estimate volatility as percentage of trade value (simplified)
            estimated_vol = abs(trade.get("pnl", 0)) / max(value, 1) if value > 0 else 0
            
            if estimated_vol > 0.05:  # >5% considered high volatility
                high_vol_trades.append(trade)
            elif estimated_vol > 0.02:  # 2-5% medium volatility
                medium_vol_trades.append(trade)
            else:  # <2% low volatility
                low_vol_trades.append(trade)
        
        def analyze_bucket(bucket_trades, bucket_name):
            if not bucket_trades:
                return {"bucket": bucket_name, "trade_count": 0, "avg_pnl": 0, "win_rate": 0}
            
            pnls = [t.get("pnl", 0) for t in bucket_trades]
            return {
                "bucket": bucket_name,
                "trade_count": len(bucket_trades),
                "avg_pnl": sum(pnls) / len(pnls),
                "total_pnl": sum(pnls),
                "win_rate": len([p for p in pnls if p > 0]) / len(pnls),
                "volatility": _std_dev(pnls) if len(pnls) > 1 else 0
            }
        
        return {
            "high_volatility": analyze_bucket(high_vol_trades, "high"),
            "medium_volatility": analyze_bucket(medium_vol_trades, "medium"),
            "low_volatility": analyze_bucket(low_vol_trades, "low")
        }
    except Exception as e:
        logger.error(f"Error analyzing volatility impact: {e}")
        return {}

async def _calculate_ml_accuracy_by_volatility(trades: List[Dict], volatility_level: str) -> float:
    """Calculate ML accuracy for specific volatility level"""
    try:
        # Filter ML trades by volatility level
        ml_trades = [t for t in trades if "ml" in t.get("strategy", "").lower()]
        
        if volatility_level == "high":
            filtered_trades = [t for t in ml_trades if abs(t.get("pnl", 0)) / max(t.get("value", 1), 1) > 0.05]
        elif volatility_level == "medium":
            filtered_trades = [t for t in ml_trades if 0.02 <= abs(t.get("pnl", 0)) / max(t.get("value", 1), 1) <= 0.05]
        else:  # low
            filtered_trades = [t for t in ml_trades if abs(t.get("pnl", 0)) / max(t.get("value", 1), 1) < 0.02]
        
        if not filtered_trades:
            return 0.0
        
        correct_predictions = len([t for t in filtered_trades if t.get("pnl", 0) > 0])
        return correct_predictions / len(filtered_trades)
        
    except Exception as e:
        logger.error(f"Error calculating ML accuracy by volatility: {e}")
        return 0.0

async def _analyze_trend_performance(trades: List[Dict]) -> Dict:
    """Analyze performance during trending markets"""
    # Simplified trend analysis - in production would use actual market data
    try:
        trending_trades = [t for t in trades if abs(t.get("pnl", 0)) > 0.03 * t.get("value", 1)]
        
        if not trending_trades:
            return {"avg_pnl": 0, "win_rate": 0, "trade_count": 0}
        
        pnls = [t.get("pnl", 0) for t in trending_trades]
        return {
            "avg_pnl": sum(pnls) / len(pnls),
            "win_rate": len([p for p in pnls if p > 0]) / len(pnls),
            "trade_count": len(trending_trades),
            "total_pnl": sum(pnls)
        }
    except Exception as e:
        logger.error(f"Error analyzing trend performance: {e}")
        return {"avg_pnl": 0, "win_rate": 0, "trade_count": 0}

async def _analyze_range_performance(trades: List[Dict]) -> Dict:
    """Analyze performance during range-bound markets"""
    try:
        range_trades = [t for t in trades if abs(t.get("pnl", 0)) <= 0.03 * t.get("value", 1)]
        
        if not range_trades:
            return {"avg_pnl": 0, "win_rate": 0, "trade_count": 0}
        
        pnls = [t.get("pnl", 0) for t in range_trades]
        return {
            "avg_pnl": sum(pnls) / len(pnls),
            "win_rate": len([p for p in pnls if p > 0]) / len(pnls),
            "trade_count": len(range_trades),
            "total_pnl": sum(pnls)
        }
    except Exception as e:
        logger.error(f"Error analyzing range performance: {e}")
        return {"avg_pnl": 0, "win_rate": 0, "trade_count": 0}

async def _calculate_regime_performance(trades: List[Dict], regime: str) -> Dict:
    """Calculate performance for specific market regime"""
    # Simplified regime detection - would use actual market indicators in production
    try:
        if regime == "bull":
            regime_trades = [t for t in trades if t.get("pnl", 0) > 0.01 * t.get("value", 1)]
        elif regime == "bear":
            regime_trades = [t for t in trades if t.get("pnl", 0) < -0.01 * t.get("value", 1)]
        else:  # sideways
            regime_trades = [t for t in trades if abs(t.get("pnl", 0)) <= 0.01 * t.get("value", 1)]
        
        if not regime_trades:
            return {"avg_pnl": 0, "win_rate": 0, "trade_count": 0}
        
        pnls = [t.get("pnl", 0) for t in regime_trades]
        return {
            "avg_pnl": sum(pnls) / len(pnls),
            "win_rate": len([p for p in pnls if p > 0]) / len(pnls),
            "trade_count": len(regime_trades),
            "total_pnl": sum(pnls),
            "sharpe_ratio": sum(pnls) / _std_dev(pnls) if len(pnls) > 1 and _std_dev(pnls) > 0 else 0
        }
    except Exception as e:
        logger.error(f"Error calculating regime performance: {e}")
        return {"avg_pnl": 0, "win_rate": 0, "trade_count": 0}

async def _calculate_conditional_sharpe(trades: List[Dict], condition: str) -> float:
    """Calculate Sharpe ratio under specific conditions"""
    try:
        if condition == "high_volatility":
            filtered_trades = [t for t in trades if abs(t.get("pnl", 0)) / max(t.get("value", 1), 1) > 0.05]
        elif condition == "low_volatility":
            filtered_trades = [t for t in trades if abs(t.get("pnl", 0)) / max(t.get("value", 1), 1) < 0.02]
        elif condition == "trending":
            filtered_trades = [t for t in trades if abs(t.get("pnl", 0)) > 0.03 * t.get("value", 1)]
        elif condition == "ranging":
            filtered_trades = [t for t in trades if abs(t.get("pnl", 0)) <= 0.03 * t.get("value", 1)]
        else:
            filtered_trades = trades
        
        if len(filtered_trades) < 2:
            return 0.0
        
        pnls = [t.get("pnl", 0) for t in filtered_trades]
        mean_return = sum(pnls) / len(pnls)
        std_return = _std_dev(pnls)
        
        return mean_return / std_return if std_return > 0 else 0.0
        
    except Exception as e:
        logger.error(f"Error calculating conditional Sharpe: {e}")
        return 0.0

# Additional helper functions for cost-benefit analysis
async def _calculate_payback_period(cost: float, daily_return: float) -> float:
    """Calculate payback period in days"""
    if daily_return <= 0:
        return float('inf')
    return cost / daily_return

async def _identify_cost_optimization_opportunities(costs: Dict, benefits: Dict, performance: Dict) -> List[str]:
    """Identify cost optimization opportunities"""
    opportunities = []
    
    try:
        if costs["training_cost"] > benefits["ml_return"] * 0.5:
            opportunities.append("Training costs are high relative to returns - consider reducing training frequency")
        
        if costs["cost_per_prediction"] > 0.01:
            opportunities.append("High cost per prediction - optimize inference pipeline")
        
        if performance.get("ml_model_accuracy", 0) < 0.7:
            opportunities.append("Low model accuracy - costs may not be justified, consider model improvements")
        
        if len(opportunities) == 0:
            opportunities.append("Cost structure appears optimized")
        
        return opportunities
    except Exception as e:
        logger.error(f"Error identifying optimization opportunities: {e}")
        return ["Error analyzing optimization opportunities"]

async def _project_future_costs(costs: Dict, session: Dict) -> Dict:
    """Project future costs based on current trends"""
    try:
        # Simple projection based on current session
        session_duration = await _calculate_session_duration(session)
        daily_cost = costs["total_cost"] / max(session_duration, 1)
        
        return {
            "daily_projected_cost": daily_cost,
            "weekly_projected_cost": daily_cost * 7,
            "monthly_projected_cost": daily_cost * 30,
            "cost_trend": "stable"  # Would calculate actual trend in production
        }
    except Exception as e:
        logger.error(f"Error projecting costs: {e}")
        return {"daily_projected_cost": 0, "weekly_projected_cost": 0, "monthly_projected_cost": 0}

async def _project_future_benefits(benefits: Dict, session: Dict) -> Dict:
    """Project future benefits based on current performance"""
    try:
        session_duration = await _calculate_session_duration(session)
        daily_benefit = benefits["ml_return"] / max(session_duration, 1)
        
        return {
            "daily_projected_benefit": daily_benefit,
            "weekly_projected_benefit": daily_benefit * 7,
            "monthly_projected_benefit": daily_benefit * 30,
            "benefit_trend": "stable"  # Would calculate actual trend in production
        }
    except Exception as e:
        logger.error(f"Error projecting benefits: {e}")
        return {"daily_projected_benefit": 0, "weekly_projected_benefit": 0, "monthly_projected_benefit": 0}

async def _calculate_ml_value_attribution(trades: List[Dict], performance: Dict) -> Dict:
    """Calculate value attribution of ML components"""
    try:
        ml_trades = [t for t in trades if "ml" in t.get("strategy", "").lower()]
        traditional_trades = [t for t in trades if "ml" not in t.get("strategy", "").lower()]
        
        ml_pnl = sum(t.get("pnl", 0) for t in ml_trades)
        traditional_pnl = sum(t.get("pnl", 0) for t in traditional_trades)
        total_pnl = ml_pnl + traditional_pnl
        
        return {
            "ml_contribution_pct": ml_pnl / max(total_pnl, 0.001) * 100,
            "traditional_contribution_pct": traditional_pnl / max(total_pnl, 0.001) * 100,
            "ml_pnl": ml_pnl,
            "traditional_pnl": traditional_pnl,
            "total_pnl": total_pnl,
            "ml_trade_ratio": len(ml_trades) / max(len(trades), 1) * 100
        }
    except Exception as e:
        logger.error(f"Error calculating value attribution: {e}")
        return {"ml_contribution_pct": 0, "traditional_contribution_pct": 0}

async def _generate_cost_benefit_recommendations(roi_metrics: Dict, efficiency_metrics: Dict) -> List[str]:
    """Generate cost-benefit recommendations"""
    recommendations = []
    
    try:
        if roi_metrics["net_benefit"] > 0:
            recommendations.append("ML implementation is profitable - consider scaling")
        else:
            recommendations.append("ML implementation currently unprofitable - review costs and model performance")
        
        if roi_metrics["payback_period_days"] < 30:
            recommendations.append("Quick payback period - excellent ROI")
        elif roi_metrics["payback_period_days"] < 90:
            recommendations.append("Reasonable payback period - acceptable ROI")
        else:
            recommendations.append("Long payback period - consider cost optimization")
        
        if efficiency_metrics["prediction_efficiency"] < 1.0:
            recommendations.append("Low prediction efficiency - optimize model inference")
        
        return recommendations if recommendations else ["No specific recommendations available"]
        
    except Exception as e:
        logger.error(f"Error generating recommendations: {e}")
        return ["Error generating recommendations"]

async def _calculate_session_duration(session: Dict) -> float:
    """Calculate session duration in days"""
    try:
        start_time = datetime.fromisoformat(session["start_time"])
        end_time = datetime.fromisoformat(session["end_time"]) if session.get("end_time") else datetime.now()
        duration = end_time - start_time
        return duration.days + duration.seconds / 86400  # Convert to fractional days
    except Exception as e:
        logger.error(f"Error calculating session duration: {e}")
        return 1.0  # Default to 1 day

# Additional placeholder functions for market analysis
async def _compare_weekend_weekday_performance(trades: List[Dict]) -> Dict:
    """Compare weekend vs weekday performance"""
    return {"placeholder": "Weekend vs weekday analysis"}

async def _calculate_volatility_correlation(trades: List[Dict], performance: Dict) -> float:
    """Calculate correlation between volatility and performance"""
    return 0.0

async def _find_optimal_volatility_range(trades: List[Dict]) -> Dict:
    """Find optimal volatility range for trading"""
    return {"optimal_min": 0.02, "optimal_max": 0.05}

async def _calculate_adaptation_speed(trades: List[Dict]) -> float:
    """Calculate how quickly strategies adapt to market changes"""
    return 0.5

async def _calculate_performance_stability(trades: List[Dict]) -> float:
    """Calculate performance stability score"""
    return 0.7

async def _analyze_drawdown_recovery(trades: List[Dict]) -> Dict:
    """Analyze drawdown recovery patterns"""
    return {"avg_recovery_time": 24, "max_recovery_time": 72}

async def _generate_market_recommendations(regime_performance: Dict, volatility_buckets: Dict) -> List[str]:
    """Generate market-based recommendations"""
    return ["Market-based recommendation"]