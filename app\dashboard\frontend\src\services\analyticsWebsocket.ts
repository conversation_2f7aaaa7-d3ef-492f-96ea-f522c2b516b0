/**
 * WebSocket service for real-time analytics updates.
 */

import { WS_URL } from '../config';

// Define event types for analytics
export enum AnalyticsEventType {
  MARKET_STATE = 'market_state',
  STRATEGY_WEIGHTS = 'strategy_weights',
  PERFORMANCE_METRICS = 'performance_metrics',
  ERROR = 'error',
  CONNECTION_STATUS = 'connection_status',
}

// Define event data interfaces
export interface MarketStateEvent {
  timestamp: string;
  regime: string;
  volatility: number;
  trend_strength: number;
}

export interface StrategyWeightsEvent {
  timestamp: string;
  weights: Record<string, number>;
}

export interface PerformanceMetricsEvent {
  timestamp: string;
  metrics: Record<string, any>;
}

export interface ConnectionStatusEvent {
    status: 'connected' | 'disconnected' | 'connecting';
    message: string;
}

export interface ErrorEvent {
    code: string;
    message: string;
}

type AnalyticsEventHandler = (data: any) => void;

class AnalyticsWebSocketService {
  private ws: WebSocket | null = null;
  private eventHandlers: { [key: string]: AnalyticsEventHandler[] } = {};
  private reconnectTimer: number | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 1000; // 1 second

  constructor() {
    this.initializeEventHandlers();
  }

  private initializeEventHandlers() {
    Object.values(AnalyticsEventType).forEach(type => {
      this.eventHandlers[type] = [];
    });
  }

  public connect(): WebSocket {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return this.ws;
    }

    const analyticsWsUrl = `${WS_URL.replace('http', 'ws')}/ws/analytics`;
    this.ws = new WebSocket(analyticsWsUrl);

    this.ws.onopen = this.handleOpen.bind(this);
    this.ws.onmessage = this.handleMessage.bind(this);
    this.ws.onclose = this.handleClose.bind(this);
    this.ws.onerror = this.handleError.bind(this);

    return this.ws;
  }

  public disconnect(): void {
    if (this.reconnectTimer) {
        window.clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
    }
    if (this.ws) {
      this.ws.close(1000, 'User disconnected');
    }
  }

  public addEventListener(eventType: AnalyticsEventType, handler: AnalyticsEventHandler): void {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].push(handler);
    }
  }

  public removeEventListener(eventType: AnalyticsEventType, handler: AnalyticsEventHandler): void {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = this.eventHandlers[eventType].filter(h => h !== handler);
    }
  }

  private handleOpen(): void {
    console.log('Analytics WebSocket connected');
    this.reconnectAttempts = 0;
    const statusEvent: ConnectionStatusEvent = {
        status: 'connected',
        message: 'Analytics WebSocket connected',
    };
    this.notifyHandlers(AnalyticsEventType.CONNECTION_STATUS, statusEvent);
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data);
      if (message.type && this.eventHandlers[message.type]) {
        this.notifyHandlers(message.type, message.data);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log(`Analytics WebSocket closed: ${event.code} - ${event.reason}`);
    const statusEvent: ConnectionStatusEvent = {
        status: 'disconnected',
        message: `Analytics WebSocket closed: ${event.reason || 'Normal closure'}`,
    };
    this.notifyHandlers(AnalyticsEventType.CONNECTION_STATUS, statusEvent);

    if (event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  private handleError(event: Event): void {
    console.error('Analytics WebSocket error:', event);
    const errorEvent: ErrorEvent = {
        code: 'connection_error',
        message: 'Analytics WebSocket connection error',
    };
    this.notifyHandlers(AnalyticsEventType.ERROR, errorEvent);
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts for analytics reached');
      return;
    }

    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
    }

    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);
    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  private notifyHandlers(eventType: AnalyticsEventType, data: any): void {
    const handlers = this.eventHandlers[eventType];
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in ${eventType} handler:`, error);
        }
      });
    }
  }
}

export const analyticsWebsocketService = new AnalyticsWebSocketService();
export default analyticsWebsocketService;
