import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Tab,
  Tabs,
  Card,
  CardContent,
  Button,
  Chip,
  Alert,
  AlertTitle,
  IconButton,
  Tooltip,
  LinearProgress,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  Memory as MemoryIcon,
  Analytics as AnalyticsIcon,
  ModelTraining as ModelTrainingIcon,
  Dashboard as DashboardIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  CloudUpload as DeployIcon,
  History as HistoryIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  <PERSON>atter<PERSON><PERSON>,
  <PERSON>atter,
} from 'recharts';
import ErrorBoundary from '../components/ErrorBoundary';
import LoadingFallback from '../components/LoadingFallback';

// Types
interface MLDashboardData {
  current_model: {
    version: string;
    accuracy: number;
    precision: number;
    recall: number;
    f1_score: number;
    confidence: number;
    last_trained: string;
    deployment_status: 'deployed' | 'staging' | 'failed' | 'training';
    training_cost: number;
    prediction_cost: number;
  };
  training_pipeline: {
    status: 'running' | 'completed' | 'failed' | 'idle';
    current_step: string;
    progress: number;
    eta: string;
    last_run: string;
    total_steps: number;
    current_step_number: number;
  };
  performance_history: {
    timestamps: string[];
    accuracy: number[];
    sharpe_improvement: number[];
    cost_reduction: number[];
    trading_return: number[];
  };
  experiments: {
    id: string;
    name: string;
    status: string;
    accuracy: number;
    created_at: string;
    hyperparameters: Record<string, any>;
  }[];
  model_registry: {
    version: string;
    stage: string;
    accuracy: number;
    created_at: string;
    is_current: boolean;
  }[];
  feature_importance: {
    feature: string;
    importance: number;
    category: string;
  }[];
  cost_analysis: {
    training_cost_history: number[];
    prediction_cost_history: number[];
    cost_savings: number;
    roi: number;
  };
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ml-tabpanel-${index}`}
      aria-labelledby={`ml-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const MLDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [mlData, setMlData] = useState<MLDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Generate mock ML data for demo purposes
  const generateMockMLData = (): MLDashboardData => {
    return {
      current_model: {
        version: 'v2.1.4',
        accuracy: 0.847,
        precision: 0.823,
        recall: 0.891,
        f1_score: 0.856,
        confidence: 0.792,
        last_trained: new Date(Date.now() - 3600000 * 6).toISOString(),
        deployment_status: 'deployed',
        training_cost: 0.0245,
        prediction_cost: 0.0008,
      },
      training_pipeline: {
        status: 'idle',
        current_step: 'Model validation',
        progress: 1.0,
        eta: '0 minutes',
        last_run: new Date(Date.now() - 3600000 * 2).toISOString(),
        total_steps: 8,
        current_step_number: 8,
      },
      performance_history: {
        timestamps: Array.from({ length: 30 }, (_, i) => 
          new Date(Date.now() - (29 - i) * 86400000).toISOString()
        ),
        accuracy: Array.from({ length: 30 }, () => 0.8 + Math.random() * 0.1),
        sharpe_improvement: Array.from({ length: 30 }, () => 0.1 + Math.random() * 0.2),
        cost_reduction: Array.from({ length: 30 }, () => 0.05 + Math.random() * 0.1),
        trading_return: Array.from({ length: 30 }, () => 0.02 + Math.random() * 0.06),
      },
      experiments: [
        {
          id: 'exp_001',
          name: 'Ensemble Weight Optimization v2.1',
          status: 'completed',
          accuracy: 0.847,
          created_at: new Date(Date.now() - 86400000).toISOString(),
          hyperparameters: { learning_rate: 0.001, batch_size: 32 },
        },
        {
          id: 'exp_002',
          name: 'Feature Selection Experiment',
          status: 'running',
          accuracy: 0.832,
          created_at: new Date(Date.now() - 3600000 * 4).toISOString(),
          hyperparameters: { features: 24, regularization: 0.01 },
        },
      ],
      model_registry: [
        {
          version: 'v2.1.4',
          stage: 'Production',
          accuracy: 0.847,
          created_at: new Date(Date.now() - 86400000).toISOString(),
          is_current: true,
        },
        {
          version: 'v2.1.3',
          stage: 'Archived',
          accuracy: 0.834,
          created_at: new Date(Date.now() - 86400000 * 3).toISOString(),
          is_current: false,
        },
      ],
      feature_importance: [
        { feature: 'Price Momentum', importance: 0.23, category: 'Technical' },
        { feature: 'Volume Profile', importance: 0.18, category: 'Technical' },
        { feature: 'Market Volatility', importance: 0.15, category: 'Market' },
        { feature: 'RSI Divergence', importance: 0.12, category: 'Technical' },
        { feature: 'Support/Resistance', importance: 0.11, category: 'Technical' },
        { feature: 'News Sentiment', importance: 0.09, category: 'Fundamental' },
        { feature: 'Order Book Depth', importance: 0.07, category: 'Market' },
        { feature: 'Correlation Matrix', importance: 0.05, category: 'Portfolio' },
      ],
      cost_analysis: {
        training_cost_history: Array.from({ length: 10 }, () => 0.02 + Math.random() * 0.01),
        prediction_cost_history: Array.from({ length: 10 }, () => 0.0005 + Math.random() * 0.0003),
        cost_savings: 0.0156,
        roi: 0.234,
      },
    };
  };

  // Fetch ML dashboard data
  const fetchMLData = useCallback(async () => {
    try {
      setError(null);
      setLoading(true);
      const response = await fetch('/api/ml/dashboard');
      if (!response.ok) {
        throw new Error('Failed to fetch ML dashboard data');
      }
      const data = await response.json();
      setMlData(data);
      setLastUpdate(new Date());
    } catch (err) {
      console.error('ML Dashboard API unavailable, using mock data:', err);
      // Use mock data instead of showing error
      setMlData(generateMockMLData());
      setLastUpdate(new Date());
      // Don't set error state for demo purposes
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    fetchMLData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchMLData, 30000); // 30 seconds
      return () => clearInterval(interval);
    }
  }, [fetchMLData, autoRefresh]);

  // Manual training trigger
  const triggerTraining = async () => {
    try {
      const response = await fetch('/api/ml/training/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          force_retrain: true,
          cost_optimization: true,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to trigger training');
      }
      
      // Refresh data after triggering
      setTimeout(fetchMLData, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger training');
    }
  };

  // Deploy model
  const deployModel = async (version: string) => {
    try {
      const response = await fetch('/api/ml/models/deploy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ model_version: version }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to deploy model');
      }
      
      fetchMLData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deploy model');
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'deployed':
      case 'completed':
        return 'success';
      case 'running':
      case 'training':
        return 'info';
      case 'failed':
        return 'error';
      case 'staging':
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatPercentage = (value: number) => `${(value * 100).toFixed(2)}%`;
  const formatCurrency = (value: number) => `$${value.toFixed(4)}`;

  if (loading) {
    return (
      <LoadingFallback message="Loading ML pipeline data..." />
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <AlertTitle>Error Loading ML Dashboard</AlertTitle>
          {error}
        </Alert>
        <Button onClick={fetchMLData} sx={{ mt: 2 }}>
          Retry
        </Button>
      </Box>
    );
  }

  if (!mlData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">No ML data available</Typography>
      </Box>
    );
  }

  return (
    <ErrorBoundary>
        <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <MemoryIcon />
          ML Dashboard
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
            }
            label="Auto-refresh"
          />
          <Typography variant="body2" color="textSecondary">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </Typography>
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchMLData}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Quick Actions */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<PlayArrowIcon />}
            onClick={triggerTraining}
            disabled={mlData.training_pipeline.status === 'running'}
          >
            Trigger Training
          </Button>
          <Button
            variant="outlined"
            startIcon={<DeployIcon />}
            onClick={() => deployModel(mlData.current_model.version)}
            disabled={mlData.current_model.deployment_status === 'training'}
          >
            Deploy Current Model
          </Button>
          <Chip
            label={`Current Model: ${mlData.current_model.version}`}
            color={getStatusColor(mlData.current_model.deployment_status)}
            variant="outlined"
          />
          <Chip
            label={`Pipeline: ${mlData.training_pipeline.status}`}
            color={getStatusColor(mlData.training_pipeline.status)}
            variant="outlined"
          />
        </Box>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ width: '100%' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="ML dashboard tabs">
          <Tab icon={<DashboardIcon />} label="Overview" />
          <Tab icon={<ModelTrainingIcon />} label="Training" />
          <Tab icon={<AnalyticsIcon />} label="Experiments" />
          <Tab icon={<AssessmentIcon />} label="Performance" />
          <Tab icon={<TimelineIcon />} label="Cost Analysis" />
        </Tabs>

        {/* Overview Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            {/* Current Model Status */}
            <Grid item xs={12} md={6} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Current Model
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Version:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {mlData.current_model.version}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Accuracy:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatPercentage(mlData.current_model.accuracy)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Confidence:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatPercentage(mlData.current_model.confidence)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Status:</Typography>
                      <Chip
                        label={mlData.current_model.deployment_status}
                        color={getStatusColor(mlData.current_model.deployment_status)}
                        size="small"
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Training Pipeline Status */}
            <Grid item xs={12} md={6} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Training Pipeline
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Status:</Typography>
                      <Chip
                        label={mlData.training_pipeline.status}
                        color={getStatusColor(mlData.training_pipeline.status)}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Step:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {mlData.training_pipeline.current_step_number}/{mlData.training_pipeline.total_steps}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" gutterBottom>
                        Progress: {Math.round(mlData.training_pipeline.progress * 100)}%
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={mlData.training_pipeline.progress * 100} 
                      />
                    </Box>
                    {mlData.training_pipeline.status === 'running' && (
                      <Typography variant="body2" color="textSecondary">
                        ETA: {mlData.training_pipeline.eta}
                      </Typography>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Performance Metrics */}
            <Grid item xs={12} md={6} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Performance Metrics
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Precision:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatPercentage(mlData.current_model.precision)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Recall:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatPercentage(mlData.current_model.recall)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">F1 Score:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatPercentage(mlData.current_model.f1_score)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Trading ROI:</Typography>
                      <Typography variant="body2" fontWeight="bold" color="primary.main">
                        {formatPercentage(mlData.cost_analysis.roi)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Performance History Chart */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Model Performance Over Time
                  </Typography>
                  <Box sx={{ width: '100%', height: 400 }}>
                    <ResponsiveContainer>
                      <LineChart data={mlData.performance_history.timestamps.map((timestamp, index) => ({
                        timestamp: new Date(timestamp).toLocaleDateString(),
                        accuracy: mlData.performance_history.accuracy[index],
                        sharpe_improvement: mlData.performance_history.sharpe_improvement[index],
                        trading_return: mlData.performance_history.trading_return[index],
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="timestamp" />
                        <YAxis />
                        <RechartsTooltip />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="accuracy" 
                          stroke="#8884d8" 
                          name="Model Accuracy"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="sharpe_improvement" 
                          stroke="#82ca9d" 
                          name="Sharpe Improvement"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="trading_return" 
                          stroke="#ffc658" 
                          name="Trading Return"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Training Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            {/* Training Status */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Training Pipeline Status
                  </Typography>
                  {mlData.training_pipeline.status === 'running' && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Current Step: {mlData.training_pipeline.current_step}
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={mlData.training_pipeline.progress * 100} 
                      />
                      <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                        {Math.round(mlData.training_pipeline.progress * 100)}% complete - ETA: {mlData.training_pipeline.eta}
                      </Typography>
                    </Box>
                  )}
                  <Typography variant="body2">
                    Last Run: {new Date(mlData.training_pipeline.last_run).toLocaleString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Training Controls */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Training Controls
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<PlayArrowIcon />}
                      onClick={triggerTraining}
                      disabled={mlData.training_pipeline.status === 'running'}
                      fullWidth
                    >
                      Start Training
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<StopIcon />}
                      disabled={mlData.training_pipeline.status !== 'running'}
                      fullWidth
                    >
                      Stop Training
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Model Registry */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Model Registry
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    {mlData.model_registry.map((model, index) => (
                      <Paper key={index} sx={{ p: 2, bgcolor: model.is_current ? 'primary.light' : 'background.paper' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center' }}>
                          <Box>
                            <Typography variant="body1" fontWeight="bold">
                              {model.version}
                              {model.is_current && (
                                <Chip label="Current" color="primary" size="small" sx={{ ml: 1 }} />
                              )}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Accuracy: {formatPercentage(model.accuracy)} | Created: {new Date(model.created_at).toLocaleDateString()}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Chip label={model.stage} color={getStatusColor(model.stage)} size="small" />
                            {!model.is_current && (
                              <Button
                                size="small"
                                variant="outlined"
                                onClick={() => deployModel(model.version)}
                              >
                                Deploy
                              </Button>
                            )}
                          </Box>
                        </Box>
                      </Paper>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Experiments Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    W&B Experiments
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    {mlData.experiments.map((experiment, index) => (
                      <Paper key={index} sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center' }}>
                          <Box>
                            <Typography variant="body1" fontWeight="bold">
                              {experiment.name}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Accuracy: {formatPercentage(experiment.accuracy)} | {new Date(experiment.created_at).toLocaleDateString()}
                            </Typography>
                          </Box>
                          <Chip label={experiment.status} color={getStatusColor(experiment.status)} size="small" />
                        </Box>
                      </Paper>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Performance Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            {/* Feature Importance */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Feature Importance
                  </Typography>
                  <Box sx={{ width: '100%', height: 300 }}>
                    <ResponsiveContainer>
                      <BarChart data={mlData.feature_importance.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="feature" angle={-45} textAnchor="end" height={100} />
                        <YAxis />
                        <RechartsTooltip />
                        <Bar dataKey="importance" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Performance Metrics */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Model Metrics
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {[
                      { label: 'Accuracy', value: mlData.current_model.accuracy },
                      { label: 'Precision', value: mlData.current_model.precision },
                      { label: 'Recall', value: mlData.current_model.recall },
                      { label: 'F1 Score', value: mlData.current_model.f1_score },
                    ].map((metric, index) => (
                      <Box key={index}>
                        <Box sx={{ display: 'flex', justifyContent: 'between', mb: 0.5 }}>
                          <Typography variant="body2">{metric.label}</Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {formatPercentage(metric.value)}
                          </Typography>
                        </Box>
                        <LinearProgress variant="determinate" value={metric.value * 100} />
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Cost Analysis Tab */}
        <TabPanel value={tabValue} index={4}>
          <Grid container spacing={3}>
            {/* Cost Metrics */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Cost Analysis
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Training Cost:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(mlData.current_model.training_cost)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Prediction Cost:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(mlData.current_model.prediction_cost)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">Cost Savings:</Typography>
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        {formatCurrency(mlData.cost_analysis.cost_savings)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'between' }}>
                      <Typography variant="body2">ROI:</Typography>
                      <Typography variant="body2" fontWeight="bold" color="primary.main">
                        {formatPercentage(mlData.cost_analysis.roi)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Cost History Chart */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Cost History
                  </Typography>
                  <Box sx={{ width: '100%', height: 300 }}>
                    <ResponsiveContainer>
                      <LineChart data={mlData.cost_analysis.training_cost_history.map((cost, index) => ({
                        index,
                        training_cost: cost,
                        prediction_cost: mlData.cost_analysis.prediction_cost_history[index],
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="index" />
                        <YAxis />
                        <RechartsTooltip />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="training_cost" 
                          stroke="#8884d8" 
                          name="Training Cost"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="prediction_cost" 
                          stroke="#82ca9d" 
                          name="Prediction Cost"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
        </Box>
      </ErrorBoundary>
  );
};

export default MLDashboard;