#!/usr/bin/env python3
"""
Test Real Binance API Connection
This script tests the actual Binance testnet API connection using credentials from .env
"""

import asyncio
import logging
from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_binance_api():
    """Test real Binance API connection and basic operations"""
    print("=" * 60)
    print("TESTING REAL BINANCE API CONNECTION")
    print("=" * 60)
    
    try:
        # Initialize settings from .env
        settings = Settings()
        print(f"✓ Settings loaded - Using testnet: {settings.use_testnet}")
        print(f"✓ API Key: {settings.binance_api_key[:8]}..." if settings.binance_api_key else "✗ No API key")
        
        # Create Binance client
        binance_client = BinanceExchangeClient(settings)
        print("✓ Binance client created")
        
        # Initialize client connection
        await binance_client._initialize_client()
        print("✓ Binance client initialized")
        
        # Test 1: Get account balance
        print("\n--- Test 1: Account Balance ---")
        balance = await binance_client.get_account_balance()
        print(f"✓ Account balance retrieved: {len(balance)} assets")
        for asset, amount in list(balance.items())[:5]:  # Show first 5 assets
            if float(amount) > 0:
                print(f"  {asset}: {amount}")
        
        # Test 2: Get ticker price
        print("\n--- Test 2: Ticker Price ---")
        ticker = await binance_client.get_ticker("BTCUSDT")
        print(f"✓ BTCUSDT ticker: ${float(ticker['price']):,.2f}")
        
        # Test 3: Get exchange info
        print("\n--- Test 3: Exchange Info ---")
        exchange_info = await binance_client.get_exchange_info()
        symbols = exchange_info.get('symbols', [])
        print(f"✓ Exchange info: {len(symbols)} symbols available")
        
        # Test 4: Get order book
        print("\n--- Test 4: Order Book ---")
        order_book = await binance_client.get_order_book("BTCUSDT", limit=5)
        bids = order_book.get('bids', [])
        asks = order_book.get('asks', [])
        print(f"✓ Order book: {len(bids)} bids, {len(asks)} asks")
        if bids:
            print(f"  Best bid: ${float(bids[0][0]):,.2f}")
        if asks:
            print(f"  Best ask: ${float(asks[0][0]):,.2f}")
        
        # Test 5: Get recent trades
        print("\n--- Test 5: Recent Trades ---")
        trades = await binance_client.get_recent_trades("BTCUSDT", limit=3)
        print(f"✓ Recent trades: {len(trades)} trades")
        for trade in trades[:2]:
            print(f"  Trade: ${float(trade['price']):,.2f} @ {trade['qty']}")
        
        # Test 6: Get open orders (should be empty initially)
        print("\n--- Test 6: Open Orders ---")
        open_orders = await binance_client.get_open_orders("BTCUSDT")
        print(f"✓ Open orders: {len(open_orders)} orders")
        
        # Test 7: Get symbol info
        print("\n--- Test 7: Symbol Info ---")
        symbol_info = await binance_client.get_symbol_info("BTCUSDT")
        if symbol_info:
            print(f"✓ Symbol info: {symbol_info['symbol']} - {symbol_info['status']}")
            print(f"  Base asset: {symbol_info['baseAsset']}")
            print(f"  Quote asset: {symbol_info['quoteAsset']}")
        
        print("\n" + "=" * 60)
        print("✅ ALL BINANCE API TESTS PASSED")
        print("Real Binance testnet API is working correctly!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Binance API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up client connection
        try:
            if binance_client and binance_client.client:
                await binance_client.close_client()
                print("\n✓ Binance client connection closed")
        except Exception as e:
            print(f"\n⚠️  Error closing Binance client: {e}")

if __name__ == "__main__":
    success = asyncio.run(test_real_binance_api())
    exit(0 if success else 1)