from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict, Optional, Any, List
from datetime import timedelta, datetime, timezone
from pydantic import BaseModel
import logging
import asyncio

# Import security functions and models
from app.security import (
    Token,
    create_access_token,
    create_refresh_token,
    get_current_active_user,
    jwt,
    JWTError,
)
# Import necessary modules for authentication
from app.services.auth_service import authenticate_user, User
from app.config.settings import settings
from app.models.api_models import AccountStatsResponse
from app.services.account_service import AccountService
from app.services.execution.trade_state import ManagedTrade

# Setup logging
logger = logging.getLogger(__name__)

# Create API router
router = APIRouter(
    prefix=settings.api_prefix,
    tags=["dashboard-core"],
    responses={404: {"description": "Not found"}},
)

# --- Dependencies ---
def get_account_service(request: Request) -> AccountService:
    """Dependency function to get the AccountService instance."""
    try:
        return request.app.state.account_service
    except AttributeError:
        logger.error("AccountService not found in application state.")
        raise HTTPException(status_code=503, detail="Account service is not available.")

# --- Authentication Endpoints ---

@router.post("/token", response_model=Token, tags=["authentication"])
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    refresh_token = create_refresh_token(
        data={"sub": user.username}
    )
    return {"access_token": access_token, "refresh_token": refresh_token, "token_type": "bearer"}

@router.post("/refresh-token", response_model=Token, tags=["authentication"])
async def refresh_access_token(request: Request):
    # This endpoint would need a body containing the refresh token
    # For simplicity, this is a placeholder for a real implementation.
    raise HTTPException(status_code=501, detail="Refresh token logic not fully implemented.")


# --- Auto-Trading Endpoints ---

@router.get("/trading/status", response_model=Dict[str, bool], tags=["trading-control"])
async def get_auto_trading_status(request: Request, current_user: User = Depends(get_current_active_user)):
    portfolio_manager = request.app.state.portfolio_manager
    is_running = portfolio_manager.is_running()
    logger.info(f"User {current_user.username} requested auto-trading status. Current status: {is_running}")
    return {"enabled": is_running}

@router.post("/trading/enable", response_model=Dict[str, str], tags=["trading-control"])
async def enable_auto_trading(
    req: Request, # Renamed to avoid conflict with `trading_request`
    trading_request: "EnableTradingRequest",
    current_user: User = Depends(get_current_active_user)
):
    portfolio_manager = req.app.state.portfolio_manager
    symbol_to_trade = trading_request.symbol or settings.default_symbol

    if portfolio_manager.is_running():
        return {"message": f"Portfolio management is already running for {portfolio_manager.symbol}."}

    try:
        logger.info(f"User {current_user.username} enabling portfolio management for: {symbol_to_trade}")
        await portfolio_manager.start()
        return {"message": f"Portfolio management enabled for {portfolio_manager.symbol}."}
    except Exception as e:
        logger.error(f"Failed to enable auto-trading: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/trading/disable", response_model=Dict[str, str], tags=["trading-control"])
async def disable_auto_trading(request: Request, current_user: User = Depends(get_current_active_user)):
    portfolio_manager = request.app.state.portfolio_manager
    if not portfolio_manager.is_running():
        return {"message": "Portfolio management is not currently running."}

    try:
        logger.info(f"User {current_user.username} disabling portfolio management for: {portfolio_manager.symbol}")
        await portfolio_manager.stop()
        return {"message": f"Portfolio management for {portfolio_manager.symbol} has been disabled."}
    except Exception as e:
        logger.error(f"Failed to disable auto-trading: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

# --- Trading Data Endpoints ---

@router.get("/trading/active-trades", response_model=List[ManagedTrade], tags=["trading-data"])
async def get_active_trades(request: Request, current_user: User = Depends(get_current_active_user)):
    execution_service = request.app.state.execution_service
    try:
        return await execution_service.get_active_trades()
    except Exception as e:
        logger.error(f"Error fetching active trades: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch active trades.")

@router.get("/trading/recent-trades", response_model=List[ManagedTrade], tags=["trading-data"])
async def get_recent_trades(request: Request, current_user: User = Depends(get_current_active_user)):
    execution_service = request.app.state.execution_service
    try:
        return await execution_service.get_closed_trades()
    except Exception as e:
        logger.error(f"Error fetching recent trades: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch recent trades.")

@router.get("/trading/trade-details/{trade_id}", response_model=ManagedTrade, tags=["trading-data"])
async def get_trade_details(request: Request, trade_id: str, current_user: User = Depends(get_current_active_user)):
    execution_service = request.app.state.execution_service
    try:
        trade = await execution_service.get_trade_by_id(trade_id)
        if trade is None:
            raise HTTPException(status_code=404, detail="Trade not found.")
        return trade
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching trade details for {trade_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch trade details.")

# --- Market Data Endpoints ---

@router.get("/market/ticker", tags=["market-data"])
async def get_market_ticker(request: Request, symbol: str, current_user: User = Depends(get_current_active_user)):
    exchange_client = request.app.state.exchange_client
    try:
        ticker = await exchange_client.get_ticker(symbol)
        return {"symbol": symbol, "price": ticker}
    except Exception as e:
        logger.error(f"Error fetching ticker for {symbol}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch market ticker.")

@router.get("/market/ohlcv", tags=["market-data"])
async def get_ohlcv_data(
    request: Request,
    symbol: str,
    timeframe: str,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
):
    exchange_client = request.app.state.exchange_client
    try:
        return await exchange_client.get_klines(symbol, timeframe, limit)
    except Exception as e:
        logger.error(f"Error fetching OHLCV for {symbol}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch OHLCV data.")

# --- Strategy Endpoints ---

@router.get("/strategies/performance", tags=["strategies"])
async def get_strategy_performance(request: Request, current_user: User = Depends(get_current_active_user)):
    portfolio_manager = request.app.state.portfolio_manager
    if not portfolio_manager.is_running():
        return {"message": "Portfolio management is not running."}
    
    status = portfolio_manager.get_status()
    return {
        "portfolio_status": status,
        "strategy_weights": portfolio_manager.current_weights,
        "active_strategies": status.get('active_strategies', []),
        "market_conditions": status.get('market_conditions', {})
    }

@router.get("/strategies/settings", tags=["strategies"])
async def get_all_strategy_settings(request: Request, current_user: User = Depends(get_current_active_user)):
    portfolio_manager = request.app.state.portfolio_manager
    return {
        "portfolio_config": {
            "symbol": portfolio_manager.symbol,
            "timeframe": portfolio_manager.timeframe,
            "current_weights": portfolio_manager.current_weights,
            "params": portfolio_manager.params
        }
    }

@router.put("/strategies/settings/{strategy_name}", tags=["strategies"])
async def update_strategy_settings(
    request: Request,
    strategy_name: str,
    settings_update: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
):
    portfolio_manager = request.app.state.portfolio_manager
    try:
        # Update portfolio parameters
        if strategy_name == "portfolio":
            portfolio_manager.params.update(settings_update)
            return portfolio_manager.params
        else:
            return {"message": f"Individual strategy settings are managed by portfolio. Current weights: {portfolio_manager.current_weights}"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating settings for {strategy_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to update strategy settings.")

# --- Account Endpoints ---

@router.get("/account/statistics", response_model=AccountStatsResponse, tags=["account-data"])
async def get_account_statistics(
    current_user: User = Depends(get_current_active_user),
    account_service: AccountService = Depends(get_account_service)
):
    try:
        stats = await account_service.get_account_statistics()
        if not stats:
            raise HTTPException(status_code=404, detail="No statistics found.")
        return stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching account statistics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch account statistics.")


# --- Real-time Analytics Endpoints ---

@router.get("/analytics/status", tags=["analytics"])
async def get_analytics_status(current_user: User = Depends(get_current_active_user)):
    """Get the status of the real-time analytics service."""
    try:
        from app.dashboard.api.analytics_websocket import get_analytics_status, is_analytics_running
        
        status = await get_analytics_status()
        return {
            "analytics_service": status,
            "is_running": is_analytics_running(),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error fetching analytics status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch analytics status")

@router.get("/analytics/summary", tags=["analytics"])
async def get_analytics_summary(current_user: User = Depends(get_current_active_user)):
    """Get real-time analytics dashboard summary."""
    try:
        from app.dashboard.api.analytics_websocket import analytics_service
        
        if not analytics_service:
            return {"status": "service_not_initialized", "message": "Analytics service not initialized"}
        
        summary = await analytics_service.get_dashboard_summary()
        return summary
    except Exception as e:
        logger.error(f"Error fetching analytics summary: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch analytics summary")

@router.get("/analytics/performance", tags=["analytics"])
async def get_performance_metrics(current_user: User = Depends(get_current_active_user)):
    """Get current performance metrics."""
    try:
        from app.dashboard.api.analytics_websocket import analytics_service
        
        if not analytics_service or not analytics_service.performance_buffer:
            return {"status": "no_data", "message": "No performance data available"}
        
        # Get latest metrics
        latest_metrics = analytics_service.performance_buffer[-1]
        
        return {
            "timestamp": latest_metrics.timestamp.isoformat(),
            "portfolio_value": latest_metrics.portfolio_value,
            "total_pnl": latest_metrics.total_pnl,
            "daily_pnl": latest_metrics.daily_pnl,
            "sharpe_ratio": latest_metrics.sharpe_ratio,
            "max_drawdown": latest_metrics.max_drawdown,
            "win_rate": latest_metrics.win_rate,
            "execution_metrics": {
                "avg_execution_time_ms": latest_metrics.avg_execution_time_ms,
                "cache_hit_rate": latest_metrics.cache_hit_rate
            },
            "strategy_weights": latest_metrics.strategy_weights,
            "strategy_pnl": latest_metrics.strategy_pnl,
            "market_regime": latest_metrics.market_regime
        }
    except Exception as e:
        logger.error(f"Error fetching performance metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch performance metrics")

@router.get("/analytics/alerts", tags=["analytics"])
async def get_active_alerts(current_user: User = Depends(get_current_active_user)):
    """Get current active alerts."""
    try:
        from app.dashboard.api.analytics_websocket import analytics_service
        
        if not analytics_service:
            return {"alerts": [], "message": "Analytics service not initialized"}
        
        alerts = await analytics_service.supabase.get_unacknowledged_alerts()
        return {"alerts": alerts, "count": len(alerts)}
    except Exception as e:
        logger.error(f"Error fetching alerts: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch alerts")

@router.post("/analytics/alerts/{alert_id}/acknowledge", tags=["analytics"])
async def acknowledge_alert(alert_id: str, current_user: User = Depends(get_current_active_user)):
    """Acknowledge a specific alert."""
    try:
        from app.dashboard.api.analytics_websocket import analytics_service
        
        if not analytics_service:
            raise HTTPException(status_code=503, detail="Analytics service not available")
        
        success = await analytics_service.supabase.acknowledge_alert(alert_id)
        
        if success:
            return {"message": "Alert acknowledged successfully", "alert_id": alert_id}
        else:
            raise HTTPException(status_code=404, detail="Alert not found or already acknowledged")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error acknowledging alert {alert_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to acknowledge alert")


# --- Request Models ---

class EnableTradingRequest(BaseModel):
    symbol: Optional[str] = None