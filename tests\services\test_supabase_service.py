"""
Test suite for Supabase service functionality.
"""

import pytest
import pytest_asyncio
import asyncio
import json
from datetime import datetime
from typing import Dict, Any

import sys
import os
# Add the app directory to the path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(project_root, 'app'))

from app.services.mcp.supabase_service import SupabaseService


class TestSupabaseService:
    """Test Supabase service functionality."""
    
    @pytest_asyncio.fixture
    async def supabase_service(self):
        """Create Supabase service instance."""
        service = SupabaseService()
        yield service
    
    @pytest.mark.asyncio
    async def test_connection(self, supabase_service):
        """Test Supabase connection."""
        result = await supabase_service.test_connection()
        assert result is True
    
    @pytest.mark.asyncio
    async def test_store_portfolio_metrics(self, supabase_service):
        """Test storing portfolio metrics."""
        metrics = {
            'total_pnl': 1250.75,
            'sharpe_ratio': 1.85,
            'max_drawdown': -0.15,
            'win_rate': 0.72,
            'strategy_contributions': {
                'GridStrategy': 450.25,
                'TechnicalAnalysisStrategy': 380.50,
                'TrendFollowingStrategy': 420.00
            },
            'correlation_matrix': {
                'GridStrategy': {'GridStrategy': 1.0, 'TechnicalAnalysisStrategy': 0.3},
                'TechnicalAnalysisStrategy': {'GridStrategy': 0.3, 'TechnicalAnalysisStrategy': 1.0}
            }
        }
        
        record_id = await supabase_service.store_portfolio_metrics(metrics)
        assert record_id is not None
        assert isinstance(record_id, str)
    
    @pytest.mark.asyncio
    async def test_store_trade_execution(self, supabase_service):
        """Test storing trade execution data."""
        trade_data = {
            'strategy_name': 'GridStrategy',
            'symbol': 'BTCUSDT',
            'action': 'BUY',
            'quantity': 0.1,
            'price': 43500.00,
            'timestamp': datetime.now().isoformat(),
            'pnl': 75.25,
            'return_pct': 0.0173,
            'fees': 4.35,
            'confidence': 0.85,
            'weight': 0.33,
            'position_size': 0.25,
            'market_conditions': {
                'volatility': 0.025,
                'volume': 1500000,
                'rsi': 65.5
            }
        }
        
        record_id = await supabase_service.store_trade_execution(trade_data)
        assert record_id is not None
        assert isinstance(record_id, str)
    
    @pytest.mark.asyncio
    async def test_store_strategy_weights(self, supabase_service):
        """Test storing strategy weights."""
        weights = {
            'GridStrategy': 0.35,
            'TechnicalAnalysisStrategy': 0.30,
            'TrendFollowingStrategy': 0.35
        }
        
        metadata = {
            'confidence': 0.92,
            'market_regime': 'trending'
        }
        
        record_id = await supabase_service.store_strategy_weights(weights, metadata)
        assert record_id is not None
        assert isinstance(record_id, str)
    
    @pytest.mark.asyncio
    async def test_store_strategy_performance(self, supabase_service):
        """Test storing strategy performance."""
        performance_data = {
            'pnl': 325.50,
            'return_pct': 0.0285,
            'trades_count': 45,
            'win_rate': 0.68,
            'confidence_score': 0.88
        }
        
        record_id = await supabase_service.store_strategy_performance(
            'GridStrategy', performance_data
        )
        assert record_id is not None
        assert isinstance(record_id, str)
    
    @pytest.mark.asyncio
    async def test_store_alert(self, supabase_service):
        """Test storing alerts."""
        metrics_snapshot = {
            'total_pnl': 1250.75,
            'drawdown': -0.08,
            'volatility': 0.035
        }
        
        record_id = await supabase_service.store_alert(
            'drawdown_warning',
            'medium',
            'Portfolio drawdown exceeded 7% threshold',
            metrics_snapshot
        )
        assert record_id is not None
        assert isinstance(record_id, str)
    
    @pytest.mark.asyncio
    async def test_get_recent_trades(self, supabase_service):
        """Test retrieving recent trades."""
        # First store a trade
        trade_data = {
            'strategy_name': 'TechnicalAnalysisStrategy',
            'symbol': 'ETHUSDT',
            'action': 'SELL',
            'quantity': 2.5,
            'price': 2850.00,
            'timestamp': datetime.now().isoformat(),
            'pnl': 125.75,
            'return_pct': 0.0441,
            'fees': 7.12,
            'confidence': 0.78,
            'weight': 0.30,
            'position_size': 0.15,
            'market_conditions': {'volatility': 0.032}
        }
        
        await supabase_service.store_trade_execution(trade_data)
        
        # Retrieve trades
        trades = await supabase_service.get_recent_trades(limit=10)
        assert isinstance(trades, list)
        # With mock client, we should get the stored trade
        assert len(trades) > 0
    
    @pytest.mark.asyncio
    async def test_get_portfolio_metrics_history(self, supabase_service):
        """Test retrieving portfolio metrics history."""
        # First store metrics
        metrics = {
            'total_pnl': 980.25,
            'sharpe_ratio': 1.72,
            'max_drawdown': -0.12,
            'win_rate': 0.69
        }
        
        await supabase_service.store_portfolio_metrics(metrics)
        
        # Retrieve history
        history = await supabase_service.get_portfolio_metrics_history(hours_back=24)
        assert isinstance(history, list)
        # With mock client, should have stored metrics
        assert len(history) > 0
    
    @pytest.mark.asyncio
    async def test_get_strategy_performance_comparison(self, supabase_service):
        """Test strategy performance comparison."""
        # Store performance for multiple strategies
        strategies = ['GridStrategy', 'TechnicalAnalysisStrategy', 'TrendFollowingStrategy']
        
        for strategy in strategies:
            performance_data = {
                'pnl': 200 + hash(strategy) % 100,
                'return_pct': 0.02 + (hash(strategy) % 10) / 1000,
                'trades_count': 20 + hash(strategy) % 10,
                'win_rate': 0.6 + (hash(strategy) % 20) / 100,
                'confidence_score': 0.8 + (hash(strategy) % 15) / 100
            }
            
            await supabase_service.store_strategy_performance(strategy, performance_data)
        
        # Get comparison
        comparison = await supabase_service.get_strategy_performance_comparison(days_back=7)
        assert isinstance(comparison, dict)
        # With mock client, should have the strategies we stored
        assert len(comparison) > 0
    
    @pytest.mark.asyncio
    async def test_alert_management(self, supabase_service):
        """Test alert storage and acknowledgment."""
        # Store an alert
        record_id = await supabase_service.store_alert(
            'high_volatility',
            'high',
            'Market volatility exceeded 5% threshold',
            {'volatility': 0.055}
        )
        assert record_id is not None
        
        # Get unacknowledged alerts
        alerts = await supabase_service.get_unacknowledged_alerts()
        assert isinstance(alerts, list)
        
        # Acknowledge the alert (with mock client, this tests the method)
        if alerts:
            result = await supabase_service.acknowledge_alert(record_id)
            assert isinstance(result, bool)
    
    @pytest.mark.asyncio
    async def test_database_stats(self, supabase_service):
        """Test database statistics."""
        stats = await supabase_service.get_database_stats()
        assert isinstance(stats, dict)
        # Should have count fields for each table
        expected_fields = ['portfolio_metrics_count', 'trades_count', 'strategy_performance_count']
        for field in expected_fields:
            assert field in stats
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data(self, supabase_service):
        """Test data cleanup functionality."""
        cleanup_stats = await supabase_service.cleanup_old_data(days_to_keep=30)
        assert isinstance(cleanup_stats, dict)
        # Should have deletion counts for cleanable tables
        expected_fields = ['portfolio_metrics_deleted', 'trades_deleted', 'strategy_performance_deleted']
        for field in expected_fields:
            assert field in cleanup_stats
    
    @pytest.mark.asyncio
    async def test_performance_benchmark(self, supabase_service):
        """Test service performance with multiple operations."""
        import time
        
        start_time = time.time()
        
        # Perform multiple operations
        tasks = []
        
        # Store portfolio metrics
        metrics = {'total_pnl': 1000, 'sharpe_ratio': 1.5}
        tasks.append(supabase_service.store_portfolio_metrics(metrics))
        
        # Store trade execution
        trade_data = {
            'strategy_name': 'GridStrategy',
            'symbol': 'BTCUSDT',
            'action': 'BUY',
            'quantity': 0.1,
            'price': 43000,
            'pnl': 50
        }
        tasks.append(supabase_service.store_trade_execution(trade_data))
        
        # Store strategy weights
        weights = {'GridStrategy': 0.4, 'TechnicalAnalysisStrategy': 0.3, 'TrendFollowingStrategy': 0.3}
        tasks.append(supabase_service.store_strategy_weights(weights))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # All operations should succeed
        assert all(result is not None for result in results)
        
        # Should complete quickly (mock operations)
        assert execution_time < 1.0  # Less than 1 second
        
        print(f"Performance test: {len(tasks)} operations completed in {execution_time:.3f}s")


if __name__ == "__main__":
    # Run specific test
    pytest.main([__file__, "-v"])