#!/bin/bash

# Production Docker Build Script for Dynamic Position Optimization System
# Task 2.2.1: Configure Docker containerization

set -e

echo "🚀 Building Production Containers for Dynamic Position Optimization System"
echo "=================================================================="

# Environment setup
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

echo "📦 Build Information:"
echo "   Date: $BUILD_DATE"
echo "   Git Commit: $GIT_COMMIT"
echo ""

# Build base image first
echo "🔨 Building base image..."
docker build \
    --target base \
    --cache-from ensemble-base:latest \
    --tag ensemble-base:latest \
    --tag ensemble-base:$GIT_COMMIT \
    --build-arg BUILD_DATE="$BUILD_DATE" \
    --build-arg GIT_COMMIT="$GIT_COMMIT" \
    .

# Build production application
echo "🔨 Building production application..."
docker build \
    --target production \
    --cache-from ensemble-base:latest \
    --cache-from ensemble-app:latest \
    --tag ensemble-app:latest \
    --tag ensemble-app:$GIT_COMMIT \
    --build-arg BUILD_DATE="$BUILD_DATE" \
    --build-arg GIT_COMMIT="$GIT_COMMIT" \
    .

# Build specialized position optimizer
echo "🔨 Building position optimizer (high-performance)..."
docker build \
    --target position-optimizer \
    --cache-from ensemble-app:latest \
    --cache-from ensemble-position-optimizer:latest \
    --tag ensemble-position-optimizer:latest \
    --tag ensemble-position-optimizer:$GIT_COMMIT \
    --build-arg BUILD_DATE="$BUILD_DATE" \
    --build-arg GIT_COMMIT="$GIT_COMMIT" \
    .

# Build ML training service
echo "🔨 Building ML training service..."
docker build \
    --target ml-training \
    --cache-from ensemble-base:latest \
    --cache-from ensemble-ml-trainer:latest \
    --tag ensemble-ml-trainer:latest \
    --tag ensemble-ml-trainer:$GIT_COMMIT \
    --build-arg BUILD_DATE="$BUILD_DATE" \
    --build-arg GIT_COMMIT="$GIT_COMMIT" \
    .

# Verify builds
echo ""
echo "✅ Verifying built images..."
docker images | grep "ensemble"

# Security scan (if trivy is available)
if command -v trivy &> /dev/null; then
    echo ""
    echo "🔒 Running security scans..."
    
    echo "   Scanning main application..."
    trivy image --exit-code 1 --severity HIGH,CRITICAL ensemble-app:latest || echo "⚠️  Security issues found in main app"
    
    echo "   Scanning position optimizer..."
    trivy image --exit-code 1 --severity HIGH,CRITICAL ensemble-position-optimizer:latest || echo "⚠️  Security issues found in position optimizer"
else
    echo "⚠️  Trivy not found, skipping security scan"
fi

# Performance test of optimized containers
echo ""
echo "⚡ Testing container performance..."

# Quick startup test
echo "   Testing startup time..."
START_TIME=$(date +%s%N)
CONTAINER_ID=$(docker run -d --rm ensemble-position-optimizer:latest)
sleep 2
docker stop $CONTAINER_ID > /dev/null 2>&1
END_TIME=$(date +%s%N)
STARTUP_TIME=$(((END_TIME - START_TIME) / 1000000))  # Convert to milliseconds

if [ $STARTUP_TIME -lt 3000 ]; then
    echo "   ✅ Container startup: ${STARTUP_TIME}ms (Target: <3000ms)"
else
    echo "   ⚠️  Container startup: ${STARTUP_TIME}ms (Exceeds 3000ms target)"
fi

# Image size optimization check
echo ""
echo "📊 Image size analysis:"
docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep ensemble

echo ""
echo "🎯 Production deployment commands:"
echo ""
echo "# Deploy full production stack:"
echo "docker-compose --profile prod up -d"
echo ""
echo "# Deploy with position optimizer:"
echo "docker-compose --profile prod --profile optimizer up -d"
echo ""
echo "# Deploy ML training pipeline:"
echo "docker-compose --profile ml up -d"
echo ""
echo "# Health check commands:"
echo "curl http://localhost/health"
echo "curl http://localhost/health/position-optimizer"
echo ""

# Create deployment summary
cat > deployment-summary.json << EOF
{
    "build_date": "$BUILD_DATE",
    "git_commit": "$GIT_COMMIT",
    "images": [
        "ensemble-base:$GIT_COMMIT",
        "ensemble-app:$GIT_COMMIT", 
        "ensemble-position-optimizer:$GIT_COMMIT",
        "ensemble-ml-trainer:$GIT_COMMIT"
    ],
    "performance": {
        "startup_time_ms": $STARTUP_TIME,
        "target_latency_ms": 100
    },
    "deployment_profiles": [
        "prod",
        "optimizer", 
        "ml"
    ]
}
EOF

echo "✅ Build completed! Deployment summary saved to deployment-summary.json"
echo ""
echo "🚀 Ready for production deployment with Dynamic Position Optimization!"