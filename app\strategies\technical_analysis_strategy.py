"""
Technical Analysis strategy implementation for the Multi-Strategy Crypto Auto Trader.

This strategy uses technical indicators to identify entry and exit points based
on market signals, suitable for trending markets with clear patterns.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Any # Removed List, Tuple
import logging
# import talib # No longer needed directly
# from talib import func # No longer needed directly
import app.utils.technical_analysis as ta
from app.config.settings import Settings # Import Settings

from app.strategies.base_strategy import BaseStrategy


class TechnicalAnalysisStrategy(BaseStrategy):
    """Technical Analysis strategy implementation.

    This strategy uses a combination of technical indicators to identify
    entry and exit points in the market.
    """

    def __init__(self, symbol: str, timeframe: str, settings: Settings):
        """Initialize the Technical Analysis strategy."""
        super().__init__(symbol, timeframe, settings)
        # Custom logger for this specific strategy if needed, otherwise BaseStrategy handles it
        self.logger = logging.getLogger(__name__)

    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market data to determine conditions.

        Args:
            data: DataFrame containing OHLCV data

        Returns:
            Dictionary containing market analysis results
        """
        result = {}
        df = data.copy()

        # Calculate ADX
        df['ADX'] = ta.calculate_adx(df['high'], df['low'], df['close'], timeperiod=14)
        result['adx'] = df['ADX'].iloc[-1]
        result['is_suitable_trend'] = 20 < result['adx'] < 25

        # Calculate ATR
        df['ATR'] = ta.calculate_atr_volatility(df, window=self.settings.atr_periods)
        result['atr'] = df['ATR'].iloc[-1]
        result['volatility_ratio'] = result['atr'] / df['close'].iloc[-1] if df['close'].iloc[-1] != 0 else 0
        result['is_suitable_volatility'] = result['volatility_ratio'] >= 0.008

        # Calculate RSI
        df['RSI'] = ta.calculate_rsi(df['close'], timeperiod=self.settings.ta_rsi_period)
        result['rsi'] = df['RSI'].iloc[-1]
        result['is_overbought'] = result['rsi'] > self.settings.ta_rsi_overbought
        result['is_oversold'] = result['rsi'] < self.settings.ta_rsi_oversold

        # Calculate MACD
        macd_results = ta.calculate_macd(
            df['close'],
            fastperiod=self.settings.ta_macd_fast,
            slowperiod=self.settings.ta_macd_slow,
            signalperiod=self.settings.ta_macd_signal
        )
        df['MACD'] = macd_results['macd']
        df['MACD_SIGNAL'] = macd_results['macd_signal']

        result['macd'] = df['MACD'].iloc[-1]
        result['macd_signal'] = df['MACD_SIGNAL'].iloc[-1]
        result['macd_histogram'] = result['macd'] - result['macd_signal']

        # MACD crossing signal line
        if len(df) > 1:
            prev_macd = df['MACD'].iloc[-2]
            prev_signal = df['MACD_SIGNAL'].iloc[-2]
            result['macd_cross_above'] = prev_macd <= prev_signal and result['macd'] > result['macd_signal']
            result['macd_cross_below'] = prev_macd >= prev_signal and result['macd'] < result['macd_signal']
        else:
            result['macd_cross_above'] = False
            result['macd_cross_below'] = False

        # Calculate EMAs
        df['EMA_SHORT'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_short)
        df['EMA_MEDIUM'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_medium)
        df['EMA_LONG'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_long)

        result['ema_short'] = df['EMA_SHORT'].iloc[-1]
        result['ema_medium'] = df['EMA_MEDIUM'].iloc[-1]
        result['ema_long'] = df['EMA_LONG'].iloc[-1]

        # EMA alignment (determine trend direction)
        if result['ema_short'] > result['ema_medium'] > result['ema_long']:
            result['ema_trend'] = 'bullish'
        elif result['ema_short'] < result['ema_medium'] < result['ema_long']:
            result['ema_trend'] = 'bearish'
        else:
            result['ema_trend'] = 'mixed'

        # EMA crossovers
        if len(df) > 1:
            prev_short = df['EMA_SHORT'].iloc[-2]
            prev_medium = df['EMA_MEDIUM'].iloc[-2]
            result['ema_cross_above'] = prev_short <= prev_medium and result['ema_short'] > result['ema_medium']
            result['ema_cross_below'] = prev_short >= prev_medium and result['ema_short'] < result['ema_medium']
        else:
            result['ema_cross_above'] = False
            result['ema_cross_below'] = False

        # Calculate Bollinger Bands
        bbands_results = ta.calculate_bollinger_bands(
            df['close'],
            timeperiod=self.settings.ta_bollinger_period,
            nbdevup=self.settings.ta_bollinger_stddev,
            nbdevdn=self.settings.ta_bollinger_stddev
        )
        df['BB_UPPER'] = bbands_results['upper']
        df['BB_MIDDLE'] = bbands_results['middle']
        df['BB_LOWER'] = bbands_results['lower']

        result['bb_upper'] = df['BB_UPPER'].iloc[-1]
        result['bb_middle'] = df['BB_MIDDLE'].iloc[-1]
        result['bb_lower'] = df['BB_LOWER'].iloc[-1]

        # Price relative to Bollinger Bands
        current_price = df['close'].iloc[-1]
        result['price_above_upper_band'] = current_price > result['bb_upper'] if result['bb_upper'] != 0.0 else False
        result['price_below_lower_band'] = current_price < result['bb_lower'] if result['bb_lower'] != 0.0 else False
        result['price_near_middle_band'] = (
            abs(current_price - result['bb_middle']) / result['bb_middle'] < 0.005 if result['bb_middle'] != 0.0 else False
        )

        # Volume analysis
        if 'volume' in df.columns:
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume_ma'].iloc[-1]

            result['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 0
            result['is_high_volume'] = result['volume_ratio'] > 1.5
        else:
            result['volume_ratio'] = 1.0
            result['is_high_volume'] = False

        # Count bullish and bearish signals
        bullish_signals = 0
        bearish_signals = 0

        # RSI signals
        if result['is_oversold']:
            bullish_signals += 1
        elif result['is_overbought']:
            bearish_signals += 1

        # MACD signals
        if result['macd_cross_above']:
            bullish_signals += 1
        elif result['macd_cross_below']:
            bearish_signals += 1

        # EMA signals
        if result['ema_trend'] == 'bullish' or result['ema_cross_above']:
            bullish_signals += 1
        elif result['ema_trend'] == 'bearish' or result['ema_cross_below']:
            bearish_signals += 1

        # Bollinger Band signals
        if result['price_below_lower_band']:
            bullish_signals += 1
        elif result['price_above_upper_band']:
            bearish_signals += 1

        result['bullish_signals'] = bullish_signals
        result['bearish_signals'] = bearish_signals

        # Overall suitability
        threshold = self.settings.ta_signal_threshold
        result['has_clear_signal'] = bullish_signals >= threshold or bearish_signals >= threshold
        result['is_suitable'] = (
            result['is_suitable_trend'] and
            result['is_suitable_volatility'] and
            result['has_clear_signal']
        )

        return result

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on technical indicators.

        Args:
            data: DataFrame containing OHLCV data

        Returns:
            DataFrame with signals added
        """
        # Make a copy of the data to avoid modifying the original
        df = data.copy()

        # Ensure correct types for TA-Lib
        df['open'] = df['open'].astype(np.float64)
        df['high'] = df['high'].astype(np.float64)
        df['low'] = df['low'].astype(np.float64)
        df['close'] = df['close'].astype(np.float64)
        if 'volume' in df.columns:
            df['volume'] = df['volume'].astype(np.float64)

        # Initialize signal column
        df['signal'] = 0

        # Run market analysis first to get overall conditions
        market_analysis = self.analyze_market(df)

        # Create input arrays for talib functions
        close_prices = df['close'].values

        # Calculate indicators needed for signal generation
        df['RSI'] = ta.calculate_rsi(df['close'], timeperiod=self.settings.ta_rsi_period)

        # MACD using utility function
        macd_results_sig = ta.calculate_macd(
            df['close'],
            fastperiod=self.settings.ta_macd_fast,
            slowperiod=self.settings.ta_macd_slow,
            signalperiod=self.settings.ta_macd_signal
        )
        df['MACD'] = macd_results_sig['macd']
        df['MACD_SIGNAL'] = macd_results_sig['macd_signal']

        # Bollinger Bands using utility function
        bbands_results_sig = ta.calculate_bollinger_bands(
            df['close'],
            timeperiod=self.settings.ta_bollinger_period,
            nbdevup=self.settings.ta_bollinger_stddev,
            nbdevdn=self.settings.ta_bollinger_stddev
        )
        df['BB_UPPER'] = bbands_results_sig['upper']
        df['BB_MIDDLE'] = bbands_results_sig['middle']
        df['BB_LOWER'] = bbands_results_sig['lower']

        # EMAs (Keeping direct talib import for now)
        # Keep direct EMA import as it's not in utils
        from talib import EMA
        df['EMA_SHORT'] = EMA(close_prices, timeperiod=self.settings.tf_ema_short)
        df['EMA_MEDIUM'] = EMA(close_prices, timeperiod=self.settings.tf_ema_medium)
        # df['EMA_LONG'] = EMA(close_prices, timeperiod=self.settings.tf_ema_long) # Not directly used in signal loop

        # Threshold for signal confirmation
        threshold = self.settings.ta_signal_threshold

        # Generate signals for each candle
        # Start from a point where indicators are likely calculated (e.g., 50)
        start_index = max(50, self.settings.ta_macd_slow, self.settings.ta_bollinger_period) # Use settings
        for i in range(start_index, len(df)):
            # Reset signals for this candle
            bullish_signals = 0
            bearish_signals = 0

            # Check for NaN values before accessing iloc[i]
            if np.isnan(df['RSI'].iloc[i]) or \
               np.isnan(df['MACD'].iloc[i]) or np.isnan(df['MACD_SIGNAL'].iloc[i]) or \
               np.isnan(df['BB_LOWER'].iloc[i]) or np.isnan(df['BB_UPPER'].iloc[i]) or \
               np.isnan(df['EMA_SHORT'].iloc[i]) or np.isnan(df['EMA_MEDIUM'].iloc[i]) or \
               np.isnan(df['MACD'].iloc[i-1]) or np.isnan(df['MACD_SIGNAL'].iloc[i-1]) or \
               np.isnan(df['EMA_SHORT'].iloc[i-1]) or np.isnan(df['EMA_MEDIUM'].iloc[i-1]):
                df.loc[df.index[i], 'signal'] = df['signal'].iloc[i-1] # Maintain previous if NaN
                continue # Skip this candle if indicators are NaN

            # RSI signals
            if df['RSI'].iloc[i] < self.settings.ta_rsi_oversold: # Use settings
                bullish_signals += 1
            elif df['RSI'].iloc[i] > self.settings.ta_rsi_overbought: # Use settings
                bearish_signals += 1

            # MACD signals
            if df['MACD'].iloc[i] > df['MACD_SIGNAL'].iloc[i] and df['MACD'].iloc[i-1] <= df['MACD_SIGNAL'].iloc[i-1]:
                bullish_signals += 1
            elif df['MACD'].iloc[i] < df['MACD_SIGNAL'].iloc[i] and df['MACD'].iloc[i-1] >= df['MACD_SIGNAL'].iloc[i-1]:
                bearish_signals += 1

            # Bollinger Band signals
            if df['close'].iloc[i] < df['BB_LOWER'].iloc[i]:
                bullish_signals += 1
            elif df['close'].iloc[i] > df['BB_UPPER'].iloc[i]:
                bearish_signals += 1

            # EMA crossover signals
            if df['EMA_SHORT'].iloc[i] > df['EMA_MEDIUM'].iloc[i] and df['EMA_SHORT'].iloc[i-1] <= df['EMA_MEDIUM'].iloc[i-1]:
                bullish_signals += 1
            elif df['EMA_SHORT'].iloc[i] < df['EMA_MEDIUM'].iloc[i] and df['EMA_SHORT'].iloc[i-1] >= df['EMA_MEDIUM'].iloc[i-1]:
                bearish_signals += 1

            # Generate signal if threshold is met
            if bullish_signals >= threshold and df['signal'].iloc[i-1] <= 0:
                df.loc[df.index[i], 'signal'] = 1
            elif bearish_signals >= threshold and df['signal'].iloc[i-1] >= 0:
                df.loc[df.index[i], 'signal'] = -1
            else:
                df.loc[df.index[i], 'signal'] = df['signal'].iloc[i-1]  # Maintain previous signal

        # Ensure the last signal is consistent (optional, might cause lookahead bias if not careful)
        # Re-evaluate the last candle based on the final indicator values if needed
        # For simplicity, we rely on the loop's last calculation.

        return df

    def calculate_risk_params(self,
                             data: pd.DataFrame,
                             entry_price: float,
                             position_type: str) -> Dict[str, float]:
        """Calculate risk parameters for a trade.

        Args:
            data: DataFrame containing OHLCV data
            entry_price: The entry price for the trade
            position_type: 'long' or 'short'

        Returns:
            Dictionary containing stop loss and take profit levels
        """
        # Ensure correct types
        high_prices = data['high'].values.astype(np.float64)
        low_prices = data['low'].values.astype(np.float64)
        close_prices = data['close'].values.astype(np.float64)

        # Calculate ATR for dynamic stop loss
        if 'ATR' in data.columns and not data['ATR'].isnull().all():
             # Use already calculated ATR if available and not NaN
             atr = data['ATR'].iloc[-1]
             if np.isnan(atr):
                 # Recalculate using utility function if last value is NaN
                 atr_series = ta.calculate_atr(data['high'], data['low'], data['close'], timeperiod=self.settings.atr_periods) # Use settings (Corrected attribute)
                 atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0
        else:
            # Calculate using utility function if not present in input data
            atr_series = ta.calculate_atr(data['high'], data['low'], data['close'], timeperiod=self.settings.atr_periods) # Use settings (Corrected attribute)
            atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0

        # Get risk multiple based on strategy
        atr_multiplier = self.settings.atr_multiplier # Use settings

        # Calculate stop loss based on ATR
        if position_type == 'long':
            stop_loss = entry_price - (atr * atr_multiplier)
            # For take profit, we use a risk-reward ratio of 1:2
            risk = entry_price - stop_loss
            take_profit = entry_price + (risk * 2)
        else:  # short
            stop_loss = entry_price + (atr * atr_multiplier)
            # For take profit, we use a risk-reward ratio of 1:2
            risk = stop_loss - entry_price
            take_profit = entry_price - (risk * 2)

        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_reward_ratio': 2.0,
            'position_size_factor': 1.0  # Full position size for TA strategies
        }

    def calculate_score(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate the strategy score based on current market conditions.

        Args:
            market_conditions: Dictionary containing market conditions

        Returns:
            Strategy score (0-100)
        """
        # Start with base score
        score = 50
        self.logger.debug(f"TA Score Start: {score}")

        # ADX factor: moderate trend strength is ideal
        adx = market_conditions.get('adx', 25)
        if 20 < adx < 25:
            score += 20
        else:
            score -= min(20, abs(adx - 22.5) * 2)
        self.logger.debug(f"TA Score after ADX ({adx:.2f}): {score}")

        # Signal alignment
        bullish_signals = market_conditions.get('bullish_signals', 0)
        bearish_signals = market_conditions.get('bearish_signals', 0)

        # Strong signal in either direction is good
        max_signals = max(bullish_signals, bearish_signals)
        threshold = self.settings.ta_signal_threshold # Use settings

        if max_signals >= threshold:
            signal_score = min(30, (max_signals - threshold + 1) * 10)
            score += signal_score
        self.logger.debug(f"TA Score after Signals ({max_signals}): {score}")

        # Volatility factor: medium-high volatility is good
        volatility_ratio = market_conditions.get('volatility_ratio', 0.01)
        if volatility_ratio >= 0.008:
            score += min(20, (volatility_ratio - 0.008) * 1000)
        else:
            score -= min(20, (0.008 - volatility_ratio) * 1000)
        self.logger.debug(f"TA Score after Volatility ({volatility_ratio:.4f}): {score}")

        # Volume confirmation
        volume_ratio = market_conditions.get('volume_ratio', 1.0)
        if volume_ratio > 1.2:
            score += min(15, (volume_ratio - 1.2) * 20)
        self.logger.debug(f"TA Score after Volume ({volume_ratio:.2f}): {score}")

        # Ensure score is between 0 and 100
        return max(0, min(100, score))

    async def execute(self, data: pd.DataFrame) -> None:
        """Execute the strategy on the given data.

        This method is called by the strategy selector to execute the strategy.

        Args:
            data: DataFrame containing OHLCV data
        """
        try:
            self.logger.info(f"Executing {self.name} strategy for {self.symbol}")

            # Generate signals
            signals_df = self.generate_signals(data)

            # Get the latest signal
            latest_signal = signals_df['signal'].iloc[-1] if not signals_df.empty else 0

            # Log the signal
            self.logger.info(f"{self.name} signal for {self.symbol}: {latest_signal}")

            # TODO: Implement actual trade execution based on the signal
            # This would involve calling the execution handler to place orders

        except Exception as e:
            self.logger.error(f"Error executing {self.name} strategy: {e}", exc_info=True)