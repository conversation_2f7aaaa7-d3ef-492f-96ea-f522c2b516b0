# W&B key, optional, running `wandb login` will also save the key to your .netrc file
# WANDB_API_KEY=

# W&B logs settings (optional)
# MCP_LOGS_WANDB_ENTITY=
MCP_LOGS_WANDB_PROJECT=wandb-mcp-logs

# Disable Code sandbox (optional)
# DISABLE_CODE_SANDBOX=1  # set to 1 to complete disable code sandbox, both local and hosted

# Login for E2B (optional)
# E2B_API_KEY=

# Packages for E2B installation security (optional)
# E2B_PACKAGE_ALLOWLIST=numpy,pandas,matplotlib,scipy,scikit-learn
# E2B_PACKAGE_DENYLIST=subprocess32,psutil,pyautogui,pynput

# E2B execution cache settings (optional)
# E2B_CACHE_TTL_SECONDS=900  # Cache TTL in seconds (default: 900 = 15 minutes)

# E2B sandbox lifetime settings (optional)
# E2B_SANDBOX_TIMEOUT_SECONDS=900  # Sandbox lifetime in seconds (default: 900 = 15 minutes)

# Sandbox rate limiting settings (optional)
# SANDBOX_RATE_LIMIT_REQUESTS=100  # Max requests per window (default: 100)
# SANDBOX_RATE_LIMIT_WINDOW_SECONDS=60  # Rate limit window in seconds (default: 60)

WEAVE_TRACE_SERVER_URL=https://trace.wandb.ai
WANDB_BASE_URL=https://api.wandb.ai
WANDBOT_BASE_URL=https://wandbot.replit.app
DISABLE_WANDBOT_SUPPORT_BOT_TOOL=false
MCP_SERVER_LOG_LEVEL="WARNING"
