# Product Context: [Project Name]

## 1. Core Problem & User Need
- **Problem Statement**: (What specific problem is this project solving?)
- **Target Users**: (Who are the primary users? Describe their personas if known.)
- **User Needs Met**: (How does this project address the users' needs?)

## 2. Product Vision & Goals
- **Product Vision**: (What is the long-term vision for this product?)
- **Key Goals/Objectives**: (What are the measurable goals for this iteration/version?)
- **Success Metrics**: (How will success be measured?)

## 3. Key Features & Scope
- **Core Features**: (List the essential features.)
  - Feature 1: Description
  - Feature 2: Description
- **Out of Scope (for now)**: (What features are explicitly not being built at this stage?)

## 4. Competitive Landscape (Optional)
- **Key Competitors**: (Who are they?)
- **Differentiation**: (How is this product different or better?)

## 5. Assumptions & Risks
- **Key Assumptions**: (What assumptions are being made?)
- **Potential Risks**: (What are the known risks? e.g., technical, market, resource)
