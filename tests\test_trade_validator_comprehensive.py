"""
Comprehensive test suite for TradeValidator functionality.
Tests all validation methods and edge cases.
"""
import pytest
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.utils.trade_validator import TradeValidator, TradeValidationError, TradeSide
from app.models.trade_status import TradeStatus


class TestTradeDataValidation:
    """Test complete trade data validation."""
    
    def test_valid_trade_data(self):
        """Test validation passes for valid trade data."""
        valid_trade = {
            'symbol': 'BTCUSDT',
            'side': 'BUY',
            'quantity': 0.001,
            'price': 50000.0,
            'timestamp': datetime.now()
        }
        
        # Should not raise any exception
        TradeValidator.validate_trade_data(valid_trade)
    
    def test_missing_required_fields(self):
        """Test validation fails for missing required fields."""
        incomplete_trade = {
            'symbol': 'BTCUSDT',
            'side': 'BUY',
            # missing quantity, price, timestamp
        }
        
        with pytest.raises(TradeValidationError) as exc_info:
            TradeValidator.validate_trade_data(incomplete_trade)
        
        assert "Missing required fields" in str(exc_info.value)
        assert "quantity" in str(exc_info.value)
        assert "price" in str(exc_info.value)


class TestSymbolValidation:
    """Test trading symbol validation."""
    
    def test_valid_symbols(self):
        """Test validation passes for valid symbols."""
        valid_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        for symbol in valid_symbols:
            TradeValidator.validate_symbol(symbol)
    
    def test_invalid_symbols(self):
        """Test validation fails for invalid symbols."""
        invalid_symbols = [
            '',           # Empty
            'BTC',        # Too short
            'btcusdt',    # Lowercase
            None,         # None
            123,          # Not string
        ]
        
        for symbol in invalid_symbols:
            with pytest.raises(TradeValidationError):
                TradeValidator.validate_symbol(symbol)


class TestSideValidation:
    """Test trade side validation."""
    
    def test_valid_sides(self):
        """Test validation passes for valid sides."""
        valid_sides = ['BUY', 'SELL', TradeSide.BUY, TradeSide.SELL]
        
        for side in valid_sides:
            TradeValidator.validate_side(side)
    
    def test_invalid_sides(self):
        """Test validation fails for invalid sides."""
        invalid_sides = ['LONG', 'SHORT', 'invalid', 123, None]
        
        for side in invalid_sides:
            with pytest.raises(TradeValidationError):
                TradeValidator.validate_side(side)
    
    def test_case_insensitive_sides(self):
        """Test validation accepts case-insensitive valid sides."""
        case_insensitive_sides = ['buy', 'sell', 'Buy', 'Sell', 'BUY', 'SELL']
        
        for side in case_insensitive_sides:
            # Should not raise any exception
            TradeValidator.validate_side(side)


class TestQuantityValidation:
    """Test trade quantity validation."""
    
    def test_valid_quantities(self):
        """Test validation passes for valid quantities."""
        test_cases = [
            ('BTCUSDT', 0.001),
            ('ETHUSDT', 0.01),
            ('ADAUSDT', 10.0),
        ]
        
        for symbol, quantity in test_cases:
            TradeValidator.validate_quantity(symbol, quantity)
    
    def test_invalid_quantities(self):
        """Test validation fails for invalid quantities."""
        test_cases = [
            ('BTCUSDT', 0.0),      # Zero quantity
            ('BTCUSDT', -0.001),   # Negative quantity
            ('BTCUSDT', 0.000001), # Below minimum
        ]
        
        for symbol, quantity in test_cases:
            with pytest.raises(TradeValidationError):
                TradeValidator.validate_quantity(symbol, quantity)


class TestPriceValidation:
    """Test price validation."""
    
    def test_valid_prices(self):
        """Test validation passes for valid prices."""
        test_cases = [
            ('BTCUSDT', 50000.25),
            ('ETHUSDT', 3000.50),
            ('ADAUSDT', 1.25000),
        ]
        
        for symbol, price in test_cases:
            TradeValidator.validate_price(symbol, price)
    
    def test_invalid_prices(self):
        """Test validation fails for invalid prices."""
        test_cases = [
            ('BTCUSDT', 0.0),        # Zero price
            ('BTCUSDT', -50000.0),   # Negative price
            ('BTCUSDT', 50000.123),  # Too many decimal places
        ]
        
        for symbol, price in test_cases:
            with pytest.raises(TradeValidationError):
                TradeValidator.validate_price(symbol, price)


class TestNotionalValidation:
    """Test notional value validation."""
    
    def test_valid_notional_values(self):
        """Test validation passes for valid notional values."""
        test_cases = [
            (0.001, 50000.0),  # $50 notional
            (0.1, 200.0),      # $20 notional
            (1.0, 15.0),       # $15 notional
        ]
        
        for quantity, price in test_cases:
            TradeValidator.validate_notional_value(quantity, price)
    
    def test_invalid_notional_values(self):
        """Test validation fails for insufficient notional values."""
        test_cases = [
            (0.0001, 50000.0),  # $5 notional (below $10 minimum)
            (0.001, 5000.0),    # $5 notional
            (0.5, 15.0),        # $7.50 notional
        ]
        
        for quantity, price in test_cases:
            with pytest.raises(TradeValidationError):
                TradeValidator.validate_notional_value(quantity, price)


class TestStopLossTakeProfitValidation:
    """Test SL/TP validation."""
    
    def test_valid_long_position_levels(self):
        """Test validation passes for valid long position SL/TP levels."""
        entry_price = 50000.0
        
        # Valid long position: SL below entry, TP above entry
        TradeValidator.validate_stop_loss_take_profit(
            side='BUY',
            entry_price=entry_price,
            stop_loss=49000.0,
            take_profit=52000.0
        )
    
    def test_valid_short_position_levels(self):
        """Test validation passes for valid short position SL/TP levels."""
        entry_price = 50000.0
        
        # Valid short position: SL above entry, TP below entry
        TradeValidator.validate_stop_loss_take_profit(
            side='SELL',
            entry_price=entry_price,
            stop_loss=51000.0,
            take_profit=48000.0
        )
    
    def test_invalid_long_position_levels(self):
        """Test validation fails for invalid long position SL/TP levels."""
        entry_price = 50000.0
        
        # Invalid long position: SL above entry
        with pytest.raises(TradeValidationError):
            TradeValidator.validate_stop_loss_take_profit(
                side='BUY',
                entry_price=entry_price,
                stop_loss=51000.0,
                take_profit=52000.0
            )
        
        # Invalid long position: TP below entry
        with pytest.raises(TradeValidationError):
            TradeValidator.validate_stop_loss_take_profit(
                side='BUY',
                entry_price=entry_price,
                stop_loss=49000.0,
                take_profit=48000.0
            )


class TestStatusTransitionValidation:
    """Test trade status transition validation."""
    
    def test_valid_transitions(self):
        """Test validation passes for valid status transitions."""
        valid_transitions = [
            (TradeStatus.PENDING_ENTRY, TradeStatus.ENTRY_FILLED),
            (TradeStatus.ENTRY_FILLED, TradeStatus.SLTP_PLACED),
            (TradeStatus.SLTP_PLACED, TradeStatus.ACTIVE),
            (TradeStatus.ACTIVE, TradeStatus.CLOSED_TP),
        ]
        
        for current, new in valid_transitions:
            TradeValidator.validate_status_transition(current, new)
    
    def test_invalid_transitions(self):
        """Test validation fails for invalid status transitions."""
        invalid_transitions = [
            (TradeStatus.PENDING_ENTRY, TradeStatus.ACTIVE),     # Skip states
            (TradeStatus.CLOSED_TP, TradeStatus.ACTIVE),         # From terminal state
            (TradeStatus.ENTRY_FILLED, TradeStatus.CLOSED_TP),   # Skip SLTP_PLACED
        ]
        
        for current, new in invalid_transitions:
            with pytest.raises(TradeValidationError):
                TradeValidator.validate_status_transition(current, new)
    
    def test_terminal_status_detection(self):
        """Test terminal status detection."""
        terminal_statuses = [
            TradeStatus.CLOSED_TP,
            TradeStatus.CLOSED_SL,
            TradeStatus.CLOSED_MANUAL,
            TradeStatus.ERROR
        ]
        
        non_terminal_statuses = [
            TradeStatus.PENDING_ENTRY,
            TradeStatus.ENTRY_FILLED,
            TradeStatus.SLTP_PLACED,
            TradeStatus.ACTIVE
        ]
        
        for status in terminal_statuses:
            assert TradeValidator.is_terminal_status(status)
        
        for status in non_terminal_statuses:
            assert not TradeValidator.is_terminal_status(status)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])