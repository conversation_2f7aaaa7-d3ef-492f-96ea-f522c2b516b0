import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Grid,
  Tab,
  Tabs,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Divider,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Speed as SpeedIcon,
  Assessment as AssessmentIcon,
  PsychologyAlt as PsychologyAltIcon,
  Security as SecurityIcon,
  Timeline as TimelineIcon,
  ShowChart as ShowChartIcon,
  AccountBalance as AccountBalanceIcon,
  FileDownload as FileDownloadIcon
} from '@mui/icons-material';
import {
  <PERSON><PERSON>hart,
  Line,
  AreaChart,
  Area,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>G<PERSON>,
  <PERSON>ltip as <PERSON><PERSON>rts<PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON>ontainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Scatter<PERSON>hart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend
} from 'recharts';
import { sessionReportsAPI } from '../../services/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`session-detail-tabpanel-${index}`}
      aria-labelledby={`session-detail-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface SessionDetailsProps {
  sessionId: string;
  onClose?: () => void;
}

interface SessionReport {
  session_id: string;
  session_summary: any;
  performance_analysis: any;
  trade_analysis: any;
  strategy_analysis: any;
  risk_analysis: any;
  time_series_data: any;
  alerts_analysis: any;
  recommendations: string[];
}

interface SessionAnalytics {
  session_id: string;
  correlation_analysis: any;
  volatility_analysis: any;
  drawdown_analysis: any;
  execution_analysis: any;
  ml_performance_analysis: any;
  market_conditions_impact: any;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];

const SessionDetails: React.FC<SessionDetailsProps> = ({ sessionId, onClose }) => {
  const [tabValue, setTabValue] = useState(0);
  const [sessionReport, setSessionReport] = useState<SessionReport | null>(null);
  const [sessionAnalytics, setSessionAnalytics] = useState<SessionAnalytics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSessionData();
  }, [sessionId]);

  const fetchSessionData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [reportResponse, analyticsResponse] = await Promise.all([
        sessionReportsAPI.getSessionReport(sessionId),
        sessionReportsAPI.getSessionAnalytics(sessionId)
      ]);
      
      setSessionReport(reportResponse.data);
      setSessionAnalytics(analyticsResponse.data);
    } catch (err: any) {
      setError(`Failed to fetch session data: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  const getPerformanceColor = (value: number) => {
    return value >= 0 ? 'success.main' : 'error.main';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!sessionReport) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No session data available
      </Alert>
    );
  }

  // Session Overview Component
  const SessionOverview: React.FC = () => {
    const summary = sessionReport.session_summary;
    const performance = sessionReport.performance_analysis;
    
    return (
      <Grid container spacing={3}>
        {/* Key Metrics Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total P&L
              </Typography>
              <Typography variant="h4" color={getPerformanceColor(performance.financial_metrics.total_pnl)}>
                {formatCurrency(performance.financial_metrics.total_pnl)}
              </Typography>
              {performance.financial_metrics.total_pnl >= 0 ? <TrendingUpIcon color="success" /> : <TrendingDownIcon color="error" />}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Return
              </Typography>
              <Typography variant="h4" color={getPerformanceColor(performance.financial_metrics.total_return)}>
                {formatPercentage(performance.financial_metrics.total_return)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Sharpe Ratio
              </Typography>
              <Typography variant="h4">
                {performance.risk_metrics.sharpe_ratio.toFixed(2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Win Rate
              </Typography>
              <Typography variant="h4" color="primary">
                {formatPercentage(performance.trading_metrics.win_rate)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Session Details */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Session Information" />
            <CardContent>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Session ID: {summary.session_id}
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Status: {summary.status}
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Duration: {summary.duration.formatted}
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Total Trades: {performance.trading_metrics.total_trades}
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Symbols: {summary.symbols_traded.join(', ')}
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Enabled Strategies:
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {summary.strategies_enabled.map((strategy: string, index: number) => (
                    <Chip key={index} label={strategy} size="small" variant="outlined" />
                  ))}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Risk Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Risk Metrics" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Max Drawdown
                  </Typography>
                  <Typography variant="h6" color="error">
                    {formatPercentage(performance.risk_metrics.max_drawdown)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Volatility
                  </Typography>
                  <Typography variant="h6">
                    {formatPercentage(performance.risk_metrics.volatility)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    VaR (95%)
                  </Typography>
                  <Typography variant="h6">
                    {formatPercentage(performance.risk_metrics.var_95)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Beta
                  </Typography>
                  <Typography variant="h6">
                    {performance.risk_metrics.beta.toFixed(2)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Performance Analysis Component
  const PerformanceAnalysis: React.FC = () => {
    const performance = sessionReport.performance_analysis;
    
    // Mock time series data for performance chart
    const performanceData = Array.from({ length: 20 }, (_, i) => ({
      time: `${i + 1}h`,
      pnl: Math.random() * 1000 - 500,
      cumulative_pnl: (Math.random() - 0.5) * 2000,
      drawdown: Math.random() * -0.1
    }));

    return (
      <Grid container spacing={3}>
        {/* Performance Chart */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Performance Over Time" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line type="monotone" dataKey="cumulative_pnl" stroke="#8884d8" name="Cumulative P&L" />
                  <Line type="monotone" dataKey="drawdown" stroke="#ff7300" name="Drawdown" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Detailed Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Financial Metrics" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Total P&L
                  </Typography>
                  <Typography variant="h6" color={getPerformanceColor(performance.financial_metrics.total_pnl)}>
                    {formatCurrency(performance.financial_metrics.total_pnl)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    ROI
                  </Typography>
                  <Typography variant="h6" color={getPerformanceColor(performance.financial_metrics.roi_percentage)}>
                    {performance.financial_metrics.roi_percentage.toFixed(2)}%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    CAGR
                  </Typography>
                  <Typography variant="h6">
                    {formatPercentage(performance.financial_metrics.compound_annual_growth_rate)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Profit Factor
                  </Typography>
                  <Typography variant="h6">
                    {performance.financial_metrics.profit_factor.toFixed(2)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Trading Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Trading Statistics" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Total Trades
                  </Typography>
                  <Typography variant="h6">
                    {performance.trading_metrics.total_trades}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Win Rate
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    {formatPercentage(performance.trading_metrics.win_rate)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Avg Win
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    {formatCurrency(performance.trading_metrics.avg_win)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Avg Loss
                  </Typography>
                  <Typography variant="h6" color="error.main">
                    {formatCurrency(performance.trading_metrics.avg_loss)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Strategy Analysis Component
  const StrategyAnalysis: React.FC = () => {
    const strategyAnalysis = sessionReport.strategy_analysis;
    
    // Mock strategy performance data
    const strategyData = [
      { name: 'Grid Strategy', performance: 0.15, weight: 0.4, trades: 45 },
      { name: 'Technical Analysis', performance: 0.08, weight: 0.35, trades: 32 },
      { name: 'Trend Following', performance: 0.12, weight: 0.25, trades: 28 }
    ];

    return (
      <Grid container spacing={3}>
        {/* Strategy Performance Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Strategy Performance" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={strategyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="performance" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Strategy Weights */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Strategy Allocation" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={strategyData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="weight"
                    label={({ name, weight }) => `${name}: ${(weight * 100).toFixed(1)}%`}
                  >
                    {strategyData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Strategy Details Table */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Strategy Details" />
            <CardContent>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Strategy</TableCell>
                      <TableCell align="right">Performance</TableCell>
                      <TableCell align="right">Weight</TableCell>
                      <TableCell align="right">Trades</TableCell>
                      <TableCell align="right">Contribution</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {strategyData.map((strategy, index) => (
                      <TableRow key={strategy.name}>
                        <TableCell component="th" scope="row">
                          {strategy.name}
                        </TableCell>
                        <TableCell align="right">
                          <Typography color={getPerformanceColor(strategy.performance)}>
                            {formatPercentage(strategy.performance)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{formatPercentage(strategy.weight)}</TableCell>
                        <TableCell align="right">{strategy.trades}</TableCell>
                        <TableCell align="right">
                          <Typography color={getPerformanceColor(strategy.performance * strategy.weight)}>
                            {formatPercentage(strategy.performance * strategy.weight)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // ML Performance Analysis Component
  const MLAnalysis: React.FC = () => {
    const mlAnalysis = sessionAnalytics?.ml_performance_analysis;
    
    if (!mlAnalysis) {
      return (
        <Alert severity="info">
          ML performance analysis data is not available for this session.
        </Alert>
      );
    }

    return (
      <Grid container spacing={3}>
        {/* ML Performance Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Model Accuracy
              </Typography>
              <Typography variant="h4" color="primary">
                {(mlAnalysis.model_performance?.accuracy * 100 || 0).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Prediction Confidence
              </Typography>
              <Typography variant="h4" color="secondary">
                {(mlAnalysis.model_performance?.confidence * 100 || 0).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Decision Accuracy
              </Typography>
              <Typography variant="h4" color="success.main">
                {(mlAnalysis.decision_metrics?.decision_accuracy_rate * 100 || 0).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                ML ROI
              </Typography>
              <Typography variant="h4" color={getPerformanceColor(mlAnalysis.cost_metrics?.roi || 0)}>
                {formatPercentage(mlAnalysis.cost_metrics?.roi || 0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* ML vs Traditional Performance */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="ML vs Traditional Performance" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Typography variant="h6" color="primary">
                    ML Performance
                  </Typography>
                  <Typography variant="h4" color={getPerformanceColor(mlAnalysis.performance_comparison?.ml || 0)}>
                    {formatPercentage(mlAnalysis.performance_comparison?.ml || 0)}
                  </Typography>
                </Grid>
                <Grid item xs={4}>
                  <Typography variant="h6" color="secondary">
                    Traditional Performance
                  </Typography>
                  <Typography variant="h4" color={getPerformanceColor(mlAnalysis.performance_comparison?.traditional || 0)}>
                    {formatPercentage(mlAnalysis.performance_comparison?.traditional || 0)}
                  </Typography>
                </Grid>
                <Grid item xs={4}>
                  <Typography variant="h6" color="success.main">
                    Combined Performance
                  </Typography>
                  <Typography variant="h4" color={getPerformanceColor(mlAnalysis.performance_comparison?.combined || 0)}>
                    {formatPercentage(mlAnalysis.performance_comparison?.combined || 0)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Recommendations Component
  const Recommendations: React.FC = () => {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Session Recommendations" />
            <CardContent>
              <List>
                {sessionReport.recommendations.map((recommendation, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircleIcon color="success" />
                    </ListItemIcon>
                    <ListItemText primary={recommendation} />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Session Analysis: {sessionId.substring(0, 8)}...
        </Typography>
        <Box>
          <Button
            startIcon={<FileDownloadIcon />}
            onClick={() => console.log('Export session')}
            sx={{ mr: 1 }}
          >
            Export
          </Button>
          {onClose && (
            <Button onClick={onClose} variant="outlined">
              Close
            </Button>
          )}
        </Box>
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="session details tabs">
          <Tab icon={<SpeedIcon />} label="Overview" />
          <Tab icon={<ShowChartIcon />} label="Performance" />
          <Tab icon={<AssessmentIcon />} label="Strategies" />
          <Tab icon={<PsychologyAltIcon />} label="ML Analysis" />
          <Tab icon={<SecurityIcon />} label="Risk" />
          <Tab icon={<CheckCircleIcon />} label="Recommendations" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <SessionOverview />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <PerformanceAnalysis />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <StrategyAnalysis />
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <MLAnalysis />
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Risk Analysis
                </Typography>
                <Typography color="textSecondary">
                  Advanced risk analysis with VaR, drawdown, and correlation metrics coming soon...
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={5}>
        <Recommendations />
      </TabPanel>
    </Box>
  );
};

export default SessionDetails;