#!/usr/bin/env python3
"""
Simple Supabase Connection Test
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

def test_connection():
    """Test basic Supabase connection"""
    print("🔗 Testing Supabase connection...")
    print(f"📍 URL: {SUPABASE_URL}")
    print(f"🔑 Key: {SUPABASE_KEY[:50]}...")
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Missing credentials")
        return False
    
    try:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase client created successfully!")
        
        # Try a simple RPC call that should always work
        result = supabase.rpc('now').execute()
        print(f"✅ Connection test successful! Server time: {result.data}")
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    test_connection()