#!/usr/bin/env python3
"""
Initialize Supabase Database Schema
This script creates the required tables and indexes for the strategy ensemble system.
"""

import asyncio
import aiohttp
import os
from dotenv import load_dotenv
import json
from typing import List, Dict, Any

# Load environment variables
load_dotenv('.env')

class SupabaseInitializer:
    """Initialize Supabase database schema"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        
        self.api_url = f"{self.supabase_url}/rest/v1"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
    
    async def check_table_exists(self, session: aiohttp.ClientSession, table_name: str) -> bool:
        """Check if a table exists"""
        try:
            async with session.get(
                f"{self.api_url}/{table_name}?select=*&limit=1",
                headers=self.headers
            ) as response:
                return response.status in [200, 206]  # 200 = OK, 206 = Partial content (empty table)
        except Exception:
            return False
    
    async def create_sample_data(self, session: aiohttp.ClientSession):
        """Create sample data for testing"""
        try:
            # Sample trade execution
            sample_trade = {
                'strategy_name': 'initialization_test',
                'symbol': 'BTCUSDT',
                'action': 'BUY',
                'quantity': 0.001,
                'price': 50000.0,
                'timestamp': '2025-06-14T09:00:00Z',
                'metadata': {
                    'initialization': True,
                    'test_data': True
                }
            }
            
            async with session.post(
                f"{self.api_url}/trade_executions",
                headers=self.headers,
                json=sample_trade
            ) as response:
                if response.status in [200, 201]:
                    print("✓ Sample trade execution created")
                    return await response.json()
                else:
                    print(f"✗ Failed to create sample trade: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            print(f"✗ Error creating sample data: {e}")
            return None
    
    async def test_operations(self, session: aiohttp.ClientSession):
        """Test basic database operations"""
        print("\nTesting database operations...")
        
        # Test 1: Create sample data
        sample_data = await self.create_sample_data(session)
        if not sample_data:
            return False
        
        # Test 2: Query data
        try:
            async with session.get(
                f"{self.api_url}/trade_executions?strategy_name=eq.initialization_test",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ Query test passed - found {len(data)} records")
                else:
                    print(f"✗ Query test failed: {response.status}")
                    return False
        except Exception as e:
            print(f"✗ Query test error: {e}")
            return False
        
        # Test 3: Delete test data
        try:
            if sample_data and len(sample_data) > 0:
                record_id = sample_data[0]['id']
                async with session.delete(
                    f"{self.api_url}/trade_executions?id=eq.{record_id}",
                    headers=self.headers
                ) as response:
                    if response.status in [200, 204]:
                        print("✓ Delete test passed")
                    else:
                        print(f"✗ Delete test failed: {response.status}")
        except Exception as e:
            print(f"✗ Delete test error: {e}")
        
        return True
    
    async def initialize_database(self):
        """Initialize the database with required schema"""
        print("Initializing Supabase database...")
        
        async with aiohttp.ClientSession() as session:
            # Check if tables exist
            tables_to_check = [
                'trade_executions',
                'portfolio_metrics',
                'strategy_performance',
                'market_data',
                'cross_exchange_validations',
                'ensemble_executions'
            ]
            
            print("\nChecking table existence...")
            table_status = {}
            for table in tables_to_check:
                exists = await self.check_table_exists(session, table)
                table_status[table] = exists
                status_icon = "✓" if exists else "✗"
                print(f"{status_icon} {table}: {'EXISTS' if exists else 'NOT FOUND'}")
            
            # If all tables exist, test operations
            if all(table_status.values()):
                print("\n✓ All required tables exist!")
                success = await self.test_operations(session)
                if success:
                    print("\n🎉 Database initialization completed successfully!")
                    print("\nYou can now run the real API tests.")
                    return True
                else:
                    print("\n❌ Database operations test failed")
                    return False
            else:
                missing_tables = [table for table, exists in table_status.items() if not exists]
                print(f"\n❌ Missing tables: {', '.join(missing_tables)}")
                print("\nTo create the required tables, please:")
                print("1. Go to your Supabase dashboard")
                print("2. Open the SQL Editor")
                print("3. Run the SQL script from 'supabase_schema.sql'")
                print("4. Then run this initialization script again")
                return False

async def main():
    """Main initialization function"""
    print("=" * 60)
    print("SUPABASE DATABASE INITIALIZATION")
    print("=" * 60)
    
    try:
        initializer = SupabaseInitializer()
        success = await initializer.initialize_database()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ INITIALIZATION SUCCESSFUL")
            print("Your Supabase database is ready for real API testing!")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ INITIALIZATION FAILED")
            print("Please check the error messages above and fix the issues.")
            print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Initialization failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)