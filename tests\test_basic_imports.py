"""
Basic import tests that don't require database connections or external dependencies.
These tests verify the core module structure and imports work correctly.
"""
import pytest
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_core_module_imports():
    """Test that core modules can be imported without external dependencies."""
    try:
        # Test basic model imports
        from app.models.base import Base
        from app.models.api_models import AccountStatsResponse
        
        # Test basic model functionality
        assert Base is not None
        assert AccountStatsResponse is not None
        
        assert True, "Core module imports successful"
    except ImportError as e:
        pytest.fail(f"Failed to import core modules: {e}")


def test_configuration_models():
    """Test configuration and settings imports."""
    try:
        from app.config.settings import Settings
        
        # Test that settings can be instantiated with minimal config
        settings = Settings()
        assert hasattr(settings, 'binance_api_key')
        assert hasattr(settings, 'binance_api_secret')
        assert hasattr(settings, 'trading_symbol')
        
    except ImportError as e:
        pytest.fail(f"Failed to import configuration: {e}")


def test_type_definitions():
    """Test type definition imports."""
    try:
        from app.models.trade_status import TradeStatus
        from app.models.trade_state_snapshot import TradeStateSnapshotDB
        
        # Test enums are properly defined
        assert hasattr(TradeStatus, 'PENDING_ENTRY')
        assert hasattr(TradeStatus, 'ACTIVE')
        
        # Test state snapshot model
        assert TradeStateSnapshotDB is not None
        
    except ImportError as e:
        pytest.fail(f"Failed to import type definitions: {e}")


def test_strategy_basic_structure():
    """Test that strategy module structure exists without importing heavy dependencies."""
    try:
        from pathlib import Path
        
        # Test that strategy files exist
        strategies_path = Path(__file__).parent.parent / "app" / "strategies"
        
        assert (strategies_path / "base_strategy.py").exists()
        assert (strategies_path / "strategy_selector.py").exists()
        assert (strategies_path / "grid_strategy.py").exists()
        
        # Test that we can read strategy file content
        base_strategy_content = (strategies_path / "base_strategy.py").read_text()
        assert "class BaseStrategy" in base_strategy_content
        assert "analyze_market" in base_strategy_content
        # BaseStrategy uses different method names, check for generate_trade_signal instead
        assert "generate_trade_signal" in base_strategy_content or "analyze_market" in base_strategy_content
        
    except Exception as e:
        pytest.fail(f"Failed to verify strategy structure: {e}")


if __name__ == "__main__":
    test_core_module_imports()
    test_configuration_models()
    test_type_definitions()
    test_strategy_basic_structure()
    print("All basic import tests passed!")