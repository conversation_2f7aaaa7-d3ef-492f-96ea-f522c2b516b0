# app/strategies/ensemble_portfolio_manager.py
"""
Enhanced Portfolio Manager with Redis caching and MCP integration.
Implements real-time signal aggregation and sub-second response times.
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from collections import defaultdict

from app.models.trade_state import TradeState
from app.models.market_data import MarketData
from app.services.execution.execution_service import ExecutionService
from app.strategies.base_strategy import BaseStrategy
from app.ml.models.weight_optimizer import WeightOptimizer
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.mcp.wandb_service import WandBService, ExperimentMetrics

logger = logging.getLogger(__name__)

@dataclass
class CachedSignal:
    """Cached strategy signal with metadata"""
    strategy_name: str
    action: str
    quantity: float
    price: float
    confidence: float
    timestamp: datetime
    market_conditions_hash: str
    ttl_seconds: int

@dataclass
class AggregatedSignal:
    """Aggregated signal from multiple strategies"""
    action: str
    quantity: float
    price: float
    confidence: float
    contributing_strategies: List[str]
    strategy_weights: Dict[str, float]
    execution_priority: int
    timestamp: datetime
    correlation_risk: float

@dataclass
class PerformanceMetrics:
    """Real-time performance metrics"""
    signal_generation_ms: float
    cache_hit_rate: float
    aggregation_time_ms: float
    total_execution_time_ms: float
    cache_operations: int
    successful_predictions: int
    failed_predictions: int

class EnsemblePortfolioManager:
    """
    Enhanced Portfolio Manager with Redis caching, real-time signal aggregation,
    and comprehensive MCP integration for sub-second performance.
    """
    
    def __init__(
        self,
        strategies: List[BaseStrategy],
        weight_optimizer: WeightOptimizer,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        wandb_service: Optional[WandBService] = None,
        execution_service: Optional[ExecutionService] = None,
        config: Optional[Dict] = None
    ):
        """
        Initialize the enhanced portfolio manager.
        
        Args:
            strategies: List of trading strategies to manage
            weight_optimizer: ML model for optimizing strategy weights
            redis_service: Redis service for caching
            supabase_service: Supabase service for analytics (optional)
            wandb_service: W&B service for experiment tracking (optional)
            execution_service: Trade execution service (optional)
            config: Configuration parameters
        """
        self.strategies = {strategy.__class__.__name__: strategy for strategy in strategies}
        self.weight_optimizer = weight_optimizer
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.wandb_service = wandb_service
        self.execution_service = execution_service
        
        # Configuration
        self.config = config or self._default_config()
        
        # Performance tracking
        self.performance_metrics = PerformanceMetrics(
            signal_generation_ms=0,
            cache_hit_rate=0,
            aggregation_time_ms=0,
            total_execution_time_ms=0,
            cache_operations=0,
            successful_predictions=0,
            failed_predictions=0
        )
        
        # Cache keys
        self.SIGNAL_CACHE_PREFIX = "signal:"
        self.WEIGHTS_CACHE_KEY = "ensemble:weights"
        self.AGGREGATED_SIGNAL_KEY = "ensemble:aggregated_signal"
        self.PERFORMANCE_CACHE_KEY = "ensemble:performance"
        self.CORRELATION_CACHE_KEY = "ensemble:correlation"
        
        # State tracking
        self.last_market_conditions_hash = None
        self.last_aggregation_time = datetime.min
        self.strategy_performance_history = defaultdict(list)
        
        logger.info(f"Initialized EnsemblePortfolioManager with {len(self.strategies)} strategies")
    
    def _default_config(self) -> Dict:
        """Default configuration for the portfolio manager."""
        return {
            "signal_cache_ttl": 30,        # 30 seconds
            "weights_cache_ttl": 300,      # 5 minutes  
            "aggregation_cache_ttl": 10,   # 10 seconds
            "performance_cache_ttl": 60,   # 1 minute
            "correlation_cache_ttl": 1800, # 30 minutes
            "min_confidence_threshold": 0.6,
            "max_position_size": 0.1,
            "risk_correlation_threshold": 0.8,
            "enable_parallel_execution": True,
            "cache_warming_enabled": True,
            "performance_tracking_enabled": True
        }
    
    async def execute_ensemble_with_caching(
        self, 
        market_data: MarketData
    ) -> Tuple[List[TradeState], PerformanceMetrics]:
        """
        Execute ensemble strategy with comprehensive caching for sub-second performance.
        
        Args:
            market_data: Current market data
            
        Returns:
            Tuple of executed trades and performance metrics
        """
        start_time = datetime.now()
        
        try:
            # Generate market conditions hash for cache validation
            market_hash = self._generate_market_hash(market_data)
            
            # Step 1: Get or compute strategy weights (cached)
            weights_start = datetime.now()
            strategy_weights = await self._get_cached_strategy_weights(market_data, market_hash)
            weights_time = (datetime.now() - weights_start).total_seconds() * 1000
            
            # Step 2: Get strategy signals with intelligent caching
            signals_start = datetime.now()
            strategy_signals = await self._get_cached_strategy_signals(market_data, market_hash)
            signals_time = (datetime.now() - signals_start).total_seconds() * 1000
            
            # Step 3: Aggregate signals with conflict resolution
            aggregation_start = datetime.now()
            aggregated_signal = await self._aggregate_signals_with_caching(
                strategy_signals, strategy_weights, market_hash
            )
            aggregation_time = (datetime.now() - aggregation_start).total_seconds() * 1000
            
            # Step 4: Execute trades if signal meets criteria
            execution_start = datetime.now()
            executed_trades = await self._execute_trades_with_monitoring(
                aggregated_signal, market_data
            )
            execution_time = (datetime.now() - execution_start).total_seconds() * 1000
            
            # Update performance metrics
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            await self._update_performance_metrics(
                signal_time=signals_time,
                aggregation_time=aggregation_time,
                total_time=total_time,
                success=True
            )
            
            # Log to W&B if enabled
            if self.wandb_service and self.config.get("performance_tracking_enabled"):
                await self._log_execution_metrics(
                    market_data, strategy_weights, aggregated_signal, executed_trades, total_time
                )
            
            logger.info(f"Ensemble execution completed in {total_time:.1f}ms")
            return executed_trades, self.performance_metrics
            
        except Exception as e:
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            await self._update_performance_metrics(
                signal_time=0, aggregation_time=0, total_time=total_time, success=False
            )
            logger.error(f"Ensemble execution failed: {e}")
            return [], self.performance_metrics
    
    async def _get_cached_strategy_weights(
        self, 
        market_data: MarketData, 
        market_hash: str
    ) -> Dict[str, float]:
        """Get strategy weights with intelligent caching."""
        cache_key = f"{self.WEIGHTS_CACHE_KEY}:{market_hash}"
        
        # Try cache first
        try:
            cached_weights = await self.redis_service.get(cache_key)
            if cached_weights:
                weights_data = json.loads(cached_weights)
                cache_time = datetime.fromisoformat(weights_data['timestamp'])
                
                # Check if cache is still valid
                if datetime.now() - cache_time < timedelta(seconds=self.config["weights_cache_ttl"]):
                    self.performance_metrics.cache_operations += 1
                    return weights_data['weights']
        except Exception as e:
            logger.warning(f"Cache retrieval failed: {e}")
        
        # Compute fresh weights
        try:
            market_conditions = self._market_data_to_conditions(market_data)
            weight_array = await self.weight_optimizer.predict_weights(market_conditions)
            
            # Convert to strategy name mapping
            strategy_names = list(self.strategies.keys())
            weights = {
                strategy_names[i]: float(weight_array[i]) 
                for i in range(min(len(strategy_names), len(weight_array)))
            }
            
            # Ensure weights sum to 1.0
            total_weight = sum(weights.values())
            if total_weight > 0:
                weights = {k: v / total_weight for k, v in weights.items()}
            else:
                # Fallback to equal weights
                equal_weight = 1.0 / len(strategy_names)
                weights = {name: equal_weight for name in strategy_names}
            
            # Cache the results
            cache_data = {
                'weights': weights,
                'timestamp': datetime.now().isoformat(),
                'market_hash': market_hash
            }
            
            await self.redis_service.setex(
                cache_key,
                self.config["weights_cache_ttl"],
                json.dumps(cache_data)
            )
            
            self.performance_metrics.cache_operations += 1
            self.performance_metrics.successful_predictions += 1
            
            return weights
            
        except Exception as e:
            logger.error(f"Weight prediction failed: {e}")
            self.performance_metrics.failed_predictions += 1
            
            # Fallback to equal weights
            equal_weight = 1.0 / len(self.strategies)
            return {name: equal_weight for name in self.strategies.keys()}
    
    async def _get_cached_strategy_signals(
        self, 
        market_data: MarketData, 
        market_hash: str
    ) -> Dict[str, Dict]:
        """Get strategy signals with parallel execution and caching."""
        signals = {}
        cache_hits = 0
        
        # Check cache for each strategy
        cached_signals = {}
        missing_strategies = []
        
        for strategy_name in self.strategies.keys():
            cache_key = f"{self.SIGNAL_CACHE_PREFIX}{strategy_name}:{market_hash}"
            
            try:
                cached_signal = await self.redis_service.get(cache_key)
                if cached_signal:
                    signal_data = json.loads(cached_signal)
                    
                    # Validate cache timestamp
                    cache_time = datetime.fromisoformat(signal_data['timestamp'])
                    if datetime.now() - cache_time < timedelta(seconds=self.config["signal_cache_ttl"]):
                        cached_signals[strategy_name] = signal_data['signal']
                        cache_hits += 1
                        continue
                        
            except Exception as e:
                logger.warning(f"Failed to retrieve cached signal for {strategy_name}: {e}")
            
            missing_strategies.append(strategy_name)
        
        # Generate missing signals in parallel
        if missing_strategies:
            if self.config.get("enable_parallel_execution", True):
                tasks = [
                    self._generate_and_cache_signal(strategy_name, market_data, market_hash)
                    for strategy_name in missing_strategies
                ]
                
                fresh_signals = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, strategy_name in enumerate(missing_strategies):
                    if isinstance(fresh_signals[i], Exception):
                        logger.error(f"Signal generation failed for {strategy_name}: {fresh_signals[i]}")
                        signals[strategy_name] = self._default_signal()
                    else:
                        signals[strategy_name] = fresh_signals[i]
            else:
                # Sequential execution fallback
                for strategy_name in missing_strategies:
                    try:
                        signal = await self._generate_and_cache_signal(strategy_name, market_data, market_hash)
                        signals[strategy_name] = signal
                    except Exception as e:
                        logger.error(f"Signal generation failed for {strategy_name}: {e}")
                        signals[strategy_name] = self._default_signal()
        
        # Combine cached and fresh signals
        signals.update(cached_signals)
        
        # Update cache hit rate
        total_strategies = len(self.strategies)
        self.performance_metrics.cache_hit_rate = cache_hits / total_strategies if total_strategies > 0 else 0
        self.performance_metrics.cache_operations += total_strategies
        
        return signals
    
    async def _generate_and_cache_signal(
        self, 
        strategy_name: str, 
        market_data: MarketData, 
        market_hash: str
    ) -> Dict:
        """Generate signal for a strategy and cache it."""
        try:
            strategy = self.strategies[strategy_name]
            
            # Generate signal
            signal = await strategy.generate_signal(market_data)
            
            # Convert to dict format
            signal_dict = {
                'action': signal.action,
                'quantity': signal.quantity,
                'price': signal.price,
                'confidence': signal.confidence
            }
            
            # Cache the signal
            cache_key = f"{self.SIGNAL_CACHE_PREFIX}{strategy_name}:{market_hash}"
            cache_data = {
                'signal': signal_dict,
                'timestamp': datetime.now().isoformat(),
                'market_hash': market_hash,
                'strategy': strategy_name
            }
            
            await self.redis_service.setex(
                cache_key,
                self.config["signal_cache_ttl"],
                json.dumps(cache_data)
            )
            
            return signal_dict
            
        except Exception as e:
            logger.error(f"Failed to generate signal for {strategy_name}: {e}")
            return self._default_signal()
    
    async def _aggregate_signals_with_caching(
        self,
        strategy_signals: Dict[str, Dict],
        strategy_weights: Dict[str, float],
        market_hash: str
    ) -> AggregatedSignal:
        """Aggregate signals with caching and conflict resolution."""
        
        # Check cache first
        cache_key = f"{self.AGGREGATED_SIGNAL_KEY}:{market_hash}"
        
        try:
            cached_aggregation = await self.redis_service.get(cache_key)
            if cached_aggregation:
                data = json.loads(cached_aggregation)
                cache_time = datetime.fromisoformat(data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["aggregation_cache_ttl"]):
                    return AggregatedSignal(**data['signal'])
        except Exception as e:
            logger.warning(f"Failed to retrieve cached aggregation: {e}")
        
        # Perform fresh aggregation
        weighted_actions = defaultdict(float)
        weighted_quantities = []
        weighted_prices = []
        contributing_strategies = []
        total_confidence = 0.0
        
        for strategy_name, signal in strategy_signals.items():
            weight = strategy_weights.get(strategy_name, 0.0)
            
            if weight <= 0:
                continue
                
            action = signal.get('action', 'HOLD')
            quantity = signal.get('quantity', 0)
            price = signal.get('price', 0)
            confidence = signal.get('confidence', 0)
            
            # Weight the action votes
            weighted_actions[action] += weight * confidence
            
            # Accumulate weighted quantities and prices for non-HOLD actions
            if action in ['BUY', 'SELL'] and quantity > 0:
                weighted_quantities.append(quantity * weight)
                weighted_prices.append(price * weight)
                contributing_strategies.append(strategy_name)
            
            total_confidence += confidence * weight
        
        # Determine final action (highest weighted vote)
        if not weighted_actions:
            final_action = 'HOLD'
        else:
            final_action = max(weighted_actions.items(), key=lambda x: x[1])[0]
        
        # Calculate final quantity and price
        final_quantity = sum(weighted_quantities) if weighted_quantities else 0
        final_price = (sum(weighted_prices) / len(weighted_prices)) if weighted_prices else 0
        
        # Normalize confidence
        total_weight = sum(strategy_weights.values())
        final_confidence = total_confidence / total_weight if total_weight > 0 else 0
        
        # Calculate correlation risk
        correlation_risk = await self._calculate_correlation_risk(contributing_strategies)
        
        # Create aggregated signal
        aggregated_signal = AggregatedSignal(
            action=final_action,
            quantity=final_quantity,
            price=final_price,
            confidence=final_confidence,
            contributing_strategies=contributing_strategies,
            strategy_weights=strategy_weights,
            execution_priority=self._calculate_execution_priority(final_action, final_confidence),
            timestamp=datetime.now(),
            correlation_risk=correlation_risk
        )
        
        # Cache the aggregation
        cache_data = {
            'signal': asdict(aggregated_signal),
            'timestamp': datetime.now().isoformat(),
            'market_hash': market_hash
        }
        
        await self.redis_service.setex(
            cache_key,
            self.config["aggregation_cache_ttl"],
            json.dumps(cache_data, default=str)
        )
        
        return aggregated_signal
    
    async def _execute_trades_with_monitoring(
        self,
        aggregated_signal: AggregatedSignal,
        market_data: MarketData
    ) -> List[TradeState]:
        """Execute trades with comprehensive monitoring."""
        executed_trades = []
        
        try:
            # Check if signal meets execution criteria
            if not self._should_execute_signal(aggregated_signal):
                logger.info(f"Signal does not meet execution criteria: {aggregated_signal.action}")
                return executed_trades
            
            # Execute trade if execution service is available
            if self.execution_service:
                trade_result = await self.execution_service.execute_trade(
                    symbol=market_data.symbol,
                    action=aggregated_signal.action,
                    quantity=aggregated_signal.quantity,
                    price=aggregated_signal.price
                )
                
                if trade_result:
                    executed_trades.append(trade_result)
                    
                    # Store trade in Supabase if available
                    if self.supabase_service:
                        await self._store_trade_execution(trade_result, aggregated_signal, market_data)
            
            # Log execution metrics
            await self._log_trade_execution(aggregated_signal, executed_trades, market_data)
            
        except Exception as e:
            logger.error(f"Trade execution failed: {e}")
        
        return executed_trades
    
    async def _calculate_correlation_risk(self, contributing_strategies: List[str]) -> float:
        """Calculate correlation risk among contributing strategies."""
        if len(contributing_strategies) < 2:
            return 0.0
        
        try:
            # Try to get cached correlation matrix
            cached_correlations = await self.redis_service.get(self.CORRELATION_CACHE_KEY)
            
            if cached_correlations:
                correlation_matrix = json.loads(cached_correlations)
            else:
                # Calculate fresh correlations (simplified implementation)
                correlation_matrix = await self._calculate_strategy_correlations()
                
                # Cache correlations
                await self.redis_service.setex(
                    self.CORRELATION_CACHE_KEY,
                    self.config["correlation_cache_ttl"],
                    json.dumps(correlation_matrix)
                )
            
            # Calculate average correlation among contributing strategies
            correlations = []
            for i, strategy1 in enumerate(contributing_strategies):
                for j, strategy2 in enumerate(contributing_strategies[i+1:], i+1):
                    corr = correlation_matrix.get(strategy1, {}).get(strategy2, 0.0)
                    correlations.append(abs(corr))
            
            return np.mean(correlations) if correlations else 0.0
            
        except Exception as e:
            logger.error(f"Correlation risk calculation failed: {e}")
            return 0.0
    
    async def _calculate_strategy_correlations(self) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between strategies (simplified)."""
        # This is a placeholder implementation
        # In a real system, you'd calculate correlations from historical performance data
        
        correlation_matrix = {}
        strategy_names = list(self.strategies.keys())
        
        for strategy1 in strategy_names:
            correlation_matrix[strategy1] = {}
            for strategy2 in strategy_names:
                if strategy1 == strategy2:
                    correlation_matrix[strategy1][strategy2] = 1.0
                else:
                    # Placeholder correlation (in reality, calculated from historical data)
                    correlation_matrix[strategy1][strategy2] = np.random.uniform(0.1, 0.7)
        
        return correlation_matrix
    
    def _should_execute_signal(self, signal: AggregatedSignal) -> bool:
        """Determine if signal should be executed based on criteria."""
        return (
            signal.action in ['BUY', 'SELL'] and
            signal.confidence >= self.config["min_confidence_threshold"] and
            signal.quantity > 0 and
            signal.correlation_risk < self.config["risk_correlation_threshold"]
        )
    
    def _calculate_execution_priority(self, action: str, confidence: float) -> int:
        """Calculate execution priority based on action and confidence."""
        if action == 'HOLD':
            return 0
        
        # Higher confidence = higher priority
        if confidence >= 0.8:
            return 1  # High priority
        elif confidence >= 0.6:
            return 2  # Medium priority
        else:
            return 3  # Low priority
    
    def _generate_market_hash(self, market_data: MarketData) -> str:
        """Generate hash for market conditions to use as cache key."""
        import hashlib
        
        # Create a string representation of key market data
        market_string = f"{market_data.symbol}:{market_data.timestamp.minute}:{market_data.price:.2f}:{market_data.volume}"
        
        # Generate hash
        return hashlib.md5(market_string.encode()).hexdigest()[:12]
    
    def _market_data_to_conditions(self, market_data: MarketData) -> Dict[str, float]:
        """Convert market data to conditions dict for weight optimizer."""
        return {
            'volatility': getattr(market_data, 'volatility', 0.02),
            'volume': market_data.volume,
            'rsi': getattr(market_data, 'rsi', 50),
            'macd': getattr(market_data, 'macd', 0),
            'price_change': getattr(market_data, 'price_change', 0),
            'volatility_ma': getattr(market_data, 'volatility_ma', 0.02),
            'volume_ma': getattr(market_data, 'volume_ma', market_data.volume),
            'rsi_ma': getattr(market_data, 'rsi_ma', 50)
        }
    
    def _default_signal(self) -> Dict:
        """Default signal for fallback cases."""
        return {
            'action': 'HOLD',
            'quantity': 0,
            'price': 0,
            'confidence': 0
        }
    
    async def _update_performance_metrics(
        self,
        signal_time: float,
        aggregation_time: float,
        total_time: float,
        success: bool
    ) -> None:
        """Update performance metrics."""
        self.performance_metrics.signal_generation_ms = signal_time
        self.performance_metrics.aggregation_time_ms = aggregation_time
        self.performance_metrics.total_execution_time_ms = total_time
        
        if success:
            self.performance_metrics.successful_predictions += 1
        else:
            self.performance_metrics.failed_predictions += 1
    
    async def _log_execution_metrics(
        self,
        market_data: MarketData,
        strategy_weights: Dict[str, float],
        aggregated_signal: AggregatedSignal,
        executed_trades: List[TradeState],
        execution_time_ms: float
    ) -> None:
        """Log execution metrics to W&B."""
        if not self.wandb_service:
            return
        
        try:
            metrics = ExperimentMetrics(
                timestamp=datetime.now(),
                experiment_name="ensemble_execution",
                strategy_weights=strategy_weights,
                portfolio_value=100000,  # Placeholder
                total_pnl=sum(trade.pnl for trade in executed_trades) if executed_trades else 0,
                sharpe_ratio=0,  # Placeholder
                max_drawdown=0,  # Placeholder
                win_rate=0,  # Placeholder
                correlation_matrix={},  # Placeholder
                individual_strategy_pnl={},  # Placeholder
                confidence_scores={},  # Placeholder
                market_conditions=self._market_data_to_conditions(market_data),
                trade_count=len(executed_trades),
                execution_time_ms=execution_time_ms
            )
            
            await self.wandb_service.log_strategy_performance(metrics)
            
        except Exception as e:
            logger.error(f"Failed to log execution metrics: {e}")
    
    async def _log_trade_execution(
        self,
        signal: AggregatedSignal,
        trades: List[TradeState],
        market_data: MarketData
    ) -> None:
        """Log trade execution details."""
        logger.info(f"Signal: {signal.action} | Confidence: {signal.confidence:.3f} | "
                   f"Strategies: {signal.contributing_strategies} | Trades: {len(trades)}")
    
    async def _store_trade_execution(
        self,
        trade: TradeState,
        signal: AggregatedSignal,
        market_data: MarketData
    ) -> None:
        """Store trade execution in Supabase."""
        if not self.supabase_service:
            return
        
        try:
            trade_data = {
                'strategy_name': 'ensemble',
                'symbol': market_data.symbol,
                'action': trade.action,
                'quantity': trade.quantity,
                'price': trade.price,
                'timestamp': datetime.now().isoformat(),
                'confidence': signal.confidence,
                'weight': 1.0,  # Ensemble weight
                'position_size': trade.quantity,
                'market_conditions': self._market_data_to_conditions(market_data)
            }
            
            await self.supabase_service.store_trade_execution(trade_data)
            
        except Exception as e:
            logger.error(f"Failed to store trade execution: {e}")
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        return {
            'cache_performance': {
                'hit_rate': f"{self.performance_metrics.cache_hit_rate:.1%}",
                'operations': self.performance_metrics.cache_operations,
                'avg_signal_time_ms': self.performance_metrics.signal_generation_ms,
                'avg_aggregation_time_ms': self.performance_metrics.aggregation_time_ms,
                'avg_total_time_ms': self.performance_metrics.total_execution_time_ms
            },
            'prediction_performance': {
                'successful': self.performance_metrics.successful_predictions,
                'failed': self.performance_metrics.failed_predictions,
                'success_rate': f"{self.performance_metrics.successful_predictions / (self.performance_metrics.successful_predictions + self.performance_metrics.failed_predictions) * 100:.1f}%" if (self.performance_metrics.successful_predictions + self.performance_metrics.failed_predictions) > 0 else "N/A"
            },
            'configuration': self.config,
            'strategies': list(self.strategies.keys()),
            'last_execution': self.last_aggregation_time.isoformat() if self.last_aggregation_time != datetime.min else "Never"
        }

# Utility functions for integration

async def create_ensemble_portfolio_manager(
    strategies: List[BaseStrategy],
    redis_url: str,
    weight_optimizer: WeightOptimizer,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    wandb_api_key: Optional[str] = None,
    config: Optional[Dict] = None
) -> EnsemblePortfolioManager:
    """Factory function to create ensemble portfolio manager with MCP services."""
    
    # Initialize Redis service
    redis_service = RedisService(redis_url)
    
    # Initialize Supabase service if credentials provided
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    # Initialize W&B service if API key provided
    wandb_service = None
    if wandb_api_key:
        wandb_service = WandBService(
            project_name="crypto-ensemble-strategy",
            api_key=wandb_api_key
        )
    
    return EnsemblePortfolioManager(
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        supabase_service=supabase_service,
        wandb_service=wandb_service,
        config=config
    )

# Performance testing utilities

async def benchmark_portfolio_manager(
    portfolio_manager: EnsemblePortfolioManager,
    test_market_data: List[MarketData],
    num_iterations: int = 100
) -> Dict[str, Any]:
    """Benchmark portfolio manager performance."""
    
    execution_times = []
    cache_hit_rates = []
    
    logger.info(f"Starting benchmark with {num_iterations} iterations")
    
    for i in range(num_iterations):
        market_data = test_market_data[i % len(test_market_data)]
        
        start_time = datetime.now()
        trades, metrics = await portfolio_manager.execute_ensemble_with_caching(market_data)
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        execution_times.append(execution_time)
        cache_hit_rates.append(metrics.cache_hit_rate)
        
        if i % 10 == 0:
            logger.info(f"Benchmark progress: {i+1}/{num_iterations}")
    
    return {
        'avg_execution_time_ms': np.mean(execution_times),
        'min_execution_time_ms': np.min(execution_times),
        'max_execution_time_ms': np.max(execution_times),
        'p95_execution_time_ms': np.percentile(execution_times, 95),
        'avg_cache_hit_rate': np.mean(cache_hit_rates),
        'sub_second_rate': sum(1 for t in execution_times if t < 1000) / len(execution_times),
        'total_iterations': num_iterations,
        'performance_target_met': np.mean(execution_times) < 1000  # Sub-second target
    }