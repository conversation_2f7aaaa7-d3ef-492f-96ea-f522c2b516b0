#!/bin/bash

# Production Deployment Script for Dynamic Position Optimization
# Task 2.2.2: Automated deployment with rollback procedures

set -e

echo "🚀 Dynamic Position Optimization - Production Deployment"
echo "========================================================"

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-production}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
ROLLBACK_TAG=""
HEALTH_CHECK_TIMEOUT=300
MAX_RETRIES=3

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handler
handle_error() {
    local exit_code=$?
    log_error "Deployment failed with exit code $exit_code"
    log_error "Initiating automatic rollback..."
    rollback_deployment
    exit $exit_code
}

trap handle_error ERR

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check if required environment variables are set
    if [[ -z "$REGISTRY" ]]; then
        log_error "REGISTRY environment variable not set"
        exit 1
    fi
    
    if [[ -z "$IMAGE_NAME" ]]; then
        log_error "IMAGE_NAME environment variable not set"
        exit 1
    fi
    
    # Validate Docker configuration
    log_info "Validating Docker configuration..."
    if ! "$PROJECT_DIR/scripts/test-docker-config.sh"; then
        log_error "Docker configuration validation failed"
        exit 1
    fi
    
    # Check if target images exist
    log_info "Verifying Docker images exist..."
    if ! docker pull "$REGISTRY/$IMAGE_NAME:$IMAGE_TAG" > /dev/null 2>&1; then
        log_error "Main application image not found: $REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
        exit 1
    fi
    
    if ! docker pull "$REGISTRY/$IMAGE_NAME-position-optimizer:$IMAGE_TAG" > /dev/null 2>&1; then
        log_error "Position optimizer image not found: $REGISTRY/$IMAGE_NAME-position-optimizer:$IMAGE_TAG"
        exit 1
    fi
    
    log_success "Pre-deployment checks passed"
}

# Get current deployment tag for rollback
get_current_deployment() {
    log_info "Recording current deployment for rollback..."
    
    # Try to get current running version
    if docker-compose --profile prod ps -q ensemble_app > /dev/null 2>&1; then
        local current_image=$(docker inspect $(docker-compose --profile prod ps -q ensemble_app) --format='{{.Config.Image}}' 2>/dev/null || echo "")
        if [[ -n "$current_image" ]]; then
            ROLLBACK_TAG=$(echo "$current_image" | grep -o '[^:]*$')
            log_info "Current deployment tag for rollback: $ROLLBACK_TAG"
        fi
    fi
    
    # Create deployment backup tag
    local backup_tag="backup-$(date +%Y%m%d-%H%M%S)"
    if [[ -n "$ROLLBACK_TAG" ]]; then
        docker tag "$REGISTRY/$IMAGE_NAME:$ROLLBACK_TAG" "$REGISTRY/$IMAGE_NAME:$backup_tag" 2>/dev/null || true
        log_info "Created backup tag: $backup_tag"
    fi
}

# Deploy new version
deploy_new_version() {
    log_info "Deploying new version: $IMAGE_TAG"
    
    # Update environment with new image tags
    export IMAGE_TAG="$IMAGE_TAG"
    
    # Deploy position optimizer first (for faster rollback if needed)
    log_info "Deploying position optimizer..."
    docker-compose --profile prod --profile optimizer pull ensemble_position_optimizer
    docker-compose --profile prod --profile optimizer up -d ensemble_position_optimizer
    
    # Wait for position optimizer to be healthy
    log_info "Waiting for position optimizer health check..."
    local retries=0
    while [[ $retries -lt $MAX_RETRIES ]]; do
        if curl -f -s "http://localhost:8001/health" > /dev/null 2>&1; then
            log_success "Position optimizer is healthy"
            break
        fi
        
        retries=$((retries + 1))
        if [[ $retries -eq $MAX_RETRIES ]]; then
            log_error "Position optimizer health check failed after $MAX_RETRIES attempts"
            return 1
        fi
        
        log_info "Position optimizer not ready, waiting... (attempt $retries/$MAX_RETRIES)"
        sleep 10
    done
    
    # Deploy main application
    log_info "Deploying main application..."
    docker-compose --profile prod pull ensemble_app
    docker-compose --profile prod up -d ensemble_app
    
    # Deploy supporting services
    log_info "Updating supporting services..."
    docker-compose --profile prod up -d ensemble_nginx ensemble_redis
    
    log_success "New version deployed successfully"
}

# Health checks
run_health_checks() {
    log_info "Running comprehensive health checks..."
    
    local start_time=$(date +%s)
    local timeout=$HEALTH_CHECK_TIMEOUT
    
    # Main application health check
    log_info "Checking main application health..."
    local retries=0
    while [[ $retries -lt $((timeout / 10)) ]]; do
        if curl -f -s "http://localhost/health" > /dev/null 2>&1; then
            log_success "Main application health check passed"
            break
        fi
        
        retries=$((retries + 1))
        if [[ $retries -eq $((timeout / 10)) ]]; then
            log_error "Main application health check failed"
            return 1
        fi
        
        sleep 10
    done
    
    # Position optimizer health check
    log_info "Checking position optimizer health..."
    local response=$(curl -s "http://localhost:8001/health" 2>/dev/null || echo '{"status":"unhealthy"}')
    local status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    
    if [[ "$status" != "healthy" ]]; then
        log_error "Position optimizer health check failed: $status"
        return 1
    fi
    log_success "Position optimizer health check passed"
    
    # Performance verification
    log_info "Verifying performance targets..."
    local perf_response=$(curl -s "http://localhost/metrics/performance" 2>/dev/null || echo '{}')
    local avg_calc_time=$(echo "$perf_response" | grep -o '"avg_calculation_time_ms":[0-9.]*' | cut -d':' -f2)
    
    if [[ -n "$avg_calc_time" ]] && (( $(echo "$avg_calc_time > 100" | bc -l) )); then
        log_warning "Performance target not met: ${avg_calc_time}ms > 100ms"
        # Don't fail deployment for performance warning, but log it
    else
        log_success "Performance targets verified: ${avg_calc_time:-N/A}ms"
    fi
    
    # Redis connectivity
    log_info "Checking Redis connectivity..."
    if docker exec ensemble_redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis connectivity verified"
    else
        log_error "Redis connectivity check failed"
        return 1
    fi
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    log_success "All health checks passed in ${total_time}s"
}

# Smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    # Test position calculation endpoint
    log_info "Testing position calculation endpoint..."
    local response=$(curl -s -X POST "http://localhost/api/v1/position/calculate" \
        -H "Content-Type: application/json" \
        -d '{"symbol":"BTCUSDT","strategy":"technical_analysis","portfolio_value":100000,"market_price":50000}' \
        2>/dev/null || echo '{}')
    
    local calc_time=$(echo "$response" | grep -o '"calculation_time_ms":[0-9.]*' | cut -d':' -f2)
    if [[ -n "$calc_time" ]] && (( $(echo "$calc_time < 100" | bc -l) )); then
        log_success "Position calculation smoke test passed: ${calc_time}ms"
    else
        log_error "Position calculation smoke test failed"
        return 1
    fi
    
    # Test volatility calculation
    log_info "Testing volatility calculation endpoint..."
    local vol_response=$(curl -s -X POST "http://localhost/api/v1/volatility/calculate" \
        -H "Content-Type: application/json" \
        -d '{"symbol":"BTCUSDT","timeframe":60,"market_data":{"price":50000,"volume":1000000,"timestamp":"'$(date -Iseconds)'"}}' \
        2>/dev/null || echo '{}')
    
    if echo "$vol_response" | grep -q '"current_volatility"'; then
        log_success "Volatility calculation smoke test passed"
    else
        log_error "Volatility calculation smoke test failed"
        return 1
    fi
    
    log_success "All smoke tests passed"
}

# Rollback function
rollback_deployment() {
    log_warning "Initiating rollback procedure..."
    
    if [[ -z "$ROLLBACK_TAG" ]]; then
        log_error "No rollback tag available"
        return 1
    fi
    
    log_info "Rolling back to previous version: $ROLLBACK_TAG"
    
    # Set rollback image tag
    export IMAGE_TAG="$ROLLBACK_TAG"
    
    # Rollback services in reverse order
    log_info "Rolling back main application..."
    docker-compose --profile prod up -d ensemble_app
    
    log_info "Rolling back position optimizer..."
    docker-compose --profile prod --profile optimizer up -d ensemble_position_optimizer
    
    # Wait for rollback health check
    sleep 30
    
    if run_health_checks; then
        log_success "Rollback completed successfully"
        
        # Send rollback notification
        send_notification "🔄 ROLLBACK COMPLETED" \
            "Production rolled back to stable version: $ROLLBACK_TAG" \
            "All services verified operational"
    else
        log_error "Rollback health checks failed"
        return 1
    fi
}

# Notification function
send_notification() {
    local title="$1"
    local message="$2"
    local details="$3"
    
    log_info "Sending notification: $title"
    
    # In a real environment, this would send to Slack, email, or other notification systems
    # For now, just log the notification
    echo "=== DEPLOYMENT NOTIFICATION ==="
    echo "Title: $title"
    echo "Message: $message"
    echo "Details: $details"
    echo "Time: $(date)"
    echo "Environment: $DEPLOYMENT_ENV"
    echo "Image Tag: $IMAGE_TAG"
    echo "=============================="
}

# Tag successful deployment
tag_successful_deployment() {
    log_info "Tagging successful deployment..."
    
    local deployment_tag="production-$(date +%Y%m%d-%H%M%S)-$IMAGE_TAG"
    
    # Tag the current commit
    if git tag "$deployment_tag" 2>/dev/null; then
        log_success "Created deployment tag: $deployment_tag"
    else
        log_warning "Failed to create git tag (may already exist)"
    fi
    
    # Record deployment in deployment log
    echo "$(date -Iseconds) | SUCCESS | $deployment_tag | $IMAGE_TAG" >> "$PROJECT_DIR/deployment.log"
}

# Main deployment function
main() {
    log_info "Starting production deployment..."
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Image Tag: $IMAGE_TAG"
    log_info "Registry: $REGISTRY"
    
    # Pre-deployment
    pre_deployment_checks
    get_current_deployment
    
    # Deployment
    deploy_new_version
    
    # Verification
    run_health_checks
    run_smoke_tests
    
    # Post-deployment
    tag_successful_deployment
    
    # Success notification
    send_notification "🎉 PRODUCTION DEPLOYMENT SUCCESSFUL" \
        "Dynamic Position Optimization v$IMAGE_TAG deployed successfully" \
        "All health checks passed, performance targets met"
    
    log_success "Production deployment completed successfully!"
    log_info "Version: $IMAGE_TAG"
    log_info "Rollback tag available: $ROLLBACK_TAG"
    
    # Display deployment summary
    echo ""
    echo "=== DEPLOYMENT SUMMARY ==="
    echo "Environment: $DEPLOYMENT_ENV"
    echo "Version: $IMAGE_TAG"
    echo "Rollback Available: $ROLLBACK_TAG"
    echo "Status: SUCCESS ✅"
    echo "Time: $(date)"
    echo "=========================="
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi