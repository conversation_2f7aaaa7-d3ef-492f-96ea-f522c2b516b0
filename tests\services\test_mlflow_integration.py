# tests/services/test_mlflow_integration.py
import pytest
import asyncio
import numpy as np
import tempfile
import os
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime

import sys
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

from app.services.mcp.mlflow_service import MLflowService, ModelDeploymentConfig, ModelPerformanceMetrics

class TestMLflowIntegration:
    """Test suite for MLflow integration"""
    
    @pytest.fixture
    def mlflow_service(self):
        """Create MLflow service instance for testing"""
        return MLflowService(
            tracking_uri="http://localhost:5000",
            experiment_name="test_ensemble_optimization",
            model_registry_name="TestEnsembleWeightOptimizer"
        )
    
    @pytest.fixture
    def mock_model(self):
        """Create mock ML model for testing"""
        mock_model = MagicMock()
        mock_model.predict = MagicMock(return_value=np.array([[0.33, 0.33, 0.34]]))
        mock_model.save = MagicMock()
        return mock_model
    
    @pytest.fixture
    def sample_metrics(self):
        """Sample metrics for testing"""
        return {
            "accuracy": 0.75,
            "sharpe_ratio": 1.2,
            "max_drawdown": -0.15,
            "win_rate": 0.65,
            "profit_factor": 1.8
        }
    
    @pytest.fixture
    def sample_hyperparameters(self):
        """Sample hyperparameters for testing"""
        return {
            "learning_rate": 0.0003,
            "batch_size": 64,
            "n_epochs": 10,
            "gamma": 0.99
        }
    
    @pytest.mark.asyncio
    async def test_start_run(self, mlflow_service):
        """Test starting MLflow run"""
        with patch('mlflow.start_run') as mock_start:
            mock_run = MagicMock()
            mock_run.info.run_id = "test_run_123"
            mock_start.return_value = mock_run
            
            run_id = await mlflow_service.start_run("test_run")
            
            assert run_id == "test_run_123"
            mock_start.assert_called_once()
    
    @pytest.mark.asyncio 
    async def test_log_model_with_signature(self, mlflow_service, mock_model, sample_metrics, sample_hyperparameters):
        """Test logging model with signature"""
        input_example = np.random.rand(1, 10)
        
        with patch('mlflow.start_run'), \
             patch('mlflow.log_params') as mock_log_params, \
             patch('mlflow.log_metrics') as mock_log_metrics, \
             patch('mlflow.log_artifact'), \
             patch('mlflow.pyfunc.log_model'):
            
            mlflow_service.current_run = MagicMock()
            mlflow_service.current_run.info.run_id = "test_run_123"
            
            run_id = await mlflow_service.log_model_with_signature(
                model=mock_model,
                model_version="v1.0.0",
                input_example=input_example,
                metrics=sample_metrics,
                hyperparameters=sample_hyperparameters
            )
            
            assert run_id == "test_run_123"
            mock_log_params.assert_called_once_with(sample_hyperparameters)
            mock_log_metrics.assert_called_once_with(sample_metrics)
    
    @pytest.mark.asyncio
    async def test_register_model(self, mlflow_service):
        """Test model registration"""
        with patch('mlflow.register_model') as mock_register, \
             patch('mlflow.update_model_version'):
            
            mock_model_version = MagicMock()
            mock_model_version.version = "1"
            mock_register.return_value = mock_model_version
            
            mlflow_service.current_run = MagicMock()
            mlflow_service.current_run.info.run_id = "test_run_123"
            
            version = await mlflow_service.register_model(
                model_version="v1.0.0",
                description="Test model"
            )
            
            assert version == "1"
            mock_register.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_deploy_model_to_stage(self, mlflow_service):
        """Test model deployment to staging/production"""
        with patch('mlflow.transition_model_version_stage') as mock_transition:
            
            success = await mlflow_service.deploy_model_to_stage(
                model_version="1",
                stage="staging"
            )
            
            assert success is True
            mock_transition.assert_called_once_with(
                name=mlflow_service.model_registry_name,
                version="1",
                stage="Staging"
            )
    
    @pytest.mark.asyncio
    async def test_load_production_model(self, mlflow_service):
        """Test loading production model"""
        with patch('mlflow.pyfunc.load_model') as mock_load:
            mock_model = MagicMock()
            mock_load.return_value = mock_model
            
            model = await mlflow_service.load_production_model()
            
            assert model == mock_model
            mock_load.assert_called_once_with(f"models:/{mlflow_service.model_registry_name}/production")
    
    @pytest.mark.asyncio
    async def test_automated_model_validation_pass(self, mlflow_service, sample_metrics):
        """Test automated model validation - passing case"""
        validation_criteria = {
            "accuracy": 0.7,
            "sharpe_ratio": 1.0,
            "win_rate": 0.6
        }
        
        with patch('mlflow.tracking.MlflowClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            # Mock model version info
            mock_version_info = MagicMock()
            mock_version_info.run_id = "test_run_123"
            mock_client.get_model_version.return_value = mock_version_info
            
            # Mock run info
            mock_run = MagicMock()
            mock_run.data.metrics = sample_metrics
            mock_client.get_run.return_value = mock_run
            
            passed, results = await mlflow_service.automated_model_validation(
                candidate_version="1",
                validation_criteria=validation_criteria
            )
            
            assert passed is True
            assert results["overall_score"] == 1.0
            assert all(results["criteria_results"][metric]["passed"] for metric in validation_criteria)
    
    @pytest.mark.asyncio
    async def test_automated_model_validation_fail(self, mlflow_service):
        """Test automated model validation - failing case"""
        validation_criteria = {
            "accuracy": 0.9,  # High threshold
            "sharpe_ratio": 2.0,  # High threshold
            "win_rate": 0.8  # High threshold
        }
        
        poor_metrics = {
            "accuracy": 0.6,  # Below threshold
            "sharpe_ratio": 0.8,  # Below threshold
            "win_rate": 0.5  # Below threshold
        }
        
        with patch('mlflow.tracking.MlflowClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            # Mock model version info
            mock_version_info = MagicMock()
            mock_version_info.run_id = "test_run_123"
            mock_client.get_model_version.return_value = mock_version_info
            
            # Mock run info
            mock_run = MagicMock()
            mock_run.data.metrics = poor_metrics
            mock_client.get_run.return_value = mock_run
            
            passed, results = await mlflow_service.automated_model_validation(
                candidate_version="1",
                validation_criteria=validation_criteria
            )
            
            assert passed is False
            assert results["overall_score"] == 0.0
            assert not any(results["criteria_results"][metric]["passed"] for metric in validation_criteria)
    
    @pytest.mark.asyncio
    async def test_compare_model_performance(self, mlflow_service):
        """Test model performance comparison"""
        baseline_metrics = {
            "accuracy": 0.7,
            "sharpe_ratio": 1.0,
            "max_drawdown": -0.2
        }
        
        candidate_metrics = {
            "accuracy": 0.75,
            "sharpe_ratio": 1.2,
            "max_drawdown": -0.15
        }
        
        with patch('mlflow.tracking.MlflowClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            # Mock baseline version
            mock_baseline_version = MagicMock()
            mock_baseline_version.run_id = "baseline_run"
            mock_client.get_model_version.return_value = mock_baseline_version
            
            # Mock candidate version  
            mock_candidate_version = MagicMock()
            mock_candidate_version.run_id = "candidate_run"
            
            # Setup return values for multiple calls
            mock_client.get_model_version.side_effect = [mock_baseline_version, mock_candidate_version]
            
            # Mock runs
            mock_baseline_run = MagicMock()
            mock_baseline_run.data.metrics = baseline_metrics
            
            mock_candidate_run = MagicMock()
            mock_candidate_run.data.metrics = candidate_metrics
            
            mock_client.get_run.side_effect = [mock_baseline_run, mock_candidate_run]
            
            comparison = await mlflow_service.compare_model_performance(
                baseline_version="1",
                candidate_version="2"
            )
            
            assert comparison["baseline_version"] == "1"
            assert comparison["candidate_version"] == "2"
            
            # Check accuracy improvement
            accuracy_comparison = comparison["metric_comparison"]["accuracy"]
            assert accuracy_comparison["baseline"] == 0.7
            assert accuracy_comparison["candidate"] == 0.75
            assert accuracy_comparison["improvement"] == 0.05
            assert abs(accuracy_comparison["improvement_pct"] - 7.14) < 0.01
            
            # Check improvement summary
            assert comparison["improvement_summary"]["accuracy"] is True
            assert comparison["improvement_summary"]["sharpe_ratio"] is True
    
    @pytest.mark.asyncio
    async def test_rollback_to_previous_version(self, mlflow_service):
        """Test model rollback functionality"""
        with patch('mlflow.tracking.MlflowClient') as mock_client_class, \
             patch('mlflow.transition_model_version_stage') as mock_transition:
            
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            # Mock current production version
            current_production = MagicMock()
            current_production.version = "2"
            current_production.current_stage = "Production"
            current_production.creation_timestamp = 1000000002
            
            # Mock previous version (archived)
            previous_version = MagicMock()
            previous_version.version = "1"
            previous_version.current_stage = "Archived"
            previous_version.creation_timestamp = 1000000001
            
            mock_client.search_model_versions.return_value = [current_production, previous_version]
            
            success = await mlflow_service.rollback_to_previous_version()
            
            assert success is True
            assert mock_transition.call_count == 2  # Archive current, promote previous
    
    @pytest.mark.asyncio
    async def test_cleanup_old_models(self, mlflow_service):
        """Test cleanup of old model versions"""
        with patch('mlflow.tracking.MlflowClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            # Create mock versions
            versions = []
            for i in range(10):
                version = MagicMock()
                version.version = str(i + 1)
                version.creation_timestamp = 1000000000 + i
                version.current_stage = "Archived" if i < 8 else "Production"
                versions.append(version)
            
            mock_client.search_model_versions.return_value = versions
            
            deleted_count = await mlflow_service.cleanup_old_models(keep_versions=3)
            
            # Should delete old archived versions, keeping latest 3 + production/staging
            assert deleted_count >= 0  # Some versions should be deleted
            assert mock_client.delete_model_version.call_count >= 0

class TestModelDeploymentWorkflow:
    """Test complete model deployment workflow"""
    
    @pytest.mark.asyncio
    async def test_deploy_trained_model_success(self):
        """Test successful model deployment workflow"""
        mock_model = MagicMock()
        mock_model.predict = MagicMock(return_value=np.array([[0.33, 0.33, 0.34]]))
        
        training_metrics = {
            "accuracy": 0.8,
            "sharpe_ratio": 1.5,
            "win_rate": 0.7
        }
        
        hyperparameters = {
            "learning_rate": 0.0003,
            "batch_size": 64
        }
        
        input_example = np.random.rand(1, 10)
        
        validation_criteria = {
            "accuracy": 0.75,
            "sharpe_ratio": 1.0,
            "win_rate": 0.6
        }
        
        with patch('app.services.mcp.mlflow_service.MLflowService') as mock_mlflow_class:
            mock_mlflow = AsyncMock()
            mock_mlflow_class.return_value = mock_mlflow
            
            # Setup mock methods
            mock_mlflow.start_run.return_value = "test_run_123"
            mock_mlflow.log_model_with_signature.return_value = "test_run_123"
            mock_mlflow.register_model.return_value = "1"
            mock_mlflow.deploy_model_to_stage.return_value = True
            mock_mlflow.automated_model_validation.return_value = (True, {"overall_score": 1.0})
            mock_mlflow.end_run.return_value = None
            
            from app.services.mcp.mlflow_service import deploy_trained_model
            
            success, message = await deploy_trained_model(
                model=mock_model,
                model_version="v1.0.0",
                training_metrics=training_metrics,
                hyperparameters=hyperparameters,
                input_example=input_example,
                validation_criteria=validation_criteria
            )
            
            assert success is True
            assert "deployed to production" in message
            
            # Verify all steps were called
            mock_mlflow.start_run.assert_called_once()
            mock_mlflow.log_model_with_signature.assert_called_once()
            mock_mlflow.register_model.assert_called_once()
            mock_mlflow.automated_model_validation.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_deploy_trained_model_validation_failure(self):
        """Test model deployment with validation failure"""
        mock_model = MagicMock()
        
        training_metrics = {
            "accuracy": 0.6,  # Below validation threshold
            "sharpe_ratio": 0.8,
            "win_rate": 0.5
        }
        
        hyperparameters = {"learning_rate": 0.0003}
        input_example = np.random.rand(1, 10)
        
        validation_criteria = {
            "accuracy": 0.75,  # Higher than model performance
            "sharpe_ratio": 1.0,
            "win_rate": 0.6
        }
        
        with patch('app.services.mcp.mlflow_service.MLflowService') as mock_mlflow_class:
            mock_mlflow = AsyncMock()
            mock_mlflow_class.return_value = mock_mlflow
            
            # Setup mock methods
            mock_mlflow.start_run.return_value = "test_run_123"
            mock_mlflow.log_model_with_signature.return_value = "test_run_123"
            mock_mlflow.register_model.return_value = "1"
            mock_mlflow.deploy_model_to_stage.return_value = True
            mock_mlflow.automated_model_validation.return_value = (False, {"overall_score": 0.3})
            mock_mlflow.end_run.return_value = None
            
            from app.services.mcp.mlflow_service import deploy_trained_model
            
            success, message = await deploy_trained_model(
                model=mock_model,
                model_version="v1.0.0",
                training_metrics=training_metrics,
                hyperparameters=hyperparameters,
                input_example=input_example,
                validation_criteria=validation_criteria
            )
            
            assert success is False
            assert "validation failed" in message

if __name__ == "__main__":
    # Run specific test
    pytest.main([__file__, "-v"])