#!/bin/bash

# Emergency Rollback Script for Dynamic Position Optimization
# Task 2.2.2: Automated rollback procedures

set -e

echo "🔄 Dynamic Position Optimization - Emergency Rollback"
echo "===================================================="

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ROLLBACK_TO="${1:-auto}"  # Target version or 'auto' for latest stable
FORCE_ROLLBACK="${2:-false}"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get available rollback versions
get_rollback_versions() {
    log_info "Scanning for available rollback versions..."
    
    # Get production tags from git
    local prod_tags=$(git tag -l "production-*" | sort -V | tail -5)
    
    if [[ -z "$prod_tags" ]]; then
        log_error "No production deployment tags found"
        return 1
    fi
    
    echo "Available rollback versions:"
    echo "$prod_tags" | nl -w2 -s'. '
    
    if [[ "$ROLLBACK_TO" == "auto" ]]; then
        # Get the second-to-last tag (current should be last)
        ROLLBACK_TO=$(echo "$prod_tags" | tail -2 | head -1)
        log_info "Auto-selected rollback version: $ROLLBACK_TO"
    fi
}

# Validate rollback target
validate_rollback_target() {
    log_info "Validating rollback target: $ROLLBACK_TO"
    
    # Extract image tag from production tag
    local image_tag=$(echo "$ROLLBACK_TO" | rev | cut -d'-' -f1 | rev)
    
    # Check if rollback images exist
    if [[ -n "$REGISTRY" ]] && [[ -n "$IMAGE_NAME" ]]; then
        log_info "Checking if rollback images exist..."
        
        if ! docker pull "$REGISTRY/$IMAGE_NAME:$image_tag" > /dev/null 2>&1; then
            log_error "Rollback image not found: $REGISTRY/$IMAGE_NAME:$image_tag"
            return 1
        fi
        
        if ! docker pull "$REGISTRY/$IMAGE_NAME-position-optimizer:$image_tag" > /dev/null 2>&1; then
            log_error "Position optimizer rollback image not found: $REGISTRY/$IMAGE_NAME-position-optimizer:$image_tag"
            return 1
        fi
        
        export IMAGE_TAG="$image_tag"
        log_success "Rollback images validated"
    else
        log_warning "Registry/image name not set, skipping image validation"
    fi
}

# Get current system status
get_current_status() {
    log_info "Checking current system status..."
    
    # Check if services are running
    local running_services=$(docker-compose --profile prod ps --services --filter "status=running" 2>/dev/null | wc -l)
    local total_services=$(docker-compose --profile prod config --services 2>/dev/null | wc -l)
    
    log_info "Running services: $running_services/$total_services"
    
    # Check health status
    if curl -f -s "http://localhost/health" > /dev/null 2>&1; then
        log_info "Main application: HEALTHY"
    else
        log_warning "Main application: UNHEALTHY"
    fi
    
    if curl -f -s "http://localhost:8001/health" > /dev/null 2>&1; then
        log_info "Position optimizer: HEALTHY"
    else
        log_warning "Position optimizer: UNHEALTHY"
    fi
}

# Confirm rollback
confirm_rollback() {
    if [[ "$FORCE_ROLLBACK" != "true" ]]; then
        echo ""
        log_warning "WARNING: This will rollback the production system to: $ROLLBACK_TO"
        log_warning "Current deployment will be replaced!"
        echo ""
        read -p "Are you sure you want to proceed? (yes/no): " -r
        
        if [[ ! $REPLY =~ ^[Yy]es$ ]]; then
            log_info "Rollback cancelled by user"
            exit 0
        fi
    fi
}

# Create emergency backup
create_emergency_backup() {
    log_info "Creating emergency backup of current state..."
    
    # Tag current images as emergency backup
    local backup_tag="emergency-backup-$(date +%Y%m%d-%H%M%S)"
    
    # Get current running images
    local current_app_image=$(docker inspect $(docker-compose --profile prod ps -q ensemble_app 2>/dev/null) --format='{{.Config.Image}}' 2>/dev/null || echo "")
    local current_opt_image=$(docker inspect $(docker-compose --profile prod ps -q ensemble_position_optimizer 2>/dev/null) --format='{{.Config.Image}}' 2>/dev/null || echo "")
    
    if [[ -n "$current_app_image" ]]; then
        docker tag "$current_app_image" "${current_app_image%:*}:$backup_tag" 2>/dev/null || true
        log_info "Created app backup: $backup_tag"
    fi
    
    if [[ -n "$current_opt_image" ]]; then
        docker tag "$current_opt_image" "${current_opt_image%:*}:$backup_tag" 2>/dev/null || true
        log_info "Created optimizer backup: $backup_tag"
    fi
    
    # Save configuration backup
    docker-compose --profile prod config > "$PROJECT_DIR/backup-config-$backup_tag.yml" 2>/dev/null || true
}

# Execute rollback
execute_rollback() {
    log_info "Executing rollback to: $ROLLBACK_TO"
    
    # Stop services gracefully
    log_info "Stopping current services..."
    docker-compose --profile prod --profile optimizer down --timeout 30
    
    # Pull rollback images
    log_info "Pulling rollback images..."
    docker-compose --profile prod --profile optimizer pull
    
    # Start services with rollback images
    log_info "Starting services with rollback version..."
    
    # Start Redis first
    docker-compose --profile prod up -d ensemble_redis
    sleep 5
    
    # Start position optimizer
    docker-compose --profile prod --profile optimizer up -d ensemble_position_optimizer
    sleep 10
    
    # Start main application
    docker-compose --profile prod up -d ensemble_app
    sleep 10
    
    # Start nginx
    docker-compose --profile prod up -d ensemble_nginx
    
    log_success "Rollback deployment completed"
}

# Verify rollback
verify_rollback() {
    log_info "Verifying rollback success..."
    
    local max_retries=30
    local retry_count=0
    
    # Wait for services to be ready
    while [[ $retry_count -lt $max_retries ]]; do
        if curl -f -s "http://localhost/health" > /dev/null 2>&1; then
            log_success "Main application is healthy after rollback"
            break
        fi
        
        retry_count=$((retry_count + 1))
        if [[ $retry_count -eq $max_retries ]]; then
            log_error "Rollback verification failed - main application not healthy"
            return 1
        fi
        
        log_info "Waiting for main application... ($retry_count/$max_retries)"
        sleep 5
    done
    
    # Check position optimizer
    retry_count=0
    while [[ $retry_count -lt $max_retries ]]; do
        if curl -f -s "http://localhost:8001/health" > /dev/null 2>&1; then
            log_success "Position optimizer is healthy after rollback"
            break
        fi
        
        retry_count=$((retry_count + 1))
        if [[ $retry_count -eq $max_retries ]]; then
            log_error "Rollback verification failed - position optimizer not healthy"
            return 1
        fi
        
        log_info "Waiting for position optimizer... ($retry_count/$max_retries)"
        sleep 5
    done
    
    # Quick smoke test
    log_info "Running rollback smoke test..."
    local response=$(curl -s -X POST "http://localhost/api/v1/position/calculate" \
        -H "Content-Type: application/json" \
        -d '{"symbol":"BTCUSDT","strategy":"technical_analysis","portfolio_value":100000,"market_price":50000}' \
        2>/dev/null || echo '{}')
    
    if echo "$response" | grep -q '"recommended_position_size"'; then
        log_success "Rollback smoke test passed"
    else
        log_error "Rollback smoke test failed"
        return 1
    fi
    
    log_success "Rollback verification completed successfully"
}

# Send rollback notification
send_rollback_notification() {
    local status="$1"
    
    if [[ "$status" == "success" ]]; then
        local title="✅ ROLLBACK COMPLETED SUCCESSFULLY"
        local message="Production system rolled back to: $ROLLBACK_TO"
        local details="All services verified operational and healthy"
    else
        local title="❌ ROLLBACK FAILED"
        local message="Emergency rollback to $ROLLBACK_TO failed"
        local details="Manual intervention required immediately"
    fi
    
    echo "=== ROLLBACK NOTIFICATION ==="
    echo "Title: $title"
    echo "Message: $message"
    echo "Details: $details"
    echo "Target Version: $ROLLBACK_TO"
    echo "Time: $(date)"
    echo "=========================="
}

# Main rollback function
main() {
    log_info "Starting emergency rollback procedure..."
    
    # Validate parameters
    if [[ "$ROLLBACK_TO" != "auto" ]] && [[ ! "$ROLLBACK_TO" =~ ^production- ]]; then
        log_error "Invalid rollback target. Must be 'auto' or a production tag (production-*)"
        exit 1
    fi
    
    # Get current status
    get_current_status
    
    # Find rollback versions
    get_rollback_versions
    
    # Validate rollback target
    validate_rollback_target
    
    # Confirm rollback
    confirm_rollback
    
    # Execute rollback
    create_emergency_backup
    execute_rollback
    
    # Verify rollback
    if verify_rollback; then
        send_rollback_notification "success"
        log_success "Emergency rollback completed successfully!"
        echo ""
        echo "=== ROLLBACK SUMMARY ==="
        echo "Rolled back to: $ROLLBACK_TO"
        echo "Status: SUCCESS ✅"
        echo "Time: $(date)"
        echo "Next steps: Investigate root cause of original failure"
        echo "======================="
    else
        send_rollback_notification "failure"
        log_error "Rollback failed! Manual intervention required."
        exit 1
    fi
}

# Display usage
usage() {
    echo "Usage: $0 [ROLLBACK_VERSION] [FORCE]"
    echo ""
    echo "Parameters:"
    echo "  ROLLBACK_VERSION  Target version (default: auto - latest stable)"
    echo "  FORCE            Set to 'true' to skip confirmation (default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Auto rollback to latest stable"
    echo "  $0 production-20240614-143022-abc123  # Rollback to specific version"
    echo "  $0 auto true                         # Force auto rollback"
    echo ""
    echo "Available versions:"
    git tag -l "production-*" | tail -5 || echo "  No production tags found"
}

# Handle script arguments
if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    usage
    exit 0
fi

# Execute main function
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi