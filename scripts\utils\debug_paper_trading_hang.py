#!/usr/bin/env python3
"""
Debug Paper Trading Hang Issue
Find exactly where the paper trading initialization hangs.
"""

import asyncio
import logging
import time
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_paper_trading_step_by_step():
    """Debug each step of paper trading initialization"""
    logger.info("🔍 Starting step-by-step paper trading debug...")
    
    try:
        # Step 1: Basic imports
        logger.info("📦 Step 1: Testing imports...")
        start_time = time.time()
        
        try:
            from app.strategies.paper_trading_portfolio_manager import PaperTradingPortfolioManager
            logger.info(f"✅ PaperTradingPortfolioManager imported successfully ({(time.time() - start_time)*1000:.1f}ms)")
        except Exception as e:
            logger.error(f"❌ Failed to import PaperTradingPortfolioManager: {e}")
            return False
        
        # Step 2: Import services
        logger.info("📦 Step 2: Testing service imports...")
        
        try:
            from app.models.market_data import MarketData
            logger.info("✅ MarketData imported")
            
            from app.services.mcp.redis_service import RedisService
            logger.info("✅ RedisService imported")
            
            from app.services.cost_calculator import CostCalculator
            logger.info("✅ CostCalculator imported")
            
            from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator
            logger.info("✅ EnhancedSlippageEstimator imported")
        except Exception as e:
            logger.error(f"❌ Failed to import services: {e}")
            return False
        
        # Step 3: Create mock services with timeout
        logger.info("🔧 Step 3: Creating mock services...")
        
        class SimpleRedisService:
            def __init__(self, url):
                self.url = url
                
            async def connect(self):
                await asyncio.sleep(0.001)  # Minimal delay
                logger.info("✅ Mock Redis connected")
                
            async def ping(self):
                return True
                
            async def get(self, key):
                return None
                
            async def setex(self, key, ttl, value):
                pass
                
            async def delete(self, key):
                pass
        
        class SimpleCostCalculator:
            async def calculate_total_trading_cost(self, **kwargs):
                from dataclasses import dataclass
                
                @dataclass
                class SimpleCostResult:
                    total_cost_usd: float = 5.0
                    total_cost_bps: float = 10.0
                    exchange_fees_usd: float = 3.0
                    slippage_cost_usd: float = 2.0
                    market_impact_cost_usd: float = 0.0
                    funding_costs_usd: float = 0.0
                    withdrawal_fees_usd: float = 0.0
                    confidence: float = 0.8
                    optimization_suggestions: list = None
                    
                    def __post_init__(self):
                        if self.optimization_suggestions is None:
                            self.optimization_suggestions = []
                
                return SimpleCostResult()
        
        class SimpleSlippageEstimator:
            def __init__(self, **kwargs):
                pass
                
            async def estimate_multi_exchange_slippage(self, **kwargs):
                from dataclasses import dataclass
                
                @dataclass
                class SimpleSlippageResult:
                    consensus_slippage_bps: float = 5.0
                    min_slippage_bps: float = 3.0
                    max_slippage_bps: float = 8.0
                
                return SimpleSlippageResult()
        
        # Create services
        redis_service = SimpleRedisService("redis://localhost:6379")
        cost_calculator = SimpleCostCalculator()
        slippage_estimator = SimpleSlippageEstimator()
        
        logger.info("✅ Mock services created")
        
        # Step 4: Create portfolio manager with timeout
        logger.info("🏗️ Step 4: Creating PaperTradingPortfolioManager...")
        start_time = time.time()
        
        try:
            # Use asyncio.wait_for to add timeout
            manager = await asyncio.wait_for(
                asyncio.create_task(
                    create_manager_async(redis_service, cost_calculator, slippage_estimator)
                ),
                timeout=10.0
            )
            
            creation_time = (time.time() - start_time) * 1000
            logger.info(f"✅ PaperTradingPortfolioManager created successfully ({creation_time:.1f}ms)")
            
        except asyncio.TimeoutError:
            logger.error("❌ PaperTradingPortfolioManager creation timed out after 10 seconds")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to create PaperTradingPortfolioManager: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Step 5: Test basic operations
        logger.info("📊 Step 5: Testing basic operations...")
        
        try:
            summary = await asyncio.wait_for(manager.get_portfolio_summary(), timeout=5.0)
            logger.info("✅ Portfolio summary retrieved successfully")
            
            if summary and summary.get('account', {}).get('initial_balance') == 100000.0:
                logger.info("✅ Portfolio summary validation passed")
                return True
            else:
                logger.error(f"❌ Portfolio summary validation failed: {summary}")
                return False
                
        except asyncio.TimeoutError:
            logger.error("❌ Portfolio summary retrieval timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Portfolio summary retrieval failed: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Debug process failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def create_manager_async(redis_service, cost_calculator, slippage_estimator):
    """Create manager asynchronously with detailed logging"""
    logger.info("🔨 Creating PaperTradingPortfolioManager instance...")
    
    # Optimized config for testing
    config = {
        "max_position_size_pct": 20.0,
        "slippage_simulation": True,
        "fee_simulation": True,
        "enable_cost_optimization": False,  # Disable to simplify
        "execution_latency_ms": 0,
        "market_data_latency_ms": 0,
        "performance_cache_ttl": 10,
        "order_cache_ttl": 10,
        "portfolio_cache_ttl": 10,
        "enable_performance_tracking": False,
        "enable_telegram_alerts": False
    }
    
    try:
        manager = PaperTradingPortfolioManager(
            initial_balance_usd=100000.0,
            redis_service=redis_service,
            cost_calculator=cost_calculator,
            slippage_estimator=slippage_estimator,
            config=config
        )
        
        logger.info("✅ PaperTradingPortfolioManager instance created")
        return manager
        
    except Exception as e:
        logger.error(f"❌ PaperTradingPortfolioManager instantiation failed: {e}")
        raise

async def main():
    """Main debug function"""
    print("=" * 60)
    print("🔍 DEBUGGING PAPER TRADING HANG ISSUE")
    print("=" * 60)
    
    try:
        success = await debug_paper_trading_step_by_step()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ PAPER TRADING DEBUG SUCCESSFUL")
            print("No hanging issues detected!")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ PAPER TRADING HANG DETECTED")
            print("Check the detailed logs above for the exact failure point.")
            print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)