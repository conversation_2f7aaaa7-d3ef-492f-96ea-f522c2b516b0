# Script to run both the backend and frontend servers

# Function to check if a port is in use
function Test-PortInUse {
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    return $null -ne $connections
}

# Function to stop a process using a specific port
function Stop-ProcessOnPort {
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($null -ne $connections) {
        foreach ($conn in $connections) {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            if ($null -ne $process) {
                Write-Host "Stopping process $($process.Name) (PID: $($process.Id)) using port $Port" -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
            }
        }
    }
}

# Function to open URLs in the default browser
function Open-Urls {
    param (
        [string[]]$Urls
    )

    foreach ($url in $Urls) {
        Write-Host "Opening $url in default browser..." -ForegroundColor Cyan
        Start-Process $url
    }
}

# Function to start the backend server
function Start-Backend {
    Write-Host "Starting FastAPI backend server on http://localhost:8000..." -ForegroundColor Green

    # Check if port 8000 is already in use
    if (Test-PortInUse -Port 8000) {
        Write-Host "Port 8000 is already in use. Attempting to free it..." -ForegroundColor Yellow
        Stop-ProcessOnPort -Port 8000
        Start-Sleep -Seconds 2
    }

    # Set environment variables for the backend
    $env:PYTHONWARNINGS = "ignore"
    $env:LOG_LEVEL = "INFO"
    $env:CORS_ORIGINS = '["http://localhost:3000"]'
    $env:DISABLE_FRONTEND_MOUNT = "true"

    # Set watchfiles logging to WARNING level to reduce verbosity
    $env:WATCHFILES_FORCE_POLLING = "False"
    $env:WATCHFILES_LOG_LEVEL = "WARNING"

    # Start the backend server in a new process with venv activated
    $backendProcess = Start-Process -FilePath "bash" -ArgumentList "-c", "source venv/bin/activate && python -m app.dashboard.main" -PassThru -NoNewWindow

    # Check if the backend server started successfully
    if ($null -eq $backendProcess -or $backendProcess.HasExited) {
        Write-Host "Failed to start backend server!" -ForegroundColor Red
        return $null
    }

    Write-Host "Backend server started with process ID: $($backendProcess.Id)" -ForegroundColor Green
    return $backendProcess
}

# Function to start the frontend server
function Start-Frontend {
    Write-Host "Starting React frontend server on http://localhost:3000..." -ForegroundColor Green

    # Check if port 3000 is already in use
    if (Test-PortInUse -Port 3000) {
        Write-Host "Port 3000 is already in use. Attempting to free it..." -ForegroundColor Yellow
        Stop-ProcessOnPort -Port 3000
        Start-Sleep -Seconds 2
    }

    # Navigate to the frontend directory
    Push-Location app\dashboard\frontend

    # Check if package.json exists
    if (-not (Test-Path -Path "package.json")) {
        Write-Host "package.json not found in frontend directory. Cannot start frontend development server." -ForegroundColor Red
        Pop-Location
        return $null
    }

    # Create or update .env file to set the correct API URL
    $envContent = "REACT_APP_API_URL=http://localhost:8000"
    Set-Content -Path ".env" -Value $envContent
    Write-Host "Created .env file with API URL pointing to backend server" -ForegroundColor Green

    # Check if Node.js is installed
    try {
        $nodeVersion = & node --version
        Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "Node.js is not installed or not in PATH. Cannot start frontend development server." -ForegroundColor Red
        Pop-Location
        return $null
    }

    # Start the frontend server with node directly
    try {
        Write-Host "Starting frontend with node directly..." -ForegroundColor Yellow
        $frontendProcess = Start-Process -FilePath "node" -ArgumentList "./node_modules/react-scripts/scripts/start.js" -PassThru -NoNewWindow -ErrorAction Stop
    }
    catch {
        Write-Host "Error starting frontend with node: $_" -ForegroundColor Red
        Pop-Location
        return $null
    }

    # Return to the original directory
    Pop-Location

    # Check if the process started successfully
    if ($null -eq $frontendProcess -or $frontendProcess.HasExited) {
        Write-Host "Failed to start frontend server!" -ForegroundColor Red
        return $null
    }

    Write-Host "Frontend server started with process ID: $($frontendProcess.Id)" -ForegroundColor Green
    return $frontendProcess
}

# Display header
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host "       Crypto App - Backend and Frontend Launcher   " -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan

# Start the backend server
# No need to apply the logging patch anymore as we've fixed the main.py file directly

$backendProcess = Start-Backend

# Wait for the backend to initialize
Write-Host "Waiting for backend server to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start the frontend server
$frontendProcess = Start-Frontend

# Wait for the frontend to initialize
if ($null -ne $frontendProcess) {
    Write-Host "Waiting for frontend server to initialize..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10

    # Open the URLs in the default browser
    Open-Urls -Urls @(
        "http://localhost:8000/docs",      # Backend API documentation
        "http://localhost:3000/trading"  # Frontend application trading page
    )
} else {
    # Only open backend API docs if frontend failed to start
    Open-Urls -Urls @(
        "http://localhost:8000/docs"       # Backend API documentation
    )
}

# Automatically start the strategy selector
Write-Host "Automatically starting the strategy selector..." -ForegroundColor Green

# Wait for the backend to be fully initialized
Start-Sleep -Seconds 5

# Check if -NoModify parameter was provided
if ($args -contains "-NoModify") {
    Write-Host "Skipping modification of strategy_selector.py file due to -NoModify parameter." -ForegroundColor Yellow
} else {
    # Modify the strategy_selector.py file to reduce the waiting period
    Write-Host "Modifying the strategy_selector.py file to reduce the waiting period..." -ForegroundColor Yellow
    $strategyFile = "app/strategies/strategy_selector.py"
    if (Test-Path $strategyFile) {
        $content = Get-Content $strategyFile -Raw
        $content = $content -replace "wait_seconds = max\(10, min\(wait_seconds, 300\)\)  # Between 10s and 5min", "wait_seconds = max(10, min(wait_seconds, 60))  # Between 10s and 1min"
        Set-Content $strategyFile -Value $content
        Write-Host "Successfully modified the strategy_selector.py file." -ForegroundColor Green
    } else {
        Write-Host "Could not find the strategy_selector.py file." -ForegroundColor Red
    }
}

# Display status information
Write-Host ""
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host "       Crypto App is now running!                   " -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Backend API:       http://localhost:8000" -ForegroundColor Green
Write-Host "API Documentation: http://localhost:8000/docs" -ForegroundColor Green

if ($null -ne $frontendProcess) {
    Write-Host "Frontend App:      http://localhost:3000/trading" -ForegroundColor Green
} else {
    Write-Host "Frontend App:      Not available - failed to start development server" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press Ctrl+C to stop the servers..." -ForegroundColor Yellow

# Wait for Ctrl+C
try {
    while ($true) {
        # Check if backend process has exited
        if ($backendProcess.HasExited) {
            Write-Host "Backend server has stopped unexpectedly!" -ForegroundColor Red
            break
        }

        # Check if frontend process has exited (only if it was started successfully)
        if ($null -ne $frontendProcess -and $frontendProcess.HasExited) {
            Write-Host "Frontend server has stopped unexpectedly!" -ForegroundColor Red
            break
        }

        Start-Sleep -Seconds 1
    }
}
finally {
    # Stop the backend process if it's still running
    if (-not $backendProcess.HasExited) {
        Write-Host "Stopping backend server (PID: $($backendProcess.Id))..." -ForegroundColor Yellow
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    }

    # Stop the frontend process if it was started and is still running
    if ($null -ne $frontendProcess -and -not $frontendProcess.HasExited) {
        Write-Host "Stopping frontend server (PID: $($frontendProcess.Id))..." -ForegroundColor Yellow
        Stop-Process -Id $frontendProcess.Id -Force -ErrorAction SilentlyContinue
    }

    Write-Host "All servers stopped." -ForegroundColor Green
}
