"""
Feature engineering module for ML weight optimization.

This module contains functions for transforming raw market data into features
suitable for training ML models.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """Transforms raw market data into features for ML models."""

    def __init__(self):
        """Initialize the FeatureEngineer."""
        self.logger = logging.getLogger(__name__)
        self.scalers = {}

    def extract_market_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract features from market data.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            DataFrame with extracted features
        """
        try:
            if df.empty:
                self.logger.warning("Empty DataFrame provided for feature extraction")
                return pd.DataFrame()

            # Diagnostic logging: check for NaNs and data shape
            self.logger.info(f"Input market_data shape: {df.shape}")
            self.logger.info(f"Input market_data NaN counts per column: {df.isnull().sum().to_dict()}")
            self.logger.info(f"Input market_data head:\n{df.head(5)}")

            # Create a copy to avoid modifying the original
            result = df.copy()

            # Basic price features
            result['returns'] = result['close'].pct_change()
            result['log_returns'] = np.log(result['close'] / result['close'].shift(1))

            # Volatility features
            # Population standard deviation for single sample should be 0
            result['volatility_1d'] = result['returns'].rolling(window=1).std(ddof=0)
            result['volatility_5d'] = result['returns'].rolling(window=5).std()
            result['volatility_10d'] = result['returns'].rolling(window=10).std()

            # Volume features
            result['volume_change'] = result['volume'].pct_change()
            result['volume_ma_5'] = result['volume'].rolling(window=5).mean()
            result['volume_ma_10'] = result['volume'].rolling(window=10).mean()

            # Price momentum
            result['price_momentum_1d'] = result['close'] / result['close'].shift(1) - 1
            result['price_momentum_5d'] = result['close'] / result['close'].shift(5) - 1
            result['price_momentum_10d'] = result['close'] / result['close'].shift(10) - 1

            # Moving averages
            result['ma_5'] = result['close'].rolling(window=5).mean()
            result['ma_10'] = result['close'].rolling(window=10).mean()
            result['ma_20'] = result['close'].rolling(window=20).mean()

            # Moving average crossovers
            result['ma_5_10_cross'] = result['ma_5'] - result['ma_10']
            result['ma_10_20_cross'] = result['ma_10'] - result['ma_20']

            # Bollinger Bands
            result['bb_middle'] = result['close'].rolling(window=20).mean()
            result['bb_std'] = result['close'].rolling(window=20).std()
            result['bb_upper'] = result['bb_middle'] + 2 * result['bb_std']
            result['bb_lower'] = result['bb_middle'] - 2 * result['bb_std']
            result['bb_width'] = (result['bb_upper'] - result['bb_lower']) / result['bb_middle']
            result['bb_position'] = (result['close'] - result['bb_lower']) / (result['bb_upper'] - result['bb_lower'])

            # RSI (Relative Strength Index)
            delta = result['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            result['rsi'] = 100 - (100 / (1 + rs))

            # After feature creation, log NaN counts per feature
            self.logger.info(f"NaN counts per feature after extraction: {result.isnull().sum().to_dict()}")

            # Drop rows only if critical features are NaN
            critical_features = ['close', 'returns', 'log_returns', 'rsi']
            before_drop = len(result)
            result = result.dropna(subset=critical_features)
            after_drop = len(result)
            self.logger.info(f"Rows dropped due to NaN in critical features: {before_drop - after_drop}")

            # For all other features, use ffill then bfill to handle NaNs
            non_critical = [col for col in result.columns if col not in critical_features]
            result[non_critical] = result[non_critical].ffill().bfill()

            # Log number of rows remaining
            self.logger.info(f"Rows remaining after NaN handling: {len(result)}")

            self.logger.info(f"Extracted {len(result.columns) - len(df.columns)} features from market data")
            # Ensure all columns are numeric float32 for RL compatibility
            result = result.astype(np.float32)
            return result

        except Exception as e:
            self.logger.error(f"Error extracting market features: {e}")
            return pd.DataFrame()

    def normalize_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """Normalize features to have zero mean and unit variance.

        Args:
            df: DataFrame with features
            fit: Whether to fit the scaler or use existing

        Returns:
            DataFrame with normalized features
        """
        try:
            if df.empty:
                self.logger.warning("Empty DataFrame provided for normalization")
                return pd.DataFrame()

            # Create a copy to avoid modifying the original
            result = df.copy()

            # Normalize each feature
            for column in result.columns:
                if column not in ['timestamp', 'date', 'time']:
                    # Create a scaler if needed
                    if fit or column not in self.scalers:
                        self.scalers[column] = StandardScaler()
                        result[column] = self.scalers[column].fit_transform(result[[column]])
                    else:
                        result[column] = self.scalers[column].transform(result[[column]])

            self.logger.info(f"Normalized {len(result.columns)} features")
            return result

        except Exception as e:
            self.logger.error(f"Error normalizing features: {e}")
            return pd.DataFrame()

    def create_time_windows(self, df: pd.DataFrame, window_size: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """Create time windows for sequence-based models.

        Args:
            df: DataFrame with features
            window_size: Size of the time window

        Returns:
            Tuple of (X, y) where X is the input sequences and y is the target values
        """
        try:
            if df.empty:
                self.logger.warning("Empty DataFrame provided for time window creation")
                return np.array([]), np.array([])

            # Create sequences
            sequences = []
            targets = []

            for i in range(len(df) - window_size):
                # Input sequence
                sequence = df.iloc[i:i+window_size].values
                sequences.append(sequence)

                # Target (next day's return)
                target = df['returns'].iloc[i+window_size]
                targets.append(target)

            X = np.array(sequences)
            y = np.array(targets)

            self.logger.info(f"Created {len(X)} time windows of size {window_size}")
            return X, y

        except Exception as e:
            self.logger.error(f"Error creating time windows: {e}")
            return np.array([]), np.array([])

    def prepare_training_data(self, market_data: pd.DataFrame,
                             strategy_performances: Dict[str, pd.DataFrame],
                             window_size: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for training ML models.

        Args:
            market_data: DataFrame with market data
            strategy_performances: Dict of DataFrames with strategy performance data
            window_size: Size of the time window

        Returns:
            Tuple of (X, y) where X is the input features and y is the target values
        """
        try:
            if market_data.empty:
                self.logger.warning("Empty market data provided for training data preparation")
                return np.array([]), np.array([])

            # Extract features from market data
            features_df = self.extract_market_features(market_data)

            # Normalize features
            normalized_df = self.normalize_features(features_df)

            # Create time windows
            X, _ = self.create_time_windows(normalized_df, window_size)

            # Prepare target values (strategy weights)
            y = []

            # For each time window, find the best strategy based on performance
            for i in range(len(X)):
                # Get the date for this window
                window_end_date = features_df.index[i + window_size]

                # Find the closest performance data for each strategy
                strategy_scores = {}

                for strategy_name, performance_df in strategy_performances.items():
                    if performance_df.empty:
                        strategy_scores[strategy_name] = 0.0
                        continue

                    # Find the closest date
                    closest_date = performance_df.index[performance_df.index.get_indexer([window_end_date], method='nearest')[0]]

                    # Get the performance score (profit)
                    if 'profit' in performance_df.columns:
                        strategy_scores[strategy_name] = performance_df.loc[closest_date, 'profit']
                    else:
                        strategy_scores[strategy_name] = 0.0

                # Normalize scores to sum to 1
                total_score = sum(strategy_scores.values())
                if total_score > 0:
                    normalized_scores = {k: v / total_score for k, v in strategy_scores.items()}
                else:
                    # If all scores are zero or negative, assign equal weight
                    num_strategies = len(strategy_performances)
                    normalized_scores = {k: 1.0 / num_strategies for k in strategy_performances.keys()}

                y.append(list(normalized_scores.values()))

            y = np.array(y)

            self.logger.info(f"Prepared training data with X shape {X.shape} and y shape {y.shape}")
            return X, y

        except Exception as e:
            self.logger.error(f"Error preparing training data: {e}")
            return np.array([]), np.array([])
