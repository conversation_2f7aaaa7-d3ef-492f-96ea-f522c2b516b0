#!/usr/bin/env python3
"""
Real-Time Correlation Matrix Calculator for Strategy Ensemble System
Implements Task 2.1.3: Real-time correlation matrix with Redis caching and position adjustments.

Features:
- Rolling correlation calculations with multiple timeframes
- Strategy correlation monitoring and risk management
- Redis caching for sub-second correlation matrix access
- Dynamic correlation-based position adjustments
- Comprehensive correlation risk analytics
- Multiple correlation models (<PERSON>, <PERSON>, Dynamic)
"""

import asyncio
import json
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import logging
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import warnings

from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

@dataclass
class CorrelationConfig:
    """Configuration for correlation calculator"""
    # Rolling window parameters
    short_window: int = 50        # 50-period short-term correlation
    medium_window: int = 200      # 200-period medium-term correlation
    long_window: int = 500        # 500-period long-term correlation
    
    # Correlation thresholds
    high_correlation_threshold: float = 0.7    # Above 70% is high correlation
    extreme_correlation_threshold: float = 0.9 # Above 90% is extreme correlation
    low_correlation_threshold: float = 0.3     # Below 30% is low correlation
    
    # Dynamic correlation parameters
    ewma_alpha: float = 0.05      # Decay factor for EWMA correlation
    correlation_half_life: int = 100  # Half-life for exponential weighting
    
    # Position adjustment parameters
    max_correlated_exposure: float = 0.6    # Maximum exposure to correlated strategies
    correlation_penalty_factor: float = 0.5  # Penalty factor for high correlation
    diversification_bonus: float = 1.2      # Bonus for uncorrelated strategies
    
    # Cache settings
    cache_ttl_matrix: int = 1800     # 30 minutes for correlation matrix
    cache_ttl_adjustments: int = 300  # 5 minutes for position adjustments
    cache_ttl_analytics: int = 600    # 10 minutes for analytics
    
    # Performance targets
    max_calculation_time_ms: float = 100  # Sub-100ms target
    
    # Data requirements
    min_data_points: int = 30        # Minimum periods for reliable correlation
    outlier_threshold: float = 3.0   # Z-score threshold for outlier removal
    significance_level: float = 0.05  # Statistical significance level

@dataclass
class CorrelationResult:
    """Individual correlation calculation result"""
    strategy_pair: Tuple[str, str]
    pearson_correlation: float
    spearman_correlation: float
    dynamic_correlation: float
    correlation_confidence: float
    correlation_stability: float
    rolling_correlations: Dict[str, float]  # short, medium, long term
    correlation_trend: str  # increasing, decreasing, stable
    statistical_significance: bool
    p_value: float
    calculation_timestamp: datetime
    data_points_used: int

@dataclass
class CorrelationMatrix:
    """Complete correlation matrix result"""
    strategies: List[str]
    correlation_matrix: Dict[str, Dict[str, float]]
    correlation_eigenvalues: List[float]
    correlation_condition_number: float
    diversification_ratio: float
    max_correlation: float
    min_correlation: float
    avg_correlation: float
    correlation_clusters: Dict[str, List[str]]
    correlation_regime: str  # low, normal, high, extreme
    position_adjustments: Dict[str, float]
    calculation_timestamp: datetime
    cache_hit: bool = False
    calculation_time_ms: float = 0.0

@dataclass
class CorrelationRiskMetrics:
    """Correlation-based risk metrics"""
    portfolio_concentration_risk: float
    correlation_adjusted_diversification: float
    maximum_correlation_exposure: float
    correlation_regime_stability: float
    strategy_correlation_rankings: Dict[str, int]
    correlation_based_var: float
    correlation_stress_test_results: Dict[str, float]
    recommended_position_limits: Dict[str, float]

class CorrelationMatrixCalculator:
    """
    High-performance correlation matrix calculator with real-time updates.
    
    Features:
    - Multiple correlation models (Pearson, Spearman, Dynamic)
    - Rolling correlation windows with different timeframes
    - Redis caching for sub-second matrix access
    - Position adjustment recommendations based on correlation
    - Comprehensive correlation risk analytics
    - Real-time correlation regime detection
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[CorrelationConfig] = None
    ):
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.config = config or CorrelationConfig()
        
        # Cache keys
        self.CORRELATION_MATRIX_KEY = "correlation:matrix"
        self.CORRELATION_PAIRS_KEY = "correlation:pairs"
        self.POSITION_ADJUSTMENTS_KEY = "correlation:position_adjustments"
        self.RISK_METRICS_KEY = "correlation:risk_metrics"
        self.RETURNS_DATA_KEY = "correlation:returns_data"
        
        # Performance tracking
        self.calculation_times = deque(maxlen=1000)
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Strategy returns buffers for real-time correlation
        self.strategy_returns: Dict[str, deque] = {}
        self.correlation_history: Dict[Tuple[str, str], deque] = {}
        
        # Correlation state tracking
        self.last_matrix_update = datetime.min
        self.current_correlation_regime = "normal"
        
        logger.info("CorrelationMatrixCalculator initialized with Redis caching")
    
    async def calculate_correlation_matrix(
        self,
        strategies: List[str],
        strategy_returns: Optional[Dict[str, List[float]]] = None
    ) -> CorrelationMatrix:
        """
        Calculate comprehensive correlation matrix with caching.
        Target: <100ms execution time.
        """
        start_time = time.perf_counter()
        
        try:
            # Generate cache key
            strategies_key = "_".join(sorted(strategies))
            cache_key = f"{self.CORRELATION_MATRIX_KEY}:{strategies_key}"
            
            # Try cache first
            cached_result = await self._get_cached_correlation_matrix(cache_key)
            if cached_result:
                self.cache_hits += 1
                calculation_time = (time.perf_counter() - start_time) * 1000
                
                cached_result.cache_hit = True
                cached_result.calculation_time_ms = calculation_time
                
                logger.debug(f"Correlation matrix cache hit for {len(strategies)} strategies: {calculation_time:.2f}ms")
                return cached_result
            
            # Cache miss - perform calculation
            self.cache_misses += 1
            
            # Get or update strategy returns data
            returns_data = await self._get_strategy_returns_data(strategies, strategy_returns)
            
            if not self._validate_returns_data(returns_data):
                return await self._create_fallback_correlation_matrix(strategies, start_time)
            
            # Calculate correlation matrix
            correlation_matrix = await self._calculate_correlation_matrix_internal(
                strategies, returns_data
            )
            
            # Calculate additional metrics
            risk_metrics = await self._calculate_correlation_risk_metrics(
                strategies, correlation_matrix, returns_data
            )
            
            # Determine correlation regime and position adjustments
            regime_data = self._determine_correlation_regime(correlation_matrix)
            position_adjustments = await self._calculate_position_adjustments(
                strategies, correlation_matrix, risk_metrics
            )
            
            # Create comprehensive result
            matrix_result = CorrelationMatrix(
                strategies=strategies,
                correlation_matrix=correlation_matrix,
                correlation_eigenvalues=self._calculate_eigenvalues(correlation_matrix),
                correlation_condition_number=self._calculate_condition_number(correlation_matrix),
                diversification_ratio=risk_metrics['diversification_ratio'],
                max_correlation=regime_data['max_correlation'],
                min_correlation=regime_data['min_correlation'],
                avg_correlation=regime_data['avg_correlation'],
                correlation_clusters=self._identify_correlation_clusters(correlation_matrix),
                correlation_regime=regime_data['regime'],
                position_adjustments=position_adjustments,
                calculation_timestamp=datetime.now(),
                cache_hit=False
            )
            
            # Cache the result
            await self._cache_correlation_matrix(cache_key, matrix_result)
            
            # Update performance metrics
            calculation_time = (time.perf_counter() - start_time) * 1000
            matrix_result.calculation_time_ms = calculation_time
            self.calculation_times.append(calculation_time)
            
            # Store analytics
            if self.supabase_service:
                asyncio.create_task(self._store_correlation_analytics(matrix_result))
            
            # Update correlation regime tracking
            self.current_correlation_regime = regime_data['regime']
            self.last_matrix_update = datetime.now()
            
            # Performance validation
            if calculation_time > self.config.max_calculation_time_ms:
                logger.warning(f"Correlation matrix calculation exceeded target: {calculation_time:.2f}ms")
            else:
                logger.debug(f"Correlation matrix calculated for {len(strategies)} strategies: {calculation_time:.2f}ms")
            
            return matrix_result
            
        except Exception as e:
            calculation_time = (time.perf_counter() - start_time) * 1000
            logger.error(f"Correlation matrix calculation failed: {e}")
            return await self._create_fallback_correlation_matrix(strategies, start_time)
    
    async def update_strategy_returns(
        self,
        strategy_name: str,
        return_value: float,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Update strategy returns for real-time correlation calculation"""
        
        try:
            # Initialize buffer if needed
            if strategy_name not in self.strategy_returns:
                self.strategy_returns[strategy_name] = deque(maxlen=self.config.long_window)
            
            # Add return value
            self.strategy_returns[strategy_name].append(return_value)
            
            # Update Redis cache with latest returns
            await self._cache_strategy_returns(strategy_name, list(self.strategy_returns[strategy_name]))
            
            logger.debug(f"Updated returns for {strategy_name}: {return_value:.6f}")
            
        except Exception as e:
            logger.error(f"Failed to update strategy returns for {strategy_name}: {e}")
    
    async def get_correlation_adjustment(
        self,
        strategy_name: str,
        target_position_size: float,
        current_positions: Dict[str, float]
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Get correlation-based position size adjustment.
        Returns: (adjusted_position_size, adjustment_metadata)
        """
        
        try:
            # Get current correlation matrix
            strategies = [strategy_name] + list(current_positions.keys())
            correlation_matrix = await self.calculate_correlation_matrix(strategies)
            
            # Calculate correlation exposure
            correlation_exposure = 0.0
            correlation_details = {}
            
            for other_strategy, position in current_positions.items():
                if other_strategy != strategy_name and position > 0:
                    correlation = correlation_matrix.correlation_matrix.get(strategy_name, {}).get(other_strategy, 0)
                    exposure = abs(correlation) * position
                    correlation_exposure += exposure
                    correlation_details[other_strategy] = {
                        'correlation': correlation,
                        'position': position,
                        'exposure': exposure
                    }
            
            # Calculate adjustment factor
            adjustment_factor = 1.0
            
            if correlation_exposure > self.config.max_correlated_exposure:
                # Reduce position size for high correlation exposure
                excess_exposure = correlation_exposure - self.config.max_correlated_exposure
                penalty = excess_exposure * self.config.correlation_penalty_factor
                adjustment_factor = max(0.1, 1.0 - penalty)
                
            elif correlation_exposure < self.config.low_correlation_threshold:
                # Bonus for low correlation (diversification benefit)
                diversification_benefit = (self.config.low_correlation_threshold - correlation_exposure)
                bonus = diversification_benefit * (self.config.diversification_bonus - 1.0)
                adjustment_factor = min(2.0, 1.0 + bonus)
            
            adjusted_position_size = target_position_size * adjustment_factor
            
            adjustment_metadata = {
                'original_position_size': target_position_size,
                'adjustment_factor': adjustment_factor,
                'correlation_exposure': correlation_exposure,
                'correlation_regime': correlation_matrix.correlation_regime,
                'correlation_details': correlation_details,
                'adjustment_reason': self._get_adjustment_reason(
                    correlation_exposure, adjustment_factor
                )
            }
            
            return adjusted_position_size, adjustment_metadata
            
        except Exception as e:
            logger.error(f"Correlation adjustment calculation failed: {e}")
            return target_position_size, {'error': str(e)}
    
    async def _get_strategy_returns_data(
        self,
        strategies: List[str],
        strategy_returns: Optional[Dict[str, List[float]]]
    ) -> Dict[str, List[float]]:
        """Get strategy returns data from cache or provided data"""
        
        returns_data = {}
        
        # Use provided data if available
        if strategy_returns:
            for strategy in strategies:
                if strategy in strategy_returns:
                    returns_data[strategy] = strategy_returns[strategy]
        
        # Fill missing data from cache or buffers
        for strategy in strategies:
            if strategy not in returns_data:
                # Try cache first
                cached_returns = await self._get_cached_strategy_returns(strategy)
                if cached_returns and len(cached_returns) >= self.config.min_data_points:
                    returns_data[strategy] = cached_returns
                elif strategy in self.strategy_returns:
                    # Use in-memory buffer
                    returns_data[strategy] = list(self.strategy_returns[strategy])
                else:
                    # Generate synthetic returns for testing
                    returns_data[strategy] = await self._generate_synthetic_returns(strategy)
        
        return returns_data
    
    def _validate_returns_data(self, returns_data: Dict[str, List[float]]) -> bool:
        """Validate returns data quality"""
        
        if len(returns_data) < 2:
            logger.warning("Need at least 2 strategies for correlation calculation")
            return False
        
        min_length = min(len(returns) for returns in returns_data.values())
        if min_length < self.config.min_data_points:
            logger.warning(f"Insufficient data points: {min_length} < {self.config.min_data_points}")
            return False
        
        # Check for valid returns (not all zeros or NaN)
        for strategy, returns in returns_data.items():
            if all(r == 0 for r in returns) or any(np.isnan(r) for r in returns):
                logger.warning(f"Invalid returns data for strategy {strategy}")
                return False
        
        return True
    
    async def _calculate_correlation_matrix_internal(
        self,
        strategies: List[str],
        returns_data: Dict[str, List[float]]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate the correlation matrix using multiple methods"""
        
        # Align data lengths
        min_length = min(len(returns_data[strategy]) for strategy in strategies)
        aligned_data = {
            strategy: returns_data[strategy][-min_length:]
            for strategy in strategies
        }
        
        # Calculate correlation matrix
        correlation_matrix = {}
        
        for i, strategy1 in enumerate(strategies):
            correlation_matrix[strategy1] = {}
            
            for j, strategy2 in enumerate(strategies):
                if i == j:
                    correlation_matrix[strategy1][strategy2] = 1.0
                elif strategy2 in correlation_matrix and strategy1 in correlation_matrix[strategy2]:
                    # Use already calculated correlation (symmetric)
                    correlation_matrix[strategy1][strategy2] = correlation_matrix[strategy2][strategy1]
                else:
                    # Calculate correlation
                    returns1 = aligned_data[strategy1]
                    returns2 = aligned_data[strategy2]
                    
                    correlation = await self._calculate_pairwise_correlation(
                        strategy1, strategy2, returns1, returns2
                    )
                    
                    correlation_matrix[strategy1][strategy2] = correlation
        
        return correlation_matrix
    
    async def _calculate_pairwise_correlation(
        self,
        strategy1: str,
        strategy2: str,
        returns1: List[float],
        returns2: List[float]
    ) -> float:
        """Calculate correlation between two strategy return series"""
        
        try:
            # Remove outliers
            clean_returns1, clean_returns2 = self._remove_paired_outliers(returns1, returns2)
            
            if len(clean_returns1) < self.config.min_data_points:
                return 0.0
            
            # Calculate multiple correlation measures
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                # Pearson correlation
                pearson_corr, pearson_p = pearsonr(clean_returns1, clean_returns2)
                
                # Spearman correlation (rank-based, more robust)
                spearman_corr, spearman_p = spearmanr(clean_returns1, clean_returns2)
                
                # Dynamic EWMA correlation
                dynamic_corr = self._calculate_ewma_correlation(clean_returns1, clean_returns2)
            
            # Handle NaN results
            if np.isnan(pearson_corr):
                pearson_corr = 0.0
            if np.isnan(spearman_corr):
                spearman_corr = 0.0
            if np.isnan(dynamic_corr):
                dynamic_corr = 0.0
            
            # Use weighted average with bias toward more stable measures
            if abs(pearson_corr - spearman_corr) < 0.2:  # Similar results
                final_correlation = 0.6 * pearson_corr + 0.4 * spearman_corr
            else:  # Different results, use more robust measure
                final_correlation = 0.3 * pearson_corr + 0.7 * spearman_corr
            
            # Include dynamic component
            final_correlation = 0.8 * final_correlation + 0.2 * dynamic_corr
            
            # Store detailed correlation result
            correlation_result = CorrelationResult(
                strategy_pair=(strategy1, strategy2),
                pearson_correlation=float(pearson_corr),
                spearman_correlation=float(spearman_corr),
                dynamic_correlation=float(dynamic_corr),
                correlation_confidence=self._calculate_correlation_confidence(
                    pearson_corr, spearman_corr, len(clean_returns1)
                ),
                correlation_stability=self._calculate_correlation_stability(
                    strategy1, strategy2, final_correlation
                ),
                rolling_correlations=self._calculate_rolling_correlations(
                    clean_returns1, clean_returns2
                ),
                correlation_trend="stable",  # Would implement trend detection
                statistical_significance=pearson_p < self.config.significance_level if not np.isnan(pearson_p) else False,
                p_value=float(pearson_p) if not np.isnan(pearson_p) else 1.0,
                calculation_timestamp=datetime.now(),
                data_points_used=len(clean_returns1)
            )
            
            # Cache detailed correlation result
            await self._cache_correlation_pair_result(strategy1, strategy2, correlation_result)
            
            return float(final_correlation)
            
        except Exception as e:
            logger.error(f"Correlation calculation failed for {strategy1}-{strategy2}: {e}")
            return 0.0
    
    def _remove_paired_outliers(
        self,
        returns1: List[float],
        returns2: List[float]
    ) -> Tuple[List[float], List[float]]:
        """Remove outliers from paired return series"""
        
        try:
            returns1_array = np.array(returns1)
            returns2_array = np.array(returns2)
            
            # Calculate z-scores for both series
            z_scores1 = np.abs(stats.zscore(returns1_array))
            z_scores2 = np.abs(stats.zscore(returns2_array))
            
            # Keep points where neither series has extreme outliers
            mask = (z_scores1 < self.config.outlier_threshold) & (z_scores2 < self.config.outlier_threshold)
            
            clean_returns1 = returns1_array[mask].tolist()
            clean_returns2 = returns2_array[mask].tolist()
            
            return clean_returns1, clean_returns2
            
        except Exception as e:
            logger.warning(f"Outlier removal failed: {e}")
            return returns1, returns2
    
    def _calculate_ewma_correlation(
        self,
        returns1: List[float],
        returns2: List[float]
    ) -> float:
        """Calculate exponentially weighted moving average correlation"""
        
        try:
            if len(returns1) < 2:
                return 0.0
            
            # Calculate EWMA covariance and variances
            alpha = self.config.ewma_alpha
            
            # Initialize with first values
            ewma_cov = returns1[0] * returns2[0]
            ewma_var1 = returns1[0] ** 2
            ewma_var2 = returns2[0] ** 2
            
            # Update with EWMA
            for i in range(1, len(returns1)):
                ewma_cov = alpha * returns1[i] * returns2[i] + (1 - alpha) * ewma_cov
                ewma_var1 = alpha * returns1[i] ** 2 + (1 - alpha) * ewma_var1
                ewma_var2 = alpha * returns2[i] ** 2 + (1 - alpha) * ewma_var2
            
            # Calculate correlation
            ewma_std1 = np.sqrt(ewma_var1)
            ewma_std2 = np.sqrt(ewma_var2)
            
            if ewma_std1 > 0 and ewma_std2 > 0:
                ewma_correlation = ewma_cov / (ewma_std1 * ewma_std2)
                return float(ewma_correlation)
            
            return 0.0
            
        except Exception as e:
            logger.warning(f"EWMA correlation calculation failed: {e}")
            return 0.0
    
    def _calculate_correlation_confidence(
        self,
        pearson_corr: float,
        spearman_corr: float,
        data_points: int
    ) -> float:
        """Calculate confidence in correlation estimate"""
        
        # Base confidence on data points
        data_confidence = min(1.0, data_points / (self.config.long_window * 0.5))
        
        # Confidence based on agreement between methods
        method_agreement = 1.0 - min(1.0, abs(pearson_corr - spearman_corr))
        
        # Overall confidence
        confidence = (data_confidence * 0.6 + method_agreement * 0.4)
        
        return float(max(0.1, min(1.0, confidence)))
    
    def _calculate_correlation_stability(
        self,
        strategy1: str,
        strategy2: str,
        current_correlation: float
    ) -> float:
        """Calculate correlation stability over time"""
        
        pair_key = (min(strategy1, strategy2), max(strategy1, strategy2))
        
        if pair_key not in self.correlation_history:
            self.correlation_history[pair_key] = deque(maxlen=100)
        
        self.correlation_history[pair_key].append(current_correlation)
        
        if len(self.correlation_history[pair_key]) < 10:
            return 0.5  # Default stability for insufficient history
        
        # Calculate stability as inverse of volatility
        correlations = list(self.correlation_history[pair_key])
        correlation_volatility = np.std(correlations)
        stability = max(0.0, 1.0 - correlation_volatility * 5)  # Scale volatility
        
        return float(min(1.0, stability))
    
    def _calculate_rolling_correlations(
        self,
        returns1: List[float],
        returns2: List[float]
    ) -> Dict[str, float]:
        """Calculate rolling correlations for different windows"""
        
        rolling_correlations = {}
        
        windows = {
            'short': self.config.short_window,
            'medium': self.config.medium_window,
            'long': self.config.long_window
        }
        
        for name, window in windows.items():
            if len(returns1) >= window:
                recent_returns1 = returns1[-window:]
                recent_returns2 = returns2[-window:]
                
                try:
                    corr, _ = pearsonr(recent_returns1, recent_returns2)
                    rolling_correlations[name] = float(corr) if not np.isnan(corr) else 0.0
                except:
                    rolling_correlations[name] = 0.0
            else:
                rolling_correlations[name] = 0.0
        
        return rolling_correlations
    
    async def _calculate_correlation_risk_metrics(
        self,
        strategies: List[str],
        correlation_matrix: Dict[str, Dict[str, float]],
        returns_data: Dict[str, List[float]]
    ) -> Dict[str, Any]:
        """Calculate comprehensive correlation-based risk metrics"""
        
        try:
            # Convert to numpy matrix for calculations
            matrix_array = np.array([
                [correlation_matrix[s1][s2] for s2 in strategies]
                for s1 in strategies
            ])
            
            # Calculate diversification ratio
            # DR = sqrt(w'Σw) / (w'σ) where w is equal weights, Σ is correlation matrix, σ is volatilities
            equal_weights = np.ones(len(strategies)) / len(strategies)
            
            # Calculate individual volatilities
            volatilities = np.array([
                np.std(returns_data[strategy]) for strategy in strategies
            ])
            
            # Portfolio volatility with correlations
            portfolio_variance = np.dot(equal_weights, np.dot(matrix_array * np.outer(volatilities, volatilities), equal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Weighted average individual volatility
            avg_individual_volatility = np.dot(equal_weights, volatilities)
            
            diversification_ratio = avg_individual_volatility / portfolio_volatility if portfolio_volatility > 0 else 1.0
            
            # Concentration risk (based on eigenvalues)
            eigenvalues = np.linalg.eigvals(matrix_array)
            max_eigenvalue = np.max(eigenvalues)
            concentration_risk = max_eigenvalue / len(strategies)  # Normalized
            
            return {
                'diversification_ratio': float(diversification_ratio),
                'concentration_risk': float(concentration_risk),
                'portfolio_volatility': float(portfolio_volatility),
                'avg_individual_volatility': float(avg_individual_volatility),
                'eigenvalues': eigenvalues.tolist(),
                'max_eigenvalue': float(max_eigenvalue)
            }
            
        except Exception as e:
            logger.error(f"Correlation risk metrics calculation failed: {e}")
            return {
                'diversification_ratio': 1.0,
                'concentration_risk': 0.5,
                'portfolio_volatility': 0.1,
                'avg_individual_volatility': 0.1,
                'eigenvalues': [],
                'max_eigenvalue': 1.0
            }
    
    def _determine_correlation_regime(
        self,
        correlation_matrix: Dict[str, Dict[str, float]]
    ) -> Dict[str, Any]:
        """Determine current correlation regime and statistics"""
        
        # Extract upper triangular correlations (excluding diagonal)
        correlations = []
        for i, strategy1 in enumerate(correlation_matrix.keys()):
            for j, strategy2 in enumerate(correlation_matrix.keys()):
                if i < j:  # Upper triangular
                    correlations.append(abs(correlation_matrix[strategy1][strategy2]))
        
        if not correlations:
            return {
                'regime': 'normal',
                'max_correlation': 0.0,
                'min_correlation': 0.0,
                'avg_correlation': 0.0
            }
        
        max_corr = max(correlations)
        min_corr = min(correlations)
        avg_corr = np.mean(correlations)
        
        # Determine regime
        if avg_corr > self.config.extreme_correlation_threshold:
            regime = "extreme"
        elif avg_corr > self.config.high_correlation_threshold:
            regime = "high"
        elif avg_corr < self.config.low_correlation_threshold:
            regime = "low"
        else:
            regime = "normal"
        
        return {
            'regime': regime,
            'max_correlation': float(max_corr),
            'min_correlation': float(min_corr),
            'avg_correlation': float(avg_corr)
        }
    
    def _calculate_eigenvalues(self, correlation_matrix: Dict[str, Dict[str, float]]) -> List[float]:
        """Calculate eigenvalues of correlation matrix"""
        try:
            strategies = list(correlation_matrix.keys())
            matrix_array = np.array([
                [correlation_matrix[s1][s2] for s2 in strategies]
                for s1 in strategies
            ])
            eigenvalues = np.linalg.eigvals(matrix_array)
            return sorted(eigenvalues.real.tolist(), reverse=True)
        except:
            return []
    
    def _calculate_condition_number(self, correlation_matrix: Dict[str, Dict[str, float]]) -> float:
        """Calculate condition number of correlation matrix"""
        try:
            eigenvalues = self._calculate_eigenvalues(correlation_matrix)
            if eigenvalues and eigenvalues[-1] > 1e-10:
                return float(eigenvalues[0] / eigenvalues[-1])
            return 1.0
        except:
            return 1.0
    
    def _identify_correlation_clusters(
        self,
        correlation_matrix: Dict[str, Dict[str, float]]
    ) -> Dict[str, List[str]]:
        """Identify clusters of highly correlated strategies"""
        
        clusters = {}
        strategies = list(correlation_matrix.keys())
        processed = set()
        
        for strategy in strategies:
            if strategy in processed:
                continue
            
            # Find highly correlated strategies
            cluster = [strategy]
            for other_strategy in strategies:
                if (other_strategy != strategy and 
                    other_strategy not in processed and
                    abs(correlation_matrix[strategy][other_strategy]) > self.config.high_correlation_threshold):
                    cluster.append(other_strategy)
            
            if len(cluster) > 1:
                cluster_name = f"cluster_{len(clusters) + 1}"
                clusters[cluster_name] = cluster
                processed.update(cluster)
            else:
                processed.add(strategy)
        
        return clusters
    
    async def _calculate_position_adjustments(
        self,
        strategies: List[str],
        correlation_matrix: Dict[str, Dict[str, float]],
        risk_metrics: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate position adjustments based on correlation"""
        
        adjustments = {}
        
        for strategy in strategies:
            # Calculate correlation exposure to other strategies
            avg_correlation = np.mean([
                abs(correlation_matrix[strategy][other])
                for other in strategies if other != strategy
            ])
            
            # Base adjustment on correlation level
            if avg_correlation > self.config.high_correlation_threshold:
                # Reduce position for highly correlated strategy
                adjustment = self.config.correlation_penalty_factor
            elif avg_correlation < self.config.low_correlation_threshold:
                # Increase position for diversifying strategy
                adjustment = self.config.diversification_bonus
            else:
                # No adjustment for normal correlation
                adjustment = 1.0
            
            # Apply concentration risk adjustment
            concentration_risk = risk_metrics.get('concentration_risk', 0.5)
            if concentration_risk > 0.7:  # High concentration
                adjustment *= 0.8  # Additional reduction
            
            adjustments[strategy] = float(max(0.1, min(2.0, adjustment)))
        
        return adjustments
    
    def _get_adjustment_reason(
        self,
        correlation_exposure: float,
        adjustment_factor: float
    ) -> str:
        """Get human-readable reason for position adjustment"""
        
        if adjustment_factor < 0.8:
            return f"Position reduced due to high correlation exposure ({correlation_exposure:.2f})"
        elif adjustment_factor > 1.2:
            return f"Position increased due to diversification benefit (low correlation: {correlation_exposure:.2f})"
        else:
            return f"No significant adjustment needed (correlation exposure: {correlation_exposure:.2f})"
    
    # Cache and storage methods
    
    async def _get_cached_correlation_matrix(self, cache_key: str) -> Optional[CorrelationMatrix]:
        """Get cached correlation matrix"""
        try:
            cached_data = await self.redis_service.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                return CorrelationMatrix(**data)
            return None
        except Exception as e:
            logger.warning(f"Failed to get cached correlation matrix: {e}")
            return None
    
    async def _cache_correlation_matrix(
        self,
        cache_key: str,
        matrix_result: CorrelationMatrix
    ) -> None:
        """Cache correlation matrix result"""
        try:
            await self.redis_service.setex(
                cache_key,
                self.config.cache_ttl_matrix,
                json.dumps(asdict(matrix_result), default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache correlation matrix: {e}")
    
    async def _cache_strategy_returns(
        self,
        strategy_name: str,
        returns: List[float]
    ) -> None:
        """Cache strategy returns data"""
        try:
            cache_key = f"{self.RETURNS_DATA_KEY}:{strategy_name}"
            await self.redis_service.setex(
                cache_key,
                self.config.cache_ttl_analytics,
                json.dumps(returns)
            )
        except Exception as e:
            logger.warning(f"Failed to cache strategy returns: {e}")
    
    async def _get_cached_strategy_returns(self, strategy_name: str) -> Optional[List[float]]:
        """Get cached strategy returns"""
        try:
            cache_key = f"{self.RETURNS_DATA_KEY}:{strategy_name}"
            cached_data = await self.redis_service.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.warning(f"Failed to get cached strategy returns: {e}")
            return None
    
    async def _cache_correlation_pair_result(
        self,
        strategy1: str,
        strategy2: str,
        result: CorrelationResult
    ) -> None:
        """Cache detailed correlation pair result"""
        try:
            pair_key = f"{min(strategy1, strategy2)}_{max(strategy1, strategy2)}"
            cache_key = f"{self.CORRELATION_PAIRS_KEY}:{pair_key}"
            await self.redis_service.setex(
                cache_key,
                self.config.cache_ttl_adjustments,
                json.dumps(asdict(result), default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache correlation pair result: {e}")
    
    async def _generate_synthetic_returns(self, strategy_name: str) -> List[float]:
        """Generate synthetic returns for testing"""
        
        # Generate realistic return series with strategy-specific characteristics
        np.random.seed(hash(strategy_name) % 2**31)
        
        base_return = 0.0001  # 0.01% base return
        volatility = 0.02     # 2% volatility
        
        # Strategy-specific adjustments
        if "Grid" in strategy_name:
            volatility *= 0.8  # Grid strategies typically lower volatility
        elif "Trend" in strategy_name:
            volatility *= 1.2  # Trend strategies higher volatility
        
        returns = np.random.normal(base_return, volatility, self.config.long_window)
        
        return returns.tolist()
    
    async def _create_fallback_correlation_matrix(
        self,
        strategies: List[str],
        start_time: float
    ) -> CorrelationMatrix:
        """Create fallback correlation matrix when calculation fails"""
        
        calculation_time = (time.perf_counter() - start_time) * 1000
        
        # Create identity matrix (no correlation)
        correlation_matrix = {}
        for strategy1 in strategies:
            correlation_matrix[strategy1] = {}
            for strategy2 in strategies:
                correlation_matrix[strategy1][strategy2] = 1.0 if strategy1 == strategy2 else 0.0
        
        return CorrelationMatrix(
            strategies=strategies,
            correlation_matrix=correlation_matrix,
            correlation_eigenvalues=[1.0] * len(strategies),
            correlation_condition_number=1.0,
            diversification_ratio=1.0,
            max_correlation=0.0,
            min_correlation=0.0,
            avg_correlation=0.0,
            correlation_clusters={},
            correlation_regime="normal",
            position_adjustments={strategy: 1.0 for strategy in strategies},
            calculation_timestamp=datetime.now(),
            cache_hit=False,
            calculation_time_ms=calculation_time
        )
    
    async def _store_correlation_analytics(self, matrix_result: CorrelationMatrix) -> None:
        """Store correlation analytics in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            analytics_data = {
                'timestamp': matrix_result.calculation_timestamp.isoformat(),
                'strategies_count': len(matrix_result.strategies),
                'correlation_regime': matrix_result.correlation_regime,
                'max_correlation': matrix_result.max_correlation,
                'min_correlation': matrix_result.min_correlation,
                'avg_correlation': matrix_result.avg_correlation,
                'diversification_ratio': matrix_result.diversification_ratio,
                'condition_number': matrix_result.correlation_condition_number,
                'calculation_time_ms': matrix_result.calculation_time_ms,
                'cache_hit': matrix_result.cache_hit,
                'clusters_count': len(matrix_result.correlation_clusters)
            }
            
            # Store in correlation_analytics table (would need to be created)
            # await self.supabase_service.store_correlation_analytics(analytics_data)
            
        except Exception as e:
            logger.error(f"Failed to store correlation analytics: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring"""
        
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        avg_time = np.mean(self.calculation_times) if self.calculation_times else 0
        p95_time = np.percentile(self.calculation_times, 95) if self.calculation_times else 0
        
        return {
            'total_calculations': total_requests,
            'cache_hit_rate': round(hit_rate, 2),
            'avg_calculation_time_ms': round(avg_time, 2),
            'p95_calculation_time_ms': round(p95_time, 2),
            'target_met_percentage': round(
                sum(1 for t in self.calculation_times if t < self.config.max_calculation_time_ms) / 
                max(1, len(self.calculation_times)) * 100, 2
            ),
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'current_correlation_regime': self.current_correlation_regime,
            'tracked_strategies': len(self.strategy_returns),
            'tracked_pairs': len(self.correlation_history)
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on correlation calculator"""
        try:
            # Test Redis connectivity
            redis_stats = await self.redis_service.get_cache_stats()
            redis_healthy = bool(redis_stats)
            
            # Test calculation with dummy data
            strategies = ["Strategy1", "Strategy2", "Strategy3"]
            
            start_time = time.perf_counter()
            result = await self.calculate_correlation_matrix(strategies)
            test_time = (time.perf_counter() - start_time) * 1000
            
            performance_stats = self.get_performance_stats()
            
            return {
                'status': 'healthy' if redis_healthy and test_time < 200 else 'degraded',
                'redis_healthy': redis_healthy,
                'test_calculation_time_ms': round(test_time, 2),
                'test_result_valid': len(result.correlation_matrix) > 0,
                'performance_stats': performance_stats,
                'config': asdict(self.config)
            }
            
        except Exception as e:
            logger.error(f"Correlation calculator health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }

# Factory function for easy initialization
async def create_correlation_calculator(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[CorrelationConfig] = None
) -> CorrelationMatrixCalculator:
    """Factory function to create correlation calculator with services"""
    
    from app.services.mcp.redis_service import RedisService
    
    redis_service = RedisService(redis_url)
    await redis_service.connect()
    
    supabase_service = None
    if supabase_url and supabase_key:
        from app.services.mcp.supabase_service import SupabaseService
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return CorrelationMatrixCalculator(
        redis_service=redis_service,
        supabase_service=supabase_service,
        config=config
    )

# Example usage and testing
if __name__ == "__main__":
    async def test_correlation_calculator():
        """Test the correlation calculator functionality"""
        
        # Initialize calculator
        calc = await create_correlation_calculator("redis://localhost:6379")
        
        # Test with sample strategies
        strategies = ["GridStrategy", "TechnicalAnalysisStrategy", "TrendFollowingStrategy"]
        
        # Update some returns
        for i in range(100):
            await calc.update_strategy_returns("GridStrategy", np.random.normal(0.001, 0.015))
            await calc.update_strategy_returns("TechnicalAnalysisStrategy", np.random.normal(0.0008, 0.012))
            await calc.update_strategy_returns("TrendFollowingStrategy", np.random.normal(0.0012, 0.018))
        
        # Calculate correlation matrix
        result = await calc.calculate_correlation_matrix(strategies)
        
        print(f"Correlation Matrix Result:")
        print(f"  Strategies: {result.strategies}")
        print(f"  Correlation Regime: {result.correlation_regime}")
        print(f"  Average Correlation: {result.avg_correlation:.3f}")
        print(f"  Max Correlation: {result.max_correlation:.3f}")
        print(f"  Diversification Ratio: {result.diversification_ratio:.2f}")
        print(f"  Calculation Time: {result.calculation_time_ms:.2f}ms")
        print(f"  Cache Hit: {result.cache_hit}")
        print(f"  Clusters: {result.correlation_clusters}")
        
        # Test position adjustment
        current_positions = {"TechnicalAnalysisStrategy": 0.3, "TrendFollowingStrategy": 0.2}
        adjusted_size, metadata = await calc.get_correlation_adjustment(
            "GridStrategy", 0.25, current_positions
        )
        
        print(f"\nPosition Adjustment:")
        print(f"  Original Size: {metadata['original_position_size']:.3f}")
        print(f"  Adjusted Size: {adjusted_size:.3f}")
        print(f"  Adjustment Factor: {metadata['adjustment_factor']:.2f}")
        print(f"  Reason: {metadata['adjustment_reason']}")
        
        # Get performance stats
        stats = calc.get_performance_stats()
        print(f"\nPerformance Stats: {stats}")
        
        # Health check
        health = await calc.health_check()
        print(f"\nHealth Check: {health}")
    
    # Run test
    asyncio.run(test_correlation_calculator())