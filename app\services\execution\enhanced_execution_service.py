#!/usr/bin/env python3
"""
Enhanced Execution Service for Real-Time Position Tracking
Implements Task 1.2.4 requirements for ensemble execution.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from collections import defaultdict

from app.models.trade_state import TradeState
from app.models.market_data import MarketData
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

@dataclass
class PositionState:
    """Real-time position state tracking"""
    strategy_name: str
    symbol: str
    position_type: str  # 'long', 'short', 'neutral'
    quantity: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime
    last_updated: datetime
    execution_id: str

@dataclass
class ExecutionMetrics:
    """Execution performance metrics"""
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_execution_time_ms: float
    total_volume_executed: float
    total_fees_paid: float
    slippage_metrics: Dict[str, float]
    latency_p95_ms: float
    timestamp: datetime

@dataclass
class MultiStrategyPosition:
    """Multi-strategy position aggregation"""
    symbol: str
    total_quantity: float
    weighted_avg_price: float
    strategy_contributions: Dict[str, float]
    net_exposure: float
    total_unrealized_pnl: float
    correlation_risk: float
    last_updated: datetime

class EnhancedExecutionService:
    """
    Enhanced execution service with real-time position tracking.
    
    Features:
    - Real-time position tracking per strategy
    - Multi-strategy position aggregation
    - Redis caching for sub-second position updates
    - Supabase integration for historical tracking
    - Execution performance monitoring
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.config = config or self._default_config()
        
        # Cache keys
        self.POSITIONS_KEY = "execution:positions"
        self.MULTI_STRATEGY_KEY = "execution:multi_strategy"
        self.METRICS_KEY = "execution:metrics"
        self.TRADE_HISTORY_KEY = "execution:trade_history"
        
        # State tracking
        self.active_positions: Dict[str, PositionState] = {}
        self.execution_metrics = ExecutionMetrics(
            total_executions=0,
            successful_executions=0,
            failed_executions=0,
            avg_execution_time_ms=0,
            total_volume_executed=0,
            total_fees_paid=0,
            slippage_metrics={},
            latency_p95_ms=0,
            timestamp=datetime.now()
        )
        
        # Execution tracking
        self.execution_times = []
        self.trade_history = []
        
        logger.info("Enhanced Execution Service initialized")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for execution service"""
        return {
            "cache_ttl_positions": 10,      # 10 seconds
            "cache_ttl_metrics": 60,        # 1 minute
            "cache_ttl_history": 300,       # 5 minutes
            "max_slippage_pct": 0.001,      # 0.1% max slippage
            "max_execution_time_ms": 200,   # 200ms target
            "position_update_frequency": 5,  # 5 seconds
            "enable_real_time_tracking": True,
            "enable_multi_strategy_aggregation": True,
            "enable_correlation_tracking": True,
            "max_position_size": 0.1,       # 10% of portfolio
            "risk_check_enabled": True
        }
    
    async def execute_trade(
        self,
        symbol: str,
        action: str,
        quantity: float,
        price: float,
        strategy_name: str = "default",
        metadata: Optional[Dict] = None
    ) -> Optional[TradeState]:
        """
        Execute trade with real-time position tracking.
        """
        start_time = datetime.now()
        execution_id = f"{strategy_name}_{symbol}_{int(start_time.timestamp())}"
        
        try:
            # Pre-execution risk checks
            if self.config["risk_check_enabled"]:
                risk_check_passed = await self._perform_risk_checks(
                    symbol, action, quantity, price, strategy_name
                )
                if not risk_check_passed:
                    logger.warning(f"Risk check failed for {action} {quantity} {symbol}")
                    await self._update_execution_metrics(start_time, False)
                    return None
            
            # Simulate trade execution (in production, would call exchange API)
            executed_trade = await self._simulate_trade_execution(
                symbol, action, quantity, price, execution_id, metadata
            )
            
            if executed_trade:
                # Update position state
                await self._update_position_state(executed_trade, strategy_name)
                
                # Update multi-strategy aggregation
                if self.config["enable_multi_strategy_aggregation"]:
                    await self._update_multi_strategy_positions(executed_trade, strategy_name)
                
                # Store in Supabase for historical tracking
                if self.supabase_service:
                    await self._store_trade_execution(executed_trade, strategy_name, metadata)
                
                # Update execution metrics
                await self._update_execution_metrics(start_time, True)
                
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                logger.info(f"Trade executed: {action} {quantity} {symbol} @ {price} in {execution_time:.1f}ms")
                
                return executed_trade
            else:
                await self._update_execution_metrics(start_time, False)
                return None
                
        except Exception as e:
            logger.error(f"Trade execution failed: {e}")
            await self._update_execution_metrics(start_time, False)
            return None
    
    async def get_position_state(
        self,
        symbol: str,
        strategy_name: str
    ) -> Optional[PositionState]:
        """Get current position state for strategy/symbol"""
        try:
            position_key = f"{self.POSITIONS_KEY}:{strategy_name}:{symbol}"
            cached_position = await self.redis_service.get(position_key)
            
            if cached_position:
                position_data = json.loads(cached_position)
                return PositionState(**position_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Position state retrieval failed: {e}")
            return None
    
    async def get_multi_strategy_position(self, symbol: str) -> Optional[MultiStrategyPosition]:
        """Get aggregated multi-strategy position for symbol"""
        try:
            multi_key = f"{self.MULTI_STRATEGY_KEY}:{symbol}"
            cached_position = await self.redis_service.get(multi_key)
            
            if cached_position:
                position_data = json.loads(cached_position)
                # Convert datetime strings back to datetime objects
                position_data['last_updated'] = datetime.fromisoformat(position_data['last_updated'])
                return MultiStrategyPosition(**position_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Multi-strategy position retrieval failed: {e}")
            return None
    
    async def get_all_positions(self) -> Dict[str, List[PositionState]]:
        """Get all active positions grouped by strategy"""
        try:
            all_positions = defaultdict(list)
            
            # Get all position keys
            position_pattern = f"{self.POSITIONS_KEY}:*"
            # In production, would use Redis SCAN to get keys matching pattern
            # For now, return cached positions
            
            for position_id, position in self.active_positions.items():
                all_positions[position.strategy_name].append(position)
            
            return dict(all_positions)
            
        except Exception as e:
            logger.error(f"All positions retrieval failed: {e}")
            return {}
    
    async def get_execution_metrics(self) -> ExecutionMetrics:
        """Get current execution performance metrics"""
        try:
            cached_metrics = await self.redis_service.get(self.METRICS_KEY)
            
            if cached_metrics:
                metrics_data = json.loads(cached_metrics)
                # Convert datetime string back
                metrics_data['timestamp'] = datetime.fromisoformat(metrics_data['timestamp'])
                return ExecutionMetrics(**metrics_data)
            
            return self.execution_metrics
            
        except Exception as e:
            logger.error(f"Execution metrics retrieval failed: {e}")
            return self.execution_metrics
    
    async def _simulate_trade_execution(
        self,
        symbol: str,
        action: str,
        quantity: float,
        price: float,
        execution_id: str,
        metadata: Optional[Dict]
    ) -> Optional[TradeState]:
        """Simulate trade execution (replace with real exchange API in production)"""
        try:
            # Simulate execution latency
            await asyncio.sleep(0.01)  # 10ms simulated latency
            
            # Simulate small slippage
            slippage_factor = 1 + (0.0005 if action == 'BUY' else -0.0005)  # 0.05% slippage
            executed_price = price * slippage_factor
            
            # Create trade state
            trade = TradeState(
                action=action,
                quantity=quantity,
                price=executed_price,
                symbol=symbol,
                timestamp=datetime.now(),
                pnl=0.0  # Will be updated when position is closed
            )
            
            # Add execution metadata
            trade.execution_id = execution_id
            trade.slippage = abs(executed_price - price) / price
            trade.metadata = metadata or {}
            
            return trade
            
        except Exception as e:
            logger.error(f"Trade execution simulation failed: {e}")
            return None
    
    async def _update_position_state(
        self,
        trade: TradeState,
        strategy_name: str
    ) -> None:
        """Update position state after trade execution"""
        try:
            position_key = f"{strategy_name}:{trade.symbol}"
            current_position = self.active_positions.get(position_key)
            
            if current_position is None:
                # New position
                position = PositionState(
                    strategy_name=strategy_name,
                    symbol=trade.symbol,
                    position_type='long' if trade.action == 'BUY' else 'short',
                    quantity=trade.quantity if trade.action == 'BUY' else -trade.quantity,
                    entry_price=trade.price,
                    current_price=trade.price,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    timestamp=trade.timestamp,
                    last_updated=datetime.now(),
                    execution_id=trade.execution_id
                )
            else:
                # Update existing position
                if trade.action == 'BUY':
                    new_quantity = current_position.quantity + trade.quantity
                else:  # SELL
                    new_quantity = current_position.quantity - trade.quantity
                
                if new_quantity == 0:
                    # Position closed
                    realized_pnl = (trade.price - current_position.entry_price) * trade.quantity
                    current_position.realized_pnl += realized_pnl
                    # Remove position from active positions
                    if position_key in self.active_positions:
                        del self.active_positions[position_key]
                    return
                else:
                    # Update position
                    # Calculate new weighted average price
                    if (current_position.quantity > 0 and trade.quantity > 0) or \
                       (current_position.quantity < 0 and trade.quantity < 0):
                        # Adding to position
                        total_cost = (current_position.quantity * current_position.entry_price + 
                                    trade.quantity * trade.price)
                        current_position.entry_price = total_cost / (current_position.quantity + trade.quantity)
                    
                    current_position.quantity = new_quantity
                    current_position.current_price = trade.price
                    current_position.last_updated = datetime.now()
                
                position = current_position
            
            # Store updated position
            self.active_positions[position_key] = position
            
            # Cache position in Redis
            cache_key = f"{self.POSITIONS_KEY}:{strategy_name}:{trade.symbol}"
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_positions"],
                json.dumps(asdict(position), default=str)
            )
            
        except Exception as e:
            logger.error(f"Position state update failed: {e}")
    
    async def _update_multi_strategy_positions(
        self,
        trade: TradeState,
        strategy_name: str
    ) -> None:
        """Update multi-strategy position aggregation"""
        try:
            # Get current multi-strategy position
            current_multi = await self.get_multi_strategy_position(trade.symbol)
            
            if current_multi is None:
                # Create new multi-strategy position
                multi_position = MultiStrategyPosition(
                    symbol=trade.symbol,
                    total_quantity=trade.quantity if trade.action == 'BUY' else -trade.quantity,
                    weighted_avg_price=trade.price,
                    strategy_contributions={strategy_name: trade.quantity},
                    net_exposure=abs(trade.quantity) * trade.price,
                    total_unrealized_pnl=0.0,
                    correlation_risk=0.0,
                    last_updated=datetime.now()
                )
            else:
                # Update existing multi-strategy position
                trade_quantity = trade.quantity if trade.action == 'BUY' else -trade.quantity
                
                # Update total quantity
                new_total_quantity = current_multi.total_quantity + trade_quantity
                
                # Update weighted average price
                if new_total_quantity != 0:
                    total_value = (current_multi.total_quantity * current_multi.weighted_avg_price + 
                                 trade_quantity * trade.price)
                    current_multi.weighted_avg_price = total_value / new_total_quantity
                
                current_multi.total_quantity = new_total_quantity
                
                # Update strategy contributions
                current_contribution = current_multi.strategy_contributions.get(strategy_name, 0)
                current_multi.strategy_contributions[strategy_name] = current_contribution + trade_quantity
                
                # Update net exposure
                current_multi.net_exposure = abs(new_total_quantity) * trade.price
                current_multi.last_updated = datetime.now()
                
                multi_position = current_multi
            
            # Cache updated multi-strategy position
            cache_key = f"{self.MULTI_STRATEGY_KEY}:{trade.symbol}"
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_positions"],
                json.dumps(asdict(multi_position), default=str)
            )
            
        except Exception as e:
            logger.error(f"Multi-strategy position update failed: {e}")
    
    async def _perform_risk_checks(
        self,
        symbol: str,
        action: str,
        quantity: float,
        price: float,
        strategy_name: str
    ) -> bool:
        """Perform pre-execution risk checks"""
        try:
            # Check position size limits
            position_value = quantity * price
            if position_value > self.config["max_position_size"] * 100000:  # Assuming 100k portfolio
                logger.warning(f"Position size too large: ${position_value:.2f}")
                return False
            
            # Check for excessive concentration in single symbol
            multi_position = await self.get_multi_strategy_position(symbol)
            if multi_position:
                new_exposure = multi_position.net_exposure + position_value
                if new_exposure > self.config["max_position_size"] * 200000:  # 2x limit for multi-strategy
                    logger.warning(f"Multi-strategy exposure too large: ${new_exposure:.2f}")
                    return False
            
            # Additional risk checks would go here
            # - Portfolio heat checks
            # - Correlation limits
            # - Volatility limits
            # - Drawdown limits
            
            return True
            
        except Exception as e:
            logger.error(f"Risk check failed: {e}")
            return False
    
    async def _store_trade_execution(
        self,
        trade: TradeState,
        strategy_name: str,
        metadata: Optional[Dict]
    ) -> None:
        """Store trade execution in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            trade_data = {
                'strategy_name': strategy_name,
                'symbol': trade.symbol,
                'action': trade.action,
                'quantity': trade.quantity,
                'price': trade.price,
                'timestamp': trade.timestamp.isoformat(),
                'execution_id': getattr(trade, 'execution_id', ''),
                'slippage': getattr(trade, 'slippage', 0),
                'metadata': metadata or {}
            }
            
            await self.supabase_service.store_trade_execution(trade_data)
            
        except Exception as e:
            logger.error(f"Trade storage failed: {e}")
    
    async def _update_execution_metrics(
        self,
        start_time: datetime,
        success: bool
    ) -> None:
        """Update execution performance metrics"""
        try:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            self.execution_times.append(execution_time)
            
            # Keep only recent execution times for percentile calculation
            if len(self.execution_times) > 1000:
                self.execution_times = self.execution_times[-1000:]
            
            self.execution_metrics.total_executions += 1
            
            if success:
                self.execution_metrics.successful_executions += 1
            else:
                self.execution_metrics.failed_executions += 1
            
            # Update average execution time
            if self.execution_metrics.avg_execution_time_ms == 0:
                self.execution_metrics.avg_execution_time_ms = execution_time
            else:
                # Exponential moving average
                alpha = 0.1
                self.execution_metrics.avg_execution_time_ms = (
                    alpha * execution_time + 
                    (1 - alpha) * self.execution_metrics.avg_execution_time_ms
                )
            
            # Update P95 latency
            if len(self.execution_times) >= 20:
                import numpy as np
                self.execution_metrics.latency_p95_ms = np.percentile(self.execution_times, 95)
            
            self.execution_metrics.timestamp = datetime.now()
            
            # Cache updated metrics
            await self.redis_service.setex(
                self.METRICS_KEY,
                self.config["cache_ttl_metrics"],
                json.dumps(asdict(self.execution_metrics), default=str)
            )
            
        except Exception as e:
            logger.error(f"Metrics update failed: {e}")
    
    async def update_position_prices(self, market_data: MarketData) -> None:
        """Update current prices for all positions to calculate unrealized PnL"""
        try:
            symbol = market_data.symbol
            current_price = market_data.price
            
            # Update individual strategy positions
            for position_key, position in self.active_positions.items():
                if position.symbol == symbol:
                    position.current_price = current_price
                    
                    # Calculate unrealized PnL
                    if position.quantity > 0:  # Long position
                        position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                    else:  # Short position
                        position.unrealized_pnl = (position.entry_price - current_price) * abs(position.quantity)
                    
                    position.last_updated = datetime.now()
                    
                    # Update cache
                    strategy_name, symbol_name = position_key.split(':', 1)
                    cache_key = f"{self.POSITIONS_KEY}:{strategy_name}:{symbol_name}"
                    await self.redis_service.setex(
                        cache_key,
                        self.config["cache_ttl_positions"],
                        json.dumps(asdict(position), default=str)
                    )
            
            # Update multi-strategy position
            multi_position = await self.get_multi_strategy_position(symbol)
            if multi_position:
                # Calculate total unrealized PnL across all strategies
                total_unrealized = 0
                for position in self.active_positions.values():
                    if position.symbol == symbol:
                        total_unrealized += position.unrealized_pnl
                
                multi_position.total_unrealized_pnl = total_unrealized
                multi_position.last_updated = datetime.now()
                
                # Update cache
                cache_key = f"{self.MULTI_STRATEGY_KEY}:{symbol}"
                await self.redis_service.setex(
                    cache_key,
                    self.config["cache_ttl_positions"],
                    json.dumps(asdict(multi_position), default=str)
                )
            
        except Exception as e:
            logger.error(f"Position price update failed: {e}")

# Utility functions for integration

async def create_enhanced_execution_service(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[Dict] = None
) -> EnhancedExecutionService:
    """Factory function to create enhanced execution service"""
    
    redis_service = RedisService(redis_url)
    
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return EnhancedExecutionService(
        redis_service=redis_service,
        supabase_service=supabase_service,
        config=config
    )