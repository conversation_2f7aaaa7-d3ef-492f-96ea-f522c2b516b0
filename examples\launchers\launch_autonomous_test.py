#!/usr/bin/env python3
"""
Autonomous Test Launcher
Simple launcher for the 10-minute ensemble strategy test.
"""

import os
import subprocess
import sys
from datetime import datetime

def check_prerequisites():
    """Check if environment is ready for test"""
    print("🔍 Checking prerequisites...")
    
    # Check environment file
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    # Check if binance library is available
    try:
        import binance
        print("✅ Binance library available")
    except ImportError:
        print("❌ Binance library not installed")
        return False
    
    # Check test script exists
    if not os.path.exists('autonomous_10min_strategy_test.py'):
        print("❌ Test script not found")
        return False
    
    print("✅ All prerequisites met")
    return True

def launch_test():
    """Launch the autonomous test"""
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please install requirements and configure .env")
        return False
    
    print("\n" + "=" * 80)
    print("🚀 LAUNCHING 10-MINUTE AUTONOMOUS ENSEMBLE STRATEGY TEST")
    print("=" * 80)
    print(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Duration: 10 minutes")
    print("Position Size: 0.01 BTC (5x larger than demo)")
    print("Expected to complete automatically with full report")
    print("=" * 80)
    
    # Activate virtual environment and run test
    try:
        # Run the test script
        result = subprocess.run([
            'python', 'autonomous_10min_strategy_test.py'
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n✅ Test completed successfully!")
            print("📄 Check for report file: autonomous_test_report_*.json")
        else:
            print("\n❌ Test failed")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"\n❌ Error launching test: {e}")
        return False

def show_instructions():
    """Show usage instructions"""
    print("=" * 80)
    print("📋 AUTONOMOUS 10-MINUTE ENSEMBLE STRATEGY TEST")
    print("=" * 80)
    print("\nThis script will:")
    print("✅ Run for exactly 10 minutes")
    print("✅ Use 0.01 BTC positions (5x larger than demo)")
    print("✅ Execute ensemble strategy (Grid + Technical + Trend)")
    print("✅ Automatically close all positions at end")
    print("✅ Generate comprehensive performance report")
    print("✅ Save detailed results for analysis")
    
    print("\nFeatures:")
    print("• Larger position sizes to overcome fee drag")
    print("• Real-time risk management and stop-losses")
    print("• Complete strategy attribution")
    print("• Detailed trade-by-trade analysis")
    print("• Autonomous operation (no manual intervention)")
    
    print("\nFiles generated:")
    print("• autonomous_test_report_YYYYMMDD_HHMMSS.json")
    print("• autonomous_trading_test.log")
    
    print("\nTo run test: python launch_autonomous_test.py")
    print("=" * 80)

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        show_instructions()
        return
    
    print("=" * 80)
    print("🤖 AUTONOMOUS ENSEMBLE STRATEGY TEST LAUNCHER")
    print("=" * 80)
    
    # Ask for confirmation
    response = input("\n🚀 Launch 10-minute autonomous test? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        success = launch_test()
        if success:
            print("\n🎉 Test launcher completed successfully!")
            print("📊 Review the generated report file for detailed results.")
        else:
            print("\n❌ Test launcher failed.")
            print("💡 Check the log file for detailed error information.")
    else:
        print("\n⏹️  Test cancelled by user.")
        print("💡 Run with --help flag for more information.")

if __name__ == "__main__":
    main()