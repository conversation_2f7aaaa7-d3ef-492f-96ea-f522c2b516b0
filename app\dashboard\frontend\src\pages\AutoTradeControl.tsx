import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Switch,
  FormControlLabel,
  CircularProgress,
  Snackbar,
  Alert,
  Divider,
  Paper,
  Button,
} from '@mui/material';
import { tradingAPI, mlAPI } from '../services/api';
import TradeDashboard from '../components/TradeDashboard';

const AutoTradeControl: React.FC = () => {
  const [autoTradingEnabled, setAutoTradingEnabled] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true); // Start loading initially
  const [trainingLoading, setTrainingLoading] = useState<boolean>(false);
  const [symbol] = useState('BTCUSDT'); // Keep symbol if needed by enable API
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Show notification
  const showNotification = (
    message: string,
    severity: 'success' | 'error' | 'info' | 'warning'
  ) => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // Fetch initial auto trading status
  const fetchAutoTradingStatus = async () => {
    setLoading(true);
    try {
      const autoTradingStatus = await tradingAPI.getAutoTradingStatus();
      setAutoTradingEnabled(autoTradingStatus.enabled);
    } catch (error) {
      console.error('Error fetching auto trading status:', error);
      showNotification(
        'Error fetching auto trading status. Please try again.',
        'error'
      );
      // Keep the switch potentially disabled or in a default state if fetch fails
      setAutoTradingEnabled(false);
    } finally {
      setLoading(false);
    }
  };

  // Toggle auto trading
  const handleToggleAutoTrading = async () => {
    const action = autoTradingEnabled ? 'disable' : 'enable';
    setLoading(true); // Show loading indicator on the switch itself maybe? Or general loading state
    try {
      if (autoTradingEnabled) {
        await tradingAPI.disableAutoTrading();
        showNotification('Auto trading disabled', 'success');
        setAutoTradingEnabled(false);
      } else {
        // Assuming enableAutoTrading might need the symbol
        await tradingAPI.enableAutoTrading(symbol);
        showNotification('Auto trading enabled', 'success');
        setAutoTradingEnabled(true);
      }
    } catch (error: any) {
      console.error(`Error ${action}ing auto trading:`, error);
      showNotification(
        `Failed to ${action} auto trading: ${error.message || 'Unknown error'}`,
        'error'
      );
      // Revert state on error? Or keep it as is? Let's revert for now.
      // setAutoTradingEnabled(autoTradingEnabled); // No change needed if state wasn't optimistically updated
    } finally {
      setLoading(false);
    }
  };

  // Trigger ML model training
  const handleTrainML = async () => {
    setTrainingLoading(true);
    try {
      await mlAPI.trainModel({});
      showNotification('ML training started', 'success');
    } catch (error: any) {
      console.error('Error starting ML training:', error);
      showNotification(`ML training failed: ${error.message}`, 'error');
    } finally {
      setTrainingLoading(false);
    }
  };

  // Fetch status on initial load
  useEffect(() => {
    fetchAutoTradingStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      {/* Auto Trading Control Section */}
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Typography variant="h5" component="h1" gutterBottom>
            Automated Trading Control
          </Typography>
          {loading && !notification.open ? ( // Show spinner only during initial load
            <CircularProgress sx={{ mt: 2 }} />
          ) : (
            <FormControlLabel
              control={
                <Switch
                  checked={autoTradingEnabled}
                  onChange={handleToggleAutoTrading}
                  disabled={loading}
                  color="success"
                  sx={{ transform: 'scale(1.5)', ml: 2 }}
                />
              }
              label={
                <Typography variant="h6">
                  {autoTradingEnabled ? 'Auto Trading Enabled' : 'Auto Trading Disabled'}
                </Typography>
              }
              labelPlacement="start"
              sx={{ mt: 3, p: 2, border: '1px solid grey', borderRadius: 2 }}
            />
          )}
          <Button
            variant="contained"
            color="primary"
            onClick={handleTrainML}
            disabled={loading || trainingLoading}
            sx={{ mt: 2 }}
          >
            {trainingLoading ? <CircularProgress size={20} /> : 'Train ML Model'}
          </Button>
        </Box>
      </Paper>

      {/* Divider between sections */}
      <Divider sx={{ my: 4 }} />

      {/* Trade Dashboard Section */}
      <TradeDashboard />

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default AutoTradeControl; // Renamed export