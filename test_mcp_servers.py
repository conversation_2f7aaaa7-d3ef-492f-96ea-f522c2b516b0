#!/usr/bin/env python3
"""
Comprehensive MCP Server Test Suite
Tests all MCP servers to ensure they're working properly
"""

import subprocess
import time
import sys
import os
from pathlib import Path

def test_python_mcp_server(server_path, server_name):
    """Test a Python-based MCP server"""
    print(f"\n🧪 Testing {server_name}...")
    try:
        # Start the server process
        process = subprocess.Popen(
            [sys.executable, server_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        # Wait a moment for startup
        time.sleep(2)
        
        # Check if process is still running (good sign)
        if process.poll() is None:
            print(f"✅ {server_name}: Started successfully")
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ {server_name}: Failed to start")
            if stderr:
                print(f"   Error: {stderr[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ {server_name}: Exception - {str(e)}")
        return False

def test_node_mcp_server(server_dir, server_name, entry_point="dist/index.js"):
    """Test a Node.js-based MCP server"""
    print(f"\n🧪 Testing {server_name}...")
    try:
        # Check if server directory exists
        if not os.path.exists(server_dir):
            print(f"❌ {server_name}: Server directory not found at {server_dir}")
            return False

        server_path = Path(server_dir) / entry_point
        if not server_path.exists():
            # Try alternative entry points
            alternatives = ["build/index.js", "index.js", "src/index.js"]
            for alt in alternatives:
                alt_path = Path(server_dir) / alt
                if alt_path.exists():
                    server_path = alt_path
                    entry_point = alt
                    break
            else:
                print(f"❌ {server_name}: Entry point not found")
                return False

        # Start the server process with correct working directory
        process = subprocess.Popen(
            ["node", entry_point],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=server_dir
        )
        
        # Wait a moment for startup
        time.sleep(2)
        
        # Check if process is still running (good sign)
        if process.poll() is None:
            print(f"✅ {server_name}: Started successfully")
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ {server_name}: Failed to start")
            if stderr:
                print(f"   Error: {stderr[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ {server_name}: Exception - {str(e)}")
        return False

def main():
    """Run comprehensive MCP server tests"""
    print("🚀 Starting MCP Server Test Suite")
    print("=" * 50)
    
    results = {}
    
    # Test Python MCP Servers
    python_servers = [
        ("mcp-servers/mlflow-local/mlflow_mcp_server.py", "MLflow MCP"),
        ("mcp-servers/mcp-zenml/zenml_server.py", "ZenML MCP"),
    ]
    
    for server_path, server_name in python_servers:
        if os.path.exists(server_path):
            results[server_name] = test_python_mcp_server(server_path, server_name)
        else:
            print(f"\n❌ {server_name}: Server file not found at {server_path}")
            results[server_name] = False
    
    # Test Node.js MCP Servers
    node_servers = [
        ("mcp-servers/mcp-crypto-price", "Crypto Price MCP"),
        ("binance-mcp", "Binance MCP"),
        ("mcp-redis-trading", "Redis Trading MCP"),
    ]
    
    for server_dir, server_name in node_servers:
        if os.path.exists(server_dir):
            results[server_name] = test_node_mcp_server(server_dir, server_name)
        else:
            print(f"\n❌ {server_name}: Server directory not found at {server_dir}")
            results[server_name] = False
    
    # Print Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for server_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {server_name}")
        if success:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} servers passed tests")
    
    if passed == total:
        print("🎉 All MCP servers are working correctly!")
        return 0
    else:
        print("⚠️  Some MCP servers need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
