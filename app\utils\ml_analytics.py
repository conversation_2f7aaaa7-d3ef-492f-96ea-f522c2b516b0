"""
ML Analytics Utilities for Session Reporting
Enhanced ML performance analysis and tracking utilities.
Created: June 16, 2025
"""

import logging
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import statistics

logger = logging.getLogger(__name__)

@dataclass
class MLPerformanceMetrics:
    """ML performance metrics for session analysis"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    confidence_score: float = 0.0
    prediction_latency_ms: float = 0.0
    model_version: str = "unknown"
    drift_score: float = 0.0
    feature_importance: Dict[str, float] = None
    training_cost: float = 0.0
    inference_cost: float = 0.0
    
    def __post_init__(self):
        if self.feature_importance is None:
            self.feature_importance = {}

@dataclass
class StrategyPerformanceComparison:
    """Compare ML vs traditional strategy performance"""
    ml_strategy_return: float = 0.0
    traditional_strategy_return: float = 0.0
    combined_strategy_return: float = 0.0
    ml_sharpe_ratio: float = 0.0
    traditional_sharpe_ratio: float = 0.0
    combined_sharpe_ratio: float = 0.0
    ml_win_rate: float = 0.0
    traditional_win_rate: float = 0.0
    correlation_coefficient: float = 0.0
    diversification_benefit: float = 0.0

class MLAnalyticsEngine:
    """Enhanced ML analytics engine for session reporting"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def analyze_ml_performance(
        self, 
        session_data: Dict,
        ml_metrics: Dict = None
    ) -> Dict[str, Any]:
        """
        Comprehensive ML performance analysis for session
        
        Args:
            session_data: Complete session data including trades and performance
            ml_metrics: Optional ML-specific metrics if available
            
        Returns:
            Detailed ML performance analysis
        """
        try:
            performance = session_data.get("performance", {})
            trades = session_data.get("trades", [])
            
            # Extract ML-specific metrics
            ml_accuracy = performance.get("ml_model_accuracy", 0.0)
            ml_confidence = performance.get("ml_model_confidence", 0.0)
            ml_decisions = performance.get("ml_decisions_count", 0)
            ml_correct = performance.get("ml_decisions_correct", 0)
            ml_profitable = performance.get("ml_decisions_profitable", 0)
            
            # Calculate ML performance metrics
            prediction_accuracy = ml_correct / ml_decisions if ml_decisions > 0 else 0.0
            profitability_rate = ml_profitable / ml_decisions if ml_decisions > 0 else 0.0
            
            # Analyze confidence vs performance correlation
            confidence_performance = await self._analyze_confidence_performance(trades, performance)
            
            # Model drift analysis
            drift_analysis = await self._analyze_model_drift(performance)
            
            # Feature importance evolution
            feature_analysis = await self._analyze_feature_importance_evolution(performance)
            
            # Cost-benefit analysis
            cost_analysis = await self._analyze_ml_costs_benefits(performance, trades)
            
            # Model decision quality analysis
            decision_analysis = await self._analyze_ml_decisions(trades, performance)
            
            return {
                "ml_model_metrics": {
                    "accuracy": ml_accuracy,
                    "confidence": ml_confidence,
                    "prediction_accuracy": prediction_accuracy,
                    "profitability_rate": profitability_rate,
                    "model_version": performance.get("ml_model_version", "unknown"),
                    "model_state": performance.get("current_model_state", "unknown"),
                    "last_update": performance.get("model_last_update", datetime.now()).isoformat() if isinstance(performance.get("model_last_update"), datetime) else str(performance.get("model_last_update", "unknown"))
                },
                "confidence_analysis": confidence_performance,
                "drift_analysis": drift_analysis,
                "feature_analysis": feature_analysis,
                "cost_analysis": cost_analysis,
                "decision_analysis": decision_analysis,
                "performance_attribution": await self._calculate_ml_performance_attribution(trades),
                "recommendations": await self._generate_ml_recommendations(performance, trades)
            }
            
        except Exception as e:
            self.logger.error(f"Error in ML performance analysis: {e}")
            return {"error": str(e), "analysis_timestamp": datetime.now().isoformat()}
    
    async def analyze_strategy_correlation(
        self, 
        session_data: Dict
    ) -> Dict[str, Any]:
        """
        Analyze correlation between different strategies including ML components
        
        Args:
            session_data: Session data with strategy performance information
            
        Returns:
            Strategy correlation analysis
        """
        try:
            performance = session_data.get("performance", {})
            strategy_performance = performance.get("strategy_performance", {})
            trades = session_data.get("trades", [])
            
            # Group trades by strategy
            strategy_trades = {}
            for trade in trades:
                strategy = trade.get("strategy", "unknown")
                if strategy not in strategy_trades:
                    strategy_trades[strategy] = []
                strategy_trades[strategy].append(trade)
            
            # Calculate strategy returns over time
            strategy_returns = {}
            for strategy, strategy_trade_list in strategy_trades.items():
                returns = [trade.get("pnl", 0) for trade in strategy_trade_list]
                strategy_returns[strategy] = returns
            
            # Calculate correlation matrix
            correlation_matrix = await self._calculate_strategy_correlation_matrix(strategy_returns)
            
            # Identify ML vs traditional strategy correlations
            ml_traditional_correlation = await self._analyze_ml_traditional_correlation(strategy_returns)
            
            # Strategy effectiveness analysis
            effectiveness_analysis = await self._analyze_strategy_effectiveness(strategy_performance, strategy_trades)
            
            # Diversification benefits
            diversification_analysis = await self._analyze_diversification_benefits(strategy_returns)
            
            return {
                "correlation_matrix": correlation_matrix,
                "ml_traditional_correlation": ml_traditional_correlation,
                "strategy_effectiveness": effectiveness_analysis,
                "diversification_analysis": diversification_analysis,
                "optimal_weights": await self._calculate_optimal_strategy_weights(strategy_returns),
                "risk_contribution": await self._analyze_risk_contribution(strategy_returns),
                "recommendations": await self._generate_correlation_recommendations(correlation_matrix, effectiveness_analysis)
            }
            
        except Exception as e:
            self.logger.error(f"Error in strategy correlation analysis: {e}")
            return {"error": str(e), "analysis_timestamp": datetime.now().isoformat()}
    
    async def analyze_market_impact(
        self, 
        session_data: Dict,
        market_data: Dict = None
    ) -> Dict[str, Any]:
        """
        Analyze how market conditions impact ML model and strategy performance
        
        Args:
            session_data: Session performance and trade data
            market_data: Optional external market condition data
            
        Returns:
            Market impact analysis
        """
        try:
            trades = session_data.get("trades", [])
            performance = session_data.get("performance", {})
            
            # Analyze performance by market conditions
            market_regime_analysis = await self._analyze_market_regime_performance(trades)
            
            # Volatility impact on ML performance
            volatility_impact = await self._analyze_volatility_impact(trades, performance)
            
            # Trend impact analysis
            trend_impact = await self._analyze_trend_impact(trades, performance)
            
            # Volume impact analysis
            volume_impact = await self._analyze_volume_impact(trades)
            
            # Time-of-day performance analysis
            temporal_analysis = await self._analyze_temporal_performance(trades)
            
            # Market stress testing
            stress_analysis = await self._analyze_stress_performance(trades, performance)
            
            return {
                "market_regime_analysis": market_regime_analysis,
                "volatility_impact": volatility_impact,
                "trend_impact": trend_impact,
                "volume_impact": volume_impact,
                "temporal_analysis": temporal_analysis,
                "stress_analysis": stress_analysis,
                "market_adaptation": await self._analyze_market_adaptation(trades, performance),
                "recommendations": await self._generate_market_impact_recommendations(market_regime_analysis, volatility_impact)
            }
            
        except Exception as e:
            self.logger.error(f"Error in market impact analysis: {e}")
            return {"error": str(e), "analysis_timestamp": datetime.now().isoformat()}
    
    async def analyze_cost_benefit(
        self, 
        session_data: Dict
    ) -> Dict[str, Any]:
        """
        Comprehensive cost-benefit analysis of ML vs traditional approaches
        
        Args:
            session_data: Session data with performance and cost information
            
        Returns:
            Cost-benefit analysis
        """
        try:
            performance = session_data.get("performance", {})
            trades = session_data.get("trades", [])
            
            # ML costs
            ml_training_cost = performance.get("ml_training_cost", 0.0)
            ml_inference_cost = performance.get("ml_inference_cost", 0.0)
            ml_total_cost = performance.get("ml_total_cost", 0.0)
            
            # Performance benefits
            ml_vs_traditional = performance.get("ml_vs_traditional_performance", {})
            ml_return = ml_vs_traditional.get("ml", 0.0)
            traditional_return = ml_vs_traditional.get("traditional", 0.0)
            
            # Calculate ROI
            ml_roi = performance.get("ml_roi", 0.0)
            
            # Calculate cost per trade/prediction
            cost_per_prediction = performance.get("cost_per_prediction", 0.0)
            
            # Analyze cost efficiency
            cost_efficiency = await self._analyze_cost_efficiency(ml_total_cost, ml_return, trades)
            
            # Break-even analysis
            breakeven_analysis = await self._calculate_breakeven_analysis(ml_total_cost, ml_return, traditional_return)
            
            # Cost trend analysis
            cost_trends = await self._analyze_cost_trends(session_data)
            
            # Value attribution
            value_attribution = await self._analyze_ml_value_attribution(trades, performance)
            
            return {
                "cost_summary": {
                    "ml_training_cost": ml_training_cost,
                    "ml_inference_cost": ml_inference_cost,
                    "ml_total_cost": ml_total_cost,
                    "cost_per_prediction": cost_per_prediction,
                    "cost_per_trade": ml_total_cost / len(trades) if trades else 0.0
                },
                "benefit_summary": {
                    "ml_return": ml_return,
                    "traditional_return": traditional_return,
                    "excess_return": ml_return - traditional_return,
                    "ml_roi": ml_roi,
                    "return_multiple": ml_return / ml_total_cost if ml_total_cost > 0 else 0.0
                },
                "efficiency_analysis": cost_efficiency,
                "breakeven_analysis": breakeven_analysis,
                "cost_trends": cost_trends,
                "value_attribution": value_attribution,
                "cost_optimization_suggestions": await self._generate_cost_optimization_suggestions(cost_efficiency, cost_trends),
                "roi_projections": await self._calculate_roi_projections(ml_roi, cost_trends)
            }
            
        except Exception as e:
            self.logger.error(f"Error in cost-benefit analysis: {e}")
            return {"error": str(e), "analysis_timestamp": datetime.now().isoformat()}
    
    # Helper methods for detailed analysis
    
    async def _analyze_confidence_performance(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze correlation between ML confidence and trade performance"""
        try:
            confidence_buckets = performance.get("ml_confidence_buckets", {"high": 0, "medium": 0, "low": 0})
            
            # Analyze trades by confidence level
            high_confidence_trades = [t for t in trades if t.get("confidence", 0) > 0.8]
            medium_confidence_trades = [t for t in trades if 0.5 <= t.get("confidence", 0) <= 0.8]
            low_confidence_trades = [t for t in trades if t.get("confidence", 0) < 0.5]
            
            def calculate_performance(trade_list):
                if not trade_list:
                    return {"avg_pnl": 0, "win_rate": 0, "total_trades": 0}
                
                pnls = [t.get("pnl", 0) for t in trade_list]
                wins = len([p for p in pnls if p > 0])
                
                return {
                    "avg_pnl": statistics.mean(pnls),
                    "win_rate": wins / len(trade_list),
                    "total_trades": len(trade_list),
                    "total_pnl": sum(pnls)
                }
            
            return {
                "high_confidence": calculate_performance(high_confidence_trades),
                "medium_confidence": calculate_performance(medium_confidence_trades),
                "low_confidence": calculate_performance(low_confidence_trades),
                "confidence_correlation": await self._calculate_confidence_correlation(trades),
                "optimal_confidence_threshold": await self._find_optimal_confidence_threshold(trades)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing confidence performance: {e}")
            return {"error": str(e)}
    
    async def _analyze_model_drift(self, performance: Dict) -> Dict:
        """Analyze model drift and performance degradation"""
        try:
            drift_score = performance.get("ml_drift_score", 0.0)
            model_accuracy = performance.get("ml_model_accuracy", 0.0)
            
            # Determine drift severity
            if drift_score < 0.1:
                drift_severity = "low"
            elif drift_score < 0.3:
                drift_severity = "moderate"
            else:
                drift_severity = "high"
            
            return {
                "drift_score": drift_score,
                "drift_severity": drift_severity,
                "current_accuracy": model_accuracy,
                "drift_threshold": 0.3,  # Configurable threshold
                "requires_retraining": drift_score > 0.3,
                "estimated_performance_impact": drift_score * 0.1,  # Rough estimate
                "recommendations": [
                    "Monitor feature distributions" if drift_score > 0.1 else "Drift within acceptable range",
                    "Consider retraining" if drift_score > 0.3 else "Model performance stable",
                    "Investigate feature engineering" if drift_score > 0.5 else "Feature quality acceptable"
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing model drift: {e}")
            return {"error": str(e)}
    
    async def _analyze_feature_importance_evolution(self, performance: Dict) -> Dict:
        """Analyze how feature importance changes over time"""
        try:
            current_features = performance.get("feature_importance_current", {})
            feature_drift = performance.get("feature_importance_drift", {})
            top_features = performance.get("top_features", [])
            
            # Calculate feature stability
            feature_stability = {}
            for feature in current_features:
                stability = 1.0 - abs(feature_drift.get(feature, 0.0))
                feature_stability[feature] = max(0.0, stability)
            
            return {
                "current_top_features": top_features[:10],
                "feature_importance": dict(sorted(current_features.items(), key=lambda x: x[1], reverse=True)[:10]),
                "feature_stability": feature_stability,
                "most_stable_features": sorted(feature_stability.items(), key=lambda x: x[1], reverse=True)[:5],
                "most_volatile_features": sorted(feature_stability.items(), key=lambda x: x[1])[:5],
                "feature_drift_summary": {
                    "avg_drift": statistics.mean(feature_drift.values()) if feature_drift else 0.0,
                    "max_drift": max(feature_drift.values()) if feature_drift else 0.0,
                    "features_with_high_drift": [f for f, d in feature_drift.items() if d > 0.3]
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing feature importance: {e}")
            return {"error": str(e)}
    
    async def _analyze_ml_costs_benefits(self, performance: Dict, trades: List[Dict]) -> Dict:
        """Analyze ML implementation costs vs benefits"""
        try:
            ml_cost = performance.get("ml_total_cost", 0.0)
            ml_roi = performance.get("ml_roi", 0.0)
            
            # Calculate benefit per trade
            ml_trades = [t for t in trades if t.get("strategy", "").lower().find("ml") >= 0]
            ml_pnl = sum(t.get("pnl", 0) for t in ml_trades)
            
            return {
                "total_cost": ml_cost,
                "total_benefit": ml_pnl,
                "net_benefit": ml_pnl - ml_cost,
                "roi": ml_roi,
                "cost_per_trade": ml_cost / len(ml_trades) if ml_trades else 0.0,
                "benefit_per_trade": ml_pnl / len(ml_trades) if ml_trades else 0.0,
                "payback_period_days": ml_cost / (ml_pnl / 30) if ml_pnl > 0 else float('inf'),
                "cost_efficiency_score": (ml_pnl / ml_cost) if ml_cost > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing ML costs/benefits: {e}")
            return {"error": str(e)}
    
    async def _analyze_ml_decisions(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze quality of ML-driven decisions"""
        try:
            ml_decisions = performance.get("ml_decisions_count", 0)
            ml_correct = performance.get("ml_decisions_correct", 0)
            ml_profitable = performance.get("ml_decisions_profitable", 0)
            
            # Analyze ML-driven trades
            ml_trades = [t for t in trades if t.get("strategy", "").lower().find("ml") >= 0]
            
            if not ml_trades:
                return {"message": "No ML-driven trades found"}
            
            ml_pnls = [t.get("pnl", 0) for t in ml_trades]
            ml_confidences = [t.get("confidence", 0) for t in ml_trades]
            
            return {
                "decision_accuracy": ml_correct / ml_decisions if ml_decisions > 0 else 0.0,
                "profitability_rate": ml_profitable / ml_decisions if ml_decisions > 0 else 0.0,
                "avg_confidence": statistics.mean(ml_confidences) if ml_confidences else 0.0,
                "avg_pnl": statistics.mean(ml_pnls) if ml_pnls else 0.0,
                "total_ml_pnl": sum(ml_pnls),
                "ml_win_rate": len([p for p in ml_pnls if p > 0]) / len(ml_pnls) if ml_pnls else 0.0,
                "confidence_pnl_correlation": np.corrcoef(ml_confidences, ml_pnls)[0,1] if len(ml_confidences) > 1 and len(ml_pnls) > 1 else 0.0,
                "decision_quality_score": (ml_correct / ml_decisions * 0.5 + ml_profitable / ml_decisions * 0.5) if ml_decisions > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing ML decisions: {e}")
            return {"error": str(e)}
    
    # Additional helper methods would continue here...
    # For brevity, I'm including key methods. The full implementation would include all helper methods.
    
    async def _calculate_ml_performance_attribution(self, trades: List[Dict]) -> Dict:
        """Calculate performance attribution to ML components"""
        try:
            ml_trades = [t for t in trades if "ml" in t.get("strategy", "").lower()]
            traditional_trades = [t for t in trades if "ml" not in t.get("strategy", "").lower()]
            
            ml_pnl = sum(t.get("pnl", 0) for t in ml_trades)
            traditional_pnl = sum(t.get("pnl", 0) for t in traditional_trades)
            total_pnl = ml_pnl + traditional_pnl
            
            return {
                "ml_contribution": ml_pnl / total_pnl if total_pnl != 0 else 0.0,
                "traditional_contribution": traditional_pnl / total_pnl if total_pnl != 0 else 0.0,
                "ml_trade_count": len(ml_trades),
                "traditional_trade_count": len(traditional_trades),
                "ml_avg_pnl": ml_pnl / len(ml_trades) if ml_trades else 0.0,
                "traditional_avg_pnl": traditional_pnl / len(traditional_trades) if traditional_trades else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating performance attribution: {e}")
            return {"error": str(e)}
    
    async def _generate_ml_recommendations(self, performance: Dict, trades: List[Dict]) -> List[str]:
        """Generate actionable ML improvement recommendations"""
        recommendations = []
        
        try:
            # Check model accuracy
            accuracy = performance.get("ml_model_accuracy", 0.0)
            if accuracy < 0.6:
                recommendations.append("Consider retraining model - accuracy below 60%")
            
            # Check drift
            drift_score = performance.get("ml_drift_score", 0.0)
            if drift_score > 0.3:
                recommendations.append("High model drift detected - schedule retraining")
            
            # Check ROI
            ml_roi = performance.get("ml_roi", 0.0)
            if ml_roi < 0.1:
                recommendations.append("ML ROI below 10% - review cost optimization")
            
            # Check confidence utilization
            confidence_buckets = performance.get("ml_confidence_buckets", {})
            high_conf = confidence_buckets.get("high", 0)
            total_decisions = sum(confidence_buckets.values())
            
            if high_conf / total_decisions < 0.3 if total_decisions > 0 else False:
                recommendations.append("Low high-confidence decisions - review model calibration")
            
            return recommendations if recommendations else ["ML system performing within acceptable parameters"]
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]
    
    # Placeholder methods for remaining functionality
    async def _calculate_strategy_correlation_matrix(self, strategy_returns: Dict) -> Dict:
        """Calculate correlation matrix between strategies"""
        # Implementation would calculate correlations between strategy returns
        return {"placeholder": "Strategy correlation matrix"}
    
    async def _analyze_ml_traditional_correlation(self, strategy_returns: Dict) -> Dict:
        """Analyze correlation between ML and traditional strategies"""
        return {"placeholder": "ML vs traditional correlation"}
    
    async def _analyze_strategy_effectiveness(self, strategy_performance: Dict, strategy_trades: Dict) -> Dict:
        """Analyze individual strategy effectiveness"""
        return {"placeholder": "Strategy effectiveness analysis"}
    
    async def _analyze_diversification_benefits(self, strategy_returns: Dict) -> Dict:
        """Analyze diversification benefits across strategies"""
        return {"placeholder": "Diversification analysis"}
    
    async def _calculate_optimal_strategy_weights(self, strategy_returns: Dict) -> Dict:
        """Calculate optimal strategy allocation weights"""
        return {"placeholder": "Optimal weights"}
    
    async def _analyze_risk_contribution(self, strategy_returns: Dict) -> Dict:
        """Analyze risk contribution of each strategy"""
        return {"placeholder": "Risk contribution analysis"}
    
    async def _generate_correlation_recommendations(self, correlation_matrix: Dict, effectiveness: Dict) -> List[str]:
        """Generate recommendations based on correlation analysis"""
        return ["Correlation-based recommendation"]
    
    # Market impact analysis helpers
    async def _analyze_market_regime_performance(self, trades: List[Dict]) -> Dict:
        """Analyze performance across different market regimes"""
        return {"placeholder": "Market regime analysis"}
    
    async def _analyze_volatility_impact(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze how volatility impacts performance"""
        return {"placeholder": "Volatility impact analysis"}
    
    async def _analyze_trend_impact(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze how trends impact performance"""
        return {"placeholder": "Trend impact analysis"}
    
    async def _analyze_volume_impact(self, trades: List[Dict]) -> Dict:
        """Analyze how volume impacts performance"""
        return {"placeholder": "Volume impact analysis"}
    
    async def _analyze_temporal_performance(self, trades: List[Dict]) -> Dict:
        """Analyze performance by time of day/week"""
        return {"placeholder": "Temporal analysis"}
    
    async def _analyze_stress_performance(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze performance under market stress"""
        return {"placeholder": "Stress analysis"}
    
    async def _analyze_market_adaptation(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze how well strategies adapt to market changes"""
        return {"placeholder": "Market adaptation analysis"}
    
    async def _generate_market_impact_recommendations(self, regime_analysis: Dict, volatility_impact: Dict) -> List[str]:
        """Generate market impact recommendations"""
        return ["Market impact recommendation"]
    
    # Cost-benefit analysis helpers
    async def _analyze_cost_efficiency(self, total_cost: float, return_value: float, trades: List[Dict]) -> Dict:
        """Analyze cost efficiency of ML implementation"""
        return {"placeholder": "Cost efficiency analysis"}
    
    async def _calculate_breakeven_analysis(self, ml_cost: float, ml_return: float, traditional_return: float) -> Dict:
        """Calculate breakeven analysis for ML investment"""
        return {"placeholder": "Breakeven analysis"}
    
    async def _analyze_cost_trends(self, session_data: Dict) -> Dict:
        """Analyze cost trends over time"""
        return {"placeholder": "Cost trends analysis"}
    
    async def _analyze_ml_value_attribution(self, trades: List[Dict], performance: Dict) -> Dict:
        """Analyze value attribution of ML components"""
        return {"placeholder": "Value attribution analysis"}
    
    async def _generate_cost_optimization_suggestions(self, efficiency: Dict, trends: Dict) -> List[str]:
        """Generate cost optimization suggestions"""
        return ["Cost optimization suggestion"]
    
    async def _calculate_roi_projections(self, current_roi: float, trends: Dict) -> Dict:
        """Calculate ROI projections"""
        return {"placeholder": "ROI projections"}
    
    # Additional helper methods
    async def _calculate_confidence_correlation(self, trades: List[Dict]) -> float:
        """Calculate correlation between confidence and performance"""
        try:
            confidences = [t.get("confidence", 0) for t in trades]
            pnls = [t.get("pnl", 0) for t in trades]
            
            if len(confidences) > 1 and len(pnls) > 1:
                return float(np.corrcoef(confidences, pnls)[0,1])
            return 0.0
            
        except Exception:
            return 0.0
    
    async def _find_optimal_confidence_threshold(self, trades: List[Dict]) -> float:
        """Find optimal confidence threshold for trade execution"""
        try:
            # Simple approach: find threshold that maximizes Sharpe ratio
            thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
            best_threshold = 0.5
            best_sharpe = 0.0
            
            for threshold in thresholds:
                filtered_trades = [t for t in trades if t.get("confidence", 0) >= threshold]
                if len(filtered_trades) < 10:  # Need minimum trades for meaningful analysis
                    continue
                
                pnls = [t.get("pnl", 0) for t in filtered_trades]
                if len(pnls) > 1:
                    sharpe = statistics.mean(pnls) / statistics.stdev(pnls) if statistics.stdev(pnls) > 0 else 0
                    if sharpe > best_sharpe:
                        best_sharpe = sharpe
                        best_threshold = threshold
            
            return best_threshold
            
        except Exception:
            return 0.5  # Default threshold