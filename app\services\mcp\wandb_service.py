# app/services/mcp/wandb_service.py
import wandb
import asyncio
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class ExperimentMetrics:
    """Structure for experiment metrics logging"""
    timestamp: datetime
    experiment_name: str
    strategy_weights: Dict[str, float]
    portfolio_value: float
    total_pnl: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    correlation_matrix: Dict[str, Dict[str, float]]
    individual_strategy_pnl: Dict[str, float]
    confidence_scores: Dict[str, float]
    market_conditions: Dict[str, Any]
    trade_count: int
    execution_time_ms: float

@dataclass
class ModelTrackingMetrics:
    """Structure for ML model performance tracking"""
    model_version: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    training_loss: float
    validation_loss: float
    convergence_episodes: int
    hyperparameters: Dict[str, Any]
    training_duration_seconds: float

class WandBService:
    """Weights & Biases integration service for experiment tracking and performance monitoring"""
    
    def __init__(
        self, 
        project_name: str = "crypto-ensemble-strategy",
        entity: Optional[str] = None,
        api_key: Optional[str] = None
    ):
        self.project_name = project_name
        self.entity = entity
        self.api_key = api_key
        self.current_run = None
        self.experiment_cache = {}
        
        # Initialize W&B
        if api_key:
            wandb.login(key=api_key)
            
    async def initialize_experiment(
        self, 
        experiment_name: str,
        config: Dict[str, Any],
        tags: List[str] = None
    ) -> str:
        """Initialize a new W&B experiment run"""
        try:
            # Create experiment configuration
            experiment_config = {
                "experiment_name": experiment_name,
                "timestamp": datetime.now().isoformat(),
                "ensemble_strategies": config.get("strategy_names", []),
                "ml_model_type": config.get("ml_model_type", "PPO"),
                "position_sizing_method": config.get("position_sizing", "kelly_criterion"),
                "risk_management": {
                    "max_position_size": config.get("max_position_size", 0.1),
                    "max_portfolio_risk": config.get("max_portfolio_risk", 0.8),
                    "correlation_threshold": config.get("correlation_threshold", 0.8)
                },
                "trading_parameters": {
                    "symbol": config.get("trading_symbol", "BTCUSDT"),
                    "portfolio_value": config.get("portfolio_value", 100000),
                    "risk_per_trade": config.get("risk_per_trade_pct", 1.0)
                }
            }
            
            # Initialize W&B run
            self.current_run = wandb.init(
                project=self.project_name,
                entity=self.entity,
                name=experiment_name,
                config=experiment_config,
                tags=tags or ["ensemble", "crypto", "automated"]
            )
            
            logger.info(f"Initialized W&B experiment: {experiment_name}")
            return self.current_run.id
            
        except Exception as e:
            logger.error(f"Failed to initialize W&B experiment: {e}")
            return None
    
    async def log_strategy_performance(
        self,
        metrics: ExperimentMetrics,
        step: Optional[int] = None
    ) -> None:
        """Log comprehensive strategy performance metrics"""
        try:
            if not self.current_run:
                await self.initialize_experiment(
                    metrics.experiment_name,
                    {"auto_initialized": True}
                )
            
            # Prepare metrics for logging
            log_data = {
                # Portfolio-level metrics
                "portfolio/total_value": metrics.portfolio_value,
                "portfolio/total_pnl": metrics.total_pnl,
                "portfolio/sharpe_ratio": metrics.sharpe_ratio,
                "portfolio/max_drawdown": metrics.max_drawdown,
                "portfolio/win_rate": metrics.win_rate,
                "portfolio/trade_count": metrics.trade_count,
                "portfolio/execution_time_ms": metrics.execution_time_ms,
                
                # Strategy weights
                **{f"weights/{strategy}": weight 
                   for strategy, weight in metrics.strategy_weights.items()},
                
                # Individual strategy PnL
                **{f"strategy_pnl/{strategy}": pnl 
                   for strategy, pnl in metrics.individual_strategy_pnl.items()},
                
                # Confidence scores
                **{f"confidence/{strategy}": score 
                   for strategy, score in metrics.confidence_scores.items()},
                
                # Market conditions
                **{f"market/{key}": value 
                   for key, value in metrics.market_conditions.items()
                   if isinstance(value, (int, float))},
                
                # Timestamp
                "timestamp": metrics.timestamp.timestamp()
            }
            
            # Log correlation matrix as separate metrics
            for strategy1, correlations in metrics.correlation_matrix.items():
                for strategy2, correlation in correlations.items():
                    if strategy1 != strategy2:
                        log_data[f"correlation/{strategy1}_vs_{strategy2}"] = correlation
            
            # Log to W&B
            wandb.log(log_data, step=step)
            
            # Cache metrics for dashboard updates
            self.experiment_cache[metrics.timestamp.isoformat()] = asdict(metrics)
            
            logger.debug(f"Logged strategy performance metrics at step {step}")
            
        except Exception as e:
            logger.error(f"Failed to log strategy performance: {e}")
    
    async def log_ml_model_performance(
        self,
        model_metrics: ModelTrackingMetrics,
        step: Optional[int] = None
    ) -> None:
        """Log ML model training and performance metrics"""
        try:
            log_data = {
                # Model performance
                "model/accuracy": model_metrics.accuracy,
                "model/precision": model_metrics.precision,
                "model/recall": model_metrics.recall,
                "model/f1_score": model_metrics.f1_score,
                "model/training_loss": model_metrics.training_loss,
                "model/validation_loss": model_metrics.validation_loss,
                "model/convergence_episodes": model_metrics.convergence_episodes,
                "model/training_duration_seconds": model_metrics.training_duration_seconds,
                "model/version": model_metrics.model_version,
                
                # Hyperparameters (flatten for logging)
                **{f"hyperparams/{key}": value 
                   for key, value in model_metrics.hyperparameters.items()
                   if isinstance(value, (int, float, str, bool))}
            }
            
            wandb.log(log_data, step=step)
            logger.debug(f"Logged ML model performance for version {model_metrics.model_version}")
            
        except Exception as e:
            logger.error(f"Failed to log ML model performance: {e}")
    
    async def log_trade_execution(
        self,
        trade_data: Dict[str, Any],
        step: Optional[int] = None
    ) -> None:
        """Log individual trade executions"""
        try:
            log_data = {
                f"trades/{trade_data['strategy_name']}/action": 1 if trade_data['action'] == 'BUY' else -1,
                f"trades/{trade_data['strategy_name']}/quantity": trade_data.get('quantity', 0),
                f"trades/{trade_data['strategy_name']}/price": trade_data.get('price', 0),
                f"trades/{trade_data['strategy_name']}/confidence": trade_data.get('confidence', 0),
                f"trades/{trade_data['strategy_name']}/pnl": trade_data.get('pnl', 0),
                "trades/total_count": step or 0
            }
            
            wandb.log(log_data, step=step)
            
        except Exception as e:
            logger.error(f"Failed to log trade execution: {e}")
    
    async def create_ensemble_comparison_chart(
        self,
        performance_data: List[ExperimentMetrics],
        title: str = "Ensemble vs Individual Strategy Performance"
    ) -> None:
        """Create comparative performance visualization"""
        try:
            # Prepare data for visualization
            timestamps = [m.timestamp for m in performance_data]
            
            # Portfolio value over time
            portfolio_values = [m.portfolio_value for m in performance_data]
            
            # Individual strategy PnL over time
            strategy_names = list(performance_data[0].individual_strategy_pnl.keys())
            strategy_data = {}
            
            for strategy in strategy_names:
                strategy_data[strategy] = [
                    m.individual_strategy_pnl.get(strategy, 0) 
                    for m in performance_data
                ]
            
            # Create W&B table for detailed comparison
            table_data = []
            for i, metrics in enumerate(performance_data):
                row = {
                    "timestamp": metrics.timestamp.isoformat(),
                    "portfolio_value": metrics.portfolio_value,
                    "total_pnl": metrics.total_pnl,
                    "sharpe_ratio": metrics.sharpe_ratio,
                    "max_drawdown": metrics.max_drawdown,
                    "win_rate": metrics.win_rate
                }
                
                # Add individual strategy data
                for strategy, pnl in metrics.individual_strategy_pnl.items():
                    row[f"{strategy}_pnl"] = pnl
                    row[f"{strategy}_weight"] = metrics.strategy_weights.get(strategy, 0)
                
                table_data.append(row)
            
            # Log as W&B table
            table = wandb.Table(
                columns=list(table_data[0].keys()),
                data=[list(row.values()) for row in table_data]
            )
            
            wandb.log({f"ensemble_comparison/{title}": table})
            
            # Create line plots for key metrics
            wandb.log({
                "charts/portfolio_value_over_time": wandb.plot.line(
                    table, "timestamp", "portfolio_value",
                    title="Portfolio Value Over Time"
                ),
                "charts/sharpe_ratio_over_time": wandb.plot.line(
                    table, "timestamp", "sharpe_ratio",
                    title="Sharpe Ratio Evolution"
                ),
                "charts/drawdown_over_time": wandb.plot.line(
                    table, "timestamp", "max_drawdown",
                    title="Maximum Drawdown Over Time"
                )
            })
            
            logger.info(f"Created ensemble comparison chart: {title}")
            
        except Exception as e:
            logger.error(f"Failed to create ensemble comparison chart: {e}")
    
    async def log_risk_metrics(
        self,
        risk_data: Dict[str, Any],
        step: Optional[int] = None
    ) -> None:
        """Log risk management metrics"""
        try:
            log_data = {
                "risk/portfolio_volatility": risk_data.get("portfolio_volatility", 0),
                "risk/var_95": risk_data.get("value_at_risk_95", 0),
                "risk/expected_shortfall": risk_data.get("expected_shortfall", 0),
                "risk/beta": risk_data.get("beta", 0),
                "risk/max_position_exposure": risk_data.get("max_position_exposure", 0),
                "risk/correlation_risk": risk_data.get("correlation_risk", 0),
                "risk/leverage_ratio": risk_data.get("leverage_ratio", 0)
            }
            
            wandb.log(log_data, step=step)
            
        except Exception as e:
            logger.error(f"Failed to log risk metrics: {e}")
    
    async def create_strategy_weight_heatmap(
        self,
        weight_history: List[Dict[str, float]],
        timestamps: List[datetime]
    ) -> None:
        """Create heatmap visualization of strategy weight changes over time"""
        try:
            # Prepare data matrix
            strategies = list(weight_history[0].keys())
            weight_matrix = []
            
            for weights in weight_history:
                weight_matrix.append([weights.get(strategy, 0) for strategy in strategies])
            
            # Create heatmap data
            heatmap_data = []
            for i, timestamp in enumerate(timestamps):
                for j, strategy in enumerate(strategies):
                    heatmap_data.append([
                        timestamp.isoformat(),
                        strategy,
                        weight_matrix[i][j]
                    ])
            
            # Log as W&B table
            heatmap_table = wandb.Table(
                columns=["timestamp", "strategy", "weight"],
                data=heatmap_data
            )
            
            wandb.log({"strategy_weights/heatmap": heatmap_table})
            
            logger.info("Created strategy weight heatmap")
            
        except Exception as e:
            logger.error(f"Failed to create strategy weight heatmap: {e}")
    
    async def log_hyperparameter_optimization(
        self,
        trial_results: List[Dict[str, Any]]
    ) -> None:
        """Log hyperparameter optimization results"""
        try:
            # Create table for trial results
            table_data = []
            for trial in trial_results:
                row = {
                    "trial_id": trial.get("trial_id", ""),
                    "objective_value": trial.get("objective_value", 0),
                    **trial.get("params", {})
                }
                table_data.append(row)
            
            table = wandb.Table(
                columns=list(table_data[0].keys()),
                data=[list(row.values()) for row in table_data]
            )
            
            wandb.log({"hyperparameter_optimization/trials": table})
            
            # Log best parameters
            best_trial = max(trial_results, key=lambda x: x.get("objective_value", 0))
            wandb.log({
                "hyperparameter_optimization/best_objective": best_trial.get("objective_value", 0),
                **{f"best_params/{key}": value for key, value in best_trial.get("params", {}).items()}
            })
            
            logger.info("Logged hyperparameter optimization results")
            
        except Exception as e:
            logger.error(f"Failed to log hyperparameter optimization: {e}")
    
    async def save_model_artifact(
        self,
        model_path: str,
        model_version: str,
        metadata: Dict[str, Any]
    ) -> str:
        """Save ML model as W&B artifact"""
        try:
            # Create artifact
            artifact = wandb.Artifact(
                name=f"ensemble_weight_optimizer",
                type="model",
                description="Ensemble strategy weight optimization model",
                metadata=metadata
            )
            
            # Add model files
            artifact.add_file(model_path)
            
            # Log artifact
            wandb.log_artifact(artifact)
            
            logger.info(f"Saved model artifact version {model_version}")
            return artifact.id
            
        except Exception as e:
            logger.error(f"Failed to save model artifact: {e}")
            return None
    
    async def finish_experiment(self) -> None:
        """Finish current W&B experiment"""
        try:
            if self.current_run:
                wandb.finish()
                self.current_run = None
                logger.info("Finished W&B experiment")
                
        except Exception as e:
            logger.error(f"Failed to finish W&B experiment: {e}")
    
    async def get_experiment_summary(self) -> Dict[str, Any]:
        """Get summary of current experiment metrics"""
        try:
            if not self.current_run:
                return {}
            
            # Get run summary
            summary = dict(self.current_run.summary)
            
            # Add run metadata
            summary.update({
                "run_id": self.current_run.id,
                "run_name": self.current_run.name,
                "project": self.current_run.project,
                "state": self.current_run.state,
                "url": self.current_run.url
            })
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get experiment summary: {e}")
            return {}
    
    def get_experiment_url(self) -> Optional[str]:
        """Get URL for current experiment dashboard"""
        if self.current_run:
            return self.current_run.url
        return None

# Utility functions for integration with existing system

async def create_experiment_metrics_from_portfolio(
    portfolio_manager,
    execution_time_ms: float = 0
) -> ExperimentMetrics:
    """Create ExperimentMetrics from PortfolioManager state"""
    try:
        # Get current portfolio metrics
        current_metrics = await portfolio_manager.track_performance()
        
        # Get strategy weights
        market_data = await portfolio_manager._get_latest_market_data()
        weights = await portfolio_manager.get_strategy_weights(market_data.to_dict())
        
        return ExperimentMetrics(
            timestamp=datetime.now(),
            experiment_name=f"ensemble_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            strategy_weights={name: weight.weight for name, weight in weights.items()},
            portfolio_value=portfolio_manager.portfolio_value,
            total_pnl=current_metrics.total_pnl,
            sharpe_ratio=current_metrics.sharpe_ratio,
            max_drawdown=current_metrics.max_drawdown,
            win_rate=current_metrics.win_rate,
            correlation_matrix=current_metrics.correlation_matrix,
            individual_strategy_pnl=current_metrics.strategy_contributions,
            confidence_scores={name: weight.confidence for name, weight in weights.items()},
            market_conditions=market_data.to_dict(),
            trade_count=len(portfolio_manager.position_history),
            execution_time_ms=execution_time_ms
        )
        
    except Exception as e:
        logger.error(f"Failed to create experiment metrics: {e}")
        return None