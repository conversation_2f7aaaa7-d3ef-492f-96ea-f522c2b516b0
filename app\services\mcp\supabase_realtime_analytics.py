# app/services/mcp/supabase_realtime_analytics.py
"""
Real-time Analytics Dashboard for Strategy Ensemble System
Provides live streaming data, performance monitoring, and alerting capabilities.
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

@dataclass
class RealTimeMetrics:
    """Real-time performance metrics for the dashboard."""
    timestamp: datetime
    portfolio_value: float
    total_pnl: float
    hourly_pnl: float
    daily_pnl: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    active_positions: int
    cache_hit_rate: float
    avg_execution_time_ms: float
    strategy_weights: Dict[str, float]
    strategy_pnl: Dict[str, float]
    correlation_matrix: Dict[str, Dict[str, float]]
    alerts_count: int
    market_regime: str

@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    metric: str
    condition: str  # 'gt', 'lt', 'eq'
    threshold: float
    severity: str  # 'info', 'warning', 'critical'
    cooldown_minutes: int
    message_template: str

@dataclass
class DashboardWidget:
    """Dashboard widget configuration."""
    widget_id: str
    widget_type: str  # 'chart', 'metric', 'table', 'alert'
    title: str
    data_source: str
    refresh_interval_seconds: int
    config: Dict[str, Any]

class SupabaseRealTimeAnalytics:
    """
    Enhanced real-time analytics service for the strategy ensemble system.
    Provides live data streaming, performance monitoring, and dashboard capabilities.
    """
    
    def __init__(self, supabase_service: SupabaseService):
        """
        Initialize real-time analytics service.
        
        Args:
            supabase_service: Configured Supabase service instance
        """
        self.supabase = supabase_service
        self.is_running = False
        self.subscribers = {}
        self.alert_rules = {}
        self.last_alert_times = {}
        self.performance_buffer = deque(maxlen=1000)  # Keep last 1000 data points
        self.dashboard_config = {}
        
        # Real-time data streams
        self.live_metrics_stream = None
        self.trade_stream = None
        self.alert_stream = None
        
        # Performance tracking
        self.execution_times = deque(maxlen=100)
        self.cache_hits = deque(maxlen=100)
        self.trade_results = deque(maxlen=500)
        
        # Initialize alert rules
        self._setup_default_alert_rules()
        
        logger.info("Real-time analytics service initialized")
    
    def _setup_default_alert_rules(self):
        """Setup default alert rules for monitoring."""
        default_rules = [
            AlertRule(
                name="high_drawdown",
                metric="max_drawdown",
                condition="lt",
                threshold=-0.15,  # 15% drawdown
                severity="critical",
                cooldown_minutes=30,
                message_template="High drawdown alert: Portfolio down {value:.1%}"
            ),
            AlertRule(
                name="low_sharpe_ratio",
                metric="sharpe_ratio",
                condition="lt",
                threshold=0.5,
                severity="warning",
                cooldown_minutes=60,
                message_template="Low Sharpe ratio: {value:.2f}"
            ),
            AlertRule(
                name="poor_execution_performance",
                metric="avg_execution_time_ms",
                condition="gt",
                threshold=1000,  # 1 second
                severity="warning",
                cooldown_minutes=15,
                message_template="Slow execution: {value:.0f}ms average"
            ),
            AlertRule(
                name="low_cache_hit_rate",
                metric="cache_hit_rate",
                condition="lt",
                threshold=0.7,  # 70%
                severity="info",
                cooldown_minutes=30,
                message_template="Low cache efficiency: {value:.1%} hit rate"
            ),
            AlertRule(
                name="high_correlation_risk",
                metric="max_strategy_correlation",
                condition="gt",
                threshold=0.8,
                severity="warning",
                cooldown_minutes=45,
                message_template="High strategy correlation: {value:.2f}"
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.name] = rule
    
    async def start_real_time_monitoring(self):
        """Start all real-time monitoring streams."""
        if self.is_running:
            logger.warning("Real-time monitoring already running")
            return
        
        self.is_running = True
        logger.info("Starting real-time monitoring...")
        
        # Start all monitoring streams concurrently
        tasks = [
            asyncio.create_task(self._performance_monitoring_loop()),
            asyncio.create_task(self._alert_monitoring_loop()),
            asyncio.create_task(self._data_aggregation_loop()),
            asyncio.create_task(self._dashboard_update_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            logger.info("Real-time monitoring stopped")
        except Exception as e:
            logger.error(f"Real-time monitoring error: {e}")
        finally:
            self.is_running = False
    
    async def stop_real_time_monitoring(self):
        """Stop real-time monitoring."""
        self.is_running = False
        logger.info("Stopping real-time monitoring...")
    
    async def _performance_monitoring_loop(self):
        """Main performance monitoring loop."""
        while self.is_running:
            try:
                # Collect current performance metrics
                metrics = await self._collect_current_metrics()
                
                if metrics:
                    # Add to buffer
                    self.performance_buffer.append(metrics)
                    
                    # Store in Supabase
                    await self.supabase.store_portfolio_metrics(asdict(metrics))
                    
                    # Check alert rules
                    await self._check_alert_rules(metrics)
                    
                    # Notify subscribers
                    await self._notify_subscribers('performance_update', metrics)
                
                # Sleep before next iteration
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(10)  # Wait longer on error
    
    async def _alert_monitoring_loop(self):
        """Monitor and process alerts."""
        while self.is_running:
            try:
                # Check for new unacknowledged alerts
                alerts = await self.supabase.get_unacknowledged_alerts()
                
                if alerts:
                    # Notify subscribers about alerts
                    await self._notify_subscribers('new_alerts', alerts)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Alert monitoring error: {e}")
                await asyncio.sleep(15)
    
    async def _data_aggregation_loop(self):
        """Aggregate and calculate derived metrics."""
        while self.is_running:
            try:
                # Calculate rolling statistics
                if len(self.performance_buffer) > 10:
                    await self._calculate_rolling_statistics()
                
                # Update strategy correlation matrix
                await self._update_correlation_matrix()
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Data aggregation error: {e}")
                await asyncio.sleep(30)
    
    async def _dashboard_update_loop(self):
        """Update dashboard data streams."""
        while self.is_running:
            try:
                # Update dashboard widgets
                dashboard_data = await self._prepare_dashboard_data()
                
                # Notify dashboard subscribers
                await self._notify_subscribers('dashboard_update', dashboard_data)
                
                await asyncio.sleep(2)  # Update dashboard every 2 seconds
                
            except Exception as e:
                logger.error(f"Dashboard update error: {e}")
                await asyncio.sleep(5)
    
    async def _collect_current_metrics(self) -> Optional[RealTimeMetrics]:
        """Collect current performance metrics."""
        try:
            # Get recent trades for calculations
            recent_trades = await self.supabase.get_recent_trades(limit=100)
            
            if not recent_trades:
                return None
            
            # Calculate portfolio metrics
            total_pnl = sum(trade.get('pnl', 0) for trade in recent_trades)
            
            # Calculate hourly and daily PnL
            now = datetime.now()
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)
            
            hourly_trades = [t for t in recent_trades if datetime.fromisoformat(t.get('executed_at', '')) > hour_ago]
            daily_trades = [t for t in recent_trades if datetime.fromisoformat(t.get('executed_at', '')) > day_ago]
            
            hourly_pnl = sum(trade.get('pnl', 0) for trade in hourly_trades)
            daily_pnl = sum(trade.get('pnl', 0) for trade in daily_trades)
            
            # Calculate win rate
            winning_trades = len([t for t in recent_trades if t.get('pnl', 0) > 0])
            win_rate = winning_trades / len(recent_trades) if recent_trades else 0
            
            # Calculate Sharpe ratio (simplified)
            returns = [trade.get('return_pct', 0) for trade in recent_trades if trade.get('return_pct') is not None]
            sharpe_ratio = 0
            if len(returns) > 1:
                returns_array = np.array(returns)
                sharpe_ratio = np.mean(returns_array) / (np.std(returns_array) + 1e-8) * np.sqrt(252)
            
            # Calculate max drawdown
            cumulative_returns = np.cumsum([trade.get('return_pct', 0) for trade in recent_trades])
            if len(cumulative_returns) > 0:
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdown = cumulative_returns - running_max
                max_drawdown = np.min(drawdown) if len(drawdown) > 0 else 0
            else:
                max_drawdown = 0
            
            # Get strategy performance
            strategy_performance = await self.supabase.get_strategy_performance_comparison(days_back=1)
            strategy_pnl = {strategy: data.get('total_pnl', 0) for strategy, data in strategy_performance.items()}
            
            # Mock some real-time metrics (would come from actual systems)
            cache_hit_rate = np.mean(self.cache_hits) if self.cache_hits else 0.8
            avg_execution_time = np.mean(self.execution_times) if self.execution_times else 150
            
            return RealTimeMetrics(
                timestamp=datetime.now(),
                portfolio_value=100000 + total_pnl,  # Starting value + PnL
                total_pnl=total_pnl,
                hourly_pnl=hourly_pnl,
                daily_pnl=daily_pnl,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                active_positions=len(set(trade.get('symbol') for trade in recent_trades)),
                cache_hit_rate=cache_hit_rate,
                avg_execution_time_ms=avg_execution_time,
                strategy_weights={"GridStrategy": 0.33, "TechnicalAnalysisStrategy": 0.34, "TrendFollowingStrategy": 0.33},
                strategy_pnl=strategy_pnl,
                correlation_matrix={},  # Will be calculated separately
                alerts_count=len(await self.supabase.get_unacknowledged_alerts()),
                market_regime="normal"  # Would be determined by ML model
            )
            
        except Exception as e:
            logger.error(f"Failed to collect current metrics: {e}")
            return None
    
    async def _check_alert_rules(self, metrics: RealTimeMetrics):
        """Check current metrics against alert rules."""
        try:
            current_time = datetime.now()
            
            for rule_name, rule in self.alert_rules.items():
                # Check cooldown
                last_alert_time = self.last_alert_times.get(rule_name)
                if last_alert_time:
                    time_since_last = (current_time - last_alert_time).total_seconds() / 60
                    if time_since_last < rule.cooldown_minutes:
                        continue
                
                # Get metric value
                metric_value = getattr(metrics, rule.metric, None)
                
                # Special handling for correlation matrix metrics
                if rule.metric == "max_strategy_correlation" and metrics.correlation_matrix:
                    correlations = []
                    for strategy1, corr_data in metrics.correlation_matrix.items():
                        for strategy2, corr_value in corr_data.items():
                            if strategy1 != strategy2:
                                correlations.append(abs(corr_value))
                    metric_value = max(correlations) if correlations else 0
                
                if metric_value is None:
                    continue
                
                # Check condition
                triggered = False
                if rule.condition == "gt" and metric_value > rule.threshold:
                    triggered = True
                elif rule.condition == "lt" and metric_value < rule.threshold:
                    triggered = True
                elif rule.condition == "eq" and metric_value == rule.threshold:
                    triggered = True
                
                if triggered:
                    # Create alert
                    message = rule.message_template.format(value=metric_value)
                    
                    await self.supabase.store_alert(
                        alert_type=rule_name,
                        severity=rule.severity,
                        message=message,
                        metrics_snapshot=asdict(metrics)
                    )
                    
                    self.last_alert_times[rule_name] = current_time
                    logger.warning(f"Alert triggered: {rule_name} - {message}")
                    
        except Exception as e:
            logger.error(f"Failed to check alert rules: {e}")
    
    async def _calculate_rolling_statistics(self):
        """Calculate rolling statistics from performance buffer."""
        try:
            if len(self.performance_buffer) < 10:
                return
            
            # Extract metrics for rolling calculations
            total_pnls = [m.total_pnl for m in self.performance_buffer]
            sharpe_ratios = [m.sharpe_ratio for m in self.performance_buffer]
            execution_times = [m.avg_execution_time_ms for m in self.performance_buffer]
            
            # Calculate rolling averages
            rolling_stats = {
                'rolling_pnl_mean': np.mean(total_pnls[-20:]),  # 20-period rolling average
                'rolling_pnl_std': np.std(total_pnls[-20:]),
                'rolling_sharpe_mean': np.mean(sharpe_ratios[-20:]),
                'rolling_execution_time_mean': np.mean(execution_times[-20:]),
                'pnl_trend': 'up' if total_pnls[-1] > total_pnls[-10] else 'down'
            }
            
            # Store rolling statistics (could be stored in a separate table)
            logger.debug(f"Rolling statistics: {rolling_stats}")
            
        except Exception as e:
            logger.error(f"Failed to calculate rolling statistics: {e}")
    
    async def _update_correlation_matrix(self):
        """Update strategy correlation matrix."""
        try:
            # Get recent strategy performance data
            strategy_performance = await self.supabase.get_strategy_performance_comparison(days_back=7)
            
            # Calculate correlation matrix (simplified implementation)
            strategies = list(strategy_performance.keys())
            correlation_matrix = {}
            
            for strategy1 in strategies:
                correlation_matrix[strategy1] = {}
                for strategy2 in strategies:
                    if strategy1 == strategy2:
                        correlation_matrix[strategy1][strategy2] = 1.0
                    else:
                        # Mock correlation calculation (would use actual returns data)
                        correlation_matrix[strategy1][strategy2] = np.random.uniform(0.1, 0.7)
            
            # Update latest metrics in buffer
            if self.performance_buffer:
                self.performance_buffer[-1].correlation_matrix = correlation_matrix
                
        except Exception as e:
            logger.error(f"Failed to update correlation matrix: {e}")
    
    async def _prepare_dashboard_data(self) -> Dict[str, Any]:
        """Prepare data for dashboard updates."""
        try:
            if not self.performance_buffer:
                return {}
            
            latest_metrics = self.performance_buffer[-1]
            
            # Prepare time series data
            time_series_data = {
                'timestamps': [m.timestamp.isoformat() for m in list(self.performance_buffer)[-50:]],
                'portfolio_values': [m.portfolio_value for m in list(self.performance_buffer)[-50:]],
                'pnl_values': [m.total_pnl for m in list(self.performance_buffer)[-50:]],
                'sharpe_ratios': [m.sharpe_ratio for m in list(self.performance_buffer)[-50:]],
                'execution_times': [m.avg_execution_time_ms for m in list(self.performance_buffer)[-50:]]
            }
            
            # Prepare current metrics
            current_metrics = {
                'portfolio_value': latest_metrics.portfolio_value,
                'total_pnl': latest_metrics.total_pnl,
                'hourly_pnl': latest_metrics.hourly_pnl,
                'daily_pnl': latest_metrics.daily_pnl,
                'win_rate': latest_metrics.win_rate,
                'sharpe_ratio': latest_metrics.sharpe_ratio,
                'max_drawdown': latest_metrics.max_drawdown,
                'cache_hit_rate': latest_metrics.cache_hit_rate,
                'avg_execution_time_ms': latest_metrics.avg_execution_time_ms,
                'active_positions': latest_metrics.active_positions,
                'alerts_count': latest_metrics.alerts_count
            }
            
            # Prepare strategy breakdown
            strategy_data = {
                'weights': latest_metrics.strategy_weights,
                'pnl': latest_metrics.strategy_pnl,
                'correlation_matrix': latest_metrics.correlation_matrix
            }
            
            return {
                'timestamp': latest_metrics.timestamp.isoformat(),
                'time_series': time_series_data,
                'current_metrics': current_metrics,
                'strategy_data': strategy_data,
                'market_regime': latest_metrics.market_regime
            }
            
        except Exception as e:
            logger.error(f"Failed to prepare dashboard data: {e}")
            return {}
    
    async def _notify_subscribers(self, event_type: str, data: Any):
        """Notify all subscribers of an event."""
        try:
            if event_type in self.subscribers:
                for subscriber_id, callback in self.subscribers[event_type].items():
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(data)
                        else:
                            callback(data)
                    except Exception as e:
                        logger.error(f"Failed to notify subscriber {subscriber_id}: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to notify subscribers: {e}")
    
    def subscribe_to_updates(self, event_type: str, subscriber_id: str, callback: Callable):
        """
        Subscribe to real-time updates.
        
        Args:
            event_type: Type of event to subscribe to
            subscriber_id: Unique identifier for the subscriber
            callback: Function to call when event occurs
        """
        if event_type not in self.subscribers:
            self.subscribers[event_type] = {}
        
        self.subscribers[event_type][subscriber_id] = callback
        logger.info(f"Subscriber {subscriber_id} registered for {event_type}")
    
    def unsubscribe_from_updates(self, event_type: str, subscriber_id: str):
        """
        Unsubscribe from real-time updates.
        
        Args:
            event_type: Type of event to unsubscribe from
            subscriber_id: Unique identifier for the subscriber
        """
        if event_type in self.subscribers and subscriber_id in self.subscribers[event_type]:
            del self.subscribers[event_type][subscriber_id]
            logger.info(f"Subscriber {subscriber_id} unsubscribed from {event_type}")
    
    async def get_live_metrics_stream(self) -> AsyncGenerator[RealTimeMetrics, None]:
        """
        Get a live stream of metrics.
        
        Yields:
            RealTimeMetrics: Real-time performance metrics
        """
        while self.is_running:
            if self.performance_buffer:
                yield self.performance_buffer[-1]
            await asyncio.sleep(1)
    
    async def add_custom_alert_rule(self, rule: AlertRule):
        """
        Add a custom alert rule.
        
        Args:
            rule: Alert rule configuration
        """
        self.alert_rules[rule.name] = rule
        logger.info(f"Added custom alert rule: {rule.name}")
    
    async def remove_alert_rule(self, rule_name: str):
        """
        Remove an alert rule.
        
        Args:
            rule_name: Name of the rule to remove
        """
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
    
    async def get_dashboard_summary(self) -> Dict[str, Any]:
        """
        Get a summary for the dashboard.
        
        Returns:
            Dashboard summary data
        """
        try:
            if not self.performance_buffer:
                return {"status": "no_data"}
            
            latest_metrics = self.performance_buffer[-1]
            
            # Get alerts summary (with timeout for robustness)
            try:
                unack_alerts = await asyncio.wait_for(
                    self.supabase.get_unacknowledged_alerts(), 
                    timeout=2.0
                )
                critical_alerts = len([a for a in unack_alerts if a.get('severity') == 'critical'])
                warning_alerts = len([a for a in unack_alerts if a.get('severity') == 'warning'])
            except (asyncio.TimeoutError, Exception):
                # Fallback to mock data if database call fails/hangs
                critical_alerts = 0
                warning_alerts = 0
            
            # Calculate performance trend
            if len(self.performance_buffer) >= 10:
                recent_pnl = [m.total_pnl for m in list(self.performance_buffer)[-10:]]
                trend = "up" if recent_pnl[-1] > recent_pnl[0] else "down"
            else:
                trend = "neutral"
            
            return {
                "status": "active",
                "last_update": latest_metrics.timestamp.isoformat(),
                "portfolio_value": latest_metrics.portfolio_value,
                "total_pnl": latest_metrics.total_pnl,
                "daily_pnl": latest_metrics.daily_pnl,
                "win_rate": latest_metrics.win_rate,
                "sharpe_ratio": latest_metrics.sharpe_ratio,
                "execution_performance": {
                    "avg_time_ms": latest_metrics.avg_execution_time_ms,
                    "cache_hit_rate": latest_metrics.cache_hit_rate
                },
                "alerts": {
                    "critical": critical_alerts,
                    "warning": warning_alerts,
                    "total": latest_metrics.alerts_count
                },
                "trend": trend,
                "market_regime": latest_metrics.market_regime
            }
            
        except Exception as e:
            logger.error(f"Failed to get dashboard summary: {e}")
            return {"status": "error", "message": str(e)}

# Utility functions for dashboard integration

async def create_real_time_analytics(supabase_url: str, supabase_key: str) -> SupabaseRealTimeAnalytics:
    """
    Create and configure real-time analytics service.
    
    Args:
        supabase_url: Supabase project URL
        supabase_key: Supabase API key
        
    Returns:
        Configured real-time analytics service
    """
    supabase_service = SupabaseService(supabase_url, supabase_key)
    analytics = SupabaseRealTimeAnalytics(supabase_service)
    
    # Test connection
    connection_ok = await supabase_service.test_connection()
    if connection_ok:
        logger.info("✅ Real-time analytics service ready")
    else:
        logger.warning("⚠️ Real-time analytics using mock data")
    
    return analytics

async def start_analytics_monitoring(analytics: SupabaseRealTimeAnalytics):
    """
    Start analytics monitoring in the background.
    
    Args:
        analytics: Real-time analytics service
    """
    asyncio.create_task(analytics.start_real_time_monitoring())
    logger.info("🚀 Real-time analytics monitoring started")