"""Account methods for Binance API client."""
from typing import Dict, List, Optional, Any
import logging
from collections import OrderedDict

logger = logging.getLogger(__name__)

class AccountMixin:
    """Account methods for Binance API client."""

    def _make_request(self, *args, **kwargs):
        """This method should be implemented by the concrete client class.

        In tests, this method will be mocked.
        """
        raise NotImplementedError("_make_request must be implemented by the concrete client class")

    def get_account_info(self) -> Dict[str, Any]:
        """Get account information.

        Returns:
            Dict[str, Any]: Account information

        Note:
            Attempts to use v3 endpoint first, then falls back to v2 and v1 if needed.
            Binance periodically updates their API versions.
        """
        # Try v3 endpoint first (newest version)
        try:
            response = self._make_request('GET', '/fapi/v3/account', signed=True)
            if isinstance(response, dict):
                return response
        except Exception as e:
            logger.warning(f"Failed to get account info from v3 endpoint: {e}. Trying v2...")

        # Fall back to v2 endpoint
        try:
            response = self._make_request('GET', '/fapi/v2/account', signed=True)
            if isinstance(response, dict):
                return response
        except Exception as e:
            logger.warning(f"Failed to get account info from v2 endpoint: {e}. Trying v1...")

        # Fall back to v1 endpoint as last resort
        try:
            response = self._make_request('GET', '/fapi/v1/account', signed=True)
            if isinstance(response, dict):
                return response
            else:
                raise TypeError("Expected dictionary response from account info endpoint")
        except Exception as e:
            logger.error(f"All account info endpoint attempts failed: {e}")
            raise Exception(f"Failed to get account information: {e}")

    def get_position_risk(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get position risk.

        Args:
            symbol (Optional[str]): Symbol to get position risk for. Defaults to None for all positions.

        Returns:
            List[Dict[str, Any]]: Position risk information

        Note:
            Attempts to use v3 endpoint first, then falls back to v2 if needed.
        """
        params = {}
        if symbol:
            params["symbol"] = symbol

        # Try v3 endpoint first
        try:
            response = self._make_request('GET', '/fapi/v3/positionRisk', signed=True, params=params)
            if isinstance(response, list):
                return response
        except Exception as e:
            logger.warning(f"Failed to get position risk from v3 endpoint: {e}. Trying v2...")

        # Fall back to v2 endpoint
        try:
            response = self._make_request('GET', '/fapi/v2/positionRisk', signed=True, params=params)
            if isinstance(response, list):
                return response
            else:
                logger.warning(f"Unexpected response type from position risk endpoint: {type(response)}")
                if isinstance(response, dict) and "positions" in response:
                    # Some API versions return positions within a dict
                    return response["positions"]
                raise TypeError("Expected list response from position risk endpoint")
        except Exception as e:
            logger.error(f"All position risk endpoint attempts failed: {e}")
            raise Exception(f"Failed to get position risk: {e}")

    def get_balance(self) -> List[Dict[str, Any]]:
        """Get account balance.

        Returns:
            List[Dict[str, Any]]: Account balance information
        """
        try:
            account_info = self.get_account_info()
            if "assets" in account_info:
                return account_info["assets"]
            elif "balances" in account_info:
                return account_info["balances"]
            else:
                logger.warning("Unexpected account info format. Could not find assets or balances.")
                return []
        except Exception as e:
            logger.error(f"Failed to get balance: {e}")
            raise Exception(f"Failed to get balance: {e}")

    def get_income_history(self, symbol: Optional[str] = None,
                          income_type: Optional[str] = None,
                          start_time: Optional[int] = None,
                          end_time: Optional[int] = None,
                          limit: int = 500) -> List[Dict[str, Any]]:
        """Get income history.

        Args:
            symbol (Optional[str], optional): Symbol to get income for. Defaults to None.
            income_type (Optional[str], optional): Income type. Defaults to None.
            start_time (Optional[int], optional): Start time in ms. Defaults to None.
            end_time (Optional[int], optional): End time in ms. Defaults to None.
            limit (int, optional): Limit of results. Defaults to 500.

        Returns:
            List[Dict[str, Any]]: Income history
        """
        params = OrderedDict()
        if symbol:
            params['symbol'] = symbol
        if income_type:
            params['incomeType'] = income_type
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
        params['limit'] = limit

        return self._make_request('GET', '/fapi/v1/income', signed=True, params=params)

    def get_leverage_brackets(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get leverage brackets.

        Args:
            symbol (Optional[str], optional): Symbol to get leverage brackets for. Defaults to None.

        Returns:
            List[Dict[str, Any]]: Leverage brackets
        """
        params = OrderedDict()
        if symbol:
            params['symbol'] = symbol

        return self._make_request('GET', '/fapi/v1/leverageBracket', signed=True, params=params)

    def change_leverage(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """Change leverage.

        Args:
            symbol (str): Symbol to change leverage for
            leverage (int): Leverage value

        Returns:
            Dict[str, Any]: Response
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['leverage'] = leverage

        return self._make_request('POST', '/fapi/v1/leverage', signed=True, params=params)

    def change_margin_type(self, symbol: str, margin_type: str) -> Dict[str, Any]:
        """Change margin type.

        Args:
            symbol (str): Symbol to change margin type for
            margin_type (str): Margin type (ISOLATED or CROSSED)

        Returns:
            Dict[str, Any]: Response
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['marginType'] = margin_type

        return self._make_request('POST', '/fapi/v1/marginType', signed=True, params=params)

    def get_position_mode(self) -> Dict[str, Any]:
        """Get position mode.

        Returns:
            Dict[str, Any]: Position mode information
        """
        return self._make_request('GET', '/fapi/v1/positionSide/dual', signed=True)

    def change_position_mode(self, dual_side_position: bool) -> Dict[str, Any]:
        """Change position mode.

        Args:
            dual_side_position (bool): Enable or disable dual side position

        Returns:
            Dict[str, Any]: Response
        """
        params = OrderedDict()
        params['dualSidePosition'] = 'true' if dual_side_position else 'false'

        return self._make_request('POST', '/fapi/v1/positionSide/dual', signed=True, params=params)
