"""
Strategy Selector for the Multi-Strategy Crypto Auto Trader.

This module analyzes current market conditions to select the optimal trading
strategy among Grid Trading, Technical Analysis, and Trend-Following.
"""

import pandas as pd
from typing import Dict, Optional, Any
import logging
import asyncio
from datetime import datetime
from decimal import Decimal

from app.strategies.grid_strategy import GridStrategy
from app.strategies.technical_analysis_strategy import TechnicalAnalysisStrategy
from app.strategies.trend_following_strategy import TrendFollowingStrategy
from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient
from app.services.execution.service import ExecutionService
from app.services.execution.models import OrderSide, OrderStatus, Order
from app.services.execution.trade_executor import execute_trade
from app.services.execution.order_management import OrderManager

# Import our new modules
from app.strategies.market_analysis import MarketAnalyzer
from app.strategies.strategy_scoring import StrategyScorer
from app.strategies.execution_handler import ExecutionHandler
from app.strategies.utils import (
    process_klines_data,
    calculate_next_candle_time
)

# Import ML weight optimizer
from app.ml.models.weight_optimizer import MLWeightOptimizer

class StrategySelector:
    """Strategy selection engine.

    This class analyzes current market conditions to select the optimal
    trading strategy based on a scoring system. It can be started and stopped
    asynchronously and connects to Binance.
    """

    def __init__(self,
                 settings: Settings,
                 execution_service: ExecutionService,
                 exchange_client: BinanceExchangeClient,
                 ml_weight_optimizer: Optional[MLWeightOptimizer] = None,
                 params: Optional[Dict[str, Any]] = None):
        """Initialize the Strategy Selector.

        Args:
            settings: Application settings
            execution_service: Service for executing trades
            exchange_client: Client for interacting with the exchange
            ml_weight_optimizer: Optional ML weight optimizer for strategy weights
            params: Optional parameters to override defaults
        """
        self.settings = settings
        self.execution_service = execution_service
        self.exchange_client = exchange_client
        self.ml_weight_optimizer = ml_weight_optimizer

        # Set up logging
        self.logger = logging.getLogger(__name__)

        # Initialize parameters with defaults, then override with provided params
        self.params = {
            'symbol': 'BTCUSDT',
            'timeframe': '15m',
            'risk_per_trade': 1.0,  # % of account to risk per trade
            'max_position_size': 20.0,  # % of account
            'strategy_switch_cooldown': 4,  # Number of candles to wait before switching
            'min_score_threshold': 0.4,  # Minimum score to select a strategy
        }

        if params:
            self.params.update(params)

        # Extract commonly used parameters
        self.symbol = self.params['symbol']
        self.timeframe = self.params['timeframe']

        # Initialize state variables
        self._is_running = False
        self._stop_requested = False
        self.current_strategy = None
        self.current_strategy_instance = None
        self.strategy_switch_count = 0
        self.last_market_conditions = {}
        self.last_strategy_scores = {}

        # Initialize execution handler
        self.execution_handler = ExecutionHandler(execution_service)

        # Initialize strategy instances
        self.strategies = {
            'grid': GridStrategy(
                symbol=self.symbol,
                timeframe=self.timeframe,
                settings=settings
            ),
            'technical_analysis': TechnicalAnalysisStrategy(
                symbol=self.symbol,
                timeframe=self.timeframe,
                settings=settings
            ),
            'trend_following': TrendFollowingStrategy(
                symbol=self.symbol,
                timeframe=self.timeframe,
                settings=settings
            )
        }

        self.logger.info(f"StrategySelector initialized for {self.symbol}")

    def is_running(self) -> bool:
        """Check if the trading loop is currently active."""
        return self._is_running

    async def stop(self):
        """Signal the trading loop to stop."""
        self.logger.info("Stop requested for StrategySelector loop.")
        self._stop_requested = True

        # Stop current strategy if running
        if self.current_strategy_instance and hasattr(self.current_strategy_instance, 'stop'):
            await self.current_strategy_instance.stop()

        await asyncio.sleep(1)

    async def start(self):
        """Start the main trading loop."""
        if self._is_running:
            self.logger.warning("Trading loop is already running.")
            return

        self.logger.debug(f"Starting StrategySelector loop for {self.symbol}...")
        self._is_running = True
        self._stop_requested = False

        # Pre-load ML model if optimization enabled
        if self.settings.ml_weight_optimization_enabled and self.ml_weight_optimizer:
            try:
                self.logger.info("Pre-loading ML model for weight optimization")
                loaded = await self.ml_weight_optimizer.load_model()
                if loaded:
                    self.logger.info("ML model loaded successfully")
                else:
                    self.logger.warning("ML model failed to load")
            except Exception as e:
                self.logger.error(f"Error pre-loading ML model: {e}")

        # --- Fetch Initial Balance ---
        try:
            self.logger.info("Fetching initial futures account balance via exchange_client...")
            balance_info = await self.exchange_client.get_account_balance()
            usdt_balance = balance_info.get('USDT')
            if usdt_balance is not None:
                self.logger.info(f"Current USDT Futures Balance: {usdt_balance}")
            else:
                self.logger.warning("Could not find USDT balance in futures account.")
        except Exception as e:
            self.logger.error(f"Failed to fetch initial balance: {e}", exc_info=True)

        # --- Main Loop ---
        try:
            while not self._stop_requested:
                self.logger.debug(f"StrategySelector loop iteration for {self.symbol}...")

                # --- Fetch Live Data ---
                try:
                    klines_list = await self.exchange_client.get_historical_klines(
                        symbol=self.symbol,
                        interval=self.timeframe,
                        limit=self.settings.kline_limit
                    )

                    # Process klines data
                    df = self.process_klines_data(klines_list)

                    if df.empty:
                        self.logger.warning(f"Empty klines data for {self.symbol}")
                        await asyncio.sleep(10)
                        continue

                    # Get current price
                    current_price = df['close'].iloc[-1]
                    self.logger.debug(f"Current {self.symbol} price: {current_price}")

                    # --- Analyze Market Conditions ---
                    market_conditions = MarketAnalyzer.analyze_market_conditions(df)
                    self.last_market_conditions = market_conditions

                    # Log market conditions
                    self.logger.debug(f"Market conditions: Volatility={market_conditions['volatility']:.2f}, " +
                                      f"Trend={market_conditions['trend']:.2f}, " +
                                      f"Range={market_conditions['range_bound']:.2f}, " +
                                      f"Volume={market_conditions['volume']:.2f}")

                    # --- Select Best Strategy ---
                    strategy_weights = None

                    # Use ML-optimized weights if enabled
                    if self.settings.ml_weight_optimization_enabled and self.ml_weight_optimizer:
                        try:
                            # Get optimized weights from ML model
                            self.logger.debug("Getting ML-optimized weights")
                            strategy_weights = await self.ml_weight_optimizer.get_optimized_weights(market_conditions)
                        except Exception as e:
                            self.logger.error(f"Error getting ML-optimized weights: {e}")

                    # Determine best strategy
                    if strategy_weights and all(isinstance(v, float) for v in strategy_weights.values()):
                        # ML-driven selection: pick highest ML weight
                        best_strategy = max(strategy_weights, key=strategy_weights.get)
                        strategy_scores = strategy_weights
                    else:
                        best_strategy, strategy_scores = StrategyScorer.select_best_strategy(
                            market_conditions,
                            min_score_threshold=self.params['min_score_threshold'],
                            strategy_weights=strategy_weights
                        )
                    self.last_strategy_scores = strategy_scores

                    # Log strategy scores
                    self.logger.debug(f"Strategy scores: Grid={strategy_scores['grid']:.2f}, " +
                                      f"TA={strategy_scores['technical_analysis']:.2f}, " +
                                      f"Trend={strategy_scores['trend_following']:.2f}")
                    self.logger.info(f"Selected strategy: {best_strategy}")

                    # --- Strategy Switching Logic ---
                    if best_strategy != 'none':
                        if self.current_strategy != best_strategy:
                            # Switch immediately on first run or after cooldown
                            if self.current_strategy is None or self.strategy_switch_count >= self.params['strategy_switch_cooldown']:
                                # Switch strategy
                                await self._switch_strategy(best_strategy)
                                self.strategy_switch_count = 0
                            else:
                                self.strategy_switch_count += 1
                                self.logger.info(f"Strategy switch cooldown: {self.strategy_switch_count}/{self.params['strategy_switch_cooldown']}")
                        else:
                            # Reset cooldown if same strategy selected
                            self.strategy_switch_count = 0

                    # --- Execute Current Strategy ---
                    if self.current_strategy_instance:
                        # Update strategy parameters based on market conditions
                        strategy_params = StrategyScorer.get_strategy_parameters(
                            self.current_strategy,
                            market_conditions,
                            self.current_strategy_instance.params
                        )
                        self.current_strategy_instance.update_params(strategy_params)

                        # Execute strategy
                        await self.current_strategy_instance.execute(df)

                except Exception as e:
                    self.logger.error(f"Error in StrategySelector loop: {e}", exc_info=True)

                # --- Wait for next candle ---
                next_candle_time = calculate_next_candle_time(self.timeframe)
                wait_seconds = (next_candle_time - datetime.now()).total_seconds()
                wait_seconds = max(10, min(wait_seconds, 60))  # Between 10s and 1min

                self.logger.debug(f"Waiting {wait_seconds:.0f} seconds for next candle...")
                await asyncio.sleep(wait_seconds)

        except asyncio.CancelledError:
            self.logger.info("StrategySelector loop cancelled.")
        except Exception as e:
            self.logger.error(f"Unexpected error in StrategySelector loop: {e}", exc_info=True)
        finally:
            # Clean up
            if self.current_strategy_instance:
                await self.current_strategy_instance.stop()

            self._is_running = False
            self.logger.info("StrategySelector loop stopped.")

    async def _switch_strategy(self, new_strategy: str):
        """Switch to a new trading strategy.

        Args:
            new_strategy: Name of the new strategy to switch to
        """
        try:
            self.logger.info(f"Switching strategy from {self.current_strategy} to {new_strategy}")

            # Stop current strategy if running
            if self.current_strategy_instance:
                self.logger.info(f"Stopping current strategy: {self.current_strategy}")
                await self.current_strategy_instance.stop()

            # Cancel all open orders
            await self.execution_handler.cancel_all_orders(self.symbol)

            # Close any open positions if switching strategies
            if self.current_strategy and self.current_strategy != new_strategy:
                self.logger.info(f"Closing positions for strategy switch")
                await self.execution_handler.close_position(self.symbol)

            # Set new strategy
            self.current_strategy = new_strategy
            self.current_strategy_instance = self.strategies.get(new_strategy)

            # Start new strategy
            if self.current_strategy_instance:
                self.logger.info(f"Starting new strategy: {new_strategy}")
                await self.current_strategy_instance.start()

        except Exception as e:
            self.logger.error(f"Error switching strategy: {e}", exc_info=True)

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the strategy selector.

        Returns:
            Dict[str, Any]: Status information
        """
        return {
            'is_running': self._is_running,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'current_strategy': self.current_strategy,
            'market_conditions': self.last_market_conditions,
            'strategy_scores': self.last_strategy_scores,
            'strategy_switch_count': self.strategy_switch_count,
        }

    def process_klines_data(self, klines):
        """Process klines data into a pandas DataFrame."""
        if not klines:
            return pd.DataFrame()

        # Define column names for the first 6 columns
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

        # Process only the first 6 columns from the klines data
        processed_klines = [kline[:6] for kline in klines]

        # Create DataFrame
        df = pd.DataFrame(processed_klines, columns=columns)

        # Convert timestamp to datetime and other columns to numeric
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in columns[1:]:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        
        return df



























