#!/usr/bin/env python3
"""
FastAPI Routes for Binance Futures Testnet Integration
Provides HTTP API endpoints for Binance account management and real-time data.

Features:
- Account information and balances
- Open positions with PnL tracking
- Order management (get, cancel)
- Trade history and transaction log
- Real-time market data
- Risk metrics and portfolio analysis

Created: June 16, 2025
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import logging
import asyncio
from decimal import Decimal

from app.services.exchange.binance_client import BinanceExchangeClient
from app.config.settings import Settings

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/binance", tags=["Binance Futures"])

# Pydantic models for API responses
class BinanceAccount(BaseModel):
    balance: float = Field(..., description="Total account balance in USDT")
    equity: float = Field(..., description="Account equity in USDT")
    margin_used: float = Field(..., description="Used margin in USDT")
    margin_available: float = Field(..., description="Available margin in USDT")
    pnl_unrealized: float = Field(..., description="Unrealized PnL in USDT")
    pnl_realized_today: float = Field(..., description="Today's realized PnL in USDT")
    margin_ratio: float = Field(..., description="Margin ratio as percentage")
    max_withdraw_amount: float = Field(..., description="Maximum withdrawable amount")

class Position(BaseModel):
    symbol: str = Field(..., description="Trading symbol")
    size: float = Field(..., description="Position size")
    entry_price: float = Field(..., description="Average entry price")
    current_price: float = Field(..., description="Current market price")
    pnl: float = Field(..., description="Unrealized PnL")
    pnl_percentage: float = Field(..., description="PnL percentage")
    side: str = Field(..., description="Position side (LONG/SHORT)")
    liquidation_price: float = Field(..., description="Liquidation price")
    margin: float = Field(..., description="Position margin")
    strategy: Optional[str] = Field(None, description="Strategy attribution")

class Order(BaseModel):
    order_id: str = Field(..., description="Order ID")
    symbol: str = Field(..., description="Trading symbol")
    side: str = Field(..., description="Order side (BUY/SELL)")
    type: str = Field(..., description="Order type")
    amount: float = Field(..., description="Order amount")
    filled: float = Field(..., description="Filled amount")
    remaining: float = Field(..., description="Remaining amount")
    price: Optional[float] = Field(None, description="Order price")
    average_price: Optional[float] = Field(None, description="Average fill price")
    status: str = Field(..., description="Order status")
    timestamp: datetime = Field(..., description="Order creation time")
    last_update: datetime = Field(..., description="Last update time")
    fees: float = Field(..., description="Trading fees")

class Trade(BaseModel):
    id: str = Field(..., description="Trade ID")
    order_id: str = Field(..., description="Related order ID")
    symbol: str = Field(..., description="Trading symbol")
    side: str = Field(..., description="Trade side")
    amount: float = Field(..., description="Trade amount")
    price: float = Field(..., description="Trade price")
    cost: float = Field(..., description="Trade cost")
    fee: float = Field(..., description="Trading fee")
    fee_currency: str = Field(..., description="Fee currency")
    timestamp: datetime = Field(..., description="Trade timestamp")
    realized_pnl: Optional[float] = Field(None, description="Realized PnL")

class RiskMetrics(BaseModel):
    portfolio_heat: float = Field(..., description="Portfolio heat percentage")
    position_concentration: Dict[str, float] = Field(..., description="Position concentration by symbol")
    margin_utilization: float = Field(..., description="Margin utilization percentage")
    var_1d: float = Field(..., description="1-day Value at Risk")
    var_7d: float = Field(..., description="7-day Value at Risk")
    correlation_risk: float = Field(..., description="Portfolio correlation risk")

class ClosePositionRequest(BaseModel):
    symbol: str = Field(..., description="Symbol to close position for")
    percentage: float = Field(default=100.0, ge=0, le=100, description="Percentage of position to close")

class CancelOrderRequest(BaseModel):
    symbol: str = Field(..., description="Trading symbol")
    order_id: str = Field(..., description="Order ID to cancel")

class PositionCloseResponse(BaseModel):
    success: bool
    order_id: Optional[str] = None
    message: str
    remaining_position: Optional[float] = None

class OrderCancelResponse(BaseModel):
    success: bool
    message: str
    cancelled_order: Optional[Order] = None

# Global Binance client instance
_binance_client: Optional[BinanceExchangeClient] = None

async def get_binance_client() -> BinanceExchangeClient:
    """Get or create Binance client instance"""
    global _binance_client
    
    if _binance_client is None:
        try:
            settings = Settings()
            _binance_client = BinanceExchangeClient(settings)
            await _binance_client._initialize_client()
            logger.info("Binance client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Binance client: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize Binance client: {e}"
            )
    
    return _binance_client

@router.get("/health")
async def health_check():
    """Health check for Binance API connection"""
    try:
        client = await get_binance_client()
        # Test connection with a simple ping
        if client.client:
            await client.client.ping()
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "testnet": client.settings.use_testnet,
                "connection": "active"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Binance client not properly initialized"
            )
    except Exception as e:
        logger.error(f"Binance health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Binance service unavailable: {e}"
        )

@router.get("/account", response_model=BinanceAccount)
async def get_account_info(client: BinanceExchangeClient = Depends(get_binance_client)):
    """Get comprehensive account information and balances"""
    try:
        # Get account information
        account = await client.client.futures_account()
        
        # Extract key metrics
        total_wallet_balance = float(account.get('totalWalletBalance', 0))
        total_unrealized_pnl = float(account.get('totalUnrealizedProfit', 0))
        total_margin_balance = float(account.get('totalMarginBalance', 0))
        available_balance = float(account.get('availableBalance', 0))
        total_position_initial_margin = float(account.get('totalPositionInitialMargin', 0))
        max_withdraw_amount = float(account.get('maxWithdrawAmount', 0))
        
        # Calculate derived metrics
        equity = total_margin_balance
        margin_ratio = (total_position_initial_margin / max(total_margin_balance, 1)) * 100
        
        # Get today's realized PnL (approximation)
        # In a production system, you'd track this more precisely
        pnl_realized_today = 0.0  # Placeholder - would need historical data
        
        return BinanceAccount(
            balance=total_wallet_balance,
            equity=equity,
            margin_used=total_position_initial_margin,
            margin_available=available_balance,
            pnl_unrealized=total_unrealized_pnl,
            pnl_realized_today=pnl_realized_today,
            margin_ratio=margin_ratio,
            max_withdraw_amount=max_withdraw_amount
        )
        
    except Exception as e:
        logger.error(f"Failed to get account info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get account info: {e}"
        )

@router.get("/positions", response_model=List[Position])
async def get_positions(client: BinanceExchangeClient = Depends(get_binance_client)):
    """Get current open positions with PnL information"""
    try:
        positions = await client.get_open_positions()
        
        position_list = []
        for pos in positions:
            if float(pos.get('positionAmt', 0)) != 0:  # Only non-zero positions
                symbol = pos.get('symbol', '')
                position_amt = float(pos.get('positionAmt', 0))
                entry_price = float(pos.get('entryPrice', 0))
                unrealized_pnl = float(pos.get('unRealizedProfit', 0))
                percentage = float(pos.get('percentage', 0))
                
                # Get current market price
                try:
                    ticker = await client.get_ticker(symbol)
                    current_price = float(ticker.get('price', entry_price))
                except Exception:
                    current_price = entry_price  # Fallback
                
                # Determine position side
                side = "LONG" if position_amt > 0 else "SHORT"
                
                # Calculate liquidation price
                liquidation_price = float(pos.get('liquidationPrice', 0))
                
                # Calculate position margin
                position_margin = float(pos.get('initialMargin', 0))
                
                position_list.append(Position(
                    symbol=symbol,
                    size=abs(position_amt),
                    entry_price=entry_price,
                    current_price=current_price,
                    pnl=unrealized_pnl,
                    pnl_percentage=percentage,
                    side=side,
                    liquidation_price=liquidation_price,
                    margin=position_margin,
                    strategy=None  # Could be enhanced with strategy attribution
                ))
        
        return position_list
        
    except Exception as e:
        logger.error(f"Failed to get positions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get positions: {e}"
        )

@router.get("/orders", response_model=List[Order])
async def get_open_orders(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    client: BinanceExchangeClient = Depends(get_binance_client)
):
    """Get open orders with optional symbol filtering"""
    try:
        orders = await client.get_open_orders(symbol=symbol)
        
        order_list = []
        for order in orders:
            order_list.append(Order(
                order_id=str(order.get('orderId', '')),
                symbol=order.get('symbol', ''),
                side=order.get('side', ''),
                type=order.get('type', ''),
                amount=float(order.get('origQty', 0)),
                filled=float(order.get('executedQty', 0)),
                remaining=float(order.get('origQty', 0)) - float(order.get('executedQty', 0)),
                price=float(order.get('price', 0)) if order.get('price') else None,
                average_price=float(order.get('avgPrice', 0)) if order.get('avgPrice') else None,
                status=order.get('status', ''),
                timestamp=datetime.fromtimestamp(int(order.get('time', 0)) / 1000),
                last_update=datetime.fromtimestamp(int(order.get('updateTime', 0)) / 1000),
                fees=0.0  # Would need separate call to get fee info
            ))
        
        return order_list
        
    except Exception as e:
        logger.error(f"Failed to get open orders: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get open orders: {e}"
        )

@router.get("/trades", response_model=List[Trade])
async def get_trades(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    limit: int = Query(100, ge=1, le=1000, description="Number of trades to return"),
    start_time: Optional[datetime] = Query(None, description="Start time for trade history"),
    end_time: Optional[datetime] = Query(None, description="End time for trade history"),
    client: BinanceExchangeClient = Depends(get_binance_client)
):
    """Get trade history with filtering options"""
    try:
        # Convert datetime to timestamp if provided
        start_ts = int(start_time.timestamp() * 1000) if start_time else None
        end_ts = int(end_time.timestamp() * 1000) if end_time else None
        
        if symbol:
            # Get trades for specific symbol
            orders = await client.get_all_orders(
                symbol=symbol,
                start_time=start_ts,
                end_time=end_ts,
                limit=limit
            )
        else:
            # For all symbols, we need to get recent orders from account
            # This is a simplified approach - in production you'd want better filtering
            account = await client.client.futures_account()
            positions = await client.get_open_positions()
            
            # Get unique symbols from positions to query their order history
            symbols = list(set([pos.get('symbol') for pos in positions if pos.get('symbol')]))
            orders = []
            
            # Limit symbols to avoid too many API calls
            for sym in symbols[:10]:  # Limit to first 10 symbols
                try:
                    sym_orders = await client.get_all_orders(
                        symbol=sym,
                        start_time=start_ts,
                        end_time=end_ts,
                        limit=min(limit // len(symbols), 50)
                    )
                    orders.extend(sym_orders)
                except Exception as e:
                    logger.warning(f"Failed to get orders for {sym}: {e}")
                    continue
        
        # Filter for filled orders only
        filled_orders = [order for order in orders if order.get('status') == 'FILLED']
        
        trade_list = []
        for order in filled_orders:
            # Calculate realized PnL (simplified)
            realized_pnl = None
            if order.get('reduceOnly'):
                # This is a position closing trade, calculate PnL
                # In production, you'd use more sophisticated PnL calculation
                pass
            
            trade_list.append(Trade(
                id=str(order.get('orderId', '')),
                order_id=str(order.get('orderId', '')),
                symbol=order.get('symbol', ''),
                side=order.get('side', ''),
                amount=float(order.get('executedQty', 0)),
                price=float(order.get('avgPrice', 0)) if order.get('avgPrice') else float(order.get('price', 0)),
                cost=float(order.get('cumQuote', 0)),
                fee=0.0,  # Would need commission data
                fee_currency='USDT',
                timestamp=datetime.fromtimestamp(int(order.get('time', 0)) / 1000),
                realized_pnl=realized_pnl
            ))
        
        # Sort by timestamp descending
        trade_list.sort(key=lambda x: x.timestamp, reverse=True)
        
        return trade_list[:limit]
        
    except Exception as e:
        logger.error(f"Failed to get trades: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get trades: {e}"
        )

@router.post("/close-position", response_model=PositionCloseResponse)
async def close_position(
    request: ClosePositionRequest,
    client: BinanceExchangeClient = Depends(get_binance_client)
):
    """Close a specific position partially or completely"""
    try:
        # Get current position
        positions = await client.get_open_positions(symbol=request.symbol)
        
        position = None
        for pos in positions:
            if pos.get('symbol') == request.symbol and float(pos.get('positionAmt', 0)) != 0:
                position = pos
                break
        
        if not position:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No open position found for symbol: {request.symbol}"
            )
        
        position_amt = float(position.get('positionAmt', 0))
        close_qty = abs(position_amt) * (request.percentage / 100.0)
        
        # Determine order side (opposite of position)
        order_side = "SELL" if position_amt > 0 else "BUY"
        
        # Place market order to close position
        order = await client.place_order(
            symbol=request.symbol,
            order_type="MARKET",
            side=order_side,
            quantity=close_qty,
            time_in_force=None  # Market orders don't need time in force
        )
        
        # Calculate remaining position
        remaining_position = abs(position_amt) - close_qty if request.percentage < 100 else 0
        
        return PositionCloseResponse(
            success=True,
            order_id=str(order.get('orderId', '')),
            message=f"Successfully closed {request.percentage}% of {request.symbol} position",
            remaining_position=remaining_position
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to close position for {request.symbol}: {e}")
        return PositionCloseResponse(
            success=False,
            message=f"Failed to close position: {e}"
        )

@router.post("/cancel-order", response_model=OrderCancelResponse)
async def cancel_order(
    request: CancelOrderRequest,
    client: BinanceExchangeClient = Depends(get_binance_client)
):
    """Cancel a specific open order"""
    try:
        # Get order details first
        order = await client.get_order(
            symbol=request.symbol,
            order_id=request.order_id
        )
        
        if order.get('status') not in ['NEW', 'PARTIALLY_FILLED']:
            return OrderCancelResponse(
                success=False,
                message=f"Order {request.order_id} cannot be cancelled (status: {order.get('status')})"
            )
        
        # Cancel the order
        cancel_result = await client.cancel_order(
            symbol=request.symbol,
            order_id=request.order_id
        )
        
        # Create cancelled order object
        cancelled_order = Order(
            order_id=str(cancel_result.get('orderId', '')),
            symbol=cancel_result.get('symbol', ''),
            side=cancel_result.get('side', ''),
            type=cancel_result.get('type', ''),
            amount=float(cancel_result.get('origQty', 0)),
            filled=float(cancel_result.get('executedQty', 0)),
            remaining=0.0,  # Cancelled orders have no remaining amount
            price=float(cancel_result.get('price', 0)) if cancel_result.get('price') else None,
            status='CANCELLED',
            timestamp=datetime.fromtimestamp(int(cancel_result.get('time', 0)) / 1000),
            last_update=datetime.now(),
            fees=0.0
        )
        
        return OrderCancelResponse(
            success=True,
            message=f"Successfully cancelled order {request.order_id}",
            cancelled_order=cancelled_order
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel order {request.order_id}: {e}")
        return OrderCancelResponse(
            success=False,
            message=f"Failed to cancel order: {e}"
        )

@router.get("/risk-metrics", response_model=RiskMetrics)
async def get_risk_metrics(client: BinanceExchangeClient = Depends(get_binance_client)):
    """Calculate portfolio risk metrics"""
    try:
        # Get account and position data
        account = await client.client.futures_account()
        positions = await client.get_open_positions()
        
        total_margin_balance = float(account.get('totalMarginBalance', 0))
        total_position_margin = float(account.get('totalPositionInitialMargin', 0))
        
        # Calculate position concentration
        position_concentration = {}
        total_position_value = 0
        
        for pos in positions:
            if float(pos.get('positionAmt', 0)) != 0:
                symbol = pos.get('symbol', '')
                position_value = abs(float(pos.get('positionAmt', 0)) * float(pos.get('entryPrice', 0)))
                position_concentration[symbol] = position_value
                total_position_value += position_value
        
        # Normalize concentration to percentages
        if total_position_value > 0:
            position_concentration = {
                symbol: (value / total_position_value) * 100 
                for symbol, value in position_concentration.items()
            }
        
        # Calculate portfolio heat (simplified)
        portfolio_heat = (total_position_margin / max(total_margin_balance, 1)) * 100
        
        # Calculate margin utilization
        margin_utilization = portfolio_heat
        
        # Simplified VaR calculation (would use historical data in production)
        var_1d = total_position_value * 0.02  # 2% daily VaR estimate
        var_7d = total_position_value * 0.05  # 5% weekly VaR estimate
        
        # Correlation risk (simplified - would need correlation matrix in production)
        correlation_risk = min(len(position_concentration) * 10, 100)  # Rough estimate
        
        return RiskMetrics(
            portfolio_heat=portfolio_heat,
            position_concentration=position_concentration,
            margin_utilization=margin_utilization,
            var_1d=var_1d,
            var_7d=var_7d,
            correlation_risk=correlation_risk
        )
        
    except Exception as e:
        logger.error(f"Failed to calculate risk metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate risk metrics: {e}"
        )

@router.get("/market/ticker/{symbol}")
async def get_ticker(
    symbol: str,
    client: BinanceExchangeClient = Depends(get_binance_client)
):
    """Get current ticker information for a symbol"""
    try:
        ticker = await client.get_ticker(symbol)
        
        return {
            "symbol": symbol,
            "price": float(ticker.get('price', 0)),
            "price_change": float(ticker.get('priceChange', 0)),
            "price_change_percent": float(ticker.get('priceChangePercent', 0)),
            "high_price": float(ticker.get('highPrice', 0)),
            "low_price": float(ticker.get('lowPrice', 0)),
            "volume": float(ticker.get('volume', 0)),
            "quote_volume": float(ticker.get('quoteVolume', 0)),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get ticker for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get ticker for {symbol}: {e}"
        )

@router.get("/status")
async def get_binance_status(client: BinanceExchangeClient = Depends(get_binance_client)):
    """Get comprehensive Binance service status"""
    try:
        # Test connection
        await client.client.ping()
        
        # Get account summary
        account = await client.client.futures_account()
        positions = await client.get_open_positions()
        
        active_positions = [pos for pos in positions if float(pos.get('positionAmt', 0)) != 0]
        
        return {
            "service": "Binance Futures Testnet",
            "status": "operational",
            "timestamp": datetime.now().isoformat(),
            "testnet": client.settings.use_testnet,
            "connection": "active",
            "account_status": {
                "total_balance": float(account.get('totalWalletBalance', 0)),
                "equity": float(account.get('totalMarginBalance', 0)),
                "active_positions": len(active_positions),
                "margin_ratio": (
                    float(account.get('totalPositionInitialMargin', 0)) / 
                    max(float(account.get('totalMarginBalance', 1)), 1)
                ) * 100
            },
            "api_limits": {
                "weight": "1200/min",  # Binance standard limits
                "orders": "300/min",
                "raw_requests": "6100/min"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get Binance status: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Binance service status check failed: {e}"
        )

# Note: Error handlers should be added at the FastAPI app level, not router level