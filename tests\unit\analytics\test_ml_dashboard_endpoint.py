#!/usr/bin/env python3
"""
Quick test script to verify the ML dashboard endpoint is working.
"""

import asyncio
import json
from fastapi.testclient import TestClient
from app.dashboard.main import app

def test_ml_dashboard_endpoint():
    """Test the ML dashboard endpoint via FastAPI test client"""
    client = TestClient(app)
    
    print("Testing ML dashboard endpoint...")
    
    # Test without authentication first (should get 401)
    response = client.get("/api/ml/dashboard")
    print(f"Response status (no auth): {response.status_code}")
    
    if response.status_code == 401:
        print("✓ Endpoint exists and requires authentication (as expected)")
        print("✓ ML routes are properly registered")
        return True
    elif response.status_code == 404:
        print("✗ Endpoint not found - routes not properly registered")
        return False
    elif response.status_code == 200:
        print("✓ Endpoint works (no auth required)")
        data = response.json()
        print(f"✓ Current model version: {data.get('current_model', {}).get('version', 'unknown')}")
        return True
    else:
        print(f"✗ Unexpected status code: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def test_available_routes():
    """Check what ML routes are available"""
    client = TestClient(app)
    
    print("\nChecking available routes...")
    ml_routes = []
    for route in app.routes:
        if hasattr(route, 'path') and '/ml' in route.path:
            ml_routes.append(route.path)
    
    print(f"Found {len(ml_routes)} ML routes:")
    for route in sorted(ml_routes):
        print(f"  - {route}")
    
    return ml_routes

if __name__ == "__main__":
    print("=" * 60)
    print("ML Dashboard Endpoint Test")
    print("=" * 60)
    
    # Test routes registration
    routes = test_available_routes()
    
    # Test the specific endpoint we need
    success = test_ml_dashboard_endpoint()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ ML Dashboard endpoint is working correctly!")
        print("✓ The issue is likely with server restart or authentication")
    else:
        print("✗ ML Dashboard endpoint has issues")
    print("=" * 60)