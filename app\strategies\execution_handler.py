"""
Execution handler module for the Strategy Selector.

This module contains functions for executing trades and managing positions
based on the selected trading strategy.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

from app.services.execution.execution_service import ExecutionService
from app.services.execution.models import OrderSide, OrderStatus, Order
from app.services.execution.position import PositionSide

logger = logging.getLogger(__name__)

class ExecutionHandler:
    """Handles trade execution and position management."""

    def __init__(self, execution_service: ExecutionService):
        """Initialize the execution handler.

        Args:
            execution_service: Service for executing trades
        """
        self.execution_service = execution_service
        self.logger = logging.getLogger(__name__)
        self.active_orders: Dict[str, Order] = {}

    async def execute_strategy_entry(self,
                                   symbol: str,
                                   strategy_name: str,
                                   side: OrderSide,
                                   quantity: float,
                                   price: Optional[float] = None,
                                   params: Optional[Dict[str, Any]] = None) -> Optional[Order]:
        """Execute a strategy entry order.

        Args:
            symbol: Trading pair symbol
            strategy_name: Name of the strategy
            side: Order side (buy/sell)
            quantity: Order quantity
            price: Optional limit price
            params: Additional parameters

        Returns:
            Optional[Order]: Executed order or None if failed
        """
        try:
            self.logger.info(f"Executing {strategy_name} entry: {side.name} {quantity} {symbol}")

            # Create order
            order = await self.execution_service.create_market_order(
                symbol=symbol,
                side=side,
                quantity=quantity
            )

            if order:
                order_id = order.order_id
                self.active_orders[order_id] = order
                self.logger.info(f"Entry order executed: {order_id}")

                # Add strategy tag to order
                order.metadata = {
                    'strategy': strategy_name,
                    'type': 'entry',
                    'timestamp': datetime.now().isoformat()
                }

                return order
            else:
                self.logger.error(f"Failed to execute entry order for {strategy_name}")
                return None

        except Exception as e:
            self.logger.error(f"Error executing strategy entry: {e}")
            return None

    async def execute_strategy_exit(self,
                                  symbol: str,
                                  strategy_name: str,
                                  position_side: PositionSide,
                                  quantity: float,
                                  price: Optional[float] = None,
                                  params: Optional[Dict[str, Any]] = None) -> Optional[Order]:
        """Execute a strategy exit order.

        Args:
            symbol: Trading pair symbol
            strategy_name: Name of the strategy
            position_side: Position side (long/short)
            quantity: Order quantity
            price: Optional limit price
            params: Additional parameters

        Returns:
            Optional[Order]: Executed order or None if failed
        """
        try:
            # Determine order side based on position side
            side = OrderSide.SELL if position_side == PositionSide.LONG else OrderSide.BUY

            self.logger.info(f"Executing {strategy_name} exit: {side.name} {quantity} {symbol}")

            # Create order
            order = await self.execution_service.create_market_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                reduce_only=True
            )

            if order:
                order_id = order.order_id
                self.active_orders[order_id] = order
                self.logger.info(f"Exit order executed: {order_id}")

                # Add strategy tag to order
                order.metadata = {
                    'strategy': strategy_name,
                    'type': 'exit',
                    'timestamp': datetime.now().isoformat()
                }

                return order
            else:
                self.logger.error(f"Failed to execute exit order for {strategy_name}")
                return None

        except Exception as e:
            self.logger.error(f"Error executing strategy exit: {e}")
            return None

    async def place_stop_loss(self,
                            symbol: str,
                            position_side: PositionSide,
                            quantity: float,
                            stop_price: float) -> Optional[Order]:
        """Place a stop loss order.

        Args:
            symbol: Trading pair symbol
            position_side: Position side (long/short)
            quantity: Order quantity
            stop_price: Stop price

        Returns:
            Optional[Order]: Placed order or None if failed
        """
        try:
            # Determine order side based on position side
            side = OrderSide.SELL if position_side == PositionSide.LONG else OrderSide.BUY

            self.logger.info(f"Placing stop loss: {side.name} {quantity} {symbol} @ {stop_price}")

            # Create stop loss order
            order = await self.execution_service.create_stop_loss_market_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )

            if order:
                order_id = order.order_id
                self.active_orders[order_id] = order
                self.logger.info(f"Stop loss placed: {order_id}")

                # Add metadata
                order.metadata = {
                    'type': 'stop_loss',
                    'timestamp': datetime.now().isoformat()
                }

                return order
            else:
                self.logger.error(f"Failed to place stop loss for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Error placing stop loss: {e}")
            return None

    async def place_take_profit(self,
                              symbol: str,
                              position_side: PositionSide,
                              quantity: float,
                              take_profit_price: float) -> Optional[Order]:
        """Place a take profit order.

        Args:
            symbol: Trading pair symbol
            position_side: Position side (long/short)
            quantity: Order quantity
            take_profit_price: Take profit price

        Returns:
            Optional[Order]: Placed order or None if failed
        """
        try:
            # Determine order side based on position side
            side = OrderSide.SELL if position_side == PositionSide.LONG else OrderSide.BUY

            self.logger.info(f"Placing take profit: {side.name} {quantity} {symbol} @ {take_profit_price}")

            # Create take profit order
            order = await self.execution_service.create_take_profit_market_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                stop_price=take_profit_price,
                reduce_only=True
            )

            if order:
                order_id = order.order_id
                self.active_orders[order_id] = order
                self.logger.info(f"Take profit placed: {order_id}")

                # Add metadata
                order.metadata = {
                    'type': 'take_profit',
                    'timestamp': datetime.now().isoformat()
                }

                return order
            else:
                self.logger.error(f"Failed to place take profit for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Error placing take profit: {e}")
            return None

    async def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all active orders for a symbol.

        Args:
            symbol: Trading pair symbol

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Cancelling all orders for {symbol}")

            # Cancel orders via execution service
            result = await self.execution_service.cancel_all_orders(symbol)

            if result:
                # Clear active orders for this symbol
                self.active_orders = {
                    order_id: order for order_id, order in self.active_orders.items()
                    if order.symbol != symbol
                }
                self.logger.info(f"All orders cancelled for {symbol}")
                return True
            else:
                self.logger.error(f"Failed to cancel all orders for {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"Error cancelling all orders: {e}")
            return False

    async def close_position(self, symbol: str) -> bool:
        """Close any open position for a symbol.

        Args:
            symbol: Trading pair symbol

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Closing position for {symbol}")

            # Close position via execution service
            result = await self.execution_service.close_position(symbol)

            if result:
                self.logger.info(f"Position closed for {symbol}")
                return True
            else:
                self.logger.warning(f"No position to close for {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return False

    async def update_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Update the status of an order.

        Args:
            order_id: Order ID to update

        Returns:
            Optional[OrderStatus]: Updated order status or None if failed
        """
        try:
            if order_id not in self.active_orders:
                self.logger.warning(f"Order {order_id} not found in active orders")
                return None

            # Get order from execution service
            order = await self.execution_service.get_order(
                symbol=self.active_orders[order_id].symbol,
                order_id=order_id
            )

            if order:
                # Update active order
                self.active_orders[order_id] = order
                self.logger.debug(f"Order {order_id} status: {order.status.name}")
                return order.status
            else:
                self.logger.error(f"Failed to get order {order_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error updating order status: {e}")
            return None
