"""
Ensemble Configuration and Data Classes for Strategy Ensemble System.
Defines the core data structures and configuration for the MCP-enhanced ensemble system.
"""

from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
from datetime import datetime
import json
import os


@dataclass
class EnsembleConfig:
    """Configuration for ensemble strategy execution."""
    strategy_names: List[str]
    default_weights: Dict[str, float]
    confidence_threshold: float
    max_position_size: float
    max_portfolio_risk: float
    correlation_threshold: float
    redis_ttl_weights: int
    redis_ttl_signals: int
    redis_ttl_volatility: int
    alert_thresholds: Dict[str, float]
    
    @classmethod
    def from_env(cls) -> 'EnsembleConfig':
        """Load configuration from environment variables."""
        return cls(
            strategy_names=['GridStrategy', 'TechnicalAnalysisStrategy', 'TrendFollowingStrategy'],
            default_weights={
                'GridStrategy': 0.33, 
                'TechnicalAnalysisStrategy': 0.33, 
                'TrendFollowingStrategy': 0.34
            },
            confidence_threshold=float(os.getenv('ENSEMBLE_CONFIDENCE_THRESHOLD', '0.6')),
            max_position_size=float(os.getenv('MAX_POSITION_SIZE', '0.1')),
            max_portfolio_risk=float(os.getenv('MAX_PORTFOLIO_RISK', '0.8')),
            correlation_threshold=float(os.getenv('CORRELATION_THRESHOLD', '0.8')),
            redis_ttl_weights=int(os.getenv('REDIS_TTL_WEIGHTS', '300')),  # 5 minutes
            redis_ttl_signals=int(os.getenv('REDIS_TTL_SIGNALS', '30')),   # 30 seconds
            redis_ttl_volatility=int(os.getenv('REDIS_TTL_VOLATILITY', '900')),  # 15 minutes
            alert_thresholds={
                'drawdown': float(os.getenv('ALERT_DRAWDOWN_THRESHOLD', '0.15')),
                'sharpe_ratio': float(os.getenv('ALERT_SHARPE_THRESHOLD', '0.5')),
                'correlation': float(os.getenv('ALERT_CORRELATION_THRESHOLD', '0.8')),
                'position_size': float(os.getenv('ALERT_POSITION_SIZE_THRESHOLD', '0.25'))
            }
        )
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary."""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert configuration to JSON string."""
        return json.dumps(self.to_dict(), default=str)


@dataclass
class StrategyWeight:
    """Strategy weight with metadata."""
    strategy_name: str
    weight: float
    confidence: float
    last_updated: datetime
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return asdict(self)


@dataclass
class AggregatedSignal:
    """Aggregated signal from multiple strategies."""
    action: str  # 'BUY', 'SELL', 'HOLD'
    quantity: float
    price: float
    confidence: float
    contributing_strategies: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return asdict(self)


@dataclass
class PortfolioMetrics:
    """Portfolio performance metrics."""
    total_pnl: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    strategy_contributions: Dict[str, float]
    correlation_matrix: Dict[str, Dict[str, float]]
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return asdict(self)


@dataclass
class RiskLimits:
    """Risk management limits for position sizing."""
    max_position_size: float
    max_portfolio_risk: float
    max_correlation_exposure: float
    max_drawdown_limit: float
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return asdict(self)