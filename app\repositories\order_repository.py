"""Module for the OrderRepository class."""
from typing import List, Optional
from sqlalchemy.orm import Session
from app.services.execution.models import Order

class OrderRepository:
    """Repository for order-related database operations."""

    def __init__(self, db_session: Session):
        self.db_session = db_session

    def add_order(self, order: Order) -> Order:
        """Adds a new order to the database."""
        self.db_session.add(order)
        self.db_session.commit()
        self.db_session.refresh(order)
        return order

    def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """Retrieves an order by its ID."""
        return self.db_session.query(Order).filter(Order.id == order_id).first()

    def get_all_orders(self) -> List[Order]:
        """Retrieves all orders from the database."""
        return self.db_session.query(Order).all() 