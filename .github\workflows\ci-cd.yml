name: Strategy Ensemble CI/CD

on:
  push:
    branches: [ main, develop, 2nd-Edit ]
  pull_request:
    branches: [ main, develop ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements_current.txt
        pip install pytest pytest-asyncio pytest-cov black isort flake8

    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 app/ --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 app/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Format check with black
      run: |
        black --check app/ tests/ --line-length 100

    - name: Import sort check with isort
      run: |
        isort --check-only app/ tests/ --profile black

    - name: Run tests with pytest
      env:
        REDIS_URL: redis://localhost:6379
        ENVIRONMENT: testing
      run: |
        python -m pytest tests/ -v --cov=app --cov-report=xml --cov-report=html
        
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build-and-push:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        target: [development, production, ml-training]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch,suffix=-${{ matrix.target }}
          type=ref,event=pr,suffix=-${{ matrix.target }}
          type=sha,suffix=-${{ matrix.target }}
          type=raw,value=latest-${{ matrix.target }},enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        target: ${{ matrix.target }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  integration-tests:
    needs: build-and-push
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Run integration tests
      run: |
        # Pull the development image
        docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}-development
        
        # Start services with docker-compose
        docker-compose --profile test up -d
        
        # Wait for services to be ready
        sleep 30
        
        # Run integration tests
        docker-compose run --rm tests python -m pytest tests/ -v -k "integration"
        
        # Cleanup
        docker-compose down -v

  ml-pipeline-test:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Test ML Training Pipeline
      run: |
        # Pull the ML training image
        docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}-ml-training
        
        # Run ML pipeline test
        docker run --rm \
          -e ENVIRONMENT=testing \
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}-ml-training \
          python -c "
            from app.ml.pipelines.ensemble_training import ensemble_training_pipeline
            try:
                result = ensemble_training_pipeline()
                print(f'ML Pipeline test successful: {result}')
            except Exception as e:
                print(f'ML Pipeline test failed: {e}')
                exit(1)
          "

  deploy-staging:
    needs: [integration-tests, ml-pipeline-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Deploy to Staging
      run: |
        echo "Deploying to staging environment..."
        # This would typically involve:
        # - Updating staging environment with new image
        # - Running smoke tests
        # - Notifying team via Slack/Discord
        echo "Staging deployment completed"

  deploy-production:
    needs: [integration-tests, ml-pipeline-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Deploy to Production
      run: |
        echo "Deploying to production environment..."
        # This would typically involve:
        # - Blue-green deployment
        # - Database migrations
        # - Health checks
        # - Rollback capability
        echo "Production deployment completed"

  notify:
    needs: [deploy-staging, deploy-production]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        text: |
          Strategy Ensemble System deployment status: ${{ job.status }}
          Branch: ${{ github.ref }}
          Commit: ${{ github.sha }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}