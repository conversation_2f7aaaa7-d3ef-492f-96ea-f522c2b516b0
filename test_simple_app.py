#!/usr/bin/env python3
"""
Simple FastAPI test to check if basic setup works
"""

from fastapi import FastAPI
import uvicorn

app = FastAPI(title="Simple Test App")

@app.get("/")
async def root():
    return {"message": "Hello World", "status": "working"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
