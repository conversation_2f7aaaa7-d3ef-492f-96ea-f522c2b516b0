{"ast": null, "code": "/**\n * WebSocket service for real-time trade updates\n */import{WS_URL}from'../config';// Define event types\nexport let WebSocketEventType=/*#__PURE__*/function(WebSocketEventType){WebSocketEventType[\"TRADE_UPDATE\"]=\"trade_update\";WebSocketEventType[\"SYSTEM_STATUS\"]=\"system_status\";WebSocketEventType[\"ERROR\"]=\"error\";// Auto Trading Events\nWebSocketEventType[\"AUTO_TRADING_SESSION_STARTED\"]=\"auto_trading_session_started\";WebSocketEventType[\"AUTO_TRADING_SESSION_STOPPED\"]=\"auto_trading_session_stopped\";WebSocketEventType[\"AUTO_TRADING_SESSION_PAUSED\"]=\"auto_trading_session_paused\";WebSocketEventType[\"AUTO_TRADING_SESSION_RESUMED\"]=\"auto_trading_session_resumed\";WebSocketEventType[\"AUTO_TRADING_PERFORMANCE_UPDATE\"]=\"auto_trading_performance_update\";WebSocketEventType[\"AUTO_TRADING_ALERT\"]=\"auto_trading_alert\";WebSocketEventType[\"AUTO_TRADING_EMERGENCY_STOP\"]=\"auto_trading_emergency_stop\";WebSocketEventType[\"AUTO_TRADING_CONNECTION_ESTABLISHED\"]=\"auto_trading_connection_established\";WebSocketEventType[\"AUTO_TRADING_INITIAL_STATUS\"]=\"auto_trading_initial_status\";// Session Reports Events\nWebSocketEventType[\"LIVE_SESSION_METRICS\"]=\"live_session_metrics\";WebSocketEventType[\"SESSION_RISK_ALERT\"]=\"session_risk_alert\";WebSocketEventType[\"SESSION_REPORT_UPDATE\"]=\"session_report_update\";return WebSocketEventType;}({});// Event type interfaces\n// Define event data interfaces\n// Define event handler types\n// WebSocket service class\nexport class WebSocketService{constructor(){this.socket=null;this.reconnectAttempts=0;this.maxReconnectAttempts=5;this.reconnectDelay=1000;// Start with 1 second delay\nthis.reconnectTimer=null;this.isConnecting=false;this.isConnected=false;this.eventHandlers={};}/**\n   * Connect to the WebSocket server\n   */connect(){if(this.socket||this.isConnecting){return;}this.isConnecting=true;try{// Use the WebSocket URL from config\nconst wsUrl=`${WS_URL}/trades`;console.log(`Connecting to WebSocket at ${wsUrl}`);this.socket=new WebSocket(wsUrl);this.socket.onopen=this.handleOpen.bind(this);this.socket.onmessage=this.handleMessage.bind(this);this.socket.onclose=this.handleClose.bind(this);this.socket.onerror=this.handleError.bind(this);}catch(error){console.error('Error connecting to WebSocket:',error);this.isConnecting=false;this.scheduleReconnect();}}/**\n   * Disconnect from the WebSocket server\n   */disconnect(){if(this.socket){this.socket.close();this.socket=null;}if(this.reconnectTimer!==null){window.clearTimeout(this.reconnectTimer);this.reconnectTimer=null;}this.isConnected=false;this.isConnecting=false;this.reconnectAttempts=0;}/**\n   * Add an event handler\n   */on(eventType,handler){if(!this.eventHandlers[eventType]){this.eventHandlers[eventType]=[];}this.eventHandlers[eventType].push(handler);}/**\n   * Remove an event handler\n   */off(eventType,handler){if(!this.eventHandlers[eventType]){return;}const index=this.eventHandlers[eventType].indexOf(handler);if(index!==-1){this.eventHandlers[eventType].splice(index,1);}}/**\n   * Handle WebSocket open event\n   */handleOpen(){console.log('WebSocket connection established');this.isConnected=true;this.isConnecting=false;this.reconnectAttempts=0;// Notify listeners of system status\nconst statusEvent={status:'online',message:'Connected to server'};this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,statusEvent);}/**\n   * Handle WebSocket message event\n   */handleMessage(event){try{const data=JSON.parse(event.data);if(data.type==='trade_update'){this.notifyHandlers(WebSocketEventType.TRADE_UPDATE,data.data);}else if(data.type==='system_status'){this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,data.data);}else if(data.type==='error'){this.notifyHandlers(WebSocketEventType.ERROR,data.data);}else{console.warn('Unknown WebSocket message type:',data.type);}}catch(error){console.error('Error parsing WebSocket message:',error,event.data);}}/**\n   * Handle WebSocket close event\n   */handleClose(event){console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);this.socket=null;this.isConnected=false;this.isConnecting=false;// Notify listeners of system status\nconst statusEvent={status:'offline',message:`Disconnected from server: ${event.reason||'Connection closed'}`};this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,statusEvent);// Attempt to reconnect if the close was unexpected\nif(event.code!==1000){this.scheduleReconnect();}}/**\n   * Handle WebSocket error event\n   */handleError(event){console.error('WebSocket error:',event);// Notify listeners of error\nconst errorEvent={code:'connection_error',message:'WebSocket connection error'};this.notifyHandlers(WebSocketEventType.ERROR,errorEvent);}/**\n   * Schedule a reconnection attempt\n   */scheduleReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.log('Maximum reconnection attempts reached');// Notify listeners of system status\nconst statusEvent={status:'offline',message:'Failed to reconnect to server after multiple attempts'};this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,statusEvent);return;}if(this.reconnectTimer!==null){window.clearTimeout(this.reconnectTimer);}const delay=this.reconnectDelay*Math.pow(1.5,this.reconnectAttempts);console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts+1}/${this.maxReconnectAttempts})`);this.reconnectTimer=window.setTimeout(()=>{this.reconnectAttempts++;this.connect();},delay);}/**\n   * Notify all handlers of an event\n   */notifyHandlers(eventType,data){if(!this.eventHandlers[eventType]){return;}for(const handler of this.eventHandlers[eventType]){try{handler(data);}catch(error){console.error(`Error in ${eventType} handler:`,error);}}}}// Create a singleton instance\nexport const websocketService=new WebSocketService();// Export the singleton instance as default\nexport default websocketService;", "map": {"version": 3, "names": ["WS_URL", "WebSocketEventType", "WebSocketService", "constructor", "socket", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "reconnectTimer", "isConnecting", "isConnected", "eventHandlers", "connect", "wsUrl", "console", "log", "WebSocket", "onopen", "handleOpen", "bind", "onmessage", "handleMessage", "onclose", "handleClose", "onerror", "handleError", "error", "scheduleReconnect", "disconnect", "close", "window", "clearTimeout", "on", "eventType", "handler", "push", "off", "index", "indexOf", "splice", "statusEvent", "status", "message", "notifyHandlers", "SYSTEM_STATUS", "event", "data", "JSON", "parse", "type", "TRADE_UPDATE", "ERROR", "warn", "code", "reason", "errorEvent", "delay", "Math", "pow", "setTimeout", "websocketService"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/websocket.ts"], "sourcesContent": ["/**\n * WebSocket service for real-time trade updates\n */\n\nimport { WS_URL } from '../config';\n\n// Define event types\nexport enum WebSocketEventType {\n  TRADE_UPDATE = 'trade_update',\n  SYSTEM_STATUS = 'system_status',\n  ERROR = 'error',\n  // Auto Trading Events\n  AUTO_TRADING_SESSION_STARTED = 'auto_trading_session_started',\n  AUTO_TRADING_SESSION_STOPPED = 'auto_trading_session_stopped',\n  AUTO_TRADING_SESSION_PAUSED = 'auto_trading_session_paused',\n  AUTO_TRADING_SESSION_RESUMED = 'auto_trading_session_resumed',\n  AUTO_TRADING_PERFORMANCE_UPDATE = 'auto_trading_performance_update',\n  AUTO_TRADING_ALERT = 'auto_trading_alert',\n  AUTO_TRADING_EMERGENCY_STOP = 'auto_trading_emergency_stop',\n  AUTO_TRADING_CONNECTION_ESTABLISHED = 'auto_trading_connection_established',\n  AUTO_TRADING_INITIAL_STATUS = 'auto_trading_initial_status',\n  // Session Reports Events\n  LIVE_SESSION_METRICS = 'live_session_metrics',\n  SESSION_RISK_ALERT = 'session_risk_alert',\n  SESSION_REPORT_UPDATE = 'session_report_update',\n}\n\n// Event type interfaces\nexport interface LiveSessionMetricsEvent {\n  session_id: string;\n  live_metrics: {\n    current_pnl: number;\n    unrealized_pnl: number;\n    win_rate: number;\n    active_trades: number;\n    sharpe_ratio: number;\n  };\n  active_alerts?: Array<{\n    level: string;\n    message: string;\n    timestamp: string;\n  }>;\n}\n\nexport interface SessionRiskAlertEvent {\n  alert: {\n    level: string;\n    message: string;\n  };\n  timestamp: string;\n}\n\nexport interface SessionReportUpdateEvent {\n  session_id: string;\n  status: string;\n}\n\n// Define event data interfaces\nexport interface TradeUpdateEvent {\n  trade_id: string;\n  status: string;\n  symbol: string;\n  entry_side: string;\n  entry_price: number | null;\n  entry_qty: number | null;\n  sl_price: number | null;\n  tp_price: number | null;\n  timestamp: string;\n  exit_price?: number | null;\n  current_price?: number;\n}\n\nexport interface SystemStatusEvent {\n  status: 'online' | 'offline' | 'degraded';\n  message: string;\n}\n\nexport interface ErrorEvent {\n  code: string;\n  message: string;\n}\n\n// Define event handler types\nexport type TradeUpdateHandler = (data: TradeUpdateEvent) => void;\nexport type SystemStatusHandler = (data: SystemStatusEvent) => void;\nexport type ErrorHandler = (data: ErrorEvent) => void;\nexport type LiveSessionMetricsHandler = (data: LiveSessionMetricsEvent) => void;\nexport type SessionRiskAlertHandler = (data: SessionRiskAlertEvent) => void;\nexport type SessionReportUpdateHandler = (data: SessionReportUpdateEvent) => void;\n\n// WebSocket service class\nexport class WebSocketService {\n  private socket: WebSocket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000; // Start with 1 second delay\n  private reconnectTimer: number | null = null;\n  private isConnecting = false;\n  private isConnected = false;\n  private eventHandlers: Record<string, Function[]> = {};\n\n  /**\n   * Connect to the WebSocket server\n   */\n  public connect(): void {\n    if (this.socket || this.isConnecting) {\n      return;\n    }\n\n    this.isConnecting = true;\n\n    try {\n      // Use the WebSocket URL from config\n      const wsUrl = `${WS_URL}/trades`;\n\n      console.log(`Connecting to WebSocket at ${wsUrl}`);\n      this.socket = new WebSocket(wsUrl);\n\n      this.socket.onopen = this.handleOpen.bind(this);\n      this.socket.onmessage = this.handleMessage.bind(this);\n      this.socket.onclose = this.handleClose.bind(this);\n      this.socket.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      this.isConnecting = false;\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Disconnect from the WebSocket server\n   */\n  public disconnect(): void {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    this.isConnected = false;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n\n  /**\n   * Add an event handler\n   */\n  public on(eventType: string, handler: Function): void {\n    if (!this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType] = [];\n    }\n    this.eventHandlers[eventType].push(handler);\n  }\n\n  /**\n   * Remove an event handler\n   */\n  public off(eventType: string, handler: Function): void {\n    if (!this.eventHandlers[eventType]) {\n      return;\n    }\n    const index = this.eventHandlers[eventType].indexOf(handler);\n    if (index !== -1) {\n      this.eventHandlers[eventType].splice(index, 1);\n    }\n  }\n\n  /**\n   * Handle WebSocket open event\n   */\n  private handleOpen(): void {\n    console.log('WebSocket connection established');\n    this.isConnected = true;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n\n    // Notify listeners of system status\n    const statusEvent: SystemStatusEvent = {\n      status: 'online',\n      message: 'Connected to server',\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n  }\n\n  /**\n   * Handle WebSocket message event\n   */\n  private handleMessage(event: MessageEvent): void {\n    try {\n      const data = JSON.parse(event.data);\n\n      if (data.type === 'trade_update') {\n        this.notifyHandlers(WebSocketEventType.TRADE_UPDATE, data.data);\n      } else if (data.type === 'system_status') {\n        this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, data.data);\n      } else if (data.type === 'error') {\n        this.notifyHandlers(WebSocketEventType.ERROR, data.data);\n      } else {\n        console.warn('Unknown WebSocket message type:', data.type);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error, event.data);\n    }\n  }\n\n  /**\n   * Handle WebSocket close event\n   */\n  private handleClose(event: CloseEvent): void {\n    console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);\n    this.socket = null;\n    this.isConnected = false;\n    this.isConnecting = false;\n\n    // Notify listeners of system status\n    const statusEvent: SystemStatusEvent = {\n      status: 'offline',\n      message: `Disconnected from server: ${event.reason || 'Connection closed'}`,\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n    // Attempt to reconnect if the close was unexpected\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Handle WebSocket error event\n   */\n  private handleError(event: Event): void {\n    console.error('WebSocket error:', event);\n\n    // Notify listeners of error\n    const errorEvent: ErrorEvent = {\n      code: 'connection_error',\n      message: 'WebSocket connection error',\n    };\n    this.notifyHandlers(WebSocketEventType.ERROR, errorEvent);\n  }\n\n  /**\n   * Schedule a reconnection attempt\n   */\n  private scheduleReconnect(): void {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log('Maximum reconnection attempts reached');\n\n      // Notify listeners of system status\n      const statusEvent: SystemStatusEvent = {\n        status: 'offline',\n        message: 'Failed to reconnect to server after multiple attempts',\n      };\n      this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n      return;\n    }\n\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n    }\n\n    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);\n    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);\n\n    this.reconnectTimer = window.setTimeout(() => {\n      this.reconnectAttempts++;\n      this.connect();\n    }, delay);\n  }\n\n  /**\n   * Notify all handlers of an event\n   */\n  private notifyHandlers(eventType: string, data: any): void {\n    if (!this.eventHandlers[eventType]) {\n      return;\n    }\n    for (const handler of this.eventHandlers[eventType]) {\n      try {\n        handler(data);\n      } catch (error) {\n        console.error(`Error in ${eventType} handler:`, error);\n      }\n    }\n  }\n}\n\n// Create a singleton instance\nexport const websocketService = new WebSocketService();\n\n// Export the singleton instance as default\nexport default websocketService;\n"], "mappings": "AAAA;AACA;AACA,GAEA,OAASA,MAAM,KAAQ,WAAW,CAElC;AACA,UAAY,CAAAC,kBAAkB,uBAAlBA,kBAAkB,EAAlBA,kBAAkB,gCAAlBA,kBAAkB,kCAAlBA,kBAAkB,kBAI5B;AAJUA,kBAAkB,gEAAlBA,kBAAkB,gEAAlBA,kBAAkB,8DAAlBA,kBAAkB,gEAAlBA,kBAAkB,sEAAlBA,kBAAkB,4CAAlBA,kBAAkB,8DAAlBA,kBAAkB,8EAAlBA,kBAAkB,8DAc5B;AAdUA,kBAAkB,gDAAlBA,kBAAkB,4CAAlBA,kBAAkB,wDAAlB,CAAAA,kBAAkB,OAoB9B;AA8BA;AAyBA;AAQA;AACA,MAAO,MAAM,CAAAC,gBAAiB,CAAAC,YAAA,OACpBC,MAAM,CAAqB,IAAI,MAC/BC,iBAAiB,CAAG,CAAC,MACrBC,oBAAoB,CAAG,CAAC,MACxBC,cAAc,CAAG,IAAI,CAAE;AAAA,KACvBC,cAAc,CAAkB,IAAI,MACpCC,YAAY,CAAG,KAAK,MACpBC,WAAW,CAAG,KAAK,MACnBC,aAAa,CAA+B,CAAC,CAAC,EAEtD;AACF;AACA,KACSC,OAAOA,CAAA,CAAS,CACrB,GAAI,IAAI,CAACR,MAAM,EAAI,IAAI,CAACK,YAAY,CAAE,CACpC,OACF,CAEA,IAAI,CAACA,YAAY,CAAG,IAAI,CAExB,GAAI,CACF;AACA,KAAM,CAAAI,KAAK,CAAG,GAAGb,MAAM,SAAS,CAEhCc,OAAO,CAACC,GAAG,CAAC,8BAA8BF,KAAK,EAAE,CAAC,CAClD,IAAI,CAACT,MAAM,CAAG,GAAI,CAAAY,SAAS,CAACH,KAAK,CAAC,CAElC,IAAI,CAACT,MAAM,CAACa,MAAM,CAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,CAC/C,IAAI,CAACf,MAAM,CAACgB,SAAS,CAAG,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC,CACrD,IAAI,CAACf,MAAM,CAACkB,OAAO,CAAG,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC,CACjD,IAAI,CAACf,MAAM,CAACoB,OAAO,CAAG,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,IAAI,CAAC,CACnD,CAAE,MAAOO,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,IAAI,CAACjB,YAAY,CAAG,KAAK,CACzB,IAAI,CAACkB,iBAAiB,CAAC,CAAC,CAC1B,CACF,CAEA;AACF;AACA,KACSC,UAAUA,CAAA,CAAS,CACxB,GAAI,IAAI,CAACxB,MAAM,CAAE,CACf,IAAI,CAACA,MAAM,CAACyB,KAAK,CAAC,CAAC,CACnB,IAAI,CAACzB,MAAM,CAAG,IAAI,CACpB,CAEA,GAAI,IAAI,CAACI,cAAc,GAAK,IAAI,CAAE,CAChCsB,MAAM,CAACC,YAAY,CAAC,IAAI,CAACvB,cAAc,CAAC,CACxC,IAAI,CAACA,cAAc,CAAG,IAAI,CAC5B,CAEA,IAAI,CAACE,WAAW,CAAG,KAAK,CACxB,IAAI,CAACD,YAAY,CAAG,KAAK,CACzB,IAAI,CAACJ,iBAAiB,CAAG,CAAC,CAC5B,CAEA;AACF;AACA,KACS2B,EAAEA,CAACC,SAAiB,CAAEC,OAAiB,CAAQ,CACpD,GAAI,CAAC,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,CAAE,CAClC,IAAI,CAACtB,aAAa,CAACsB,SAAS,CAAC,CAAG,EAAE,CACpC,CACA,IAAI,CAACtB,aAAa,CAACsB,SAAS,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC,CAC7C,CAEA;AACF;AACA,KACSE,GAAGA,CAACH,SAAiB,CAAEC,OAAiB,CAAQ,CACrD,GAAI,CAAC,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,CAAE,CAClC,OACF,CACA,KAAM,CAAAI,KAAK,CAAG,IAAI,CAAC1B,aAAa,CAACsB,SAAS,CAAC,CAACK,OAAO,CAACJ,OAAO,CAAC,CAC5D,GAAIG,KAAK,GAAK,CAAC,CAAC,CAAE,CAChB,IAAI,CAAC1B,aAAa,CAACsB,SAAS,CAAC,CAACM,MAAM,CAACF,KAAK,CAAE,CAAC,CAAC,CAChD,CACF,CAEA;AACF;AACA,KACUnB,UAAUA,CAAA,CAAS,CACzBJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/C,IAAI,CAACL,WAAW,CAAG,IAAI,CACvB,IAAI,CAACD,YAAY,CAAG,KAAK,CACzB,IAAI,CAACJ,iBAAiB,CAAG,CAAC,CAE1B;AACA,KAAM,CAAAmC,WAA8B,CAAG,CACrCC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,qBACX,CAAC,CACD,IAAI,CAACC,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,CAAEJ,WAAW,CAAC,CACpE,CAEA;AACF;AACA,KACUnB,aAAaA,CAACwB,KAAmB,CAAQ,CAC/C,GAAI,CACF,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CAEnC,GAAIA,IAAI,CAACG,IAAI,GAAK,cAAc,CAAE,CAChC,IAAI,CAACN,cAAc,CAAC1C,kBAAkB,CAACiD,YAAY,CAAEJ,IAAI,CAACA,IAAI,CAAC,CACjE,CAAC,IAAM,IAAIA,IAAI,CAACG,IAAI,GAAK,eAAe,CAAE,CACxC,IAAI,CAACN,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,CAAEE,IAAI,CAACA,IAAI,CAAC,CAClE,CAAC,IAAM,IAAIA,IAAI,CAACG,IAAI,GAAK,OAAO,CAAE,CAChC,IAAI,CAACN,cAAc,CAAC1C,kBAAkB,CAACkD,KAAK,CAAEL,IAAI,CAACA,IAAI,CAAC,CAC1D,CAAC,IAAM,CACLhC,OAAO,CAACsC,IAAI,CAAC,iCAAiC,CAAEN,IAAI,CAACG,IAAI,CAAC,CAC5D,CACF,CAAE,MAAOvB,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAEmB,KAAK,CAACC,IAAI,CAAC,CACtE,CACF,CAEA;AACF;AACA,KACUvB,WAAWA,CAACsB,KAAiB,CAAQ,CAC3C/B,OAAO,CAACC,GAAG,CAAC,gCAAgC8B,KAAK,CAACQ,IAAI,IAAIR,KAAK,CAACS,MAAM,EAAE,CAAC,CACzE,IAAI,CAAClD,MAAM,CAAG,IAAI,CAClB,IAAI,CAACM,WAAW,CAAG,KAAK,CACxB,IAAI,CAACD,YAAY,CAAG,KAAK,CAEzB;AACA,KAAM,CAAA+B,WAA8B,CAAG,CACrCC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,6BAA6BG,KAAK,CAACS,MAAM,EAAI,mBAAmB,EAC3E,CAAC,CACD,IAAI,CAACX,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,CAAEJ,WAAW,CAAC,CAElE;AACA,GAAIK,KAAK,CAACQ,IAAI,GAAK,IAAI,CAAE,CACvB,IAAI,CAAC1B,iBAAiB,CAAC,CAAC,CAC1B,CACF,CAEA;AACF;AACA,KACUF,WAAWA,CAACoB,KAAY,CAAQ,CACtC/B,OAAO,CAACY,KAAK,CAAC,kBAAkB,CAAEmB,KAAK,CAAC,CAExC;AACA,KAAM,CAAAU,UAAsB,CAAG,CAC7BF,IAAI,CAAE,kBAAkB,CACxBX,OAAO,CAAE,4BACX,CAAC,CACD,IAAI,CAACC,cAAc,CAAC1C,kBAAkB,CAACkD,KAAK,CAAEI,UAAU,CAAC,CAC3D,CAEA;AACF;AACA,KACU5B,iBAAiBA,CAAA,CAAS,CAChC,GAAI,IAAI,CAACtB,iBAAiB,EAAI,IAAI,CAACC,oBAAoB,CAAE,CACvDQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CAEpD;AACA,KAAM,CAAAyB,WAA8B,CAAG,CACrCC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,uDACX,CAAC,CACD,IAAI,CAACC,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,CAAEJ,WAAW,CAAC,CAElE,OACF,CAEA,GAAI,IAAI,CAAChC,cAAc,GAAK,IAAI,CAAE,CAChCsB,MAAM,CAACC,YAAY,CAAC,IAAI,CAACvB,cAAc,CAAC,CAC1C,CAEA,KAAM,CAAAgD,KAAK,CAAG,IAAI,CAACjD,cAAc,CAAGkD,IAAI,CAACC,GAAG,CAAC,GAAG,CAAE,IAAI,CAACrD,iBAAiB,CAAC,CACzES,OAAO,CAACC,GAAG,CAAC,2BAA2ByC,KAAK,eAAe,IAAI,CAACnD,iBAAiB,CAAG,CAAC,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAEtH,IAAI,CAACE,cAAc,CAAGsB,MAAM,CAAC6B,UAAU,CAAC,IAAM,CAC5C,IAAI,CAACtD,iBAAiB,EAAE,CACxB,IAAI,CAACO,OAAO,CAAC,CAAC,CAChB,CAAC,CAAE4C,KAAK,CAAC,CACX,CAEA;AACF;AACA,KACUb,cAAcA,CAACV,SAAiB,CAAEa,IAAS,CAAQ,CACzD,GAAI,CAAC,IAAI,CAACnC,aAAa,CAACsB,SAAS,CAAC,CAAE,CAClC,OACF,CACA,IAAK,KAAM,CAAAC,OAAO,GAAI,KAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,CAAE,CACnD,GAAI,CACFC,OAAO,CAACY,IAAI,CAAC,CACf,CAAE,MAAOpB,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,YAAYO,SAAS,WAAW,CAAEP,KAAK,CAAC,CACxD,CACF,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAAkC,gBAAgB,CAAG,GAAI,CAAA1D,gBAAgB,CAAC,CAAC,CAEtD;AACA,cAAe,CAAA0D,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}