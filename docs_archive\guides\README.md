# Multi-Strategy Crypto Auto Trader

A sophisticated cryptocurrency trading bot that combines Grid Trading, Technical Analysis, and Trend-Following strategies. The system dynamically selects the optimal strategy based on current market conditions to maximize trading performance on Binance Futures.

## Features

- **Multiple Trading Strategies**:
  - Grid Trading for sideways markets
  - Technical Analysis for pattern-based trading
  - Trend-Following for strong directional markets

- **Dynamic Strategy Selection**: Automatically selects the best strategy based on current market conditions

- **ML Weight Optimization**: Uses reinforcement learning to optimize strategy weights based on market conditions

- **Risk Management**: Comprehensive risk controls including position sizing, stop-loss mechanisms, and exposure limits

- **Real-time Monitoring**: Web dashboard for monitoring performance and controlling the trading bot

## Project Structure

```
/Crypto_App_V2
├── app/
│   ├── api/                  # API integrations with Binance
│   ├── strategies/           # Trading strategy implementations
│   ├── models/               # Data models and schema
│   ├── utils/                # Utility functions
│   ├── services/             # Market analysis and strategy selection
│   ├── config/               # Configuration files
│   ├── dashboard/            # Web dashboard components
│   └── ml/                   # Machine learning components
├── tests/                    # Unit and integration tests
├── docs/                     # Documentation (see [Documentation](#documentation) section)
├── scripts/                  # Helper scripts
├── .env.example              # Environment variables template
├── requirements.txt          # Python dependencies
├── main.py                   # Application entry point
└── README.md                 # Project documentation
```

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/samueladegoke/Crypto_App_V2.git
   cd Crypto_App_V2
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt

   # For ML features, install additional dependencies
   pip install gym stable-baselines3 optuna
   ```

4. Set up environment variables:
   ```
   cp .env.example .env
   # Edit .env with your Binance API keys and configuration
   ```

## Usage

1. Start the backend API server (from the project root directory):
   ```
   python -m app.dashboard.main
   ```

2. Start the frontend dashboard (in a separate terminal):
   ```
   cd app\dashboard\frontend
   npm start
   ```

3. Start both frontend and backend simultaneously (Windows PowerShell):
   ```
   # In PowerShell Terminal 1 (from project root)
   python -m app.dashboard.main
   
   # In PowerShell Terminal 2
   cd app\dashboard\frontend; npm start
   ```

4. Access the dashboard in your browser:
   ```
   http://localhost:3000
   ```

5. Enable ML weight optimization:
   ```
   # Edit .env file
   ML_WEIGHT_OPTIMIZATION_ENABLED=True

   # Or use command line flag
   python main.py --enable-ml
   ```

4. Train the ML model:
   ```
   # Using the API
   curl -X POST http://localhost:8000/api/ml/train -H "Content-Type: application/json" -d '{"symbol":"BTCUSDT","timeframe":"1h","lookback_days":90,"window_size":10,"total_timesteps":100000,"optimize_hyperparameters":false,"optimization_trials":10}'
   ```

5. Get optimized weights:
   ```
   # Using the API
   curl -X POST http://localhost:8000/api/ml/weights -H "Content-Type: application/json" -d '{"market_conditions":{"volatility":0.02,"trend":0.3,"range_bound":0.4,"volume":0.8}}'
   ```

## Development

### Testing Framework

The project includes a comprehensive testing framework:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **Backtesting Framework**: Test trading strategies with historical data
- **Performance Benchmarks**: Evaluate strategy performance metrics
- **ML Tests**: Test machine learning components and reinforcement learning models

To run tests:

```bash
# Run all tests
pytest

# Run with coverage report
pytest --cov=app tests/

# Run specific test file
pytest tests/test_file.py

# Run with verbose output
pytest -v
```

### Test Structure

```
/tests
├── backtesting/           # Strategy backtesting tests
├── services/              # Service component tests
│   ├── market_data/       # Market data service tests
│   └── risk_management/   # Risk management tests
├── utils/                 # Utility function tests
├── ml/                    # Machine learning tests
│   ├── models/            # ML model tests
│   ├── training/          # Training process tests
│   └── evaluation/        # Model evaluation tests
├── config/                # Configuration tests
├── dashboard/             # Dashboard API tests
├── conftest.py            # Shared test fixtures
└── test_*.py              # Other test modules
```

### CI/CD Pipeline

The project uses GitHub Actions for continuous integration and deployment:

- **Automated Testing**: Tests run on multiple Python versions (3.11, 3.12)
- **Code Quality**: Includes linting (flake8) and type checking (mypy)
- **Docker Build**: Automatically builds container images
- **Deployment**: Supports automated deployment to AWS (when configured)

You can find the workflow configuration in `.github/workflows/main.yml`.

### Running the Application

For development:

```bash
# Run with debug logging
python main.py --log-level=DEBUG

# Run the API server (from project root)
python -m app.dashboard.main

# Run with specific configuration file
python main.py --config=config/custom_config.json

# Enable ML weight optimization
python main.py --enable-ml
```

Monitoring logs:

```bash
# View real-time logs
tail -f logs/crypto_trader.log
```

## Documentation

Comprehensive documentation is available in the `docs/` folder:

- **Strategy Improvements**:
  - [Machine Learning for Weight Optimization](docs/improvements/ml_weight_optimization.md)
  - [Risk-Adjusted Scoring Metrics](docs/improvements/risk_adjusted_scoring.md)
  - [Market Regime Detection](docs/improvements/market_regime_detection.md)
  - [Ensemble Strategy Allocation](docs/improvements/ensemble_strategy_allocation.md)

- **Setup and Configuration**:
  - [GitHub Setup Guide](docs/setup/GITHUB_SETUP.md)
  - [MCP Servers Setup Guide](docs/mcp_servers_setup.md)

- **Refactoring Documentation**:
  - [Refactoring Plan](docs/refactoring/REFACTORING_PLAN.md)

- **WebSocket Implementation**:
  - [WebSocket Plans and Testing](docs/websocket/)

See the [Documentation Index](docs/README.md) for a complete list of available documentation.

## License

MIT

## Disclaimer

This software is for educational purposes only. Use at your own risk. The authors are not responsible for any financial losses incurred from using this software.
