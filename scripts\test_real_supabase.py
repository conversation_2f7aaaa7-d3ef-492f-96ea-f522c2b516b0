#!/usr/bin/env python3
"""
Test Real Supabase Integration
Tests the actual Supabase service with real tables
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the app directory to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'app'))

from app.services.mcp.supabase_service import SupabaseService

# Load environment variables
load_dotenv()

async def test_real_supabase():
    """Test real Supabase integration"""
    print("🧪 Testing Real Supabase Integration")
    print("=" * 50)
    
    # Initialize service with real credentials
    supabase_service = SupabaseService()
    
    print("🔗 Testing connection...")
    connection_test = await supabase_service.test_connection()
    if not connection_test:
        print("❌ Connection failed")
        return False
    print("✅ Connection successful!")
    
    print("\n📊 Testing portfolio metrics storage...")
    test_metrics = {
        'total_pnl': 2500.75,
        'sharpe_ratio': 1.85,
        'max_drawdown': -0.12,
        'win_rate': 0.73,
        'strategy_contributions': {
            'GridStrategy': 850.25,
            'TechnicalAnalysisStrategy': 750.50,
            'TrendFollowingStrategy': 900.00
        },
        'correlation_matrix': {
            'GridStrategy': {'GridStrategy': 1.0, 'TechnicalAnalysisStrategy': 0.25},
            'TechnicalAnalysisStrategy': {'GridStrategy': 0.25, 'TechnicalAnalysisStrategy': 1.0}
        }
    }
    
    metrics_id = await supabase_service.store_portfolio_metrics(test_metrics)
    if metrics_id:
        print(f"✅ Portfolio metrics stored with ID: {metrics_id}")
    else:
        print("❌ Failed to store portfolio metrics")
        return False
    
    print("\n📈 Testing trade execution storage...")
    test_trade = {
        'strategy_name': 'GridStrategy',
        'symbol': 'BTCUSDT',
        'action': 'BUY',
        'quantity': 0.15,
        'price': 43750.00,
        'pnl': 87.50,
        'return_pct': 0.002,
        'fees': 6.56,
        'confidence': 0.88,
        'weight': 0.35,
        'position_size': 0.28,
        'market_conditions': {
            'volatility': 0.028,
            'volume': 1750000,
            'rsi': 68.2,
            'macd': 45.3
        }
    }
    
    trade_id = await supabase_service.store_trade_execution(test_trade)
    if trade_id:
        print(f"✅ Trade execution stored with ID: {trade_id}")
    else:
        print("❌ Failed to store trade execution")
        return False
    
    print("\n⚖️ Testing strategy weights storage...")
    test_weights = {
        'GridStrategy': 0.38,
        'TechnicalAnalysisStrategy': 0.32,
        'TrendFollowingStrategy': 0.30
    }
    
    weights_metadata = {
        'confidence': 0.94,
        'market_regime': 'trending_up'
    }
    
    weights_id = await supabase_service.store_strategy_weights(test_weights, weights_metadata)
    if weights_id:
        print(f"✅ Strategy weights stored with ID: {weights_id}")
    else:
        print("❌ Failed to store strategy weights")
        return False
    
    print("\n🚨 Testing alert storage...")
    alert_id = await supabase_service.store_alert(
        'performance_milestone',
        'info',
        'Portfolio reached $2500 milestone!',
        {'total_pnl': 2500.75, 'milestone': '$2500'}
    )
    
    if alert_id:
        print(f"✅ Alert stored with ID: {alert_id}")
    else:
        print("❌ Failed to store alert")
        return False
    
    print("\n📋 Testing data retrieval...")
    recent_trades = await supabase_service.get_recent_trades(limit=5)
    print(f"✅ Retrieved {len(recent_trades)} recent trades")
    
    portfolio_history = await supabase_service.get_portfolio_metrics_history(hours_back=1)
    print(f"✅ Retrieved {len(portfolio_history)} portfolio metrics records")
    
    unacked_alerts = await supabase_service.get_unacknowledged_alerts()
    print(f"✅ Retrieved {len(unacked_alerts)} unacknowledged alerts")
    
    print("\n📊 Testing database statistics...")
    stats = await supabase_service.get_database_stats()
    print("✅ Database statistics:")
    for key, value in stats.items():
        if key.endswith('_count'):
            print(f"   • {key}: {value}")
    
    print("\n🎉 ALL TESTS PASSED!")
    print("🔄 Your Supabase integration is fully operational!")
    print("\n📋 Next steps:")
    print("   • The system will now use real Supabase instead of mock data")
    print("   • Monitor your tables in the Supabase dashboard")
    print("   • Run ensemble system to see live data flow")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_real_supabase())
    if not success:
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure you've created the database tables using the SQL above")
        print("   2. Check your Supabase dashboard for any errors")
        print("   3. Verify table permissions allow INSERT/SELECT operations")
        exit(1)