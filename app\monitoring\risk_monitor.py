#!/usr/bin/env python3
"""
Real-Time Risk Monitor with Telegram Alerts
Implements Task 2.1.4: Integrate Telegram risk alerts with real-time violation detection and <5sec delivery.

Features:
- Real-time risk limit monitoring and violation detection
- Position size and drawdown warning notifications
- Correlation risk and volatility regime alerts
- Telegram bot integration for instant notifications
- Comprehensive risk analytics and reporting
- Emergency stop mechanisms for extreme risk scenarios
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import numpy as np

from app.services.volatility_calculator import VolatilityCalculator, VolatilityResult
from app.services.correlation_calculator import CorrelationMatrixCalculator, CorrelationMatrix
from app.strategies.position_size_calculator import PositionSizeCalculator, PositionSizeResult
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk alert levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class AlertType(Enum):
    """Types of risk alerts"""
    POSITION_SIZE = "position_size"
    DRAWDOWN = "drawdown"
    VOLATILITY = "volatility"
    CORRELATION = "correlation"
    PORTFOLIO_RISK = "portfolio_risk"
    SYSTEM_ERROR = "system_error"
    PERFORMANCE_DEGRADATION = "performance_degradation"

@dataclass
class RiskThresholds:
    """Risk monitoring thresholds"""
    # Position size thresholds
    max_single_position: float = 0.25        # 25% max single position
    max_strategy_allocation: float = 0.40     # 40% max per strategy
    position_size_warning: float = 0.20      # 20% warning threshold
    
    # Drawdown thresholds
    max_drawdown_critical: float = 0.15      # 15% critical drawdown
    max_drawdown_warning: float = 0.10       # 10% warning drawdown
    daily_loss_limit: float = 0.05           # 5% daily loss limit
    
    # Volatility thresholds
    volatility_extreme_threshold: float = 0.25  # 25% volatility extreme
    volatility_high_threshold: float = 0.15     # 15% volatility high
    vol_regime_change_alert: bool = True        # Alert on regime changes
    
    # Correlation thresholds
    max_correlation_exposure: float = 0.8      # 80% max correlated exposure
    correlation_clustering_alert: float = 0.7   # 70% clustering alert
    diversification_ratio_min: float = 1.2     # Minimum diversification ratio
    
    # Portfolio risk thresholds
    portfolio_var_95: float = 0.08             # 8% daily VaR at 95%
    sharpe_ratio_min: float = 0.5              # Minimum Sharpe ratio
    kelly_fraction_max: float = 0.15           # Maximum Kelly fraction
    
    # System performance thresholds
    execution_time_warning: float = 1000      # 1 second execution warning
    cache_hit_rate_min: float = 70            # 70% minimum cache hit rate
    error_rate_max: float = 0.05              # 5% maximum error rate
    
    # Emergency stop conditions
    emergency_drawdown: float = 0.20          # 20% emergency stop
    system_failure_threshold: int = 5         # Consecutive system failures

@dataclass
class RiskAlert:
    """Risk alert data structure"""
    alert_id: str
    alert_type: AlertType
    risk_level: RiskLevel
    title: str
    message: str
    current_value: float
    threshold_value: float
    strategy_name: Optional[str] = None
    symbol: Optional[str] = None
    recommendations: List[str] = None
    metadata: Dict[str, Any] = None
    timestamp: datetime = None
    resolved: bool = False
    resolution_timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.recommendations is None:
            self.recommendations = []
        if self.metadata is None:
            self.metadata = {}

@dataclass
class RiskStatus:
    """Current risk status summary"""
    overall_risk_level: RiskLevel
    active_alerts_count: int
    total_portfolio_exposure: float
    current_drawdown: float
    portfolio_volatility: float
    correlation_risk_score: float
    system_health_score: float
    last_update: datetime
    emergency_stop_triggered: bool = False

class RiskMonitor:
    """
    Real-time risk monitor with Telegram integration.
    
    Features:
    - Real-time risk limit monitoring
    - Multi-level alerting system (LOW to EMERGENCY)
    - Telegram notification integration
    - Comprehensive risk analytics
    - Emergency stop mechanisms
    - Performance monitoring integration
    """
    
    def __init__(
        self,
        volatility_calculator: VolatilityCalculator,
        correlation_calculator: CorrelationMatrixCalculator,
        position_calculator: PositionSizeCalculator,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        telegram_bot_token: Optional[str] = None,
        telegram_chat_id: Optional[str] = None,
        thresholds: Optional[RiskThresholds] = None
    ):
        self.volatility_calculator = volatility_calculator
        self.correlation_calculator = correlation_calculator
        self.position_calculator = position_calculator
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.thresholds = thresholds or RiskThresholds()
        
        # Telegram configuration
        self.telegram_bot_token = telegram_bot_token
        self.telegram_chat_id = telegram_chat_id
        self.telegram_enabled = bool(telegram_bot_token and telegram_chat_id)
        
        # Cache keys
        self.RISK_STATUS_KEY = "risk_monitor:status"
        self.ACTIVE_ALERTS_KEY = "risk_monitor:active_alerts"
        self.ALERT_HISTORY_KEY = "risk_monitor:alert_history"
        self.EMERGENCY_STOP_KEY = "risk_monitor:emergency_stop"
        
        # State tracking
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.alert_history: List[RiskAlert] = []
        self.last_alert_times: Dict[AlertType, datetime] = {}
        self.consecutive_failures = 0
        self.emergency_stop_active = False
        
        # Performance tracking
        self.alert_delivery_times = []
        self.monitoring_cycle_times = []
        
        logger.info(f"RiskMonitor initialized with Telegram {'enabled' if self.telegram_enabled else 'disabled'}")
    
    async def monitor_risk_continuous(
        self,
        strategies: List[str],
        portfolio_data: Dict[str, Any],
        monitoring_interval: float = 10.0  # 10 seconds
    ) -> None:
        """
        Start continuous risk monitoring loop.
        """
        logger.info(f"Starting continuous risk monitoring for {len(strategies)} strategies")
        
        while True:
            try:
                cycle_start = time.perf_counter()
                
                # Perform risk assessment
                risk_status = await self.assess_portfolio_risk(strategies, portfolio_data)
                
                # Process any new alerts
                await self._process_risk_alerts(risk_status)
                
                # Update monitoring cycle time
                cycle_time = (time.perf_counter() - cycle_start) * 1000
                self.monitoring_cycle_times.append(cycle_time)
                if len(self.monitoring_cycle_times) > 1000:
                    self.monitoring_cycle_times.pop(0)
                
                # Check for system performance issues
                if cycle_time > self.thresholds.execution_time_warning:
                    await self._create_performance_alert(
                        "Slow monitoring cycle",
                        f"Risk monitoring cycle took {cycle_time:.1f}ms",
                        cycle_time,
                        self.thresholds.execution_time_warning
                    )
                
                await asyncio.sleep(monitoring_interval)
                
            except Exception as e:
                logger.error(f"Risk monitoring cycle failed: {e}")
                self.consecutive_failures += 1
                
                if self.consecutive_failures >= self.thresholds.system_failure_threshold:
                    await self._trigger_emergency_stop("System monitoring failure")
                
                await asyncio.sleep(monitoring_interval * 2)  # Longer wait on failure
    
    async def assess_portfolio_risk(
        self,
        strategies: List[str],
        portfolio_data: Dict[str, Any]
    ) -> RiskStatus:
        """
        Assess current portfolio risk across all dimensions.
        """
        try:
            # Get current positions and metrics
            current_positions = portfolio_data.get('positions', {})
            portfolio_value = portfolio_data.get('total_value', 100000)  # Default for testing
            
            # Parallel risk calculations
            tasks = []
            
            # Position size risk assessment
            tasks.append(self._assess_position_size_risk(current_positions, portfolio_value))
            
            # Drawdown risk assessment
            tasks.append(self._assess_drawdown_risk(portfolio_data))
            
            # Volatility risk assessment
            tasks.append(self._assess_volatility_risk(strategies))
            
            # Correlation risk assessment
            tasks.append(self._assess_correlation_risk(strategies, current_positions))
            
            # System performance risk assessment
            tasks.append(self._assess_system_performance_risk())
            
            # Execute all assessments
            (position_risk, drawdown_risk, volatility_risk, 
             correlation_risk, performance_risk) = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle calculation errors
            risks = {
                'position': position_risk if not isinstance(position_risk, Exception) else {'level': RiskLevel.MEDIUM, 'score': 0.5},
                'drawdown': drawdown_risk if not isinstance(drawdown_risk, Exception) else {'level': RiskLevel.MEDIUM, 'score': 0.5},
                'volatility': volatility_risk if not isinstance(volatility_risk, Exception) else {'level': RiskLevel.MEDIUM, 'score': 0.5},
                'correlation': correlation_risk if not isinstance(correlation_risk, Exception) else {'level': RiskLevel.MEDIUM, 'score': 0.5},
                'performance': performance_risk if not isinstance(performance_risk, Exception) else {'level': RiskLevel.MEDIUM, 'score': 0.5}
            }
            
            # Calculate overall risk level
            overall_risk = self._calculate_overall_risk_level(risks)
            
            # Calculate portfolio metrics
            total_exposure = sum(abs(pos) for pos in current_positions.values())
            current_drawdown = portfolio_data.get('current_drawdown', 0.0)
            portfolio_volatility = risks['volatility'].get('portfolio_volatility', 0.1)
            correlation_score = risks['correlation'].get('score', 0.5)
            system_health = risks['performance'].get('score', 0.8)
            
            # Create risk status
            risk_status = RiskStatus(
                overall_risk_level=overall_risk,
                active_alerts_count=len(self.active_alerts),
                total_portfolio_exposure=total_exposure,
                current_drawdown=current_drawdown,
                portfolio_volatility=portfolio_volatility,
                correlation_risk_score=correlation_score,
                system_health_score=system_health,
                last_update=datetime.now(),
                emergency_stop_triggered=self.emergency_stop_active
            )
            
            # Cache risk status
            await self._cache_risk_status(risk_status)
            
            # Store analytics
            if self.supabase_service:
                asyncio.create_task(self._store_risk_analytics(risk_status, risks))
            
            return risk_status
            
        except Exception as e:
            logger.error(f"Portfolio risk assessment failed: {e}")
            return RiskStatus(
                overall_risk_level=RiskLevel.HIGH,
                active_alerts_count=len(self.active_alerts),
                total_portfolio_exposure=0.0,
                current_drawdown=0.0,
                portfolio_volatility=0.0,
                correlation_risk_score=0.0,
                system_health_score=0.0,
                last_update=datetime.now()
            )
    
    async def _assess_position_size_risk(
        self,
        current_positions: Dict[str, float],
        portfolio_value: float
    ) -> Dict[str, Any]:
        """Assess position size related risks"""
        
        risk_level = RiskLevel.LOW
        risk_score = 0.0
        alerts = []
        
        # Check individual position sizes
        for strategy, position in current_positions.items():
            position_pct = abs(position)
            
            if position_pct > self.thresholds.max_single_position:
                alert = RiskAlert(
                    alert_id=f"position_size_{strategy}_{int(time.time())}",
                    alert_type=AlertType.POSITION_SIZE,
                    risk_level=RiskLevel.CRITICAL,
                    title="Position Size Limit Exceeded",
                    message=f"Strategy {strategy} position ({position_pct:.1%}) exceeds maximum ({self.thresholds.max_single_position:.1%})",
                    current_value=position_pct,
                    threshold_value=self.thresholds.max_single_position,
                    strategy_name=strategy,
                    recommendations=[
                        f"Reduce {strategy} position to below {self.thresholds.max_single_position:.1%}",
                        "Consider rebalancing across other strategies",
                        "Review position sizing model parameters"
                    ]
                )
                alerts.append(alert)
                risk_level = max(risk_level, RiskLevel.CRITICAL)
                risk_score = max(risk_score, 0.9)
                
            elif position_pct > self.thresholds.position_size_warning:
                alert = RiskAlert(
                    alert_id=f"position_warning_{strategy}_{int(time.time())}",
                    alert_type=AlertType.POSITION_SIZE,
                    risk_level=RiskLevel.MEDIUM,
                    title="Position Size Warning",
                    message=f"Strategy {strategy} position ({position_pct:.1%}) approaching maximum",
                    current_value=position_pct,
                    threshold_value=self.thresholds.position_size_warning,
                    strategy_name=strategy,
                    recommendations=[
                        "Monitor position size closely",
                        "Consider partial position reduction"
                    ]
                )
                alerts.append(alert)
                risk_level = max(risk_level, RiskLevel.MEDIUM)
                risk_score = max(risk_score, 0.6)
        
        # Check total exposure
        total_exposure = sum(abs(pos) for pos in current_positions.values())
        if total_exposure > 0.95:  # 95% max total exposure
            alert = RiskAlert(
                alert_id=f"total_exposure_{int(time.time())}",
                alert_type=AlertType.PORTFOLIO_RISK,
                risk_level=RiskLevel.HIGH,
                title="High Portfolio Exposure",
                message=f"Total portfolio exposure ({total_exposure:.1%}) is very high",
                current_value=total_exposure,
                threshold_value=0.95,
                recommendations=[
                    "Reduce overall portfolio exposure",
                    "Maintain cash reserves for opportunities",
                    "Review risk management parameters"
                ]
            )
            alerts.append(alert)
            risk_level = max(risk_level, RiskLevel.HIGH)
            risk_score = max(risk_score, 0.8)
        
        return {
            'level': risk_level,
            'score': risk_score,
            'alerts': alerts,
            'total_exposure': total_exposure,
            'max_position': max(abs(pos) for pos in current_positions.values()) if current_positions else 0
        }
    
    async def _assess_drawdown_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess drawdown and loss related risks"""
        
        risk_level = RiskLevel.LOW
        risk_score = 0.0
        alerts = []
        
        current_drawdown = portfolio_data.get('current_drawdown', 0.0)
        daily_pnl = portfolio_data.get('daily_pnl', 0.0)
        
        # Check current drawdown
        if current_drawdown > self.thresholds.max_drawdown_critical:
            alert = RiskAlert(
                alert_id=f"drawdown_critical_{int(time.time())}",
                alert_type=AlertType.DRAWDOWN,
                risk_level=RiskLevel.CRITICAL,
                title="Critical Drawdown Level",
                message=f"Current drawdown ({current_drawdown:.1%}) exceeds critical threshold ({self.thresholds.max_drawdown_critical:.1%})",
                current_value=current_drawdown,
                threshold_value=self.thresholds.max_drawdown_critical,
                recommendations=[
                    "Consider emergency position reduction",
                    "Review and adjust risk management rules",
                    "Halt new position openings until recovery",
                    "Analyze drawdown causes and fix issues"
                ]
            )
            alerts.append(alert)
            risk_level = RiskLevel.CRITICAL
            risk_score = 0.95
            
        elif current_drawdown > self.thresholds.max_drawdown_warning:
            alert = RiskAlert(
                alert_id=f"drawdown_warning_{int(time.time())}",
                alert_type=AlertType.DRAWDOWN,
                risk_level=RiskLevel.MEDIUM,
                title="Drawdown Warning",
                message=f"Current drawdown ({current_drawdown:.1%}) approaching warning level",
                current_value=current_drawdown,
                threshold_value=self.thresholds.max_drawdown_warning,
                recommendations=[
                    "Monitor closely for further deterioration",
                    "Review position sizing and strategy allocation",
                    "Consider partial position reduction"
                ]
            )
            alerts.append(alert)
            risk_level = RiskLevel.MEDIUM
            risk_score = 0.6
        
        # Check daily loss limit
        if daily_pnl < -self.thresholds.daily_loss_limit:
            alert = RiskAlert(
                alert_id=f"daily_loss_{int(time.time())}",
                alert_type=AlertType.DRAWDOWN,
                risk_level=RiskLevel.HIGH,
                title="Daily Loss Limit Exceeded",
                message=f"Daily loss ({daily_pnl:.1%}) exceeds limit ({-self.thresholds.daily_loss_limit:.1%})",
                current_value=daily_pnl,
                threshold_value=-self.thresholds.daily_loss_limit,
                recommendations=[
                    "Stop trading for the day",
                    "Review what went wrong",
                    "Adjust position sizes for tomorrow"
                ]
            )
            alerts.append(alert)
            risk_level = max(risk_level, RiskLevel.HIGH)
            risk_score = max(risk_score, 0.8)
        
        # Emergency stop condition
        if current_drawdown > self.thresholds.emergency_drawdown:
            await self._trigger_emergency_stop(f"Drawdown exceeded emergency threshold: {current_drawdown:.1%}")
        
        return {
            'level': risk_level,
            'score': risk_score,
            'alerts': alerts,
            'current_drawdown': current_drawdown,
            'daily_pnl': daily_pnl
        }
    
    async def _assess_volatility_risk(self, strategies: List[str]) -> Dict[str, Any]:
        """Assess volatility related risks"""
        
        risk_level = RiskLevel.LOW
        risk_score = 0.0
        alerts = []
        volatility_data = {}
        
        # Check volatility for each strategy's market
        for strategy in strategies:
            try:
                # Mock market data for testing - in production would get real data
                market_data = MarketData(
                    symbol="BTC",  # Would be strategy-specific
                    price=50000.0,
                    volume=1000000,
                    timestamp=datetime.now()
                )
                
                vol_result = await self.volatility_calculator.calculate_volatility(
                    "BTC", market_data, 60
                )
                
                volatility_data[strategy] = vol_result
                
                # Check volatility levels
                if vol_result.current_volatility > self.thresholds.volatility_extreme_threshold:
                    alert = RiskAlert(
                        alert_id=f"volatility_extreme_{strategy}_{int(time.time())}",
                        alert_type=AlertType.VOLATILITY,
                        risk_level=RiskLevel.CRITICAL,
                        title="Extreme Volatility Detected",
                        message=f"Volatility for {strategy} ({vol_result.current_volatility:.1%}) is extremely high",
                        current_value=vol_result.current_volatility,
                        threshold_value=self.thresholds.volatility_extreme_threshold,
                        strategy_name=strategy,
                        metadata={'volatility_regime': vol_result.volatility_regime},
                        recommendations=[
                            "Reduce position sizes significantly",
                            "Increase stop-loss levels",
                            "Consider halting new positions"
                        ]
                    )
                    alerts.append(alert)
                    risk_level = max(risk_level, RiskLevel.CRITICAL)
                    risk_score = max(risk_score, 0.9)
                    
                elif vol_result.current_volatility > self.thresholds.volatility_high_threshold:
                    alert = RiskAlert(
                        alert_id=f"volatility_high_{strategy}_{int(time.time())}",
                        alert_type=AlertType.VOLATILITY,
                        risk_level=RiskLevel.MEDIUM,
                        title="High Volatility Warning",
                        message=f"Volatility for {strategy} ({vol_result.current_volatility:.1%}) is elevated",
                        current_value=vol_result.current_volatility,
                        threshold_value=self.thresholds.volatility_high_threshold,
                        strategy_name=strategy,
                        metadata={'volatility_regime': vol_result.volatility_regime},
                        recommendations=[
                            "Monitor position sizes",
                            "Adjust risk parameters",
                            "Increase monitoring frequency"
                        ]
                    )
                    alerts.append(alert)
                    risk_level = max(risk_level, RiskLevel.MEDIUM)
                    risk_score = max(risk_score, 0.6)
                
            except Exception as e:
                logger.warning(f"Volatility assessment failed for {strategy}: {e}")
        
        # Calculate portfolio volatility
        portfolio_volatility = np.mean([
            vol_data.current_volatility for vol_data in volatility_data.values()
        ]) if volatility_data else 0.1
        
        return {
            'level': risk_level,
            'score': risk_score,
            'alerts': alerts,
            'portfolio_volatility': portfolio_volatility,
            'strategy_volatilities': {k: v.current_volatility for k, v in volatility_data.items()}
        }
    
    async def _assess_correlation_risk(
        self,
        strategies: List[str],
        current_positions: Dict[str, float]
    ) -> Dict[str, Any]:
        """Assess correlation related risks"""
        
        risk_level = RiskLevel.LOW
        risk_score = 0.0
        alerts = []
        
        try:
            # Calculate correlation matrix
            correlation_matrix = await self.correlation_calculator.calculate_correlation_matrix(strategies)
            
            # Check correlation regime
            if correlation_matrix.correlation_regime == "extreme":
                alert = RiskAlert(
                    alert_id=f"correlation_extreme_{int(time.time())}",
                    alert_type=AlertType.CORRELATION,
                    risk_level=RiskLevel.HIGH,
                    title="Extreme Correlation Regime",
                    message=f"Strategies showing extreme correlation (avg: {correlation_matrix.avg_correlation:.1%})",
                    current_value=correlation_matrix.avg_correlation,
                    threshold_value=0.9,
                    metadata={'correlation_regime': correlation_matrix.correlation_regime},
                    recommendations=[
                        "Reduce correlated positions",
                        "Seek diversification opportunities",
                        "Review strategy selection"
                    ]
                )
                alerts.append(alert)
                risk_level = RiskLevel.HIGH
                risk_score = 0.8
                
            elif correlation_matrix.correlation_regime == "high":
                alert = RiskAlert(
                    alert_id=f"correlation_high_{int(time.time())}",
                    alert_type=AlertType.CORRELATION,
                    risk_level=RiskLevel.MEDIUM,
                    title="High Correlation Warning",
                    message=f"Strategies showing high correlation (avg: {correlation_matrix.avg_correlation:.1%})",
                    current_value=correlation_matrix.avg_correlation,
                    threshold_value=0.7,
                    metadata={'correlation_regime': correlation_matrix.correlation_regime},
                    recommendations=[
                        "Monitor correlation levels",
                        "Consider position adjustments"
                    ]
                )
                alerts.append(alert)
                risk_level = max(risk_level, RiskLevel.MEDIUM)
                risk_score = max(risk_score, 0.6)
            
            # Check diversification ratio
            if correlation_matrix.diversification_ratio < self.thresholds.diversification_ratio_min:
                alert = RiskAlert(
                    alert_id=f"diversification_low_{int(time.time())}",
                    alert_type=AlertType.CORRELATION,
                    risk_level=RiskLevel.MEDIUM,
                    title="Low Diversification",
                    message=f"Portfolio diversification ratio ({correlation_matrix.diversification_ratio:.2f}) is low",
                    current_value=correlation_matrix.diversification_ratio,
                    threshold_value=self.thresholds.diversification_ratio_min,
                    recommendations=[
                        "Add uncorrelated strategies",
                        "Rebalance strategy weights",
                        "Review correlation patterns"
                    ]
                )
                alerts.append(alert)
                risk_level = max(risk_level, RiskLevel.MEDIUM)
                risk_score = max(risk_score, 0.6)
            
            return {
                'level': risk_level,
                'score': risk_score,
                'alerts': alerts,
                'avg_correlation': correlation_matrix.avg_correlation,
                'diversification_ratio': correlation_matrix.diversification_ratio,
                'correlation_regime': correlation_matrix.correlation_regime
            }
            
        except Exception as e:
            logger.error(f"Correlation risk assessment failed: {e}")
            return {
                'level': RiskLevel.MEDIUM,
                'score': 0.5,
                'alerts': [],
                'avg_correlation': 0.5,
                'diversification_ratio': 1.0,
                'correlation_regime': 'unknown'
            }
    
    async def _assess_system_performance_risk(self) -> Dict[str, Any]:
        """Assess system performance related risks"""
        
        risk_level = RiskLevel.LOW
        risk_score = 0.0
        alerts = []
        
        # Check execution times
        if self.monitoring_cycle_times:
            avg_cycle_time = np.mean(self.monitoring_cycle_times)
            if avg_cycle_time > self.thresholds.execution_time_warning:
                risk_level = RiskLevel.MEDIUM
                risk_score = 0.6
        
        # Check cache performance
        try:
            redis_stats = await self.redis_service.get_cache_stats()
            hit_rate = redis_stats.get('cache_hit_rate', 0)
            
            if hit_rate < self.thresholds.cache_hit_rate_min:
                alert = RiskAlert(
                    alert_id=f"cache_performance_{int(time.time())}",
                    alert_type=AlertType.PERFORMANCE_DEGRADATION,
                    risk_level=RiskLevel.MEDIUM,
                    title="Cache Performance Degraded",
                    message=f"Cache hit rate ({hit_rate:.1f}%) below threshold ({self.thresholds.cache_hit_rate_min}%)",
                    current_value=hit_rate / 100,
                    threshold_value=self.thresholds.cache_hit_rate_min / 100,
                    recommendations=[
                        "Check Redis connectivity",
                        "Review cache TTL settings",
                        "Monitor system resources"
                    ]
                )
                alerts.append(alert)
                risk_level = max(risk_level, RiskLevel.MEDIUM)
                risk_score = max(risk_score, 0.6)
                
        except Exception as e:
            logger.warning(f"Cache performance check failed: {e}")
        
        # System health score
        health_score = 1.0 - (risk_score * 0.5)  # Convert risk to health score
        
        return {
            'level': risk_level,
            'score': health_score,
            'alerts': alerts,
            'avg_cycle_time': np.mean(self.monitoring_cycle_times) if self.monitoring_cycle_times else 0,
            'consecutive_failures': self.consecutive_failures
        }
    
    def _calculate_overall_risk_level(self, risks: Dict[str, Dict]) -> RiskLevel:
        """Calculate overall portfolio risk level"""
        
        risk_levels = [risk_data['level'] for risk_data in risks.values()]
        
        # Priority order: EMERGENCY > CRITICAL > HIGH > MEDIUM > LOW
        if RiskLevel.EMERGENCY in risk_levels:
            return RiskLevel.EMERGENCY
        elif RiskLevel.CRITICAL in risk_levels:
            return RiskLevel.CRITICAL
        elif RiskLevel.HIGH in risk_levels:
            return RiskLevel.HIGH
        elif RiskLevel.MEDIUM in risk_levels:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    async def _process_risk_alerts(self, risk_status: RiskStatus) -> None:
        """Process and send risk alerts"""
        
        # Get all current alerts from risk assessments
        all_alerts = []
        
        # Collect alerts from all risk categories (would be passed from assess_portfolio_risk)
        # For now, create alert based on overall risk level
        if risk_status.overall_risk_level != RiskLevel.LOW:
            alert = RiskAlert(
                alert_id=f"overall_risk_{int(time.time())}",
                alert_type=AlertType.PORTFOLIO_RISK,
                risk_level=risk_status.overall_risk_level,
                title=f"{risk_status.overall_risk_level.value.title()} Risk Level",
                message=f"Portfolio risk elevated to {risk_status.overall_risk_level.value} level",
                current_value=float(risk_status.overall_risk_level.value == "high"),
                threshold_value=0.5,
                metadata={
                    'portfolio_exposure': risk_status.total_portfolio_exposure,
                    'current_drawdown': risk_status.current_drawdown,
                    'correlation_risk': risk_status.correlation_risk_score
                },
                recommendations=self._get_risk_level_recommendations(risk_status.overall_risk_level)
            )
            all_alerts.append(alert)
        
        # Process each alert
        for alert in all_alerts:
            await self._process_single_alert(alert)
    
    async def _process_single_alert(self, alert: RiskAlert) -> None:
        """Process a single risk alert"""
        
        # Check if this is a duplicate alert (within cooldown period)
        if await self._is_duplicate_alert(alert):
            return
        
        # Add to active alerts
        self.active_alerts[alert.alert_id] = alert
        
        # Send Telegram notification
        if self.telegram_enabled:
            await self._send_telegram_alert(alert)
        
        # Store alert in history
        self.alert_history.append(alert)
        if len(self.alert_history) > 1000:  # Keep last 1000 alerts
            self.alert_history.pop(0)
        
        # Cache active alerts
        await self._cache_active_alerts()
        
        logger.warning(f"Risk alert generated: {alert.title} - {alert.message}")
    
    async def _send_telegram_alert(self, alert: RiskAlert) -> None:
        """Send alert via Telegram bot"""
        
        if not self.telegram_enabled:
            return
        
        try:
            start_time = time.perf_counter()
            
            # Format alert message
            message = self._format_telegram_message(alert)
            
            # Send via Telegram API (using aiohttp)
            import aiohttp
            
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                'chat_id': self.telegram_chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        delivery_time = (time.perf_counter() - start_time) * 1000
                        self.alert_delivery_times.append(delivery_time)
                        
                        if delivery_time > 5000:  # 5 second target
                            logger.warning(f"Telegram alert delivery exceeded target: {delivery_time:.1f}ms")
                        else:
                            logger.info(f"Telegram alert delivered in {delivery_time:.1f}ms ✓")
                    else:
                        logger.error(f"Telegram alert delivery failed: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to send Telegram alert: {e}")
    
    def _format_telegram_message(self, alert: RiskAlert) -> str:
        """Format alert for Telegram message"""
        
        # Risk level emoji mapping
        level_emojis = {
            RiskLevel.LOW: "🟢",
            RiskLevel.MEDIUM: "🟡",
            RiskLevel.HIGH: "🟠",
            RiskLevel.CRITICAL: "🔴",
            RiskLevel.EMERGENCY: "🚨"
        }
        
        emoji = level_emojis.get(alert.risk_level, "⚠️")
        
        message = f"{emoji} *{alert.title}*\n\n"
        message += f"{alert.message}\n\n"
        
        if alert.strategy_name:
            message += f"Strategy: `{alert.strategy_name}`\n"
        
        if alert.symbol:
            message += f"Symbol: `{alert.symbol}`\n"
        
        message += f"Current: `{alert.current_value:.3f}`\n"
        message += f"Threshold: `{alert.threshold_value:.3f}`\n"
        message += f"Time: `{alert.timestamp.strftime('%H:%M:%S')}`\n"
        
        if alert.recommendations:
            message += "\n*Recommendations:*\n"
            for i, rec in enumerate(alert.recommendations[:3], 1):  # Max 3 recommendations
                message += f"{i}. {rec}\n"
        
        return message
    
    def _get_risk_level_recommendations(self, risk_level: RiskLevel) -> List[str]:
        """Get recommendations based on risk level"""
        
        recommendations = {
            RiskLevel.LOW: [
                "Continue current risk management approach",
                "Monitor for emerging risks"
            ],
            RiskLevel.MEDIUM: [
                "Review position sizes and adjust if necessary",
                "Increase monitoring frequency",
                "Check correlation levels"
            ],
            RiskLevel.HIGH: [
                "Reduce position sizes immediately",
                "Review and strengthen risk controls",
                "Consider halting new positions"
            ],
            RiskLevel.CRITICAL: [
                "Immediate position size reduction required",
                "Emergency review of all open positions",
                "Halt all new trading until risks are reduced"
            ],
            RiskLevel.EMERGENCY: [
                "EMERGENCY: Stop all trading immediately",
                "Close risky positions",
                "Full system review required"
            ]
        }
        
        return recommendations.get(risk_level, ["Review risk management procedures"])
    
    async def _trigger_emergency_stop(self, reason: str) -> None:
        """Trigger emergency stop mechanism"""
        
        if self.emergency_stop_active:
            return  # Already triggered
        
        self.emergency_stop_active = True
        
        # Create emergency alert
        emergency_alert = RiskAlert(
            alert_id=f"emergency_stop_{int(time.time())}",
            alert_type=AlertType.SYSTEM_ERROR,
            risk_level=RiskLevel.EMERGENCY,
            title="🚨 EMERGENCY STOP TRIGGERED",
            message=f"Emergency stop activated: {reason}",
            current_value=1.0,
            threshold_value=0.0,
            recommendations=[
                "All trading halted immediately",
                "Manual intervention required",
                "System must be reviewed before restart"
            ]
        )
        
        # Send emergency notification
        if self.telegram_enabled:
            await self._send_telegram_alert(emergency_alert)
        
        # Cache emergency stop status
        await self.redis_service.setex(
            self.EMERGENCY_STOP_KEY,
            3600,  # 1 hour
            json.dumps({
                'active': True,
                'reason': reason,
                'timestamp': datetime.now().isoformat()
            })
        )
        
        logger.critical(f"EMERGENCY STOP TRIGGERED: {reason}")
    
    async def _is_duplicate_alert(self, alert: RiskAlert) -> bool:
        """Check if alert is a duplicate within cooldown period"""
        
        cooldown_minutes = 5  # 5 minute cooldown for same alert type
        
        last_alert_time = self.last_alert_times.get(alert.alert_type)
        if last_alert_time:
            time_since_last = datetime.now() - last_alert_time
            if time_since_last < timedelta(minutes=cooldown_minutes):
                return True
        
        self.last_alert_times[alert.alert_type] = datetime.now()
        return False
    
    async def _create_performance_alert(
        self,
        title: str,
        message: str,
        current_value: float,
        threshold_value: float
    ) -> None:
        """Create a performance-related alert"""
        
        alert = RiskAlert(
            alert_id=f"performance_{int(time.time())}",
            alert_type=AlertType.PERFORMANCE_DEGRADATION,
            risk_level=RiskLevel.MEDIUM,
            title=title,
            message=message,
            current_value=current_value,
            threshold_value=threshold_value,
            recommendations=[
                "Check system resources",
                "Review performance bottlenecks",
                "Consider scaling infrastructure"
            ]
        )
        
        await self._process_single_alert(alert)
    
    # Cache and storage methods
    
    async def _cache_risk_status(self, risk_status: RiskStatus) -> None:
        """Cache current risk status"""
        try:
            await self.redis_service.setex(
                self.RISK_STATUS_KEY,
                300,  # 5 minutes
                json.dumps(asdict(risk_status), default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache risk status: {e}")
    
    async def _cache_active_alerts(self) -> None:
        """Cache active alerts"""
        try:
            alerts_data = {
                alert_id: asdict(alert) for alert_id, alert in self.active_alerts.items()
            }
            await self.redis_service.setex(
                self.ACTIVE_ALERTS_KEY,
                3600,  # 1 hour
                json.dumps(alerts_data, default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache active alerts: {e}")
    
    async def _store_risk_analytics(
        self,
        risk_status: RiskStatus,
        risk_breakdown: Dict[str, Dict]
    ) -> None:
        """Store risk analytics in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            analytics_data = {
                'timestamp': risk_status.last_update.isoformat(),
                'overall_risk_level': risk_status.overall_risk_level.value,
                'active_alerts_count': risk_status.active_alerts_count,
                'portfolio_exposure': risk_status.total_portfolio_exposure,
                'current_drawdown': risk_status.current_drawdown,
                'portfolio_volatility': risk_status.portfolio_volatility,
                'correlation_risk_score': risk_status.correlation_risk_score,
                'system_health_score': risk_status.system_health_score,
                'emergency_stop_active': risk_status.emergency_stop_triggered,
                'risk_breakdown': risk_breakdown
            }
            
            # Store in risk_analytics table (would need to be created)
            # await self.supabase_service.store_risk_analytics(analytics_data)
            
        except Exception as e:
            logger.error(f"Failed to store risk analytics: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring"""
        
        avg_delivery_time = np.mean(self.alert_delivery_times) if self.alert_delivery_times else 0
        avg_cycle_time = np.mean(self.monitoring_cycle_times) if self.monitoring_cycle_times else 0
        
        return {
            'active_alerts_count': len(self.active_alerts),
            'total_alerts_generated': len(self.alert_history),
            'avg_alert_delivery_time_ms': round(avg_delivery_time, 2),
            'avg_monitoring_cycle_time_ms': round(avg_cycle_time, 2),
            'telegram_enabled': self.telegram_enabled,
            'emergency_stop_active': self.emergency_stop_active,
            'consecutive_failures': self.consecutive_failures,
            'alert_delivery_target_met': (
                sum(1 for t in self.alert_delivery_times if t < 5000) /
                max(1, len(self.alert_delivery_times)) * 100
            ) if self.alert_delivery_times else 100
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on risk monitor"""
        try:
            # Test Telegram connectivity
            telegram_healthy = False
            if self.telegram_enabled:
                try:
                    import aiohttp
                    url = f"https://api.telegram.org/bot{self.telegram_bot_token}/getMe"
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url) as response:
                            telegram_healthy = response.status == 200
                except:
                    telegram_healthy = False
            
            # Test Redis connectivity
            redis_stats = await self.redis_service.get_cache_stats()
            redis_healthy = bool(redis_stats)
            
            performance_stats = self.get_performance_stats()
            
            return {
                'status': 'healthy' if redis_healthy and (not self.telegram_enabled or telegram_healthy) else 'degraded',
                'redis_healthy': redis_healthy,
                'telegram_healthy': telegram_healthy,
                'emergency_stop_active': self.emergency_stop_active,
                'performance_stats': performance_stats,
                'thresholds': asdict(self.thresholds)
            }
            
        except Exception as e:
            logger.error(f"Risk monitor health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }

# Factory function for easy initialization
async def create_risk_monitor(
    redis_url: str,
    telegram_bot_token: Optional[str] = None,
    telegram_chat_id: Optional[str] = None,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    thresholds: Optional[RiskThresholds] = None
) -> RiskMonitor:
    """Factory function to create risk monitor with all dependencies"""
    
    from app.services.volatility_calculator import create_volatility_calculator
    from app.services.correlation_calculator import create_correlation_calculator
    from app.strategies.position_size_calculator import create_position_size_calculator
    from app.services.mcp.redis_service import RedisService
    
    # Initialize all dependencies
    volatility_calculator = await create_volatility_calculator(redis_url, supabase_url, supabase_key)
    correlation_calculator = await create_correlation_calculator(redis_url, supabase_url, supabase_key)
    position_calculator = await create_position_size_calculator(redis_url, supabase_url, supabase_key)
    
    redis_service = RedisService(redis_url)
    await redis_service.connect()
    
    supabase_service = None
    if supabase_url and supabase_key:
        from app.services.mcp.supabase_service import SupabaseService
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return RiskMonitor(
        volatility_calculator=volatility_calculator,
        correlation_calculator=correlation_calculator,
        position_calculator=position_calculator,
        redis_service=redis_service,
        supabase_service=supabase_service,
        telegram_bot_token=telegram_bot_token,
        telegram_chat_id=telegram_chat_id,
        thresholds=thresholds
    )

# Example usage and testing
if __name__ == "__main__":
    async def test_risk_monitor():
        """Test the risk monitor functionality"""
        
        # Initialize risk monitor
        monitor = await create_risk_monitor(
            redis_url="redis://localhost:6379",
            telegram_bot_token="your_bot_token",  # Replace with actual token
            telegram_chat_id="your_chat_id"       # Replace with actual chat ID
        )
        
        # Test risk assessment
        strategies = ["GridStrategy", "TechnicalAnalysisStrategy", "TrendFollowingStrategy"]
        portfolio_data = {
            'positions': {
                'GridStrategy': 0.3,
                'TechnicalAnalysisStrategy': 0.25,
                'TrendFollowingStrategy': 0.2
            },
            'total_value': 100000,
            'current_drawdown': 0.08,  # 8% drawdown
            'daily_pnl': -0.02  # -2% daily loss
        }
        
        # Assess portfolio risk
        risk_status = await monitor.assess_portfolio_risk(strategies, portfolio_data)
        
        print(f"Risk Assessment Result:")
        print(f"  Overall Risk Level: {risk_status.overall_risk_level.value}")
        print(f"  Active Alerts: {risk_status.active_alerts_count}")
        print(f"  Portfolio Exposure: {risk_status.total_portfolio_exposure:.1%}")
        print(f"  Current Drawdown: {risk_status.current_drawdown:.1%}")
        print(f"  Portfolio Volatility: {risk_status.portfolio_volatility:.1%}")
        print(f"  System Health: {risk_status.system_health_score:.2f}")
        print(f"  Emergency Stop: {risk_status.emergency_stop_triggered}")
        
        # Get performance stats
        stats = monitor.get_performance_stats()
        print(f"\nPerformance Stats: {stats}")
        
        # Health check
        health = await monitor.health_check()
        print(f"\nHealth Check: {health}")
    
    # Run test
    asyncio.run(test_risk_monitor())