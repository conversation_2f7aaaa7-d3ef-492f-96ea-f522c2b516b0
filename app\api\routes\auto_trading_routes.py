"""
Auto Trading API Routes - RESTful endpoints for the Auto Trading Controller system.
Provides complete CRUD operations for trading sessions and real-time monitoring.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pydantic import BaseModel, Field

from app.services.auto_trading_controller import (
    AutoTradingController, 
    TradingParameters, 
    TradingSessionStatus
)
# from app.dependencies import get_current_user  # Assuming auth dependency exists

# Temporary stub for get_current_user until authentication is implemented
def get_current_user():
    return {"user_id": "admin", "username": "admin"}

logger = logging.getLogger(__name__)

# Router setup
router = APIRouter(prefix="/api/trading", tags=["auto-trading"])

# Request/Response Models
class StartTradingRequest(BaseModel):
    """Request model for starting a trading session"""
    session_name: Optional[str] = Field(None, description="Optional session name")
    
    # Risk Management
    max_position_size: float = Field(0.1, ge=0.01, le=0.5, description="Maximum position size (0.01-0.5)")
    portfolio_exposure_limit: float = Field(0.8, ge=0.1, le=1.0, description="Portfolio exposure limit")
    max_drawdown_limit: float = Field(0.15, ge=0.05, le=0.5, description="Maximum drawdown limit")
    stop_loss_pct: float = Field(0.02, ge=0.005, le=0.1, description="Stop loss percentage")
    take_profit_pct: float = Field(0.05, ge=0.01, le=0.2, description="Take profit percentage")
    
    # Strategy Settings
    grid_strategy_enabled: bool = Field(True, description="Enable grid strategy")
    ta_strategy_enabled: bool = Field(True, description="Enable technical analysis strategy")
    trend_strategy_enabled: bool = Field(True, description="Enable trend following strategy")
    min_confidence_threshold: float = Field(0.6, ge=0.1, le=1.0, description="Minimum confidence threshold")
    
    # Execution Settings
    order_type: str = Field("MARKET", pattern="^(MARKET|LIMIT)$", description="Order type")
    slippage_tolerance: float = Field(0.001, ge=0.0001, le=0.01, description="Slippage tolerance")
    execution_speed: str = Field("FAST", pattern="^(FAST|NORMAL|CAREFUL)$", description="Execution speed")
    
    # ML Settings
    model_refresh_frequency: int = Field(300, ge=60, le=3600, description="Model refresh frequency in seconds")
    weight_confidence_threshold: float = Field(0.7, ge=0.1, le=1.0, description="Weight confidence threshold")
    enable_dynamic_rebalancing: bool = Field(True, description="Enable dynamic rebalancing")
    
    # Monitoring Settings
    alert_thresholds: Optional[Dict[str, float]] = Field(None, description="Custom alert thresholds")
    reporting_frequency: int = Field(60, ge=10, le=300, description="Reporting frequency in seconds")
    telegram_alerts_enabled: bool = Field(True, description="Enable Telegram alerts")
    
    # Trading Symbols
    symbols: List[str] = Field(["BTCUSDT", "ETHUSDT"], description="Trading symbols")
    base_currency: str = Field("USDT", description="Base currency")

class StopTradingRequest(BaseModel):
    """Request model for stopping a trading session"""
    emergency: bool = Field(False, description="Emergency stop flag")
    reason: Optional[str] = Field(None, description="Reason for stopping")

class TradingStatusResponse(BaseModel):
    """Response model for trading status"""
    session_active: bool
    session_id: Optional[str] = None
    status: str
    start_time: Optional[str] = None
    duration_seconds: Optional[float] = None
    message: Optional[str] = None

class SessionListResponse(BaseModel):
    """Response model for session list"""
    sessions: List[Dict[str, Any]]
    pagination: Dict[str, Any]

# Global controller instance (would be injected in production)
_trading_controller: Optional[AutoTradingController] = None

def get_trading_controller() -> AutoTradingController:
    """Dependency to get trading controller instance"""
    global _trading_controller
    if _trading_controller is None:
        raise HTTPException(
            status_code=503, 
            detail="Auto Trading Controller not initialized"
        )
    return _trading_controller

def set_trading_controller(controller: AutoTradingController):
    """Set the global trading controller instance"""
    global _trading_controller
    _trading_controller = controller

# API Endpoints

@router.post("/start", response_model=Dict[str, Any])
async def start_auto_trading(
    request: StartTradingRequest,
    background_tasks: BackgroundTasks,
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Start a new auto trading session with the complete ensemble pipeline.
    
    This endpoint initializes and starts the full end-to-end trading system:
    - Market data collection and cross-validation
    - Strategy ensemble execution (Grid, TA, Trend)
    - ML-based weight optimization
    - Risk management and position sizing
    - Trade execution and monitoring
    - Real-time analytics and alerts
    """
    try:
        # Convert request to TradingParameters
        parameters = TradingParameters(
            max_position_size=request.max_position_size,
            portfolio_exposure_limit=request.portfolio_exposure_limit,
            max_drawdown_limit=request.max_drawdown_limit,
            stop_loss_pct=request.stop_loss_pct,
            take_profit_pct=request.take_profit_pct,
            grid_strategy_enabled=request.grid_strategy_enabled,
            ta_strategy_enabled=request.ta_strategy_enabled,
            trend_strategy_enabled=request.trend_strategy_enabled,
            min_confidence_threshold=request.min_confidence_threshold,
            order_type=request.order_type,
            slippage_tolerance=request.slippage_tolerance,
            execution_speed=request.execution_speed,
            model_refresh_frequency=request.model_refresh_frequency,
            weight_confidence_threshold=request.weight_confidence_threshold,
            enable_dynamic_rebalancing=request.enable_dynamic_rebalancing,
            alert_thresholds=request.alert_thresholds,
            reporting_frequency=request.reporting_frequency,
            telegram_alerts_enabled=request.telegram_alerts_enabled,
            symbols=request.symbols,
            base_currency=request.base_currency
        )
        
        # Start trading session
        session_id = await controller.start_trading_session(
            parameters=parameters,
            session_name=request.session_name
        )
        
        logger.info(f"Auto trading session started by user {current_user.get('username', 'unknown')}: {session_id}")
        
        return {
            "success": True,
            "message": "Auto trading session started successfully",
            "session_id": session_id,
            "start_time": datetime.now().isoformat(),
            "parameters": request.dict()
        }
        
    except Exception as e:
        logger.error(f"Failed to start auto trading: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to start auto trading: {str(e)}"
        )

@router.post("/stop", response_model=Dict[str, Any])
async def stop_auto_trading(
    request: StopTradingRequest,
    background_tasks: BackgroundTasks,
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Stop the current auto trading session.
    
    Performs either graceful or emergency stop:
    - Graceful: Closes positions, completes pending trades, generates report
    - Emergency: Immediate halt with position closing and cleanup
    """
    try:
        # Stop trading session
        session_report = await controller.stop_trading_session(
            emergency=request.emergency
        )
        
        stop_type = "Emergency" if request.emergency else "Graceful"
        logger.info(f"{stop_type} stop completed by user {current_user.get('username', 'unknown')}")
        
        return {
            "success": True,
            "message": f"Auto trading stopped successfully ({stop_type.lower()})",
            "stop_time": datetime.now().isoformat(),
            "session_report": session_report,
            "emergency_stop": request.emergency,
            "reason": request.reason
        }
        
    except Exception as e:
        logger.error(f"Failed to stop auto trading: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to stop auto trading: {str(e)}"
        )

@router.get("/status", response_model=Dict[str, Any])
async def get_trading_status(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get comprehensive current trading status and real-time metrics.
    
    Returns:
    - Session status and duration
    - Real-time performance metrics
    - Current market data and strategy signals
    - Recent trades and alerts
    - System health information
    """
    try:
        status = await controller.get_session_status()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "user": current_user.get('username', 'unknown'),
            **status
        }
        
    except Exception as e:
        logger.error(f"Failed to get trading status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get trading status: {str(e)}"
        )

@router.get("/session/{session_id}", response_model=Dict[str, Any])
async def get_session_details(
    session_id: str,
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get detailed information about a specific trading session.
    
    Includes:
    - Complete session data and configuration
    - Performance summary and analytics
    - Full trade history and alerts
    - Strategy attribution analysis
    - Risk analysis and metrics
    """
    try:
        session_details = await controller.get_session_details(session_id)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "user": current_user.get('username', 'unknown'),
            **session_details
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get session details: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get session details: {str(e)}"
        )

@router.get("/sessions", response_model=SessionListResponse)
async def list_trading_sessions(
    limit: int = Query(50, ge=1, le=100, description="Number of sessions to return"),
    offset: int = Query(0, ge=0, description="Number of sessions to skip"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    List trading sessions with pagination and filtering.
    
    Supports:
    - Pagination with limit/offset
    - Status filtering (running, stopped, paused, error)
    - Sorted by start time (newest first)
    - Session summaries with key metrics
    """
    try:
        sessions_data = await controller.list_sessions(
            limit=limit,
            offset=offset,
            status_filter=status
        )
        
        return SessionListResponse(
            sessions=sessions_data["sessions"],
            pagination=sessions_data["pagination"]
        )
        
    except Exception as e:
        logger.error(f"Failed to list sessions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list sessions: {str(e)}"
        )

@router.post("/pause", response_model=Dict[str, Any])
async def pause_trading_session(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Pause the current trading session.
    
    Temporarily stops trading execution while maintaining session state.
    Market monitoring and risk management continue.
    """
    try:
        success = await controller.pause_session()
        
        if success:
            logger.info(f"Trading session paused by user {current_user.get('username', 'unknown')}")
            return {
                "success": True,
                "message": "Trading session paused successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="No active session to pause or session already paused"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to pause session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to pause session: {str(e)}"
        )

@router.post("/resume", response_model=Dict[str, Any])
async def resume_trading_session(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Resume a paused trading session.
    
    Restarts trading execution from the paused state.
    """
    try:
        success = await controller.resume_session()
        
        if success:
            logger.info(f"Trading session resumed by user {current_user.get('username', 'unknown')}")
            return {
                "success": True,
                "message": "Trading session resumed successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="No paused session to resume"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to resume session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to resume session: {str(e)}"
        )

@router.post("/emergency-stop", response_model=Dict[str, Any])
async def emergency_stop_trading(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Trigger emergency stop of all trading operations.
    
    Immediately halts all trading activity, closes positions, and stops the session.
    Use only in critical situations.
    """
    try:
        success = await controller.emergency_stop()
        
        if success:
            logger.critical(f"Emergency stop triggered by user {current_user.get('username', 'unknown')}")
            return {
                "success": True,
                "message": "Emergency stop completed successfully",
                "timestamp": datetime.now().isoformat(),
                "emergency": True
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Emergency stop failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Emergency stop failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Emergency stop failed: {str(e)}"
        )

# Additional endpoints for monitoring and management

@router.get("/health", response_model=Dict[str, Any])
async def get_system_health(
    controller: AutoTradingController = Depends(get_trading_controller)
):
    """
    Get system health status for the auto trading system.
    
    Returns health status of all components:
    - Trading controller
    - Market data feeds
    - Execution services
    - Monitoring systems
    - Cache and database connections
    """
    try:
        # This would implement comprehensive health checks
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "trading_controller": "healthy",
                "market_data": "healthy",
                "execution_service": "healthy",
                "redis_cache": "healthy",
                "supabase_db": "healthy",
                "ml_services": "healthy"
            },
            "uptime_seconds": 0,  # Would track actual uptime
            "memory_usage_mb": 0,  # Would track actual memory
            "cpu_usage_pct": 0     # Would track actual CPU
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/metrics", response_model=Dict[str, Any])
async def get_performance_metrics(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get detailed performance metrics for the current session.
    
    Returns comprehensive metrics including:
    - Financial performance (PnL, Sharpe ratio, drawdown)
    - Trading metrics (win rate, trade frequency, execution time)
    - Strategy attribution and correlation analysis
    - Risk metrics and exposure analysis
    - System performance (latency, cache hit rates)
    """
    try:
        status = await controller.get_session_status()
        
        if not status.get("session_active"):
            return {
                "message": "No active trading session",
                "session_active": False
            }
        
        # Extract and format performance metrics
        performance = status.get("performance", {})
        
        return {
            "timestamp": datetime.now().isoformat(),
            "session_id": status.get("session_id"),
            "duration_seconds": status.get("duration_seconds"),
            "financial_metrics": {
                "total_pnl": performance.get("total_pnl", 0),
                "total_return": performance.get("total_return", 0),
                "sharpe_ratio": performance.get("sharpe_ratio", 0),
                "max_drawdown": performance.get("max_drawdown", 0),
                "current_drawdown": performance.get("current_drawdown", 0),
                "volatility": performance.get("volatility", 0)
            },
            "trading_metrics": {
                "total_trades": performance.get("total_trades", 0),
                "win_rate": performance.get("win_rate", 0),
                "profit_factor": performance.get("profit_factor", 0),
                "avg_execution_time_ms": performance.get("avg_execution_time_ms", 0)
            },
            "strategy_attribution": performance.get("strategy_performance", {}),
            "current_weights": performance.get("strategy_weights", {}),
            "market_data": status.get("market_data", {}),
            "system_health": status.get("system_health", {})
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance metrics: {str(e)}"
        )

# Backward compatibility endpoints (to match existing frontend expectations)

@router.get("/auto-trading/status", response_model=TradingStatusResponse)
async def get_auto_trading_status_legacy(
    controller: AutoTradingController = Depends(get_trading_controller)
):
    """Legacy endpoint for auto trading status (backward compatibility)"""
    try:
        status = await controller.get_session_status()
        
        return TradingStatusResponse(
            session_active=status.get("session_active", False),
            session_id=status.get("session_id"),
            status=status.get("status", "NO_SESSION"),
            start_time=status.get("start_time"),
            duration_seconds=status.get("duration_seconds"),
            message=status.get("message")
        )
        
    except Exception as e:
        logger.error(f"Failed to get auto trading status: {e}")
        return TradingStatusResponse(
            session_active=False,
            status="ERROR",
            message=f"Error: {str(e)}"
        )

@router.post("/enable")
async def enable_auto_trading_legacy(
    symbol: str = "BTCUSDT",
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """Legacy endpoint for enabling auto trading (backward compatibility)"""
    try:
        # Use default parameters for legacy endpoint
        parameters = TradingParameters(symbols=[symbol])
        
        session_id = await controller.start_trading_session(
            parameters=parameters,
            session_name=f"Legacy session {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        return {"success": True, "session_id": session_id, "enabled": True}
        
    except Exception as e:
        logger.error(f"Failed to enable auto trading: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to enable auto trading: {str(e)}"
        )

@router.post("/disable")
async def disable_auto_trading_legacy(
    controller: AutoTradingController = Depends(get_trading_controller),
    current_user: Dict = Depends(get_current_user)
):
    """Legacy endpoint for disabling auto trading (backward compatibility)"""
    try:
        await controller.stop_trading_session(emergency=False)
        return {"success": True, "enabled": False}
        
    except Exception as e:
        logger.error(f"Failed to disable auto trading: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to disable auto trading: {str(e)}"
        )

# Note: Error handlers should be added at the FastAPI app level, not router level