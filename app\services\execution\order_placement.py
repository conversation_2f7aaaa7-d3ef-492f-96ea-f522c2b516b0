"""Order placement methods for the execution service."""
import logging
from typing import Optional, Union
from decimal import Decimal

from app.services.execution.models import Order, OrderType, OrderSide

logger = logging.getLogger(__name__)

class OrderPlacementMixin:
    """Mixin for execution service order placement methods."""

    async def place_market_entry_order(self, symbol: str, side: Union[OrderSide, str],
                                     quantity: Union[float, Decimal]) -> Optional[Decimal]:
        """Place a market entry order.

        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.

        Returns:
            The fill price if successful, None otherwise.
        """
        # Convert Decimal to float if needed
        if isinstance(quantity, Decimal):
            quantity = float(quantity)

        order = await self.order_manager.place_market_order(symbol, side, quantity)
        if order and order.average_price:
            return Decimal(str(order.average_price))
        return None

    async def place_limit_entry_order(self, symbol: str, side: Union[OrderSide, str],
                                    quantity: Union[float, Decimal], price: Union[float, Decimal]) -> Optional[Order]:
        """Place a limit entry order.

        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            price: The limit price.

        Returns:
            The created order if successful, None otherwise.
        """
        # Convert Decimal to float if needed
        if isinstance(quantity, Decimal):
            quantity = float(quantity)
        if isinstance(price, Decimal):
            price = float(price)

        return await self.order_manager.place_limit_order(symbol, side, quantity, price)

    async def place_stop_market_order(self, symbol: str, side: Union[OrderSide, str],
                                    quantity: Union[float, Decimal], stop_price: Union[float, Decimal]) -> Optional[Order]:
        """Place a stop market order.

        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            stop_price: The stop price.

        Returns:
            The created order if successful, None otherwise.
        """
        # Convert Decimal to float if needed
        if isinstance(quantity, Decimal):
            quantity = float(quantity)
        if isinstance(stop_price, Decimal):
            stop_price = float(stop_price)

        return await self.order_manager.place_stop_market_order(symbol, side, quantity, stop_price)

    async def place_take_profit_market_order(self, symbol: str, side: Union[OrderSide, str],
                                          quantity: Union[float, Decimal], stop_price: Union[float, Decimal]) -> Optional[Order]:
        """Place a take profit market order.

        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            stop_price: The stop price.

        Returns:
            The created order if successful, None otherwise.
        """
        # Convert Decimal to float if needed
        if isinstance(quantity, Decimal):
            quantity = float(quantity)
        if isinstance(stop_price, Decimal):
            stop_price = float(stop_price)

        return await self.order_manager.place_take_profit_market_order(symbol, side, quantity, stop_price)

    async def place_entry_order_with_sl_tp(self, symbol: str, side: Union[OrderSide, str],
                                         quantity: Union[float, Decimal],
                                         order_type: Union[OrderType, str] = OrderType.MARKET,
                                         entry_price: Optional[Union[float, Decimal]] = None,
                                         stop_loss_price: Optional[Union[float, Decimal]] = None,
                                         take_profit_price: Optional[Union[float, Decimal]] = None,
                                         client_order_id: Optional[str] = None) -> Optional[Order]:
        """Place an entry order with optional stop loss and take profit orders.

        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            order_type: MARKET or LIMIT for the entry.
            entry_price: Required if order_type is LIMIT.
            stop_loss_price: Optional stop loss trigger price.
            take_profit_price: Optional take profit trigger price.
            client_order_id: Optional client order ID.

        Returns:
            The created order if successful, None otherwise.
        """
        # Convert Decimal to float if needed
        if isinstance(quantity, Decimal):
            quantity = float(quantity)
        if isinstance(entry_price, Decimal):
            entry_price = float(entry_price)
        if isinstance(stop_loss_price, Decimal):
            stop_loss_price = float(stop_loss_price)
        if isinstance(take_profit_price, Decimal):
            take_profit_price = float(take_profit_price)

        # Normalize side to string if enum
        if isinstance(side, OrderSide):
            side = side.value

        # Normalize order_type to string if enum
        if isinstance(order_type, OrderType):
            order_type = order_type.value

        # Validate params
        if order_type == OrderType.LIMIT.value and entry_price is None:
            raise ValueError("Entry price is required for LIMIT entry orders.")

        # Check if trade_manager is available
        if not self.trade_manager:
            logger.warning("Cannot place entry order with SL/TP: trade_manager is not initialized.")
            return None

        # Use the trade manager to place the entry order with SL/TP
        trade = await self.trade_manager.place_entry_order_with_sl_tp(
            symbol=symbol,
            side=side,
            quantity=quantity,
            entry_price=entry_price,
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price,
            client_order_id=client_order_id
        )

        if not trade:
            return None

        # Create an Order object to return for backward compatibility
        order = Order(
            symbol=symbol,
            side=OrderSide(side),
            order_type=OrderType.MARKET if order_type == OrderType.MARKET.value else OrderType.LIMIT,
            quantity=quantity,
            price=entry_price,
            client_order_id=client_order_id,
            exchange_order_id=trade.entry_order_id
        )

        if trade.entry_fill_price:
            order.update_execution(
                filled_quantity=float(trade.entry_fill_qty or 0),
                average_price=float(trade.entry_fill_price or 0)
            )

        return order
