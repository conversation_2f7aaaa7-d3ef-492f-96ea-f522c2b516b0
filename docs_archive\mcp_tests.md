# MCP Server Tests

This document provides information about the tests for the MCP (Model Context Protocol) servers used in the Crypto_App_V2 project.

## Overview

The MCP server tests use a Test-Driven Development (TDD) approach to verify the functionality of the MCP servers. The tests are designed to work with real data and real MCP servers, rather than using mocks or simulated data.

The tests are organized into three categories for each MCP server:

1. **Basic Tests**: Test basic functionality of the MCP server
2. **Advanced Tests**: Test more advanced features of the MCP server
3. **Integration Tests**: Test integration with other components of the system

## Test Files

### PIF (Personal Intelligence Framework) MCP Server Tests

1. **test_pif_mcp.js**: Tests basic PIF functionality
   - File operations (pwd, cd, read, write)
   - Basic reasoning tools
   - Journal creation and reading

2. **test_pif_mcp_advanced.js**: Tests advanced PIF functionality
   - Complex file operations (mkdir, delete, move, rename)
   - Advanced reasoning tools
   - Journal system with tags and search

3. **test_pif_mcp_integration.js**: Tests PIF integration with cryptocurrency trading
   - Using PIF for task management in the context of cryptocurrency trading
   - Storing and retrieving trading strategies
   - Documenting trading decisions

### Simple Vega-Lite MCP Server Tests

1. **test_vegalite_mcp.js**: Tests basic Vega-Lite functionality
   - Saving data to a named table
   - Creating a simple visualization

2. **test_vegalite_mcp_advanced.js**: Tests advanced Vega-Lite functionality
   - Creating complex visualizations with multiple layers
   - Using different chart types
   - Customizing visualization appearance

3. **test_vegalite_mcp_integration.js**: Tests Vega-Lite integration with cryptocurrency trading
   - Visualizing cryptocurrency trading data
   - Creating interactive dashboards
   - Integrating with trading strategies

### Test Runner

- **run_mcp_tests.js**: Runs all tests and reports results

## Running the Tests

To run all tests, use the following command:

```bash
node tests/mcp/run_mcp_tests.js
```

To run a specific test, use the following command:

```bash
node tests/mcp/test_pif_mcp.js
```

## Expected Results

When running the tests, you should see output similar to the following:

```
=================================================
Starting MCP Server Tests
=================================================

=================================================
Running PIF MCP Tests
=================================================

Starting PIF MCP basic test...

--- Testing File Operations ---
Testing pwd...
Current directory: /home/<USER>/pif-workspace
Testing cd...
Changed directory: Changed directory to home
Testing write...
Write result: Successfully wrote to test_file.txt
Testing read...
Read result: This is a test file created by PIF MCP test script.
✅ Read content matches write content

--- Testing Basic Reasoning Tools ---
Testing reason...
Reason result: Reasoning process completed successfully

--- Testing Journal System ---
Testing journal_create...
Journal create result: Journal entry "Test Journal Entry" created successfully
Testing journal_read...
Journal read result: [{"title":"Test Journal Entry","content":"This is a test journal entry created by PIF MCP test script.","tags":["test","pif","mcp"],"created":"2025-04-14T12:34:56.789Z"}]

--- Test Summary ---
✅ PIF MCP basic test completed successfully
All tests completed successfully

... (more test output) ...

=================================================
Test Results Summary
=================================================
pif_basic: ✅ PASSED
pif_advanced: ✅ PASSED
pif_integration: ✅ PASSED
vegalite_basic: ✅ PASSED
vegalite_advanced: ✅ PASSED
vegalite_integration: ✅ PASSED

=================================================
✅ All tests passed successfully!
=================================================
```

## Troubleshooting

If you encounter issues with the tests, try the following:

1. **Check that the MCP servers are running**: Make sure the PIF and Simple Vega-Lite MCP servers are running before running the tests.

2. **Check the MCP server logs**: Check the logs for the MCP servers to see if there are any errors.

3. **Check the test output**: The test output should provide information about what went wrong.

4. **Check the test files**: Make sure the test files are correctly implemented and that the helper functions are working as expected.

5. **Run individual tests**: Try running individual tests to isolate the issue.

## Extending the Tests

To add new tests or extend existing tests, follow these guidelines:

1. **Create a new test file**: Create a new test file in the `tests/mcp` directory.

2. **Import required modules**: Import the required modules at the top of the file.

3. **Define a test function**: Define a function that performs the test.

4. **Use helper functions**: Use helper functions to interact with the MCP servers.

5. **Run the test**: Add code to run the test if the file is executed directly.

6. **Export the test function**: Export the test function so it can be used by the test runner.

7. **Update the test runner**: Update the test runner to include the new test.

## Test Results

All tests have been run successfully. Here's a summary of the test results:

```
=================================================
Test Results Summary
=================================================
pif_basic: ✅ PASSED
pif_advanced: ✅ PASSED
pif_integration: ✅ PASSED
vegalite_basic: ✅ PASSED
vegalite_advanced: ✅ PASSED
vegalite_integration: ✅ PASSED

=================================================
✅ All tests passed successfully!
=================================================
```

## Conclusion

The MCP server tests provide a way to verify the functionality of the MCP servers used in the Crypto_App_V2 project. By using a Test-Driven Development approach with real data, we can ensure that the MCP servers are working correctly and that they integrate properly with the rest of the system.

The tests have confirmed that both the PIF (Personal Intelligence Framework) MCP server and the Simple Vega-Lite MCP server are functioning correctly and can be used in the Crypto_App_V2 project. The PIF MCP server provides task management, file operations, and structured reasoning capabilities, while the Simple Vega-Lite MCP server provides data visualization capabilities.
