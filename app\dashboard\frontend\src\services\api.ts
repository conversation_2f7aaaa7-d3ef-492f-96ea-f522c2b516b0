import { LoginCredentials, AuthResponse, MarketTicker, ManagedTrade, MLTrainingResponse, MLWeightsResponse, MLBacktestResponse, MLModelInfoResponse, MLStatusResponse, MLToggleResponse } from '../types'; // Keep only needed types
import apiClient from './apiService';

// Use the apiClient from apiService.ts which already has token handling

// Authentication API (kept because Login.tsx and ProtectedRoute are kept)
export const authAPI = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    try {
      const response = await apiClient.post<AuthResponse>('/token', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      return response.data;
    } catch (error: any) {
      // Simplified error handling for brevity
      const message = error.response?.data?.detail || error.response?.data || error.message || 'Login failed';
      throw new Error(typeof message === 'string' ? message : JSON.stringify(message));
    }
  },

  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post<AuthResponse>('/refresh-token', { refresh_token: refreshToken });
      return response.data;
    } catch (error: any) {
      // Simplified error handling for brevity
      const message = error.response?.data?.detail || error.response?.data || error.message || 'Token refresh failed';
      throw new Error(typeof message === 'string' ? message : JSON.stringify(message));
    }
  },
};

// Trading API - Minimal version for AutoTradeControl
export const tradingAPI = {
  // Assumes GET /trading/status returns { enabled: boolean }
  getAutoTradingStatus: async (): Promise<{ enabled: boolean }> => {
    const response = await apiClient.get<{ enabled: boolean }>('/api/trading/status');
    return response.data;
  },

  // Assumes POST /trading/enable takes { symbol: string } (optional?) and returns success/fail
  enableAutoTrading: async (symbol: string): Promise<any> => {
    // Backend API might not need the symbol, adjust if necessary
    const response = await apiClient.post('/api/trading/enable', { symbol });
    return response.data;
  },

  // Assumes POST /trading/disable returns success/fail
  disableAutoTrading: async (): Promise<any> => {
    const response = await apiClient.post('/api/trading/disable');
    return response.data;
  },
};

// Function to fetch account statistics
export const getAccountStatistics = async () => {
  try {
    // Assuming apiClient handles auth headers via interceptors
    const response = await apiClient.get('/api/account/statistics');
    return response.data; // Return the data part of the response
  } catch (error) {
    console.error('Error fetching account statistics:', error);
    // Re-throw the error or handle it as needed for the UI
    throw error;
  }
};

// Trading API - Additional endpoints
export const getActiveTrades = async (): Promise<ManagedTrade[]> => {
  const response = await apiClient.get<ManagedTrade[]>('/api/trading/active-trades');
  return response.data;
};

export const getRecentTrades = async (): Promise<ManagedTrade[]> => {
  const response = await apiClient.get<ManagedTrade[]>('/api/trading/recent-trades');
  return response.data;
};

// Market API
export const getMarketTicker = async (symbol: string): Promise<MarketTicker> => {
  const response = await apiClient.get<MarketTicker>(`/api/market/ticker?symbol=${encodeURIComponent(symbol)}`);
  return response.data;
};

// ML API
export const mlAPI = {
  trainModel: async (payload: any): Promise<MLTrainingResponse> => {
    const response = await apiClient.post<MLTrainingResponse>('/api/ml/train', payload);
    return response.data;
  },
  getOptimizedWeights: async (payload: any): Promise<MLWeightsResponse> => {
    const response = await apiClient.post<MLWeightsResponse>('/api/ml/weights', payload);
    return response.data;
  },
  backtestMLWeights: async (payload: any): Promise<MLBacktestResponse> => {
    const response = await apiClient.post<MLBacktestResponse>('/api/ml/backtest', payload);
    return response.data;
  },
  getModelInfo: async (): Promise<MLModelInfoResponse> => {
    const response = await apiClient.get<MLModelInfoResponse>('/api/ml/info');
    return response.data;
  },
  getMLStatus: async (): Promise<MLStatusResponse> => {
    const response = await apiClient.get<MLStatusResponse>('/api/ml/status');
    return response.data;
  },
  toggleMLOptimization: async (enabled: boolean): Promise<MLToggleResponse> => {
    const response = await apiClient.post<MLToggleResponse>('/api/ml/toggle', { enabled });
    return response.data;
  },
};

// Session Reports API
export const sessionReportsAPI = {
  getSessionsSummary: async (days: number, status?: string): Promise<any> => {
    const params = new URLSearchParams();
    params.append('days', days.toString());
    if (status) {
      params.append('status', status);
    }
    const response = await apiClient.get(`/api/sessions/summary?${params.toString()}`);
    return response.data;
  },

  getLiveSessionReport: async (): Promise<any> => {
    const response = await apiClient.get('/api/sessions/live');
    return response.data;
  },

  getSessionReport: async (sessionId: string): Promise<any> => {
    const response = await apiClient.get(`/api/sessions/${sessionId}/report`);
    return response.data;
  },

  getSessionAnalytics: async (sessionId: string): Promise<any> => {
    const response = await apiClient.get(`/api/sessions/${sessionId}/analytics`);
    return response.data;
  },
};

// Ensemble API
export const ensembleAPI = {
  getEnsembleData: async (): Promise<any> => {
    const response = await apiClient.get('/api/ensemble/data');
    return response.data;
  },

  getEnsembleStatus: async (): Promise<any> => {
    const response = await apiClient.get('/api/ensemble/status');
    return response.data;
  },

  updateEnsembleConfig: async (config: any): Promise<any> => {
    const response = await apiClient.post('/api/ensemble/config', config);
    return response.data;
  },
};

export { apiClient }; // Export the api instance