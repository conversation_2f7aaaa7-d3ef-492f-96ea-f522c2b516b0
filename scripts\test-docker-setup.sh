#!/bin/bash
# Test script for Docker setup validation

set -e

echo "🐳 Testing Docker Setup for Strategy Ensemble System"
echo "=================================================="

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not available. Please install Docker Desktop with WSL2 integration."
    echo "Visit: https://docs.docker.com/go/wsl2/"
    exit 1
fi

echo "✅ Docker is available"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not available. Please install docker-compose."
    exit 1
fi

echo "✅ docker-compose is available"

# Validate Dockerfile syntax
echo "🔍 Validating Dockerfile syntax..."
if docker build --target development -t ensemble-test-dev . --dry-run 2>/dev/null; then
    echo "✅ Dockerfile syntax is valid"
else
    echo "❌ Dockerfile syntax validation failed"
    exit 1
fi

# Validate docker-compose.yml syntax
echo "🔍 Validating docker-compose.yml syntax..."
if docker-compose config >/dev/null; then
    echo "✅ docker-compose.yml syntax is valid"
else
    echo "❌ docker-compose.yml syntax validation failed"
    exit 1
fi

# Test environment file
echo "🔍 Checking environment configuration..."
if [ -f ".env.example" ]; then
    echo "✅ Environment example file exists"
    echo "📝 Remember to copy .env.example to .env and configure your settings"
else
    echo "❌ Environment example file missing"
    exit 1
fi

# Check .dockerignore
if [ -f ".dockerignore" ]; then
    echo "✅ .dockerignore file exists"
else
    echo "❌ .dockerignore file missing"
    exit 1
fi

# Check CI/CD workflow
if [ -f ".github/workflows/ci-cd.yml" ]; then
    echo "✅ GitHub CI/CD workflow exists"
else
    echo "❌ GitHub CI/CD workflow missing"
    exit 1
fi

echo ""
echo "🎉 Docker setup validation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Copy .env.example to .env and configure your settings"
echo "2. Run 'docker-compose --profile dev up' for development"
echo "3. Run 'docker-compose --profile prod up' for production"
echo "4. Run 'docker-compose --profile test up' for testing"
echo "5. Run 'docker-compose --profile ml up' for ML training"
echo ""
echo "Available profiles:"
echo "  - dev: Development environment with hot reload"
echo "  - prod: Production environment with optimizations"
echo "  - test: Testing environment with coverage reports"
echo "  - ml: ML training environment with ZenML and MLflow"
echo ""
echo "Example commands:"
echo "  docker-compose --profile dev up -d        # Start development"
echo "  docker-compose --profile dev logs -f app-dev  # View logs"
echo "  docker-compose --profile dev down         # Stop services"
echo "  docker-compose --profile test run tests   # Run tests"
echo "  docker-compose --profile ml run ml-trainer # Train models"