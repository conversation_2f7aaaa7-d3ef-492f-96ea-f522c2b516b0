"""
Dependencies for FastAPI.

This module contains dependencies for FastAPI.
"""
import logging
from typing import Optional, Callable

from app.services.execution.execution_service import ExecutionService
from app.services.exchange.binance_client import BinanceExchangeClient
from app.config.settings import Settings
from app.database import get_db_session_factory as _get_db_session_factory

# Singleton instance of the execution service
_execution_service: Optional[ExecutionService] = None

logger = logging.getLogger(__name__)


# Singleton instances
_settings: Optional[Settings] = None
_exchange_client: Optional[BinanceExchangeClient] = None

def get_settings() -> Settings:
    """
    Get the settings instance.

    Returns:
        Settings: The settings instance.
    """
    global _settings

    if _settings is None:
        logger.info("Creating new settings instance")
        _settings = Settings()

    return _settings

def get_exchange_client() -> BinanceExchangeClient:
    """
    Get the exchange client instance.

    Returns:
        BinanceExchangeClient: The exchange client instance.
    """
    global _exchange_client

    if _exchange_client is None:
        logger.info("Creating new exchange client instance")
        settings = get_settings()
        _exchange_client = BinanceExchangeClient(settings)

    return _exchange_client

def get_execution_service() -> ExecutionService:
    """
    Get the execution service instance.

    Returns:
        ExecutionService: The execution service instance.
    """
    global _execution_service

    if _execution_service is None:
        logger.info("Creating new execution service instance")
        # Initialize execution service with all required parameters
        _execution_service = ExecutionService(
            exchange_client=get_exchange_client(),
            settings=get_settings(),
            db_session_factory=get_db_session_factory()
        )

    return _execution_service


def get_db_session_factory() -> Callable:
    """
    Get the database session factory.

    Returns:
        Callable: The database session factory.
    """
    return _get_db_session_factory()
