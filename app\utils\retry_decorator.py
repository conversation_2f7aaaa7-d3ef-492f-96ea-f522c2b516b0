"""Retry mechanism for API operations."""
import time
import logging
import functools
import traceback
from typing import Callable, Any, Optional, Dict, List, Tuple

logger = logging.getLogger(__name__)


class RetryError(Exception):
    """Exception raised when retry attempts fail."""
    
    def __init__(self, message: str, original_exception: Exception, attempts: int):
        """Initialize retry error.
        
        Args:
            message: Error message.
            original_exception: The original exception that caused the retry.
            attempts: Number of retry attempts made.
        """
        self.original_exception = original_exception
        self.attempts = attempts
        super().__init__(f"{message}. Original exception: {str(original_exception)}. Attempts: {attempts}")


def retry(max_retries: int = 3, 
         delay: float = 2.0, 
         backoff_factor: float = 1.5, 
         exceptions_to_retry: Tuple = (Exception,), 
         should_retry_fn: Optional[Callable[[Exception], bool]] = None) -> Callable:
    """Decorator for retrying functions when exceptions occur.
    
    Args:
        max_retries: Maximum number of retry attempts.
        delay: Initial delay between retries in seconds.
        backoff_factor: Factor to increase delay with each retry.
        exceptions_to_retry: Tuple of exceptions to retry on.
        should_retry_fn: Optional function that takes an exception and returns
            whether to retry based on the exception.
            
    Returns:
        Decorated function with retry logic.
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            retries = 0
            current_delay = delay
            
            while True:
                try:
                    return func(*args, **kwargs)
                except exceptions_to_retry as e:
                    # Check if we should retry based on the exception
                    if should_retry_fn and not should_retry_fn(e):
                        logger.warning(f"Not retrying function {func.__name__} due to should_retry_fn result: {str(e)}")
                        raise
                    
                    retries += 1
                    
                    if retries > max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries")
                        logger.error(traceback.format_exc())
                        raise RetryError(
                            f"Function {func.__name__} failed after {max_retries} retries", 
                            e, 
                            retries
                        )
                    
                    logger.warning(f"Retrying function {func.__name__} after exception: {str(e)}. "
                                 f"Attempt {retries}/{max_retries} in {current_delay:.2f}s")
                    
                    time.sleep(current_delay)
                    current_delay *= backoff_factor
        
        return wrapper
    
    return decorator


def should_retry_binance_error(exception: Exception) -> bool:
    """Determine if a Binance API error should be retried.
    
    Args:
        exception: The exception to check.
        
    Returns:
        True if the error should be retried, False otherwise.
    """
    # Get the error message as string
    error_msg = str(exception)
    
    # Common Binance errors that should be retried
    retryable_errors = [
        "Connection refused",
        "Connection reset",
        "Connection timed out",
        "Timeout waiting for response",
        "Read timed out",
        "Service unavailable",
        "Internal server error",
        "502 Bad Gateway",
        "503 Service Unavailable",
        "504 Gateway Timeout",
        "429 Too Many Requests",  # Rate limit, but we'll retry after backoff
        "4XX",  # Generic API error 
        "5XX",  # Generic server error
        "Network error",
        "Connection error"
    ]
    
    # Non-retryable errors
    non_retryable_errors = [
        "Invalid API-key",
        "Invalid signature",
        "Order does not exist",
        "Account has insufficient balance",
        "MIN_NOTIONAL",
        "MAX_NUM_ORDERS",
        "Invalid symbol",
        "Invalid quantity",
        "Invalid price",
        "PRICE_FILTER",
        "LOT_SIZE"
    ]
    
    # Check for non-retryable errors first
    for non_retryable in non_retryable_errors:
        if non_retryable in error_msg:
            return False
    
    # Then check for retryable errors
    for retryable in retryable_errors:
        if retryable in error_msg:
            return True
    
    # For other errors, retry by default for network-related issues
    return "http" in error_msg.lower() or "network" in error_msg.lower() or "socket" in error_msg.lower()


def retry_binance_operation(max_retries: int = 3, delay: float = 2.0, backoff_factor: float = 1.5) -> Callable:
    """Decorator for retrying Binance API operations.
    
    Args:
        max_retries: Maximum number of retry attempts.
        delay: Initial delay between retries in seconds.
        backoff_factor: Factor to increase delay with each retry.
        
    Returns:
        Decorated function with retry logic for Binance operations.
    """
    return retry(
        max_retries=max_retries,
        delay=delay,
        backoff_factor=backoff_factor,
        should_retry_fn=should_retry_binance_error
    )