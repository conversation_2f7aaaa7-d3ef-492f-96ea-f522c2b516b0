import React from 'react';
import { Container, Typography, Box } from '@mui/material';
import MLControls from '../components/MLControls';
import DashboardLayout from '../components/DashboardLayout';

const MLOptimization: React.FC = () => {
  return (
    <DashboardLayout>
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            ML Weight Optimization
          </Typography>
          <Typography variant="subtitle1" gutterBottom>
            Configure and control ML-based strategy weight optimization
          </Typography>
          <MLControls />
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default MLOptimization;
