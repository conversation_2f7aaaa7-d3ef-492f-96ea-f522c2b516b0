"""
Enhanced Portfolio Manager with MCP Integration for Strategy Ensemble System.
Implements real-time strategy execution with Redis caching, Supabase analytics, 
and Telegram alerting as specified in the PRD.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import json
import pickle
from datetime import datetime, timedelta
from dataclasses import asdict
import numpy as np
import pandas as pd
import logging

from app.models.trade_state import TradeState
from app.models.market_data import MarketData
from app.services.execution.execution_service import ExecutionService
from app.strategies.base_strategy import BaseStrategy
from app.ml.models.weight_optimizer import WeightOptimizer

# MCP Integration imports
import redis.asyncio as redis
from supabase import Client

# Ensemble configuration and data classes
from .config import (
    EnsembleConfig,
    StrategyWeight,
    AggregatedSignal,
    PortfolioMetrics,
    RiskLimits
)

logger = logging.getLogger(__name__)


class EnhancedPortfolioManager:
    """
    Enhanced Portfolio Manager with MCP integration for strategy ensemble execution.
    
    Features:
    - Concurrent execution of multiple strategies
    - ML-optimized strategy weight allocation
    - Redis caching for sub-second response times
    - Supabase real-time analytics
    - Telegram alerting for risk management
    - Cross-exchange data validation
    """
    
    def __init__(
        self,
        config: EnsembleConfig,
        strategies: List[BaseStrategy],
        execution_service: ExecutionService,
        weight_optimizer: WeightOptimizer,
        redis_client: redis.Redis,
        supabase_client: Client,
        telegram_chat_id: str
    ):
        self.config = config
        self.strategies = {s.__class__.__name__: s for s in strategies}
        self.execution_service = execution_service
        self.weight_optimizer = weight_optimizer
        self.redis = redis_client
        self.supabase = supabase_client
        self.telegram_chat_id = telegram_chat_id
        
        # Performance tracking
        self.strategy_performance: Dict[str, List[float]] = {
            name: [] for name in self.strategies.keys()
        }
        self.position_history: List[Dict] = []
        
        # Redis cache keys
        self.WEIGHTS_KEY = "ensemble:weights"
        self.SIGNALS_KEY = "ensemble:signals"
        self.METRICS_KEY = "ensemble:metrics"
        self.CORRELATION_KEY = "ensemble:correlation"
        
        logger.info(f"Initialized EnhancedPortfolioManager with {len(self.strategies)} strategies")
    
    async def get_strategy_weights(
        self, 
        market_conditions: Dict
    ) -> Dict[str, StrategyWeight]:
        """Get ML-optimized strategy weights with Redis caching."""
        
        try:
            # Check Redis cache first (sub-second response)
            cached_weights = await self.redis.get(self.WEIGHTS_KEY)
            if cached_weights:
                weights_data = json.loads(cached_weights)
                cache_time = datetime.fromisoformat(weights_data['timestamp'])
                
                # Use cached weights if less than 5 minutes old
                if datetime.now() - cache_time < timedelta(minutes=5):
                    return {
                        name: StrategyWeight(
                            strategy_name=data['strategy_name'],
                            weight=data['weight'],
                            confidence=data['confidence'],
                            last_updated=datetime.fromisoformat(data['last_updated'])
                        )
                        for name, data in weights_data['weights'].items()
                    }
            
            # Get fresh weights from ML model
            state_features = await self._prepare_state_features(market_conditions)
            
            # Get weights from ML model (via MLflow deployment)
            raw_weights = await self.weight_optimizer.predict_weights(state_features)
            
            # Ensure weights sum to 1.0 and are non-negative
            raw_weights = np.maximum(raw_weights, 0)
            raw_weights = raw_weights / np.sum(raw_weights)
            
            # Create StrategyWeight objects
            strategy_weights = {}
            for i, strategy_name in enumerate(self.strategies.keys()):
                strategy_weights[strategy_name] = StrategyWeight(
                    strategy_name=strategy_name,
                    weight=float(raw_weights[i]),
                    confidence=await self._calculate_confidence(strategy_name),
                    last_updated=datetime.now()
                )
            
            # Cache in Redis for 5 minutes
            cache_data = {
                'weights': {name: asdict(weight) for name, weight in strategy_weights.items()},
                'timestamp': datetime.now().isoformat()
            }
            await self.redis.setex(
                self.WEIGHTS_KEY, 
                self.config.redis_ttl_weights,
                json.dumps(cache_data, default=str)
            )
            
            # Send Telegram alert for significant weight changes
            await self._alert_weight_changes(strategy_weights)
            
            logger.info(f"Generated fresh strategy weights: {[f'{name}: {w.weight:.3f}' for name, w in strategy_weights.items()]}")
            return strategy_weights
            
        except Exception as e:
            logger.error(f"Failed to get strategy weights: {e}")
            # Fallback to equal weights if ML model fails
            await self._send_telegram_alert(f"ML model failed, using equal weights: {e}")
            equal_weight = 1.0 / len(self.strategies)
            return {
                name: StrategyWeight(
                    strategy_name=name,
                    weight=equal_weight,
                    confidence=0.5,
                    last_updated=datetime.now()
                ) for name in self.strategies.keys()
            }
    
    async def execute_ensemble(
        self, 
        market_data: MarketData
    ) -> List[TradeState]:
        """Execute all strategies simultaneously with weighted allocation."""
        
        try:
            # Get current strategy weights
            weights = await self.get_strategy_weights(market_data.to_dict())
            
            # Get signals from all strategies concurrently
            strategy_signals = await self._get_all_strategy_signals(market_data)
            
            # Aggregate signals with weight-based scaling
            aggregated_signal = await self.aggregate_signals(strategy_signals, weights)
            
            # Execute trades if signal is strong enough
            executed_trades = []
            if aggregated_signal.confidence > self.config.confidence_threshold:
                try:
                    trade_result = await self.execution_service.execute_trade(
                        symbol=market_data.symbol,
                        action=aggregated_signal.action,
                        quantity=aggregated_signal.quantity,
                        price=aggregated_signal.price
                    )
                    executed_trades.append(trade_result)
                    
                    # Update performance tracking
                    await self._update_performance_tracking(
                        weights, strategy_signals, trade_result
                    )
                    
                    logger.info(f"Executed ensemble trade: {trade_result.action} {trade_result.quantity} {trade_result.symbol} @ {trade_result.price}")
                    
                except Exception as e:
                    await self._send_telegram_alert(f"Trade execution failed: {e}")
                    logger.error(f"Trade execution failed: {e}")
            else:
                logger.info(f"Signal confidence {aggregated_signal.confidence:.3f} below threshold {self.config.confidence_threshold}")
            
            return executed_trades
            
        except Exception as e:
            logger.error(f"Ensemble execution failed: {e}")
            await self._send_telegram_alert(f"Ensemble execution failed: {e}")
            return []
    
    async def aggregate_signals(
        self, 
        strategy_signals: Dict[str, Dict], 
        weights: Dict[str, StrategyWeight]
    ) -> AggregatedSignal:
        """Aggregate signals from multiple strategies with conflict resolution."""
        
        try:
            # Check Redis cache for recent aggregation
            cache_key = f"{self.SIGNALS_KEY}:{hash(str(strategy_signals))}"
            cached_signal = await self.redis.get(cache_key)
            if cached_signal:
                signal_data = json.loads(cached_signal)
                return AggregatedSignal(
                    action=signal_data['action'],
                    quantity=signal_data['quantity'],
                    price=signal_data['price'],
                    confidence=signal_data['confidence'],
                    contributing_strategies=signal_data['contributing_strategies'],
                    timestamp=datetime.fromisoformat(signal_data['timestamp'])
                )
            
            weighted_actions = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
            weighted_quantities = []
            weighted_prices = []
            contributing_strategies = []
            total_confidence = 0
            
            for strategy_name, signal in strategy_signals.items():
                if strategy_name not in weights:
                    continue
                    
                weight = weights[strategy_name].weight
                action = signal.get('action', 'HOLD')
                quantity = signal.get('quantity', 0)
                price = signal.get('price', 0)
                confidence = signal.get('confidence', 0)
                
                # Weight the action votes
                weighted_actions[action] += weight * confidence
                
                # Weight quantities and prices
                if action in ['BUY', 'SELL'] and quantity > 0:
                    weighted_quantities.append(quantity * weight)
                    weighted_prices.append(price * weight)
                    contributing_strategies.append(strategy_name)
                
                total_confidence += confidence * weight
            
            # Determine final action (highest weighted vote wins)
            final_action = max(weighted_actions, key=weighted_actions.get)
            
            # Calculate final quantity and price
            final_quantity = sum(weighted_quantities) if weighted_quantities else 0
            final_price = sum(weighted_prices) / len(weighted_prices) if weighted_prices else 0
            
            # Normalize confidence
            final_confidence = total_confidence / sum(weights[name].weight for name in weights) if weights else 0
            
            aggregated_signal = AggregatedSignal(
                action=final_action,
                quantity=final_quantity,
                price=final_price,
                confidence=final_confidence,
                contributing_strategies=contributing_strategies,
                timestamp=datetime.now()
            )
            
            # Cache for 30 seconds
            await self.redis.setex(
                cache_key, 
                self.config.redis_ttl_signals,
                json.dumps(asdict(aggregated_signal), default=str)
            )
            
            logger.debug(f"Aggregated signal: {final_action} confidence={final_confidence:.3f} from {len(contributing_strategies)} strategies")
            return aggregated_signal
            
        except Exception as e:
            logger.error(f"Signal aggregation failed: {e}")
            # Return neutral signal on error
            return AggregatedSignal(
                action="HOLD",
                quantity=0,
                price=0,
                confidence=0,
                contributing_strategies=[],
                timestamp=datetime.now()
            )
    
    async def track_performance(self) -> PortfolioMetrics:
        """Track real-time portfolio performance with Supabase storage."""
        
        try:
            # Check Redis cache for recent metrics
            cached_metrics = await self.redis.get(self.METRICS_KEY)
            if cached_metrics:
                cache_data = json.loads(cached_metrics)
                cache_time = datetime.fromisoformat(cache_data['timestamp'])
                if datetime.now() - cache_time < timedelta(minutes=1):
                    return PortfolioMetrics(**cache_data)
            
            # Calculate fresh metrics
            recent_trades = await self._get_recent_trades()
            
            # Calculate portfolio metrics
            total_pnl = sum(trade.get('pnl', 0) for trade in recent_trades)
            
            # Calculate Sharpe ratio (annualized)
            returns = [trade.get('return_pct', 0) for trade in recent_trades]
            if len(returns) > 10:
                sharpe_ratio = (np.mean(returns) * 365) / (np.std(returns) * np.sqrt(365))
            else:
                sharpe_ratio = 0
            
            # Calculate max drawdown
            cumulative_returns = np.cumsum(returns) if returns else [0]
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / np.where(running_max == 0, 1, running_max)
            max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0
            
            # Calculate win rate
            winning_trades = [r for r in returns if r > 0]
            win_rate = len(winning_trades) / len(returns) if returns else 0
            
            # Calculate strategy contributions
            strategy_contributions = await self._calculate_strategy_contributions(recent_trades)
            
            # Calculate correlation matrix
            correlation_matrix = await self._calculate_correlation_matrix()
            
            metrics = PortfolioMetrics(
                total_pnl=total_pnl,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                strategy_contributions=strategy_contributions,
                correlation_matrix=correlation_matrix,
                timestamp=datetime.now()
            )
            
            # Cache metrics for 1 minute
            await self.redis.setex(
                self.METRICS_KEY,
                60,
                json.dumps(asdict(metrics), default=str)
            )
            
            # Store in Supabase for historical tracking
            await self._store_metrics_supabase(metrics)
            
            # Send alerts for significant performance changes
            await self._check_performance_alerts(metrics)
            
            logger.debug(f"Portfolio metrics: PnL={total_pnl:.2f}, Sharpe={sharpe_ratio:.3f}, Win Rate={win_rate:.3f}")
            return metrics
            
        except Exception as e:
            logger.error(f"Performance tracking error: {e}")
            await self._send_telegram_alert(f"Performance tracking error: {e}")
            return PortfolioMetrics(
                total_pnl=0,
                sharpe_ratio=0,
                max_drawdown=0,
                win_rate=0,
                strategy_contributions={},
                correlation_matrix={},
                timestamp=datetime.now()
            )
    
    # Private helper methods
    
    async def _prepare_state_features(self, market_conditions: Dict) -> np.ndarray:
        """Prepare state features for ML model."""
        features = []
        
        # Market condition features
        features.extend([
            market_conditions.get('volatility', 0),
            market_conditions.get('volume', 0),
            market_conditions.get('price_change', 0),
            market_conditions.get('rsi', 50),
            market_conditions.get('macd', 0)
        ])
        
        # Portfolio features
        try:
            current_metrics = await self.track_performance()
            features.extend([
                current_metrics.sharpe_ratio,
                current_metrics.max_drawdown,
                current_metrics.win_rate
            ])
        except Exception as e:
            # Use default values if performance tracking fails
            features.extend([0, 0, 0.5])
            logger.warning(f"Using default portfolio features due to error: {e}")
        
        # Strategy correlation features
        try:
            correlation_matrix = await self._calculate_correlation_matrix()
            correlation_values = []
            strategy_names = list(self.strategies.keys())
            for i, strategy1 in enumerate(strategy_names):
                for j, strategy2 in enumerate(strategy_names):
                    if i != j:
                        correlation_values.append(
                            correlation_matrix.get(strategy1, {}).get(strategy2, 0)
                        )
            features.extend(correlation_values)
        except Exception as e:
            # Use default correlations if calculation fails
            num_strategies = len(self.strategies)
            default_correlations = [0] * (num_strategies * (num_strategies - 1))
            features.extend(default_correlations)
            logger.warning(f"Using default correlation features due to error: {e}")
        
        return np.array(features, dtype=np.float32)
    
    async def _get_all_strategy_signals(self, market_data: MarketData) -> Dict[str, Dict]:
        """Get signals from all strategies concurrently."""
        tasks = []
        for strategy_name, strategy in self.strategies.items():
            tasks.append(self._get_strategy_signal(strategy, market_data))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        signals = {}
        for i, (strategy_name, result) in enumerate(zip(self.strategies.keys(), results)):
            if isinstance(result, Exception):
                await self._send_telegram_alert(f"Strategy {strategy_name} failed: {result}")
                signals[strategy_name] = {'action': 'HOLD', 'quantity': 0, 'price': 0, 'confidence': 0}
                logger.error(f"Strategy {strategy_name} failed: {result}")
            else:
                signals[strategy_name] = result
        
        return signals
    
    async def _get_strategy_signal(self, strategy: BaseStrategy, market_data: MarketData) -> Dict:
        """Get signal from individual strategy."""
        try:
            signal = await strategy.generate_signal(market_data)
            return {
                'action': signal.action,
                'quantity': signal.quantity,
                'price': signal.price,
                'confidence': signal.confidence
            }
        except Exception as e:
            logger.error(f"Strategy signal generation failed: {e}")
            return {'action': 'HOLD', 'quantity': 0, 'price': 0, 'confidence': 0}
    
    async def _send_telegram_alert(self, message: str):
        """Send alert via Telegram MCP."""
        try:
            # Telegram MCP integration would go here
            # For now, just log the alert
            logger.warning(f"Telegram Alert: {message}")
            # TODO: Implement actual Telegram MCP call
        except Exception as e:
            logger.error(f"Failed to send Telegram alert: {e}")
    
    async def _calculate_confidence(self, strategy_name: str) -> float:
        """Calculate confidence score for strategy based on recent performance."""
        recent_performance = self.strategy_performance.get(strategy_name, [])
        if len(recent_performance) < 5:
            return 0.5  # Default confidence
        
        # Calculate confidence based on recent win rate and consistency
        wins = [p for p in recent_performance[-20:] if p > 0]
        win_rate = len(wins) / len(recent_performance[-20:])
        
        # Calculate consistency (lower volatility = higher confidence)
        volatility = np.std(recent_performance[-20:]) if len(recent_performance) >= 20 else 1.0
        consistency = 1.0 / (1.0 + volatility)
        
        return (win_rate * 0.7) + (consistency * 0.3)
    
    async def _alert_weight_changes(self, weights: Dict[str, StrategyWeight]):
        """Send alerts for significant weight changes."""
        # TODO: Implement weight change detection and alerting
        pass
    
    async def _update_performance_tracking(
        self, 
        weights: Dict[str, StrategyWeight], 
        strategy_signals: Dict[str, Dict], 
        trade_result: TradeState
    ):
        """Update performance tracking after trade execution."""
        # TODO: Implement performance tracking update
        pass
    
    async def _get_recent_trades(self, limit: int = 100) -> List[Dict]:
        """Get recent trades from Supabase."""
        try:
            # Mock implementation - replace with actual Supabase query
            return []
        except Exception as e:
            logger.error(f"Failed to get recent trades: {e}")
            return []
    
    async def _calculate_strategy_contributions(self, trades: List[Dict]) -> Dict[str, float]:
        """Calculate strategy contributions to portfolio performance."""
        # Mock implementation
        return {name: 0.0 for name in self.strategies.keys()}
    
    async def _calculate_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between strategies."""
        # Mock implementation
        strategy_names = list(self.strategies.keys())
        matrix = {}
        for strategy1 in strategy_names:
            matrix[strategy1] = {}
            for strategy2 in strategy_names:
                if strategy1 != strategy2:
                    matrix[strategy1][strategy2] = 0.0
        return matrix
    
    async def _store_metrics_supabase(self, metrics: PortfolioMetrics):
        """Store metrics in Supabase for historical tracking."""
        try:
            # TODO: Implement Supabase storage
            pass
        except Exception as e:
            logger.error(f"Failed to store metrics in Supabase: {e}")
    
    async def _check_performance_alerts(self, metrics: PortfolioMetrics):
        """Check for performance alerts and send notifications."""
        # Check drawdown alert
        if abs(metrics.max_drawdown) > self.config.alert_thresholds['drawdown']:
            await self._send_telegram_alert(
                f"🚨 HIGH DRAWDOWN: {metrics.max_drawdown:.2%}"
            )
        
        # Check Sharpe ratio alert
        if metrics.sharpe_ratio < self.config.alert_thresholds['sharpe_ratio']:
            await self._send_telegram_alert(
                f"⚠️ LOW SHARPE RATIO: {metrics.sharpe_ratio:.3f}"
            )
        
        # Check correlation alerts
        max_correlation = 0
        for strategy1, corr_dict in metrics.correlation_matrix.items():
            for strategy2, corr in corr_dict.items():
                max_correlation = max(max_correlation, abs(corr))
        
        if max_correlation > self.config.alert_thresholds['correlation']:
            await self._send_telegram_alert(
                f"📊 HIGH CORRELATION: {max_correlation:.2%}"
            )