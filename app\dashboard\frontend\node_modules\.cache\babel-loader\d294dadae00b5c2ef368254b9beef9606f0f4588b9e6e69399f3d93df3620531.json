{"ast": null, "code": "/**\n * WebSocket service for real-time trade updates\n */import{WS_URL}from'../config';// Define event types\nexport let WebSocketEventType=/*#__PURE__*/function(WebSocketEventType){WebSocketEventType[\"TRADE_UPDATE\"]=\"trade_update\";WebSocketEventType[\"SYSTEM_STATUS\"]=\"system_status\";WebSocketEventType[\"ERROR\"]=\"error\";// Auto Trading Events\nWebSocketEventType[\"AUTO_TRADING_SESSION_STARTED\"]=\"auto_trading_session_started\";WebSocketEventType[\"AUTO_TRADING_SESSION_STOPPED\"]=\"auto_trading_session_stopped\";WebSocketEventType[\"AUTO_TRADING_SESSION_PAUSED\"]=\"auto_trading_session_paused\";WebSocketEventType[\"AUTO_TRADING_SESSION_RESUMED\"]=\"auto_trading_session_resumed\";WebSocketEventType[\"AUTO_TRADING_PERFORMANCE_UPDATE\"]=\"auto_trading_performance_update\";WebSocketEventType[\"AUTO_TRADING_ALERT\"]=\"auto_trading_alert\";WebSocketEventType[\"AUTO_TRADING_EMERGENCY_STOP\"]=\"auto_trading_emergency_stop\";WebSocketEventType[\"AUTO_TRADING_CONNECTION_ESTABLISHED\"]=\"auto_trading_connection_established\";WebSocketEventType[\"AUTO_TRADING_INITIAL_STATUS\"]=\"auto_trading_initial_status\";// Session Reports Events\nWebSocketEventType[\"LIVE_SESSION_METRICS\"]=\"live_session_metrics\";WebSocketEventType[\"SESSION_RISK_ALERT\"]=\"session_risk_alert\";WebSocketEventType[\"SESSION_REPORT_UPDATE\"]=\"session_report_update\";return WebSocketEventType;}({});// Event type interfaces\n// Define event data interfaces\n// Define event handler types\n// WebSocket service class\nexport class WebSocketService{constructor(){this.socket=null;this.reconnectAttempts=0;this.maxReconnectAttempts=5;this.reconnectDelay=1000;// Start with 1 second delay\nthis.reconnectTimer=null;this.isConnecting=false;this.isConnected=false;this.eventHandlers={[WebSocketEventType.TRADE_UPDATE]:[],[WebSocketEventType.SYSTEM_STATUS]:[],[WebSocketEventType.ERROR]:[]};}/**\n   * Connect to the WebSocket server\n   */connect(){if(this.socket||this.isConnecting){return;}this.isConnecting=true;try{// Use the WebSocket URL from config\nconst wsUrl=`${WS_URL}/trades`;console.log(`Connecting to WebSocket at ${wsUrl}`);this.socket=new WebSocket(wsUrl);this.socket.onopen=this.handleOpen.bind(this);this.socket.onmessage=this.handleMessage.bind(this);this.socket.onclose=this.handleClose.bind(this);this.socket.onerror=this.handleError.bind(this);}catch(error){console.error('Error connecting to WebSocket:',error);this.isConnecting=false;this.scheduleReconnect();}}/**\n   * Disconnect from the WebSocket server\n   */disconnect(){if(this.socket){this.socket.close();this.socket=null;}if(this.reconnectTimer!==null){window.clearTimeout(this.reconnectTimer);this.reconnectTimer=null;}this.isConnected=false;this.isConnecting=false;this.reconnectAttempts=0;}/**\n   * Add an event handler\n   */on(eventType,handler){// Type assertion is needed here due to TypeScript limitations\nthis.eventHandlers[eventType].push(handler);}/**\n   * Remove an event handler\n   */off(eventType,handler){const index=this.eventHandlers[eventType].indexOf(handler);if(index!==-1){this.eventHandlers[eventType].splice(index,1);}}/**\n   * Handle WebSocket open event\n   */handleOpen(){console.log('WebSocket connection established');this.isConnected=true;this.isConnecting=false;this.reconnectAttempts=0;// Notify listeners of system status\nconst statusEvent={status:'online',message:'Connected to server'};this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,statusEvent);}/**\n   * Handle WebSocket message event\n   */handleMessage(event){try{const data=JSON.parse(event.data);if(data.type==='trade_update'){this.notifyHandlers(WebSocketEventType.TRADE_UPDATE,data.data);}else if(data.type==='system_status'){this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,data.data);}else if(data.type==='error'){this.notifyHandlers(WebSocketEventType.ERROR,data.data);}else{console.warn('Unknown WebSocket message type:',data.type);}}catch(error){console.error('Error parsing WebSocket message:',error,event.data);}}/**\n   * Handle WebSocket close event\n   */handleClose(event){console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);this.socket=null;this.isConnected=false;this.isConnecting=false;// Notify listeners of system status\nconst statusEvent={status:'offline',message:`Disconnected from server: ${event.reason||'Connection closed'}`};this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,statusEvent);// Attempt to reconnect if the close was unexpected\nif(event.code!==1000){this.scheduleReconnect();}}/**\n   * Handle WebSocket error event\n   */handleError(event){console.error('WebSocket error:',event);// Notify listeners of error\nconst errorEvent={code:'connection_error',message:'WebSocket connection error'};this.notifyHandlers(WebSocketEventType.ERROR,errorEvent);}/**\n   * Schedule a reconnection attempt\n   */scheduleReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.log('Maximum reconnection attempts reached');// Notify listeners of system status\nconst statusEvent={status:'offline',message:'Failed to reconnect to server after multiple attempts'};this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS,statusEvent);return;}if(this.reconnectTimer!==null){window.clearTimeout(this.reconnectTimer);}const delay=this.reconnectDelay*Math.pow(1.5,this.reconnectAttempts);console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts+1}/${this.maxReconnectAttempts})`);this.reconnectTimer=window.setTimeout(()=>{this.reconnectAttempts++;this.connect();},delay);}/**\n   * Notify all handlers of an event\n   */notifyHandlers(eventType,data){for(const handler of this.eventHandlers[eventType]){try{// Use type assertion to handle the type mismatch\nhandler(data);}catch(error){console.error(`Error in ${eventType} handler:`,error);}}}}// Create a singleton instance\nexport const websocketService=new WebSocketService();// Export the singleton instance as default\nexport default websocketService;", "map": {"version": 3, "names": ["WS_URL", "WebSocketEventType", "WebSocketService", "constructor", "socket", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "reconnectTimer", "isConnecting", "isConnected", "eventHandlers", "TRADE_UPDATE", "SYSTEM_STATUS", "ERROR", "connect", "wsUrl", "console", "log", "WebSocket", "onopen", "handleOpen", "bind", "onmessage", "handleMessage", "onclose", "handleClose", "onerror", "handleError", "error", "scheduleReconnect", "disconnect", "close", "window", "clearTimeout", "on", "eventType", "handler", "push", "off", "index", "indexOf", "splice", "statusEvent", "status", "message", "notifyHandlers", "event", "data", "JSON", "parse", "type", "warn", "code", "reason", "errorEvent", "delay", "Math", "pow", "setTimeout", "websocketService"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/websocket.ts"], "sourcesContent": ["/**\n * WebSocket service for real-time trade updates\n */\n\nimport { WS_URL } from '../config';\n\n// Define event types\nexport enum WebSocketEventType {\n  TRADE_UPDATE = 'trade_update',\n  SYSTEM_STATUS = 'system_status',\n  ERROR = 'error',\n  // Auto Trading Events\n  AUTO_TRADING_SESSION_STARTED = 'auto_trading_session_started',\n  AUTO_TRADING_SESSION_STOPPED = 'auto_trading_session_stopped',\n  AUTO_TRADING_SESSION_PAUSED = 'auto_trading_session_paused',\n  AUTO_TRADING_SESSION_RESUMED = 'auto_trading_session_resumed',\n  AUTO_TRADING_PERFORMANCE_UPDATE = 'auto_trading_performance_update',\n  AUTO_TRADING_ALERT = 'auto_trading_alert',\n  AUTO_TRADING_EMERGENCY_STOP = 'auto_trading_emergency_stop',\n  AUTO_TRADING_CONNECTION_ESTABLISHED = 'auto_trading_connection_established',\n  AUTO_TRADING_INITIAL_STATUS = 'auto_trading_initial_status',\n  // Session Reports Events\n  LIVE_SESSION_METRICS = 'live_session_metrics',\n  SESSION_RISK_ALERT = 'session_risk_alert',\n  SESSION_REPORT_UPDATE = 'session_report_update',\n}\n\n// Event type interfaces\nexport interface LiveSessionMetricsEvent {\n  session_id: string;\n  live_metrics: {\n    current_pnl: number;\n    unrealized_pnl: number;\n    win_rate: number;\n    active_trades: number;\n    sharpe_ratio: number;\n  };\n  active_alerts?: Array<{\n    level: string;\n    message: string;\n    timestamp: string;\n  }>;\n}\n\nexport interface SessionRiskAlertEvent {\n  alert: {\n    level: string;\n    message: string;\n  };\n  timestamp: string;\n}\n\nexport interface SessionReportUpdateEvent {\n  session_id: string;\n  status: string;\n}\n\n// Define event data interfaces\nexport interface TradeUpdateEvent {\n  trade_id: string;\n  status: string;\n  symbol: string;\n  entry_side: string;\n  entry_price: number | null;\n  entry_qty: number | null;\n  sl_price: number | null;\n  tp_price: number | null;\n  timestamp: string;\n  exit_price?: number | null;\n  current_price?: number;\n}\n\nexport interface SystemStatusEvent {\n  status: 'online' | 'offline' | 'degraded';\n  message: string;\n}\n\nexport interface ErrorEvent {\n  code: string;\n  message: string;\n}\n\n// Define event handler types\nexport type TradeUpdateHandler = (data: TradeUpdateEvent) => void;\nexport type SystemStatusHandler = (data: SystemStatusEvent) => void;\nexport type ErrorHandler = (data: ErrorEvent) => void;\nexport type LiveSessionMetricsHandler = (data: LiveSessionMetricsEvent) => void;\nexport type SessionRiskAlertHandler = (data: SessionRiskAlertEvent) => void;\nexport type SessionReportUpdateHandler = (data: SessionReportUpdateEvent) => void;\n\n// WebSocket service class\nexport class WebSocketService {\n  private socket: WebSocket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000; // Start with 1 second delay\n  private reconnectTimer: number | null = null;\n  private isConnecting = false;\n  private isConnected = false;\n  private eventHandlers: {\n    [WebSocketEventType.TRADE_UPDATE]: TradeUpdateHandler[];\n    [WebSocketEventType.SYSTEM_STATUS]: SystemStatusHandler[];\n    [WebSocketEventType.ERROR]: ErrorHandler[];\n  } = {\n    [WebSocketEventType.TRADE_UPDATE]: [],\n    [WebSocketEventType.SYSTEM_STATUS]: [],\n    [WebSocketEventType.ERROR]: [],\n  };\n\n  /**\n   * Connect to the WebSocket server\n   */\n  public connect(): void {\n    if (this.socket || this.isConnecting) {\n      return;\n    }\n\n    this.isConnecting = true;\n\n    try {\n      // Use the WebSocket URL from config\n      const wsUrl = `${WS_URL}/trades`;\n\n      console.log(`Connecting to WebSocket at ${wsUrl}`);\n      this.socket = new WebSocket(wsUrl);\n\n      this.socket.onopen = this.handleOpen.bind(this);\n      this.socket.onmessage = this.handleMessage.bind(this);\n      this.socket.onclose = this.handleClose.bind(this);\n      this.socket.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      this.isConnecting = false;\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Disconnect from the WebSocket server\n   */\n  public disconnect(): void {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    this.isConnected = false;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n\n  /**\n   * Add an event handler\n   */\n  public on<T extends WebSocketEventType>(\n    eventType: T,\n    handler: T extends WebSocketEventType.TRADE_UPDATE\n      ? TradeUpdateHandler\n      : T extends WebSocketEventType.SYSTEM_STATUS\n      ? SystemStatusHandler\n      : ErrorHandler\n  ): void {\n    // Type assertion is needed here due to TypeScript limitations\n    this.eventHandlers[eventType].push(handler as any);\n  }\n\n  /**\n   * Remove an event handler\n   */\n  public off<T extends WebSocketEventType>(\n    eventType: T,\n    handler: T extends WebSocketEventType.TRADE_UPDATE\n      ? TradeUpdateHandler\n      : T extends WebSocketEventType.SYSTEM_STATUS\n      ? SystemStatusHandler\n      : ErrorHandler\n  ): void {\n    const index = this.eventHandlers[eventType].indexOf(handler as any);\n    if (index !== -1) {\n      this.eventHandlers[eventType].splice(index, 1);\n    }\n  }\n\n  /**\n   * Handle WebSocket open event\n   */\n  private handleOpen(): void {\n    console.log('WebSocket connection established');\n    this.isConnected = true;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n\n    // Notify listeners of system status\n    const statusEvent: SystemStatusEvent = {\n      status: 'online',\n      message: 'Connected to server',\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n  }\n\n  /**\n   * Handle WebSocket message event\n   */\n  private handleMessage(event: MessageEvent): void {\n    try {\n      const data = JSON.parse(event.data);\n\n      if (data.type === 'trade_update') {\n        this.notifyHandlers(WebSocketEventType.TRADE_UPDATE, data.data);\n      } else if (data.type === 'system_status') {\n        this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, data.data);\n      } else if (data.type === 'error') {\n        this.notifyHandlers(WebSocketEventType.ERROR, data.data);\n      } else {\n        console.warn('Unknown WebSocket message type:', data.type);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error, event.data);\n    }\n  }\n\n  /**\n   * Handle WebSocket close event\n   */\n  private handleClose(event: CloseEvent): void {\n    console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);\n    this.socket = null;\n    this.isConnected = false;\n    this.isConnecting = false;\n\n    // Notify listeners of system status\n    const statusEvent: SystemStatusEvent = {\n      status: 'offline',\n      message: `Disconnected from server: ${event.reason || 'Connection closed'}`,\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n    // Attempt to reconnect if the close was unexpected\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Handle WebSocket error event\n   */\n  private handleError(event: Event): void {\n    console.error('WebSocket error:', event);\n\n    // Notify listeners of error\n    const errorEvent: ErrorEvent = {\n      code: 'connection_error',\n      message: 'WebSocket connection error',\n    };\n    this.notifyHandlers(WebSocketEventType.ERROR, errorEvent);\n  }\n\n  /**\n   * Schedule a reconnection attempt\n   */\n  private scheduleReconnect(): void {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log('Maximum reconnection attempts reached');\n\n      // Notify listeners of system status\n      const statusEvent: SystemStatusEvent = {\n        status: 'offline',\n        message: 'Failed to reconnect to server after multiple attempts',\n      };\n      this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n      return;\n    }\n\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n    }\n\n    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);\n    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);\n\n    this.reconnectTimer = window.setTimeout(() => {\n      this.reconnectAttempts++;\n      this.connect();\n    }, delay);\n  }\n\n  /**\n   * Notify all handlers of an event\n   */\n  private notifyHandlers<T extends WebSocketEventType>(\n    eventType: T,\n    data: T extends WebSocketEventType.TRADE_UPDATE\n      ? TradeUpdateEvent\n      : T extends WebSocketEventType.SYSTEM_STATUS\n      ? SystemStatusEvent\n      : ErrorEvent\n  ): void {\n    for (const handler of this.eventHandlers[eventType]) {\n      try {\n        // Use type assertion to handle the type mismatch\n        (handler as Function)(data);\n      } catch (error) {\n        console.error(`Error in ${eventType} handler:`, error);\n      }\n    }\n  }\n}\n\n// Create a singleton instance\nexport const websocketService = new WebSocketService();\n\n// Export the singleton instance as default\nexport default websocketService;\n"], "mappings": "AAAA;AACA;AACA,GAEA,OAASA,MAAM,KAAQ,WAAW,CAElC;AACA,UAAY,CAAAC,kBAAkB,uBAAlBA,kBAAkB,EAAlBA,kBAAkB,gCAAlBA,kBAAkB,kCAAlBA,kBAAkB,kBAI5B;AAJUA,kBAAkB,gEAAlBA,kBAAkB,gEAAlBA,kBAAkB,8DAAlBA,kBAAkB,gEAAlBA,kBAAkB,sEAAlBA,kBAAkB,4CAAlBA,kBAAkB,8DAAlBA,kBAAkB,8EAAlBA,kBAAkB,8DAc5B;AAdUA,kBAAkB,gDAAlBA,kBAAkB,4CAAlBA,kBAAkB,wDAAlB,CAAAA,kBAAkB,OAoB9B;AA8BA;AAyBA;AAQA;AACA,MAAO,MAAM,CAAAC,gBAAiB,CAAAC,YAAA,OACpBC,MAAM,CAAqB,IAAI,MAC/BC,iBAAiB,CAAG,CAAC,MACrBC,oBAAoB,CAAG,CAAC,MACxBC,cAAc,CAAG,IAAI,CAAE;AAAA,KACvBC,cAAc,CAAkB,IAAI,MACpCC,YAAY,CAAG,KAAK,MACpBC,WAAW,CAAG,KAAK,MACnBC,aAAa,CAIjB,CACF,CAACV,kBAAkB,CAACW,YAAY,EAAG,EAAE,CACrC,CAACX,kBAAkB,CAACY,aAAa,EAAG,EAAE,CACtC,CAACZ,kBAAkB,CAACa,KAAK,EAAG,EAC9B,CAAC,EAED;AACF;AACA,KACSC,OAAOA,CAAA,CAAS,CACrB,GAAI,IAAI,CAACX,MAAM,EAAI,IAAI,CAACK,YAAY,CAAE,CACpC,OACF,CAEA,IAAI,CAACA,YAAY,CAAG,IAAI,CAExB,GAAI,CACF;AACA,KAAM,CAAAO,KAAK,CAAG,GAAGhB,MAAM,SAAS,CAEhCiB,OAAO,CAACC,GAAG,CAAC,8BAA8BF,KAAK,EAAE,CAAC,CAClD,IAAI,CAACZ,MAAM,CAAG,GAAI,CAAAe,SAAS,CAACH,KAAK,CAAC,CAElC,IAAI,CAACZ,MAAM,CAACgB,MAAM,CAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,CAC/C,IAAI,CAAClB,MAAM,CAACmB,SAAS,CAAG,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC,CACrD,IAAI,CAAClB,MAAM,CAACqB,OAAO,CAAG,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC,CACjD,IAAI,CAAClB,MAAM,CAACuB,OAAO,CAAG,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,IAAI,CAAC,CACnD,CAAE,MAAOO,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,IAAI,CAACpB,YAAY,CAAG,KAAK,CACzB,IAAI,CAACqB,iBAAiB,CAAC,CAAC,CAC1B,CACF,CAEA;AACF;AACA,KACSC,UAAUA,CAAA,CAAS,CACxB,GAAI,IAAI,CAAC3B,MAAM,CAAE,CACf,IAAI,CAACA,MAAM,CAAC4B,KAAK,CAAC,CAAC,CACnB,IAAI,CAAC5B,MAAM,CAAG,IAAI,CACpB,CAEA,GAAI,IAAI,CAACI,cAAc,GAAK,IAAI,CAAE,CAChCyB,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC1B,cAAc,CAAC,CACxC,IAAI,CAACA,cAAc,CAAG,IAAI,CAC5B,CAEA,IAAI,CAACE,WAAW,CAAG,KAAK,CACxB,IAAI,CAACD,YAAY,CAAG,KAAK,CACzB,IAAI,CAACJ,iBAAiB,CAAG,CAAC,CAC5B,CAEA;AACF;AACA,KACS8B,EAAEA,CACPC,SAAY,CACZC,OAIgB,CACV,CACN;AACA,IAAI,CAAC1B,aAAa,CAACyB,SAAS,CAAC,CAACE,IAAI,CAACD,OAAc,CAAC,CACpD,CAEA;AACF;AACA,KACSE,GAAGA,CACRH,SAAY,CACZC,OAIgB,CACV,CACN,KAAM,CAAAG,KAAK,CAAG,IAAI,CAAC7B,aAAa,CAACyB,SAAS,CAAC,CAACK,OAAO,CAACJ,OAAc,CAAC,CACnE,GAAIG,KAAK,GAAK,CAAC,CAAC,CAAE,CAChB,IAAI,CAAC7B,aAAa,CAACyB,SAAS,CAAC,CAACM,MAAM,CAACF,KAAK,CAAE,CAAC,CAAC,CAChD,CACF,CAEA;AACF;AACA,KACUnB,UAAUA,CAAA,CAAS,CACzBJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/C,IAAI,CAACR,WAAW,CAAG,IAAI,CACvB,IAAI,CAACD,YAAY,CAAG,KAAK,CACzB,IAAI,CAACJ,iBAAiB,CAAG,CAAC,CAE1B;AACA,KAAM,CAAAsC,WAA8B,CAAG,CACrCC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,qBACX,CAAC,CACD,IAAI,CAACC,cAAc,CAAC7C,kBAAkB,CAACY,aAAa,CAAE8B,WAAW,CAAC,CACpE,CAEA;AACF;AACA,KACUnB,aAAaA,CAACuB,KAAmB,CAAQ,CAC/C,GAAI,CACF,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CAEnC,GAAIA,IAAI,CAACG,IAAI,GAAK,cAAc,CAAE,CAChC,IAAI,CAACL,cAAc,CAAC7C,kBAAkB,CAACW,YAAY,CAAEoC,IAAI,CAACA,IAAI,CAAC,CACjE,CAAC,IAAM,IAAIA,IAAI,CAACG,IAAI,GAAK,eAAe,CAAE,CACxC,IAAI,CAACL,cAAc,CAAC7C,kBAAkB,CAACY,aAAa,CAAEmC,IAAI,CAACA,IAAI,CAAC,CAClE,CAAC,IAAM,IAAIA,IAAI,CAACG,IAAI,GAAK,OAAO,CAAE,CAChC,IAAI,CAACL,cAAc,CAAC7C,kBAAkB,CAACa,KAAK,CAAEkC,IAAI,CAACA,IAAI,CAAC,CAC1D,CAAC,IAAM,CACL/B,OAAO,CAACmC,IAAI,CAAC,iCAAiC,CAAEJ,IAAI,CAACG,IAAI,CAAC,CAC5D,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAEkB,KAAK,CAACC,IAAI,CAAC,CACtE,CACF,CAEA;AACF;AACA,KACUtB,WAAWA,CAACqB,KAAiB,CAAQ,CAC3C9B,OAAO,CAACC,GAAG,CAAC,gCAAgC6B,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACO,MAAM,EAAE,CAAC,CACzE,IAAI,CAAClD,MAAM,CAAG,IAAI,CAClB,IAAI,CAACM,WAAW,CAAG,KAAK,CACxB,IAAI,CAACD,YAAY,CAAG,KAAK,CAEzB;AACA,KAAM,CAAAkC,WAA8B,CAAG,CACrCC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,6BAA6BE,KAAK,CAACO,MAAM,EAAI,mBAAmB,EAC3E,CAAC,CACD,IAAI,CAACR,cAAc,CAAC7C,kBAAkB,CAACY,aAAa,CAAE8B,WAAW,CAAC,CAElE;AACA,GAAII,KAAK,CAACM,IAAI,GAAK,IAAI,CAAE,CACvB,IAAI,CAACvB,iBAAiB,CAAC,CAAC,CAC1B,CACF,CAEA;AACF;AACA,KACUF,WAAWA,CAACmB,KAAY,CAAQ,CACtC9B,OAAO,CAACY,KAAK,CAAC,kBAAkB,CAAEkB,KAAK,CAAC,CAExC;AACA,KAAM,CAAAQ,UAAsB,CAAG,CAC7BF,IAAI,CAAE,kBAAkB,CACxBR,OAAO,CAAE,4BACX,CAAC,CACD,IAAI,CAACC,cAAc,CAAC7C,kBAAkB,CAACa,KAAK,CAAEyC,UAAU,CAAC,CAC3D,CAEA;AACF;AACA,KACUzB,iBAAiBA,CAAA,CAAS,CAChC,GAAI,IAAI,CAACzB,iBAAiB,EAAI,IAAI,CAACC,oBAAoB,CAAE,CACvDW,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CAEpD;AACA,KAAM,CAAAyB,WAA8B,CAAG,CACrCC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,uDACX,CAAC,CACD,IAAI,CAACC,cAAc,CAAC7C,kBAAkB,CAACY,aAAa,CAAE8B,WAAW,CAAC,CAElE,OACF,CAEA,GAAI,IAAI,CAACnC,cAAc,GAAK,IAAI,CAAE,CAChCyB,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC1B,cAAc,CAAC,CAC1C,CAEA,KAAM,CAAAgD,KAAK,CAAG,IAAI,CAACjD,cAAc,CAAGkD,IAAI,CAACC,GAAG,CAAC,GAAG,CAAE,IAAI,CAACrD,iBAAiB,CAAC,CACzEY,OAAO,CAACC,GAAG,CAAC,2BAA2BsC,KAAK,eAAe,IAAI,CAACnD,iBAAiB,CAAG,CAAC,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAEtH,IAAI,CAACE,cAAc,CAAGyB,MAAM,CAAC0B,UAAU,CAAC,IAAM,CAC5C,IAAI,CAACtD,iBAAiB,EAAE,CACxB,IAAI,CAACU,OAAO,CAAC,CAAC,CAChB,CAAC,CAAEyC,KAAK,CAAC,CACX,CAEA;AACF;AACA,KACUV,cAAcA,CACpBV,SAAY,CACZY,IAIc,CACR,CACN,IAAK,KAAM,CAAAX,OAAO,GAAI,KAAI,CAAC1B,aAAa,CAACyB,SAAS,CAAC,CAAE,CACnD,GAAI,CACF;AACCC,OAAO,CAAcW,IAAI,CAAC,CAC7B,CAAE,MAAOnB,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,YAAYO,SAAS,WAAW,CAAEP,KAAK,CAAC,CACxD,CACF,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAA+B,gBAAgB,CAAG,GAAI,CAAA1D,gBAAgB,CAAC,CAAC,CAEtD;AACA,cAAe,CAAA0D,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}