version: '3.8'

services:
  # Redis for real-time caching - Optimized for Position Calculations
  redis:
    image: redis:7-alpine
    container_name: ensemble_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: >
      redis-server 
        --appendonly yes 
        --appendfsync everysec
        --maxmemory 256mb
        --maxmemory-policy allkeys-lru
        --save 900 1 300 10 60 10000
        --tcp-keepalive 60
        --timeout 0
        --tcp-backlog 511
        --databases 16
        --maxclients 10000
        --lazyfree-lazy-eviction yes
        --lazyfree-lazy-expire yes
        --lazyfree-lazy-server-del yes
    deploy:
      resources:
        limits:
          memory: 512MB
        reservations:
          memory: 256MB
    networks:
      - ensemble_network
    healthcheck:
      test: ["CMD", "redis-cli", "--latency-history", "-i", "1", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 10s

  # Main application (development)
  app-dev:
    build:
      context: .
      target: development
    container_name: ensemble_app_dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/venv  # Exclude venv from bind mount
    environment:
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - ENVIRONMENT=development
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ensemble_network
    profiles:
      - dev

  # Main application (production)
  app:
    build:
      context: .
      target: production
    container_name: ensemble_app
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - ENVIRONMENT=production
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ensemble_network
    restart: unless-stopped
    profiles:
      - prod
      
  # Dynamic Position Optimization Service - High Performance
  position-optimizer:
    build:
      context: .
      target: position-optimizer
    container_name: ensemble_position_optimizer
    ports:
      - "8001:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - ENVIRONMENT=production
      - OPTIMIZATION_MODE=position_sizing
      - TARGET_LATENCY_MS=100
      - CACHE_PRELOAD=true
      - PERFORMANCE_MONITORING=enabled
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ensemble_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1GB
        reservations:
          cpus: '1.0'
          memory: 512MB
    profiles:
      - prod
      - optimizer

  # ML Training service
  ml-trainer:
    build:
      context: .
      target: ml-training
    container_name: ensemble_ml_trainer
    environment:
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    volumes:
      - ./models:/app/models
      - ml_artifacts:/app/zenml_artifacts
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ensemble_network
    profiles:
      - ml

  # ZenML Server (optional)
  zenml-server:
    image: zenmldocker/zenml-server:latest
    container_name: ensemble_zenml_server
    ports:
      - "8080:8080"
    environment:
      - ZENML_ANALYTICS_OPT_IN=false
      - ZENML_SERVER_AUTO_ACTIVATE=true
    volumes:
      - zenml_data:/zenml
    networks:
      - ensemble_network
    profiles:
      - ml

  # MLflow Server (optional)
  mlflow-server:
    image: python:3.12-slim
    container_name: ensemble_mlflow_server
    ports:
      - "5000:5000"
    volumes:
      - mlflow_artifacts:/mlflow
    working_dir: /mlflow
    command: >
      bash -c "
        pip install mlflow[extras] &&
        mlflow server 
          --host 0.0.0.0 
          --port 5000 
          --backend-store-uri sqlite:///mlflow.db 
          --default-artifact-root /mlflow/artifacts
      "
    networks:
      - ensemble_network
    profiles:
      - ml

  # Paper Trading Environment - Task 3.2.1
  paper-trading:
    build:
      context: .
      target: paper-trading
    container_name: ensemble_paper_trading
    ports:
      - "8002:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - WANDB_API_KEY=${WANDB_API_KEY}
      - ENVIRONMENT=paper_trading
      - PAPER_TRADING_MODE=true
      - INITIAL_BALANCE_USD=100000
      - COST_OPTIMIZATION_ENABLED=true
      - TELEGRAM_ALERTS_ENABLED=true
      - WANDB_TRACKING_ENABLED=true
      - PERFORMANCE_MONITORING=enabled
      - EXECUTION_TARGET_MS=100
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ensemble_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 768MB
        reservations:
          cpus: '0.5'
          memory: 256MB
    healthcheck:
      test: ["CMD", "python", "-c", "
import requests
import sys
try:
    response = requests.get('http://localhost:8000/health/paper-trading', timeout=5)
    if response.status_code != 200:
        sys.exit(1)
except Exception:
    sys.exit(1)
"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    profiles:
      - paper-trading
      - prod

  # Testing service
  tests:
    build:
      context: .
      target: testing
    container_name: ensemble_tests
    volumes:
      - .:/app
      - test_coverage:/app/htmlcov
    environment:
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=testing
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ensemble_network
    profiles:
      - test

  # Nginx reverse proxy (production)
  nginx:
    image: nginx:alpine
    container_name: ensemble_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
    depends_on:
      - app
    networks:
      - ensemble_network
    restart: unless-stopped
    profiles:
      - prod

volumes:
  redis_data:
    driver: local
  zenml_data:
    driver: local
  mlflow_artifacts:
    driver: local
  ml_artifacts:
    driver: local
  test_coverage:
    driver: local

networks:
  ensemble_network:
    driver: bridge