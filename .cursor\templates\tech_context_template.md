# Tech Context: [Project Name]

## 1. Programming Languages
- **Language 1**: (e.g., Python 3.9)
  - **Key Libraries/Frameworks**: (e.g., Flask, Pandas, NumPy)
- **Language 2**: (e.g., JavaScript ES6+)
  - **Key Libraries/Frameworks**: (e.g., React 18, Node.js 18.x, Express.js)

## 2. Datastores
- **Primary Database**: (e.g., PostgreSQL 15 - Name, Version, Purpose)
- **Caching System**: (e.g., Redis 7 - Purpose)
- **Other Storage**: (e.g., S3 for file storage - Purpose)

## 3. Development Environment & Tools
- **IDE(s)**: (e.g., VS Code with specific extensions, PyCharm)
- **Version Control**: (e.g., Git, GitHub/GitLab/Bitbucket - include repo URL if public or contextually safe)
- **Build Tools**: (e.g., Webpack, Maven, Gradle)
- **Dependency Management**: (e.g., npm, pip, Poetry)
- **Containerization (if any)**: (e.g., Docker, Docker Compose - link to Dockerfile/compose file)

## 4. Deployment & Infrastructure
- **Hosting Provider(s)**: (e.g., AWS, Azure, Google Cloud, Netlify, Vercel)
- **CI/CD Pipeline**: (e.g., GitHub Actions, Jenkins, GitLab CI - describe key stages)
- **Operating System(s)**: (Development OS, Server OS)
- **Web Server (if applicable)**: (e.g., Nginx, Apache)

## 5. Key APIs & Integrations
- **Internal API 1**: (Name, Purpose, Link to docs if available)
- **External API 1**: (e.g., Stripe API for payments - Purpose, Link to docs)

## 6. Style Guides & Linters
- **Linter Configuration**: (e.g., ESLint with Airbnb config, Flake8, Black)
- **Code Formatting**: (e.g., Prettier, Black)
- **Documentation Standards**: (e.g., JSDoc, Sphinx)
