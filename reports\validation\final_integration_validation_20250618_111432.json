{"timestamp": "2025-06-18T11:14:08.606262", "tests": {"env_var_binance_api_key": {"success": true, "details": {"length": 64}, "error": null, "timestamp": "2025-06-18T11:14:08.606504"}, "env_var_binance_api_secret": {"success": true, "details": {"length": 64}, "error": null, "timestamp": "2025-06-18T11:14:08.606542"}, "env_var_supabase_url": {"success": true, "details": {"length": 40}, "error": null, "timestamp": "2025-06-18T11:14:08.606574"}, "env_var_supabase_key": {"success": true, "details": {"length": 208}, "error": null, "timestamp": "2025-06-18T11:14:08.606606"}, "env_var_wandb_api_key": {"success": true, "details": {"length": 40}, "error": null, "timestamp": "2025-06-18T11:14:08.606673"}, "file__env": {"success": true, "details": {"exists": true, "readable": true}, "error": null, "timestamp": "2025-06-18T11:14:08.607984"}, "file_app_config_settings_py": {"success": true, "details": {"exists": true, "readable": true}, "error": null, "timestamp": "2025-06-18T11:14:08.609993"}, "file_requirements_txt": {"success": true, "details": {"exists": true, "readable": true}, "error": null, "timestamp": "2025-06-18T11:14:08.611434"}, "import_settings": {"success": true, "details": {"class": "Settings"}, "error": null, "timestamp": "2025-06-18T11:14:09.824223"}, "import_redis_service": {"success": true, "details": {"class": "RedisService"}, "error": null, "timestamp": "2025-06-18T11:14:10.208121"}, "import_binance_client": {"success": true, "details": {"class": "BinanceExchangeClient"}, "error": null, "timestamp": "2025-06-18T11:14:13.270583"}, "redis_local_connection": {"success": true, "details": {"operation": "set_get", "value_match": true}, "error": null, "timestamp": "2025-06-18T11:14:13.276156"}, "redis_existing_data": {"success": true, "details": {"crypto_trader_keys": 3}, "error": null, "timestamp": "2025-06-18T11:14:13.276670"}, "wandb_offline_mode": {"success": true, "details": {"project": "final-validation", "mode": "offline"}, "error": null, "timestamp": "2025-06-18T11:14:32.517497"}, "virtual_environment": {"success": true, "details": {"python_path": "/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/venv/bin/python", "is_venv": true}, "error": null, "timestamp": "2025-06-18T11:14:32.517616"}}, "summary": {"passed": 15, "failed": 0, "total": 15}}