#!/usr/bin/env python3
"""
FastAPI Routes for Paper Trading Environment - Task 3.2.1
Provides HTTP API endpoints for paper trading operations.

Features:
- Portfolio management endpoints
- Trade execution API
- Performance monitoring
- Health checks
- Real-time status
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
import asyncio

from app.strategies.paper_trading_portfolio_manager import (
    PaperTradingPortfolioManager,
    create_paper_trading_portfolio_manager
)
from app.services.cost_calculator import CostCalculator
from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator
from app.services.mcp.wandb_cost_tracker import create_wandb_cost_tracker
from app.models.market_data import MarketData

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/paper-trading", tags=["Paper Trading"])

# Pydantic models for API
class TradeRequest(BaseModel):
    symbol: str = Field(..., example="BTCUSDT")
    side: str = Field(..., regex="^(BUY|SELL)$", example="BUY")
    quantity: float = Field(..., gt=0, example=1.0)
    order_type: str = Field(default="MARKET", regex="^(MARKET|LIMIT)$")
    price: Optional[float] = Field(None, gt=0, example=50000.0)

class TradeResponse(BaseModel):
    order_id: str
    symbol: str
    side: str
    quantity: float
    filled_quantity: float
    filled_price: float
    status: str
    timestamp: datetime
    fees: float
    cost_optimization_used: bool

class PortfolioSummary(BaseModel):
    account: Dict[str, Any]
    balances: Dict[str, Any]
    positions: Dict[str, Any]
    trading_stats: Dict[str, Any]
    recent_trades: List[Dict[str, Any]]

class HealthStatus(BaseModel):
    status: str
    timestamp: datetime
    portfolio_value: float
    total_trades: int
    system_performance: Dict[str, Any]

class ResetRequest(BaseModel):
    new_initial_balance: Optional[float] = Field(None, gt=0, example=100000.0)

# Global paper trading manager instance
_paper_trading_manager: Optional[PaperTradingPortfolioManager] = None

async def get_paper_trading_manager() -> PaperTradingPortfolioManager:
    """Get or create paper trading manager instance"""
    global _paper_trading_manager
    
    if _paper_trading_manager is None:
        try:
            # Initialize services
            cost_calculator = CostCalculator()
            
            slippage_estimator = EnhancedSlippageEstimator(
                redis_service=None,
                config={
                    "base_slippage_bps": 2.0,
                    "size_impact_factor": 0.1,
                    "volatility_factor": 0.05,
                    "liquidity_factor": 0.03
                }
            )
            
            wandb_cost_tracker = await create_wandb_cost_tracker(
                redis_url="redis://redis:6379",
                cost_calculator=cost_calculator,
                slippage_estimator=slippage_estimator
            )
            
            # Create paper trading manager
            _paper_trading_manager = await create_paper_trading_portfolio_manager(
                initial_balance_usd=100000.0,
                redis_url="redis://redis:6379",
                cost_calculator=cost_calculator,
                slippage_estimator=slippage_estimator,
                wandb_cost_tracker=wandb_cost_tracker,
                config={
                    "max_position_size_pct": 20.0,
                    "slippage_simulation": True,
                    "fee_simulation": True,
                    "enable_cost_optimization": True,
                    "enable_performance_tracking": True,
                    "enable_telegram_alerts": True
                }
            )
            
            logger.info("Paper trading manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize paper trading manager: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize paper trading manager: {e}"
            )
    
    return _paper_trading_manager

@router.get("/health", response_model=HealthStatus)
async def health_check(manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)):
    """
    Health check endpoint for paper trading system
    """
    try:
        summary = await manager.get_portfolio_summary()
        
        health_status = HealthStatus(
            status="healthy",
            timestamp=datetime.now(),
            portfolio_value=summary["account"]["current_value"],
            total_trades=summary["trading_stats"]["total_trades"],
            system_performance={
                "avg_execution_time_ms": 50.0,  # Placeholder
                "success_rate": 99.5,
                "cache_hit_rate": 85.0
            }
        )
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {e}"
        )

@router.get("/portfolio/summary", response_model=PortfolioSummary)
async def get_portfolio_summary(manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)):
    """
    Get comprehensive portfolio summary
    """
    try:
        summary = await manager.get_portfolio_summary()
        
        return PortfolioSummary(
            account=summary["account"],
            balances=summary["balances"],
            positions=summary["positions"],
            trading_stats=summary["trading_stats"],
            recent_trades=summary["recent_trades"]
        )
        
    except Exception as e:
        logger.error(f"Failed to get portfolio summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get portfolio summary: {e}"
        )

@router.post("/trade", response_model=TradeResponse)
async def execute_trade(
    trade_request: TradeRequest,
    background_tasks: BackgroundTasks,
    manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)
):
    """
    Execute a paper trade
    """
    try:
        # Create market data for the trade
        market_data = MarketData(
            symbol=trade_request.symbol,
            price=trade_request.price or 50000.0,  # Default price if not provided
            volume=1000000.0,
            timestamp=datetime.now(),
            bid=49999.0,
            ask=50001.0,
            high_24h=51000.0,
            low_24h=49000.0,
            volatility=0.02
        )
        
        # Execute the trade
        order = await manager.execute_paper_trade(
            symbol=trade_request.symbol,
            side=trade_request.side,
            quantity=trade_request.quantity,
            order_type=trade_request.order_type,
            price=trade_request.price,
            market_data=market_data
        )
        
        if order is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Trade execution failed - check order parameters and portfolio balance"
            )
        
        # Background task for additional processing
        background_tasks.add_task(log_trade_metrics, order, manager)
        
        return TradeResponse(
            order_id=order.order_id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            filled_quantity=order.filled_quantity,
            filled_price=order.filled_price,
            status=order.status,
            timestamp=order.filled_timestamp or order.timestamp,
            fees=order.simulated_fees,
            cost_optimization_used=order.cost_optimization_used
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Trade execution failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Trade execution failed: {e}"
        )

@router.get("/portfolio/positions")
async def get_positions(manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)):
    """
    Get current portfolio positions
    """
    try:
        summary = await manager.get_portfolio_summary()
        return {"positions": summary["positions"]}
        
    except Exception as e:
        logger.error(f"Failed to get positions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get positions: {e}"
        )

@router.get("/portfolio/balances")
async def get_balances(manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)):
    """
    Get current portfolio balances
    """
    try:
        summary = await manager.get_portfolio_summary()
        return {"balances": summary["balances"]}
        
    except Exception as e:
        logger.error(f"Failed to get balances: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get balances: {e}"
        )

@router.get("/portfolio/performance")
async def get_performance_metrics(manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)):
    """
    Get detailed performance metrics
    """
    try:
        summary = await manager.get_portfolio_summary()
        
        # Calculate additional performance metrics
        performance_metrics = {
            "account_performance": summary["account"],
            "trading_statistics": summary["trading_stats"],
            "risk_metrics": {
                "max_drawdown": summary["account"]["max_drawdown"],
                "portfolio_volatility": 0.15,  # Placeholder
                "beta": 1.0,  # Placeholder
                "alpha": 0.02  # Placeholder
            },
            "cost_analysis": {
                "total_fees_paid": summary["trading_stats"]["total_fees_paid"],
                "cost_savings": summary["trading_stats"]["cost_savings"],
                "avg_fee_per_trade": (
                    summary["trading_stats"]["total_fees_paid"] / 
                    max(1, summary["trading_stats"]["total_trades"])
                )
            }
        }
        
        return performance_metrics
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get performance metrics: {e}"
        )

@router.get("/trades/history")
async def get_trade_history(
    limit: int = 50,
    offset: int = 0,
    manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)
):
    """
    Get trade history with pagination
    """
    try:
        # Get all order history
        all_orders = manager.order_history
        
        # Apply pagination
        start_idx = offset
        end_idx = offset + limit
        paginated_orders = all_orders[start_idx:end_idx]
        
        # Format response
        trades = []
        for order in paginated_orders:
            trades.append({
                "order_id": order.order_id,
                "symbol": order.symbol,
                "side": order.side,
                "quantity": order.quantity,
                "filled_quantity": order.filled_quantity,
                "filled_price": order.filled_price,
                "status": order.status,
                "timestamp": order.filled_timestamp.isoformat() if order.filled_timestamp else order.timestamp.isoformat(),
                "fees": order.simulated_fees,
                "cost_optimization_used": order.cost_optimization_used
            })
        
        return {
            "trades": trades,
            "total_count": len(all_orders),
            "limit": limit,
            "offset": offset,
            "has_more": end_idx < len(all_orders)
        }
        
    except Exception as e:
        logger.error(f"Failed to get trade history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get trade history: {e}"
        )

@router.post("/portfolio/reset")
async def reset_portfolio(
    reset_request: ResetRequest,
    manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)
):
    """
    Reset portfolio to initial state
    """
    try:
        await manager.reset_portfolio(reset_request.new_initial_balance)
        
        summary = await manager.get_portfolio_summary()
        
        return {
            "message": "Portfolio reset successfully",
            "new_balance": summary["account"]["initial_balance"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to reset portfolio: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset portfolio: {e}"
        )

@router.get("/market/price/{symbol}")
async def get_market_price(
    symbol: str,
    manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)
):
    """
    Get current market price for a symbol
    """
    try:
        # Get current price using the manager's price function
        current_price = await manager._get_current_price(symbol)
        
        if current_price is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Price not available for symbol: {symbol}"
            )
        
        return {
            "symbol": symbol,
            "price": current_price,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get market price for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get market price: {e}"
        )

@router.get("/system/status")
async def get_system_status(manager: PaperTradingPortfolioManager = Depends(get_paper_trading_manager)):
    """
    Get comprehensive system status
    """
    try:
        summary = await manager.get_portfolio_summary()
        
        # Calculate system metrics
        uptime_hours = 24  # Placeholder - would calculate actual uptime
        
        status_info = {
            "system": {
                "status": "operational",
                "uptime_hours": uptime_hours,
                "version": "3.2.1",
                "environment": "paper_trading"
            },
            "portfolio": {
                "total_value": summary["account"]["current_value"],
                "total_pnl": summary["account"]["total_pnl"],
                "pnl_percentage": summary["account"]["pnl_percentage"],
                "active_positions": len(summary["positions"])
            },
            "trading": {
                "total_trades": summary["trading_stats"]["total_trades"],
                "win_rate": summary["trading_stats"]["win_rate"],
                "total_fees": summary["trading_stats"]["total_fees_paid"],
                "cost_savings": summary["trading_stats"]["cost_savings"]
            },
            "performance": {
                "avg_execution_time_ms": 75.0,  # Placeholder
                "system_load": 45.2,  # Placeholder
                "memory_usage_mb": 256.5,  # Placeholder
                "cache_hit_rate": 88.5  # Placeholder
            }
        }
        
        return status_info
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system status: {e}"
        )

# Background tasks
async def log_trade_metrics(order, manager: PaperTradingPortfolioManager):
    """Background task to log trade metrics"""
    try:
        # Log additional metrics asynchronously
        logger.info(f"Trade completed: {order.side} {order.filled_quantity} {order.symbol} @ {order.filled_price}")
        
        # Additional processing could include:
        # - Updating external analytics
        # - Triggering alerts
        # - Calculating performance metrics
        
    except Exception as e:
        logger.error(f"Failed to log trade metrics: {e}")

# Health check endpoint at root level
@router.get("/")
async def root():
    """Root endpoint for paper trading API"""
    return {
        "service": "Paper Trading Environment",
        "version": "3.2.1",
        "status": "operational",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "health": "/api/paper-trading/health",
            "portfolio": "/api/paper-trading/portfolio/summary",
            "trade": "/api/paper-trading/trade",
            "history": "/api/paper-trading/trades/history",
            "status": "/api/paper-trading/system/status"
        }
    }

# Error handlers
@router.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"detail": f"Invalid value: {str(exc)}"}
    )

@router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception in paper trading API: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"}
    )