import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from app.models.base import Base

# --- Database Setup ---
# Get PostgreSQL connection details from environment variables
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASS = os.getenv("DB_PASS", "crypto_trader_pass")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "crypto_trading")

# Create PostgreSQL connection URL
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
ASYNC_DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Create synchronous engine for SQLAlchemy ORM
engine = create_engine(
    DATABASE_URL,
    echo=False
)

# Create asynchronous engine for SQLAlchemy ORM
async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    echo=False,
)

# Create session factories
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = sessionmaker(class_=AsyncSession, expire_on_commit=False, bind=async_engine)

# --- Database Initialization Function ---

def init_db():
    """Initializes the database and creates tables."""
    try:
        # Import all models to ensure they are registered with Base
        from app.models import ManagedTradeDB, TrainingSessionDB

        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("Database initialized successfully.")
        print(f"Created tables: {', '.join(Base.metadata.tables.keys())}")
    except Exception as e:
        print(f"Error initializing database: {e}")

async def init_async_db():
    """Initializes the database asynchronously and creates tables."""
    try:
        # Import all models to ensure they are registered with Base
        from app.models import ManagedTradeDB, TrainingSessionDB

        # Create a connection
        async with async_engine.begin() as conn:
            # Create tables
            await conn.run_sync(Base.metadata.create_all)
        print("Async database initialized successfully.")
        print(f"Created tables: {', '.join(Base.metadata.tables.keys())}")
    except Exception as e:
        print(f"Error initializing async database: {e}")

# --- Dependency for FastAPI ---

def get_db():
    """Dependency to get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def get_async_db():
    """Dependency to get an async database session."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

def get_db_session_factory():
    """Returns the async session factory."""
    return AsyncSessionLocal