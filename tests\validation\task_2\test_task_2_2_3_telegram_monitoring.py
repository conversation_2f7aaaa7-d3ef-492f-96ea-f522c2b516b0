#!/usr/bin/env python3
"""
Task 2.2.3: Complete Telegram Monitoring System Test
Tests comprehensive Telegram monitoring implementation with performance alerts,
weight change notifications, and system reliability validation.

Features tested:
- Real-time performance notifications
- Ensemble weight change alerts
- Monitoring system reliability 
- Alert delivery performance (<5 second target)
- Health check automation
- Notification throttling and formatting
"""

import asyncio
import os
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import sys

# Add project root to path
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

from app.monitoring.telegram_performance_monitor import (
    TelegramPerformanceMonitor, 
    TelegramConfig, 
    PerformanceMetrics,
    WeightChangeAlert,
    create_telegram_performance_monitor
)
from app.monitoring.risk_monitor import create_risk_monitor
from app.services.mcp.redis_service import RedisService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TelegramMonitoringTestSuite:
    """Comprehensive test suite for Telegram monitoring system."""
    
    def __init__(self):
        self.test_results = []
        self.performance_measurements = []
        self.telegram_monitor = None
        self.redis_service = None
        
        # Test configuration
        self.telegram_config = {
            'bot_token': os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID', '6906678033'),
            'redis_url': os.getenv('REDIS_URL', 'redis://localhost:6379')
        }
    
    def log_test_result(self, test_name: str, success: bool, details: str = "", metrics: Dict = None):
        """Log test result with timing and details."""
        result = {
            'test_name': test_name,
            'success': success,
            'timestamp': datetime.now(),
            'details': details,
            'metrics': metrics or {}
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {details}")
    
    async def setup(self):
        """Set up test environment."""
        logger.info("🚀 Setting up Telegram monitoring test environment...")
        
        try:
            # Initialize Redis service
            self.redis_service = RedisService(self.telegram_config['redis_url'])
            await self.redis_service.connect()
            
            # Create risk monitor for integration testing
            risk_monitor = await create_risk_monitor(
                redis_url=self.telegram_config['redis_url'],
                telegram_bot_token=self.telegram_config['bot_token'],
                telegram_chat_id=self.telegram_config['chat_id']
            )
            
            # Initialize Telegram performance monitor
            self.telegram_monitor = await create_telegram_performance_monitor(
                telegram_bot_token=self.telegram_config['bot_token'],
                telegram_chat_id=self.telegram_config['chat_id'],
                redis_url=self.telegram_config['redis_url'],
                risk_monitor=risk_monitor
            )
            
            self.log_test_result("Environment Setup", True, "Telegram monitor initialized successfully")
            
        except Exception as e:
            self.log_test_result("Environment Setup", False, f"Setup failed: {e}")
            raise
    
    async def test_telegram_connectivity(self):
        """Test basic Telegram bot connectivity."""
        logger.info("🔗 Testing Telegram connectivity...")
        
        try:
            start_time = time.perf_counter()
            success = await self.telegram_monitor._test_telegram_connectivity()
            response_time = (time.perf_counter() - start_time) * 1000
            
            self.log_test_result(
                "Telegram Connectivity", 
                success,
                f"Bot responsive in {response_time:.1f}ms",
                {'response_time_ms': response_time, 'target_ms': 5000}
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Telegram Connectivity", False, f"Connectivity test failed: {e}")
            return False
    
    async def test_performance_alert_delivery(self):
        """Test performance alert delivery and timing."""
        logger.info("📊 Testing performance alert delivery...")
        
        try:
            # Create mock performance metrics with threshold violations
            mock_metrics = PerformanceMetrics(
                avg_execution_time_ms=150.0,  # Exceeds 100ms threshold
                cache_hit_rate_pct=60.0,      # Below 70% threshold
                api_response_time_ms=1200.0,  # Exceeds 1000ms threshold
                error_rate_pct=2.0,
                memory_usage_pct=45.0,
                cpu_usage_pct=35.0,
                throughput_ops_sec=15.0,
                active_connections=5,
                last_updated=datetime.now()
            )
            
            # Test execution time alert
            start_time = time.perf_counter()
            
            violation = {
                'type': 'execution_time',
                'message': "Execution time (150.0ms) exceeds target (100ms)",
                'current': 150.0,
                'threshold': 100.0,
                'severity': 'medium'
            }
            
            await self.telegram_monitor._send_performance_alert(violation, mock_metrics)
            
            delivery_time = (time.perf_counter() - start_time) * 1000
            
            success = delivery_time < 5000  # Target: <5 seconds
            
            self.log_test_result(
                "Performance Alert Delivery",
                success,
                f"Alert delivered in {delivery_time:.1f}ms",
                {
                    'delivery_time_ms': delivery_time,
                    'target_ms': 5000,
                    'threshold_violations': 3
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Performance Alert Delivery", False, f"Alert delivery failed: {e}")
            return False
    
    async def test_weight_change_alerts(self):
        """Test ensemble weight change alert functionality."""
        logger.info("⚖️ Testing weight change alerts...")
        
        try:
            # Create mock weight change scenario
            previous_weights = {
                'GridStrategy': 0.33,
                'TechnicalAnalysisStrategy': 0.33,
                'TrendFollowingStrategy': 0.34
            }
            
            new_weights = {
                'GridStrategy': 0.15,      # -18% change (significant)
                'TechnicalAnalysisStrategy': 0.45,  # +12% change
                'TrendFollowingStrategy': 0.40     # +6% change
            }
            
            # Analyze weight changes
            weight_change = await self.telegram_monitor._analyze_weight_changes(
                {'weights': previous_weights, 'model_version': 'test_v1.0', 'confidence': 0.85},
                {'weights': new_weights, 'model_version': 'test_v1.1', 'confidence': 0.92}
            )
            
            if weight_change:
                start_time = time.perf_counter()
                await self.telegram_monitor._send_weight_change_alert(weight_change)
                delivery_time = (time.perf_counter() - start_time) * 1000
                
                success = delivery_time < 5000 and weight_change.max_change >= 0.1
                
                self.log_test_result(
                    "Weight Change Alert",
                    success,
                    f"Alert sent for {weight_change.max_change:.1%} max change in {delivery_time:.1f}ms",
                    {
                        'max_change_pct': weight_change.max_change * 100,
                        'delivery_time_ms': delivery_time,
                        'model_version': weight_change.model_version
                    }
                )
            else:
                self.log_test_result("Weight Change Alert", False, "No significant weight change detected")
                success = False
            
            return success
            
        except Exception as e:
            self.log_test_result("Weight Change Alert", False, f"Weight change alert failed: {e}")
            return False
    
    async def test_system_health_monitoring(self):
        """Test comprehensive system health monitoring."""
        logger.info("🏥 Testing system health monitoring...")
        
        try:
            start_time = time.perf_counter()
            health_status = await self.telegram_monitor._perform_health_check()
            health_check_time = (time.perf_counter() - start_time) * 1000
            
            # Verify health check completeness
            required_components = ['redis', 'telegram']
            if self.telegram_monitor.risk_monitor:
                required_components.append('risk_monitor')
            
            components_checked = set(health_status['components'].keys())
            all_components_checked = all(comp in components_checked for comp in required_components)
            
            # Test health status alert (simulate degraded status)
            if health_status['overall_status'] != 'healthy':
                await self.telegram_monitor._send_health_status_alert(health_status)
            
            success = (
                all_components_checked and
                health_check_time < 10000 and  # <10 seconds
                'performance_score' in health_status
            )
            
            self.log_test_result(
                "System Health Monitoring",
                success,
                f"Health check completed in {health_check_time:.1f}ms, status: {health_status['overall_status']}",
                {
                    'health_check_time_ms': health_check_time,
                    'components_checked': len(components_checked),
                    'overall_status': health_status['overall_status'],
                    'performance_score': health_status['performance_score']
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("System Health Monitoring", False, f"Health monitoring failed: {e}")
            return False
    
    async def test_performance_metrics_collection(self):
        """Test performance metrics collection and validation."""
        logger.info("📈 Testing performance metrics collection...")
        
        try:
            start_time = time.perf_counter()
            metrics = await self.telegram_monitor._collect_performance_metrics()
            collection_time = (time.perf_counter() - start_time) * 1000
            
            # Validate metrics completeness
            required_fields = [
                'avg_execution_time_ms', 'cache_hit_rate_pct', 'api_response_time_ms',
                'error_rate_pct', 'memory_usage_pct', 'cpu_usage_pct',
                'throughput_ops_sec', 'active_connections', 'last_updated'
            ]
            
            all_fields_present = all(hasattr(metrics, field) for field in required_fields)
            metrics_valid = all(
                isinstance(getattr(metrics, field), (int, float, datetime))
                for field in required_fields
            )
            
            success = (
                all_fields_present and
                metrics_valid and
                collection_time < 2000  # <2 seconds
            )
            
            self.log_test_result(
                "Performance Metrics Collection",
                success,
                f"Metrics collected in {collection_time:.1f}ms",
                {
                    'collection_time_ms': collection_time,
                    'fields_present': len([f for f in required_fields if hasattr(metrics, f)]),
                    'avg_execution_time_ms': metrics.avg_execution_time_ms,
                    'cache_hit_rate_pct': metrics.cache_hit_rate_pct
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Performance Metrics Collection", False, f"Metrics collection failed: {e}")
            return False
    
    async def test_alert_throttling(self):
        """Test alert throttling mechanism."""
        logger.info("🔄 Testing alert throttling mechanism...")
        
        try:
            from app.monitoring.telegram_performance_monitor import PerformanceAlertType
            
            # Test alert throttling
            alert_type = PerformanceAlertType.EXECUTION_TIME
            
            # First alert should be allowed
            should_send_1 = await self.telegram_monitor._should_send_alert(alert_type)
            
            # Update throttle
            if should_send_1:
                await self.telegram_monitor._update_alert_throttle(alert_type)
            
            # Second alert should be throttled
            should_send_2 = await self.telegram_monitor._should_send_alert(alert_type)
            
            success = should_send_1 and not should_send_2
            
            self.log_test_result(
                "Alert Throttling",
                success,
                f"First alert: {'allowed' if should_send_1 else 'blocked'}, Second alert: {'allowed' if should_send_2 else 'blocked'}",
                {
                    'first_alert_allowed': should_send_1,
                    'second_alert_blocked': not should_send_2,
                    'throttle_minutes': self.telegram_monitor.config.alert_throttle_minutes
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Alert Throttling", False, f"Throttling test failed: {e}")
            return False
    
    async def test_redis_integration(self):
        """Test Redis caching integration."""
        logger.info("💾 Testing Redis integration...")
        
        try:
            # Test Redis connectivity
            start_time = time.perf_counter()
            await self.redis_service.ping()
            ping_time = (time.perf_counter() - start_time) * 1000
            
            # Test cache operations
            test_key = "test_monitor_cache"
            test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
            
            # Write to cache
            await self.redis_service.setex(test_key, 300, json.dumps(test_data))
            
            # Read from cache
            cached_data = await self.redis_service.get(test_key)
            
            # Verify data integrity
            cached_parsed = json.loads(cached_data) if cached_data else None
            data_integrity = cached_parsed == test_data if cached_parsed else False
            
            # Get cache stats
            cache_stats = await self.redis_service.get_cache_stats()
            
            success = (
                ping_time < 50 and  # <50ms Redis response
                data_integrity and
                bool(cache_stats)
            )
            
            self.log_test_result(
                "Redis Integration",
                success,
                f"Redis ping: {ping_time:.1f}ms, cache operations successful",
                {
                    'ping_time_ms': ping_time,
                    'data_integrity': data_integrity,
                    'cache_stats_available': bool(cache_stats)
                }
            )
            
            # Cleanup
            await self.redis_service.delete(test_key)
            
            return success
            
        except Exception as e:
            self.log_test_result("Redis Integration", False, f"Redis integration failed: {e}")
            return False
    
    async def test_monitoring_loop_performance(self):
        """Test monitoring loop performance."""
        logger.info("🔄 Testing monitoring loop performance...")
        
        try:
            # Simulate monitoring cycles
            cycle_times = []
            
            for i in range(5):  # Run 5 monitoring cycles
                start_time = time.perf_counter()
                
                # Simulate monitoring operations
                metrics = await self.telegram_monitor._collect_performance_metrics()
                await self.telegram_monitor._check_performance_thresholds(metrics)
                health_status = await self.telegram_monitor._perform_health_check()
                
                cycle_time = (time.perf_counter() - start_time) * 1000
                cycle_times.append(cycle_time)
                
                # Small delay between cycles
                await asyncio.sleep(0.1)
            
            avg_cycle_time = sum(cycle_times) / len(cycle_times)
            max_cycle_time = max(cycle_times)
            
            success = avg_cycle_time < 2000 and max_cycle_time < 5000  # Reasonable performance targets
            
            self.log_test_result(
                "Monitoring Loop Performance",
                success,
                f"Avg cycle: {avg_cycle_time:.1f}ms, Max cycle: {max_cycle_time:.1f}ms",
                {
                    'avg_cycle_time_ms': avg_cycle_time,
                    'max_cycle_time_ms': max_cycle_time,
                    'cycles_tested': len(cycle_times),
                    'target_avg_ms': 2000
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Monitoring Loop Performance", False, f"Loop performance test failed: {e}")
            return False
    
    async def test_notification_formatting(self):
        """Test Telegram message formatting."""
        logger.info("💬 Testing notification formatting...")
        
        try:
            # Test performance alert formatting
            violation = {
                'type': 'execution_time',
                'message': "Test execution time violation",
                'current': 150.0,
                'threshold': 100.0,
                'severity': 'medium'
            }
            
            mock_metrics = PerformanceMetrics(
                avg_execution_time_ms=150.0,
                cache_hit_rate_pct=85.0,
                api_response_time_ms=800.0,
                error_rate_pct=1.0,
                memory_usage_pct=60.0,
                cpu_usage_pct=45.0,
                throughput_ops_sec=20.0,
                active_connections=8,
                last_updated=datetime.now()
            )
            
            # Test weight change formatting
            weight_change = WeightChangeAlert(
                previous_weights={'GridStrategy': 0.33, 'TechnicalAnalysisStrategy': 0.33, 'TrendFollowingStrategy': 0.34},
                new_weights={'GridStrategy': 0.40, 'TechnicalAnalysisStrategy': 0.30, 'TrendFollowingStrategy': 0.30},
                weight_changes={'GridStrategy': 0.07, 'TechnicalAnalysisStrategy': 0.03, 'TrendFollowingStrategy': 0.04},
                max_change=0.07,
                model_version='test_v1.0',
                market_regime='trending',
                confidence=0.85,
                timestamp=datetime.now()
            )
            
            # Test formatting by sending actual messages
            start_time = time.perf_counter()
            
            # Send performance alert
            perf_success = await self.telegram_monitor._send_performance_alert(violation, mock_metrics)
            
            # Send weight change alert  
            weight_success = await self.telegram_monitor._send_weight_change_alert(weight_change)
            
            formatting_time = (time.perf_counter() - start_time) * 1000
            
            success = perf_success and weight_success and formatting_time < 10000
            
            self.log_test_result(
                "Notification Formatting",
                success,
                f"Messages formatted and sent in {formatting_time:.1f}ms",
                {
                    'performance_alert_sent': perf_success,
                    'weight_alert_sent': weight_success,
                    'formatting_time_ms': formatting_time
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Notification Formatting", False, f"Formatting test failed: {e}")
            return False
    
    async def test_comprehensive_reliability(self):
        """Test overall system reliability and robustness."""
        logger.info("🛡️ Testing comprehensive system reliability...")
        
        try:
            reliability_score = 0
            total_tests = 5
            
            # Test 1: Error handling
            try:
                # Simulate error condition
                await self.telegram_monitor._send_telegram_message("🧪 **Reliability Test Message**\n\nThis is a test of the monitoring system reliability.")
                reliability_score += 1
            except:
                pass
            
            # Test 2: Performance under load
            try:
                tasks = []
                for i in range(10):  # Simulate 10 concurrent operations
                    tasks.append(self.telegram_monitor._collect_performance_metrics())
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                successful_operations = sum(1 for r in results if not isinstance(r, Exception))
                
                if successful_operations >= 8:  # 80% success rate
                    reliability_score += 1
            except:
                pass
            
            # Test 3: Cache resilience
            try:
                # Test cache operations under various conditions
                for i in range(5):
                    await self.telegram_monitor._cache_performance_metrics(
                        PerformanceMetrics(
                            avg_execution_time_ms=50.0,
                            cache_hit_rate_pct=90.0,
                            api_response_time_ms=500.0,
                            error_rate_pct=0.5,
                            memory_usage_pct=40.0,
                            cpu_usage_pct=30.0,
                            throughput_ops_sec=25.0,
                            active_connections=3,
                            last_updated=datetime.now()
                        )
                    )
                reliability_score += 1
            except:
                pass
            
            # Test 4: Alert delivery consistency
            try:
                delivery_times = []
                for i in range(3):
                    start_time = time.perf_counter()
                    success = await self.telegram_monitor._send_telegram_message(f"🔧 Test message {i+1}/3")
                    delivery_time = (time.perf_counter() - start_time) * 1000
                    if success:
                        delivery_times.append(delivery_time)
                
                if len(delivery_times) >= 2 and all(t < 5000 for t in delivery_times):
                    reliability_score += 1
            except:
                pass
            
            # Test 5: Monitoring stats accuracy
            try:
                stats = self.telegram_monitor.get_monitoring_stats()
                required_stats = ['monitoring_active', 'notifications_sent', 'avg_notification_time_ms']
                
                if all(stat in stats for stat in required_stats):
                    reliability_score += 1
            except:
                pass
            
            # Calculate reliability percentage
            reliability_percentage = (reliability_score / total_tests) * 100
            success = reliability_percentage >= 80  # 80% reliability threshold
            
            self.log_test_result(
                "Comprehensive Reliability",
                success,
                f"Reliability score: {reliability_score}/{total_tests} ({reliability_percentage:.1f}%)",
                {
                    'reliability_score': reliability_score,
                    'total_tests': total_tests,
                    'reliability_percentage': reliability_percentage,
                    'threshold_percentage': 80
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result("Comprehensive Reliability", False, f"Reliability test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run complete test suite."""
        logger.info("🚀 Starting comprehensive Telegram monitoring test suite...")
        
        start_time = time.perf_counter()
        
        # Test sequence
        tests = [
            ("Setup", self.setup),
            ("Telegram Connectivity", self.test_telegram_connectivity),
            ("Redis Integration", self.test_redis_integration),
            ("Performance Metrics Collection", self.test_performance_metrics_collection),
            ("Performance Alert Delivery", self.test_performance_alert_delivery),
            ("Weight Change Alerts", self.test_weight_change_alerts),
            ("System Health Monitoring", self.test_system_health_monitoring),
            ("Alert Throttling", self.test_alert_throttling),
            ("Monitoring Loop Performance", self.test_monitoring_loop_performance),
            ("Notification Formatting", self.test_notification_formatting),
            ("Comprehensive Reliability", self.test_comprehensive_reliability)
        ]
        
        passed_tests = 0
        total_tests = len(tests) - 1  # Exclude setup
        
        for test_name, test_func in tests:
            if test_name == "Setup":
                await test_func()
                continue
                
            try:
                result = await test_func()
                if result:
                    passed_tests += 1
            except Exception as e:
                logger.error(f"Test {test_name} crashed: {e}")
        
        total_time = (time.perf_counter() - start_time) * 1000
        
        # Final summary
        success_rate = (passed_tests / total_tests) * 100
        
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 TELEGRAM MONITORING TEST RESULTS")
        logger.info(f"{'='*60}")
        logger.info(f"✅ Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        logger.info(f"⏱️  Total Time: {total_time:.1f}ms")
        logger.info(f"🎯 Success Threshold: 80%")
        logger.info(f"📈 Overall Status: {'✅ PASS' if success_rate >= 80 else '❌ FAIL'}")
        
        # Task completion criteria
        task_complete = (
            success_rate >= 80 and
            any(r['test_name'] == 'Performance Alert Delivery' and r['success'] for r in self.test_results) and
            any(r['test_name'] == 'Weight Change Alerts' and r['success'] for r in self.test_results) and
            any(r['test_name'] == 'System Health Monitoring' and r['success'] for r in self.test_results)
        )
        
        logger.info(f"\n🎯 Task 2.2.3 Status: {'✅ COMPLETED' if task_complete else '❌ INCOMPLETE'}")
        
        if task_complete:
            logger.info("🎉 Telegram monitoring system deployment completed successfully!")
            logger.info("✅ Real-time performance notifications: WORKING")
            logger.info("✅ Ensemble weight change alerts: WORKING") 
            logger.info("✅ Monitoring system reliability: VALIDATED")
            
            # Send completion notification
            completion_message = (
                "🎉 **Task 2.2.3 COMPLETED** 🎉\n\n"
                "✅ **Telegram Monitoring System Deployed**\n\n"
                f"📊 **Test Results:**\n"
                f"• Tests Passed: `{passed_tests}/{total_tests}` ({success_rate:.1f}%)\n"
                f"• Total Time: `{total_time:.1f}ms`\n"
                f"• Real-time Performance Notifications: ✅\n"
                f"• Ensemble Weight Change Alerts: ✅\n"
                f"• System Reliability Monitoring: ✅\n\n"
                f"🚀 **System Ready for Production!**"
            )
            
            if self.telegram_monitor:
                await self.telegram_monitor._send_telegram_message(completion_message)
        
        return task_complete
    
    async def cleanup(self):
        """Clean up test environment."""
        try:
            if self.telegram_monitor:
                await self.telegram_monitor.stop_monitoring()
            
            if self.redis_service:
                await self.redis_service.disconnect()
                
            logger.info("🧹 Test environment cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

async def main():
    """Main test execution."""
    test_suite = TelegramMonitoringTestSuite()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n✅ Task 2.2.3: Complete Telegram monitoring system deployment - COMPLETED")
            return 0
        else:
            print("\n❌ Task 2.2.3: Complete Telegram monitoring system deployment - FAILED")
            return 1
            
    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return 1
        
    finally:
        await test_suite.cleanup()

if __name__ == "__main__":
    import sys
    
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ Test suite interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)