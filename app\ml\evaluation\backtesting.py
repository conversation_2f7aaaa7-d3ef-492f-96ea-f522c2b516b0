"""
Backtesting module for ML weight optimization.

This module contains functions for backtesting ML models for strategy weight
optimization.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta

from app.ml.models.weight_optimizer import <PERSON><PERSON><PERSON>eightOptimizer
from app.ml.data_collector import <PERSON>Collector
from app.ml.feature_engineering import FeatureEngineer
from app.ml.evaluation.metrics import PerformanceMetrics

logger = logging.getLogger(__name__)

class Backtester:
    """Backtests ML models for strategy weight optimization."""
    
    def __init__(self, exchange_client, db_client=None):
        """Initialize the Backtester.
        
        Args:
            exchange_client: Client for fetching market data from exchange
            db_client: Client for database operations (optional)
        """
        self.exchange_client = exchange_client
        self.db_client = db_client
        self.data_collector = DataCollector(exchange_client, db_client)
        self.feature_engineer = FeatureEngineer()
        self.logger = logging.getLogger(__name__)
    
    async def backtest_fixed_weights(self, symbol: str, timeframe: str, 
                                   start_date: datetime, end_date: datetime,
                                   weights: Dict[str, float]) -> Dict[str, Any]:
        """Backtest with fixed strategy weights.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            start_date: Start date for backtesting
            end_date: End date for backtesting
            weights: Dictionary of strategy weights
            
        Returns:
            Dictionary of backtest results
        """
        try:
            # Collect market data
            self.logger.info(f"Collecting market data for {symbol} ({timeframe})")
            market_data, strategy_performances = await self.data_collector.collect_training_data(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=(end_date - start_date).days + 1
            )
            
            if market_data.empty:
                self.logger.warning("No market data collected for backtesting")
                return {'error': 'No market data collected'}
            
            # Filter data by date range
            market_data = market_data[start_date:end_date]
            
            for strategy in strategy_performances:
                strategy_performances[strategy] = strategy_performances[strategy][start_date:end_date]
            
            if market_data.empty:
                self.logger.warning("No market data in specified date range")
                return {'error': 'No market data in specified date range'}
            
            # Extract features
            self.logger.info("Extracting features from market data")
            features_df = self.feature_engineer.extract_market_features(market_data)
            
            # Create weight array
            strategy_names = sorted(weights.keys())
            weight_values = np.array([weights[name] for name in strategy_names])
            weight_array = np.tile(weight_values, (len(features_df), 1))
            
            # Create return array
            return_array = np.zeros((len(features_df), len(strategy_names)))
            
            for i, name in enumerate(strategy_names):
                if name in strategy_performances and not strategy_performances[name].empty:
                    # Align dates
                    aligned_returns = pd.Series(0.0, index=features_df.index)
                    if 'profit' in strategy_performances[name].columns:
                        for date in strategy_performances[name].index:
                            if date in aligned_returns.index:
                                aligned_returns[date] = strategy_performances[name].loc[date, 'profit']
                    
                    return_array[:, i] = aligned_returns.values
            
            # Calculate portfolio returns
            portfolio_returns = np.sum(weight_array * return_array, axis=1)
            
            # Calculate metrics
            metrics = PerformanceMetrics.calculate_portfolio_metrics(portfolio_returns)
            allocation_metrics = PerformanceMetrics.calculate_strategy_allocation_metrics(weight_array, return_array)
            
            # Combine metrics
            metrics.update(allocation_metrics)
            
            # Create result
            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'weights': weights,
                'metrics': metrics,
                'returns': portfolio_returns.tolist(),
                'dates': [d.isoformat() for d in features_df.index]
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error backtesting fixed weights: {e}")
            return {'error': str(e)}
    
    async def backtest_ml_weights(self, symbol: str, timeframe: str, 
                                start_date: datetime, end_date: datetime,
                                model_path: str, window_size: int = 10) -> Dict[str, Any]:
        """Backtest with ML-optimized strategy weights.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            start_date: Start date for backtesting
            end_date: End date for backtesting
            model_path: Path to the ML model
            window_size: Size of the observation window
            
        Returns:
            Dictionary of backtest results
        """
        try:
            # Create weight optimizer
            weight_optimizer = MLWeightOptimizer(
                exchange_client=self.exchange_client,
                db_client=self.db_client,
                model_path=model_path
            )
            
            # Load the model
            self.logger.info(f"Loading ML model from {model_path}")
            success = await weight_optimizer.load_model()
            
            if not success:
                self.logger.warning(f"Failed to load ML model from {model_path}")
                return {'error': f"Failed to load ML model from {model_path}"}
            
            # Collect market data
            self.logger.info(f"Collecting market data for {symbol} ({timeframe})")
            market_data, strategy_performances = await self.data_collector.collect_training_data(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=(end_date - start_date).days + 1
            )
            
            if market_data.empty:
                self.logger.warning("No market data collected for backtesting")
                return {'error': 'No market data collected'}
            
            # Filter data by date range
            market_data = market_data[start_date:end_date]
            
            for strategy in strategy_performances:
                strategy_performances[strategy] = strategy_performances[strategy][start_date:end_date]
            
            if market_data.empty:
                self.logger.warning("No market data in specified date range")
                return {'error': 'No market data in specified date range'}
            
            # Extract features
            self.logger.info("Extracting features from market data")
            features_df = self.feature_engineer.extract_market_features(market_data)
            
            # Create return array
            strategy_names = weight_optimizer.strategy_names
            return_array = np.zeros((len(features_df), len(strategy_names)))
            
            for i, name in enumerate(strategy_names):
                if name in strategy_performances and not strategy_performances[name].empty:
                    # Align dates
                    aligned_returns = pd.Series(0.0, index=features_df.index)
                    if 'profit' in strategy_performances[name].columns:
                        for date in strategy_performances[name].index:
                            if date in aligned_returns.index:
                                aligned_returns[date] = strategy_performances[name].loc[date, 'profit']
                    
                    return_array[:, i] = aligned_returns.values
            
            # Get optimized weights for each time step
            self.logger.info("Getting optimized weights for each time step")
            weight_array = np.zeros((len(features_df), len(strategy_names)))
            
            for i in range(window_size, len(features_df)):
                # Get market conditions
                market_conditions = {
                    'volatility': features_df['volatility_5d'].iloc[i] if 'volatility_5d' in features_df.columns else 0.0,
                    'trend': features_df['price_momentum_5d'].iloc[i] if 'price_momentum_5d' in features_df.columns else 0.0,
                    'range_bound': 1.0 - abs(features_df['price_momentum_5d'].iloc[i]) if 'price_momentum_5d' in features_df.columns else 0.0,
                    'volume': features_df['volume_change'].iloc[i] if 'volume_change' in features_df.columns else 0.0
                }
                
                # Get optimized weights
                weights = await weight_optimizer.get_optimized_weights(market_conditions, window_size)
                
                # Store weights
                for j, name in enumerate(strategy_names):
                    weight_array[i, j] = weights[name]
            
            # Fill initial weights with equal weights
            for i in range(min(window_size, len(features_df))):
                weight_array[i] = 1.0 / len(strategy_names)
            
            # Calculate portfolio returns
            portfolio_returns = np.sum(weight_array * return_array, axis=1)
            
            # Calculate metrics
            metrics = PerformanceMetrics.calculate_portfolio_metrics(portfolio_returns)
            allocation_metrics = PerformanceMetrics.calculate_strategy_allocation_metrics(weight_array, return_array)
            
            # Combine metrics
            metrics.update(allocation_metrics)
            
            # Create result
            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'model_path': model_path,
                'metrics': metrics,
                'returns': portfolio_returns.tolist(),
                'dates': [d.isoformat() for d in features_df.index],
                'weights': {
                    'dates': [d.isoformat() for d in features_df.index],
                    'values': {
                        name: weight_array[:, i].tolist()
                        for i, name in enumerate(strategy_names)
                    }
                }
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error backtesting ML weights: {e}")
            return {'error': str(e)}
    
    async def compare_backtest_results(self, fixed_weights_result: Dict[str, Any], 
                                     ml_weights_result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare backtest results between fixed weights and ML weights.
        
        Args:
            fixed_weights_result: Result from backtest_fixed_weights
            ml_weights_result: Result from backtest_ml_weights
            
        Returns:
            Dictionary of comparison results
        """
        try:
            # Check for errors
            if 'error' in fixed_weights_result:
                return {'error': f"Fixed weights error: {fixed_weights_result['error']}"}
            
            if 'error' in ml_weights_result:
                return {'error': f"ML weights error: {ml_weights_result['error']}"}
            
            # Compare metrics
            fixed_metrics = fixed_weights_result.get('metrics', {})
            ml_metrics = ml_weights_result.get('metrics', {})
            
            comparison = PerformanceMetrics.compare_models(fixed_metrics, ml_metrics)
            
            # Create result
            result = {
                'symbol': ml_weights_result.get('symbol'),
                'timeframe': ml_weights_result.get('timeframe'),
                'start_date': ml_weights_result.get('start_date'),
                'end_date': ml_weights_result.get('end_date'),
                'fixed_weights': fixed_weights_result.get('weights'),
                'model_path': ml_weights_result.get('model_path'),
                'comparison': comparison,
                'fixed_metrics': fixed_metrics,
                'ml_metrics': ml_metrics
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error comparing backtest results: {e}")
            return {'error': str(e)}
    
    async def run_comparison_backtest(self, symbol: str, timeframe: str, 
                                    start_date: datetime, end_date: datetime,
                                    fixed_weights: Dict[str, float], model_path: str) -> Dict[str, Any]:
        """Run a comparison backtest between fixed weights and ML weights.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            start_date: Start date for backtesting
            end_date: End date for backtesting
            fixed_weights: Dictionary of fixed strategy weights
            model_path: Path to the ML model
            
        Returns:
            Dictionary of comparison results
        """
        try:
            # Run fixed weights backtest
            self.logger.info(f"Running fixed weights backtest for {symbol} ({timeframe})")
            fixed_result = await self.backtest_fixed_weights(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                weights=fixed_weights
            )
            
            # Run ML weights backtest
            self.logger.info(f"Running ML weights backtest for {symbol} ({timeframe})")
            ml_result = await self.backtest_ml_weights(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                model_path=model_path
            )
            
            # Compare results
            self.logger.info("Comparing backtest results")
            comparison = await self.compare_backtest_results(fixed_result, ml_result)
            
            # Create result
            result = {
                'fixed_result': fixed_result,
                'ml_result': ml_result,
                'comparison': comparison
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error running comparison backtest: {e}")
            return {'error': str(e)}
