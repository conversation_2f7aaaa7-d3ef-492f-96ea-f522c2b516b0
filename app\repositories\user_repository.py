"""Module for the UserRepository class."""
from typing import Optional
from sqlalchemy.orm import Session
from app.services.auth_service import User

class UserRepository:
    """Repository for user-related database operations."""

    def __init__(self, db_session: Session):
        self.db_session = db_session

    def add_user(self, user: User) -> User:
        """Adds a new user to the database."""
        self.db_session.add(user)
        self.db_session.commit()
        self.db_session.refresh(user)
        return user

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Retrieves a user by their ID."""
        return self.db_session.query(User).filter(User.id == user_id).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Retrieves a user by their email address."""
        return self.db_session.query(User).filter(User.email == email).first() 