#!/usr/bin/env python3
"""
Test script for Binance Futures Testnet integration
Tests all major components of the Binance dashboard integration:
- API routes functionality
- WebSocket connections
- Frontend routing
- Real-time data streaming

Created: June 16, 2025
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime
import sys
import os

# Add project root to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__))))

from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BinanceIntegrationTest:
    """Test suite for Binance integration components."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.ws_url = "ws://localhost:8000"
        self.settings = Settings()
        self.test_results = {}
        
    async def test_binance_client(self):
        """Test Binance client initialization and basic functionality."""
        logger.info("🔧 Testing Binance client...")
        
        try:
            client = BinanceExchangeClient(self.settings)
            await client._initialize_client()
            
            if client.client:
                # Test basic operations
                await client.client.ping()
                logger.info("✅ Binance client ping successful")
                
                # Test account info
                account = await client.client.futures_account()
                balance = float(account.get('totalWalletBalance', 0))
                logger.info(f"✅ Account balance: ${balance:.2f}")
                
                self.test_results['binance_client'] = {
                    'status': 'success',
                    'balance': balance,
                    'testnet': self.settings.use_testnet
                }
                
                await client.close_client()
            else:
                raise Exception("Client not initialized")
                
        except Exception as e:
            logger.error(f"❌ Binance client test failed: {e}")
            self.test_results['binance_client'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    async def test_api_routes(self):
        """Test Binance API routes."""
        logger.info("🌐 Testing API routes...")
        
        routes_to_test = [
            ("/api/binance/health", "GET"),
            ("/api/binance/status", "GET"),
            ("/api/binance/account", "GET"),
            ("/api/binance/positions", "GET"),
            ("/api/binance/orders", "GET"),
            ("/api/binance/trades?limit=10", "GET"),
            ("/api/binance/risk-metrics", "GET"),
        ]
        
        async with aiohttp.ClientSession() as session:
            results = {}
            
            for route, method in routes_to_test:
                try:
                    url = f"{self.base_url}{route}"
                    logger.info(f"Testing {method} {route}")
                    
                    if method == "GET":
                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                results[route] = {
                                    'status': 'success',
                                    'response_code': response.status,
                                    'data_keys': list(data.keys()) if isinstance(data, dict) else len(data)
                                }
                                logger.info(f"✅ {route} - OK")
                            else:
                                results[route] = {
                                    'status': 'failed',
                                    'response_code': response.status,
                                    'error': await response.text()
                                }
                                logger.warning(f"⚠️ {route} - Status: {response.status}")
                                
                except Exception as e:
                    results[route] = {
                        'status': 'error',
                        'error': str(e)
                    }
                    logger.error(f"❌ {route} - Error: {e}")
            
            self.test_results['api_routes'] = results
    
    async def test_websocket_connection(self):
        """Test WebSocket connection for real-time updates."""
        logger.info("🔌 Testing WebSocket connection...")
        
        try:
            import websockets
            
            async with websockets.connect(f"{self.ws_url}/ws/binance") as websocket:
                logger.info("✅ WebSocket connection established")
                
                # Send ping message
                ping_message = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send(json.dumps(ping_message))
                logger.info("📤 Sent ping message")
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    logger.info(f"📥 Received: {data.get('type', 'unknown')}")
                    
                    self.test_results['websocket'] = {
                        'status': 'success',
                        'connection': 'established',
                        'response_type': data.get('type', 'unknown')
                    }
                    
                except asyncio.TimeoutError:
                    logger.warning("⚠️ WebSocket response timeout")
                    self.test_results['websocket'] = {
                        'status': 'timeout',
                        'connection': 'established'
                    }
                    
        except ImportError:
            logger.warning("⚠️ websockets library not available, skipping WebSocket test")
            self.test_results['websocket'] = {
                'status': 'skipped',
                'reason': 'websockets library not available'
            }
        except Exception as e:
            logger.error(f"❌ WebSocket test failed: {e}")
            self.test_results['websocket'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def test_frontend_files(self):
        """Test that frontend files are properly created."""
        logger.info("📁 Testing frontend files...")
        
        files_to_check = [
            "app/dashboard/frontend/src/types/binance.ts",
            "app/dashboard/frontend/src/components/BinanceAccountPanel.tsx",
            "app/dashboard/frontend/src/pages/BinanceAccount.tsx",
        ]
        
        results = {}
        for file_path in files_to_check:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                results[file_path] = {
                    'status': 'exists',
                    'size_bytes': file_size
                }
                logger.info(f"✅ {file_path} - {file_size} bytes")
            else:
                results[file_path] = {
                    'status': 'missing'
                }
                logger.error(f"❌ {file_path} - Missing")
        
        self.test_results['frontend_files'] = results
    
    def test_backend_files(self):
        """Test that backend files are properly created."""
        logger.info("🐍 Testing backend files...")
        
        files_to_check = [
            "app/api/routes/binance_routes.py",
            "app/dashboard/api/binance_websocket.py",
        ]
        
        results = {}
        for file_path in files_to_check:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                results[file_path] = {
                    'status': 'exists',
                    'size_bytes': file_size
                }
                logger.info(f"✅ {file_path} - {file_size} bytes")
            else:
                results[file_path] = {
                    'status': 'missing'
                }
                logger.error(f"❌ {file_path} - Missing")
        
        self.test_results['backend_files'] = results
    
    async def run_all_tests(self):
        """Run all tests and generate a summary report."""
        logger.info("🚀 Starting Binance integration tests...")
        start_time = datetime.now()
        
        # Run tests
        self.test_frontend_files()
        self.test_backend_files()
        await self.test_binance_client()
        await self.test_api_routes()
        await self.test_websocket_connection()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Generate summary
        logger.info("📊 Test Summary:")
        logger.info(f"⏱️ Total duration: {duration:.2f} seconds")
        
        for test_name, result in self.test_results.items():
            if isinstance(result, dict):
                if result.get('status') == 'success':
                    logger.info(f"✅ {test_name}: PASSED")
                elif result.get('status') == 'failed':
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                elif result.get('status') == 'skipped':
                    logger.warning(f"⏭️ {test_name}: SKIPPED - {result.get('reason', 'Unknown reason')}")
                else:
                    # For complex results like API routes
                    success_count = sum(1 for v in result.values() if isinstance(v, dict) and v.get('status') == 'success')
                    total_count = len(result)
                    logger.info(f"📈 {test_name}: {success_count}/{total_count} passed")
        
        # Save detailed results
        report_file = f"binance_integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                'test_results': self.test_results,
                'summary': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration_seconds': duration,
                    'testnet_mode': self.settings.use_testnet
                }
            }, f, indent=2, default=str)
        
        logger.info(f"📄 Detailed report saved to: {report_file}")
        
        return self.test_results

async def main():
    """Main test runner."""
    test_suite = BinanceIntegrationTest()
    
    try:
        results = await test_suite.run_all_tests()
        
        # Determine overall success
        overall_success = True
        for test_name, result in results.items():
            if isinstance(result, dict):
                if result.get('status') == 'failed':
                    overall_success = False
                elif isinstance(result, dict) and any(
                    v.get('status') == 'failed' for v in result.values() 
                    if isinstance(v, dict)
                ):
                    overall_success = False
        
        if overall_success:
            logger.info("🎉 All tests completed successfully!")
            return 0
        else:
            logger.error("💥 Some tests failed. Check the report for details.")
            return 1
            
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"💥 Test suite failed with error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)