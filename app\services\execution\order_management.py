"""Order management functionality for the execution service."""
import logging
from typing import Dict, Optional, Union, Any
from datetime import datetime
from decimal import Decimal

from app.services.execution.models import Order, OrderType, OrderSide, OrderStatus
from app.services.exchange.binance_client import BinanceExchangeClient

logger = logging.getLogger(__name__)

class OrderManager:
    """Handles order placement and management."""
    
    def __init__(self, exchange_client: BinanceExchangeClient):
        """Initialize the order manager.
        
        Args:
            exchange_client: The exchange client to use for order operations.
        """
        self.exchange_client = exchange_client
        self.active_orders: Dict[str, Order] = {}
    
    async def place_order(self, order: Order) -> Optional[Order]:
        """Place an order on the exchange.
        
        Args:
            order: The order to place.
            
        Returns:
            The updated order with exchange information, or None if the order failed.
        """
        try:
            logger.info(f"Placing order: {order.symbol} {order.side.value} {order.quantity} {order.order_type.value}")
            
            exchange_order = await self.exchange_client.place_order(
                symbol=order.symbol,
                order_type=order.order_type.value,
                side=order.side.value,
                quantity=order.quantity,
                price=order.price,
                stop_price=order.stop_price,
                client_order_id=order.client_order_id
            )
            
            logger.info(f"Order response received: {exchange_order}")
            
            # Update the order with exchange response
            order.exchange_order_id = str(exchange_order.get('orderId'))
            order.status = self._map_exchange_status(exchange_order.get('status', ''))
            order.timestamp = datetime.fromtimestamp(exchange_order.get('updateTime', datetime.now().timestamp() * 1000) / 1000)
            
            filled_qty = float(exchange_order.get('executedQty', 0.0))
            avg_price_str = exchange_order.get('avgPrice', '0.0')
            avg_price = float(avg_price_str) if avg_price_str and float(avg_price_str) > 0 else order.price
            
            order.update_execution(
                filled_quantity=filled_qty,
                average_price=avg_price
            )
            
            # Store the active order
            if order.exchange_order_id is not None:
                self.active_orders[order.exchange_order_id] = order
            else:
                logger.warning(f"Order has no exchange_order_id, cannot track in active orders")
            
            logger.info(f"Order placed: {order.exchange_order_id} - {order.symbol} {order.side} {order.quantity}")
            return order
            
        except Exception as e:
            logger.error(f"Error placing order: {e}", exc_info=True)
            return None
    
    async def place_market_order(self, symbol: str, side: Union[OrderSide, str], 
                                quantity: float, client_order_id: Optional[str] = None) -> Optional[Order]:
        """Place a market order.
        
        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            client_order_id: Optional client order ID.
            
        Returns:
            The placed order, or None if the order failed.
        """
        # Normalize side to enum if string
        if isinstance(side, str):
            side = OrderSide(side)
            
        order = Order(
            symbol=symbol,
            side=side,
            order_type=OrderType.MARKET,
            quantity=quantity,
            client_order_id=client_order_id
        )
        
        return await self.place_order(order)
    
    async def place_limit_order(self, symbol: str, side: Union[OrderSide, str], 
                               quantity: float, price: float, 
                               client_order_id: Optional[str] = None) -> Optional[Order]:
        """Place a limit order.
        
        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            price: The limit price.
            client_order_id: Optional client order ID.
            
        Returns:
            The placed order, or None if the order failed.
        """
        # Normalize side to enum if string
        if isinstance(side, str):
            side = OrderSide(side)
            
        order = Order(
            symbol=symbol,
            side=side,
            order_type=OrderType.LIMIT,
            quantity=quantity,
            price=price,
            client_order_id=client_order_id
        )
        
        return await self.place_order(order)
    
    async def place_stop_market_order(self, symbol: str, side: Union[OrderSide, str], 
                                     quantity: float, stop_price: float, 
                                     client_order_id: Optional[str] = None) -> Optional[Order]:
        """Place a stop market order.
        
        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            stop_price: The stop price.
            client_order_id: Optional client order ID.
            
        Returns:
            The placed order, or None if the order failed.
        """
        # Normalize side to enum if string
        if isinstance(side, str):
            side = OrderSide(side)
            
        order = Order(
            symbol=symbol,
            side=side,
            order_type=OrderType.STOP_LOSS,
            quantity=quantity,
            stop_price=stop_price,
            client_order_id=client_order_id
        )
        
        return await self.place_order(order)
    
    async def place_take_profit_market_order(self, symbol: str, side: Union[OrderSide, str], 
                                           quantity: float, stop_price: float, 
                                           client_order_id: Optional[str] = None) -> Optional[Order]:
        """Place a take profit market order.
        
        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            stop_price: The stop price.
            client_order_id: Optional client order ID.
            
        Returns:
            The placed order, or None if the order failed.
        """
        # Normalize side to enum if string
        if isinstance(side, str):
            side = OrderSide(side)
            
        order = Order(
            symbol=symbol,
            side=side,
            order_type=OrderType.TAKE_PROFIT,
            quantity=quantity,
            stop_price=stop_price,
            client_order_id=client_order_id
        )
        
        return await self.place_order(order)
    
    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an order.
        
        Args:
            symbol: The trading symbol.
            order_id: The order ID to cancel.
            
        Returns:
            True if the order was cancelled, False otherwise.
        """
        try:
            logger.info(f"Cancelling order {order_id} for {symbol}")
            result = await self.exchange_client.cancel_order(symbol=symbol, order_id=order_id)
            
            if result and result.get('status') == 'CANCELED':
                logger.info(f"Order {order_id} cancelled successfully")
                
                # Remove from active orders if present
                if order_id in self.active_orders:
                    del self.active_orders[order_id]
                
                return True
            else:
                logger.warning(f"Order {order_id} cancellation returned unexpected result: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}", exc_info=True)
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get the status of an order.
        
        Args:
            order_id: The order ID to check.
            
        Returns:
            The order status, or None if the order was not found.
        """
        try:
            # First check our local cache
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                
                # If the order is in a final state, return it
                if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                    return order.status
                
                # Otherwise, refresh from the exchange
                try:
                    exchange_order = await self.exchange_client.get_order(
                        order_id=order_id,
                        symbol=order.symbol
                    )
                    
                    if exchange_order:
                        status = self._map_exchange_status(exchange_order.get('status', ''))
                        
                        # Update our cached order
                        order.status = status
                        
                        # If the order is filled, update execution details
                        if status == OrderStatus.FILLED:
                            if 'executedQty' in exchange_order and 'price' in exchange_order:
                                order.update_execution(
                                    filled_quantity=float(exchange_order['executedQty']),
                                    average_price=float(exchange_order['price']),
                                    commission=exchange_order.get('commission'),
                                    commission_asset=exchange_order.get('commissionAsset')
                                )
                            
                        # If the order is no longer active, remove it from active orders
                        if status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                            del self.active_orders[order_id]
                        
                        return status
                except Exception as e:
                    logger.error(f"Error getting order details from exchange: {e}")
                    return None
            
            # If not in cache, try to find it on the exchange
            for symbol in self._get_all_trading_symbols():
                try:
                    exchange_order = await self.exchange_client.get_order(
                        order_id=order_id,
                        symbol=symbol
                    )
                    if exchange_order:
                        return self._map_exchange_status(exchange_order.get('status', ''))
                except:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting order status for {order_id}: {e}", exc_info=True)
            return None
    
    def _map_exchange_status(self, exchange_status: str) -> OrderStatus:
        """Map exchange-specific status to our OrderStatus enum.
        
        Args:
            exchange_status: The exchange-specific status string.
            
        Returns:
            The corresponding OrderStatus enum value.
        """
        status_map = {
            'new': OrderStatus.OPEN,
            'open': OrderStatus.OPEN,
            'partially_filled': OrderStatus.PARTIALLY_FILLED,
            'filled': OrderStatus.FILLED,
            'canceled': OrderStatus.CANCELLED,
            'cancelled': OrderStatus.CANCELLED,
            'rejected': OrderStatus.REJECTED,
            'expired': OrderStatus.CANCELLED
        }
        
        return status_map.get(exchange_status.lower(), OrderStatus.UNKNOWN)
    
    def _get_all_trading_symbols(self) -> list:
        """Get all trading symbols from the exchange.
        
        Returns:
            A list of all trading symbols.
        """
        # This would ideally come from exchange info
        # For now, return a default list or get from settings
        return ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
