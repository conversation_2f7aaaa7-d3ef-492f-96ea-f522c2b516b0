"""
Configuration for the Dashboard API.
"""
import os
import json
from pydantic import BaseModel, ConfigDict
from typing import List

class DashboardSettings(BaseModel):
    model_config = ConfigDict(extra="allow")
    """Dashboard settings from environment variables."""
    # API settings
    API_PREFIX: str = "/api"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "replace_with_secure_random_key_in_production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    ALGORITHM: str = "HS256"

    # CORS settings
    CORS_ORIGINS: List[str] = []

    # Server settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    RELOAD: bool = os.getenv("RELOAD", "True").lower() == "true"

    def __init__(self, **data):
        super().__init__(**data)
        # Parse CORS_ORIGINS from environment if available
        cors_env = os.getenv("CORS_ORIGINS")
        if cors_env:
            try:
                self.CORS_ORIGINS = json.loads(cors_env)
            except json.JSONDecodeError:
                self.CORS_ORIGINS = cors_env.split(",")

        # Default CORS origins if none specified
        if not self.CORS_ORIGINS:
            self.CORS_ORIGINS = [
                "http://localhost:3000",
                "http://localhost:8000",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8000"
            ]

# Create and export settings object
settings = DashboardSettings()