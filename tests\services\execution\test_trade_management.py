import pytest
from unittest.mock import AsyncMock, MagicMock
import unittest.mock
import uuid

from app.services.execution.trade_management import TradeManager
from app.services.execution.trade_state import ManagedTrade, TradeStatus

@pytest.fixture
def mock_order_manager():
    """Provides a mock OrderManager."""
    return AsyncMock()

@pytest.fixture
def mock_db_session_factory():
    """Provides a mock database session factory."""
    session = AsyncMock()
    factory = MagicMock(return_value=session)
    # This is a bit of a trick to make the async context manager work
    session.__aenter__.return_value = session
    return factory

@pytest.mark.asyncio
async def test_trade_manager_initialization(mock_order_manager, mock_db_session_factory):
    """
    Tests that the TradeManager can be initialized successfully.
    """
    try:
        trade_manager = TradeManager(
            order_manager=mock_order_manager,
            db_session_factory=mock_db_session_factory
        )
        assert isinstance(trade_manager, TradeManager)
        assert trade_manager.order_manager is mock_order_manager
        assert trade_manager.db_session_factory is mock_db_session_factory
    except Exception as e:
        pytest.fail(f"TradeManager initialization failed: {e}")

@pytest.mark.asyncio
async def test_place_market_entry_order_with_sl_tp_filled(mock_order_manager, mock_db_session_factory):
    """
    Tests placing a market entry order that gets filled immediately,
    triggering SL/TP order placement.
    """
    # Arrange
    trade_manager = TradeManager(
        order_manager=mock_order_manager,
        db_session_factory=mock_db_session_factory
    )

    # Inject mock repository
    mock_repo = AsyncMock()
    trade_manager.trade_repository = mock_repo

    # Mock the return value of place_market_order
    entry_order = MagicMock()
    entry_order.exchange_order_id = "entry-123"
    entry_order.status = "FILLED"
    entry_order.average_price = 50000.0
    entry_order.filled_quantity = 1.0
    mock_order_manager.place_market_order.return_value = entry_order

    # Mock the return values for SL/TP orders
    sl_order = MagicMock()
    sl_order.exchange_order_id = "sl-123"
    mock_order_manager.place_stop_market_order.return_value = sl_order

    tp_order = MagicMock()
    tp_order.exchange_order_id = "tp-123"
    mock_order_manager.place_take_profit_market_order.return_value = tp_order

    # Act
    trade = await trade_manager.place_entry_order_with_sl_tp(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0,
        stop_loss_price=49000.0,
        take_profit_price=51000.0
    )

    # Assert
    assert trade is not None
    assert trade.status == "SLTP_PLACED"
    assert trade.entry_order_id == "entry-123"
    assert trade.sl_order_id == "sl-123"
    assert trade.tp_order_id == "tp-123"

    # Check that the correct calls were made
    mock_order_manager.place_market_order.assert_called_once_with(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0,
        client_order_id=None
    )
    mock_order_manager.place_stop_market_order.assert_called_once()
    mock_order_manager.place_take_profit_market_order.assert_called_once()

    # Check that the trade was created and updated in the repo
    mock_repo.create_trade.assert_called_once()
    mock_repo.update_trade.assert_called_once()

@pytest.mark.asyncio
async def test_place_limit_entry_order_pending(mock_order_manager, mock_db_session_factory):
    """
    Tests placing a limit entry order that remains pending.
    """
    # Arrange
    trade_manager = TradeManager(
        order_manager=mock_order_manager,
        db_session_factory=mock_db_session_factory
    )

    # Inject mock repository
    mock_repo = AsyncMock()
    trade_manager.trade_repository = mock_repo

    # Mock the return value of place_limit_order
    entry_order = MagicMock()
    entry_order.exchange_order_id = "entry-456"
    entry_order.status = "NEW"
    entry_order.filled_quantity = 0.0
    mock_order_manager.place_limit_order.return_value = entry_order

    # Act
    trade = await trade_manager.place_entry_order_with_sl_tp(
        symbol="ETHUSDT",
        side="BUY",
        quantity=1.0,
        entry_price=2000.0,
        stop_loss_price=1900.0,
        take_profit_price=2100.0
    )

    # Assert
    assert trade is not None
    assert trade.status == "PENDING_ENTRY"
    assert trade.entry_order_id == "entry-456"
    assert trade.sl_order_id is None
    assert trade.tp_order_id is None

    # Check that the correct calls were made
    mock_order_manager.place_limit_order.assert_called_once_with(
        symbol="ETHUSDT",
        side="BUY",
        quantity=1.0,
        price=2000.0,
        client_order_id=None
    )
    # Ensure SL/TP orders were NOT placed
    mock_order_manager.place_stop_market_order.assert_not_called()
    mock_order_manager.place_take_profit_market_order.assert_not_called()

    # Check that the trade was created in the repo, but not updated
    mock_repo.create_trade.assert_called_once()
    mock_repo.update_trade.assert_not_called()

@pytest.mark.asyncio
async def test_place_entry_order_fails(mock_order_manager, mock_db_session_factory):
    """
    Tests that the method handles a failure to place an entry order gracefully.
    """
    # Arrange
    trade_manager = TradeManager(
        order_manager=mock_order_manager,
        db_session_factory=mock_db_session_factory
    )

    # Inject mock repository
    mock_repo = AsyncMock()
    trade_manager.trade_repository = mock_repo

    # Mock the return value of place_market_order to be None
    mock_order_manager.place_market_order.return_value = None

    # Act
    trade = await trade_manager.place_entry_order_with_sl_tp(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0
    )

    # Assert
    assert trade is None

    # Check that no trade was created
    mock_repo.create_trade.assert_not_called()

@pytest.mark.asyncio
async def test_close_trade(mock_order_manager, mock_db_session_factory):
    """
    Tests that closing a trade cancels the associated orders and updates the trade state.
    """
    # Arrange
    trade_manager = TradeManager(
        order_manager=mock_order_manager,
        db_session_factory=mock_db_session_factory
    )

    # Inject mock repository and mock the get_trade_by_id method
    mock_repo = AsyncMock()
    trade_manager.trade_repository = mock_repo

    # Create a real ManagedTrade object to be "found"
    trade_id = uuid.uuid4()
    mock_trade = ManagedTrade(
        trade_id=trade_id,
        symbol="LTCUSDT",
        entry_side="BUY",
        status=TradeStatus.SLTP_PLACED
    )
    mock_trade.sl_order_id = "sl-789"
    mock_trade.tp_order_id = "tp-789"
    
    # Directly mock the method on the manager instance
    trade_manager.get_trade_by_id = AsyncMock(return_value=mock_trade)

    # Mock the cancellation calls to succeed
    mock_order_manager.cancel_order.return_value = True

    # Act
    result = await trade_manager.close_trade(str(trade_id), reason="TEST_CLOSE")

    # Assert
    assert result is True

    # Check that cancel_order was called for both SL and TP orders
    mock_order_manager.cancel_order.assert_any_call("LTCUSDT", "sl-789")
    mock_order_manager.cancel_order.assert_any_call("LTCUSDT", "tp-789")
    assert mock_order_manager.cancel_order.call_count == 2

    # Check that the trade status was updated in the repo
    mock_repo.update_trade.assert_called_once()
    updated_trade = mock_repo.update_trade.call_args[0][0]
    assert updated_trade.status == TradeStatus.CLOSED
    assert updated_trade.exit_reason == "TEST_CLOSE" 