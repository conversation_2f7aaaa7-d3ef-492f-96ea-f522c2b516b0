#!/usr/bin/env python3
"""
Enhanced Base Strategy for Ensemble Execution
Optimized for concurrent Redis-cached execution with real-time position tracking.
Implements Task 1.2.4 requirements.
"""

import asyncio
import json
import hashlib
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging

from app.models.trade_state import TradeState
from app.models.market_data import MarketData

# MCP Services for ensemble execution
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

@dataclass
class EnhancedSignal:
    """Enhanced signal structure for ensemble execution"""
    action: str  # 'BUY', 'SELL', 'HOLD'
    quantity: float
    price: float
    confidence: float
    timestamp: datetime
    strategy_name: str
    market_conditions: Dict[str, Any]
    execution_priority: int
    correlation_risk: float = 0.0
    cache_ttl: int = 30

@dataclass
class StrategyPerformance:
    """Strategy performance tracking for ensemble optimization"""
    strategy_name: str
    total_trades: int
    winning_trades: int
    total_pnl: float
    sharpe_ratio: float
    max_drawdown: float
    avg_execution_time_ms: float
    cache_hit_rate: float
    last_updated: datetime

@dataclass
class ConcurrentExecutionMetrics:
    """Metrics for concurrent strategy execution"""
    signal_generation_time_ms: float
    cache_lookup_time_ms: float
    market_analysis_time_ms: float
    total_execution_time_ms: float
    cache_hits: int
    cache_misses: int
    concurrent_executions: int
    timestamp: datetime

class EnhancedBaseStrategy(ABC):
    """
    Enhanced base strategy optimized for ensemble execution.
    
    Features:
    - Redis caching for sub-second performance
    - Concurrent execution support
    - Real-time position tracking
    - Supabase integration for analytics
    - Enhanced signal generation with metadata
    """
    
    def __init__(
        self,
        symbol: str,
        timeframe: str,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        self.symbol = symbol
        self.timeframe = timeframe
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.config = config or self._default_config()
        
        # Strategy identification
        self.name = self.__class__.__name__
        self.strategy_id = f"{self.name}_{symbol}_{timeframe}"
        self.logger = logging.getLogger(self.name)
        
        # Performance tracking
        self.performance = StrategyPerformance(
            strategy_name=self.name,
            total_trades=0,
            winning_trades=0,
            total_pnl=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            avg_execution_time_ms=0.0,
            cache_hit_rate=0.0,
            last_updated=datetime.now()
        )
        
        # Cache keys
        self.SIGNAL_CACHE_KEY = f"strategy:{self.strategy_id}:signal"
        self.ANALYSIS_CACHE_KEY = f"strategy:{self.strategy_id}:analysis" 
        self.PERFORMANCE_CACHE_KEY = f"strategy:{self.strategy_id}:performance"
        self.POSITION_CACHE_KEY = f"strategy:{self.strategy_id}:position"
        
        # State tracking
        self.last_execution_time = datetime.min
        self.execution_count = 0
        self.cache_operations = 0
        
        self.logger.info(f"Initialized enhanced strategy: {self.strategy_id}")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for enhanced strategy"""
        return {
            "cache_ttl_signals": 30,       # 30 seconds
            "cache_ttl_analysis": 60,      # 1 minute
            "cache_ttl_performance": 300,  # 5 minutes
            "cache_ttl_positions": 10,     # 10 seconds
            "enable_concurrent_execution": True,
            "enable_performance_tracking": True,
            "enable_position_tracking": True,
            "max_execution_time_ms": 500,  # 500ms target
            "confidence_decay_factor": 0.95,
            "position_size_limit": 0.1,
            "risk_correlation_threshold": 0.8
        }
    
    async def generate_enhanced_signal(
        self,
        market_data: MarketData
    ) -> EnhancedSignal:
        """
        Generate enhanced signal with caching and concurrent execution support.
        This is the main entry point for ensemble execution.
        """
        start_time = datetime.now()
        
        try:
            # Generate market conditions hash for caching
            market_hash = self._generate_market_hash(market_data)
            
            # Check cache first for recent signal
            cached_signal = await self._get_cached_signal(market_hash)
            if cached_signal:
                self._update_cache_metrics(True)
                return cached_signal
            
            # Generate fresh signal with market analysis
            analysis_start = datetime.now()
            market_analysis = await self._get_cached_market_analysis(market_data, market_hash)
            analysis_time = (datetime.now() - analysis_start).total_seconds() * 1000
            
            # Generate signal based on analysis
            signal_start = datetime.now()
            raw_signal = await self._generate_raw_signal(market_data, market_analysis)
            signal_time = (datetime.now() - signal_start).total_seconds() * 1000
            
            # Create enhanced signal with metadata
            enhanced_signal = EnhancedSignal(
                action=raw_signal['action'],
                quantity=raw_signal['quantity'],
                price=raw_signal['price'],
                confidence=raw_signal['confidence'],
                timestamp=datetime.now(),
                strategy_name=self.name,
                market_conditions=market_analysis,
                execution_priority=self._calculate_execution_priority(raw_signal),
                correlation_risk=await self._calculate_correlation_risk(market_data),
                cache_ttl=self.config["cache_ttl_signals"]
            )
            
            # Cache the enhanced signal
            await self._cache_signal(enhanced_signal, market_hash)
            
            # Update performance metrics
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            await self._update_execution_metrics(total_time, analysis_time, signal_time)
            
            # Track position if enabled
            if self.config["enable_position_tracking"]:
                await self._track_position_change(enhanced_signal, market_data)
            
            self._update_cache_metrics(False)
            self.logger.debug(f"Generated signal: {enhanced_signal.action} conf={enhanced_signal.confidence:.3f} in {total_time:.1f}ms")
            
            return enhanced_signal
            
        except Exception as e:
            self.logger.error(f"Enhanced signal generation failed: {e}")
            # Return safe default signal
            return self._create_default_signal(market_data)
    
    async def _get_cached_signal(self, market_hash: str) -> Optional[EnhancedSignal]:
        """Get cached signal if available and valid"""
        try:
            cache_key = f"{self.SIGNAL_CACHE_KEY}:{market_hash}"
            cached_data = await self.redis_service.get(cache_key)
            
            if cached_data:
                signal_data = json.loads(cached_data)
                cache_time = datetime.fromisoformat(signal_data['timestamp'])
                
                # Check if cache is still valid
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_signals"]):
                    return EnhancedSignal(**signal_data)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Cache retrieval failed: {e}")
            return None
    
    async def _get_cached_market_analysis(
        self,
        market_data: MarketData,
        market_hash: str
    ) -> Dict[str, Any]:
        """Get market analysis with caching"""
        try:
            cache_key = f"{self.ANALYSIS_CACHE_KEY}:{market_hash}"
            cached_analysis = await self.redis_service.get(cache_key)
            
            if cached_analysis:
                analysis_data = json.loads(cached_analysis)
                cache_time = datetime.fromisoformat(analysis_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_analysis"]):
                    return analysis_data['analysis']
            
            # Generate fresh analysis
            analysis = await self._perform_market_analysis(market_data)
            
            # Cache the analysis
            cache_data = {
                'analysis': analysis,
                'timestamp': datetime.now().isoformat(),
                'market_hash': market_hash
            }
            
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_analysis"],
                json.dumps(cache_data, default=str)
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Market analysis failed: {e}")
            return self._default_market_analysis()
    
    async def _cache_signal(self, signal: EnhancedSignal, market_hash: str) -> None:
        """Cache the enhanced signal"""
        try:
            cache_key = f"{self.SIGNAL_CACHE_KEY}:{market_hash}"
            cache_data = asdict(signal)
            
            await self.redis_service.setex(
                cache_key,
                signal.cache_ttl,
                json.dumps(cache_data, default=str)
            )
            
            self.cache_operations += 1
            
        except Exception as e:
            self.logger.warning(f"Signal caching failed: {e}")
    
    async def _track_position_change(
        self,
        signal: EnhancedSignal,
        market_data: MarketData
    ) -> None:
        """Track position changes for ensemble analytics"""
        if not self.supabase_service:
            return
            
        try:
            position_data = {
                'strategy_name': self.name,
                'symbol': market_data.symbol,
                'action': signal.action,
                'quantity': signal.quantity,
                'price': signal.price,
                'confidence': signal.confidence,
                'timestamp': signal.timestamp.isoformat(),
                'market_conditions': signal.market_conditions,
                'execution_priority': signal.execution_priority,
                'correlation_risk': signal.correlation_risk
            }
            
            # Store in Supabase for multi-strategy tracking
            await self.supabase_service.store_trade_execution(position_data)
            
            # Also cache current position
            cache_key = f"{self.POSITION_CACHE_KEY}:current"
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_positions"],
                json.dumps(position_data, default=str)
            )
            
        except Exception as e:
            self.logger.error(f"Position tracking failed: {e}")
    
    async def _update_execution_metrics(
        self,
        total_time: float,
        analysis_time: float,
        signal_time: float
    ) -> None:
        """Update execution performance metrics"""
        try:
            self.execution_count += 1
            
            # Update average execution time
            if self.performance.avg_execution_time_ms == 0:
                self.performance.avg_execution_time_ms = total_time
            else:
                # Exponential moving average
                alpha = 0.1
                self.performance.avg_execution_time_ms = (
                    alpha * total_time + 
                    (1 - alpha) * self.performance.avg_execution_time_ms
                )
            
            self.performance.last_updated = datetime.now()
            
            # Cache updated performance metrics
            await self.redis_service.setex(
                self.PERFORMANCE_CACHE_KEY,
                self.config["cache_ttl_performance"],
                json.dumps(asdict(self.performance), default=str)
            )
            
            # Log performance warning if execution is slow
            if total_time > self.config["max_execution_time_ms"]:
                self.logger.warning(f"Slow execution: {total_time:.1f}ms > {self.config['max_execution_time_ms']}ms")
            
        except Exception as e:
            self.logger.error(f"Metrics update failed: {e}")
    
    def _update_cache_metrics(self, cache_hit: bool) -> None:
        """Update cache hit rate metrics"""
        if cache_hit:
            hit_count = getattr(self, '_cache_hits', 0) + 1
            setattr(self, '_cache_hits', hit_count)
        
        total_requests = getattr(self, '_total_cache_requests', 0) + 1
        setattr(self, '_total_cache_requests', total_requests)
        
        # Update cache hit rate
        self.performance.cache_hit_rate = getattr(self, '_cache_hits', 0) / total_requests
    
    def _generate_market_hash(self, market_data: MarketData) -> str:
        """Generate hash for market conditions to use as cache key"""
        market_string = f"{market_data.symbol}:{market_data.timestamp.minute}:{market_data.price:.2f}:{market_data.volume}"
        return hashlib.md5(market_string.encode()).hexdigest()[:12]
    
    def _calculate_execution_priority(self, signal: Dict[str, Any]) -> int:
        """Calculate execution priority based on signal characteristics"""
        confidence = signal.get('confidence', 0)
        action = signal.get('action', 'HOLD')
        
        if action == 'HOLD':
            return 0
        elif confidence >= 0.8:
            return 1  # High priority
        elif confidence >= 0.6:
            return 2  # Medium priority
        else:
            return 3  # Low priority
    
    async def _calculate_correlation_risk(self, market_data: MarketData) -> float:
        """Calculate correlation risk with other strategies"""
        # Simplified implementation - in production would calculate actual correlations
        return 0.3  # Default moderate correlation risk
    
    def _create_default_signal(self, market_data: MarketData) -> EnhancedSignal:
        """Create safe default signal for error cases"""
        return EnhancedSignal(
            action='HOLD',
            quantity=0,
            price=market_data.price,
            confidence=0,
            timestamp=datetime.now(),
            strategy_name=self.name,
            market_conditions={},
            execution_priority=0,
            correlation_risk=0.0,
            cache_ttl=self.config["cache_ttl_signals"]
        )
    
    def _default_market_analysis(self) -> Dict[str, Any]:
        """Default market analysis for error cases"""
        return {
            'volatility': 0.02,
            'trend': 'neutral',
            'volume_ratio': 1.0,
            'rsi': 50,
            'macd': 0,
            'support': 0,
            'resistance': 0,
            'confidence': 0
        }
    
    # Abstract methods to be implemented by specific strategies
    
    @abstractmethod
    async def _perform_market_analysis(self, market_data: MarketData) -> Dict[str, Any]:
        """
        Perform strategy-specific market analysis.
        Must be implemented by each strategy.
        """
        pass
    
    @abstractmethod
    async def _generate_raw_signal(
        self,
        market_data: MarketData,
        market_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate raw signal based on strategy logic.
        Must return dict with: action, quantity, price, confidence
        """
        pass
    
    # Utility methods for ensemble integration
    
    async def get_current_performance(self) -> StrategyPerformance:
        """Get current strategy performance metrics"""
        return self.performance
    
    async def get_execution_metrics(self) -> ConcurrentExecutionMetrics:
        """Get concurrent execution metrics"""
        return ConcurrentExecutionMetrics(
            signal_generation_time_ms=self.performance.avg_execution_time_ms,
            cache_lookup_time_ms=0,  # Would track separately in production
            market_analysis_time_ms=0,  # Would track separately in production
            total_execution_time_ms=self.performance.avg_execution_time_ms,
            cache_hits=getattr(self, '_cache_hits', 0),
            cache_misses=getattr(self, '_total_cache_requests', 0) - getattr(self, '_cache_hits', 0),
            concurrent_executions=self.execution_count,
            timestamp=datetime.now()
        )
    
    async def reset_performance_metrics(self) -> None:
        """Reset performance metrics for new evaluation period"""
        self.performance = StrategyPerformance(
            strategy_name=self.name,
            total_trades=0,
            winning_trades=0,
            total_pnl=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            avg_execution_time_ms=0.0,
            cache_hit_rate=0.0,
            last_updated=datetime.now()
        )
        
        # Reset cache metrics
        setattr(self, '_cache_hits', 0)
        setattr(self, '_total_cache_requests', 0)
        
        self.logger.info(f"Reset performance metrics for {self.strategy_id}")
    
    async def update_trade_result(self, trade_result: TradeState) -> None:
        """Update strategy performance based on trade result"""
        try:
            self.performance.total_trades += 1
            
            if trade_result.pnl > 0:
                self.performance.winning_trades += 1
            
            self.performance.total_pnl += trade_result.pnl
            
            # Update Sharpe ratio (simplified calculation)
            if self.performance.total_trades > 1:
                win_rate = self.performance.winning_trades / self.performance.total_trades
                avg_return = self.performance.total_pnl / self.performance.total_trades
                # Simplified Sharpe calculation
                self.performance.sharpe_ratio = avg_return / 0.02 if avg_return > 0 else 0
            
            self.performance.last_updated = datetime.now()
            
            # Store updated performance
            await self.redis_service.setex(
                self.PERFORMANCE_CACHE_KEY,
                self.config["cache_ttl_performance"],
                json.dumps(asdict(self.performance), default=str)
            )
            
        except Exception as e:
            self.logger.error(f"Trade result update failed: {e}")

# Utility functions for strategy ensemble integration

async def create_enhanced_strategy_pool(
    strategy_classes: List[type],
    symbol: str,
    timeframe: str,
    redis_service: RedisService,
    supabase_service: Optional[SupabaseService] = None,
    strategy_configs: Optional[Dict[str, Dict]] = None
) -> List[EnhancedBaseStrategy]:
    """Create a pool of enhanced strategies for ensemble execution"""
    
    strategies = []
    
    for strategy_class in strategy_classes:
        try:
            config = strategy_configs.get(strategy_class.__name__, {}) if strategy_configs else {}
            
            strategy = strategy_class(
                symbol=symbol,
                timeframe=timeframe,
                redis_service=redis_service,
                supabase_service=supabase_service,
                config=config
            )
            
            strategies.append(strategy)
            logger.info(f"Created enhanced strategy: {strategy.name}")
            
        except Exception as e:
            logger.error(f"Failed to create strategy {strategy_class.__name__}: {e}")
    
    return strategies

async def execute_strategies_concurrently(
    strategies: List[EnhancedBaseStrategy],
    market_data: MarketData
) -> List[EnhancedSignal]:
    """Execute multiple strategies concurrently with performance tracking"""
    
    start_time = datetime.now()
    
    # Execute all strategies concurrently
    tasks = [strategy.generate_enhanced_signal(market_data) for strategy in strategies]
    signals = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Filter out exceptions and collect valid signals
    valid_signals = []
    for i, signal in enumerate(signals):
        if isinstance(signal, Exception):
            logger.error(f"Strategy {strategies[i].name} failed: {signal}")
        else:
            valid_signals.append(signal)
    
    total_time = (datetime.now() - start_time).total_seconds() * 1000
    
    logger.info(f"Executed {len(strategies)} strategies concurrently in {total_time:.1f}ms, got {len(valid_signals)} valid signals")
    
    return valid_signals