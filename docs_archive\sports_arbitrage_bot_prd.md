# Product Requirements Document (PRD): Sports Arbitrage Bot

**Version:** 1.0
**Date:** 2025-06-17
**Owner:** User/Developer

---

## 1. Problem Statement

Manual sports arbitrage betting is time-consuming, requires constant monitoring for fleeting opportunities, and is highly susceptible to human error. Bookmakers actively limit accounts engaged in consistent profitable arbitrage, particularly for those using automated methods or displaying patterns indicative of such activity. There is a critical need for an automated system that can:

1.  Rapidly detect live arbitrage opportunities across multiple Nigerian bookmakers.
2.  Execute simultaneous bets with precision and speed to capitalize on these short-lived opportunities.
3.  Implement sophisticated anti-limitation techniques to ensure the longevity and profitability of bookmaker accounts, particularly focusing on Pinnacle as a less-limiting counterparty.

---

## 2. Strategic Principles & Goals

### A. 80/20 Pareto Principle Application (Cost & Efficiency Optimized for Live Arbing)

We will focus 80% of our development and operational effort on the 20% of activities that yield the highest impact and profit, specifically for Live Arbing. This includes:

*   **High-Value Live Arbitrage Opportunities**: Prioritize detecting and acting on live arbs with the highest realistic profit margins (e.g., >2-3%), as these represent the most significant returns. The system will be optimized for speed to capture these fleeting opportunities.
*   **Bookmaker Pair Optimization**: 
    *   **Pinnacle Integration**: Heavily prioritize arbs involving Pinnacle as one leg, given their reputation for higher limits and less aggressive account limitation.
    *   **High-Volume Nigerian Bookmakers**: Focus on the User's confirmed bookmakers: `SportyBet`, `Bet9ja`, `BetWGB`, `BetKing`, `MSport`, `betPawa`, `NairaBet`. Development will focus on robust integration with these specific platforms.
    *   **Least-Resistance Pairs**: Through continuous monitoring and historical data, identify and prioritize bookmaker pairs that consistently offer arbs and exhibit slower odds movement or less aggressive anti-bot measures. This identification will be an ongoing process based on logged bet outcomes and account health metrics.
*   **Dynamic Sports Specialization**: Concentrate primary live arbing efforts on `Basketball` and `Tennis` due to their rapid, frequent odds changes, which generate more live arb opportunities.
*   **Anti-Limitation Strategy Focus**: Devote significant effort to implementing and refining the most effective account longevity techniques. Preventing limitations directly impacts long-term, sustainable profitability.

### B. Development Methodology

*   **Test-Driven Development (TDD)**: Every new feature (e.g., a new scraping module for a bookmaker, a new arb calculation logic, an anti-limitation technique) will begin with writing a failing automated test. We will then write the minimum code necessary to pass the test, followed by refactoring to improve code quality and maintainability. This ensures robust, reliable, and maintainable code from the outset.
*   **Modular Design**: The bot will be built with loosely coupled, independent modules for each major function (e.g., data collection, arb detection, execution, risk management, account management). This modularity will facilitate easier updates, maintenance, and adaptation to changing bookmaker websites, anti-bot measures, or new arbitrage opportunities.

### C. Goals & Measurable Outcomes

*   Successfully identify and execute at least 5 live arbitrage opportunities per day on average, with an average profit margin of >2% (after accounting for fees/slippage).
*   Maintain active status on at least 70% of integrated non-Pinnacle bookmaker accounts for a period of 3 months without significant stake limitations or closures.
*   Achieve an average execution time from arb detection to simultaneous bet placement of under 5 seconds.
*   Ensure the system operates with minimal manual intervention for 90% of detected opportunities.

---

## 3. Technical Stack & Services

### A. Core Components (Free/Low-Cost)

*   **Browser Automation**: `Browser-Use` (open-source AI agent) powered by `Playwright` and `undetected-chromedriver`. This combination provides unparalleled human-like interaction capabilities, essential for navigating complex web interfaces and bypassing sophisticated anti-bot measures. It allows for dynamic adaptation to UI changes. **Note:** Rigorous testing and continuous fine-tuning of `Browser-Use`'s natural language commands will be essential, with a fallback to direct Playwright selectors if reliability issues arise for critical betting paths.
*   **Backend/Logic**: Python 3.x with `asyncio` for high-performance concurrent operations. This enables the bot to handle multiple real-time data streams, perform rapid calculations, and manage simultaneous bet placements efficiently.
*   **Data Scraping**: `Beautiful Soup` and `Requests` for initial static HTML parsing from `Breaking-Bet.com`'s free tier. For dynamic content and direct bookmaker interaction, `Playwright` will be the primary tool, integrated with `Browser-Use`.
*   **Database**: **Supabase** (Free Tier). This will serve as the central, real-time data store for:
    *   **Arbitrage Opportunities**: Detailed records of detected, active, placed, and completed arbitrage opportunities.
    *   **Bookmaker Accounts**: Securely stored (encrypted) bookmaker account credentials, real-time balances, betting history per account, and status (e.g., limited, active).
    *   **Betting History**: Comprehensive logs of all placed bets, including stakes, odds, outcomes, and associated arb details.
    *   **Profit & Loss (P&L)**: Real-time P&L tracking per account and overall.
    *   **Configuration Settings**: Dynamic configuration for the bot, including active bookmakers, stake sizing rules, anti-limitation parameters, and user preferences.
    *   **Real-time Updates**: Leverage Supabase's real-time capabilities for internal monitoring, triggering execution workflows, and potentially a future lightweight dashboard.
*   **Monitoring/Alerting**: A custom Python-based notification system (e.g., integrating with Telegram Bot API or email service) for real-time alerts on new arbs, bet outcomes, account status changes, and critical errors.
*   **Deployment Environment**: A dedicated Virtual Private Server (VPS) or a low-cost cloud instance (e.g., AWS EC2 Free Tier, Google Cloud Free Tier) for stable, always-on operation and consistent IP addresses. This provides a controlled environment for running the bot and managing proxies. **Critical Note:** The effectiveness of avoiding IP blocks and maintaining human-like behavior heavily relies on the use of *reliable, paid residential proxy services* rather than free ones, which are often quickly detected and blocked.

### B. Arb Alert Service (Cost-Optimized for Live Arbing)

*   **Primary Source**: `Breaking-Bet.com` (`FREE` tier for arbs up to 1% profit). This service will be used as the initial trigger mechanism. The bot will continuously scrape this free tier to identify potential matches or events that are likely to have arbitrage opportunities.
    *   **Strategy for >1% Arbs**: For arbitrage opportunities above 1% (which `Breaking-Bet.com`'s free tier obscures), the bot will use the identified match/market information to immediately trigger direct, real-time odds scraping from the relevant bookmakers (your confirmed list) using `Playwright` via `Browser-Use`. This allows us to access the full potential of higher-profit arbs without paying for a premium alert service.
*   **Validation/Supplement**: `Oddspedia` (free sure bets finder) can be used as an optional secondary validation source to cross-check opportunities or identify bookmakers that `Breaking-Bet.com` might miss. This will be a lower priority, manual check if discrepancies arise.

---

## 4. End-to-End Live Arbing Pipeline (Detailed)

### Phase 1: Real-Time Opportunity Detection & Data Collection

1.  **Breaking-Bet Free Tier Monitoring & Initial Trigger**: 
    *   The bot will continuously (e.g., every 5-10 seconds) scrape the live arbitrage section of `Breaking-Bet.com`'s free tier. It will parse the displayed arbitrage opportunities, noting the events, involved bookmakers, and any visible profit percentages (up to 1%).
    *   This initial scrape acts as a rapid "first-pass" filter. For any detected events, regardless of the displayed profit (especially those >1% where details are hidden), the system will flag them as potential high-value arbs.
2.  **Direct Bookmaker Odds Fetching (High-Speed)**: 
    *   Upon detection of a potential arb from `Breaking-Bet` (or an internal trigger), the system will immediately initiate parallel, high-speed odds fetching directly from the relevant bookmaker websites (from your confirmed list: `Pinnacle`, `SportyBet`, `Bet9ja`, `BetWGB`, `BetKing`, `MSport`, `betPawa`, `NairaBet`).
    *   `Browser-Use` (leveraging `Playwright` and `undetected-chromedriver`) will navigate to the specific match/market pages on these bookmakers and extract the live odds for all relevant outcomes.
    *   This direct scraping is paramount for obtaining the most real-time, un-delayed odds data, critical for capturing fleeting live arbitrage opportunities with higher profit margins.
3.  **Data Storage & Update (Supabase)**: All raw and processed odds data, as well as the details of each detected arb opportunity (e.g., timestamp, profit %, bookmakers, event details, current odds, calculated stakes), will be stored and continuously updated in Supabase. This provides a persistent record for analysis, error recovery, and historical performance tracking.

### Phase 2: Arbitrage Calculation & Decision Engine

1.  **Arbitrage Calculation**: Implement precise algorithms to calculate the exact arbitrage percentage for each opportunity using the most current, directly fetched odds. This will involve handling different odds formats (decimal, fractional, American) and ensuring accurate conversions.
2.  **Profitability & Threshold Filtering**: Opportunities will be filtered based on a configurable minimum profit threshold (e.g., 2% for live arbs, which can be adjusted in Supabase configuration). Only arbs exceeding this threshold and with a recalculated profit margin not less than 0.5% will proceed to execution.
3.  **Stake Calculation & Allocation**: 
    *   Utilize a dynamic stake sizing strategy, such as a modified Kelly Criterion or a fixed percentage of available bankroll per bookmaker. The system will calculate optimal bet amounts for each leg of the arb, taking into account current account balances, minimum/maximum bet limits of each bookmaker, and any pre-configured anti-limitation stake adjustments.
    *   Supabase will store real-time account balances to inform these calculations.
4.  **Real-time Validation & Order Prioritization**: Immediately before initiating bet placement, the system will perform a final, rapid re-fetch and re-validation of the odds to ensure the arbitrage opportunity is still valid and profitable. Opportunities will be prioritized based on profit margin and the perceived volatility of the odds (e.g., less volatile arbs might be slightly delayed for more critical, higher-margin opportunities).

### Phase 3: Automated, Anti-Limitation Execution

1.  **Simultaneous Bet Placement Orchestration**: 
    *   For each validated arb, `Browser-Use` will simultaneously open and navigate to the relevant match/market pages on all involved bookmakers (e.g., Pinnacle and SportyBet). Each bookmaker will operate in its own isolated `Playwright` browser context to maintain distinct sessions and prevent cross-site detection.
    *   Bets will be placed as close to simultaneously as possible. The system will queue HTTP requests or `Playwright` actions in parallel to minimize the time window during which odds might change.
    *   **Execution Order**: The system will prioritize placing the bet on the bookmaker most likely to limit accounts first (e.g., `SportyBet`, `Bet9ja`), followed by the less restrictive one (e.g., `Pinnacle`). This reduces exposure on the more sensitive accounts.
2.  **Anti-Limitation Techniques (Crucial for Account Longevity)**: These techniques are paramount for sustainable profitability and will be continuously refined.
    *   **Evading "Honeypot" Traps**: The bot will incorporate a logic that flags extremely high, "too good to be true" profit margins, especially if they appear on less-frequented bookmakers or accounts that have previously been flagged. This will trigger a more cautious approach, potentially a manual review, or a smaller initial stake to test the waters.
    *   **Sophisticated Withdrawal Patterns**: Beyond just avoiding frequent withdrawals, the bot's withdrawal management strategy will involve varying withdrawal amounts and timings to mimic genuine human behavior. Consistently high account balances with small, regular withdrawals can be a red flag. The system will aim to cycle funds more naturally, perhaps by letting balances build up slightly longer or making less frequent, larger withdrawals. Additionally, the bot will indicate when it is safe for manual withdrawals and recommend optimal amounts to minimize detection risk. Similarly, for deposits, the bot will indicate when it is safe to deposit and suggest optimal amounts based on account activity and current needs.
    *   **Refined "Mug" Betting**: The "mug" bets will be as genuinely random and varied as possible in terms of sport, market, and timing, rather than following a predictable routine. This will include betting on popular events without an arb component, just to blend in with recreational bettors.
    *   **Rounded Stakes**: Bet amounts will be slightly rounded to appear less machine-generated (e.g., if Kelly suggests $102.34, the bot might bet $100 or $105). Rounding strategy will be configurable in Supabase.
    *   **Varying Bet Patterns**: Avoid rigid betting patterns. Introduce slight, random variations in stake sizes (within reasonable limits) and timing of bets. Do not always bet the maximum allowed stake.
    *   **Consistent IP/Device Fingerprinting**: Utilize a dedicated proxy/VPN for each bookmaker account or groups of accounts to maintain a consistent IP address and geographical location (preferably Nigerian IPs). `Playwright` browser contexts will be configured to maintain consistent browser fingerprints (user agent, screen size, language settings, etc.) for each session.
    *   **Human-like Delays**: Introduce small, seemingly random delays between browser actions (e.g., navigating to a page, clicking an odd, entering a stake, confirming a bet) rather than instantaneous, robotic actions. This mimics human browsing speed.
    *   **Time of Day Awareness**: Consider varying betting activity times to mimic human behavior. Avoid predictable, 24/7 robotic activity, especially during unusual hours unless profitable opportunities are consistently present.
    *   **Small Stakes First (for new/risky accounts)**: When integrating a new bookmaker account or one known for quick limitations, the bot will initially place smaller stakes to "test the waters" and build a non-suspicious betting history before increasing stake sizes.
    *   **Account Rotation**: If the User has multiple accounts with the same bookmaker, the system will implement a strategy to rotate between them to spread betting activity and reduce the likelihood of individual account flags.
3.  **Supabase Update (Real-time)**: Immediately after a bet is placed (or attempted), its status, actual stake, odds, unique transaction IDs, and any errors will be logged and updated in Supabase. This ensures data integrity and provides real-time visibility into the bot's operations.

### Phase 4: Risk Management & Continuous Monitoring

1.  **Real-time P&L Tracking (Supabase)**: The system will continuously update and track account balances in Supabase based on bet outcomes (wins/losses). This provides a clear, aggregated overview of overall profitability and per-bookmaker performance.
2.  **Automated Alerting System**: Critical events will trigger real-time notifications via the configured alert system (e.g., Telegram).
    *   **Opportunity Alerts**: Notification for high-profit arbs detected that might require manual oversight.
    *   **Execution Alerts**: Confirmation of successful bet placements and detailed reports of failed attempts (with error codes/reasons).
    *   **Account Status Alerts**: Notifications for login failures, unusual balance changes, or suspected account limitations/closures.
    *   **System Health Alerts**: Alerts for bot crashes, proxy failures, or data feed issues.
3.  **Comprehensive Error Handling & Fallbacks**:
    *   **Dynamic Hedge Betting Integration**: If one leg of an arbitrage bet is successfully placed but the other fails due to odds change, account limitation, or technical error, the bot will immediately calculate and attempt to place a hedge bet on the remaining open position with the same bookmaker to minimize potential losses. This dynamic hedging can also be used to lock in a smaller guaranteed profit if market conditions shift unexpectedly after the first leg is placed. Manual intervention alerts will be triggered for such events.
    *   **Partial Bet Placement**: If one leg of an arbitrage bet fails to place after the other has been confirmed, the system will immediately attempt to place an offsetting bet on the remaining leg (if possible) to minimize potential losses. Manual intervention alerts will be triggered.
    *   **Dynamic Website Changes**: Implement logic to detect significant changes in bookmaker website layouts or element selectors. If detected, the system will alert for manual review and gracefully pause operations on that bookmaker until the scraping logic is updated.
    *   **Anti-Bot Challenges**: If CAPTCHAs or other verification challenges are encountered, the system will attempt to solve them via integrated third-party services (if applicable and cost-effective) or alert for immediate manual intervention.
    *   **Rate Limit Management**: Implement robust retry logic with exponential back-off for API requests or browser actions that hit rate limits, preventing temporary blocks.
    *   **Proxy Management**: Automatic proxy rotation and health checks to ensure reliable and consistent IP addresses.

### V. Nigerian Market Specifics

*   **Bookmaker Integration Priority**: Development and rigorous testing will primarily target your specified bookmakers: `Pinnacle`, `SportyBet`, `Bet9ja`, `BetWGB`, `BetKing`, `MSport`, `betPawa`, `NairaBet`. Each bookmaker will have a dedicated, extensible scraping and betting module.
*   **Local IP Addresses**: The deployment strategy will ensure that all bot traffic originates from Nigerian IP addresses (via dedicated VPNs or proxies) to make activity appear local and reduce regional geo-blocking or suspicion.
*   **Payment Methods Awareness**: The bot will not automate deposits or withdrawals. The system will track bookmaker balances based on manual input or successful bet outcomes. Any specific local payment method considerations for manual operations will be noted.

### VI. Cost Breakdown (Monthly) - Highly Optimized

*   **Breaking-Bet Subscription**: €0 (utilizing the free tier for initial triggers).
*   **Supabase**: $0 (utilizing the free tier for database and real-time features).
*   **Browser-Use/Playwright/Python Stack**: $0 (open source, self-hosted, running on your chosen VPS).
*   **VPS/Proxy Service**: ~$15-30/month (estimated cost for a reliable VPS for continuous operation and *quality paid residential proxies* essential for anti-limitation strategies and consistent IP locations).
*   **Total Estimated Operating Cost (Monthly)**: ~$15-30, focusing on minimizing recurring expenditures by prioritizing free services where feasible.

---

## 7. Out of Scope (for MVP)

*   Integration with bookmaker APIs (unless explicitly permitted and advantageous, which is rare for arbitrage).
*   Support for pre-match arbitrage beyond what the Breaking-Bet free tier offers as a trigger.
*   Full-fledged web dashboard (initial monitoring will be via logs and alerts).
*   Advanced machine learning for predictive odds movement (beyond simple profit calculation).
*   Multi-user support or public accessibility.

---

## 8. Success Criteria (Refined)

*   The bot successfully identifies and executes at least 5 profitable live arbitrage opportunities daily, demonstrating the ability to capitalize on fleeting market inefficiencies.
*   Achieve an average success rate of >80% for simultaneous bet placement on both legs of an arb, minimizing single-leg exposure.
*   Account longevity for non-Pinnacle bookmakers is extended beyond typical manual arbitrage lifespans (e.g., 3-6 months before significant limitations are applied).
*   The system can automatically adapt to minor UI changes on bookmaker websites and gracefully handle common errors (e.g., temporary rate limits).
*   All critical events (new arbs, bet outcomes, account issues) trigger immediate and reliable alerts.
*   The codebase adheres to TDD principles, with comprehensive unit and integration tests for core logic and bookmaker interactions.
*   **Average Account Longevity**: Maintain non-Pinnacle bookmaker accounts for an average of at least 3-6 months before significant stake limitations are applied, demonstrating the effectiveness of anti-limitation techniques.

---

## 9. References

*   [PostgreSQL vs. Supabase](https://medium.com/@shariq.ahmed525/postgresql-vs-supabase-df10e2795180)
*   [Understanding Your Backend Options in Flutter: Supabase vs. PostgreSQL](https://www.dhiwise.com/post/understand-backend-options-in-flutter-supabase-vs-postgresql)
*   [Supabase Next.js Quickstart](https://supabase.com/docs/guides/getting-started/quickstarts/nextjs)
*   [Supabase React Quickstart](https://supabase.com/docs/guides/getting-started/quickstarts/reactjs)
*   [Supabase Vue Quickstart](https://supabase.com/docs/guides/getting-started/quickstarts/vue)
*   [Supabase React Auth Quickstart](https://supabase.com/docs/guides/auth/quickstarts/react)
*   [Writing PRDs and Product Requirements](https://carlinyuen.medium.com/@carlinyuen/writing-prds-and-product-requirements-2effdb9c6def)
*   [How to Write a Product Requirements Document (PRD)](https://www.perforce.com/blog/alm/how-write-product-requirements-document-prd)