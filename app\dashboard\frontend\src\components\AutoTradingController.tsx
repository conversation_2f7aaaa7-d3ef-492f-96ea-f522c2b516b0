import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Switch,
  FormControlLabel,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Divider,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Pause,
  PlayCircleFilled,
  Settings,
  ExpandMore,
  Refresh,
  Emergency,
  Warning,
  CheckCircle,
  Error as ErrorIcon,
  Info,
  TrendingUp,
  ShowChart,
  AccountBalance,
  Speed,
  Timeline
} from '@mui/icons-material';
import apiClient from '../services/apiService';

// Types
interface TradingParameters {
  session_name?: string;
  max_position_size: number;
  portfolio_exposure_limit: number;
  max_drawdown_limit: number;
  stop_loss_pct: number;
  take_profit_pct: number;
  grid_strategy_enabled: boolean;
  ta_strategy_enabled: boolean;
  trend_strategy_enabled: boolean;
  min_confidence_threshold: number;
  order_type: 'MARKET' | 'LIMIT';
  slippage_tolerance: number;
  execution_speed: 'FAST' | 'NORMAL' | 'CAREFUL';
  model_refresh_frequency: number;
  weight_confidence_threshold: number;
  enable_dynamic_rebalancing: boolean;
  alert_thresholds?: { [key: string]: number };
  reporting_frequency: number;
  telegram_alerts_enabled: boolean;
  symbols: string[];
  base_currency: string;
}

interface TradingStatus {
  session_active: boolean;
  session_id?: string;
  status: string;
  start_time?: string;
  duration_seconds?: number;
  parameters?: TradingParameters;
  performance?: {
    total_pnl: number;
    total_return: number;
    sharpe_ratio: number;
    max_drawdown: number;
    current_drawdown: number;
    total_trades: number;
    win_rate: number;
    strategy_performance: { [key: string]: number };
    strategy_weights: { [key: string]: number };
  };
  recent_trades?: any[];
  recent_alerts?: any[];
  market_data?: { [key: string]: any };
  strategy_signals?: { [key: string]: any };
  system_health?: { [key: string]: any };
}

interface Session {
  id: string;
  status: string;
  start_time: string;
  end_time?: string;
  duration: string;
  total_pnl: number;
  total_trades: number;
  win_rate: number;
  symbols: string[];
}

const AutoTradingController: React.FC = () => {
  // State management
  const [status, setStatus] = useState<TradingStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configOpen, setConfigOpen] = useState(false);
  const [emergencyOpen, setEmergencyOpen] = useState(false);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [sessionsLoading, setSessionsLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' as 'info' | 'success' | 'warning' | 'error' });

  // Configuration state
  const [config, setConfig] = useState<TradingParameters>({
    session_name: '',
    max_position_size: 0.1,
    portfolio_exposure_limit: 0.8,
    max_drawdown_limit: 0.15,
    stop_loss_pct: 0.02,
    take_profit_pct: 0.05,
    grid_strategy_enabled: true,
    ta_strategy_enabled: true,
    trend_strategy_enabled: true,
    min_confidence_threshold: 0.6,
    order_type: 'MARKET',
    slippage_tolerance: 0.001,
    execution_speed: 'FAST',
    model_refresh_frequency: 300,
    weight_confidence_threshold: 0.7,
    enable_dynamic_rebalancing: true,
    reporting_frequency: 60,
    telegram_alerts_enabled: true,
    symbols: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
    base_currency: 'USDT'
  });

  // Fetch status
  const fetchStatus = useCallback(async () => {
    try {
      const response = await apiClient.get('/api/trading/status');
      setStatus(response.data);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching status:', err);
      setError(err.response?.data?.detail || 'Failed to fetch status');
    }
  }, []);

  // Fetch sessions
  const fetchSessions = useCallback(async () => {
    setSessionsLoading(true);
    try {
      const response = await apiClient.get('/api/trading/sessions?limit=10');
      setSessions(response.data.sessions);
    } catch (err: any) {
      console.error('Error fetching sessions:', err);
    } finally {
      setSessionsLoading(false);
    }
  }, []);

  // Start trading
  const startTrading = async () => {
    setLoading(true);
    try {
      const response = await apiClient.post('/api/trading/start', config);
      await fetchStatus();
      await fetchSessions();
      showSnackbar('Auto trading started successfully!', 'success');
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || 'Failed to start trading';
      setError(errorMsg);
      showSnackbar(errorMsg, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Stop trading
  const stopTrading = async (emergency = false) => {
    setLoading(true);
    try {
      await apiClient.post('/api/trading/stop', { emergency });
      await fetchStatus();
      await fetchSessions();
      showSnackbar(`Trading stopped ${emergency ? '(EMERGENCY)' : 'successfully'}!`, emergency ? 'warning' : 'success');
      if (emergency) setEmergencyOpen(false);
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || 'Failed to stop trading';
      setError(errorMsg);
      showSnackbar(errorMsg, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Pause/Resume trading
  const pauseResume = async () => {
    if (!status) return;
    
    const isPaused = status.status === 'paused';
    const endpoint = isPaused ? '/api/trading/resume' : '/api/trading/pause';
    
    try {
      await apiClient.post(endpoint);
      await fetchStatus();
      showSnackbar(`Trading ${isPaused ? 'resumed' : 'paused'}!`, 'info');
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || `Failed to ${isPaused ? 'resume' : 'pause'} trading`;
      showSnackbar(errorMsg, 'error');
    }
  };

  // Show snackbar
  const showSnackbar = (message: string, severity: 'info' | 'success' | 'warning' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  // Format duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Format currency
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'success';
      case 'paused': return 'warning';
      case 'stopped': return 'default';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  // Effect for periodic updates and WebSocket setup
  useEffect(() => {
    fetchStatus();
    fetchSessions();
    
    // Set up WebSocket connection for real-time updates
    import('../services/websocket').then(({ websocketService, WebSocketEventType }) => {
      // Connect to WebSocket
      websocketService.connect();
      
      // Set up event handlers
      const handleSessionStarted = (data: any) => {
        console.log('Session started:', data);
        fetchStatus();
        fetchSessions();
        showSnackbar('Auto trading session started!', 'success');
      };
      
      const handleSessionStopped = (data: any) => {
        console.log('Session stopped:', data);
        fetchStatus();
        fetchSessions();
        showSnackbar('Auto trading session stopped!', 'info');
      };
      
      const handleSessionPaused = (data: any) => {
        console.log('Session paused:', data);
        fetchStatus();
        showSnackbar('Auto trading session paused!', 'warning');
      };
      
      const handleSessionResumed = (data: any) => {
        console.log('Session resumed:', data);
        fetchStatus();
        showSnackbar('Auto trading session resumed!', 'success');
      };
      
      const handlePerformanceUpdate = (data: any) => {
        console.log('Performance update:', data);
        // Update status with new performance data
        setStatus(prevStatus => {
          if (prevStatus) {
            return {
              ...prevStatus,
              performance: {
                ...prevStatus.performance,
                ...data
              }
            };
          }
          return prevStatus;
        });
      };
      
      const handleAlert = (data: any) => {
        console.log('Alert received:', data);
        const severity = data.level === 'CRITICAL' ? 'error' : 
                         data.level === 'ERROR' ? 'error' :
                         data.level === 'WARNING' ? 'warning' : 'info';
        showSnackbar(`${data.type}: ${data.message}`, severity);
      };
      
      const handleEmergencyStop = (data: any) => {
        console.log('Emergency stop:', data);
        fetchStatus();
        fetchSessions();
        showSnackbar('🚨 EMERGENCY STOP ACTIVATED!', 'error');
      };
      
      const handleConnectionEstablished = (data: any) => {
        console.log('WebSocket connected:', data);
        // Fetch initial status
        fetchStatus();
      };
      
      const handleInitialStatus = (data: any) => {
        console.log('Initial status received:', data);
        setStatus(data);
      };
      
      // Register event handlers
      websocketService.on(WebSocketEventType.AUTO_TRADING_SESSION_STARTED, handleSessionStarted);
      websocketService.on(WebSocketEventType.AUTO_TRADING_SESSION_STOPPED, handleSessionStopped);
      websocketService.on(WebSocketEventType.AUTO_TRADING_SESSION_PAUSED, handleSessionPaused);
      websocketService.on(WebSocketEventType.AUTO_TRADING_SESSION_RESUMED, handleSessionResumed);
      websocketService.on(WebSocketEventType.AUTO_TRADING_PERFORMANCE_UPDATE, handlePerformanceUpdate);
      websocketService.on(WebSocketEventType.AUTO_TRADING_ALERT, handleAlert);
      websocketService.on(WebSocketEventType.AUTO_TRADING_EMERGENCY_STOP, handleEmergencyStop);
      websocketService.on(WebSocketEventType.AUTO_TRADING_CONNECTION_ESTABLISHED, handleConnectionEstablished);
      websocketService.on(WebSocketEventType.AUTO_TRADING_INITIAL_STATUS, handleInitialStatus);
      
      // Cleanup function
      return () => {
        websocketService.off(WebSocketEventType.AUTO_TRADING_SESSION_STARTED, handleSessionStarted);
        websocketService.off(WebSocketEventType.AUTO_TRADING_SESSION_STOPPED, handleSessionStopped);
        websocketService.off(WebSocketEventType.AUTO_TRADING_SESSION_PAUSED, handleSessionPaused);
        websocketService.off(WebSocketEventType.AUTO_TRADING_SESSION_RESUMED, handleSessionResumed);
        websocketService.off(WebSocketEventType.AUTO_TRADING_PERFORMANCE_UPDATE, handlePerformanceUpdate);
        websocketService.off(WebSocketEventType.AUTO_TRADING_ALERT, handleAlert);
        websocketService.off(WebSocketEventType.AUTO_TRADING_EMERGENCY_STOP, handleEmergencyStop);
        websocketService.off(WebSocketEventType.AUTO_TRADING_CONNECTION_ESTABLISHED, handleConnectionEstablished);
        websocketService.off(WebSocketEventType.AUTO_TRADING_INITIAL_STATUS, handleInitialStatus);
        websocketService.disconnect();
      };
    });
    
    // Fallback polling for status updates (reduced frequency due to WebSocket)
    const interval = setInterval(() => {
      if (status?.session_active) {
        fetchStatus();
      }
    }, 15000); // Update every 15 seconds as fallback

    return () => {
      clearInterval(interval);
    };
  }, [fetchStatus, fetchSessions, status?.session_active]);

  return (
    <Box sx={{ p: 3 }}>
      {/* Main Control Panel */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AccountBalance />
              Auto Trading Controller
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton onClick={fetchStatus} disabled={loading}>
                <Refresh />
              </IconButton>
              <IconButton onClick={() => setConfigOpen(true)} disabled={status?.session_active}>
                <Settings />
              </IconButton>
            </Box>
          </Box>

          {/* Status Display */}
          {status && (
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Chip
                    label={status.status.toUpperCase()}
                    color={getStatusColor(status.status) as any}
                    icon={status.session_active ? <CheckCircle /> : <ErrorIcon />}
                    size="medium"
                  />
                  {status.session_active && (
                    <Typography variant="body1">
                      Duration: {status.duration_seconds ? formatDuration(status.duration_seconds) : 'N/A'}
                    </Typography>
                  )}
                </Box>
                
                {status.session_id && (
                  <Typography variant="body2" color="textSecondary">
                    Session ID: {status.session_id.slice(0, 8)}...
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
                {status.performance && (
                  <Box>
                    <Typography variant="h6" gutterBottom>Performance</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="textSecondary">Total PnL</Typography>
                        <Typography 
                          variant="h6" 
                          color={status.performance.total_pnl >= 0 ? 'success.main' : 'error.main'}
                        >
                          {formatCurrency(status.performance.total_pnl)}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="textSecondary">Win Rate</Typography>
                        <Typography variant="h6">
                          {formatPercentage(status.performance.win_rate)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </Grid>
            </Grid>
          )}

          {/* Main Control Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            {!status?.session_active ? (
              <Button
                variant="contained"
                size="medium"
                startIcon={<PlayArrow />}
                onClick={startTrading}
                disabled={loading}
                sx={{ 
                  minWidth: 200, 
                  minHeight: 60,
                  fontSize: '1.2rem',
                  background: 'linear-gradient(45deg, #4caf50 30%, #8bc34a 90%)'
                }}
              >
                {loading ? <CircularProgress size={24} /> : 'START TRADING'}
              </Button>
            ) : (
              <>
                <Button
                  variant="contained"
                  color={status.status === 'paused' ? 'primary' : 'warning'}
                  size="medium"
                  startIcon={status.status === 'paused' ? <PlayCircleFilled /> : <Pause />}
                  onClick={pauseResume}
                  disabled={loading}
                  sx={{ minWidth: 150, minHeight: 60 }}
                >
                  {status.status === 'paused' ? 'RESUME' : 'PAUSE'}
                </Button>
                
                <Button
                  variant="contained"
                  color="error"
                  size="medium"
                  startIcon={<Stop />}
                  onClick={() => stopTrading(false)}
                  disabled={loading}
                  sx={{ minWidth: 150, minHeight: 60 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'STOP'}
                </Button>

                <Button
                  variant="outlined"
                  color="error"
                  size="medium"
                  startIcon={<Emergency />}
                  onClick={() => setEmergencyOpen(true)}
                  sx={{ minWidth: 150, minHeight: 60 }}
                >
                  EMERGENCY
                </Button>
              </>
            )}
          </Box>

          {/* Error Display */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError(null)}>
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Real-time Metrics */}
      {status?.session_active && status.performance && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrendingUp />
              Real-time Performance Metrics
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color={status.performance.total_pnl >= 0 ? 'success.main' : 'error.main'}>
                    {formatCurrency(status.performance.total_pnl)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">Total PnL</Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4">{status.performance.total_trades}</Typography>
                  <Typography variant="body2" color="textSecondary">Total Trades</Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4">{formatPercentage(status.performance.win_rate)}</Typography>
                  <Typography variant="body2" color="textSecondary">Win Rate</Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color={status.performance.current_drawdown > 0.05 ? 'error.main' : 'success.main'}>
                    {formatPercentage(status.performance.current_drawdown)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">Current Drawdown</Typography>
                </Box>
              </Grid>
            </Grid>

            {/* Strategy Performance */}
            {status.performance.strategy_performance && Object.keys(status.performance.strategy_performance).length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Strategy Performance</Typography>
                <Grid container spacing={2}>
                  {Object.entries(status.performance.strategy_performance).map(([strategy, performance]) => (
                    <Grid item xs={12} md={4} key={strategy}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="h6" color={performance >= 0 ? 'success.main' : 'error.main'}>
                          {formatCurrency(performance as number)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {strategy.replace('Strategy', '')}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {/* Session History */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Timeline />
              Session History
            </Typography>
            <Button
              startIcon={<Refresh />}
              onClick={fetchSessions}
              disabled={sessionsLoading}
              size="small"
            >
              Refresh
            </Button>
          </Box>

          {sessionsLoading ? (
            <LinearProgress />
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Session ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Start Time</TableCell>
                    <TableCell>Duration</TableCell>
                    <TableCell align="right">PnL</TableCell>
                    <TableCell align="right">Trades</TableCell>
                    <TableCell align="right">Win Rate</TableCell>
                    <TableCell>Symbols</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow key={session.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {session.id.slice(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={session.status}
                          color={getStatusColor(session.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(session.start_time).toLocaleString()}
                      </TableCell>
                      <TableCell>{session.duration}</TableCell>
                      <TableCell 
                        align="right"
                        sx={{ color: session.total_pnl >= 0 ? 'success.main' : 'error.main' }}
                      >
                        {formatCurrency(session.total_pnl)}
                      </TableCell>
                      <TableCell align="right">{session.total_trades}</TableCell>
                      <TableCell align="right">{formatPercentage(session.win_rate)}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                          {session.symbols.map((symbol) => (
                            <Chip key={symbol} label={symbol} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                  {sessions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                        <Typography color="textSecondary">No trading sessions found</Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Configuration Dialog */}
      <Dialog
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Trading Configuration</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography>Risk Management</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <Typography gutterBottom>Max Position Size: {formatPercentage(config.max_position_size)}</Typography>
                    <Slider
                      value={config.max_position_size}
                      onChange={(_, value) => setConfig({...config, max_position_size: value as number})}
                      min={0.01}
                      max={0.5}
                      step={0.01}
                      marks={[
                        { value: 0.01, label: '1%' },
                        { value: 0.1, label: '10%' },
                        { value: 0.25, label: '25%' },
                        { value: 0.5, label: '50%' }
                      ]}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography gutterBottom>Max Drawdown: {formatPercentage(config.max_drawdown_limit)}</Typography>
                    <Slider
                      value={config.max_drawdown_limit}
                      onChange={(_, value) => setConfig({...config, max_drawdown_limit: value as number})}
                      min={0.05}
                      max={0.5}
                      step={0.01}
                      marks={[
                        { value: 0.05, label: '5%' },
                        { value: 0.15, label: '15%' },
                        { value: 0.3, label: '30%' },
                        { value: 0.5, label: '50%' }
                      ]}
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography>Strategy Settings</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={config.grid_strategy_enabled}
                          onChange={(e) => setConfig({...config, grid_strategy_enabled: e.target.checked})}
                        />
                      }
                      label="Grid Strategy"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={config.ta_strategy_enabled}
                          onChange={(e) => setConfig({...config, ta_strategy_enabled: e.target.checked})}
                        />
                      }
                      label="Technical Analysis"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={config.trend_strategy_enabled}
                          onChange={(e) => setConfig({...config, trend_strategy_enabled: e.target.checked})}
                        />
                      }
                      label="Trend Following"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography>Execution Settings</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Order Type</InputLabel>
                      <Select
                        value={config.order_type}
                        onChange={(e) => setConfig({...config, order_type: e.target.value as 'MARKET' | 'LIMIT'})}
                      >
                        <MenuItem value="MARKET">Market</MenuItem>
                        <MenuItem value="LIMIT">Limit</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Execution Speed</InputLabel>
                      <Select
                        value={config.execution_speed}
                        onChange={(e) => setConfig({...config, execution_speed: e.target.value as 'FAST' | 'NORMAL' | 'CAREFUL'})}
                      >
                        <MenuItem value="FAST">Fast</MenuItem>
                        <MenuItem value="NORMAL">Normal</MenuItem>
                        <MenuItem value="CAREFUL">Careful</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography>Trading Symbols</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <TextField
                  fullWidth
                  label="Symbols (comma-separated)"
                  value={config.symbols.join(', ')}
                  onChange={(e) => setConfig({
                    ...config, 
                    symbols: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                  })}
                  helperText="Example: BTCUSDT, ETHUSDT, ADAUSDT"
                />
              </AccordionDetails>
            </Accordion>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfigOpen(false)}>Cancel</Button>
          <Button onClick={() => setConfigOpen(false)} variant="contained">Save Configuration</Button>
        </DialogActions>
      </Dialog>

      {/* Emergency Stop Dialog */}
      <Dialog
        open={emergencyOpen}
        onClose={() => setEmergencyOpen(false)}
      >
        <DialogTitle sx={{ color: 'error.main' }}>
          <Warning sx={{ mr: 1 }} />
          Emergency Stop Confirmation
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Emergency stop will immediately halt all trading operations and close any open positions.
            This action cannot be undone.
          </Alert>
          <Typography>
            Are you sure you want to perform an emergency stop?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyOpen(false)}>Cancel</Button>
          <Button 
            onClick={() => stopTrading(true)} 
            color="error" 
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'EMERGENCY STOP'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AutoTradingController;