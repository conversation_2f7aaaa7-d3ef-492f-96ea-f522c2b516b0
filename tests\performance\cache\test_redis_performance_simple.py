#!/usr/bin/env python3
"""
Simplified Redis Performance Test
Validates <100ms cache operations without import dependencies.
"""

import asyncio
import time
import statistics
import json
from datetime import datetime
from typing import Dict, Any

async def test_redis_mcp_performance():
    """Test Redis MCP performance with direct operations."""
    print("🚀 Redis MCP Performance Test")
    print("=" * 50)
    
    try:
        # Test Redis MCP directly
        from app.services.mcp import mcp__redis__set, mcp__redis__get, mcp__redis__list
        
        # Test data
        test_signals = [
            ("signal:GridStrategy:hash1", json.dumps({
                "action": "BUY", "confidence": 0.85, "quantity": 0.05, "timestamp": datetime.now().isoformat()
            })),
            ("signal:TechnicalAnalysis:hash2", json.dumps({
                "action": "SELL", "confidence": 0.75, "quantity": 0.03, "timestamp": datetime.now().isoformat()
            })),
            ("signal:TrendFollowing:hash3", json.dumps({
                "action": "HOLD", "confidence": 0.65, "quantity": 0.0, "timestamp": datetime.now().isoformat()
            }))
        ]
        
        # Test weights
        test_weights = json.dumps({
            "GridStrategy": 0.4,
            "TechnicalAnalysisStrategy": 0.35, 
            "TrendFollowingStrategy": 0.25,
            "timestamp": datetime.now().isoformat(),
            "confidence": 0.85
        })
        
        print("📊 Testing Individual Cache Operations...")
        
        # Test signal caching performance
        cache_times = []
        for key, value in test_signals:
            start_time = time.perf_counter()
            await mcp__redis__set(key=key, value=value, expireSeconds=30)
            cache_time = (time.perf_counter() - start_time) * 1000
            cache_times.append(cache_time)
            print(f"   📝 Cached {key}: {cache_time:.2f}ms")
        
        # Test signal retrieval performance  
        retrieval_times = []
        for key, expected_value in test_signals:
            start_time = time.perf_counter()
            result = await mcp__redis__get(key=key)
            retrieval_time = (time.perf_counter() - start_time) * 1000
            retrieval_times.append(retrieval_time)
            print(f"   📖 Retrieved {key}: {retrieval_time:.2f}ms")
        
        # Test weight caching
        start_time = time.perf_counter()
        await mcp__redis__set(key="ensemble:weights", value=test_weights, expireSeconds=300)
        weight_cache_time = (time.perf_counter() - start_time) * 1000
        
        start_time = time.perf_counter()
        cached_weights = await mcp__redis__get(key="ensemble:weights")
        weight_retrieval_time = (time.perf_counter() - start_time) * 1000
        
        print(f"   ⚖️ Weight cache: {weight_cache_time:.2f}ms")
        print(f"   ⚖️ Weight retrieval: {weight_retrieval_time:.2f}ms")
        
        # Performance analysis
        avg_cache_time = statistics.mean(cache_times)
        max_cache_time = max(cache_times)
        avg_retrieval_time = statistics.mean(retrieval_times)
        max_retrieval_time = max(retrieval_times)
        
        print(f"\n📈 Performance Summary:")
        print(f"   Average cache time: {avg_cache_time:.2f}ms")
        print(f"   Max cache time: {max_cache_time:.2f}ms")
        print(f"   Average retrieval time: {avg_retrieval_time:.2f}ms")
        print(f"   Max retrieval time: {max_retrieval_time:.2f}ms")
        print(f"   Weight cache time: {weight_cache_time:.2f}ms")
        print(f"   Weight retrieval time: {weight_retrieval_time:.2f}ms")
        
        # Validate 100ms requirement
        all_operations = cache_times + retrieval_times + [weight_cache_time, weight_retrieval_time]
        max_operation_time = max(all_operations)
        
        performance_passed = max_operation_time < 100
        
        print(f"\n🎯 100ms Requirement Check:")
        print(f"   Max operation time: {max_operation_time:.2f}ms")
        
        if performance_passed:
            print("   ✅ PASSED: All operations under 100ms")
        else:
            print("   ❌ FAILED: Some operations exceed 100ms")
        
        return performance_passed, {
            'avg_cache_time': avg_cache_time,
            'max_cache_time': max_cache_time,
            'avg_retrieval_time': avg_retrieval_time,
            'max_retrieval_time': max_retrieval_time,
            'weight_cache_time': weight_cache_time,
            'weight_retrieval_time': weight_retrieval_time,
            'max_operation_time': max_operation_time
        }
        
    except Exception as e:
        print(f"❌ Redis MCP test failed: {e}")
        return False, {}

async def test_concurrent_operations():
    """Test Redis performance under concurrent load."""
    print("\n🔄 Testing Concurrent Operations...")
    
    try:
        from app.services.mcp import mcp__redis__set, mcp__redis__get
        
        async def cache_operation(task_id: int, operation_id: int):
            """Single cache operation for load testing."""
            key = f"load_test:{task_id}:{operation_id}"
            value = json.dumps({
                "task_id": task_id,
                "operation_id": operation_id,
                "timestamp": datetime.now().isoformat(),
                "data": f"test_data_{task_id}_{operation_id}"
            })
            
            # Cache operation
            start_time = time.perf_counter()
            await mcp__redis__set(key=key, value=value, expireSeconds=30)
            cache_time = (time.perf_counter() - start_time) * 1000
            
            # Retrieval operation
            start_time = time.perf_counter()
            await mcp__redis__get(key=key)
            retrieval_time = (time.perf_counter() - start_time) * 1000
            
            return cache_time + retrieval_time
        
        # Create concurrent operations
        num_concurrent = 10
        operations_per_task = 3
        
        start_time = time.perf_counter()
        
        tasks = []
        for task_id in range(num_concurrent):
            for op_id in range(operations_per_task):
                tasks.append(cache_operation(task_id, op_id))
        
        operation_times = await asyncio.gather(*tasks)
        
        total_time = (time.perf_counter() - start_time) * 1000
        
        # Analyze results
        avg_operation_time = statistics.mean(operation_times)
        max_operation_time = max(operation_times)
        p95_time = statistics.quantiles(operation_times, n=20)[18] if len(operation_times) > 20 else max_operation_time
        
        total_operations = len(operation_times)
        throughput = total_operations / (total_time / 1000)
        
        print(f"   Concurrent operations: {total_operations}")
        print(f"   Total time: {total_time:.2f}ms")
        print(f"   Average operation: {avg_operation_time:.2f}ms")
        print(f"   Max operation: {max_operation_time:.2f}ms")
        print(f"   95th percentile: {p95_time:.2f}ms")
        print(f"   Throughput: {throughput:.1f} ops/sec")
        
        # Performance validation
        concurrent_performance_ok = (
            max_operation_time < 150 and  # Allow higher limit under concurrent load
            p95_time < 100 and
            throughput > 50
        )
        
        if concurrent_performance_ok:
            print("   ✅ Concurrent performance: PASSED")
        else:
            print("   ❌ Concurrent performance: FAILED")
        
        return concurrent_performance_ok, {
            'avg_operation_time': avg_operation_time,
            'max_operation_time': max_operation_time,
            'p95_time': p95_time,
            'throughput': throughput,
            'total_operations': total_operations
        }
        
    except Exception as e:
        print(f"❌ Concurrent operations test failed: {e}")
        return False, {}

async def main():
    """Run simplified Redis performance validation."""
    print("🎯 Task 1.2.1: Redis Signal Caching Performance Validation")
    print("📋 Requirement: All cache operations complete in <100ms")
    print("=" * 60)
    
    # Test individual operations
    individual_passed, individual_data = await test_redis_mcp_performance()
    
    # Test concurrent operations
    concurrent_passed, concurrent_data = await test_concurrent_operations()
    
    # Final validation
    print("\n" + "=" * 60)
    print("🏆 FINAL VALIDATION RESULTS")
    print("=" * 60)
    
    all_tests_passed = individual_passed and concurrent_passed
    
    if all_tests_passed:
        print("✅ Task 1.2.1: SUCCESSFULLY COMPLETED")
        print("🎉 Redis signal caching meets performance requirements")
        print("⚡ All cache operations complete in <100ms")
        
        if individual_data:
            print(f"📊 Key metrics:")
            print(f"   - Max individual operation: {individual_data.get('max_operation_time', 0):.2f}ms")
            
        if concurrent_data:
            print(f"   - Throughput under load: {concurrent_data.get('throughput', 0):.1f} ops/sec")
            print(f"   - 95th percentile under load: {concurrent_data.get('p95_time', 0):.2f}ms")
        
    else:
        print("❌ Task 1.2.1: REQUIREMENTS NOT MET")
        if not individual_passed:
            print("   - Individual operations exceed 100ms")
        if not concurrent_passed:
            print("   - Concurrent performance insufficient")
    
    print(f"\n🔄 Next: Task 1.2.3 - Create Portfolio Manager with automation")
    
    return all_tests_passed

if __name__ == "__main__":
    asyncio.run(main())