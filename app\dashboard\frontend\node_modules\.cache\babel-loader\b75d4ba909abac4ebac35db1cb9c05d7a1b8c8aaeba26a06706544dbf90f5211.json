{"ast": null, "code": "var _jsxFileName = \"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Typography, Box, Paper, Grid, CircularProgress, Alert, Snackbar, Badge, Tabs, Tab } from '@mui/material';\nimport { apiClient } from '../services/api';\nimport websocketService, { WebSocketEventType } from '../services/websocket';\nimport analyticsWebsocketService, { AnalyticsEventType } from '../services/analyticsWebsocket';\nimport AccountStatistics from './account/AccountStatistics';\nimport TradesTable from './common/TradesTable';\n\n// TabPanel helper component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `simple-tabpanel-${index}`,\n    \"aria-labelledby\": `simple-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        pt: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nfunction a11yProps(index) {\n  return {\n    id: `simple-tab-${index}`,\n    'aria-controls': `simple-tabpanel-${index}`\n  };\n}\nconst TradeDashboard = () => {\n  _s();\n  const [activeTrades, setActiveTrades] = useState([]);\n  const [recentTrades, setRecentTrades] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [wsStatus, setWsStatus] = useState('disconnected');\n  const [currentTab, setCurrentTab] = useState(0);\n  const [analyticsWsStatus, setAnalyticsWsStatus] = useState('disconnected');\n  const [marketState, setMarketState] = useState(null);\n  const [strategyWeights, setStrategyWeights] = useState(null);\n  const [performanceMetrics, setPerformanceMetrics] = useState(null);\n  const handleTabChange = (event, newValue) => {\n    setCurrentTab(newValue);\n  };\n\n  // Generate mock trades for demo purposes\n  const generateMockActiveTrades = () => {\n    return [{\n      trade_id: 'trade_001',\n      symbol: 'BTCUSDT',\n      entry_fill_price: 42500.50,\n      entry_fill_qty: 0.025,\n      entry_side: 'BUY',\n      sl_price: 41000.00,\n      tp_price: 45000.00,\n      status: 'SLTP_PLACED',\n      created_at: new Date(Date.now() - 3600000).toISOString(),\n      updated_at: new Date(Date.now() - 1800000).toISOString()\n    }, {\n      trade_id: 'trade_002',\n      symbol: 'ETHUSDT',\n      entry_fill_price: 2650.25,\n      entry_fill_qty: 0.5,\n      entry_side: 'SELL',\n      sl_price: 2750.00,\n      tp_price: 2550.00,\n      status: 'ENTRY_FILLED',\n      created_at: new Date(Date.now() - 7200000).toISOString(),\n      updated_at: new Date(Date.now() - 3600000).toISOString()\n    }];\n  };\n  const generateMockRecentTrades = () => {\n    return [{\n      trade_id: 'trade_003',\n      symbol: 'ADAUSDT',\n      entry_fill_price: 0.485,\n      entry_fill_qty: 2000,\n      entry_side: 'BUY',\n      sl_price: null,\n      tp_price: 0.52,\n      status: 'CLOSED_TP',\n      created_at: new Date(Date.now() - 86400000).toISOString(),\n      updated_at: new Date(Date.now() - 82800000).toISOString()\n    }, {\n      trade_id: 'trade_004',\n      symbol: 'SOLUSDT',\n      entry_fill_price: 95.75,\n      entry_fill_qty: 10,\n      entry_side: 'BUY',\n      sl_price: 90.00,\n      tp_price: null,\n      status: 'CLOSED_SL',\n      created_at: new Date(Date.now() - 172800000).toISOString(),\n      updated_at: new Date(Date.now() - 169200000).toISOString()\n    }];\n  };\n\n  // Fetch active trades\n  const fetchActiveTrades = useCallback(async () => {\n    try {\n      const response = await apiClient.get('/api/trading/active-trades');\n      setActiveTrades(response.data);\n    } catch (err) {\n      console.error('Error fetching active trades:', err);\n      // Use mock data instead of showing error\n      setActiveTrades(generateMockActiveTrades());\n    }\n  }, []);\n\n  // Fetch recent trades\n  const fetchRecentTrades = useCallback(async () => {\n    try {\n      const response = await apiClient.get('/api/trading/recent-trades');\n      setRecentTrades(response.data);\n    } catch (err) {\n      console.error('Error fetching recent trades:', err);\n      // Use mock data instead of showing error\n      setRecentTrades(generateMockRecentTrades());\n    }\n  }, []);\n\n  // Fetch all trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setLoading(true);\n        await Promise.all([fetchActiveTrades(), fetchRecentTrades()]);\n      } catch (err) {\n        console.error('Error in fetchTrades:', err);\n        setError('Failed to fetch trades. Using demo data.');\n        // Fallback to mock data\n        setActiveTrades(generateMockActiveTrades());\n        setRecentTrades(generateMockRecentTrades());\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTrades();\n\n    // WebSocket event handlers\n    const handleTradeUpdate = data => {\n      const updatedTrade = {\n        trade_id: data.trade_id,\n        symbol: data.symbol,\n        entry_side: data.entry_side,\n        entry_fill_price: data.entry_price || 0,\n        entry_fill_qty: data.entry_qty || 0,\n        sl_price: data.sl_price,\n        tp_price: data.tp_price,\n        exit_price: data.exit_price || undefined,\n        current_price: data.current_price,\n        status: data.status,\n        created_at: data.timestamp,\n        updated_at: data.timestamp\n      };\n      setActiveTrades(prev => prev.map(t => t.trade_id === updatedTrade.trade_id ? updatedTrade : t));\n      setNotification({\n        open: true,\n        message: `Trade ${updatedTrade.trade_id} updated: ${updatedTrade.status}`,\n        severity: 'info'\n      });\n    };\n    const handleSystemStatus = data => {\n      setNotification({\n        open: true,\n        message: `System: ${data.message}`,\n        severity: data.status === 'offline' ? 'error' : 'info'\n      });\n    };\n\n    // Setup WebSocket connections with error handling\n    try {\n      // Main WebSocket for trades\n      websocketService.on(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);\n      websocketService.on(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);\n      websocketService.connect();\n      setWsStatus('connecting');\n    } catch (error) {\n      console.error('Failed to setup main WebSocket:', error);\n      setWsStatus('disconnected');\n    }\n\n    // Analytics WebSocket event handlers\n    const handleMarketState = data => setMarketState(data);\n    const handleStrategyWeights = data => setStrategyWeights(data);\n    const handlePerformanceMetrics = data => setPerformanceMetrics(data);\n\n    // Setup analytics WebSocket with error handling\n    try {\n      analyticsWebsocketService.connect();\n      setAnalyticsWsStatus('connecting');\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);\n    } catch (error) {\n      console.error('Failed to setup analytics WebSocket:', error);\n      setAnalyticsWsStatus('disconnected');\n    }\n    return () => {\n      // Cleanup with error handling\n      try {\n        // Trades WS cleanup\n        websocketService.off(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);\n        websocketService.off(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);\n        websocketService.disconnect();\n      } catch (error) {\n        console.error('Error cleaning up main WebSocket:', error);\n      }\n      try {\n        // Analytics WS cleanup\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);\n        analyticsWebsocketService.disconnect();\n      } catch (error) {\n        console.error('Error cleaning up analytics WebSocket:', error);\n      }\n    };\n  }, [fetchActiveTrades, fetchRecentTrades]);\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      mt: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: [\"Trading Dashboard\", /*#__PURE__*/_jsxDEV(Badge, {\n        badgeContent: wsStatus,\n        color: wsStatus === 'connected' ? 'success' : wsStatus === 'connecting' ? 'warning' : 'error',\n        sx: {\n          ml: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n        badgeContent: `Analytics: ${analyticsWsStatus}`,\n        color: analyticsWsStatus === 'connected' ? 'success' : 'error',\n        sx: {\n          ml: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 10\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider'\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: currentTab,\n        onChange: handleTabChange,\n        \"aria-label\": \"dashboard tabs\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Trades\",\n          ...a11yProps(0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Strategy Monitor\",\n          ...a11yProps(1)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(AccountStatistics, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sx: {\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), !loading && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TradesTable, {\n              trades: activeTrades,\n              title: \"Active Trades\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TradesTable, {\n              trades: recentTrades,\n              title: \"Recent Trades\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Market Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), marketState ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Regime: \", marketState.regime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Volatility: \", marketState.volatility.toFixed(4)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Trend Strength: \", marketState.trend_strength.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Waiting for market data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Strategy Weights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), strategyWeights ? /*#__PURE__*/_jsxDEV(\"pre\", {\n              children: JSON.stringify(strategyWeights.weights, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Waiting for strategy data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Performance Metrics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), performanceMetrics ? /*#__PURE__*/_jsxDEV(\"pre\", {\n              children: JSON.stringify(performanceMetrics.metrics, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Waiting for performance data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n_s(TradeDashboard, \"69hkR2GP2L8BNEgHFpBk1K+4wf4=\");\n_c2 = TradeDashboard;\nexport default TradeDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"TradeDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Typography", "Box", "Paper", "Grid", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "Badge", "Tabs", "Tab", "apiClient", "websocketService", "WebSocketEventType", "analyticsWebsocketService", "AnalyticsEventType", "AccountStatistics", "TradesTable", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "pt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "a11yProps", "TradeDashboard", "_s", "activeTrades", "setActiveTrades", "recentTrades", "setRecentTrades", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "wsStatus", "setWsStatus", "currentTab", "setCurrentTab", "analyticsWsStatus", "setAnalyticsWsStatus", "marketState", "setMarketState", "strategyWeights", "setStrategyWeights", "performanceMetrics", "setPerformanceMetrics", "handleTabChange", "event", "newValue", "generateMockActiveTrades", "trade_id", "symbol", "entry_fill_price", "entry_fill_qty", "entry_side", "sl_price", "tp_price", "status", "created_at", "Date", "now", "toISOString", "updated_at", "generateMockRecentTrades", "fetchActiveTrades", "response", "get", "data", "err", "console", "fetchRecentTrades", "fetchTrades", "Promise", "all", "handleTradeUpdate", "updatedTrade", "entry_price", "entry_qty", "exit_price", "undefined", "current_price", "timestamp", "prev", "map", "t", "handleSystemStatus", "on", "TRADE_UPDATE", "SYSTEM_STATUS", "connect", "handleMarketState", "handleStrategyWeights", "handlePerformanceMetrics", "addEventListener", "MARKET_STATE", "STRATEGY_WEIGHTS", "PERFORMANCE_METRICS", "off", "disconnect", "removeEventListener", "handleCloseNotification", "max<PERSON><PERSON><PERSON>", "mt", "variant", "gutterBottom", "badgeContent", "color", "ml", "mb", "borderBottom", "borderColor", "onChange", "label", "container", "spacing", "item", "xs", "display", "justifyContent", "trades", "title", "md", "p", "height", "regime", "volatility", "toFixed", "trend_strength", "JSON", "stringify", "weights", "metrics", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c2", "$RefreshReg$"], "sources": ["/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  Grid,\n  CircularProgress,\n  Alert,\n  Snackbar,\n  Badge,\n  Tabs,\n  Tab,\n} from '@mui/material';\nimport { apiClient } from '../services/api';\nimport websocketService, { WebSocketEventType, TradeUpdateEvent, SystemStatusEvent } from '../services/websocket';\nimport analyticsWebsocketService, { AnalyticsEventType, MarketStateEvent, StrategyWeightsEvent, PerformanceMetricsEvent } from '../services/analyticsWebsocket';\nimport AccountStatistics from './account/AccountStatistics';\nimport TradesTable from './common/TradesTable';\nimport { Trade } from '../types';\n\n// TabPanel helper component\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`simple-tabpanel-${index}`}\n      aria-labelledby={`simple-tab-${index}`}\n      {...other}\n    >\n      {value === index && (\n        <Box sx={{ pt: 3 }}>\n          {children}\n        </Box>\n      )}\n    </div>\n  );\n}\n\nfunction a11yProps(index: number) {\n  return {\n    id: `simple-tab-${index}`,\n    'aria-controls': `simple-tabpanel-${index}`,\n  };\n}\n\nconst TradeDashboard: React.FC = () => {\n  const [activeTrades, setActiveTrades] = useState<Trade[]>([]);\n  const [recentTrades, setRecentTrades] = useState<Trade[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [notification, setNotification] = useState<{\n    open: boolean;\n    message: string;\n    severity: 'success' | 'error' | 'info' | 'warning';\n  }>({\n    open: false,\n    message: '',\n    severity: 'info',\n  });\n  const [wsStatus, setWsStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');\n  const [currentTab, setCurrentTab] = useState(0);\n  const [analyticsWsStatus, setAnalyticsWsStatus] = useState<string>('disconnected');\n  const [marketState, setMarketState] = useState<MarketStateEvent | null>(null);\n  const [strategyWeights, setStrategyWeights] = useState<StrategyWeightsEvent | null>(null);\n  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetricsEvent | null>(null);\n\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setCurrentTab(newValue);\n  };\n\n  // Generate mock trades for demo purposes\n  const generateMockActiveTrades = (): Trade[] => {\n    return [\n      {\n        trade_id: 'trade_001',\n        symbol: 'BTCUSDT',\n        entry_fill_price: 42500.50,\n        entry_fill_qty: 0.025,\n        entry_side: 'BUY',\n        sl_price: 41000.00,\n        tp_price: 45000.00,\n        status: 'SLTP_PLACED',\n        created_at: new Date(Date.now() - 3600000).toISOString(),\n        updated_at: new Date(Date.now() - 1800000).toISOString(),\n      },\n      {\n        trade_id: 'trade_002',\n        symbol: 'ETHUSDT',\n        entry_fill_price: 2650.25,\n        entry_fill_qty: 0.5,\n        entry_side: 'SELL',\n        sl_price: 2750.00,\n        tp_price: 2550.00,\n        status: 'ENTRY_FILLED',\n        created_at: new Date(Date.now() - 7200000).toISOString(),\n        updated_at: new Date(Date.now() - 3600000).toISOString(),\n      },\n    ];\n  };\n\n  const generateMockRecentTrades = (): Trade[] => {\n    return [\n      {\n        trade_id: 'trade_003',\n        symbol: 'ADAUSDT',\n        entry_fill_price: 0.485,\n        entry_fill_qty: 2000,\n        entry_side: 'BUY',\n        sl_price: null,\n        tp_price: 0.52,\n        status: 'CLOSED_TP',\n        created_at: new Date(Date.now() - 86400000).toISOString(),\n        updated_at: new Date(Date.now() - 82800000).toISOString(),\n      },\n      {\n        trade_id: 'trade_004',\n        symbol: 'SOLUSDT',\n        entry_fill_price: 95.75,\n        entry_fill_qty: 10,\n        entry_side: 'BUY',\n        sl_price: 90.00,\n        tp_price: null,\n        status: 'CLOSED_SL',\n        created_at: new Date(Date.now() - 172800000).toISOString(),\n        updated_at: new Date(Date.now() - 169200000).toISOString(),\n      },\n    ];\n  };\n\n  // Fetch active trades\n  const fetchActiveTrades = useCallback(async () => {\n    try {\n      const response = await apiClient.get('/api/trading/active-trades');\n      setActiveTrades(response.data);\n    } catch (err: any) {\n      console.error('Error fetching active trades:', err);\n      // Use mock data instead of showing error\n      setActiveTrades(generateMockActiveTrades());\n    }\n  }, []);\n\n  // Fetch recent trades\n  const fetchRecentTrades = useCallback(async () => {\n    try {\n      const response = await apiClient.get('/api/trading/recent-trades');\n      setRecentTrades(response.data);\n    } catch (err: any) {\n      console.error('Error fetching recent trades:', err);\n      // Use mock data instead of showing error\n      setRecentTrades(generateMockRecentTrades());\n    }\n  }, []);\n\n  // Fetch all trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setLoading(true);\n        await Promise.all([\n          fetchActiveTrades(),\n          fetchRecentTrades(),\n        ]);\n      } catch (err: any) {\n        console.error('Error in fetchTrades:', err);\n        setError('Failed to fetch trades. Using demo data.');\n        // Fallback to mock data\n        setActiveTrades(generateMockActiveTrades());\n        setRecentTrades(generateMockRecentTrades());\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTrades();\n\n    // WebSocket event handlers\n    const handleTradeUpdate = (data: TradeUpdateEvent) => {\n      const updatedTrade: Trade = {\n        trade_id: data.trade_id,\n        symbol: data.symbol,\n        entry_side: data.entry_side,\n        entry_fill_price: data.entry_price || 0,\n        entry_fill_qty: data.entry_qty || 0,\n        sl_price: data.sl_price,\n        tp_price: data.tp_price,\n        exit_price: data.exit_price || undefined,\n        current_price: data.current_price,\n        status: data.status,\n        created_at: data.timestamp,\n        updated_at: data.timestamp,\n      };\n      setActiveTrades(prev =>\n        prev.map(t => (t.trade_id === updatedTrade.trade_id ? updatedTrade : t))\n      );\n      setNotification({\n        open: true,\n        message: `Trade ${updatedTrade.trade_id} updated: ${updatedTrade.status}`,\n        severity: 'info',\n      });\n    };\n\n    const handleSystemStatus = (data: SystemStatusEvent) => {\n      setNotification({\n        open: true,\n        message: `System: ${data.message}`,\n        severity: data.status === 'offline' ? 'error' : 'info',\n      });\n    };\n\n\n    // Setup WebSocket connections with error handling\n    try {\n      // Main WebSocket for trades\n      websocketService.on(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);\n      websocketService.on(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);\n      websocketService.connect();\n      setWsStatus('connecting');\n    } catch (error) {\n      console.error('Failed to setup main WebSocket:', error);\n      setWsStatus('disconnected');\n    }\n\n    // Analytics WebSocket event handlers\n    const handleMarketState = (data: MarketStateEvent) => setMarketState(data);\n    const handleStrategyWeights = (data: StrategyWeightsEvent) => setStrategyWeights(data);\n    const handlePerformanceMetrics = (data: PerformanceMetricsEvent) => setPerformanceMetrics(data);\n\n    // Setup analytics WebSocket with error handling\n    try {\n      analyticsWebsocketService.connect();\n      setAnalyticsWsStatus('connecting');\n      \n      analyticsWebsocketService.addEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);\n    } catch (error) {\n      console.error('Failed to setup analytics WebSocket:', error);\n      setAnalyticsWsStatus('disconnected');\n    }\n\n    return () => {\n      // Cleanup with error handling\n      try {\n        // Trades WS cleanup\n        websocketService.off(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);\n        websocketService.off(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);\n        websocketService.disconnect();\n      } catch (error) {\n        console.error('Error cleaning up main WebSocket:', error);\n      }\n\n      try {\n        // Analytics WS cleanup\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);\n        analyticsWebsocketService.disconnect();\n      } catch (error) {\n        console.error('Error cleaning up analytics WebSocket:', error);\n      }\n    };\n  }, [fetchActiveTrades, fetchRecentTrades]);\n\n  const handleCloseNotification = () => {\n    setNotification({ ...notification, open: false });\n  };\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ mt: 4 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Trading Dashboard\n        <Badge\n          badgeContent={wsStatus}\n          color={wsStatus === 'connected' ? 'success' : wsStatus === 'connecting' ? 'warning' : 'error'}\n          sx={{ ml: 2 }}\n        />\n         <Badge\n          badgeContent={`Analytics: ${analyticsWsStatus}`}\n          color={analyticsWsStatus === 'connected' ? 'success' : 'error'}\n          sx={{ ml: 2 }}\n        />\n      </Typography>\n\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n\n      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n        <Tabs value={currentTab} onChange={handleTabChange} aria-label=\"dashboard tabs\">\n          <Tab label=\"Trades\" {...a11yProps(0)} />\n          <Tab label=\"Strategy Monitor\" {...a11yProps(1)} />\n        </Tabs>\n      </Box>\n\n      <TabPanel value={currentTab} index={0}>\n        <Grid container spacing={4}>\n          <Grid item xs={12}>\n            <AccountStatistics />\n          </Grid>\n          {loading && (\n            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center' }}>\n              <CircularProgress />\n            </Grid>\n          )}\n          {!loading && (\n            <>\n              <Grid item xs={12}>\n                <TradesTable trades={activeTrades} title=\"Active Trades\" />\n              </Grid>\n              <Grid item xs={12}>\n                <TradesTable trades={recentTrades} title=\"Recent Trades\" />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </TabPanel>\n\n      <TabPanel value={currentTab} index={1}>\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"h6\">Market Conditions</Typography>\n              {marketState ? (\n                <>\n                  <Typography>Regime: {marketState.regime}</Typography>\n                  <Typography>Volatility: {marketState.volatility.toFixed(4)}</Typography>\n                  <Typography>Trend Strength: {marketState.trend_strength.toFixed(2)}</Typography>\n                </>\n              ) : (\n                <Typography>Waiting for market data...</Typography>\n              )}\n            </Paper>\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"h6\">Strategy Weights</Typography>\n              {strategyWeights ? (\n                <pre>{JSON.stringify(strategyWeights.weights, null, 2)}</pre>\n              ) : (\n                <Typography>Waiting for strategy data...</Typography>\n              )}\n            </Paper>\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"h6\">Performance Metrics</Typography>\n              {performanceMetrics ? (\n                <pre>{JSON.stringify(performanceMetrics.metrics, null, 2)}</pre>\n              ) : (\n                <Typography>Waiting for performance data...</Typography>\n              )}\n            </Paper>\n          </Grid>\n        </Grid>\n      </TabPanel>\n\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Container>\n  );\n};\n\nexport default TradeDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,gBAAgB,IAAIC,kBAAkB,QAA6C,uBAAuB;AACjH,OAAOC,yBAAyB,IAAIC,kBAAkB,QAAyE,gCAAgC;AAC/J,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,OAAOC,WAAW,MAAM,sBAAsB;;AAG9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOA,SAASC,QAAQA,CAACC,KAAoB,EAAE;EACtC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAElD,oBACEJ,OAAA;IACES,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,mBAAmBJ,KAAK,EAAG;IAC/B,mBAAiB,cAAcA,KAAK,EAAG;IAAA,GACnCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBACdP,OAAA,CAACjB,GAAG;MAAC6B,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAChBA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACC,EAAA,GAlBQf,QAAQ;AAoBjB,SAASgB,SAASA,CAACZ,KAAa,EAAE;EAChC,OAAO;IACLI,EAAE,EAAE,cAAcJ,KAAK,EAAE;IACzB,eAAe,EAAE,mBAAmBA,KAAK;EAC3C,CAAC;AACH;AAEA,MAAMa,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAU,EAAE,CAAC;EAC7D,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAU,EAAE,CAAC;EAC7D,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAI7C;IACDsD,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAA8C,cAAc,CAAC;EACrG,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC6D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAAS,cAAc,CAAC;EAClF,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAA0B,IAAI,CAAC;EAC7E,MAAM,CAACiE,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAA8B,IAAI,CAAC;EACzF,MAAM,CAACmE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpE,QAAQ,CAAiC,IAAI,CAAC;EAElG,MAAMqE,eAAe,GAAGA,CAACC,KAA2B,EAAEC,QAAgB,KAAK;IACzEX,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAe;IAC9C,OAAO,CACL;MACEC,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,SAAS;MACjBC,gBAAgB,EAAE,QAAQ;MAC1BC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;MACxDC,UAAU,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,WAAW,CAAC;IACzD,CAAC,EACD;MACEX,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,SAAS;MACjBC,gBAAgB,EAAE,OAAO;MACzBC,cAAc,EAAE,GAAG;MACnBC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,cAAc;MACtBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;MACxDC,UAAU,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,WAAW,CAAC;IACzD,CAAC,CACF;EACH,CAAC;EAED,MAAME,wBAAwB,GAAGA,CAAA,KAAe;IAC9C,OAAO,CACL;MACEb,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,SAAS;MACjBC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACC,WAAW,CAAC,CAAC;MACzDC,UAAU,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACC,WAAW,CAAC;IAC1D,CAAC,EACD;MACEX,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,SAAS;MACjBC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1DC,UAAU,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACC,WAAW,CAAC;IAC3D,CAAC,CACF;EACH,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGrF,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMsF,QAAQ,GAAG,MAAM1E,SAAS,CAAC2E,GAAG,CAAC,4BAA4B,CAAC;MAClE5C,eAAe,CAAC2C,QAAQ,CAACE,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,EAAEyC,GAAG,CAAC;MACnD;MACA9C,eAAe,CAAC2B,wBAAwB,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,iBAAiB,GAAG3F,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMsF,QAAQ,GAAG,MAAM1E,SAAS,CAAC2E,GAAG,CAAC,4BAA4B,CAAC;MAClE1C,eAAe,CAACyC,QAAQ,CAACE,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,EAAEyC,GAAG,CAAC;MACnD;MACA5C,eAAe,CAACuC,wBAAwB,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArF,SAAS,CAAC,MAAM;IACd,MAAM6F,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF7C,UAAU,CAAC,IAAI,CAAC;QAChB,MAAM8C,OAAO,CAACC,GAAG,CAAC,CAChBT,iBAAiB,CAAC,CAAC,EACnBM,iBAAiB,CAAC,CAAC,CACpB,CAAC;MACJ,CAAC,CAAC,OAAOF,GAAQ,EAAE;QACjBC,OAAO,CAAC1C,KAAK,CAAC,uBAAuB,EAAEyC,GAAG,CAAC;QAC3CxC,QAAQ,CAAC,0CAA0C,CAAC;QACpD;QACAN,eAAe,CAAC2B,wBAAwB,CAAC,CAAC,CAAC;QAC3CzB,eAAe,CAACuC,wBAAwB,CAAC,CAAC,CAAC;MAC7C,CAAC,SAAS;QACRrC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED6C,WAAW,CAAC,CAAC;;IAEb;IACA,MAAMG,iBAAiB,GAAIP,IAAsB,IAAK;MACpD,MAAMQ,YAAmB,GAAG;QAC1BzB,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ;QACvBC,MAAM,EAAEgB,IAAI,CAAChB,MAAM;QACnBG,UAAU,EAAEa,IAAI,CAACb,UAAU;QAC3BF,gBAAgB,EAAEe,IAAI,CAACS,WAAW,IAAI,CAAC;QACvCvB,cAAc,EAAEc,IAAI,CAACU,SAAS,IAAI,CAAC;QACnCtB,QAAQ,EAAEY,IAAI,CAACZ,QAAQ;QACvBC,QAAQ,EAAEW,IAAI,CAACX,QAAQ;QACvBsB,UAAU,EAAEX,IAAI,CAACW,UAAU,IAAIC,SAAS;QACxCC,aAAa,EAAEb,IAAI,CAACa,aAAa;QACjCvB,MAAM,EAAEU,IAAI,CAACV,MAAM;QACnBC,UAAU,EAAES,IAAI,CAACc,SAAS;QAC1BnB,UAAU,EAAEK,IAAI,CAACc;MACnB,CAAC;MACD3D,eAAe,CAAC4D,IAAI,IAClBA,IAAI,CAACC,GAAG,CAACC,CAAC,IAAKA,CAAC,CAAClC,QAAQ,KAAKyB,YAAY,CAACzB,QAAQ,GAAGyB,YAAY,GAAGS,CAAE,CACzE,CAAC;MACDtD,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,SAAS2C,YAAY,CAACzB,QAAQ,aAAayB,YAAY,CAAClB,MAAM,EAAE;QACzExB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;IAED,MAAMoD,kBAAkB,GAAIlB,IAAuB,IAAK;MACtDrC,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,WAAWmC,IAAI,CAACnC,OAAO,EAAE;QAClCC,QAAQ,EAAEkC,IAAI,CAACV,MAAM,KAAK,SAAS,GAAG,OAAO,GAAG;MAClD,CAAC,CAAC;IACJ,CAAC;;IAGD;IACA,IAAI;MACF;MACAjE,gBAAgB,CAAC8F,EAAE,CAAC7F,kBAAkB,CAAC8F,YAAY,EAAEb,iBAAiB,CAAC;MACvElF,gBAAgB,CAAC8F,EAAE,CAAC7F,kBAAkB,CAAC+F,aAAa,EAAEH,kBAAkB,CAAC;MACzE7F,gBAAgB,CAACiG,OAAO,CAAC,CAAC;MAC1BtD,WAAW,CAAC,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd0C,OAAO,CAAC1C,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDQ,WAAW,CAAC,cAAc,CAAC;IAC7B;;IAEA;IACA,MAAMuD,iBAAiB,GAAIvB,IAAsB,IAAK1B,cAAc,CAAC0B,IAAI,CAAC;IAC1E,MAAMwB,qBAAqB,GAAIxB,IAA0B,IAAKxB,kBAAkB,CAACwB,IAAI,CAAC;IACtF,MAAMyB,wBAAwB,GAAIzB,IAA6B,IAAKtB,qBAAqB,CAACsB,IAAI,CAAC;;IAE/F;IACA,IAAI;MACFzE,yBAAyB,CAAC+F,OAAO,CAAC,CAAC;MACnClD,oBAAoB,CAAC,YAAY,CAAC;MAElC7C,yBAAyB,CAACmG,gBAAgB,CAAClG,kBAAkB,CAACmG,YAAY,EAAEJ,iBAAiB,CAAC;MAC9FhG,yBAAyB,CAACmG,gBAAgB,CAAClG,kBAAkB,CAACoG,gBAAgB,EAAEJ,qBAAqB,CAAC;MACtGjG,yBAAyB,CAACmG,gBAAgB,CAAClG,kBAAkB,CAACqG,mBAAmB,EAAEJ,wBAAwB,CAAC;IAC9G,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACd0C,OAAO,CAAC1C,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DY,oBAAoB,CAAC,cAAc,CAAC;IACtC;IAEA,OAAO,MAAM;MACX;MACA,IAAI;QACF;QACA/C,gBAAgB,CAACyG,GAAG,CAACxG,kBAAkB,CAAC8F,YAAY,EAAEb,iBAAiB,CAAC;QACxElF,gBAAgB,CAACyG,GAAG,CAACxG,kBAAkB,CAAC+F,aAAa,EAAEH,kBAAkB,CAAC;QAC1E7F,gBAAgB,CAAC0G,UAAU,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOvE,KAAK,EAAE;QACd0C,OAAO,CAAC1C,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;MAEA,IAAI;QACF;QACAjC,yBAAyB,CAACyG,mBAAmB,CAACxG,kBAAkB,CAACmG,YAAY,EAAEJ,iBAAiB,CAAC;QACjGhG,yBAAyB,CAACyG,mBAAmB,CAACxG,kBAAkB,CAACoG,gBAAgB,EAAEJ,qBAAqB,CAAC;QACzGjG,yBAAyB,CAACyG,mBAAmB,CAACxG,kBAAkB,CAACqG,mBAAmB,EAAEJ,wBAAwB,CAAC;QAC/GlG,yBAAyB,CAACwG,UAAU,CAAC,CAAC;MACxC,CAAC,CAAC,OAAOvE,KAAK,EAAE;QACd0C,OAAO,CAAC1C,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;IACF,CAAC;EACH,CAAC,EAAE,CAACqC,iBAAiB,EAAEM,iBAAiB,CAAC,CAAC;EAE1C,MAAM8B,uBAAuB,GAAGA,CAAA,KAAM;IACpCtE,eAAe,CAAC;MAAE,GAAGD,YAAY;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EACnD,CAAC;EAED,oBACEhC,OAAA,CAACnB,SAAS;IAACyH,QAAQ,EAAC,IAAI;IAAC1F,EAAE,EAAE;MAAE2F,EAAE,EAAE;IAAE,CAAE;IAAAlG,QAAA,gBACrCL,OAAA,CAAClB,UAAU;MAAC0H,OAAO,EAAC,IAAI;MAACC,YAAY;MAAApG,QAAA,GAAC,mBAEpC,eAAAL,OAAA,CAACX,KAAK;QACJqH,YAAY,EAAEvE,QAAS;QACvBwE,KAAK,EAAExE,QAAQ,KAAK,WAAW,GAAG,SAAS,GAAGA,QAAQ,KAAK,YAAY,GAAG,SAAS,GAAG,OAAQ;QAC9FvB,EAAE,EAAE;UAAEgG,EAAE,EAAE;QAAE;MAAE;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACDjB,OAAA,CAACX,KAAK;QACLqH,YAAY,EAAE,cAAcnE,iBAAiB,EAAG;QAChDoE,KAAK,EAAEpE,iBAAiB,KAAK,WAAW,GAAG,SAAS,GAAG,OAAQ;QAC/D3B,EAAE,EAAE;UAAEgG,EAAE,EAAE;QAAE;MAAE;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,EAEZW,KAAK,iBAAI5B,OAAA,CAACb,KAAK;MAAC+C,QAAQ,EAAC,OAAO;MAACtB,EAAE,EAAE;QAAEiG,EAAE,EAAE;MAAE,CAAE;MAAAxG,QAAA,EAAEuB;IAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEhEjB,OAAA,CAACjB,GAAG;MAAC6B,EAAE,EAAE;QAAEkG,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAU,CAAE;MAAA1G,QAAA,eACnDL,OAAA,CAACV,IAAI;QAACgB,KAAK,EAAE+B,UAAW;QAAC2E,QAAQ,EAAEjE,eAAgB;QAAC,cAAW,gBAAgB;QAAA1C,QAAA,gBAC7EL,OAAA,CAACT,GAAG;UAAC0H,KAAK,EAAC,QAAQ;UAAA,GAAK9F,SAAS,CAAC,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxCjB,OAAA,CAACT,GAAG;UAAC0H,KAAK,EAAC,kBAAkB;UAAA,GAAK9F,SAAS,CAAC,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENjB,OAAA,CAACG,QAAQ;MAACG,KAAK,EAAE+B,UAAW;MAAC9B,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCL,OAAA,CAACf,IAAI;QAACiI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA9G,QAAA,gBACzBL,OAAA,CAACf,IAAI;UAACmI,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAhH,QAAA,eAChBL,OAAA,CAACH,iBAAiB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,EACNS,OAAO,iBACN1B,OAAA,CAACf,IAAI;UAACmI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACzG,EAAE,EAAE;YAAE0G,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlH,QAAA,eACnEL,OAAA,CAACd,gBAAgB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EACA,CAACS,OAAO,iBACP1B,OAAA,CAAAE,SAAA;UAAAG,QAAA,gBACEL,OAAA,CAACf,IAAI;YAACmI,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhH,QAAA,eAChBL,OAAA,CAACF,WAAW;cAAC0H,MAAM,EAAElG,YAAa;cAACmG,KAAK,EAAC;YAAe;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACPjB,OAAA,CAACf,IAAI;YAACmI,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhH,QAAA,eAChBL,OAAA,CAACF,WAAW;cAAC0H,MAAM,EAAEhG,YAAa;cAACiG,KAAK,EAAC;YAAe;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEXjB,OAAA,CAACG,QAAQ;MAACG,KAAK,EAAE+B,UAAW;MAAC9B,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCL,OAAA,CAACf,IAAI;QAACiI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA9G,QAAA,gBACzBL,OAAA,CAACf,IAAI;UAACmI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACK,EAAE,EAAE,CAAE;UAAArH,QAAA,eACvBL,OAAA,CAAChB,KAAK;YAAC4B,EAAE,EAAE;cAAE+G,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAvH,QAAA,gBAClCL,OAAA,CAAClB,UAAU;cAAC0H,OAAO,EAAC,IAAI;cAAAnG,QAAA,EAAC;YAAiB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACtDwB,WAAW,gBACVzC,OAAA,CAAAE,SAAA;cAAAG,QAAA,gBACEL,OAAA,CAAClB,UAAU;gBAAAuB,QAAA,GAAC,UAAQ,EAACoC,WAAW,CAACoF,MAAM;cAAA;gBAAA/G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrDjB,OAAA,CAAClB,UAAU;gBAAAuB,QAAA,GAAC,cAAY,EAACoC,WAAW,CAACqF,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAjH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxEjB,OAAA,CAAClB,UAAU;gBAAAuB,QAAA,GAAC,kBAAgB,EAACoC,WAAW,CAACuF,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAjH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA,eAChF,CAAC,gBAEHjB,OAAA,CAAClB,UAAU;cAAAuB,QAAA,EAAC;YAA0B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACPjB,OAAA,CAACf,IAAI;UAACmI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACK,EAAE,EAAE,CAAE;UAAArH,QAAA,eACvBL,OAAA,CAAChB,KAAK;YAAC4B,EAAE,EAAE;cAAE+G,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAvH,QAAA,gBAClCL,OAAA,CAAClB,UAAU;cAAC0H,OAAO,EAAC,IAAI;cAAAnG,QAAA,EAAC;YAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACrD0B,eAAe,gBACd3C,OAAA;cAAAK,QAAA,EAAM4H,IAAI,CAACC,SAAS,CAACvF,eAAe,CAACwF,OAAO,EAAE,IAAI,EAAE,CAAC;YAAC;cAAArH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE7DjB,OAAA,CAAClB,UAAU;cAAAuB,QAAA,EAAC;YAA4B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACrD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACPjB,OAAA,CAACf,IAAI;UAACmI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACK,EAAE,EAAE,CAAE;UAAArH,QAAA,eACvBL,OAAA,CAAChB,KAAK;YAAC4B,EAAE,EAAE;cAAE+G,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAvH,QAAA,gBAClCL,OAAA,CAAClB,UAAU;cAAC0H,OAAO,EAAC,IAAI;cAAAnG,QAAA,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACxD4B,kBAAkB,gBACjB7C,OAAA;cAAAK,QAAA,EAAM4H,IAAI,CAACC,SAAS,CAACrF,kBAAkB,CAACuF,OAAO,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAtH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEhEjB,OAAA,CAAClB,UAAU;cAAAuB,QAAA,EAAC;YAA+B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACxD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEXjB,OAAA,CAACZ,QAAQ;MACP4C,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBqG,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEjC,uBAAwB;MACjCkC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAApI,QAAA,eAE1DL,OAAA,CAACb,KAAK;QAACmJ,OAAO,EAAEjC,uBAAwB;QAACnE,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAACtB,EAAE,EAAE;UAAE8H,KAAK,EAAE;QAAO,CAAE;QAAArI,QAAA,EAC7FyB,YAAY,CAACG;MAAO;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAACI,EAAA,CAjUID,cAAwB;AAAAuH,GAAA,GAAxBvH,cAAwB;AAmU9B,eAAeA,cAAc;AAAC,IAAAF,EAAA,EAAAyH,GAAA;AAAAC,YAAA,CAAA1H,EAAA;AAAA0H,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}