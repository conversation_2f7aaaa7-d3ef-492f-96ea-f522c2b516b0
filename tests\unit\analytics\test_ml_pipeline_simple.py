#!/usr/bin/env python3
"""
Simple ML Pipeline Test - Tests core ML functionality without external dependencies
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__))))

async def test_ml_pipeline_core():
    """Test core ML pipeline components without external dependencies"""
    
    print("🧠 SIMPLE ML PIPELINE TEST")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {},
        "overall_status": "PASS"
    }
    
    # Test 1: WeightOptimizer Import and Basic Functionality
    print("\n1️⃣ Testing WeightOptimizer import and initialization...")
    try:
        from app.ml.models.weight_optimizer import WeightOptimizer
        
        # Create optimizer with minimal config (no external dependencies)
        optimizer = WeightOptimizer(
            redis_url=None,  # Skip Redis
            enable_tracking=False,  # Skip MLflow/W&B
            enable_advanced_features=False  # Skip advanced features
        )
        
        print("   ✅ WeightOptimizer imported successfully")
        print("   ✅ WeightOptimizer initialized without external dependencies")
        results["tests"]["weight_optimizer_import"] = "PASS"
        
    except Exception as e:
        print(f"   ❌ WeightOptimizer test failed: {e}")
        results["tests"]["weight_optimizer_import"] = f"FAIL: {e}"
        results["overall_status"] = "FAIL"
    
    # Test 2: Auto Trading Controller Import
    print("\n2️⃣ Testing Auto Trading Controller import...")
    try:
        from app.services.auto_trading_controller import AutoTradingController
        print("   ✅ AutoTradingController imported successfully")
        results["tests"]["auto_trading_controller_import"] = "PASS"
        
    except Exception as e:
        print(f"   ❌ AutoTradingController import failed: {e}")
        results["tests"]["auto_trading_controller_import"] = f"FAIL: {e}"
        results["overall_status"] = "FAIL"
    
    # Test 3: ML Routes Import
    print("\n3️⃣ Testing ML API routes import...")
    try:
        from app.api.routes.ml_routes import router as ml_router
        print("   ✅ ML routes imported successfully")
        print(f"   ✅ ML router has {len(ml_router.routes)} endpoints")
        results["tests"]["ml_routes_import"] = "PASS"
        
    except Exception as e:
        print(f"   ❌ ML routes import failed: {e}")
        results["tests"]["ml_routes_import"] = f"FAIL: {e}"
        results["overall_status"] = "FAIL"
    
    # Test 4: Basic Weight Optimization Logic
    print("\n4️⃣ Testing basic weight optimization logic...")
    try:
        from app.ml.models.weight_optimizer import WeightOptimizer
        
        optimizer = WeightOptimizer(
            enable_tracking=False,
            enable_advanced_features=False
        )
        
        # Test basic weight calculation with mock data
        mock_signals = {
            "momentum": 0.7,
            "mean_reversion": 0.3,
            "trend_following": 0.8,
            "volatility_targeting": 0.5
        }
        
        mock_performance = {
            "momentum": {"sharpe": 1.2, "returns": 0.15},
            "mean_reversion": {"sharpe": 0.8, "returns": 0.08},
            "trend_following": {"sharpe": 1.5, "returns": 0.20},
            "volatility_targeting": {"sharpe": 1.0, "returns": 0.12}
        }
        
        weights = optimizer.calculate_weights(mock_signals, mock_performance)
        
        # Validate weights
        total_weight = sum(weights.values())
        assert abs(total_weight - 1.0) < 0.01, f"Weights don't sum to 1.0: {total_weight}"
        assert all(w >= 0 for w in weights.values()), "Negative weights found"
        assert len(weights) == len(mock_signals), "Weight count mismatch"
        
        print("   ✅ Weight calculation successful")
        print(f"   ✅ Total weight: {total_weight:.3f}")
        print(f"   ✅ Weights: {weights}")
        results["tests"]["weight_calculation"] = "PASS"
        
    except Exception as e:
        print(f"   ❌ Weight calculation failed: {e}")
        results["tests"]["weight_calculation"] = f"FAIL: {e}"
        results["overall_status"] = "FAIL"
    
    # Test 5: Mock ML Prediction Generation
    print("\n5️⃣ Testing ML prediction generation...")
    try:
        from app.ml.models.weight_optimizer import WeightOptimizer
        
        optimizer = WeightOptimizer(enable_tracking=False)
        
        # Test prediction generation
        mock_market_data = {
            "price": 50000,
            "volume": 1000000,
            "volatility": 0.02,
            "trend": "bullish",
            "timestamp": datetime.now().isoformat()
        }
        
        prediction = await optimizer.generate_prediction(mock_market_data)
        
        assert "confidence" in prediction
        assert "signal_strength" in prediction
        assert "recommended_allocation" in prediction
        assert 0 <= prediction["confidence"] <= 1
        
        print("   ✅ ML prediction generated successfully")
        print(f"   ✅ Prediction confidence: {prediction['confidence']:.3f}")
        print(f"   ✅ Signal strength: {prediction['signal_strength']:.3f}")
        results["tests"]["ml_prediction"] = "PASS"
        
    except Exception as e:
        print(f"   ❌ ML prediction failed: {e}")
        results["tests"]["ml_prediction"] = f"FAIL: {e}"
        results["overall_status"] = "FAIL"
    
    # Test 6: Performance Metrics Calculation
    print("\n6️⃣ Testing performance metrics calculation...")
    try:
        from app.ml.models.weight_optimizer import WeightOptimizer
        
        optimizer = WeightOptimizer(enable_tracking=False)
        
        # Mock performance data
        mock_returns = [0.01, -0.005, 0.02, 0.015, -0.01, 0.008, 0.012]
        
        metrics = optimizer.calculate_performance_metrics(mock_returns)
        
        assert "sharpe_ratio" in metrics
        assert "total_return" in metrics
        assert "volatility" in metrics
        assert "max_drawdown" in metrics
        
        print("   ✅ Performance metrics calculated successfully")
        print(f"   ✅ Sharpe ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"   ✅ Total return: {metrics['total_return']:.3f}")
        results["tests"]["performance_metrics"] = "PASS"
        
    except Exception as e:
        print(f"   ❌ Performance metrics failed: {e}")
        results["tests"]["performance_metrics"] = f"FAIL: {e}"
        results["overall_status"] = "FAIL"
    
    # Generate summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed_tests = sum(1 for test in results["tests"].values() if test == "PASS")
    total_tests = len(results["tests"])
    
    print(f"Overall Status: {'✅ PASS' if results['overall_status'] == 'PASS' else '❌ FAIL'}")
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print()
    
    for test_name, status in results["tests"].items():
        status_emoji = "✅" if status == "PASS" else "❌"
        print(f"  {status_emoji} {test_name}: {status}")
    
    print("\n" + "=" * 50)
    
    if results["overall_status"] == "PASS":
        print("🎉 All core ML pipeline components are working correctly!")
        print("✅ Ready for production ML trading operations")
    else:
        print("⚠️  Some core components failed. Review the errors above.")
    
    # Save results
    with open("ml_pipeline_simple_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Results saved to: ml_pipeline_simple_test_results.json")
    
    return results["overall_status"] == "PASS"

if __name__ == "__main__":
    success = asyncio.run(test_ml_pipeline_core())
    sys.exit(0 if success else 1)