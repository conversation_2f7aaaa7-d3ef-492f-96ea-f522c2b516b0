{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Hello from test notebook')\n", "import pandas as pd\n", "df = pd.DataFrame({'test': [1, 2, 3]})\n", "df.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}