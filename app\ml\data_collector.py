"""
Data collection module for ML weight optimization.

This module contains functions for collecting historical market data and
strategy performance metrics for training ML models.
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

class DataCollector:
    """Collects and prepares data for ML model training."""
    
    def __init__(self, exchange_client, db_client=None):
        """Initialize the DataCollector.
        
        Args:
            exchange_client: Client for fetching market data from exchange
            db_client: Client for database operations (optional)
        """
        self.exchange_client = exchange_client
        self.db_client = db_client
        self.logger = logging.getLogger(__name__)
    
    async def collect_market_data(self, symbol: str, timeframe: str, 
                                 start_time: Optional[datetime] = None,
                                 end_time: Optional[datetime] = None,
                                 limit: int = 1000) -> pd.DataFrame:
        """Collect historical market data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            start_time: Start time for data collection (optional)
            end_time: End time for data collection (optional)
            limit: Maximum number of klines to fetch (total, not per request)
            
        Returns:
            DataFrame with historical market data
        """
        try:
            # Calculate timestamps if provided
            start_timestamp = int(start_time.timestamp() * 1000) if start_time else None
            end_timestamp = int(end_time.timestamp() * 1000) if end_time else None

            # Binance API max per request
            max_per_request = 1000
            all_klines = []
            batch_count = 0
            current_start = start_timestamp
            total_fetched = 0
            while True:
                batch_count += 1
                batch_limit = min(max_per_request, limit - total_fetched) if limit else max_per_request
                self.logger.info(f"Batch {batch_count}: Fetching up to {batch_limit} {timeframe} klines for {symbol} starting at {current_start}")
                # Fetch klines using exchange client's historical kline method
                klines = await self.exchange_client.get_historical_klines(
                    symbol=symbol,
                    interval=timeframe,
                    start_time=current_start,
                    end_time=end_timestamp,
                    limit=batch_limit
                )
                if not klines:
                    self.logger.info(f"No more klines returned at batch {batch_count}.")
                    break
                all_klines.extend(klines)
                total_fetched += len(klines)
                if limit and total_fetched >= limit:
                    break
                if len(klines) < batch_limit:
                    # No more data available
                    break
                # Update current_start to the next timestamp after the last kline
                last_kline_time = klines[-1][0]  # timestamp in ms
                # Add 1 ms to avoid overlap
                current_start = last_kline_time + 1
            if not all_klines:
                self.logger.warning(f"No klines data returned for {symbol}")
                return pd.DataFrame()
            # Convert klines to DataFrame
            df = pd.DataFrame(all_klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            # Convert types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            self.logger.info(f"Collected {len(df)} klines for {symbol} (batched)")
            return df
        except Exception as e:
            self.logger.error(f"Error collecting market data: {e}")
            return pd.DataFrame()
    
    async def collect_strategy_performance(self, strategy_name: str, 
                                          start_time: Optional[datetime] = None,
                                          end_time: Optional[datetime] = None) -> pd.DataFrame:
        """Collect historical performance data for a strategy.
        
        Args:
            strategy_name: Name of the strategy
            start_time: Start time for data collection (optional)
            end_time: End time for data collection (optional)
            
        Returns:
            DataFrame with strategy performance metrics
        """
        try:
            if not self.db_client:
                self.logger.warning("No database client provided for collecting strategy performance")
                return pd.DataFrame()
            
            # Build query
            query = f"SELECT * FROM strategy_performance WHERE strategy_name = '{strategy_name}'"
            
            if start_time:
                query += f" AND timestamp >= '{start_time.isoformat()}'"
            if end_time:
                query += f" AND timestamp <= '{end_time.isoformat()}'"
                
            query += " ORDER BY timestamp ASC"
            
            # Execute query
            self.logger.info(f"Querying performance data for {strategy_name}")
            result = await self.db_client.execute_query(query)
            
            if not result:
                self.logger.warning(f"No performance data found for {strategy_name}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(result)
            
            # Convert timestamp to datetime
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            self.logger.info(f"Collected {len(df)} performance records for {strategy_name}")
            return df
            
        except Exception as e:
            self.logger.error(f"Error collecting strategy performance: {e}")
            return pd.DataFrame()
    
    async def collect_training_data(self, symbol: str, timeframe: str,
                                   lookback_days: int = 90,
                                   window_size: int = 10,
                                   max_steps: int = 100) -> Tuple[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """Collect all data needed for training ML models.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for data
            window_size: Window size for feature engineering
            max_steps: Max steps for RL environment
        
        Returns:
            Tuple of (market_data, strategy_performances)
        """
        try:
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(days=lookback_days)

            # Calculate required number of klines
            # Required: window_size + max_steps + buffer (e.g., 2) + extra for feature engineering
            required_klines = window_size + max_steps + 100  # 100 as a buffer for feature engineering
            # Calculate how many klines are in the lookback period
            # Estimate: klines_per_day = 24*60 / interval_minutes
            interval_map = {'1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30, '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720, '1d': 1440}
            interval_minutes = interval_map.get(timeframe, 15)
            klines_per_day = int(24*60 / interval_minutes)
            estimated_klines = lookback_days * klines_per_day
            limit = max(required_klines, estimated_klines)

            self.logger.info(f"Requesting {limit} klines for {symbol} ({timeframe}) for lookback_days={lookback_days}, window_size={window_size}, max_steps={max_steps}")
            # Collect market data
            market_data = await self.collect_market_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_time,
                end_time=end_time,
                limit=limit
            )
            # Collect performance data for each strategy
            strategy_names = ['grid', 'technical_analysis', 'trend_following']
            strategy_performances = {}
            for strategy in strategy_names:
                performance = await self.collect_strategy_performance(
                    strategy_name=strategy,
                    start_time=start_time,
                    end_time=end_time
                )
                strategy_performances[strategy] = performance
            return market_data, strategy_performances
        except Exception as e:
            self.logger.error(f"Error collecting training data: {e}")
            return pd.DataFrame(), {}
