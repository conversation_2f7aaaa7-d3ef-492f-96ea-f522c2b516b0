import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict

class TradeStatus(str, Enum):
    PENDING_ENTRY = "PENDING_ENTRY"
    ENTRY_FILLED = "ENTRY_FILLED"
    SLTP_PLACED = "SLTP_PLACED"
    CLOSED_SL = "CLOSED_SL"
    CLOSED_TP = "CLOSED_TP"
    CLOSED_MANUAL = "CLOSED_MANUAL" # For potential future manual closure feature
    CLOSED = "CLOSED"
    ERROR = "ERROR"

class ManagedTrade(BaseModel):
    """Represents the state of a trade managed by the system."""
    trade_id: uuid.UUID = Field(default_factory=uuid.uuid4)
    symbol: str
    entry_order_id: Optional[str] = None
    entry_fill_price: Optional[Decimal] = None
    entry_fill_qty: Optional[Decimal] = None
    entry_side: str # 'BUY' or 'SELL'
    sl_order_id: Optional[str] = None
    tp_order_id: Optional[str] = None
    sl_price: Optional[Decimal] = None
    tp_price: Optional[Decimal] = None
    status: TradeStatus = TradeStatus.PENDING_ENTRY
    exit_reason: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    model_config = ConfigDict(
        from_attributes=True,  # Renamed from orm_mode in v2
        use_enum_values=True  # Store enum values as strings
    )


    def update_timestamp(self):
        """Updates the updated_at timestamp."""
        self.updated_at = datetime.now(timezone.utc)