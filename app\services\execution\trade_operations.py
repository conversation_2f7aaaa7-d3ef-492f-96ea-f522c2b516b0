"""Trade management operations for the execution service."""
import logging
from typing import List, Optional

from app.services.execution.trade_state import ManagedTrade
from app.services.execution.models import OrderStatus

logger = logging.getLogger(__name__)

class TradeOperationsMixin:
    """Mixin for execution service trade operations."""

    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an order.

        Args:
            symbol: The trading symbol.
            order_id: The order ID to cancel.

        Returns:
            True if the order was cancelled, False otherwise.
        """
        return await self.order_manager.cancel_order(symbol, order_id)

    async def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get the status of an order.

        Args:
            order_id: The order ID to check.

        Returns:
            The order status, or None if the order was not found.
        """
        return await self.order_manager.get_order_status(order_id)

    async def get_active_trades(self) -> List[ManagedTrade]:
        """Get all active trades.

        Returns:
            A list of all active trades.
        """
        if self.trade_manager:
            return await self.trade_manager.get_active_trades()
        else:
            logger.warning("Cannot get active trades: trade_manager is not initialized.")
            return []

    async def get_trade_by_id(self, trade_id: str) -> Optional[ManagedTrade]:
        """Get a trade by ID.

        Args:
            trade_id: The trade ID to look up.

        Returns:
            The trade, or None if not found.
        """
        if self.trade_manager:
            return await self.trade_manager.get_trade_by_id(trade_id)
        else:
            logger.warning(f"Cannot get trade {trade_id}: trade_manager is not initialized.")
            return None

    async def close_trade(self, trade_id: str, reason: str = "MANUAL") -> bool:
        """Close a trade manually.

        Args:
            trade_id: The trade ID to close.
            reason: The reason for closing the trade.

        Returns:
            True if the trade was closed successfully, False otherwise.
        """
        if self.trade_manager:
            return await self.trade_manager.close_trade(trade_id, reason)
        else:
            logger.warning(f"Cannot close trade {trade_id}: trade_manager is not initialized.")
            return False

    async def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all open orders for a symbol.

        Args:
            symbol: The trading symbol.

        Returns:
            True if all orders were cancelled, False otherwise.
        """
        try:
            if self.order_manager:
                # Get all active orders for the symbol
                active_orders = [order for order in self.active_orders.values()
                                if order.symbol == symbol]

                # Cancel each order
                success = True
                for order in active_orders:
                    if not await self.cancel_order(symbol, order.exchange_order_id):
                        success = False

                return success
            else:
                logger.warning(f"Cannot cancel orders for {symbol}: order_manager is not initialized.")
                return False
        except Exception as e:
            logger.error(f"Error cancelling all orders for {symbol}: {e}")
            return False

    async def close_position(self, symbol: str) -> bool:
        """Close any open position for a symbol.

        Args:
            symbol: The trading symbol.

        Returns:
            True if the position was closed successfully, False otherwise.
        """
        try:
            if self.trade_manager:
                # Get all active trades for the symbol
                active_trades = [trade for trade in await self.get_active_trades()
                               if trade.symbol == symbol]

                # Close each trade
                success = True
                for trade in active_trades:
                    if not await self.close_trade(trade.id, "STRATEGY_SWITCH"):
                        success = False

                return success
            else:
                logger.warning(f"Cannot close position for {symbol}: trade_manager is not initialized.")
                return False
        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            return False
