from uuid import UUID
from typing import List, Optional, Sequence, Any, TypeVar, Union, cast
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
import logging
logger = logging.getLogger(__name__)

# Import models
from app.models import ManagedTradeDB # SQLAlchemy model
# Move this import to avoid circular imports
# from app.services.execution.trade_state import ManagedTrade as ManagedTradePydantic # Pydantic model
from app.models.trade_status import TradeStatus

# Define a TypeVar for the return type for create_trade and update_trade methods
T = TypeVar('T', bound=ManagedTradeDB)

class TradeRepository:
    """Handles database operations for ManagedTradeDB."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_trade(self, trade_pydantic: Any) -> ManagedTradeDB:
        """Creates a new trade record in the database."""
        # Import here to avoid circular imports
        # from app.services.execution.trade_state import ManagedTrade as ManagedTradePydantic
        
        logger.info(f"Creating trade record for trade_id: {trade_pydantic.trade_id}")
        # Use model_dump() if it exists, otherwise fallback to dict()
        if hasattr(trade_pydantic, 'model_dump'):
            trade_data = trade_pydantic.model_dump()
        else:
            trade_data = trade_pydantic.__dict__.copy()
            
        db_trade = ManagedTradeDB(**trade_data)
        self.session.add(db_trade)
        try:
            await self.session.commit()
            await self.session.refresh(db_trade)
            logger.info(f"Successfully created trade record: {db_trade.trade_id}")
            return db_trade
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error creating trade {trade_pydantic.trade_id}: {e}")
            raise

    async def get_trade_by_id(self, trade_id: UUID) -> Optional[ManagedTradeDB]:
        """Retrieves a trade by its UUID."""
        logger.debug(f"Fetching trade by id: {trade_id}")
        stmt = select(ManagedTradeDB).where(ManagedTradeDB.trade_id == str(trade_id))
        result = await self.session.execute(stmt)
        trade = result.scalar_one_or_none()
        if trade:
            logger.debug(f"Found trade: {trade_id}")
        else:
            logger.warning(f"Trade not found: {trade_id}")
        return trade

    async def update_trade(self, trade_pydantic: Any) -> Optional[ManagedTradeDB]:
        """Updates an existing trade record."""
        logger.info(f"Updating trade record for trade_id: {trade_pydantic.trade_id}")
        
        # Call update_timestamp if it exists
        if hasattr(trade_pydantic, 'update_timestamp') and callable(getattr(trade_pydantic, 'update_timestamp')):
            trade_pydantic.update_timestamp() # Ensure updated_at is current
            
        # Use model_dump if it exists, otherwise fallback to dict
        if hasattr(trade_pydantic, 'model_dump'):
            trade_data = trade_pydantic.model_dump(exclude_unset=True)
        else:
            trade_data = {k: v for k, v in trade_pydantic.__dict__.items() if not k.startswith('_')}
            
        stmt = (
            update(ManagedTradeDB)
            .where(ManagedTradeDB.trade_id == str(trade_pydantic.trade_id))
            .values(**trade_data) # Only update provided fields
            .execution_options(synchronize_session="fetch") # Strategy for ORM state sync
        )
        try:
            result = await self.session.execute(stmt)
            if result.rowcount == 0:
                logger.warning(f"Trade not found for update: {trade_pydantic.trade_id}")
                return None
            await self.session.commit()
            # Re-fetch the updated object to return it
            updated_trade = await self.get_trade_by_id(trade_pydantic.trade_id)
            logger.info(f"Successfully updated trade record: {trade_pydantic.trade_id}")
            return updated_trade
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error updating trade {trade_pydantic.trade_id}: {e}")
            raise

    async def get_active_trades(self) -> Sequence[ManagedTradeDB]:
        """Retrieves all trades that are not in a final closed or error state."""
        logger.debug("Fetching active trades")
        active_statuses = [
            TradeStatus.PENDING_ENTRY,
            TradeStatus.ENTRY_FILLED,
            TradeStatus.SLTP_PLACED,
        ]
        stmt = select(ManagedTradeDB).where(ManagedTradeDB.status.in_(active_statuses))
        result = await self.session.execute(stmt)
        trades = result.scalars().all()
        logger.debug(f"Found {len(trades)} active trades.")
        return trades

    async def get_trade_by_order_id(self, order_id: str) -> Optional[ManagedTradeDB]:
        """Finds a trade associated with a given SL or TP order ID."""
        logger.debug(f"Searching for trade by order_id: {order_id}")
        stmt = select(ManagedTradeDB).where(
            (ManagedTradeDB.sl_order_id == order_id) | (ManagedTradeDB.tp_order_id == order_id)
        )
        result = await self.session.execute(stmt)
        trade = result.scalar_one_or_none()
        if trade:
            logger.debug(f"Found trade {trade.trade_id} associated with order {order_id}")
        else:
            logger.debug(f"No trade found for order_id {order_id}")
        return trade

    async def get_closed_trades(self, user_id: Optional[str] = None, order_by: Any = None) -> List[ManagedTradeDB]:
        """Retrieves closed trades with optional ordering and filtering by user_id.

        Args:
            user_id: Optional user identifier to filter trades by user
            order_by: Optional SQLAlchemy column to order the results by

        Returns:
            A list of closed trades
        """
        logger.debug(f"Fetching closed trades for user_id: {user_id or 'all'}")
        closed_statuses = [
            TradeStatus.CLOSED_SL,
            TradeStatus.CLOSED_TP,
            TradeStatus.CLOSED_MANUAL,
            TradeStatus.ERROR
        ]

        # Build query
        query = select(ManagedTradeDB).where(ManagedTradeDB.status.in_(closed_statuses))
        
        # Add order_by if specified
        if order_by is not None:
            query = query.order_by(order_by)
        else:
            # Default ordering by closed_at timestamp
            query = query.order_by(ManagedTradeDB.closed_at)
            
        # Add user filtering if specified (assuming a user_id column exists)
        # if user_id is not None:
        #     query = query.where(ManagedTradeDB.user_id == user_id)
        # Note: Commented out as your model might not have user_id column yet

        result = await self.session.execute(query)
        trades = result.scalars().all()
        logger.debug(f"Found {len(trades)} closed trades.")
        return list(trades)

    async def get_recent_trades(self, limit: int = 20) -> Sequence[ManagedTradeDB]:
        """Retrieves recently closed trades.

        Args:
            limit: Maximum number of trades to return.

        Returns:
            A sequence of recently closed trades, ordered by updated_at timestamp (newest first).
        """
        logger.debug(f"Fetching recent trades with limit {limit}")
        closed_statuses = [
            TradeStatus.CLOSED_SL,
            TradeStatus.CLOSED_TP,
            TradeStatus.CLOSED_MANUAL,
            TradeStatus.ERROR
        ]

        # Select trades with closed statuses, ordered by updated_at timestamp (newest first)
        stmt = (
            select(ManagedTradeDB)
            .where(ManagedTradeDB.status.in_(closed_statuses))
            .order_by(ManagedTradeDB.updated_at.desc())
            .limit(limit)
        )

        result = await self.session.execute(stmt)
        trades = result.scalars().all()
        logger.debug(f"Found {len(trades)} recent trades.")
        return trades

    async def get_all_closed_trades(self) -> Sequence[ManagedTradeDB]:
        """Retrieves all closed trades."""
        logger.debug("Fetching all closed trades")
        closed_statuses = [
            TradeStatus.CLOSED_SL,
            TradeStatus.CLOSED_TP,
            TradeStatus.CLOSED_MANUAL,
            TradeStatus.ERROR
        ]

        # Select all trades with closed statuses, ordered by updated_at timestamp (newest first)
        stmt = (
            select(ManagedTradeDB)
            .where(ManagedTradeDB.status.in_(closed_statuses))
            .order_by(ManagedTradeDB.updated_at.desc())
        )

        result = await self.session.execute(stmt)
        trades = result.scalars().all()
        logger.debug(f"Found {len(trades)} closed trades.")
        return trades

    async def save_trade_state_snapshot(self, snapshot: dict) -> None:
        """Saves a trade state snapshot to the database.
        
        Args:
            snapshot: Dictionary representing the trade state snapshot
        """
        logger.info("Saving trade state snapshot")
        
        try:
            # Import TradeStateSnapshotDB here to avoid circular imports
            from app.models import TradeStateSnapshotDB
            
            snapshot_record = TradeStateSnapshotDB(
                snapshot_data=snapshot
            )
            self.session.add(snapshot_record)
            await self.session.commit()
            logger.info(f"Successfully saved trade state snapshot (id: {snapshot_record.id})")
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error saving trade state snapshot: {e}")
            raise


    # Optional: Add delete method if needed, though likely trades should be marked inactive/closed
    # async def delete_trade(self, trade_id: uuid.UUID) -> bool:
    #     """Deletes a trade record."""
    #     logger.warning(f"Attempting to delete trade: {trade_id}")
    #     stmt = delete(ManagedTradeDB).where(ManagedTradeDB.trade_id == str(trade_id))
    #     try:
    #         result = await self.session.execute(stmt)
    #         await self.session.commit()
    #         deleted = result.rowcount > 0
    #         if deleted:
    #             logger.info(f"Successfully deleted trade: {trade_id}")
    #         else:
    #             logger.warning(f"Trade not found for deletion: {trade_id}")
    #         return deleted
    #     except Exception as e:
    #         await self.session.rollback()
    #         logger.error(f"Error deleting trade {trade_id}: {e}")
    #         raise

# Example usage (for testing or integration):
# from app.database import SessionLocal, init_db
# async def main():
#     init_db()
#     async with SessionLocal() as session:
#         repo = TradeRepository(session)
#         # ... perform operations ...
# if __name__ == "__main__":
#     import asyncio
#     asyncio.run(main())