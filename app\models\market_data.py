"""SQLAlchemy model for market data."""
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Numeric, Integer, Index
from sqlalchemy.dialects.postgresql import UUID

from app.models.base import Base

class MarketDataDB(Base):
    """SQLAlchemy model for persisting market data (OHLCV)."""
    __tablename__ = "market_data"

    # Primary key is a composite of symbol, timeframe, and timestamp
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    symbol = Column(String, nullable=False, index=True)
    timeframe = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # OHLCV data
    open = Column(Numeric(precision=20, scale=10), nullable=False)
    high = Column(Numeric(precision=20, scale=10), nullable=False)
    low = Column(Numeric(precision=20, scale=10), nullable=False)
    close = Column(Numeric(precision=20, scale=10), nullable=False)
    volume = Column(Numeric(precision=20, scale=10), nullable=False)
    
    # Additional fields
    trades = Column(Integer, nullable=True)
    quote_asset_volume = Column(Numeric(precision=20, scale=10), nullable=True)
    taker_buy_base_asset_volume = Column(Numeric(precision=20, scale=10), nullable=True)
    taker_buy_quote_asset_volume = Column(Numeric(precision=20, scale=10), nullable=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    
    # Create a unique constraint on symbol, timeframe, and timestamp
    __table_args__ = (Index('ix_market_data_unique', 'symbol', 'timeframe', 'timestamp', unique=True),)
