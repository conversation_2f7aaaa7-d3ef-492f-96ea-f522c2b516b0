#!/usr/bin/env python3
"""
Test Real Binance API Connection - Simple Version
This script tests the actual Binance testnet API connection directly without circular imports
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from binance import AsyncClient

# Load environment variables
load_dotenv('.env')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_binance_api():
    """Test real Binance API connection and basic operations"""
    print("=" * 60)
    print("TESTING REAL BINANCE API CONNECTION")
    print("=" * 60)
    
    client = None
    
    try:
        # Get credentials from environment
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        use_testnet = os.getenv('USE_TESTNET', 'True').lower() == 'true'
        
        print(f"✓ API Key: {api_key[:8]}..." if api_key else "✗ No API key")
        print(f"✓ Using testnet: {use_testnet}")
        
        if not api_key or not api_secret:
            raise ValueError("BINANCE_API_KEY and BINANCE_API_SECRET must be set in .env file")
        
        # Create Binance client
        client = await AsyncClient.create(
            api_key=api_key,
            api_secret=api_secret,
            testnet=use_testnet
        )
        print("✓ Binance client created and connected")
        
        # Test connection
        await client.ping()
        print("✓ Ping successful")
        
        # Test 1: Get account balance
        print("\n--- Test 1: Account Balance ---")
        balance = await client.futures_account_balance()
        print(f"✓ Account balance retrieved: {len(balance)} assets")
        for asset_data in balance[:5]:  # Show first 5 assets
            asset = asset_data['asset']
            available = float(asset_data['availableBalance'])
            if available > 0:
                print(f"  {asset}: {available}")
        
        # Test 2: Get ticker price
        print("\n--- Test 2: Ticker Price ---")
        ticker = await client.futures_symbol_ticker(symbol="BTCUSDT")
        price = float(ticker['price'])
        print(f"✓ BTCUSDT ticker: ${price:,.2f}")
        
        # Test 3: Get exchange info
        print("\n--- Test 3: Exchange Info ---")
        exchange_info = await client.futures_exchange_info()
        symbols = exchange_info.get('symbols', [])
        print(f"✓ Exchange info: {len(symbols)} symbols available")
        
        # Test 4: Get order book
        print("\n--- Test 4: Order Book ---")
        order_book = await client.futures_order_book(symbol="BTCUSDT", limit=5)
        bids = order_book.get('bids', [])
        asks = order_book.get('asks', [])
        print(f"✓ Order book: {len(bids)} bids, {len(asks)} asks")
        if bids:
            print(f"  Best bid: ${float(bids[0][0]):,.2f}")
        if asks:
            print(f"  Best ask: ${float(asks[0][0]):,.2f}")
        
        # Test 5: Get recent trades
        print("\n--- Test 5: Recent Trades ---")
        trades = await client.futures_recent_trades(symbol="BTCUSDT", limit=3)
        print(f"✓ Recent trades: {len(trades)} trades")
        for trade in trades[:2]:
            print(f"  Trade: ${float(trade['price']):,.2f} @ {trade['qty']}")
        
        # Test 6: Get open orders (should be empty initially)
        print("\n--- Test 6: Open Orders ---")
        open_orders = await client.futures_get_open_orders(symbol="BTCUSDT")
        print(f"✓ Open orders: {len(open_orders)} orders")
        
        # Test 7: Get positions
        print("\n--- Test 7: Positions ---")
        positions = await client.futures_position_information(symbol="BTCUSDT")
        print(f"✓ Position info: {len(positions)} positions")
        for pos in positions:
            size = float(pos['positionAmt'])
            if size != 0:
                print(f"  Position: {pos['symbol']} - {size} @ ${float(pos['entryPrice']):,.2f}")
        
        # Test 8: Get historical klines
        print("\n--- Test 8: Historical Data ---")
        klines = await client.futures_klines(
            symbol="BTCUSDT",
            interval="1h",
            limit=5
        )
        print(f"✓ Historical klines: {len(klines)} candles")
        if klines:
            latest = klines[-1]
            print(f"  Latest candle: O:{float(latest[1]):,.2f} H:{float(latest[2]):,.2f} L:{float(latest[3]):,.2f} C:{float(latest[4]):,.2f}")
        
        print("\n" + "=" * 60)
        print("✅ ALL BINANCE API TESTS PASSED")
        print("Real Binance testnet API is working correctly!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Binance API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up client connection
        if client:
            try:
                await client.close_connection()
                print("\n✓ Binance client connection closed")
            except Exception as e:
                print(f"\n⚠️  Error closing Binance client: {e}")

if __name__ == "__main__":
    success = asyncio.run(test_real_binance_api())
    exit(0 if success else 1)