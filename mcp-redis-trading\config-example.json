{"description": "Example Claude MCP configuration for Redis Trading MCP Server", "mcpServers": {"redis-trading": {"command": "node", "args": ["/absolute/path/to/mcp-redis-trading/dist/index.js"], "env": {"REDIS_URL": "redis://localhost:6379", "REDIS_DB": "0"}}}, "instructions": ["This configuration integrates the custom Redis Trading MCP server with Claude.", "Replace '/absolute/path/to/mcp-redis-trading' with the actual installation path.", "For global installation, use: 'mcp-redis-trading' instead of the full path.", "Ensure Redis server is running before starting <PERSON> with this configuration."], "environment_variables": {"REDIS_URL": {"description": "Redis connection URL", "default": "redis://localhost:6379", "examples": ["redis://localhost:6379", "redis://username:password@redis-host:6379", "rediss://secure-redis-host:6380"]}, "REDIS_DB": {"description": "Redis database number", "default": "0", "examples": ["0", "1", "2"]}}, "usage_examples": {"strategy_weights": {"description": "Cache and retrieve ML-optimized strategy weights", "tools": ["cache_strategy_weights", "get_strategy_weights"]}, "portfolio_metrics": {"description": "Cache real-time portfolio performance metrics", "tools": ["cache_portfolio_metrics", "get_portfolio_metrics"]}, "position_sizing": {"description": "<PERSON><PERSON> Criterion and volatility calculations", "tools": ["cache_kelly_stats", "get_kelly_stats", "cache_volatility_adjustment", "get_volatility_adjustment"]}, "signal_aggregation": {"description": "Cache aggregated signals from multiple strategies", "tools": ["cache_aggregated_signals", "get_aggregated_signals"]}, "audit_trail": {"description": "Track position calculation history for compliance", "tools": ["cache_position_calculation", "get_calculation_history"]}, "maintenance": {"description": "Cache management and monitoring", "tools": ["clear_cache_by_pattern", "get_cache_stats"]}}, "integration_with_your_project": {"portfolio_manager": {"file": "app/strategies/portfolio_manager.py", "usage": "Replace direct Redis calls with MCP tool calls for better abstraction and validation"}, "position_calculator": {"file": "app/strategies/position_size_calculator.py", "usage": "Use specialized Kelly stats and volatility caching tools"}, "claude_config": {"file": ".claude/mcp_config.json", "merge_with": "Combine with existing MCP servers (ZenML, Supabase, Telegram, etc.)"}}, "performance_characteristics": {"cache_hit_rate": "Expected >90% for strategy weights and portfolio metrics", "response_time": "<100ms for cached operations, <1s for complex calculations", "memory_usage": "~10-50MB depending on cache size and history depth", "concurrent_operations": "Supports multiple strategies accessing cache simultaneously"}}