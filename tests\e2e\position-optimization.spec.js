/**
 * E2E Tests for Dynamic Position Optimization System
 * Task 2.2.2: Comprehensive testing with Playwright MCP
 */

import { test, expect } from '@playwright/test';

test.describe('Dynamic Position Optimization E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for application to be ready
    await page.waitForLoadState('networkidle');
  });

  test.describe('Performance Requirements', () => {
    
    test('Position size calculation meets <100ms target', async ({ page }) => {
      // Navigate to position calculation endpoint
      const startTime = Date.now();
      
      const response = await page.request.post('/api/v1/position/calculate', {
        data: {
          symbol: 'BTCUSDT',
          strategy: 'technical_analysis',
          portfolio_value: 100000,
          market_price: 50000
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(100); // Sub-100ms requirement
      
      const result = await response.json();
      expect(result).toHaveProperty('recommended_position_size');
      expect(result).toHaveProperty('calculation_time_ms');
      expect(result.calculation_time_ms).toBeLessThan(100);
      
      console.log(`✅ Position calculation time: ${responseTime}ms (target: <100ms)`);
    });

    test('Volatility calculation performance', async ({ page }) => {
      const startTime = Date.now();
      
      const response = await page.request.post('/api/v1/volatility/calculate', {
        data: {
          symbol: 'BTCUSDT',
          timeframe: 60,
          market_data: {
            price: 50000,
            volume: 1000000,
            timestamp: new Date().toISOString()
          }
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(100);
      
      const result = await response.json();
      expect(result).toHaveProperty('current_volatility');
      expect(result).toHaveProperty('volatility_regime');
      expect(result.calculation_time_ms).toBeLessThan(100);
      
      console.log(`✅ Volatility calculation time: ${responseTime}ms`);
    });

    test('Correlation matrix calculation performance', async ({ page }) => {
      const startTime = Date.now();
      
      const response = await page.request.post('/api/v1/correlation/matrix', {
        data: {
          symbols: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
          window_size: 24,
          strategies: ['technical_analysis', 'grid', 'trend_following']
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(100);
      
      const result = await response.json();
      expect(result).toHaveProperty('correlation_matrix');
      expect(result.calculation_time_ms).toBeLessThan(100);
      
      console.log(`✅ Correlation matrix time: ${responseTime}ms`);
    });
  });

  test.describe('Redis Cache Performance', () => {
    
    test('Cache hit rate optimization', async ({ page }) => {
      // Make initial request to warm cache
      await page.request.post('/api/v1/position/calculate', {
        data: {
          symbol: 'BTCUSDT',
          strategy: 'technical_analysis',
          portfolio_value: 100000,
          market_price: 50000
        }
      });
      
      // Make second request - should hit cache
      const startTime = Date.now();
      const response = await page.request.post('/api/v1/position/calculate', {
        data: {
          symbol: 'BTCUSDT',
          strategy: 'technical_analysis',
          portfolio_value: 100000,
          market_price: 50000
        }
      });
      const endTime = Date.now();
      
      expect(response.ok()).toBeTruthy();
      
      const result = await response.json();
      expect(result.cache_hit).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(50); // Cache should be even faster
      
      console.log(`✅ Cache hit response time: ${endTime - startTime}ms`);
    });

    test('Cache performance under load', async ({ page }) => {
      const requests = [];
      const startTime = Date.now();
      
      // Send 10 concurrent requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          page.request.post('/api/v1/position/calculate', {
            data: {
              symbol: 'BTCUSDT',
              strategy: 'technical_analysis',
              portfolio_value: 100000 + i * 1000,
              market_price: 50000 + i * 100
            }
          })
        );
      }
      
      const responses = await Promise.all(requests);
      const endTime = Date.now();
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.ok()).toBeTruthy();
      });
      
      // Average time per request should still be reasonable
      const avgTime = (endTime - startTime) / requests.length;
      expect(avgTime).toBeLessThan(200); // Allow some overhead for concurrent requests
      
      console.log(`✅ Concurrent requests avg time: ${avgTime}ms`);
    });
  });

  test.describe('Real-Time Data Integration', () => {
    
    test('Cross-exchange validation workflow', async ({ page }) => {
      const response = await page.request.post('/api/v1/validation/cross-exchange', {
        data: {
          symbol: 'BTCUSDT',
          sources: ['binance', 'coincap'],
          validation_threshold: 0.01
        }
      });
      
      expect(response.ok()).toBeTruthy();
      
      const result = await response.json();
      expect(result).toHaveProperty('validation_passed');
      expect(result).toHaveProperty('price_sources');
      expect(result).toHaveProperty('validation_score');
      expect(result.calculation_time_ms).toBeLessThan(200);
      
      console.log(`✅ Cross-exchange validation: ${result.validation_passed ? 'PASSED' : 'FAILED'}`);
    });

    test('Multi-source Kelly criterion calculation', async ({ page }) => {
      const response = await page.request.post('/api/v1/kelly/multi-source', {
        data: {
          symbol: 'BTCUSDT',
          data_sources: ['binance', 'coincap'],
          lookback_periods: [24, 48, 72]
        }
      });
      
      expect(response.ok()).toBeTruthy();
      
      const result = await response.json();
      expect(result).toHaveProperty('optimal_fraction');
      expect(result).toHaveProperty('cross_validation_score');
      expect(result).toHaveProperty('reliability_weighted_fraction');
      expect(result.calculation_time_ms).toBeLessThan(150);
      
      console.log(`✅ Multi-source Kelly: ${result.optimal_fraction} (confidence: ${result.cross_validation_score})`);
    });
  });

  test.describe('Portfolio Management Integration', () => {
    
    test('Ensemble strategy execution', async ({ page }) => {
      const response = await page.request.post('/api/v1/portfolio/execute-ensemble', {
        data: {
          symbol: 'BTCUSDT',
          portfolio_value: 100000,
          strategies: {
            technical_analysis: 0.4,
            grid: 0.3,
            trend_following: 0.3
          }
        }
      });
      
      expect(response.ok()).toBeTruthy();
      
      const result = await response.json();
      expect(result).toHaveProperty('execution_results');
      expect(result).toHaveProperty('aggregated_signal');
      expect(result).toHaveProperty('risk_metrics');
      expect(result.total_execution_time_ms).toBeLessThan(500);
      
      console.log(`✅ Ensemble execution time: ${result.total_execution_time_ms}ms`);
    });

    test('Risk monitoring and alerts', async ({ page }) => {
      // Simulate high risk scenario
      const response = await page.request.post('/api/v1/risk/monitor', {
        data: {
          portfolio: {
            total_exposure: 0.95, // High exposure
            volatility: 0.25,     // High volatility
            correlation: 0.85     // High correlation
          },
          thresholds: {
            max_exposure: 0.9,
            max_volatility: 0.2,
            max_correlation: 0.8
          }
        }
      });
      
      expect(response.ok()).toBeTruthy();
      
      const result = await response.json();
      expect(result).toHaveProperty('risk_violations');
      expect(result).toHaveProperty('alert_level');
      expect(result).toHaveProperty('recommended_actions');
      
      // Should detect violations
      expect(result.risk_violations.length).toBeGreaterThan(0);
      expect(['HIGH', 'CRITICAL'].includes(result.alert_level)).toBeTruthy();
      
      console.log(`✅ Risk monitoring detected ${result.risk_violations.length} violations`);
    });
  });

  test.describe('Health Checks and Monitoring', () => {
    
    test('Application health check', async ({ page }) => {
      const response = await page.request.get('/health');
      
      expect(response.ok()).toBeTruthy();
      
      const health = await response.json();
      expect(health.status).toBe('healthy');
      expect(health).toHaveProperty('services');
      expect(health).toHaveProperty('performance_metrics');
      
      // Verify all critical services are healthy
      expect(health.services.redis).toBe('healthy');
      expect(health.services.position_calculator).toBe('healthy');
      expect(health.services.volatility_calculator).toBe('healthy');
      
      console.log(`✅ Application health: ${health.status}`);
    });

    test('Position optimizer health check', async ({ page }) => {
      const response = await page.request.get('/health/position-optimizer');
      
      expect(response.ok()).toBeTruthy();
      
      const health = await response.json();
      expect(health.status).toBe('healthy');
      expect(health).toHaveProperty('performance_stats');
      expect(health.performance_stats.avg_calculation_time_ms).toBeLessThan(100);
      
      console.log(`✅ Position optimizer health: ${health.status}`);
      console.log(`   Avg calc time: ${health.performance_stats.avg_calculation_time_ms}ms`);
    });

    test('Performance metrics endpoint', async ({ page }) => {
      const response = await page.request.get('/metrics');
      
      expect(response.ok()).toBeTruthy();
      
      const metrics = await response.text();
      expect(metrics).toContain('position_calculation_duration');
      expect(metrics).toContain('cache_hit_rate');
      expect(metrics).toContain('volatility_calculation_duration');
      
      console.log(`✅ Metrics endpoint responding with Prometheus format`);
    });
  });

  test.describe('Error Handling and Resilience', () => {
    
    test('Graceful degradation under high load', async ({ page }) => {
      // Simulate high load scenario
      const requests = [];
      for (let i = 0; i < 50; i++) {
        requests.push(
          page.request.post('/api/v1/position/calculate', {
            data: {
              symbol: 'BTCUSDT',
              strategy: 'technical_analysis',
              portfolio_value: 100000 + i,
              market_price: 50000 + i
            }
          })
        );
      }
      
      const responses = await Promise.all(requests);
      
      // Most requests should succeed
      const successfulRequests = responses.filter(r => r.ok()).length;
      expect(successfulRequests).toBeGreaterThan(40); // 80% success rate minimum
      
      console.log(`✅ High load test: ${successfulRequests}/50 requests succeeded`);
    });

    test('Fallback mechanisms', async ({ page }) => {
      // Test with invalid data to trigger fallback
      const response = await page.request.post('/api/v1/position/calculate', {
        data: {
          symbol: 'INVALID',
          strategy: 'invalid_strategy',
          portfolio_value: -1000, // Invalid value
          market_price: 0         // Invalid price
        }
      });
      
      expect(response.ok()).toBeTruthy(); // Should still respond
      
      const result = await response.json();
      expect(result).toHaveProperty('recommended_position_size');
      expect(result.recommended_position_size).toBeGreaterThan(0); // Should fallback to minimum
      expect(result).toHaveProperty('safety_warnings');
      expect(result.safety_warnings.length).toBeGreaterThan(0);
      
      console.log(`✅ Fallback mechanism triggered: ${result.safety_warnings.length} warnings`);
    });
  });
});