#!/usr/bin/env python3
"""
Test script for real-time analytics integration with the dashboard.
Verifies that all components are working together correctly.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_supabase_service():
    """Test Supabase service initialization."""
    print("🔍 Testing Supabase Service...")
    
    try:
        from app.services.mcp.supabase_service import SupabaseService
        
        # Initialize service
        supabase_service = SupabaseService()
        
        # Test connection
        connection_ok = await supabase_service.test_connection()
        if connection_ok:
            print("✅ Supabase service connection successful")
        else:
            print("⚠️ Supabase using mock implementation")
        
        # Test storing metrics
        test_metrics = {
            "total_pnl": 1250.75,
            "sharpe_ratio": 1.2,
            "max_drawdown": -0.08,
            "win_rate": 0.65,
            "strategy_contributions": {"GridStrategy": 500, "TechnicalAnalysisStrategy": 750},
            "correlation_matrix": {"GridStrategy": {"TechnicalAnalysisStrategy": 0.3}}
        }
        
        record_id = await supabase_service.store_portfolio_metrics(test_metrics)
        if record_id:
            print(f"✅ Portfolio metrics stored with ID: {record_id}")
        else:
            print("⚠️ Portfolio metrics stored in mock")
        
        print("🔄 About to return from test_supabase_service...")    
        return True
        
    except Exception as e:
        print(f"❌ Supabase service test failed: {e}")
        return False

async def test_real_time_analytics():
    """Test real-time analytics service."""
    print("\n📊 Testing Real-time Analytics Service...")
    print("🔄 Starting real-time analytics test...")
    
    try:
        from app.services.mcp.supabase_service import SupabaseService
        from app.services.mcp.supabase_realtime_analytics import SupabaseRealTimeAnalytics
        
        # Initialize services
        supabase_service = SupabaseService()
        analytics = SupabaseRealTimeAnalytics(supabase_service)
        
        print("✅ Real-time analytics service initialized")
        
        # Test dashboard summary (with explicit timeout)
        try:
            summary = await asyncio.wait_for(analytics.get_dashboard_summary(), timeout=5.0)
            print(f"📈 Dashboard summary: {summary['status']}")
        except asyncio.TimeoutError:
            print("📈 Dashboard summary: timeout (using fallback)")
            summary = {"status": "no_data"}
        
        # Test alert rules
        print(f"⚠️ Alert rules configured: {len(analytics.alert_rules)}")
        
        # Test subscription mechanism
        print("🔄 Setting up test callback...")
        def test_callback(data):
            print(f"📢 Callback received: {type(data)}")
        
        print("🔄 Calling subscribe_to_updates...")
        analytics.subscribe_to_updates("test_event", "test_subscriber", test_callback)
        print("✅ Subscription mechanism working")
        
        print("🔄 Returning from test_real_time_analytics...")
        return True
        
    except Exception as e:
        print(f"❌ Real-time analytics test failed: {e}")
        return False

async def test_websocket_integration():
    """Test WebSocket integration components."""
    print("\n🔌 Testing WebSocket Integration...")
    
    try:
        from app.dashboard.api.analytics_websocket import get_analytics_status, is_analytics_running
        
        # Test status functions
        print(f"🔍 Analytics running: {is_analytics_running()}")
        
        status = await get_analytics_status()
        print(f"📊 Analytics status: {status}")
        
        print("✅ WebSocket integration components working")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket integration test failed: {e}")
        return False

async def test_api_endpoints():
    """Test analytics API endpoints (mock test)."""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        # Import the router to verify it loads correctly
        from app.dashboard.api_router import router
        from app.dashboard.api.analytics_websocket import router as analytics_router
        
        print("✅ API routers imported successfully")
        
        # Check that analytics endpoints are available
        analytics_routes = [route for route in router.routes if hasattr(route, 'path') and '/analytics' in route.path]
        print(f"📡 Analytics API endpoints found: {len(analytics_routes)}")
        
        for route in analytics_routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                print(f"  - {list(route.methods)[0]} {route.path}")
        
        print("✅ API endpoints configured correctly")
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test failed: {e}")
        return False

async def test_dashboard_integration():
    """Test dashboard main.py integration."""
    print("\n🎛️ Testing Dashboard Integration...")
    
    try:
        # Test importing the main dashboard app
        from app.dashboard.main import app
        
        print("✅ Dashboard app imported successfully")
        
        # Check if analytics router is included
        router_prefixes = []
        for route in app.routes:
            if hasattr(route, 'path_regex'):
                router_prefixes.append(str(route.path_regex.pattern))
        
        analytics_routes_found = any('/analytics' in pattern for pattern in router_prefixes)
        websocket_routes_found = any('/ws' in pattern for pattern in router_prefixes)
        
        if analytics_routes_found:
            print("✅ Analytics routes integrated into dashboard")
        else:
            print("⚠️ Analytics routes may not be properly integrated")
            
        if websocket_routes_found:
            print("✅ WebSocket routes available")
        else:
            print("⚠️ WebSocket routes may not be available")
        
        print("✅ Dashboard integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ Dashboard integration test failed: {e}")
        return False

async def test_performance_metrics():
    """Test performance metrics calculation."""
    print("\n⚡ Testing Performance Metrics...")
    
    try:
        from app.services.mcp.supabase_realtime_analytics import RealTimeMetrics
        from datetime import datetime
        
        # Create test metrics
        test_metrics = RealTimeMetrics(
            timestamp=datetime.now(),
            portfolio_value=105000.0,
            total_pnl=5000.0,
            hourly_pnl=250.0,
            daily_pnl=1200.0,
            sharpe_ratio=1.5,
            max_drawdown=-0.12,
            win_rate=0.68,
            active_positions=3,
            cache_hit_rate=0.87,
            avg_execution_time_ms=145.0,
            strategy_weights={"GridStrategy": 0.35, "TechnicalAnalysisStrategy": 0.40, "TrendFollowingStrategy": 0.25},
            strategy_pnl={"GridStrategy": 1800.0, "TechnicalAnalysisStrategy": 2200.0, "TrendFollowingStrategy": 1000.0},
            correlation_matrix={"GridStrategy": {"TechnicalAnalysisStrategy": 0.25}},
            alerts_count=2,
            market_regime="normal"
        )
        
        print("✅ RealTimeMetrics dataclass working")
        print(f"📊 Portfolio Value: ${test_metrics.portfolio_value:,.2f}")
        print(f"💰 Total PnL: ${test_metrics.total_pnl:,.2f}")
        print(f"📈 Sharpe Ratio: {test_metrics.sharpe_ratio:.2f}")
        print(f"⚡ Execution Time: {test_metrics.avg_execution_time_ms:.1f}ms")
        print(f"🎯 Cache Hit Rate: {test_metrics.cache_hit_rate:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance metrics test failed: {e}")
        return False

async def main():
    """Run all integration tests."""
    print("🚀 Starting Real-time Analytics Integration Tests")
    print("=" * 60)
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Supabase Service", test_supabase_service),
        ("Real-time Analytics", test_real_time_analytics),
        ("WebSocket Integration", test_websocket_integration),
        ("API Endpoints", test_api_endpoints),
        ("Dashboard Integration", test_dashboard_integration),
        ("Performance Metrics", test_performance_metrics)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(test_results)} tests, {passed} passed, {failed} failed")
    
    if failed == 0:
        print("\n🎉 All tests passed! Real-time analytics integration is ready.")
        print("\n🚀 Next steps:")
        print("  1. Start the dashboard: python app/dashboard/main.py")
        print("  2. Connect to WebSocket endpoints:")
        print("     - ws://localhost:8000/ws/analytics")
        print("     - ws://localhost:8000/ws/ensemble")
        print("  3. Access analytics API endpoints:")
        print("     - GET /api/analytics/status")
        print("     - GET /api/analytics/summary")
        print("     - GET /api/analytics/performance")
        print("     - GET /api/analytics/alerts")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Check the output above for details.")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Tests failed with error: {e}")
        sys.exit(1)