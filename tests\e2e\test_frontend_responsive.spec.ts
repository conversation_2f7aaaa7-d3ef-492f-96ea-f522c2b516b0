import { test, expect } from '@playwright/test';

test.describe('Responsive Design Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*\/trading/);
  });

  test('should adapt layout for desktop screens', async ({ page }) => {
    // Test large desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Navigate to ML page where sidebar is visible
    await page.goto('http://localhost:3000/ml');
    await page.waitForTimeout(1000);
    
    // On desktop, sidebar should be visible
    await expect(page.getByRole('link', { name: 'Trading Control' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'ML Optimization' })).toBeVisible();
    
    // Main content should be properly sized
    await expect(page.locator('h1')).toContainText('ML Weight Optimization');
    
    // Form elements should be properly arranged
    const symbolInput = page.getByRole('textbox', { name: 'Symbol' });
    const timeframeInput = page.getByRole('textbox', { name: 'Timeframe' });
    
    if (await symbolInput.isVisible() && await timeframeInput.isVisible()) {
      const symbolBox = await symbolInput.boundingBox();
      const timeframeBox = await timeframeInput.boundingBox();
      
      // On desktop, inputs should be side by side or properly spaced
      if (symbolBox && timeframeBox) {
        expect(symbolBox.width).toBeGreaterThan(200);
        expect(timeframeBox.width).toBeGreaterThan(200);
      }
    }
  });

  test('should adapt layout for tablet screens', async ({ page }) => {
    // Test tablet size
    await page.setViewportSize({ width: 768, height: 1024 });
    
    await page.goto('http://localhost:3000/ml');
    await page.waitForTimeout(1000);
    
    // Content should still be readable and functional
    await expect(page.locator('h1')).toContainText('ML Weight Optimization');
    
    // Navigation should still be accessible (might be collapsed)
    const navigationLinks = page.getByRole('link', { name: 'Trading Control' });
    if (await navigationLinks.isVisible()) {
      await expect(navigationLinks).toBeVisible();
    }
    
    // Forms should adapt to tablet width
    await expect(page.getByRole('button', { name: 'Train Model' })).toBeVisible();
  });

  test('should adapt layout for mobile screens', async ({ page }) => {
    // Test mobile size
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Test login page on mobile
    await page.goto('http://localhost:3000/login');
    await expect(page.locator('h1')).toContainText('Crypto Trading Bot');
    await expect(page.getByRole('button', { name: 'Login' })).toBeVisible();
    
    // Login form should be mobile-friendly
    const usernameInput = page.getByRole('textbox', { name: 'Username' });
    const loginButton = page.getByRole('button', { name: 'Login' });
    
    await expect(usernameInput).toBeVisible();
    await expect(loginButton).toBeVisible();
    
    // Inputs should be appropriately sized for mobile
    const inputBox = await usernameInput.boundingBox();
    const buttonBox = await loginButton.boundingBox();
    
    if (inputBox && buttonBox) {
      expect(inputBox.width).toBeGreaterThan(200); // Should take reasonable width
      expect(buttonBox.width).toBeGreaterThan(100);
    }
  });

  test('should maintain functionality across screen sizes', async ({ page }) => {
    const sizes = [
      { width: 1400, height: 900, name: 'desktop' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const size of sizes) {
      await page.setViewportSize({ width: size.width, height: size.height });
      
      // Test login functionality at each size
      await page.goto('http://localhost:3000/login');
      await page.getByRole('button', { name: 'Login' }).click();
      await expect(page).toHaveURL(/.*\/trading/);
      
      // Core functionality should work
      await page.waitForTimeout(1000);
      
      // Trading control should be visible and functional
      await expect(page.locator('h1')).toContainText('Automated Trading Control');
      
      // Auto trading toggle should be accessible
      const autoTradingToggle = page.getByRole('checkbox', { name: /Auto Trading/ });
      if (await autoTradingToggle.isVisible()) {
        await expect(autoTradingToggle).toBeVisible();
        
        // Should be able to interact with it
        const isChecked = await autoTradingToggle.isChecked();
        await autoTradingToggle.click();
        await expect(autoTradingToggle).toBeChecked(!isChecked);
      }
    }
  });

  test('should handle navigation on mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Test direct URL navigation on mobile
    await page.goto('http://localhost:3000/ml');
    await expect(page.locator('h1')).toContainText('ML Weight Optimization');
    
    await page.goto('http://localhost:3000/reports');
    await expect(page.locator('h4')).toContainText('Session Reports & Analytics');
    
    await page.goto('http://localhost:3000/binance');
    await expect(page.locator('h1')).toContainText('Binance Futures Account');
    
    // Each page should be usable on mobile
    await page.goto('http://localhost:3000/trading');
    await expect(page.locator('h1')).toContainText('Automated Trading Control');
  });

  test('should display tables appropriately on different screen sizes', async ({ page }) => {
    await page.goto('http://localhost:3000/trading');
    await page.waitForTimeout(2000);
    
    const sizes = [
      { width: 1400, height: 900 },
      { width: 768, height: 1024 },
      { width: 375, height: 667 }
    ];

    for (const size of sizes) {
      await page.setViewportSize({ width: size.width, height: size.height });
      await page.waitForTimeout(500);
      
      // Tables should be visible and accessible
      await expect(page.getByText('Active Trades')).toBeVisible();
      await expect(page.getByText('Recent Trades')).toBeVisible();
      
      // Table headers should be visible
      await expect(page.getByRole('columnheader', { name: 'Symbol' })).toBeVisible();
      await expect(page.getByRole('columnheader', { name: 'Status' })).toBeVisible();
      
      // Tables might scroll horizontally on small screens, which is acceptable
    }
  });
});