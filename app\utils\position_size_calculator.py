"""
Advanced Position Size Calculator with Kelly Criterion and Cross-Exchange Validation
Implements multi-source data validation, correlation-based adjustments, and risk limits
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import redis.asyncio as redis
import logging

from app.utils.volatility_calculator import VolatilityCalculator, VolatilityMetrics

logger = logging.getLogger(__name__)

@dataclass
class KellyStats:
    """Kelly Criterion statistics with validation"""
    win_rate: float
    avg_win: float
    avg_loss: float
    kelly_fraction: float
    confidence: float
    data_sources: List[str]
    sample_size: int
    validation_score: float

@dataclass
class RiskLimits:
    """Comprehensive risk limits for position sizing"""
    max_position_size: float
    max_portfolio_risk: float
    max_correlation_exposure: float
    max_drawdown_limit: float
    max_leverage: float
    min_position_size: float

@dataclass
class PositionSizeResult:
    """Complete position sizing result with breakdown"""
    final_size: float
    base_kelly_size: float
    volatility_adjusted_size: float
    correlation_adjusted_size: float
    risk_limited_size: float
    confidence_score: float
    risk_metrics: Dict[str, float]
    adjustments_applied: List[str]

class PositionSizeCalculator:
    """
    Advanced position size calculator with multi-source validation
    """
    
    def __init__(
        self,
        redis_client: redis.Redis,
        volatility_calculator: VolatilityCalculator,
        default_kelly_fraction: float = 0.1,
        max_kelly_fraction: float = 0.25,
        min_sample_size: int = 30
    ):
        self.redis = redis_client
        self.volatility_calculator = volatility_calculator
        self.default_kelly_fraction = default_kelly_fraction
        self.max_kelly_fraction = max_kelly_fraction
        self.min_sample_size = min_sample_size
        
        # Cache keys
        self.KELLY_CACHE_KEY = "position:kelly_stats"
        self.CORRELATION_CACHE_KEY = "position:correlation"
        self.TRADE_HISTORY_KEY = "position:trade_history"
        
    async def calculate_optimal_position_size(
        self,
        strategy_name: str,
        symbol: str,
        signal_confidence: float,
        current_portfolio: Dict[str, float],
        risk_limits: RiskLimits,
        market_data: Optional[Dict] = None
    ) -> PositionSizeResult:
        """
        Calculate optimal position size using comprehensive analysis
        """
        try:
            # Track adjustments applied
            adjustments_applied = []
            
            # 1. Get Kelly statistics with cross-validation
            kelly_stats = await self.calculate_kelly_size_cross_validated(strategy_name, symbol)
            
            # 2. Calculate base Kelly size
            base_size = kelly_stats.kelly_fraction * signal_confidence
            
            # 3. Apply volatility adjustment
            volatility_adjusted_size = await self._apply_volatility_adjustment(
                base_size, symbol, market_data
            )
            if volatility_adjusted_size != base_size:
                adjustments_applied.append("volatility_adjustment")
            
            # 4. Apply correlation discount
            correlation_adjusted_size = await self._apply_correlation_discount(
                volatility_adjusted_size, strategy_name, current_portfolio
            )
            if correlation_adjusted_size != volatility_adjusted_size:
                adjustments_applied.append("correlation_discount")
            
            # 5. Apply risk limits
            risk_limited_size = self._apply_risk_limits(
                correlation_adjusted_size, risk_limits, current_portfolio
            )
            if risk_limited_size != correlation_adjusted_size:
                adjustments_applied.append("risk_limits")
            
            # 6. Calculate confidence score
            confidence_score = self._calculate_overall_confidence(
                kelly_stats, signal_confidence, current_portfolio
            )
            
            # 7. Calculate risk metrics
            risk_metrics = await self._calculate_risk_metrics(
                risk_limited_size, symbol, current_portfolio, risk_limits
            )
            
            # 8. Create result
            result = PositionSizeResult(
                final_size=risk_limited_size,
                base_kelly_size=base_size,
                volatility_adjusted_size=volatility_adjusted_size,
                correlation_adjusted_size=correlation_adjusted_size,
                risk_limited_size=risk_limited_size,
                confidence_score=confidence_score,
                risk_metrics=risk_metrics,
                adjustments_applied=adjustments_applied
            )
            
            # 9. Cache the calculation for audit trail
            await self._cache_position_calculation(strategy_name, symbol, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating position size for {strategy_name}/{symbol}: {e}")
            return self._get_default_position_size(risk_limits)
    
    async def calculate_kelly_size_cross_validated(
        self,
        strategy_name: str,
        symbol: str
    ) -> KellyStats:
        """
        Calculate Kelly Criterion with cross-exchange data validation
        """
        try:
            # Check cache first
            cache_key = f"{self.KELLY_CACHE_KEY}:{strategy_name}:{symbol}"
            cached_stats = await self.redis.get(cache_key)
            
            if cached_stats:
                return KellyStats(**json.loads(cached_stats))
            
            # Get trade history from multiple sources
            trade_data = await self._get_cross_validated_trade_data(strategy_name, symbol)
            
            if len(trade_data) < self.min_sample_size:
                # Not enough data for reliable Kelly calculation
                return self._get_default_kelly_stats()
            
            # Calculate Kelly statistics
            kelly_stats = self._calculate_kelly_from_trades(trade_data)
            
            # Validate and adjust Kelly fraction
            kelly_stats.kelly_fraction = min(
                kelly_stats.kelly_fraction,
                self.max_kelly_fraction
            )
            kelly_stats.kelly_fraction = max(
                kelly_stats.kelly_fraction,
                0.01  # Minimum 1%
            )
            
            # Cache for 1 hour
            await self.redis.setex(
                cache_key,
                3600,
                json.dumps(asdict(kelly_stats))
            )
            
            return kelly_stats
            
        except Exception as e:
            logger.error(f"Error calculating Kelly stats: {e}")
            return self._get_default_kelly_stats()
    
    def _calculate_kelly_from_trades(self, trade_data: List[Dict]) -> KellyStats:
        """Calculate Kelly statistics from trade data"""
        returns = [trade['return_pct'] for trade in trade_data]
        
        wins = [r for r in returns if r > 0]
        losses = [r for r in returns if r < 0]
        
        if not wins:
            win_rate = 0.0
            avg_win = 0.0
        else:
            win_rate = len(wins) / len(returns)
            avg_win = np.mean(wins)
        
        if not losses:
            avg_loss = 0.01  # Small default to avoid division by zero
        else:
            avg_loss = abs(np.mean(losses))
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        if avg_loss > 0:
            b = avg_win / avg_loss
            kelly_fraction = (b * win_rate - (1 - win_rate)) / b
            kelly_fraction = max(0, kelly_fraction)  # Kelly can't be negative
        else:
            kelly_fraction = self.default_kelly_fraction
        
        # Calculate confidence based on sample size and consistency
        confidence = self._calculate_kelly_confidence(returns)
        
        # Validation score based on data quality
        validation_score = self._calculate_validation_score(trade_data)
        
        return KellyStats(
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            kelly_fraction=kelly_fraction,
            confidence=confidence,
            data_sources=['binance', 'internal'],  # Placeholder
            sample_size=len(trade_data),
            validation_score=validation_score
        )
    
    def _calculate_kelly_confidence(self, returns: List[float]) -> float:
        """Calculate confidence in Kelly calculation"""
        n = len(returns)
        
        # Base confidence on sample size
        size_confidence = min(1.0, n / 100)
        
        # Adjust for return consistency (lower std = higher confidence)
        if len(returns) > 1:
            return_std = np.std(returns)
            consistency_confidence = 1.0 / (1.0 + return_std * 10)
        else:
            consistency_confidence = 0.3
        
        return (size_confidence * 0.7 + consistency_confidence * 0.3)
    
    def _calculate_validation_score(self, trade_data: List[Dict]) -> float:
        """Calculate data validation score"""
        if not trade_data:
            return 0.0
        
        # Check data completeness
        required_fields = ['return_pct', 'timestamp', 'strategy_name']
        complete_trades = [
            trade for trade in trade_data
            if all(field in trade and trade[field] is not None for field in required_fields)
        ]
        
        completeness_score = len(complete_trades) / len(trade_data)
        
        # Check data recency (more recent = higher score)
        if complete_trades:
            latest_trade = max(
                complete_trades,
                key=lambda x: datetime.fromisoformat(x['timestamp']) if isinstance(x['timestamp'], str) else x['timestamp']
            )
            
            if isinstance(latest_trade['timestamp'], str):
                latest_time = datetime.fromisoformat(latest_trade['timestamp'])
            else:
                latest_time = latest_trade['timestamp']
            
            days_old = (datetime.now() - latest_time).days
            recency_score = max(0, 1.0 - days_old / 30)  # Decay over 30 days
        else:
            recency_score = 0.0
        
        return (completeness_score * 0.6 + recency_score * 0.4)
    
    async def _apply_volatility_adjustment(
        self,
        base_size: float,
        symbol: str,
        market_data: Optional[Dict] = None
    ) -> float:
        """Apply volatility-based position size adjustment"""
        try:
            volatility_scaling = await self.volatility_calculator.get_volatility_scaling_factor(
                symbol, target_volatility=0.20
            )
            
            return base_size * volatility_scaling
            
        except Exception as e:
            logger.error(f"Error applying volatility adjustment: {e}")
            return base_size
    
    async def _apply_correlation_discount(
        self,
        size: float,
        strategy_name: str,
        current_portfolio: Dict[str, float]
    ) -> float:
        """Apply correlation-based position size discount"""
        try:
            if not current_portfolio:
                return size
            
            # Get correlation matrix from cache
            correlation_data = await self.redis.get(self.CORRELATION_CACHE_KEY)
            if not correlation_data:
                correlation_matrix = await self._calculate_strategy_correlations()
                await self.redis.setex(
                    self.CORRELATION_CACHE_KEY,
                    1800,  # 30 minutes
                    json.dumps(correlation_matrix)
                )
            else:
                correlation_matrix = json.loads(correlation_data)
            
            # Calculate correlation penalty
            total_correlation = 0
            active_strategies = 0
            
            for other_strategy, position in current_portfolio.items():
                if other_strategy != strategy_name and position > 0:
                    correlation = correlation_matrix.get(strategy_name, {}).get(other_strategy, 0)
                    position_weight = position / sum(current_portfolio.values())
                    total_correlation += abs(correlation) * position_weight
                    active_strategies += 1
            
            # Apply correlation discount (higher correlation = smaller additional position)
            if active_strategies > 0:
                avg_correlation = total_correlation / active_strategies
                correlation_discount = 1.0 - (avg_correlation * 0.5)  # Max 50% discount
            else:
                correlation_discount = 1.0
            
            return size * correlation_discount
            
        except Exception as e:
            logger.error(f"Error applying correlation discount: {e}")
            return size
    
    def _apply_risk_limits(
        self,
        size: float,
        limits: RiskLimits,
        current_portfolio: Dict[str, float]
    ) -> float:
        """Apply risk limits to position size"""
        
        # Individual position size limit
        size = min(size, limits.max_position_size)
        
        # Portfolio risk limit
        current_portfolio_risk = sum(current_portfolio.values())
        available_risk = limits.max_portfolio_risk - current_portfolio_risk
        size = min(size, available_risk)
        
        # Minimum position size
        if size > 0:
            size = max(size, limits.min_position_size)
        
        # Ensure positive size
        size = max(0, size)
        
        return size
    
    def _calculate_overall_confidence(
        self,
        kelly_stats: KellyStats,
        signal_confidence: float,
        current_portfolio: Dict[str, float]
    ) -> float:
        """Calculate overall confidence in position size"""
        
        # Base confidence on Kelly statistics
        kelly_confidence = kelly_stats.confidence
        
        # Adjust for signal confidence
        signal_weight = 0.4
        
        # Adjust for portfolio concentration
        portfolio_size = len(current_portfolio)
        concentration_factor = min(1.0, portfolio_size / 5)  # Prefer diversification
        
        overall_confidence = (
            kelly_confidence * 0.5 +
            signal_confidence * signal_weight +
            concentration_factor * 0.1
        )
        
        return min(1.0, overall_confidence)
    
    async def _calculate_risk_metrics(
        self,
        position_size: float,
        symbol: str,
        current_portfolio: Dict[str, float],
        risk_limits: RiskLimits
    ) -> Dict[str, float]:
        """Calculate comprehensive risk metrics"""
        try:
            # Get volatility metrics
            vol_metrics = await self.volatility_calculator.calculate_comprehensive_volatility(symbol)
            
            # Portfolio utilization
            portfolio_utilization = sum(current_portfolio.values()) / risk_limits.max_portfolio_risk
            
            # Position concentration
            total_positions = sum(current_portfolio.values()) + position_size
            position_concentration = position_size / total_positions if total_positions > 0 else 0
            
            # Risk-adjusted return estimate
            estimated_return = position_size * 0.1  # Placeholder expected return
            risk_adjusted_return = estimated_return / vol_metrics.ewma_volatility if vol_metrics.ewma_volatility > 0 else 0
            
            return {
                'portfolio_utilization': portfolio_utilization,
                'position_concentration': position_concentration,
                'volatility_estimate': vol_metrics.ewma_volatility,
                'confidence_level': vol_metrics.confidence_level,
                'risk_adjusted_return': risk_adjusted_return,
                'kelly_fraction_used': position_size / max(0.01, vol_metrics.ewma_volatility),
                'leverage_ratio': position_size / 0.1,  # Assuming 10% base capital
                'risk_limit_utilization': position_size / risk_limits.max_position_size
            }
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {
                'portfolio_utilization': 0.0,
                'position_concentration': 0.0,
                'volatility_estimate': 0.25,
                'confidence_level': 0.5,
                'risk_adjusted_return': 0.0,
                'kelly_fraction_used': 0.0,
                'leverage_ratio': 1.0,
                'risk_limit_utilization': 0.0
            }
    
    async def _get_cross_validated_trade_data(
        self,
        strategy_name: str,
        symbol: str
    ) -> List[Dict]:
        """Get trade data with cross-validation"""
        try:
            # Try to get from cache first
            cache_key = f"{self.TRADE_HISTORY_KEY}:{strategy_name}:{symbol}"
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                return json.loads(cached_data)
            
            # Generate synthetic trade data for testing
            # In production, this would fetch from Supabase and cross-validate with exchanges
            np.random.seed(42)
            trade_data = []
            
            for i in range(50):  # Generate 50 synthetic trades
                return_pct = np.random.normal(0.01, 0.05)  # 1% mean, 5% std
                trade_data.append({
                    'id': f"trade_{i}",
                    'strategy_name': strategy_name,
                    'symbol': symbol,
                    'return_pct': return_pct,
                    'timestamp': (datetime.now() - timedelta(days=i)).isoformat(),
                    'validated': True
                })
            
            # Cache for 30 minutes
            await self.redis.setex(cache_key, 1800, json.dumps(trade_data))
            
            return trade_data
            
        except Exception as e:
            logger.error(f"Error getting cross-validated trade data: {e}")
            return []
    
    async def _calculate_strategy_correlations(self) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between strategies"""
        try:
            # Placeholder correlation matrix
            # In production, calculate from actual strategy returns
            strategies = ['GridStrategy', 'TechnicalAnalysisStrategy', 'TrendFollowingStrategy']
            correlation_matrix = {}
            
            for strategy1 in strategies:
                correlation_matrix[strategy1] = {}
                for strategy2 in strategies:
                    if strategy1 == strategy2:
                        correlation_matrix[strategy1][strategy2] = 1.0
                    else:
                        # Use realistic correlation values
                        correlation_matrix[strategy1][strategy2] = np.random.uniform(0.1, 0.4)
            
            return correlation_matrix
            
        except Exception as e:
            logger.error(f"Error calculating strategy correlations: {e}")
            return {}
    
    async def _cache_position_calculation(
        self,
        strategy_name: str,
        symbol: str,
        result: PositionSizeResult
    ):
        """Cache position calculation for audit trail"""
        try:
            cache_key = f"position:calculation:{strategy_name}:{symbol}:{int(datetime.now().timestamp())}"
            cache_data = {
                'strategy_name': strategy_name,
                'symbol': symbol,
                'result': asdict(result),
                'timestamp': datetime.now().isoformat()
            }
            
            await self.redis.setex(
                cache_key,
                3600,  # 1 hour
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.error(f"Error caching position calculation: {e}")
    
    def _get_default_kelly_stats(self) -> KellyStats:
        """Return default Kelly statistics when calculation fails"""
        return KellyStats(
            win_rate=0.5,
            avg_win=0.02,
            avg_loss=0.02,
            kelly_fraction=self.default_kelly_fraction,
            confidence=0.3,
            data_sources=['default'],
            sample_size=0,
            validation_score=0.0
        )
    
    def _get_default_position_size(self, risk_limits: RiskLimits) -> PositionSizeResult:
        """Return default position size when calculation fails"""
        default_size = min(0.05, risk_limits.max_position_size)  # 5% or max allowed
        
        return PositionSizeResult(
            final_size=default_size,
            base_kelly_size=default_size,
            volatility_adjusted_size=default_size,
            correlation_adjusted_size=default_size,
            risk_limited_size=default_size,
            confidence_score=0.3,
            risk_metrics={
                'portfolio_utilization': 0.0,
                'position_concentration': 1.0,
                'volatility_estimate': 0.25,
                'confidence_level': 0.3,
                'risk_adjusted_return': 0.0,
                'kelly_fraction_used': 0.1,
                'leverage_ratio': 1.0,
                'risk_limit_utilization': default_size / risk_limits.max_position_size
            },
            adjustments_applied=['default_fallback']
        )