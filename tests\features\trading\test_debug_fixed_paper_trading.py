#!/usr/bin/env python3
"""
Debug Fixed Paper Trading Manager
Identify what's causing the hang in the fixed version.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def debug_fixed_paper_trading():
    """Debug fixed paper trading step by step"""
    
    try:
        logger.info("Step 1: Importing fixed manager...")
        from app.strategies.fixed_paper_trading_portfolio_manager import FixedPaperTradingPortfolioManager
        logger.info("✅ Import successful")
        
        logger.info("Step 2: Creating manager instance...")
        manager = FixedPaperTradingPortfolioManager(100000.0)
        logger.info("✅ Manager created")
        
        logger.info("Step 3: Testing portfolio summary...")
        summary = await manager.get_portfolio_summary()
        logger.info(f"✅ Portfolio summary: {summary['account']['current_value']}")
        
        logger.info("Step 4: Creating simple market data...")
        from dataclasses import dataclass
        
        @dataclass
        class SimpleMarketData:
            symbol: str
            price: float
            volume: float
            timestamp: datetime
            bid: float
            ask: float
            high_24h: float
            low_24h: float
            volatility: float
        
        market_data = SimpleMarketData(
            symbol="BTCUSDT",
            price=50000.0,
            volume=1000000.0,
            timestamp=datetime.now(),
            bid=49999.0,
            ask=50001.0,
            high_24h=51000.0,
            low_24h=49000.0,
            volatility=0.02
        )
        logger.info("✅ Market data created")
        
        logger.info("Step 5: Testing small trade execution...")
        buy_order = await manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="BUY",
            quantity=0.01,  # Very small quantity
            order_type="MARKET",
            market_data=market_data
        )
        
        if buy_order:
            logger.info(f"✅ Trade executed: {buy_order.status}")
        else:
            logger.warning("⚠️ Trade was not executed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Debug test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main debug function"""
    print("=" * 60)
    print("DEBUG FIXED PAPER TRADING MANAGER")
    print("=" * 60)
    
    success = await debug_fixed_paper_trading()
    
    if success:
        print("\n✅ DEBUG TEST SUCCESSFUL")
    else:
        print("\n❌ DEBUG TEST FAILED")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)