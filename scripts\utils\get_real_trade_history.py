#!/usr/bin/env python3
"""
Get Real Trade History from Binance
Fetch actual executed trades with real prices and fees.
"""

import asyncio
import os
from dotenv import load_dotenv
from binance.client import Client
from datetime import datetime, timedelta
import json

async def get_real_trade_history():
    """Get real trade history from Binance account"""
    print("=" * 80)
    print("📊 REAL TRADE HISTORY FROM BINANCE ACCOUNT")
    print("=" * 80)
    
    try:
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        client = Client(api_key, api_secret, testnet=True)
        
        # Get recent trades (last 24 hours)
        trades = client.futures_account_trades(symbol='BTCUSDT', limit=50)
        
        print(f"📋 RECENT BTCUSDT FUTURES TRADES:")
        print("-" * 100)
        print(f"{'Time':<20} {'Side':<6} {'Qty':<12} {'Price':<12} {'Quote Qty':<12} {'Fee':<10} {'PnL':<12}")
        print("-" * 100)
        
        total_fee = 0
        total_pnl = 0
        trade_count = 0
        
        for trade in trades:
            # Convert timestamp
            timestamp = datetime.fromtimestamp(int(trade['time']) / 1000)
            time_str = timestamp.strftime('%H:%M:%S')
            
            side = 'BUY' if trade['buyer'] else 'SELL'
            qty = float(trade['qty'])
            price = float(trade['price'])
            quote_qty = float(trade['quoteQty'])
            fee = float(trade['commission'])
            realized_pnl = float(trade['realizedPnl'])
            
            total_fee += fee
            total_pnl += realized_pnl
            trade_count += 1
            
            print(f"{time_str:<20} {side:<6} {qty:<12.6f} {price:<12.2f} {quote_qty:<12.2f} {fee:<10.6f} {realized_pnl:<12.6f}")
        
        print("-" * 100)
        print(f"{'TOTALS:':<20} {'':<6} {'':<12} {'':<12} {'':<12} {total_fee:<10.6f} {total_pnl:<12.6f}")
        
        # Get current account info
        account = client.futures_account()
        current_balance = float(account['availableBalance'])
        
        print(f"\n💰 ACCOUNT SUMMARY:")
        print(f"{'Current Balance:':<25} ${current_balance:.8f} USDT")
        print(f"{'Total Trades:':<25} {trade_count}")
        print(f"{'Total Fees Paid:':<25} ${total_fee:.6f} USDT")
        print(f"{'Total Realized PnL:':<25} ${total_pnl:.6f} USDT")
        print(f"{'Net P&L (after fees):':<25} ${total_pnl - total_fee:.6f} USDT")
        
        # Calculate fee percentage
        total_volume = sum(float(t['quoteQty']) for t in trades)
        fee_percentage = (total_fee / total_volume * 100) if total_volume > 0 else 0
        
        print(f"{'Total Volume Traded:':<25} ${total_volume:.2f} USDT")
        print(f"{'Effective Fee Rate:':<25} {fee_percentage:.4f}%")
        
        # Analyze fee impact
        print(f"\n🔍 FEE ANALYSIS:")
        expected_binance_fee = total_volume * 0.0004  # 0.04% standard futures fee
        print(f"{'Expected Fees (0.04%):':<25} ${expected_binance_fee:.6f} USDT")
        print(f"{'Actual Fees Paid:':<25} ${total_fee:.6f} USDT")
        
        fee_difference = total_fee - expected_binance_fee
        if abs(fee_difference) > 0.000001:
            print(f"{'Fee Difference:':<25} ${fee_difference:.6f} USDT")
        
        # Performance analysis
        print(f"\n📈 PERFORMANCE ANALYSIS:")
        
        if total_pnl > 0:
            print(f"✅ Gross Trading Profit: ${total_pnl:.6f} USDT")
        else:
            print(f"📉 Gross Trading Loss: ${abs(total_pnl):.6f} USDT")
        
        net_result = total_pnl - total_fee
        if net_result > 0:
            print(f"✅ Net Result (after fees): +${net_result:.6f} USDT")
        else:
            print(f"📉 Net Result (after fees): -${abs(net_result):.6f} USDT")
        
        # Return data for further analysis
        return {
            'trades': trades,
            'total_fee': total_fee,
            'total_pnl': total_pnl,
            'net_result': net_result,
            'trade_count': trade_count,
            'current_balance': current_balance
        }
        
    except Exception as e:
        print(f"❌ Error getting trade history: {e}")
        return None

async def main():
    """Main function"""
    trade_data = await get_real_trade_history()
    
    if trade_data:
        # Save real trade data
        with open('real_trade_history.json', 'w') as f:
            json.dump(trade_data, f, indent=2, default=str)
        
        print(f"\n💾 Real trade data saved to: real_trade_history.json")
        
        # Final analysis
        print("\n" + "=" * 80)
        print("🎯 CORRECTED ENSEMBLE TRADING ANALYSIS")
        print("=" * 80)
        print(f"✅ Trades Executed: {trade_data['trade_count']}")
        print(f"📊 Gross PnL: ${trade_data['total_pnl']:.6f} USDT")
        print(f"💸 Trading Fees: ${trade_data['total_fee']:.6f} USDT")
        print(f"🎯 Net Result: ${trade_data['net_result']:.6f} USDT")
        
        if trade_data['net_result'] < 0:
            loss_percentage = abs(trade_data['net_result']) / 14951.80 * 100
            print(f"📉 Net Loss: {loss_percentage:.4f}% of starting capital")
        
        print("\n💡 KEY INSIGHT: Trading fees significantly impact small position profitability")
        print("   Recommendation: Increase position sizes to overcome fee drag")

if __name__ == "__main__":
    asyncio.run(main())