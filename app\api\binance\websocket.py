"""Websocket methods for Binance API client."""
from typing import Dict, List, Optional, Any, Callable
import logging
from collections import OrderedDict

logger = logging.getLogger(__name__)

class WebsocketMixin:
    """Websocket methods for Binance API client."""

    def _make_request(self, *args, **kwargs):
        """This method should be implemented by the concrete client class.

        In tests, this method will be mocked.
        """
        raise NotImplementedError("_make_request must be implemented by the concrete client class")

    def get_listen_key(self) -> str:
        """Get a listen key for user data stream.

        Returns:
            str: Listen key
        """
        response = self._make_request('POST', '/fapi/v1/listenKey', signed=True)

        if isinstance(response, dict) and 'listenKey' in response:
            return response['listenKey']
        else:
            logger.error(f"Unexpected response format from listenKey endpoint: {response}")
            raise ValueError("Failed to get listen key")

    def keep_alive_listen_key(self, listen_key: str) -> Dict[str, Any]:
        """Keep a listen key alive.

        Args:
            listen_key (str): Listen key to keep alive

        Returns:
            Dict[str, Any]: Response
        """
        params = OrderedDict()
        params['listenKey'] = listen_key

        return self._make_request('PUT', '/fapi/v1/listenKey', signed=True, params=params)

    def close_listen_key(self, listen_key: str) -> Dict[str, Any]:
        """Close a listen key.

        Args:
            listen_key (str): Listen key to close

        Returns:
            Dict[str, Any]: Response
        """
        params = OrderedDict()
        params['listenKey'] = listen_key

        return self._make_request('DELETE', '/fapi/v1/listenKey', signed=True, params=params)

    def get_socket_manager(self):
        """Get a socket manager for websocket connections.

        This method should be implemented by the concrete client class.

        Returns:
            Any: Socket manager instance
        """
        raise NotImplementedError("get_socket_manager must be implemented by the concrete client class")
