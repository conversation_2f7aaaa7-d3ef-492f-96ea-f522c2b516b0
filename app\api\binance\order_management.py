"""Order management methods for Binance API client."""
from typing import Dict, List, Optional, Any, Union
import logging
from collections import OrderedDict

logger = logging.getLogger(__name__)

class OrderManagementMixin:
    """Order management methods for Binance API client."""

    def _make_request(self, *args, **kwargs):
        """This method should be implemented by the concrete client class.

        In tests, this method will be mocked.
        """
        raise NotImplementedError("_make_request must be implemented by the concrete client class")

    def place_order(self, symbol: str, side: str, order_type: str,
                   quantity: Optional[float] = None,
                   price: Optional[float] = None,
                   stop_price: Optional[float] = None,
                   time_in_force: str = 'GTC',
                   reduce_only: Optional[bool] = None,
                   close_position: Optional[bool] = None,
                   activation_price: Optional[float] = None,
                   callback_rate: Optional[float] = None,
                   working_type: Optional[str] = None,
                   position_side: Optional[str] = None,
                   client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Place an order.

        Args:
            symbol (str): Symbol to place order for
            side (str): Order side (BUY or SELL)
            order_type (str): Order type (LIMIT, MARKET, STOP, etc.)
            quantity (Optional[float], optional): Order quantity. Defaults to None.
            price (Optional[float], optional): Order price. Defaults to None.
            stop_price (Optional[float], optional): Stop price. Defaults to None.
            time_in_force (str, optional): Time in force. Defaults to 'GTC'.
            reduce_only (Optional[bool], optional): Reduce only flag. Defaults to None.
            close_position (Optional[bool], optional): Close position flag. Defaults to None.
            activation_price (Optional[float], optional): Activation price. Defaults to None.
            callback_rate (Optional[float], optional): Callback rate. Defaults to None.
            working_type (Optional[str], optional): Working type. Defaults to None.
            position_side (Optional[str], optional): Position side. Defaults to None.
            client_order_id (Optional[str], optional): Client order ID. Defaults to None.

        Returns:
            Dict[str, Any]: Order information
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['side'] = side
        params['type'] = order_type

        # Add required parameters based on order type
        if order_type != 'MARKET':
            if time_in_force:
                params['timeInForce'] = time_in_force

        if order_type in ['LIMIT', 'STOP', 'TAKE_PROFIT', 'STOP_MARKET', 'TAKE_PROFIT_MARKET']:
            if price:
                params['price'] = price

        if order_type in ['STOP', 'TAKE_PROFIT', 'STOP_MARKET', 'TAKE_PROFIT_MARKET']:
            if stop_price:
                params['stopPrice'] = stop_price

        if order_type in ['TRAILING_STOP_MARKET']:
            if callback_rate:
                params['callbackRate'] = callback_rate

        if quantity:
            params['quantity'] = quantity

        # Add optional parameters
        if reduce_only is not None:
            params['reduceOnly'] = 'true' if reduce_only else 'false'

        if close_position is not None:
            params['closePosition'] = 'true' if close_position else 'false'

        if activation_price:
            params['activationPrice'] = activation_price

        if working_type:
            params['workingType'] = working_type

        if position_side:
            params['positionSide'] = position_side

        if client_order_id:
            params['newClientOrderId'] = client_order_id

        return self._make_request('POST', '/fapi/v1/order', signed=True, params=params)

    def cancel_order(self, symbol: str, order_id: Optional[int] = None,
                    client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Cancel an order.

        Args:
            symbol (str): Symbol to cancel order for
            order_id (Optional[int], optional): Order ID. Defaults to None.
            client_order_id (Optional[str], optional): Client order ID. Defaults to None.

        Returns:
            Dict[str, Any]: Cancellation response
        """
        params = OrderedDict()
        params['symbol'] = symbol

        if order_id:
            params['orderId'] = order_id
        elif client_order_id:
            params['origClientOrderId'] = client_order_id
        else:
            raise ValueError("Either order_id or client_order_id must be provided")

        return self._make_request('DELETE', '/fapi/v1/order', signed=True, params=params)

    def cancel_all_orders(self, symbol: str) -> List[Dict[str, Any]]:
        """Cancel all orders for a symbol.

        Args:
            symbol (str): Symbol to cancel orders for

        Returns:
            List[Dict[str, Any]]: Cancellation responses
        """
        params = OrderedDict()
        params['symbol'] = symbol

        return self._make_request('DELETE', '/fapi/v1/allOpenOrders', signed=True, params=params)

    def get_order(self, symbol: str, order_id: Optional[int] = None,
                 client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Get order information.

        Args:
            symbol (str): Symbol to get order for
            order_id (Optional[int], optional): Order ID. Defaults to None.
            client_order_id (Optional[str], optional): Client order ID. Defaults to None.

        Returns:
            Dict[str, Any]: Order information
        """
        params = OrderedDict()
        params['symbol'] = symbol

        if order_id:
            params['orderId'] = order_id
        elif client_order_id:
            params['origClientOrderId'] = client_order_id
        else:
            raise ValueError("Either order_id or client_order_id must be provided")

        return self._make_request('GET', '/fapi/v1/order', signed=True, params=params)

    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get open orders.

        Args:
            symbol (Optional[str], optional): Symbol to get open orders for. Defaults to None for all symbols.

        Returns:
            List[Dict[str, Any]]: Open orders
        """
        params = OrderedDict()
        if symbol:
            params['symbol'] = symbol

        return self._make_request('GET', '/fapi/v1/openOrders', signed=True, params=params)

    def get_all_orders(self, symbol: str, order_id: Optional[int] = None,
                      start_time: Optional[int] = None,
                      end_time: Optional[int] = None,
                      limit: int = 500) -> List[Dict[str, Any]]:
        """Get all orders.

        Args:
            symbol (str): Symbol to get orders for
            order_id (Optional[int], optional): Order ID. Defaults to None.
            start_time (Optional[int], optional): Start time in ms. Defaults to None.
            end_time (Optional[int], optional): End time in ms. Defaults to None.
            limit (int, optional): Limit of results. Defaults to 500.

        Returns:
            List[Dict[str, Any]]: Orders
        """
        params = OrderedDict()
        params['symbol'] = symbol

        if order_id:
            params['orderId'] = order_id
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time

        params['limit'] = limit

        return self._make_request('GET', '/fapi/v1/allOrders', signed=True, params=params)

    def get_user_trades(self, symbol: str, order_id: Optional[int] = None,
                       start_time: Optional[int] = None,
                       end_time: Optional[int] = None,
                       from_id: Optional[int] = None,
                       limit: int = 500) -> List[Dict[str, Any]]:
        """Get user trades.

        Args:
            symbol (str): Symbol to get trades for
            order_id (Optional[int], optional): Order ID. Defaults to None.
            start_time (Optional[int], optional): Start time in ms. Defaults to None.
            end_time (Optional[int], optional): End time in ms. Defaults to None.
            from_id (Optional[int], optional): Trade ID to fetch from. Defaults to None.
            limit (int, optional): Limit of results. Defaults to 500.

        Returns:
            List[Dict[str, Any]]: User trades
        """
        params = OrderedDict()
        params['symbol'] = symbol

        if order_id:
            params['orderId'] = order_id
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
        if from_id:
            params['fromId'] = from_id

        params['limit'] = limit

        return self._make_request('GET', '/fapi/v1/userTrades', signed=True, params=params)

    def place_batch_orders(self, orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Place batch orders.

        Args:
            orders (List[Dict[str, Any]]): List of order parameters

        Returns:
            List[Dict[str, Any]]: Batch order responses
        """
        params = OrderedDict()
        params['batchOrders'] = orders

        return self._make_request('POST', '/fapi/v1/batchOrders', signed=True, params=params)
