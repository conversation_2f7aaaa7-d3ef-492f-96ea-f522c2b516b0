"""
Weight Allocation Service for Strategy Ensemble System
Provides cached weight allocation with MLflow integration for dynamic model updates.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import numpy as np
import logging

from app.services.mcp.redis_service import RedisService
from app.services.mcp.mlflow_service import MLflowService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)


@dataclass
class WeightAllocation:
    """Strategy weight allocation result."""
    weights: Dict[str, float]
    model_version: str
    confidence: float
    allocation_timestamp: datetime
    market_regime: str
    cache_hit: bool = False
    processing_time_ms: float = 0.0
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class MarketFeatures:
    """Market features for weight prediction."""
    volatility: float
    volume_ratio: float  # Current volume / average volume
    price_momentum: float  # Price change over lookback period
    rsi: float
    bollinger_position: float  # Position within Bollinger bands
    trend_strength: float
    correlation_risk: float  # Average correlation between strategies
    timestamp: datetime
    
    def to_array(self) -> np.ndarray:
        """Convert to numpy array for model input."""
        return np.array([
            self.volatility,
            self.volume_ratio,
            self.price_momentum,
            self.rsi / 100.0,  # Normalize RSI
            self.bollinger_position,
            self.trend_strength,
            self.correlation_risk
        ])
    
    def to_hash(self) -> str:
        """Generate hash for caching."""
        # Round features for cache efficiency
        rounded_features = {
            'volatility': round(self.volatility, 4),
            'volume_ratio': round(self.volume_ratio, 2),
            'price_momentum': round(self.price_momentum, 4),
            'rsi': round(self.rsi, 1),
            'bollinger_position': round(self.bollinger_position, 3),
            'trend_strength': round(self.trend_strength, 3),
            'correlation_risk': round(self.correlation_risk, 3),
            # Use hour-level timestamp for cache efficiency
            'hour': self.timestamp.strftime('%Y-%m-%d-%H')
        }
        
        # Simple hash generation
        import hashlib
        data_str = json.dumps(rounded_features, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()[:16]


class CachedWeightAllocator:
    """
    High-performance weight allocation service with MLflow model integration.
    Uses Redis caching for sub-second weight allocation responses.
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        mlflow_service: MLflowService,
        supabase_service: SupabaseService = None,
        cache_ttl: int = 300  # 5 minutes
    ):
        """
        Initialize weight allocator.
        
        Args:
            redis_service: Redis caching service
            mlflow_service: MLflow model management
            supabase_service: Optional Supabase for analytics
            cache_ttl: Cache TTL in seconds
        """
        self.redis = redis_service
        self.mlflow = mlflow_service
        self.supabase = supabase_service
        self.cache_ttl = cache_ttl
        
        # Performance tracking
        self.allocation_times = []
        self.cache_hits = 0
        self.cache_misses = 0
        self.model_load_times = []
        
        # Model management
        self.current_model = None
        self.current_model_version = None
        self.model_last_updated = None
        self.model_refresh_interval = timedelta(hours=1)
        
        # Default weights (fallback)
        self.default_weights = {
            'GridStrategy': 0.33,
            'TechnicalAnalysisStrategy': 0.33,
            'TrendFollowingStrategy': 0.34
        }
        
        # Market regime mapping
        self.regime_weights = {
            'trending': {'GridStrategy': 0.2, 'TechnicalAnalysisStrategy': 0.4, 'TrendFollowingStrategy': 0.4},
            'ranging': {'GridStrategy': 0.6, 'TechnicalAnalysisStrategy': 0.3, 'TrendFollowingStrategy': 0.1},
            'volatile': {'GridStrategy': 0.4, 'TechnicalAnalysisStrategy': 0.3, 'TrendFollowingStrategy': 0.3},
            'low_volume': {'GridStrategy': 0.5, 'TechnicalAnalysisStrategy': 0.3, 'TrendFollowingStrategy': 0.2}
        }
    
    async def allocate_weights(
        self,
        market_features: MarketFeatures,
        use_ml_model: bool = True
    ) -> WeightAllocation:
        """
        Allocate strategy weights based on market conditions.
        
        Args:
            market_features: Current market features
            use_ml_model: Whether to use ML model for allocation
            
        Returns:
            Weight allocation result
        """
        start_time = time.perf_counter()
        
        # Generate cache key
        features_hash = market_features.to_hash()
        model_version = await self._get_current_model_version()
        cache_key = f"weights:{features_hash}:{model_version}"
        
        # Try to get from cache first
        cached_result = await self.redis.get_cached_weights()
        if cached_result and self._is_cache_valid(cached_result, features_hash):
            self.cache_hits += 1
            processing_time = (time.perf_counter() - start_time) * 1000
            
            # Reconstruct WeightAllocation from cached data
            allocation = WeightAllocation(
                weights=cached_result['weights'],
                model_version=cached_result.get('model_version', 'cached'),
                confidence=cached_result.get('confidence', 1.0),
                allocation_timestamp=datetime.fromisoformat(cached_result['timestamp']),
                market_regime=self._determine_market_regime(market_features),
                cache_hit=True,
                processing_time_ms=processing_time
            )
            
            logger.debug(f"Cache hit for weight allocation: {processing_time:.2f}ms")
            return allocation
        
        # Cache miss - perform allocation
        self.cache_misses += 1
        
        # Allocate weights
        allocation = await self._perform_allocation(market_features, use_ml_model)
        
        # Cache the result
        await self._cache_allocation(allocation, features_hash)
        
        processing_time = (time.perf_counter() - start_time) * 1000
        allocation.processing_time_ms = processing_time
        self.allocation_times.append(processing_time)
        
        logger.debug(f"Weight allocation completed: {processing_time:.2f}ms")
        
        # Log to Supabase if available
        if self.supabase:
            asyncio.create_task(self._log_allocation_metrics(allocation, market_features))
        
        return allocation
    
    async def _perform_allocation(
        self,
        market_features: MarketFeatures,
        use_ml_model: bool
    ) -> WeightAllocation:
        """
        Perform the actual weight allocation.
        
        Args:
            market_features: Market features
            use_ml_model: Whether to use ML model
            
        Returns:
            Weight allocation result
        """
        market_regime = self._determine_market_regime(market_features)
        
        if use_ml_model:
            # Try ML model first
            try:
                weights, confidence, model_version = await self._allocate_with_ml_model(market_features)
                
                return WeightAllocation(
                    weights=weights,
                    model_version=model_version,
                    confidence=confidence,
                    allocation_timestamp=datetime.now(),
                    market_regime=market_regime
                )
                
            except Exception as e:
                logger.warning(f"ML model allocation failed, falling back to rule-based: {e}")
        
        # Fallback to rule-based allocation
        weights, confidence = self._allocate_rule_based(market_features, market_regime)
        
        return WeightAllocation(
            weights=weights,
            model_version="rule_based",
            confidence=confidence,
            allocation_timestamp=datetime.now(),
            market_regime=market_regime
        )
    
    async def _allocate_with_ml_model(
        self,
        market_features: MarketFeatures
    ) -> Tuple[Dict[str, float], float, str]:
        """
        Allocate weights using ML model.
        
        Args:
            market_features: Market features
            
        Returns:
            Tuple of (weights, confidence, model_version)
        """
        # Ensure we have the latest model
        await self._ensure_model_loaded()
        
        if not self.current_model:
            raise ValueError("No ML model available")
        
        # Prepare features for model
        feature_array = market_features.to_array().reshape(1, -1)
        
        # Get prediction from model
        prediction = await self.mlflow.predict_weights(
            self.current_model,
            feature_array
        )
        
        # Convert prediction to weights dictionary
        strategy_names = ['GridStrategy', 'TechnicalAnalysisStrategy', 'TrendFollowingStrategy']
        raw_weights = dict(zip(strategy_names, prediction[0]))
        
        # Normalize weights to sum to 1.0
        weights = self._normalize_weights(raw_weights)
        
        # Calculate confidence based on weight distribution
        confidence = self._calculate_allocation_confidence(weights)
        
        return weights, confidence, self.current_model_version
    
    def _allocate_rule_based(
        self,
        market_features: MarketFeatures,
        market_regime: str
    ) -> Tuple[Dict[str, float], float]:
        """
        Allocate weights using rule-based logic.
        
        Args:
            market_features: Market features
            market_regime: Determined market regime
            
        Returns:
            Tuple of (weights, confidence)
        """
        base_weights = self.regime_weights.get(market_regime, self.default_weights).copy()
        
        # Adjust based on specific conditions
        
        # High volatility adjustment
        if market_features.volatility > 0.03:
            # Reduce risky strategies
            base_weights['TrendFollowingStrategy'] *= 0.7
            base_weights['GridStrategy'] *= 1.2
        
        # High correlation adjustment
        if market_features.correlation_risk > 0.8:
            # Diversify more when strategies are highly correlated
            # Move towards equal weighting
            for key in base_weights:
                base_weights[key] = 0.8 * base_weights[key] + 0.2 * (1.0 / len(base_weights))
        
        # RSI extremes adjustment
        if market_features.rsi > 80:  # Overbought
            base_weights['TechnicalAnalysisStrategy'] *= 1.3
            base_weights['TrendFollowingStrategy'] *= 0.8
        elif market_features.rsi < 20:  # Oversold
            base_weights['TechnicalAnalysisStrategy'] *= 1.3
            base_weights['TrendFollowingStrategy'] *= 0.8
        
        # Normalize weights
        normalized_weights = self._normalize_weights(base_weights)
        
        # Calculate confidence based on market conditions clarity
        confidence = self._calculate_rule_based_confidence(market_features)
        
        return normalized_weights, confidence
    
    def _determine_market_regime(self, market_features: MarketFeatures) -> str:
        """
        Determine current market regime.
        
        Args:
            market_features: Market features
            
        Returns:
            Market regime string
        """
        # High volatility regime
        if market_features.volatility > 0.04:
            return 'volatile'
        
        # Low volume regime
        if market_features.volume_ratio < 0.5:
            return 'low_volume'
        
        # Trending regime
        if abs(market_features.price_momentum) > 0.02 and market_features.trend_strength > 0.7:
            return 'trending'
        
        # Default to ranging
        return 'ranging'
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """
        Normalize weights to sum to 1.0.
        
        Args:
            weights: Raw weights dictionary
            
        Returns:
            Normalized weights
        """
        total = sum(weights.values())
        if total <= 0:
            # Return equal weights if total is zero or negative
            return {k: 1.0 / len(weights) for k in weights}
        
        return {k: v / total for k, v in weights.items()}
    
    def _calculate_allocation_confidence(self, weights: Dict[str, float]) -> float:
        """
        Calculate confidence score for weight allocation.
        
        Args:
            weights: Allocated weights
            
        Returns:
            Confidence score (0.0 to 1.0)
        """
        # Higher confidence when weights are more decisive (less equal)
        weight_values = list(weights.values())
        
        # Calculate entropy (lower entropy = higher confidence)
        entropy = -sum(w * np.log(w + 1e-10) for w in weight_values if w > 0)
        max_entropy = np.log(len(weight_values))
        
        # Convert to confidence (1.0 = maximum confidence, 0.0 = minimum)
        confidence = 1.0 - (entropy / max_entropy)
        
        return max(0.0, min(1.0, confidence))
    
    def _calculate_rule_based_confidence(self, market_features: MarketFeatures) -> float:
        """
        Calculate confidence for rule-based allocation.
        
        Args:
            market_features: Market features
            
        Returns:
            Confidence score
        """
        confidence_factors = []
        
        # Volatility clarity (more extreme = higher confidence)
        vol_confidence = min(market_features.volatility * 25, 1.0)
        confidence_factors.append(vol_confidence)
        
        # Trend strength
        confidence_factors.append(market_features.trend_strength)
        
        # RSI extremes (more extreme = higher confidence)
        rsi_distance = min(abs(market_features.rsi - 50) / 50, 1.0)
        confidence_factors.append(rsi_distance)
        
        # Volume confirmation
        vol_confidence = min(abs(market_features.volume_ratio - 1.0) + 0.5, 1.0)
        confidence_factors.append(vol_confidence)
        
        # Average of all factors
        return np.mean(confidence_factors)
    
    async def _ensure_model_loaded(self) -> None:
        """Ensure the latest ML model is loaded."""
        if (self.current_model is None or 
            self.model_last_updated is None or 
            datetime.now() - self.model_last_updated > self.model_refresh_interval):
            
            await self._load_latest_model()
    
    async def _load_latest_model(self) -> None:
        """Load the latest model from MLflow."""
        try:
            start_time = time.perf_counter()
            
            # Get latest model version
            latest_version = await self.mlflow.get_latest_model_version("EnsembleWeightOptimizer")
            
            if latest_version != self.current_model_version:
                # Load new model
                self.current_model = await self.mlflow.load_model(
                    "EnsembleWeightOptimizer", 
                    latest_version
                )
                self.current_model_version = latest_version
                self.model_last_updated = datetime.now()
                
                load_time = (time.perf_counter() - start_time) * 1000
                self.model_load_times.append(load_time)
                
                logger.info(f"Loaded model version {latest_version} in {load_time:.2f}ms")
                
                # Clear weight cache since model changed
                await self.redis.clear_cache("ensemble:weights")
            
        except Exception as e:
            logger.error(f"Failed to load latest model: {e}")
            # Keep using current model if available
    
    async def _get_current_model_version(self) -> str:
        """Get current model version for cache key."""
        if self.current_model_version is None:
            try:
                self.current_model_version = await self.mlflow.get_latest_model_version(
                    "EnsembleWeightOptimizer"
                )
            except Exception:
                self.current_model_version = "fallback"
        
        return self.current_model_version
    
    def _is_cache_valid(self, cached_data: Dict, features_hash: str) -> bool:
        """
        Check if cached data is still valid.
        
        Args:
            cached_data: Cached weight data
            features_hash: Current features hash
            
        Returns:
            True if cache is valid
        """
        # Check if we have required fields
        if 'timestamp' not in cached_data or 'cache_version' not in cached_data:
            return False
        
        # Check if cache is not too old
        cached_time = datetime.fromisoformat(cached_data['timestamp'])
        if datetime.now() - cached_time > timedelta(seconds=self.cache_ttl):
            return False
        
        # Check if features hash matches (if available)
        if 'features_hash' in cached_data:
            return cached_data['features_hash'] == features_hash
        
        return True
    
    async def _cache_allocation(self, allocation: WeightAllocation, features_hash: str) -> None:
        """
        Cache weight allocation result.
        
        Args:
            allocation: Weight allocation to cache
            features_hash: Features hash for cache validation
        """
        try:
            cache_data = {
                'weights': allocation.weights,
                'model_version': allocation.model_version,
                'confidence': allocation.confidence,
                'timestamp': allocation.allocation_timestamp.isoformat(),
                'market_regime': allocation.market_regime,
                'features_hash': features_hash,
                'cache_version': '2.0'
            }
            
            await self.redis.cache_strategy_weights(
                allocation.weights,
                allocation.confidence,
                self.cache_ttl
            )
            
        except Exception as e:
            logger.error(f"Failed to cache allocation: {e}")
    
    async def _log_allocation_metrics(
        self,
        allocation: WeightAllocation,
        market_features: MarketFeatures
    ) -> None:
        """
        Log allocation metrics to Supabase.
        
        Args:
            allocation: Weight allocation result
            market_features: Market features used
        """
        try:
            metrics_data = {
                'timestamp': allocation.allocation_timestamp.isoformat(),
                'weights': allocation.weights,
                'model_version': allocation.model_version,
                'confidence': allocation.confidence,
                'market_regime': allocation.market_regime,
                'processing_time_ms': allocation.processing_time_ms,
                'cache_hit': allocation.cache_hit,
                'market_volatility': market_features.volatility,
                'market_rsi': market_features.rsi,
                'trend_strength': market_features.trend_strength,
                'correlation_risk': market_features.correlation_risk
            }
            
            await self.supabase.store_weight_allocation_metrics(metrics_data)
            
        except Exception as e:
            logger.error(f"Failed to log allocation metrics: {e}")
    
    async def force_model_refresh(self) -> bool:
        """
        Force refresh of ML model.
        
        Returns:
            True if successful
        """
        try:
            await self._load_latest_model()
            return True
        except Exception as e:
            logger.error(f"Force model refresh failed: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get allocation performance statistics.
        
        Returns:
            Performance metrics dictionary
        """
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        avg_time = np.mean(self.allocation_times) if self.allocation_times else 0
        p95_time = np.percentile(self.allocation_times, 95) if self.allocation_times else 0
        
        avg_model_load = np.mean(self.model_load_times) if self.model_load_times else 0
        
        return {
            'total_allocations': total_requests,
            'cache_hit_rate': round(hit_rate, 2),
            'avg_allocation_time_ms': round(avg_time, 2),
            'p95_allocation_time_ms': round(p95_time, 2),
            'avg_model_load_time_ms': round(avg_model_load, 2),
            'current_model_version': self.current_model_version,
            'model_last_updated': self.model_last_updated.isoformat() if self.model_last_updated else None,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on weight allocation system.
        
        Returns:
            Health status dictionary
        """
        try:
            # Test Redis connectivity
            redis_stats = await self.redis.get_cache_stats()
            redis_healthy = bool(redis_stats)
            
            # Test MLflow connectivity
            try:
                model_version = await self.mlflow.get_latest_model_version("EnsembleWeightOptimizer")
                mlflow_healthy = bool(model_version)
            except Exception:
                mlflow_healthy = False
            
            # Test allocation with dummy data
            dummy_features = MarketFeatures(
                volatility=0.02,
                volume_ratio=1.0,
                price_momentum=0.001,
                rsi=50.0,
                bollinger_position=0.5,
                trend_strength=0.5,
                correlation_risk=0.5,
                timestamp=datetime.now()
            )
            
            start_time = time.perf_counter()
            allocation = await self.allocate_weights(dummy_features, use_ml_model=mlflow_healthy)
            test_time = (time.perf_counter() - start_time) * 1000
            
            performance_stats = self.get_performance_stats()
            
            return {
                'status': 'healthy' if redis_healthy and test_time < 100 else 'degraded',
                'redis_healthy': redis_healthy,
                'mlflow_healthy': mlflow_healthy,
                'test_allocation_time_ms': round(test_time, 2),
                'current_model_available': self.current_model is not None,
                'performance_stats': performance_stats
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }