"""
Advanced Risk Calculator for Portfolio Risk Management
Implements Value at Risk (VaR), Expected Shortfall (ES), and comprehensive risk metrics
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
import redis.asyncio as redis
import logging
from scipy import stats
from scipy.optimize import minimize

logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Comprehensive portfolio risk metrics"""
    portfolio_var_95: float  # 95% Value at Risk
    portfolio_var_99: float  # 99% Value at Risk
    expected_shortfall_95: float  # Expected Shortfall at 95%
    expected_shortfall_99: float  # Expected Shortfall at 99%
    max_drawdown: float
    current_drawdown: float
    portfolio_volatility: float
    portfolio_beta: float
    concentration_risk: float
    leverage_ratio: float
    risk_adjusted_return: float
    sortino_ratio: float
    calmar_ratio: float
    timestamp: datetime

@dataclass
class PositionRisk:
    """Individual position risk metrics"""
    symbol: str
    weight: float
    volatility: float
    var_95: float
    var_99: float
    contribution_to_portfolio_var: float
    marginal_var: float
    component_var: float
    beta: float
    correlation_with_portfolio: float

@dataclass
class RiskLimits:
    """Risk limits and thresholds"""
    max_portfolio_var: float = 0.05  # 5% daily VaR
    max_individual_weight: float = 0.25  # 25% max position size
    max_concentration: float = 0.60  # 60% max in any sector/category
    max_leverage: float = 2.0  # 2x max leverage
    max_drawdown: float = 0.15  # 15% max drawdown
    min_liquidity_ratio: float = 0.10  # 10% minimum cash/liquid assets

class RiskCalculator:
    """
    Advanced risk calculator with comprehensive portfolio risk analysis
    """
    
    def __init__(
        self,
        redis_client: redis.Redis,
        confidence_levels: List[float] = [0.95, 0.99],
        lookback_window: int = 252,  # 1 year of daily data
        risk_free_rate: float = 0.02  # 2% annual risk-free rate
    ):
        self.redis = redis_client
        self.confidence_levels = confidence_levels
        self.lookback_window = lookback_window
        self.risk_free_rate = risk_free_rate
        
        # Cache keys
        self.RISK_METRICS_CACHE = "risk:portfolio_metrics"
        self.RETURNS_CACHE = "risk:returns_history"
        self.CORRELATION_CACHE = "risk:correlation_matrix"
        self.POSITION_RISK_CACHE = "risk:position_metrics"
        
    async def calculate_portfolio_risk(
        self,
        positions: Dict[str, float],
        returns_data: Optional[Dict[str, List[float]]] = None,
        correlation_matrix: Optional[np.ndarray] = None
    ) -> RiskMetrics:
        """
        Calculate comprehensive portfolio risk metrics
        """
        try:
            # Check cache first
            cache_key = f"{self.RISK_METRICS_CACHE}:{hash(str(sorted(positions.items())))}"
            cached_metrics = await self.redis.get(cache_key)
            
            if cached_metrics:
                cached_data = json.loads(cached_metrics)
                cache_time = datetime.fromisoformat(cached_data['timestamp'])
                
                # Use cached if less than 10 minutes old
                if datetime.now() - cache_time < timedelta(minutes=10):
                    return RiskMetrics(**cached_data)
            
            # Get returns data if not provided
            if returns_data is None:
                returns_data = await self._get_returns_data(list(positions.keys()))
            
            # Calculate portfolio returns
            portfolio_returns = self._calculate_portfolio_returns(positions, returns_data)
            
            if len(portfolio_returns) < 30:
                # Not enough data for reliable risk calculation
                return self._get_default_risk_metrics()
            
            # Calculate VaR and ES
            var_95, var_99 = self._calculate_var(portfolio_returns, self.confidence_levels)
            es_95, es_99 = self._calculate_expected_shortfall(portfolio_returns, self.confidence_levels)
            
            # Calculate drawdown metrics
            max_drawdown, current_drawdown = self._calculate_drawdown_metrics(portfolio_returns)
            
            # Calculate portfolio volatility
            portfolio_vol = np.std(portfolio_returns) * np.sqrt(252)  # Annualized
            
            # Calculate portfolio beta (vs market proxy)
            portfolio_beta = await self._calculate_portfolio_beta(portfolio_returns)
            
            # Calculate concentration risk
            concentration_risk = self._calculate_concentration_risk(positions)
            
            # Calculate leverage ratio
            leverage_ratio = sum(abs(weight) for weight in positions.values())
            
            # Calculate risk-adjusted metrics
            portfolio_return = np.mean(portfolio_returns) * 252  # Annualized
            risk_adjusted_return = (portfolio_return - self.risk_free_rate) / portfolio_vol if portfolio_vol > 0 else 0
            
            # Calculate Sortino ratio
            downside_returns = [r for r in portfolio_returns if r < 0]
            downside_vol = np.std(downside_returns) * np.sqrt(252) if downside_returns else portfolio_vol
            sortino_ratio = (portfolio_return - self.risk_free_rate) / downside_vol if downside_vol > 0 else 0
            
            # Calculate Calmar ratio
            calmar_ratio = portfolio_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Create risk metrics
            risk_metrics = RiskMetrics(
                portfolio_var_95=var_95,
                portfolio_var_99=var_99,
                expected_shortfall_95=es_95,
                expected_shortfall_99=es_99,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                portfolio_volatility=portfolio_vol,
                portfolio_beta=portfolio_beta,
                concentration_risk=concentration_risk,
                leverage_ratio=leverage_ratio,
                risk_adjusted_return=risk_adjusted_return,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                timestamp=datetime.now()
            )
            
            # Cache the results
            await self._cache_risk_metrics(cache_key, risk_metrics)
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return self._get_default_risk_metrics()
    
    async def calculate_position_risk(
        self,
        positions: Dict[str, float],
        returns_data: Optional[Dict[str, List[float]]] = None
    ) -> List[PositionRisk]:
        """
        Calculate individual position risk metrics and contributions
        """
        try:
            position_risks = []
            
            # Get returns data if not provided
            if returns_data is None:
                returns_data = await self._get_returns_data(list(positions.keys()))
            
            # Calculate portfolio returns for correlation
            portfolio_returns = self._calculate_portfolio_returns(positions, returns_data)
            
            for symbol, weight in positions.items():
                if symbol not in returns_data or weight == 0:
                    continue
                
                asset_returns = returns_data[symbol]
                if len(asset_returns) < 30:
                    continue
                
                # Calculate individual asset metrics
                asset_vol = np.std(asset_returns) * np.sqrt(252)
                asset_var_95 = np.percentile(asset_returns, 5) * abs(weight)
                asset_var_99 = np.percentile(asset_returns, 1) * abs(weight)
                
                # Calculate beta
                asset_beta = self._calculate_beta(asset_returns, portfolio_returns)
                
                # Calculate correlation with portfolio
                correlation = np.corrcoef(asset_returns[-min(len(asset_returns), len(portfolio_returns)):], 
                                       portfolio_returns[-min(len(asset_returns), len(portfolio_returns)):])[0, 1]
                
                # Calculate risk contributions
                portfolio_var = np.var(portfolio_returns)
                marginal_var = self._calculate_marginal_var(asset_returns, portfolio_returns, weight)
                component_var = weight * marginal_var
                contribution_to_var = component_var / portfolio_var if portfolio_var > 0 else 0
                
                position_risk = PositionRisk(
                    symbol=symbol,
                    weight=weight,
                    volatility=asset_vol,
                    var_95=asset_var_95,
                    var_99=asset_var_99,
                    contribution_to_portfolio_var=contribution_to_var,
                    marginal_var=marginal_var,
                    component_var=component_var,
                    beta=asset_beta,
                    correlation_with_portfolio=correlation if not np.isnan(correlation) else 0.0
                )
                
                position_risks.append(position_risk)
            
            # Sort by contribution to portfolio risk
            position_risks.sort(key=lambda x: abs(x.contribution_to_portfolio_var), reverse=True)
            
            return position_risks
            
        except Exception as e:
            logger.error(f"Error calculating position risk: {e}")
            return []
    
    def _calculate_var(self, returns: List[float], confidence_levels: List[float]) -> Tuple[float, ...]:
        """Calculate Value at Risk using historical simulation"""
        if not returns:
            return tuple(0.0 for _ in confidence_levels)
        
        var_values = []
        for confidence_level in confidence_levels:
            percentile = (1 - confidence_level) * 100
            var_value = -np.percentile(returns, percentile)  # Negative for loss
            var_values.append(var_value)
        
        return tuple(var_values)
    
    def _calculate_expected_shortfall(self, returns: List[float], confidence_levels: List[float]) -> Tuple[float, ...]:
        """Calculate Expected Shortfall (Conditional VaR)"""
        if not returns:
            return tuple(0.0 for _ in confidence_levels)
        
        es_values = []
        returns_array = np.array(returns)
        
        for confidence_level in confidence_levels:
            percentile = (1 - confidence_level) * 100
            var_threshold = np.percentile(returns, percentile)
            
            # Expected shortfall is the mean of returns below VaR threshold
            tail_returns = returns_array[returns_array <= var_threshold]
            es_value = -np.mean(tail_returns) if len(tail_returns) > 0 else 0.0
            es_values.append(es_value)
        
        return tuple(es_values)
    
    def _calculate_drawdown_metrics(self, returns: List[float]) -> Tuple[float, float]:
        """Calculate maximum drawdown and current drawdown"""
        if not returns:
            return 0.0, 0.0
        
        # Calculate cumulative returns
        cumulative_returns = np.cumprod(1 + np.array(returns))
        
        # Calculate running maximum
        running_max = np.maximum.accumulate(cumulative_returns)
        
        # Calculate drawdowns
        drawdowns = (cumulative_returns - running_max) / running_max
        
        max_drawdown = np.min(drawdowns)
        current_drawdown = drawdowns[-1]
        
        return max_drawdown, current_drawdown
    
    async def _calculate_portfolio_beta(self, portfolio_returns: List[float]) -> float:
        """Calculate portfolio beta vs market proxy"""
        try:
            # For crypto, we'll use BTC as market proxy
            # In production, this would fetch actual BTC returns
            market_returns = await self._get_market_proxy_returns(len(portfolio_returns))
            
            if len(market_returns) != len(portfolio_returns):
                return 1.0  # Default beta
            
            return self._calculate_beta(portfolio_returns, market_returns)
            
        except Exception as e:
            logger.error(f"Error calculating portfolio beta: {e}")
            return 1.0
    
    def _calculate_beta(self, asset_returns: List[float], market_returns: List[float]) -> float:
        """Calculate beta between asset and market"""
        if len(asset_returns) != len(market_returns) or len(asset_returns) < 2:
            return 1.0
        
        try:
            covariance = np.cov(asset_returns, market_returns)[0, 1]
            market_variance = np.var(market_returns)
            
            beta = covariance / market_variance if market_variance > 0 else 1.0
            return beta
            
        except Exception:
            return 1.0
    
    def _calculate_concentration_risk(self, positions: Dict[str, float]) -> float:
        """Calculate concentration risk using Herfindahl-Hirschman Index"""
        if not positions:
            return 0.0
        
        # Normalize weights to sum to 1
        total_weight = sum(abs(weight) for weight in positions.values())
        if total_weight == 0:
            return 0.0
        
        normalized_weights = [abs(weight) / total_weight for weight in positions.values()]
        
        # Calculate HHI
        hhi = sum(weight ** 2 for weight in normalized_weights)
        
        # Convert to concentration risk (1 = maximum concentration, 0 = perfect diversification)
        n = len(positions)
        min_hhi = 1 / n if n > 0 else 1
        max_hhi = 1
        
        concentration_risk = (hhi - min_hhi) / (max_hhi - min_hhi) if max_hhi > min_hhi else 0
        
        return concentration_risk
    
    def _calculate_marginal_var(self, asset_returns: List[float], portfolio_returns: List[float], weight: float) -> float:
        """Calculate marginal VaR contribution"""
        try:
            if len(asset_returns) != len(portfolio_returns) or len(asset_returns) < 10:
                return 0.0
            
            # Calculate correlation between asset and portfolio
            correlation = np.corrcoef(asset_returns, portfolio_returns)[0, 1]
            if np.isnan(correlation):
                correlation = 0.0
            
            # Marginal VaR = correlation * asset_volatility * portfolio_volatility
            asset_vol = np.std(asset_returns)
            portfolio_vol = np.std(portfolio_returns)
            
            marginal_var = correlation * asset_vol * portfolio_vol
            
            return marginal_var
            
        except Exception as e:
            logger.error(f"Error calculating marginal VaR: {e}")
            return 0.0
    
    def _calculate_portfolio_returns(self, positions: Dict[str, float], returns_data: Dict[str, List[float]]) -> List[float]:
        """Calculate portfolio returns from position weights and asset returns"""
        if not positions or not returns_data:
            return []
        
        # Find the minimum length across all assets
        min_length = min(len(returns_data.get(symbol, [])) for symbol in positions.keys() if symbol in returns_data)
        
        if min_length == 0:
            return []
        
        portfolio_returns = []
        
        for i in range(min_length):
            period_return = 0.0
            for symbol, weight in positions.items():
                if symbol in returns_data and i < len(returns_data[symbol]):
                    period_return += weight * returns_data[symbol][i]
            
            portfolio_returns.append(period_return)
        
        return portfolio_returns
    
    async def _get_returns_data(self, symbols: List[str]) -> Dict[str, List[float]]:
        """Get historical returns data for symbols"""
        try:
            returns_data = {}
            
            for symbol in symbols:
                cache_key = f"{self.RETURNS_CACHE}:{symbol}"
                cached_returns = await self.redis.get(cache_key)
                
                if cached_returns:
                    returns_data[symbol] = json.loads(cached_returns)
                else:
                    # Generate synthetic returns for testing
                    # In production, this would fetch from market data API
                    np.random.seed(hash(symbol) % 2**32)
                    returns = np.random.normal(0.0005, 0.02, self.lookback_window)  # Daily returns
                    returns_data[symbol] = returns.tolist()
                    
                    # Cache for 30 minutes
                    await self.redis.setex(cache_key, 1800, json.dumps(returns.tolist()))
            
            return returns_data
            
        except Exception as e:
            logger.error(f"Error getting returns data: {e}")
            return {}
    
    async def _get_market_proxy_returns(self, length: int) -> List[float]:
        """Get market proxy returns (BTC for crypto markets)"""
        try:
            cache_key = f"{self.RETURNS_CACHE}:BTC_PROXY"
            cached_returns = await self.redis.get(cache_key)
            
            if cached_returns:
                market_returns = json.loads(cached_returns)
                return market_returns[:length]  # Return only needed length
            
            # Generate synthetic BTC returns
            np.random.seed(42)  # Fixed seed for consistent market proxy
            market_returns = np.random.normal(0.0008, 0.025, self.lookback_window)
            
            # Cache for 30 minutes
            await self.redis.setex(cache_key, 1800, json.dumps(market_returns.tolist()))
            
            return market_returns[:length].tolist()
            
        except Exception as e:
            logger.error(f"Error getting market proxy returns: {e}")
            return [0.0] * length
    
    async def _cache_risk_metrics(self, cache_key: str, metrics: RiskMetrics):
        """Cache risk metrics"""
        try:
            cache_data = asdict(metrics)
            cache_data['timestamp'] = metrics.timestamp.isoformat()
            
            await self.redis.setex(cache_key, 600, json.dumps(cache_data))  # 10 minutes
            
        except Exception as e:
            logger.error(f"Error caching risk metrics: {e}")
    
    def _get_default_risk_metrics(self) -> RiskMetrics:
        """Return default risk metrics when calculation fails"""
        return RiskMetrics(
            portfolio_var_95=0.05,
            portfolio_var_99=0.08,
            expected_shortfall_95=0.07,
            expected_shortfall_99=0.12,
            max_drawdown=0.0,
            current_drawdown=0.0,
            portfolio_volatility=0.25,
            portfolio_beta=1.0,
            concentration_risk=0.3,
            leverage_ratio=1.0,
            risk_adjusted_return=0.0,
            sortino_ratio=0.0,
            calmar_ratio=0.0,
            timestamp=datetime.now()
        )
    
    async def check_risk_limits(self, positions: Dict[str, float], risk_limits: RiskLimits) -> Dict[str, bool]:
        """Check if current portfolio violates risk limits"""
        try:
            risk_metrics = await self.calculate_portfolio_risk(positions)
            
            violations = {
                'var_limit': risk_metrics.portfolio_var_95 > risk_limits.max_portfolio_var,
                'concentration_limit': risk_metrics.concentration_risk > risk_limits.max_concentration,
                'leverage_limit': risk_metrics.leverage_ratio > risk_limits.max_leverage,
                'drawdown_limit': abs(risk_metrics.current_drawdown) > risk_limits.max_drawdown,
                'position_size_limit': any(abs(weight) > risk_limits.max_individual_weight for weight in positions.values())
            }
            
            return violations
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {key: False for key in ['var_limit', 'concentration_limit', 'leverage_limit', 'drawdown_limit', 'position_size_limit']}
    
    async def optimize_risk_allocation(self, expected_returns: Dict[str, float], risk_target: float = 0.05) -> Dict[str, float]:
        """Optimize portfolio allocation to target risk level"""
        try:
            symbols = list(expected_returns.keys())
            n_assets = len(symbols)
            
            if n_assets == 0:
                return {}
            
            # Get returns data for covariance calculation
            returns_data = await self._get_returns_data(symbols)
            
            # Build covariance matrix
            returns_matrix = []
            for symbol in symbols:
                if symbol in returns_data:
                    returns_matrix.append(returns_data[symbol])
                else:
                    # Default returns if not available
                    returns_matrix.append([0.0] * 100)
            
            cov_matrix = np.cov(returns_matrix)
            
            # Expected returns vector
            expected_returns_vector = np.array([expected_returns.get(symbol, 0.001) for symbol in symbols])
            
            # Optimization objective: minimize portfolio variance subject to target return
            def objective(weights):
                portfolio_var = np.dot(weights, np.dot(cov_matrix, weights))
                return portfolio_var
            
            # Constraints
            constraints = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # Weights sum to 1
            ]
            
            # Bounds (no short selling, max 25% per position)
            bounds = [(0, 0.25) for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            x0 = np.array([1/n_assets] * n_assets)
            
            # Optimize
            result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
            
            if result.success:
                optimal_weights = dict(zip(symbols, result.x))
                return {symbol: weight for symbol, weight in optimal_weights.items() if weight > 0.001}
            else:
                # Fallback to equal weights
                equal_weight = 1.0 / n_assets
                return {symbol: equal_weight for symbol in symbols}
                
        except Exception as e:
            logger.error(f"Error optimizing risk allocation: {e}")
            # Return equal weights as fallback
            n_assets = len(expected_returns)
            if n_assets > 0:
                equal_weight = 1.0 / n_assets
                return {symbol: equal_weight for symbol in expected_returns.keys()}
            return {}