"""Execution service for handling order execution."""
import logging
from app.services.execution.service import ExecutionService as BaseExecutionService
from app.services.execution.lifecycle import LifecycleMixin
from app.services.execution.order_placement import OrderPlacementMixin
from app.services.execution.trade_operations import TradeOperationsMixin
from app.services.execution.utils import UtilsMixin

logger = logging.getLogger(__name__)

class ExecutionService(BaseExecutionService, LifecycleMixin, OrderPlacementMixin, TradeOperationsMixin, UtilsMixin):
    """Service for executing orders on exchanges.

    This service handles the execution of orders, including order placement,
    cancellation, and monitoring. It integrates order management, trade management,
    and websocket handling components.

    This class inherits from:
    - BaseExecutionService: Core initialization and attributes
    - LifecycleMixin: Lifecycle management methods (start/stop monitoring)
    - OrderPlacementMixin: Order placement methods
    - TradeOperationsMixin: Trade management operations
    - UtilsMixin: Utility functions
    """

    # The implementation is provided by the mixins
