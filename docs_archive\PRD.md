# Product Requirements Document (PRD): Crypto_App_V2

**Version:** 1.0  
**Date:** 2025-04-07  
**Owner:** User/Developer

---

## 1. Problem Statement
Manual cryptocurrency futures trading is inefficient, error-prone, and requires constant monitoring. There is a need for an automated system that dynamically selects and executes the most suitable trading strategy based on real-time market conditions, improving efficiency, consistency, and reducing operational risk on the Binance Futures Testnet.

---

## 2. Target Users & Use Cases
- **Primary User:** Technically proficient developer/trader interested in algorithmic trading and strategy testing.
- **Key Use Cases:**
  - Automate the selection and execution of trading strategies (Grid, Technical Analysis, Trend Following).
  - Monitor and control trading activity via a web dashboard.
  - Avoid manual errors (e.g., minimum order size, incorrect trade quantity).
  - Test and optimize strategies using real market data.

---

## 3. Solution Overview
A modular FastAPI/React application that:
- Integrates multiple trading strategies.
- Dynamically scores and selects the optimal strategy based on live market data.
- Executes trades automatically on Binance Futures Testnet.
- Provides a real-time dashboard for monitoring, control, and ML-based strategy weight optimization.
- Ensures robust error handling and configuration management.

---

## 4. Functional Requirements
- Implement Grid, Technical Analysis, and Trend Following strategies.
- Dynamic strategy scoring and selection logic.
- Automated trade execution on Binance Futures Testnet.
- Real-time dashboard (React) for status, control, and ML controls.
- Robust error handling for API limits, minimum notional, and order size.
- Centralized configuration via `.env` and `settings.py`.
- ML weight optimization for strategy selection (enable/disable via dashboard).
- Logging for debugging and monitoring.
- Modular codebase for maintainability.
- Unit and integration tests using real Binance Testnet data.
- WebSocket-based real-time trade state updates.
- Automated ML model training and hyperparameter optimization.
- MCP server integration for advanced context and automation.

---

## 5. Technical Architecture
- **Backend:** Python 3.12, FastAPI, Uvicorn, asyncio, python-binance, pandas, numpy, TA-Lib, pydantic-settings, pytest, gym, stable-baselines3, optuna, scikit-learn.
- **Frontend:** React (TypeScript), Material UI, WebSocket, axios.
- **Database:** PostgreSQL for market/trade/ML data (Testnet only).
- **Infrastructure:** Local dev via Uvicorn/npm; Docker files exist; MCP servers managed via PowerShell scripts.
- **Configuration:** Centralized in `app/config/settings.py` and `.env`.
- **Testing:** Real data only (no mocks); TDD approach; async test classes; coverage improving.

---

## 6. MCP Server Integration
- **Installed & Required MCPs:**
  - PIF (Personal Intelligence Framework): File ops, reasoning, journal.
  - Simple Vega-Lite MCP: Data management, visualization.
  - Playwright MCP: Browser automation/testing.
  - Git MCP: Version control integration.
  - Time MCP: Accurate time services.
  - CoinCap MCP: Market data integration.
  - CCXT MCP: Exchange integration.
  - Memory MCP: In-memory data storage.
- **Startup:** All MCPs started via `start_mcp_servers.ps1`.
- **Configuration:** Absolute paths required in all scripts/configs.
- **Testing:** Playwright MCP used for browser/E2E tests; Vega-Lite for visualization tests.

---

## 7. Known Errors, Bugs, and Issues to Avoid
- **Binance API:**
  - Minimum notional/order size errors (e.g., -4164). Ensure dynamic quantity calculation.
  - API rate limits. Implement retry logic and error handling.
- **Concurrency:**
  - Thread safety in DB/cache updates (especially for SL/TP monitoring).
- **Orphaned Orders:**
  - Edge cases where SL/TP orders are cancelled externally or filled simultaneously.
- **Testing:**
  - Pydantic v2 deprecation warnings (addressed with warning filters).
  - Coroutine warnings in async tests (use unittest.IsolatedAsyncioTestCase).
- **Frontend:**
  - Null values in statistics data (handled in AccountStatistics component).
  - WebSocket connection state checks required.
- **MCP Servers:**
  - All MCP scripts/configs must use absolute paths.
  - Only install/enable necessary MCPs; avoid deprecated/unused MCPs (e.g., Shrimp Task Manager MCP).
- **General:**
  - Remove unused code/configs (e.g., app_config.py, config_loader.py).
  - Keep files under 500 lines; modularize large files.

---

## 8. Out of Scope
- Live trading on Binance Mainnet.
- Advanced risk management beyond basic position sizing.
- Multi-user support.
- Historical backtesting framework.
- Persistent trade history database.

---

## 9. Success Criteria
- Automated, correct execution of trades based on dynamically selected strategies.
- Dashboard accurately reflects bot status, selected strategy, and trade activity.
- No Binance API errors (e.g., minimum notional, order size).
- Stable, reliable operation of backend and frontend.
- ML weight optimization demonstrably improves strategy selection over static weights.
- All MCP servers required for the project are installed, configured, and operational.

---

## 10. References
- [Project Brief](../memory-bank/projectbrief.md)
- [Technical Context](../memory-bank/techContext.md)
- [Product Context](../memory-bank/productContext.md)
- [Active Context](../memory-bank/activeContext.md)
- [Progress Tracker](../memory-bank/progress.md)
- [ML Weight Optimization Documentation](../memory-bank/ml_weight_optimization.md)
- [MCP Servers Documentation](../memory-bank/mcp_servers.md)
- [README](../README.md)

---

*This PRD is detailed, actionable, and focused on clarity and efficiency. For further details, see the referenced memory-bank documents.* 