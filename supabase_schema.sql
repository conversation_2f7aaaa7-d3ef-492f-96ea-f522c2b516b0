-- Supabase Database Schema for Strategy Ensemble System
-- Run this in your Supabase SQL Editor to create the required tables

-- Enable Row Level Security (RLS) - Important for Supabase
ALTER DATABASE postgres SET "app.jwt_secret" TO 'super-secret-jwt-token-with-at-least-32-characters-long';

-- Create trade_executions table
CREATE TABLE IF NOT EXISTS trade_executions (
    id BIGSERIAL PRIMARY KEY,
    strategy_name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    action TEXT NOT NULL,
    quantity DECIMAL(20, 8) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create portfolio_metrics table
CREATE TABLE IF NOT EXISTS portfolio_metrics (
    id BIGSERIAL PRIMARY KEY,
    portfolio_value DECIMAL(20, 2) NOT NULL,
    total_return DECIMAL(10, 6) DEFAULT 0,
    sharpe_ratio DECIMAL(10, 6) DEFAULT 0,
    max_drawdown DECIMAL(10, 6) DEFAULT 0,
    win_rate DECIMAL(5, 4) DEFAULT 0,
    symbol TEXT DEFAULT 'PORTFOLIO',
    timestamp TIMESTAMPTZ NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create strategy_performance table
CREATE TABLE IF NOT EXISTS strategy_performance (
    id BIGSERIAL PRIMARY KEY,
    strategy_name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5, 4) DEFAULT 0,
    total_return DECIMAL(20, 8) DEFAULT 0,
    sharpe_ratio DECIMAL(10, 6) DEFAULT 0,
    max_drawdown DECIMAL(10, 6) DEFAULT 0,
    profit_factor DECIMAL(10, 6) DEFAULT 0,
    avg_win DECIMAL(20, 8) DEFAULT 0,
    avg_loss DECIMAL(20, 8) DEFAULT 0,
    confidence_score DECIMAL(5, 4) DEFAULT 0,
    timestamp TIMESTAMPTZ NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create market_data table
CREATE TABLE IF NOT EXISTS market_data (
    id BIGSERIAL PRIMARY KEY,
    symbol TEXT NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8) NOT NULL,
    high_24h DECIMAL(20, 8),
    low_24h DECIMAL(20, 8),
    change_24h DECIMAL(10, 6),
    source TEXT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create cross_exchange_validations table
CREATE TABLE IF NOT EXISTS cross_exchange_validations (
    id BIGSERIAL PRIMARY KEY,
    symbol TEXT NOT NULL,
    consensus_price DECIMAL(20, 8) NOT NULL,
    price_variance DECIMAL(20, 8) DEFAULT 0,
    data_quality_score DECIMAL(5, 4) DEFAULT 0,
    source_count INTEGER DEFAULT 0,
    outlier_sources TEXT[] DEFAULT '{}',
    price_spread_pct DECIMAL(10, 6) DEFAULT 0,
    volume_weighted_price DECIMAL(20, 8) DEFAULT 0,
    timestamp TIMESTAMPTZ NOT NULL,
    individual_sources JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create ensemble_executions table
CREATE TABLE IF NOT EXISTS ensemble_executions (
    id BIGSERIAL PRIMARY KEY,
    symbol TEXT NOT NULL,
    action TEXT NOT NULL,
    quantity DECIMAL(20, 8) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    confidence DECIMAL(5, 4) DEFAULT 0,
    strategy_signals JSONB DEFAULT '{}',
    execution_time_ms DECIMAL(10, 3) DEFAULT 0,
    timestamp TIMESTAMPTZ NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trade_executions_strategy ON trade_executions(strategy_name);
CREATE INDEX IF NOT EXISTS idx_trade_executions_symbol ON trade_executions(symbol);
CREATE INDEX IF NOT EXISTS idx_trade_executions_timestamp ON trade_executions(timestamp);
CREATE INDEX IF NOT EXISTS idx_trade_executions_created_at ON trade_executions(created_at);

CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_timestamp ON portfolio_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_symbol ON portfolio_metrics(symbol);

CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy ON strategy_performance(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_symbol ON strategy_performance(symbol);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_timestamp ON strategy_performance(timestamp);

CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol);
CREATE INDEX IF NOT EXISTS idx_market_data_source ON market_data(source);
CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp);

CREATE INDEX IF NOT EXISTS idx_cross_exchange_symbol ON cross_exchange_validations(symbol);
CREATE INDEX IF NOT EXISTS idx_cross_exchange_timestamp ON cross_exchange_validations(timestamp);

CREATE INDEX IF NOT EXISTS idx_ensemble_executions_symbol ON ensemble_executions(symbol);
CREATE INDEX IF NOT EXISTS idx_ensemble_executions_timestamp ON ensemble_executions(timestamp);

-- Enable RLS (Row Level Security) but allow all operations for now
-- In production, you should configure proper RLS policies
ALTER TABLE trade_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE strategy_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE cross_exchange_validations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ensemble_executions ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for development (MODIFY FOR PRODUCTION)
CREATE POLICY "Enable all operations for service role" ON trade_executions FOR ALL USING (true);
CREATE POLICY "Enable all operations for service role" ON portfolio_metrics FOR ALL USING (true);
CREATE POLICY "Enable all operations for service role" ON strategy_performance FOR ALL USING (true);
CREATE POLICY "Enable all operations for service role" ON market_data FOR ALL USING (true);
CREATE POLICY "Enable all operations for service role" ON cross_exchange_validations FOR ALL USING (true);
CREATE POLICY "Enable all operations for service role" ON ensemble_executions FOR ALL USING (true);

-- Update triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_trade_executions_updated_at BEFORE UPDATE ON trade_executions
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_portfolio_metrics_updated_at BEFORE UPDATE ON portfolio_metrics
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_strategy_performance_updated_at BEFORE UPDATE ON strategy_performance
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Views for common queries

-- Recent trade executions view
CREATE OR REPLACE VIEW recent_trade_executions AS
SELECT 
    te.*,
    EXTRACT(EPOCH FROM (NOW() - te.created_at)) / 60 AS minutes_ago
FROM trade_executions te
ORDER BY te.created_at DESC
LIMIT 100;

-- Strategy performance summary view
CREATE OR REPLACE VIEW strategy_performance_summary AS
SELECT 
    strategy_name,
    COUNT(*) as total_strategies,
    AVG(win_rate) as avg_win_rate,
    AVG(sharpe_ratio) as avg_sharpe_ratio,
    AVG(total_return) as avg_return,
    MAX(created_at) as last_updated
FROM strategy_performance
GROUP BY strategy_name
ORDER BY avg_return DESC;

-- Portfolio performance view
CREATE OR REPLACE VIEW portfolio_performance_latest AS
SELECT DISTINCT ON (symbol)
    symbol,
    portfolio_value,
    total_return,
    sharpe_ratio,
    max_drawdown,
    win_rate,
    timestamp,
    created_at
FROM portfolio_metrics
ORDER BY symbol, created_at DESC;

-- Market data latest view  
CREATE OR REPLACE VIEW market_data_latest AS
SELECT DISTINCT ON (symbol, source)
    symbol,
    source,
    price,
    volume,
    high_24h,
    low_24h,
    change_24h,
    timestamp,
    created_at
FROM market_data
ORDER BY symbol, source, created_at DESC;

-- Grant permissions to authenticated users (adjust as needed)
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant permissions to anon users for read operations (adjust as needed)
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- Comment on tables
COMMENT ON TABLE trade_executions IS 'Stores all strategy trade executions and ensemble decisions';
COMMENT ON TABLE portfolio_metrics IS 'Stores portfolio-level performance metrics over time';
COMMENT ON TABLE strategy_performance IS 'Stores individual strategy performance analytics';
COMMENT ON TABLE market_data IS 'Stores market data from multiple sources for analysis';
COMMENT ON TABLE cross_exchange_validations IS 'Stores cross-exchange validation results';
COMMENT ON TABLE ensemble_executions IS 'Stores ensemble system execution results and metrics';

-- Example queries for testing (commented out)
-- INSERT INTO trade_executions (strategy_name, symbol, action, quantity, price, timestamp)
-- VALUES ('test_strategy', 'BTCUSDT', 'BUY', 0.001, 50000.0, NOW());

-- SELECT * FROM recent_trade_executions LIMIT 5;
-- SELECT * FROM strategy_performance_summary;
-- SELECT * FROM portfolio_performance_latest;