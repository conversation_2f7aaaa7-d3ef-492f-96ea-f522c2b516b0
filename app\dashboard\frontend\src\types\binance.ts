/**
 * TypeScript type definitions for Binance Futures Testnet Integration
 * 
 * Defines interfaces for:
 * - Account information and balances
 * - Positions with PnL tracking
 * - Orders and trade history
 * - Risk metrics and portfolio analysis
 * - API request/response types
 * 
 * Created: June 16, 2025
 */

export interface BinanceAccount {
  balance: number;           // Total account balance in USDT
  equity: number;            // Account equity in USDT
  margin_used: number;       // Used margin in USDT
  margin_available: number;  // Available margin in USDT
  pnl_unrealized: number;    // Unrealized PnL in USDT
  pnl_realized_today: number; // Today's realized PnL in USDT
  margin_ratio: number;      // Margin ratio as percentage
  max_withdraw_amount: number; // Maximum withdrawable amount
}

export interface Position {
  symbol: string;            // Trading symbol (e.g., 'BTCUSDT')
  size: number;              // Position size
  entry_price: number;       // Average entry price
  current_price: number;     // Current market price
  pnl: number;               // Unrealized PnL
  pnl_percentage: number;    // PnL percentage
  side: 'LONG' | 'SHORT';    // Position side
  liquidation_price: number; // Liquidation price
  margin: number;            // Position margin
  strategy?: string;         // Strategy attribution (optional)
}

export interface Order {
  order_id: string;          // Order ID
  symbol: string;            // Trading symbol
  side: 'BUY' | 'SELL';      // Order side
  type: string;              // Order type (MARKET, LIMIT, etc.)
  amount: number;            // Order amount
  filled: number;            // Filled amount
  remaining: number;         // Remaining amount
  price?: number;            // Order price (optional for market orders)
  average_price?: number;    // Average fill price
  status: string;            // Order status
  timestamp: string;         // Order creation time (ISO string)
  last_update: string;       // Last update time (ISO string)
  fees: number;              // Trading fees
}

export interface Trade {
  id: string;                // Trade ID
  order_id: string;          // Related order ID
  symbol: string;            // Trading symbol
  side: 'BUY' | 'SELL';      // Trade side
  amount: number;            // Trade amount
  price: number;             // Trade price
  cost: number;              // Trade cost
  fee: number;               // Trading fee
  fee_currency: string;      // Fee currency
  timestamp: string;         // Trade timestamp (ISO string)
  realized_pnl?: number;     // Realized PnL (optional)
}

export interface RiskMetrics {
  portfolio_heat: number;                    // Portfolio heat percentage
  position_concentration: Record<string, number>; // Position concentration by symbol
  margin_utilization: number;               // Margin utilization percentage
  var_1d: number;                          // 1-day Value at Risk
  var_7d: number;                          // 7-day Value at Risk
  correlation_risk: number;                // Portfolio correlation risk
}

export interface TickerData {
  symbol: string;            // Trading symbol
  price: number;             // Current price
  price_change: number;      // Price change (absolute)
  price_change_percent: number; // Price change percentage
  high_price: number;        // 24h high price
  low_price: number;         // 24h low price
  volume: number;            // 24h volume
  quote_volume: number;      // 24h quote volume
  timestamp: string;         // Timestamp (ISO string)
}

export interface BinanceStatus {
  service: string;           // Service name
  status: string;            // Service status
  timestamp: string;         // Status timestamp
  testnet: boolean;          // Whether using testnet
  connection: string;        // Connection status
  account_status: {
    total_balance: number;
    equity: number;
    active_positions: number;
    margin_ratio: number;
  };
  api_limits: {
    weight: string;
    orders: string;
    raw_requests: string;
  };
}

// Request/Response types for API calls
export interface ClosePositionRequest {
  symbol: string;            // Symbol to close position for
  percentage?: number;       // Percentage of position to close (default: 100)
}

export interface PositionCloseResponse {
  success: boolean;
  order_id?: string;
  message: string;
  remaining_position?: number;
}

export interface CancelOrderRequest {
  symbol: string;            // Trading symbol
  order_id: string;          // Order ID to cancel
}

export interface OrderCancelResponse {
  success: boolean;
  message: string;
  cancelled_order?: Order;
}

// Filter types for API queries
export interface TradeHistoryFilters {
  symbol?: string;           // Filter by symbol
  limit?: number;            // Number of trades to return
  start_time?: string;       // Start time (ISO string)
  end_time?: string;         // End time (ISO string)
}

export interface OrderFilters {
  symbol?: string;           // Filter by symbol
}

// UI component props
export interface BinanceAccountPanelProps {
  refreshInterval?: number;  // Auto-refresh interval in milliseconds
  showRiskMetrics?: boolean; // Whether to show risk metrics section
  showAdvancedActions?: boolean; // Whether to show advanced action buttons
  theme?: 'light' | 'dark';  // Theme preference
}

// Real-time update types
export interface BinanceUpdate {
  type: 'ACCOUNT' | 'POSITION' | 'ORDER' | 'TRADE';
  data: BinanceAccount | Position | Order | Trade;
  timestamp: string;
}

export interface PositionUpdate extends Position {
  previous_pnl?: number;     // Previous PnL for change calculation
  change_indicator?: 'up' | 'down' | 'stable'; // Visual change indicator
}

// Portfolio summary for dashboard
export interface PortfolioSummary {
  account: BinanceAccount;
  positions: Position[];
  open_orders: Order[];
  recent_trades: Trade[];
  risk_metrics: RiskMetrics;
  last_updated: string;
}

// WebSocket message types
export interface BinanceWebSocketMessage {
  event: 'account_update' | 'position_update' | 'order_update' | 'price_update';
  data: any;
  timestamp: number;
}

export interface PriceUpdate {
  symbol: string;
  price: number;
  change: number;
  change_percent: number;
  timestamp: number;
}

// Export utility types
export type PositionSide = 'LONG' | 'SHORT';
export type OrderSide = 'BUY' | 'SELL';
export type OrderType = 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_MARKET' | 'TAKE_PROFIT' | 'TAKE_PROFIT_MARKET';
export type OrderStatus = 'NEW' | 'PARTIALLY_FILLED' | 'FILLED' | 'CANCELED' | 'REJECTED' | 'EXPIRED';

// Color coding for UI components
export interface ColorTheme {
  profit: string;            // Color for profitable positions/trades
  loss: string;              // Color for losing positions/trades
  neutral: string;           // Color for neutral/zero PnL
  long: string;              // Color for long positions
  short: string;             // Color for short positions
  buy: string;               // Color for buy orders
  sell: string;              // Color for sell orders
}

// Table column definitions
export interface TableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
  sortable?: boolean;
}

// Export default color theme
export const DEFAULT_COLOR_THEME: ColorTheme = {
  profit: '#4caf50',         // Green
  loss: '#f44336',           // Red
  neutral: '#757575',        // Grey
  long: '#2196f3',           // Blue
  short: '#ff9800',          // Orange
  buy: '#4caf50',            // Green
  sell: '#f44336'            // Red
};

// Utility type for API responses
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

// Error types
export interface BinanceError {
  code: number;
  message: string;
  timestamp: string;
}