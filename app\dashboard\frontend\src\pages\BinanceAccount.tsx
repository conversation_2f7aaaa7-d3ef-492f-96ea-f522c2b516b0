/**
 * Binance Account Page
 * 
 * Main page component for Binance Futures Testnet account management.
 * Integrates the BinanceAccountPanel component with page-level layout and navigation.
 * 
 * Features:
 * - Comprehensive account overview
 * - Real-time position and order management
 * - Transaction history and analysis
 * - Risk metrics and portfolio insights
 * - WebSocket real-time updates
 * 
 * Created: June 16, 2025
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Breadcrumbs,
  Link,
  Alert,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Paper,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  Home as HomeIcon,
  AccountBalance as AccountBalanceIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

import BinanceAccountPanel from '../components/BinanceAccountPanel';
import { apiClient } from '../services/api';

interface ServiceStatus {
  status: string;
  timestamp: string;
  testnet: boolean;
  connection: string;
  account_status: {
    total_balance: number;
    equity: number;
    active_positions: number;
    margin_ratio: number;
  };
}

const BinanceAccount: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [statusLoading, setStatusLoading] = useState<boolean>(true);
  const [showInfoDialog, setShowInfoDialog] = useState<boolean>(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState<boolean>(false);
  const [refreshInterval, setRefreshInterval] = useState<number>(5000);
  const [showRiskMetrics, setShowRiskMetrics] = useState<boolean>(true);
  const [showAdvancedActions, setShowAdvancedActions] = useState<boolean>(true);

  // Check service status on component mount
  useEffect(() => {
    checkServiceStatus();
  }, []);

  const checkServiceStatus = async () => {
    setStatusLoading(true);
    try {
      const response = await apiClient.get('/api/binance/status');
      setServiceStatus(response.data);
    } catch (error) {
      console.error('Failed to check Binance service status:', error);
      setServiceStatus(null);
    } finally {
      setStatusLoading(false);
    }
  };

  const getStatusChipColor = (status: string) => {
    switch (status) {
      case 'operational':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'offline':
        return 'error';
      default:
        return 'default';
    }
  };

  const handleBreadcrumbClick = (path: string) => {
    navigate(path);
  };

  const handleSaveSettings = () => {
    // Save settings to localStorage or backend
    localStorage.setItem('binance_settings', JSON.stringify({
      refreshInterval,
      showRiskMetrics,
      showAdvancedActions
    }));
    setShowSettingsDialog(false);
  };

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('binance_settings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        setRefreshInterval(settings.refreshInterval || 5000);
        setShowRiskMetrics(settings.showRiskMetrics !== false);
        setShowAdvancedActions(settings.showAdvancedActions !== false);
      } catch (error) {
        console.error('Error loading saved settings:', error);
      }
    }
  }, []);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header with breadcrumbs and status */}
      <Box mb={3}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            color="inherit"
            href="#"
            onClick={() => handleBreadcrumbClick('/dashboard')}
            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Dashboard
          </Link>
          <Typography
            color="textPrimary"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <AccountBalanceIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Binance Account
          </Typography>
        </Breadcrumbs>

        <Box display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Binance Futures Account
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              Real-time account management and trading interface
            </Typography>
          </Box>

          <Box display="flex" gap={1} alignItems="center">
            {/* Service Status Chip */}
            {!statusLoading && serviceStatus && (
              <Chip
                icon={serviceStatus.status === 'operational' ? <CheckCircleIcon /> : <WarningIcon />}
                label={`${serviceStatus.status} (${serviceStatus.testnet ? 'Testnet' : 'Live'})`}
                color={getStatusChipColor(serviceStatus.status)}
                variant="outlined"
              />
            )}

            {/* Action Buttons */}
            <Tooltip title="Account Information">
              <IconButton onClick={() => setShowInfoDialog(true)}>
                <InfoIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Settings">
              <IconButton onClick={() => setShowSettingsDialog(true)}>
                <SettingsIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Help">
              <IconButton onClick={() => window.open('https://testnet.binancefuture.com', '_blank')}>
                <HelpIcon />
              </IconButton>
            </Tooltip>

            <Button
              variant="outlined"
              onClick={checkServiceStatus}
              disabled={statusLoading}
            >
              {statusLoading ? 'Checking...' : 'Refresh Status'}
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Status Alerts */}
      {!statusLoading && !serviceStatus && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6">Service Unavailable</Typography>
          <Typography>
            Unable to connect to Binance service. Please check your configuration and try again.
            Make sure your API keys are properly configured and the service is running.
          </Typography>
        </Alert>
      )}

      {serviceStatus && serviceStatus.status !== 'operational' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6">Service Degraded</Typography>
          <Typography>
            Binance service is experiencing issues. Some features may be limited or delayed.
            Data updates might be slower than usual.
          </Typography>
        </Alert>
      )}

      {serviceStatus && serviceStatus.testnet && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6">Testnet Environment</Typography>
          <Typography>
            You are connected to Binance Futures Testnet. This is a simulation environment
            using virtual funds. No real trading is occurring.
          </Typography>
        </Alert>
      )}

      {/* Main Content */}
      {serviceStatus && (
        <BinanceAccountPanel
          refreshInterval={refreshInterval}
          showRiskMetrics={showRiskMetrics}
          showAdvancedActions={showAdvancedActions}
        />
      )}

      {/* Information Dialog */}
      <Dialog open={showInfoDialog} onClose={() => setShowInfoDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            Binance Account Information
            <IconButton onClick={() => setShowInfoDialog(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Service Status
              </Typography>
              {serviceStatus && (
                <Card variant="outlined">
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">
                          Status
                        </Typography>
                        <Typography variant="body1">
                          {serviceStatus.status}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">
                          Environment
                        </Typography>
                        <Typography variant="body1">
                          {serviceStatus.testnet ? 'Testnet' : 'Live Trading'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">
                          Connection
                        </Typography>
                        <Typography variant="body1">
                          {serviceStatus.connection}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">
                          Last Updated
                        </Typography>
                        <Typography variant="body1">
                          {new Date(serviceStatus.timestamp).toLocaleString()}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              )}
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Features
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <li>Real-time account balance and equity tracking</li>
                <li>Live position monitoring with PnL updates</li>
                <li>Order management and cancellation</li>
                <li>Complete transaction history</li>
                <li>Risk metrics and portfolio analysis</li>
                <li>WebSocket real-time data streaming</li>
                <li>Position closing and order cancellation</li>
                <li>CSV export functionality</li>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Safety Notice
              </Typography>
              <Alert severity="warning">
                <Typography variant="body2">
                  This interface provides direct access to your Binance Futures account.
                  {serviceStatus?.testnet ? 
                    ' You are using testnet with virtual funds - no real money is at risk.' :
                    ' Please exercise caution when placing or canceling orders as these actions affect real positions.'
                  }
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInfoDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog open={showSettingsDialog} onClose={() => setShowSettingsDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            Account Panel Settings
            <IconButton onClick={() => setShowSettingsDialog(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Display Options
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body1">
                    Show Risk Metrics
                  </Typography>
                  <Button
                    variant={showRiskMetrics ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setShowRiskMetrics(!showRiskMetrics)}
                  >
                    {showRiskMetrics ? 'Enabled' : 'Disabled'}
                  </Button>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body1">
                    Advanced Actions
                  </Typography>
                  <Button
                    variant={showAdvancedActions ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setShowAdvancedActions(!showAdvancedActions)}
                  >
                    {showAdvancedActions ? 'Enabled' : 'Disabled'}
                  </Button>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Refresh Settings
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="body2" color="textSecondary">
                  Auto-refresh interval (seconds)
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {[1000, 5000, 10000, 30000, 60000].map((interval) => (
                    <Button
                      key={interval}
                      variant={refreshInterval === interval ? 'contained' : 'outlined'}
                      size="small"
                      onClick={() => setRefreshInterval(interval)}
                    >
                      {interval === 1000 ? '1s' :
                       interval === 5000 ? '5s' :
                       interval === 10000 ? '10s' :
                       interval === 30000 ? '30s' : '1m'}
                    </Button>
                  ))}
                </Box>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSettingsDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveSettings} variant="contained">
            Save Settings
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BinanceAccount;