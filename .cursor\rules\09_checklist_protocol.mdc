---
description: 
globs: 
alwaysApply: true
---

# Rule: Implementation Checklist Protocol

## 1. Mandate for Checklists
Whenever <PERSON> is in PLAN mode and has formulated a plan to complete a significant User-defined Task or a Main Goal (as identified by the USER or from project documentation like `TaskChecklist.md`), an `IMPLEMENTATION CHECKLIST` MUST be generated as the final output of the PLAN mode. This checklist becomes the guiding document for the EXECUTE mode.

## 2. Checklist Formatting Requirements
The `IMPLEMENTATION CHECKLIST` MUST be formatted as a fenced code block, closely adhering to the layout exemplified in the USER's provided image.

**Reference Layout (Content is illustrative and for formatting guidance ONLY):**

```
IMPLEMENTATION CHECKLIST:
1.  [ ] Example step 1: Analyze requirements for [feature_name].
2.  [X] Example step 2: Draft initial design document [design_doc.md](mdc:cci_link_placeholder).
3.  [USER INPUT NEEDED] Example step 3: Choose technology for backend:
      a. Option Alpha (e.g., Python/Flask)
      b. Option Beta (e.g., Node.js/Express)
4.  [ ] Example step 4: Implement core logic in [core_logic.py](mdc:cci_link_placeholder).
    - Sub-task 4.1: Define data models.
    - Sub-task 4.2: Implement main processing function.
...
N.  [ ] Final example step: Deploy to staging environment.
```

### Key Formatting Elements:
*   **Main Header**: The checklist MUST begin with the exact line: `IMPLEMENTATION CHECKLIST:`.
*   **Numbering**: Each primary checklist item MUST be numbered sequentially (e.g., `1.`, `2.`, ..., `N.`).
*   **Status Indicators**: Use the following bracketed indicators for task status:
    *   `[ ]`: Task is pending / not yet started.
    *   `[X]`: Task is completed.
    *   `[!]`: Task is blocked or requires immediate attention/clarification.
    *   `[P]`: Task is partially completed or currently in progress.
*   **[USER INPUT NEEDED]**: Clearly mark steps requiring a decision or input from the USER with this prefix. Optional lettered sub-choices (a., b., c.) can follow.
*   **File References**: When a step pertains to a specific file, include the filename. If a CCI link is available or anticipated, use markdown link format: `[filename.ext](mdc:cci_link_or_path_placeholder)`.
*   **Indentation**: Sub-tasks or multi-line descriptions for a primary item should be indented to maintain clarity, as shown in the reference.
*   **Atomicity**: Each checklist item should ideally represent a single, manageable action or a small, logically grouped set of actions.

## 3. Content Guidance
*   The specific tasks, filenames, or decisions shown in any reference layout (like the one above or from the USER's image example) are for formatting and structural illustration PURPOSES ONLY.
*   The actual content of each new `IMPLEMENTATION CHECKLIST` (i.e., the tasks themselves) MUST be derived from the specific plan developed for the current Task or Main Goal being addressed.
*   The checklist should be comprehensive enough to allow another developer, or Cascade in a future session, to understand the planned steps and track progress effectively.

This rule is intended to ensure that all significant development efforts are systematically planned and tracked, enhancing clarity and continuity.