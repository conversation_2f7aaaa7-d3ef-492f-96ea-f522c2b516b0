{"test_results": {"frontend_files": {"app/dashboard/frontend/src/types/binance.ts": {"status": "exists", "size_bytes": 7922}, "app/dashboard/frontend/src/components/BinanceAccountPanel.tsx": {"status": "exists", "size_bytes": 35518}, "app/dashboard/frontend/src/pages/BinanceAccount.tsx": {"status": "exists", "size_bytes": 15172}}, "backend_files": {"app/api/routes/binance_routes.py": {"status": "exists", "size_bytes": 26161}, "app/dashboard/api/binance_websocket.py": {"status": "exists", "size_bytes": 17347}}, "binance_client": {"status": "success", "balance": 14948.********, "testnet": true}, "api_routes": {"/api/binance/health": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "/api/binance/status": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "/api/binance/account": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "/api/binance/positions": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "/api/binance/orders": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "/api/binance/trades?limit=10": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "/api/binance/risk-metrics": {"status": "error", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}}, "websocket": {"status": "failed", "error": "[Errno 111] Connect call failed ('127.0.0.1', 8000)"}}, "summary": {"start_time": "2025-06-16T09:56:25.362892", "end_time": "2025-06-16T09:56:31.474014", "duration_seconds": 6.111122, "testnet_mode": true}}