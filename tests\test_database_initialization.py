import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base, get_db, init_db

# Mock database for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Override get_db dependency for tests
def override_get_db():
    pass

def test_database_and_models_can_be_imported():
    """
    This test checks that the database and model modules can be imported
    without circular dependency errors.
    """
    try:
        from app import database
        from app import models
        assert True
    except ImportError as e:
        pytest.fail(f"Failed to import database or models due to ImportError: {e}") 