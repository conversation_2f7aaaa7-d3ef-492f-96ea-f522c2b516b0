"""
Market analysis module for the Strategy Selector.

This module contains the MarketAnalyzer class, which uses the technical_analysis
utility to analyze market conditions and determine which trading strategy is
most appropriate.
"""
import pandas as pd
import logging
from typing import Dict
from app.utils import technical_analysis as ta

logger = logging.getLogger(__name__)

class MarketAnalyzer:
    """Analyzes market conditions to determine optimal trading strategy."""

    @staticmethod
    def analyze_market_conditions(df: pd.DataFrame) -> Dict[str, float]:
        """
        Analyze overall market conditions using the technical_analysis utility.
        
        Args:
            df: DataFrame with OHLC and volume data.
            
        Returns:
            Dict[str, float]: Dictionary of market condition scores.
        """
        if df.empty:
            return {
                'volatility': 0.0,
                'trend': 0.0,
                'range_bound': 0.0,
                'volume': 0.0
            }
            
        try:
            volatility = ta.calculate_atr_volatility(df)
            trend = ta.calculate_trend_strength(df)
            range_bound = ta.detect_range_bound(df)
            volume = ta.analyze_volume(df)
            
            return {
                'volatility': volatility,
                'trend': trend,
                'range_bound': range_bound,
                'volume': volume
            }
        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}", exc_info=True)
            return {
                'volatility': 0.0,
                'trend': 0.0,
                'range_bound': 0.0,
                'volume': 0.0
            }
