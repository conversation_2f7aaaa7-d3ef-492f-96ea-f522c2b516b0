#!/usr/bin/env python3
"""
W&B Strategy Performance Tracking Service for Task 1.3.3
Implements comprehensive strategy performance tracking and attribution analysis.
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging
from statistics import mean, stdev
import uuid

from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

@dataclass
class StrategyMetrics:
    """Individual strategy performance metrics"""
    strategy_name: str
    symbol: str
    total_trades: int
    winning_trades: int
    win_rate: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    avg_trade_duration_minutes: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    current_position: float
    unrealized_pnl: float
    realized_pnl: float
    total_volume: float
    success_rate: float
    confidence_score: float
    timestamp: datetime

@dataclass
class PerformanceAttribution:
    """Performance attribution analysis"""
    symbol: str
    total_portfolio_return: float
    strategy_contributions: Dict[str, float]
    strategy_weights: Dict[str, float]
    active_return: float
    tracking_error: float
    information_ratio: float
    attribution_accuracy: float
    benchmark_return: float
    excess_return: float
    timestamp: datetime

@dataclass
class ComparisonMetrics:
    """Strategy comparison and ranking metrics"""
    ranking_by_return: List[Tuple[str, float]]
    ranking_by_sharpe: List[Tuple[str, float]]
    ranking_by_win_rate: List[Tuple[str, float]]
    correlation_matrix: Dict[str, Dict[str, float]]
    diversification_ratio: float
    ensemble_benefit: float
    risk_contribution: Dict[str, float]
    alpha_generation: Dict[str, float]
    timestamp: datetime

@dataclass
class RealTimeMetrics:
    """Real-time tracking metrics"""
    current_signals: Dict[str, Dict[str, Any]]
    live_positions: Dict[str, float]
    unrealized_pnl: Dict[str, float]
    portfolio_value: float
    total_exposure: float
    risk_metrics: Dict[str, float]
    execution_latency: Dict[str, float]
    signal_frequency: Dict[str, int]
    timestamp: datetime

class WandBStrategyTracker:
    """
    Comprehensive W&B integration for strategy performance tracking.
    
    Features:
    - Individual strategy metrics logging
    - Real-time performance attribution
    - Strategy comparison dashboards
    - Automated performance tracking
    - Risk and return analysis
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        wandb_config: Optional[Dict] = None,
        config: Optional[Dict] = None
    ):
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.wandb_config = wandb_config or self._default_wandb_config()
        self.config = config or self._default_config()
        
        # Cache keys
        self.STRATEGY_METRICS_KEY = "wandb_tracker:strategy_metrics"
        self.ATTRIBUTION_KEY = "wandb_tracker:attribution"
        self.COMPARISON_KEY = "wandb_tracker:comparison"
        self.REALTIME_KEY = "wandb_tracker:realtime"
        
        # Strategy tracking data
        self.strategy_history = {}
        self.performance_cache = {}
        self.experiment_id = str(uuid.uuid4())
        
        logger.info("W&B Strategy Performance Tracker initialized")
    
    def _default_wandb_config(self) -> Dict[str, Any]:
        """Default W&B configuration"""
        return {
            "project_name": "crypto_strategy_ensemble",
            "entity": "crypto_trading_team",
            "experiment_name": f"strategy_tracking_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "tags": ["strategy_ensemble", "real_time", "automated"],
            "log_frequency": 60,  # Log every 60 seconds
            "enable_real_time": True,
            "enable_comparison": True,
            "enable_attribution": True,
            "dashboard_update_interval": 300  # 5 minutes
        }
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for performance tracking"""
        return {
            "cache_ttl_metrics": 60,           # 1 minute for metrics
            "cache_ttl_attribution": 300,     # 5 minutes for attribution
            "cache_ttl_comparison": 600,      # 10 minutes for comparison
            "min_trades_for_analysis": 5,     # Minimum trades for meaningful analysis
            "benchmark_symbol": "BTCUSDT",    # Benchmark for comparison
            "risk_free_rate": 0.02,           # 2% annual risk-free rate
            "lookback_periods": [24, 168, 720], # 1 day, 1 week, 1 month (hours)
            "enable_real_time_logging": True,
            "enable_performance_alerts": True,
            "alert_thresholds": {
                "max_drawdown": 0.15,         # 15% max drawdown alert
                "min_sharpe": 0.5,            # Below 0.5 Sharpe ratio alert
                "min_win_rate": 0.45          # Below 45% win rate alert
            }
        }
    
    async def track_strategy_performance(
        self,
        strategy_name: str,
        symbol: str,
        trade_data: Dict[str, Any],
        market_data: Dict[str, Any]
    ) -> StrategyMetrics:
        """
        Track individual strategy performance with W&B integration.
        """
        try:
            # Calculate strategy metrics
            metrics = await self._calculate_strategy_metrics(
                strategy_name, symbol, trade_data, market_data
            )
            
            # Log to W&B using MCP
            await self._log_strategy_metrics_to_wandb(strategy_name, metrics)
            
            # Cache metrics for fast retrieval
            cache_key = f"{self.STRATEGY_METRICS_KEY}:{strategy_name}:{symbol}"
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_metrics"],
                json.dumps(asdict(metrics), default=str)
            )
            
            # Store in Supabase for historical analysis
            if self.supabase_service:
                await self._store_strategy_metrics(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Strategy performance tracking failed for {strategy_name}: {e}")
            return self._create_fallback_metrics(strategy_name, symbol)
    
    async def calculate_performance_attribution(
        self,
        symbol: str,
        strategy_returns: Dict[str, float],
        strategy_weights: Dict[str, float],
        benchmark_return: float
    ) -> PerformanceAttribution:
        """
        Calculate performance attribution analysis.
        """
        try:
            # Check cache first
            cache_key = f"{self.ATTRIBUTION_KEY}:{symbol}"
            cached_attribution = await self.redis_service.get(cache_key)
            
            if cached_attribution:
                attribution_data = json.loads(cached_attribution)
                cache_time = datetime.fromisoformat(attribution_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_attribution"]):
                    return PerformanceAttribution(**attribution_data)
            
            # Calculate portfolio return
            total_portfolio_return = sum(
                strategy_returns.get(strategy, 0) * weight
                for strategy, weight in strategy_weights.items()
            )
            
            # Calculate strategy contributions
            strategy_contributions = {
                strategy: strategy_returns.get(strategy, 0) * weight
                for strategy, weight in strategy_weights.items()
            }
            
            # Calculate active return and tracking error
            active_return = total_portfolio_return - benchmark_return
            excess_return = active_return
            
            # Simulate tracking error calculation (would use actual return series in production)
            tracking_error = np.std(list(strategy_returns.values())) * np.sqrt(252)
            information_ratio = active_return / tracking_error if tracking_error > 0 else 0
            
            # Calculate attribution accuracy
            attribution_sum = sum(strategy_contributions.values())
            attribution_accuracy = 1.0 - abs(attribution_sum - total_portfolio_return) / max(abs(total_portfolio_return), 0.001)
            
            attribution = PerformanceAttribution(
                symbol=symbol,
                total_portfolio_return=total_portfolio_return,
                strategy_contributions=strategy_contributions,
                strategy_weights=strategy_weights,
                active_return=active_return,
                tracking_error=tracking_error,
                information_ratio=information_ratio,
                attribution_accuracy=max(0.0, min(1.0, attribution_accuracy)),
                benchmark_return=benchmark_return,
                excess_return=excess_return,
                timestamp=datetime.now()
            )
            
            # Log to W&B
            await self._log_attribution_to_wandb(attribution)
            
            # Cache results
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_attribution"],
                json.dumps(asdict(attribution), default=str)
            )
            
            return attribution
            
        except Exception as e:
            logger.error(f"Performance attribution calculation failed: {e}")
            return self._create_fallback_attribution(symbol, strategy_weights)
    
    async def generate_strategy_comparison(
        self,
        strategy_metrics: Dict[str, StrategyMetrics]
    ) -> ComparisonMetrics:
        """
        Generate comprehensive strategy comparison metrics.
        """
        try:
            # Check cache
            cache_key = f"{self.COMPARISON_KEY}:all_strategies"
            cached_comparison = await self.redis_service.get(cache_key)
            
            if cached_comparison:
                comparison_data = json.loads(cached_comparison)
                cache_time = datetime.fromisoformat(comparison_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_comparison"]):
                    return ComparisonMetrics(**comparison_data)
            
            # Extract metrics for comparison
            strategy_names = list(strategy_metrics.keys())
            returns = {name: metrics.total_return for name, metrics in strategy_metrics.items()}
            sharpe_ratios = {name: metrics.sharpe_ratio for name, metrics in strategy_metrics.items()}
            win_rates = {name: metrics.win_rate for name, metrics in strategy_metrics.items()}
            
            # Rankings
            ranking_by_return = sorted(returns.items(), key=lambda x: x[1], reverse=True)
            ranking_by_sharpe = sorted(sharpe_ratios.items(), key=lambda x: x[1], reverse=True)
            ranking_by_win_rate = sorted(win_rates.items(), key=lambda x: x[1], reverse=True)
            
            # Correlation matrix (simplified for demonstration)
            correlation_matrix = {}
            for strategy1 in strategy_names:
                correlation_matrix[strategy1] = {}
                for strategy2 in strategy_names:
                    if strategy1 == strategy2:
                        correlation_matrix[strategy1][strategy2] = 1.0
                    else:
                        # Simulate correlation based on strategy types and performance
                        return1 = returns[strategy1]
                        return2 = returns[strategy2]
                        base_correlation = 0.3 + 0.4 * abs(return1 - return2) / (abs(return1) + abs(return2) + 0.001)
                        correlation_matrix[strategy1][strategy2] = min(0.95, max(0.05, base_correlation))
            
            # Diversification metrics
            avg_correlation = np.mean([
                correlation_matrix[s1][s2] 
                for s1 in strategy_names 
                for s2 in strategy_names 
                if s1 != s2
            ]) if len(strategy_names) > 1 else 0
            
            diversification_ratio = max(0.1, 1.0 - avg_correlation)
            
            # Ensemble benefit (improvement over best individual strategy)
            best_individual_return = max(returns.values()) if returns else 0
            equal_weight_return = mean(returns.values()) if returns else 0
            ensemble_benefit = equal_weight_return - best_individual_return
            
            # Risk contribution (simplified)
            total_risk = sum(abs(r) for r in returns.values())
            risk_contribution = {
                name: abs(returns[name]) / total_risk if total_risk > 0 else 0
                for name in strategy_names
            }
            
            # Alpha generation (excess return over benchmark)
            benchmark_return = 0.05  # Simplified 5% benchmark
            alpha_generation = {
                name: returns[name] - benchmark_return
                for name in strategy_names
            }
            
            comparison = ComparisonMetrics(
                ranking_by_return=ranking_by_return,
                ranking_by_sharpe=ranking_by_sharpe,
                ranking_by_win_rate=ranking_by_win_rate,
                correlation_matrix=correlation_matrix,
                diversification_ratio=diversification_ratio,
                ensemble_benefit=ensemble_benefit,
                risk_contribution=risk_contribution,
                alpha_generation=alpha_generation,
                timestamp=datetime.now()
            )
            
            # Log to W&B
            await self._log_comparison_to_wandb(comparison)
            
            # Cache results
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_comparison"],
                json.dumps(asdict(comparison), default=str)
            )
            
            return comparison
            
        except Exception as e:
            logger.error(f"Strategy comparison generation failed: {e}")
            return self._create_fallback_comparison()
    
    async def track_real_time_metrics(
        self,
        current_signals: Dict[str, Dict[str, Any]],
        positions: Dict[str, float],
        portfolio_value: float
    ) -> RealTimeMetrics:
        """
        Track real-time performance metrics.
        """
        try:
            # Calculate unrealized PnL for each strategy
            unrealized_pnl = {}
            for strategy, position in positions.items():
                # Simplified PnL calculation
                unrealized_pnl[strategy] = position * 0.02  # Assume 2% unrealized gain
            
            # Calculate portfolio metrics
            total_exposure = sum(abs(pos) for pos in positions.values())
            
            # Risk metrics
            risk_metrics = {
                "portfolio_var": portfolio_value * 0.02,  # 2% VaR
                "max_leverage": total_exposure / max(portfolio_value, 1),
                "beta": 1.1,  # Portfolio beta
                "volatility": 0.25  # 25% annualized volatility
            }
            
            # Execution latency (from signal generation to execution)
            execution_latency = {
                strategy: np.random.normal(50, 10)  # Mock latency in ms
                for strategy in current_signals.keys()
            }
            
            # Signal frequency tracking
            signal_frequency = {
                strategy: len(signals.get('history', []))
                for strategy, signals in current_signals.items()
            }
            
            real_time_metrics = RealTimeMetrics(
                current_signals=current_signals,
                live_positions=positions,
                unrealized_pnl=unrealized_pnl,
                portfolio_value=portfolio_value,
                total_exposure=total_exposure,
                risk_metrics=risk_metrics,
                execution_latency=execution_latency,
                signal_frequency=signal_frequency,
                timestamp=datetime.now()
            )
            
            # Log to W&B for real-time dashboard
            await self._log_realtime_to_wandb(real_time_metrics)
            
            # Store in Redis for fast access
            cache_key = f"{self.REALTIME_KEY}:current"
            await self.redis_service.setex(
                cache_key,
                30,  # 30 second TTL for real-time data
                json.dumps(asdict(real_time_metrics), default=str)
            )
            
            return real_time_metrics
            
        except Exception as e:
            logger.error(f"Real-time metrics tracking failed: {e}")
            return self._create_fallback_realtime_metrics()
    
    async def _calculate_strategy_metrics(
        self,
        strategy_name: str,
        symbol: str,
        trade_data: Dict[str, Any],
        market_data: Dict[str, Any]
    ) -> StrategyMetrics:
        """Calculate comprehensive strategy metrics"""
        
        # Extract trade information
        trades = trade_data.get('trades', [])
        signals = trade_data.get('signals', [])
        
        if not trades:
            return self._create_fallback_metrics(strategy_name, symbol)
        
        # Calculate basic metrics
        total_trades = len(trades)
        winning_trades = sum(1 for trade in trades if trade.get('pnl', 0) > 0)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Return calculation
        pnls = [trade.get('pnl', 0) for trade in trades]
        total_return = sum(pnls)
        
        # Risk metrics
        returns_series = np.array(pnls)
        if len(returns_series) > 1:
            sharpe_ratio = np.mean(returns_series) / (np.std(returns_series) + 0.001) * np.sqrt(252)
            
            # Max drawdown calculation
            cumulative_returns = np.cumsum(returns_series)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / (running_max + 0.001)
            max_drawdown = abs(np.min(drawdowns))
        else:
            sharpe_ratio = 0
            max_drawdown = 0
        
        # Trade analysis
        winning_pnls = [pnl for pnl in pnls if pnl > 0]
        losing_pnls = [pnl for pnl in pnls if pnl < 0]
        
        avg_win = np.mean(winning_pnls) if winning_pnls else 0
        avg_loss = np.mean(losing_pnls) if losing_pnls else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
        
        # Volume and duration
        total_volume = sum(trade.get('volume', 0) for trade in trades)
        durations = [trade.get('duration_minutes', 60) for trade in trades]
        avg_trade_duration = np.mean(durations) if durations else 60
        
        # Current position and unrealized PnL
        current_position = market_data.get('current_position', 0)
        current_price = market_data.get('price', 0)
        unrealized_pnl = current_position * current_price * 0.01  # Simplified
        
        # Confidence and success metrics
        confidence_scores = [signal.get('confidence', 0.5) for signal in signals]
        confidence_score = np.mean(confidence_scores) if confidence_scores else 0.5
        success_rate = win_rate  # Same as win rate for simplification
        
        return StrategyMetrics(
            strategy_name=strategy_name,
            symbol=symbol,
            total_trades=total_trades,
            winning_trades=winning_trades,
            win_rate=win_rate,
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            avg_trade_duration_minutes=avg_trade_duration,
            profit_factor=profit_factor,
            avg_win=avg_win,
            avg_loss=avg_loss,
            current_position=current_position,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=total_return,
            total_volume=total_volume,
            success_rate=success_rate,
            confidence_score=confidence_score,
            timestamp=datetime.now()
        )
    
    async def _log_strategy_metrics_to_wandb(
        self,
        strategy_name: str,
        metrics: StrategyMetrics
    ) -> None:
        """Log strategy metrics to W&B using MCP"""
        try:
            # In a real implementation, we would use the W&B MCP to log metrics
            # For now, we'll simulate the logging by storing structured data
            
            wandb_data = {
                'experiment_id': self.experiment_id,
                'strategy_name': strategy_name,
                'metrics': asdict(metrics),
                'step': int(datetime.now().timestamp()),
                'tags': self.wandb_config['tags'] + [strategy_name]
            }
            
            # Store in Redis as W&B log queue
            wandb_queue_key = f"wandb_logs:strategy_metrics:{strategy_name}"
            await self.redis_service.setex(
                wandb_queue_key,
                3600,  # 1 hour
                json.dumps(wandb_data, default=str)
            )
            
            logger.info(f"Logged strategy metrics for {strategy_name} to W&B queue")
            
        except Exception as e:
            logger.error(f"Failed to log strategy metrics to W&B: {e}")
    
    async def _log_attribution_to_wandb(self, attribution: PerformanceAttribution) -> None:
        """Log performance attribution to W&B"""
        try:
            wandb_data = {
                'experiment_id': self.experiment_id,
                'type': 'performance_attribution',
                'attribution': asdict(attribution),
                'step': int(datetime.now().timestamp())
            }
            
            wandb_queue_key = f"wandb_logs:attribution:{attribution.symbol}"
            await self.redis_service.setex(
                wandb_queue_key,
                3600,
                json.dumps(wandb_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to log attribution to W&B: {e}")
    
    async def _log_comparison_to_wandb(self, comparison: ComparisonMetrics) -> None:
        """Log strategy comparison to W&B"""
        try:
            wandb_data = {
                'experiment_id': self.experiment_id,
                'type': 'strategy_comparison',
                'comparison': asdict(comparison),
                'step': int(datetime.now().timestamp())
            }
            
            wandb_queue_key = "wandb_logs:comparison:all_strategies"
            await self.redis_service.setex(
                wandb_queue_key,
                3600,
                json.dumps(wandb_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to log comparison to W&B: {e}")
    
    async def _log_realtime_to_wandb(self, metrics: RealTimeMetrics) -> None:
        """Log real-time metrics to W&B"""
        try:
            wandb_data = {
                'experiment_id': self.experiment_id,
                'type': 'realtime_metrics',
                'metrics': asdict(metrics),
                'step': int(datetime.now().timestamp())
            }
            
            wandb_queue_key = "wandb_logs:realtime:current"
            await self.redis_service.setex(
                wandb_queue_key,
                60,  # 1 minute for real-time data
                json.dumps(wandb_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to log real-time metrics to W&B: {e}")
    
    async def _store_strategy_metrics(self, metrics: StrategyMetrics) -> None:
        """Store strategy metrics in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            # Convert metrics to dict and handle datetime serialization
            metrics_dict = asdict(metrics)
            if 'timestamp' in metrics_dict:
                metrics_dict['timestamp'] = metrics_dict['timestamp'].isoformat()
            
            metrics_data = {
                'strategy_name': metrics.strategy_name,
                'symbol': metrics.symbol,
                'action': 'TRACK_PERFORMANCE',
                'quantity': metrics.total_trades,
                'price': 0,  # Not applicable
                'timestamp': metrics.timestamp.isoformat(),
                'metadata': {
                    'strategy_metrics': metrics_dict,
                    'tracking_type': 'wandb_integration'
                }
            }
            
            await self.supabase_service.store_trade_execution(metrics_data)
            
        except Exception as e:
            logger.error(f"Failed to store strategy metrics: {e}")
    
    def _create_fallback_metrics(self, strategy_name: str, symbol: str) -> StrategyMetrics:
        """Create fallback metrics when calculation fails"""
        return StrategyMetrics(
            strategy_name=strategy_name,
            symbol=symbol,
            total_trades=0,
            winning_trades=0,
            win_rate=0.0,
            total_return=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            avg_trade_duration_minutes=0.0,
            profit_factor=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            current_position=0.0,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            total_volume=0.0,
            success_rate=0.0,
            confidence_score=0.0,
            timestamp=datetime.now()
        )
    
    def _create_fallback_attribution(
        self,
        symbol: str,
        strategy_weights: Dict[str, float]
    ) -> PerformanceAttribution:
        """Create fallback attribution when calculation fails"""
        return PerformanceAttribution(
            symbol=symbol,
            total_portfolio_return=0.0,
            strategy_contributions={strategy: 0.0 for strategy in strategy_weights.keys()},
            strategy_weights=strategy_weights,
            active_return=0.0,
            tracking_error=0.0,
            information_ratio=0.0,
            attribution_accuracy=0.0,
            benchmark_return=0.0,
            excess_return=0.0,
            timestamp=datetime.now()
        )
    
    def _create_fallback_comparison(self) -> ComparisonMetrics:
        """Create fallback comparison when calculation fails"""
        return ComparisonMetrics(
            ranking_by_return=[],
            ranking_by_sharpe=[],
            ranking_by_win_rate=[],
            correlation_matrix={},
            diversification_ratio=0.0,
            ensemble_benefit=0.0,
            risk_contribution={},
            alpha_generation={},
            timestamp=datetime.now()
        )
    
    def _create_fallback_realtime_metrics(self) -> RealTimeMetrics:
        """Create fallback real-time metrics when calculation fails"""
        return RealTimeMetrics(
            current_signals={},
            live_positions={},
            unrealized_pnl={},
            portfolio_value=0.0,
            total_exposure=0.0,
            risk_metrics={},
            execution_latency={},
            signal_frequency={},
            timestamp=datetime.now()
        )

# Utility functions for integration

async def create_wandb_strategy_tracker(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    wandb_config: Optional[Dict] = None,
    config: Optional[Dict] = None
) -> WandBStrategyTracker:
    """Factory function to create W&B strategy tracker"""
    
    from app.services.mcp.redis_service import RedisService
    from app.services.mcp.supabase_service import SupabaseService
    
    redis_service = RedisService(redis_url)
    
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return WandBStrategyTracker(
        redis_service=redis_service,
        supabase_service=supabase_service,
        wandb_config=wandb_config,
        config=config
    )

# Example usage
if __name__ == "__main__":
    # This would be used for testing the W&B strategy tracker
    pass