#!/usr/bin/env python3
"""
Test Automated Portfolio Manager - Task 1.2.3 Validation
Tests all requirements:
- Redis integration for sub-second performance
- Automated weight allocation from MLflow  
- Conflict resolution with cached signals
- Real-time data processing and execution
"""

import sys
import asyncio
import json
import numpy as np
import redis.asyncio as redis
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import AsyncMock, MagicMock
import logging

# Add project path
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test implementation with mocks
from dataclasses import dataclass
from app.strategies.automated_portfolio_manager import (
    AutomatedPortfolioManager, 
    AutomatedConfig,
    AutomatedMetrics,
    ConflictResolution
)

@dataclass
class MockMarketData:
    """Mock market data for testing"""
    symbol: str = "BTCUSDT"
    price: float = 50000.0
    volume: float = 1000000.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self):
        return {
            'symbol': self.symbol,
            'price': self.price,
            'volume': self.volume,
            'timestamp': self.timestamp.isoformat()
        }

@dataclass  
class MockTradeState:
    """Mock trade state for testing"""
    action: str
    quantity: float
    price: float
    symbol: str
    timestamp: datetime = None
    pnl: float = 0.0
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class MockBaseStrategy:
    """Mock base strategy for testing"""
    def __init__(self, name: str, default_action: str = "BUY", default_confidence: float = 0.7):
        self.name = name
        self.default_action = default_action
        self.default_confidence = default_confidence
    
    async def generate_signal(self, market_data):
        """Generate mock signal"""
        return MockSignal(
            action=self.default_action,
            quantity=100.0,
            price=market_data.price,
            confidence=self.default_confidence
        )

@dataclass
class MockSignal:
    """Mock signal for testing"""
    action: str
    quantity: float
    price: float
    confidence: float

class MockRedisService:
    """Mock Redis service for testing"""
    def __init__(self):
        self.data = {}
        self.get_call_count = 0
        self.set_call_count = 0
    
    async def get(self, key: str) -> str:
        self.get_call_count += 1
        return self.data.get(key)
    
    async def setex(self, key: str, ttl: int, value: str):
        self.set_call_count += 1
        self.data[key] = value
        return True

class MockMLflowService:
    """Mock MLflow service for testing"""
    def __init__(self):
        self.model_version = "v1.0.0"
        self.load_calls = 0
    
    async def load_production_model(self):
        self.load_calls += 1
        # Return mock model that returns normalized weights
        mock_model = MagicMock()
        mock_model.predict.return_value = np.array([[0.4, 0.35, 0.25]])  # 3 strategies
        return mock_model
    
    async def get_model_info(self, stage: str = "production"):
        return {
            'version': self.model_version,
            'stage': stage,
            'creation_timestamp': datetime.now().timestamp()
        }

class MockWeightOptimizer:
    """Mock weight optimizer for testing"""
    async def predict_weights(self, features):
        # Return mock weights for 3 strategies
        return np.array([0.4, 0.35, 0.25])

# Test functions

async def test_redis_integration():
    """Test Redis integration for caching and performance"""
    print("Testing Redis integration...")
    
    redis_service = MockRedisService()
    
    # Test caching
    test_key = "test:weights"
    test_data = json.dumps({'weights': {'strategy1': 0.5, 'strategy2': 0.5}})
    
    await redis_service.setex(test_key, 300, test_data)
    cached_data = await redis_service.get(test_key)
    
    assert cached_data == test_data
    assert redis_service.set_call_count == 1
    assert redis_service.get_call_count == 1
    
    print("✓ Redis integration tests passed")

async def test_automated_weight_allocation():
    """Test automated weight allocation from MLflow"""
    print("Testing automated weight allocation...")
    
    # Create mocks
    strategies = [
        MockBaseStrategy("GridStrategy"),
        MockBaseStrategy("TechnicalAnalysisStrategy"), 
        MockBaseStrategy("TrendFollowingStrategy")
    ]
    
    config = AutomatedConfig()
    redis_service = MockRedisService()
    mlflow_service = MockMLflowService()
    weight_optimizer = MockWeightOptimizer()
    
    # Create portfolio manager
    portfolio_manager = AutomatedPortfolioManager(
        config=config,
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service
    )
    
    # Test weight allocation
    market_data = MockMarketData()
    weights = await portfolio_manager._get_automated_weights(market_data)
    
    # Verify weights
    assert len(weights) == 3
    assert abs(sum(weights.values()) - 1.0) < 0.001  # Should sum to 1
    assert all(w >= 0 for w in weights.values())  # Should be non-negative
    assert mlflow_service.load_calls == 1
    
    print("✓ Automated weight allocation tests passed")

async def test_conflict_resolution():
    """Test conflict resolution with cached signals"""
    print("Testing conflict resolution...")
    
    # Create conflicting signals
    strategy_signals = {
        "GridStrategy": {"action": "BUY", "quantity": 100, "price": 50000, "confidence": 0.8},
        "TechnicalAnalysisStrategy": {"action": "SELL", "quantity": 120, "price": 49900, "confidence": 0.7},
        "TrendFollowingStrategy": {"action": "BUY", "quantity": 80, "price": 50100, "confidence": 0.6}
    }
    
    strategy_weights = {
        "GridStrategy": 0.4,
        "TechnicalAnalysisStrategy": 0.3,
        "TrendFollowingStrategy": 0.3
    }
    
    # Create portfolio manager with conflict resolution enabled
    config = AutomatedConfig(enable_conflict_resolution=True)
    redis_service = MockRedisService()
    mlflow_service = MockMLflowService()
    weight_optimizer = MockWeightOptimizer()
    
    portfolio_manager = AutomatedPortfolioManager(
        config=config,
        strategies=[],
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service
    )
    
    # Test conflict resolution
    market_data = MockMarketData()
    aggregated_signal, conflicts = await portfolio_manager._resolve_conflicts_and_aggregate(
        strategy_signals, strategy_weights, market_data
    )
    
    # Verify conflict detection and resolution
    assert len(conflicts) > 0  # Should detect conflicts
    assert aggregated_signal['action'] in ['BUY', 'SELL', 'HOLD']
    assert aggregated_signal['confidence'] > 0
    assert 'conflicts' in aggregated_signal
    
    print("✓ Conflict resolution tests passed")

async def test_signal_caching():
    """Test strategy signal caching for performance"""
    print("Testing signal caching...")
    
    strategies = [
        MockBaseStrategy("GridStrategy", "BUY", 0.8),
        MockBaseStrategy("TechnicalAnalysisStrategy", "BUY", 0.7)
    ]
    
    config = AutomatedConfig()
    redis_service = MockRedisService()
    mlflow_service = MockMLflowService()
    weight_optimizer = MockWeightOptimizer()
    
    portfolio_manager = AutomatedPortfolioManager(
        config=config,
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service
    )
    
    market_data = MockMarketData()
    
    # First call should generate fresh signals
    signals1 = await portfolio_manager._get_cached_strategy_signals(market_data)
    initial_get_calls = redis_service.get_call_count
    initial_set_calls = redis_service.set_call_count
    
    # Simulate cached signals by pre-populating Redis
    market_hash = portfolio_manager._generate_market_hash(market_data)
    for strategy_name in strategies:
        cache_key = f"automated:signals:{strategy_name.__class__.__name__}:{market_hash}"
        cache_data = {
            'signal': {'action': 'BUY', 'quantity': 100, 'price': 50000, 'confidence': 0.8},
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy_name.__class__.__name__
        }
        await redis_service.setex(cache_key, 30, json.dumps(cache_data))
    
    # Second call should use cached signals
    signals2 = await portfolio_manager._get_cached_strategy_signals(market_data)
    
    # Verify caching behavior
    assert len(signals1) == len(strategies)
    assert len(signals2) == len(strategies)
    assert redis_service.get_call_count > initial_get_calls  # Should have made cache lookups
    
    print("✓ Signal caching tests passed")

async def test_real_time_execution():
    """Test real-time execution with performance requirements"""
    print("Testing real-time execution...")
    
    strategies = [
        MockBaseStrategy("GridStrategy", "BUY", 0.8),
        MockBaseStrategy("TechnicalAnalysisStrategy", "BUY", 0.7),
        MockBaseStrategy("TrendFollowingStrategy", "HOLD", 0.5)
    ]
    
    config = AutomatedConfig(max_execution_time_ms=1000)  # 1 second target
    redis_service = MockRedisService()
    mlflow_service = MockMLflowService()
    weight_optimizer = MockWeightOptimizer()
    
    # Mock execution service
    mock_execution_service = AsyncMock()
    mock_execution_service.execute_trade.return_value = MockTradeState(
        action="BUY", quantity=100, price=50000, symbol="BTCUSDT"
    )
    
    portfolio_manager = AutomatedPortfolioManager(
        config=config,
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service,
        execution_service=mock_execution_service
    )
    
    # Test execution time
    market_data = MockMarketData()
    start_time = datetime.now()
    trades, metrics = await portfolio_manager.execute_automated_strategy(market_data)
    execution_time = (datetime.now() - start_time).total_seconds() * 1000
    
    # Verify performance requirements
    assert execution_time < config.max_execution_time_ms  # Should be under 1 second
    assert isinstance(metrics, AutomatedMetrics)
    assert metrics.execution_time_ms > 0
    
    print(f"✓ Real-time execution completed in {execution_time:.1f}ms (target: {config.max_execution_time_ms}ms)")

async def test_end_to_end_automation():
    """Test complete end-to-end automated execution"""
    print("Testing end-to-end automation...")
    
    strategies = [
        MockBaseStrategy("GridStrategy", "BUY", 0.8),
        MockBaseStrategy("TechnicalAnalysisStrategy", "BUY", 0.6),
        MockBaseStrategy("TrendFollowingStrategy", "SELL", 0.7)
    ]
    
    config = AutomatedConfig(
        enable_conflict_resolution=True,
        enable_parallel_execution=True,
        min_confidence_threshold=0.5
    )
    
    redis_service = MockRedisService()
    mlflow_service = MockMLflowService()
    weight_optimizer = MockWeightOptimizer()
    
    # Mock all services
    mock_execution_service = AsyncMock()
    mock_execution_service.execute_trade.return_value = MockTradeState(
        action="BUY", quantity=100, price=50000, symbol="BTCUSDT"
    )
    
    mock_supabase_service = AsyncMock()
    mock_wandb_service = AsyncMock()
    
    portfolio_manager = AutomatedPortfolioManager(
        config=config,
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service,
        execution_service=mock_execution_service,
        supabase_service=mock_supabase_service,
        wandb_service=mock_wandb_service
    )
    
    # Execute multiple cycles to test automation
    results = []
    for i in range(5):
        market_data = MockMarketData(price=50000 + i * 100)
        trades, metrics = await portfolio_manager.execute_automated_strategy(market_data)
        results.append({
            'trades': len(trades),
            'execution_time': metrics.execution_time_ms,
            'cache_hit_rate': metrics.cache_hit_rate
        })
        
        # Small delay to simulate real-time processing
        await asyncio.sleep(0.01)
    
    # Verify end-to-end results
    assert len(results) == 5
    assert all(r['execution_time'] > 0 for r in results)
    assert mock_execution_service.execute_trade.call_count > 0  # Should have executed trades
    
    # Calculate performance metrics
    avg_execution_time = np.mean([r['execution_time'] for r in results])
    max_execution_time = np.max([r['execution_time'] for r in results])
    
    print(f"✓ End-to-end automation: {len(results)} cycles, avg: {avg_execution_time:.1f}ms, max: {max_execution_time:.1f}ms")

async def test_portfolio_manager_integration():
    """Integration test of all portfolio manager components"""
    print("Testing portfolio manager integration...")
    
    # Test data
    market_data_stream = [
        MockMarketData(price=50000 + i * 50, volume=1000000 + i * 10000)
        for i in range(10)
    ]
    
    strategies = [
        MockBaseStrategy("GridStrategy", "BUY", 0.8),
        MockBaseStrategy("TechnicalAnalysisStrategy", "SELL", 0.7),
        MockBaseStrategy("TrendFollowingStrategy", "BUY", 0.6)
    ]
    
    config = AutomatedConfig(
        max_execution_time_ms=500,  # Aggressive target
        enable_conflict_resolution=True,
        enable_parallel_execution=True
    )
    
    redis_service = MockRedisService()
    mlflow_service = MockMLflowService()
    weight_optimizer = MockWeightOptimizer()
    
    portfolio_manager = AutomatedPortfolioManager(
        config=config,
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service
    )
    
    # Run integration test
    execution_times = []
    cache_operations = 0
    conflicts_detected = 0
    
    for market_data in market_data_stream:
        start_time = datetime.now()
        trades, metrics = await portfolio_manager.execute_automated_strategy(market_data)
        exec_time = (datetime.now() - start_time).total_seconds() * 1000
        
        execution_times.append(exec_time)
        cache_operations += redis_service.get_call_count + redis_service.set_call_count
        conflicts_detected += metrics.conflict_resolution_count
    
    # Verify integration results
    avg_time = np.mean(execution_times)
    sub_second_rate = sum(1 for t in execution_times if t < 1000) / len(execution_times)
    
    assert avg_time < 1000  # Average should be under 1 second
    assert sub_second_rate == 1.0  # All executions should be sub-second
    assert cache_operations > 0  # Should have used caching
    
    print(f"✓ Integration test: {len(market_data_stream)} executions, avg: {avg_time:.1f}ms, cache ops: {cache_operations}")

async def main():
    """Run all automated portfolio manager tests"""
    print("=" * 80)
    print("AUTOMATED PORTFOLIO MANAGER TESTS - Task 1.2.3 Validation")
    print("=" * 80)
    
    try:
        # Test individual components
        await test_redis_integration()
        await test_automated_weight_allocation()
        await test_conflict_resolution()
        await test_signal_caching()
        await test_real_time_execution()
        
        # Test end-to-end scenarios
        await test_end_to_end_automation()
        await test_portfolio_manager_integration()
        
        print("\n" + "=" * 80)
        print("🎉 ALL TESTS PASSED! Task 1.2.3 Requirements Validated:")
        print("✅ Redis integration for sub-second performance")
        print("✅ Automated weight allocation from MLflow") 
        print("✅ Conflict resolution with cached signals")
        print("✅ Real-time data processing and execution")
        print("✅ Portfolio manager with automation COMPLETE")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)