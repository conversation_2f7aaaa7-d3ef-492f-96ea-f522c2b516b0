"""Utility functions for the execution service."""
import logging
from app.services.execution.models import OrderStatus

logger = logging.getLogger(__name__)

class UtilsMixin:
    """Mixin for execution service utility functions."""
    
    def _map_exchange_status(self, exchange_status: str) -> OrderStatus:
        """Map exchange-specific status to our OrderStatus enum.
        
        This is a pass-through to the order manager's method.
        
        Args:
            exchange_status: The exchange-specific status string.
            
        Returns:
            The corresponding OrderStatus enum value.
        """
        return self.order_manager._map_exchange_status(exchange_status)
