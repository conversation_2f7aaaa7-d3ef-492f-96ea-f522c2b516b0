# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
coverage.xml
*.cover
.hypothesis/
.cache

# Documentation
docs/_build/
*.md

# Logs
*.log
logs/

# Environment variables
.env
.env.local
.env.*.local

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Database files
*.db
*.sqlite3

# Config files with secrets
config/secrets/
*.key
*.pem
*.p12

# Node modules (if any)
node_modules/

# Jupyter notebooks checkpoints
.ipynb_checkpoints/

# ML artifacts (will be mounted as volumes)
models/weights/
wandb/
mlruns/

# ZenML
.zenml/
zenml_artifacts/

# Backup files
*.bak
*.backup

# Large data files
data/
datasets/
*.csv
*.parquet
*.json
*.pickle
*.pkl

# Redis dumps
dump.rdb

# Test files that shouldn't be in production
tests/
pytest.ini

# Development only files
scripts/dev/
.editorconfig