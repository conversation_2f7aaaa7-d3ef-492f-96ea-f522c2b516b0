/**
 * Global Teardown for Dynamic Position Optimization E2E Tests
 * Task 2.2.2: Clean up test environment
 */

async function globalTeardown(config) {
  console.log('🧹 Cleaning up Dynamic Position Optimization E2E Test Environment...');
  
  const { chromium } = require('@playwright/test');
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Clean up test data
    console.log('🗑️  Cleaning up test data...');
    
    // Clear Redis test data
    try {
      await page.request.delete(
        `${config.projects[0].use.baseURL}/api/v1/test/redis/cleanup`,
        { timeout: 10000 }
      );
      console.log('✅ Redis test data cleaned');
    } catch (error) {
      console.log('⚠️  Redis cleanup failed:', error.message);
    }
    
    // Clear any test positions
    try {
      await page.request.delete(
        `${config.projects[0].use.baseURL}/api/v1/test/positions/cleanup`,
        { timeout: 10000 }
      );
      console.log('✅ Test positions cleaned');
    } catch (error) {
      console.log('⚠️  Position cleanup failed:', error.message);
    }
    
    // Generate performance report
    try {
      const response = await page.request.get(
        `${config.projects[0].use.baseURL}/api/v1/metrics/performance`,
        { timeout: 5000 }
      );
      if (response.ok()) {
        const metrics = await response.json();
        console.log('📊 Performance Metrics Summary:');
        console.log(`   • Average position calc time: ${metrics.avg_position_calc_time || 'N/A'}ms`);
        console.log(`   • Cache hit rate: ${metrics.cache_hit_rate || 'N/A'}%`);
        console.log(`   • Total requests: ${metrics.total_requests || 'N/A'}`);
      }
    } catch (error) {
      console.log('⚠️  Performance metrics retrieval failed:', error.message);
    }
    
  } catch (error) {
    console.log('⚠️  Teardown error:', error.message);
  } finally {
    await browser.close();
  }
  
  console.log('✅ E2E test environment cleanup complete!');
}

module.exports = globalTeardown;