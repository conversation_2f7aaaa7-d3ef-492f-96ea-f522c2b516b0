# Cascade AI Assistant - Core Rule Directives
# Version 2.0 (Modular)
# IMPORTANT: This file primarily loads detailed rules from the .cursor/rules/ and .cursor/templates/ directories.

## Primary Rule Source Declaration:
- You MUST load, parse, and strictly adhere to the content of all [.md](cci:7://file:///c:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/rules/09_checklist_protocol.md:0:0-0:0) files within the `c:\Users\<USER>\Documents\Programming\Crypto_App_V2\.cursor\rules\` directory as your primary operational guidelines. This includes, but is not limited to, `08_development_methodology.md` and `09_checklist_protocol.md`.
- You MUST also be aware of and utilize templates stored in `c:\Users\<USER>\Documents\Programming\Sports_A rb_V1\.cursor\templates\` as referenced in `c:\Users\<USER>\Documents\Programming\Crypto_App_V2\.cursor\rules\05_template_references.md`.

## Core Overrides & Global Reminders (apply universally):
- **Absolute Paths**: Always use full absolute paths for all file and directory references in tool calls and communications.
- **User Instruction Precedence**: User instructions take immediate precedence. If an instruction conflicts with established rules, comply with the user's request and gently note the standard protocol for future reference if appropriate.
- **Mode Adherence**: Strictly operate within the declared RIPER-5 mode. Mode declaration (`[MODE: MODE_NAME]`) is mandatory at the beginning of every response.
**- Development Methodology: Adhere to Test-Driven Development (TDD) as outlined in `c:\Users\<USER>\Documents\Programming\Crypto_App_V2\.cursor\rules\08_development_methodology.md`.**
**- Implementation Checklist Protocol: Adhere to the `IMPLEMENTATION CHECKLIST` protocol as outlined in `c:\Users\<USER>\Documents\Programming\Crypto_App_V2\.cursor\rules\09_checklist_protocol.md`.**

# (End of root .cursorrules. Detailed rules are in .cursor/rules/*.md)