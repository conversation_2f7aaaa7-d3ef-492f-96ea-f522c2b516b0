"""
Signal Aggregation Service for Strategy Ensemble System
Provides sub-second signal aggregation with Redis caching integration.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import numpy as np
import logging

from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)


@dataclass
class StrategySignal:
    """Individual strategy signal data structure."""
    strategy_name: str
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0.0 to 1.0
    quantity: float
    price: Optional[float]
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class AggregatedSignal:
    """Aggregated signal from multiple strategies."""
    final_action: str
    confidence: float
    weighted_quantity: float
    contributing_strategies: List[str]
    signal_hash: str
    aggregation_timestamp: datetime
    cache_hit: bool = False
    processing_time_ms: float = 0.0


@dataclass
class MarketSnapshot:
    """Current market conditions snapshot."""
    symbol: str
    price: float
    volume: float
    volatility: float
    timestamp: datetime
    additional_data: Optional[Dict[str, Any]] = None
    
    def to_hash(self) -> str:
        """Generate hash for caching purposes."""
        # Round values to avoid cache misses from tiny price differences
        rounded_data = {
            'symbol': self.symbol,
            'price': round(self.price, 2),
            'volume': round(self.volume, -3),  # Round to nearest 1000
            'volatility': round(self.volatility, 4),
            # Use minute-level timestamp for cache efficiency
            'minute': self.timestamp.strftime('%Y-%m-%d-%H-%M')
        }
        return RedisService().generate_hash(rounded_data)


class HighPerformanceSignalAggregator:
    """
    High-performance signal aggregation with sub-second response times.
    Uses Redis caching and optimized algorithms for real-time execution.
    """
    
    def __init__(
        self, 
        redis_service: RedisService,
        supabase_service: SupabaseService = None,
        cache_ttl: int = 30
    ):
        """
        Initialize signal aggregator.
        
        Args:
            redis_service: Redis caching service
            supabase_service: Optional Supabase for analytics
            cache_ttl: Cache TTL in seconds
        """
        self.redis = redis_service
        self.supabase = supabase_service
        self.cache_ttl = cache_ttl
        
        # Performance tracking
        self.aggregation_times = []
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Aggregation weights (can be updated dynamically)
        self.strategy_weights = {
            'GridStrategy': 0.33,
            'TechnicalAnalysisStrategy': 0.33,
            'TrendFollowingStrategy': 0.34
        }
        
        # Confidence thresholds
        self.min_confidence_threshold = 0.6
        self.conflict_resolution_threshold = 0.8
        
    async def aggregate_signals(
        self,
        strategy_signals: Dict[str, StrategySignal],
        market_snapshot: MarketSnapshot,
        weights: Optional[Dict[str, float]] = None
    ) -> AggregatedSignal:
        """
        Aggregate signals from multiple strategies with caching.
        
        Args:
            strategy_signals: Dictionary of strategy signals
            market_snapshot: Current market conditions
            weights: Optional strategy weights override
            
        Returns:
            Aggregated signal result
        """
        start_time = time.perf_counter()
        
        # Use provided weights or defaults
        current_weights = weights or self.strategy_weights
        
        # Generate cache key
        signals_hash = self._generate_signals_hash(strategy_signals, current_weights)
        market_hash = market_snapshot.to_hash()
        cache_key = f"aggregated:{signals_hash}:{market_hash}"
        
        # Try to get from cache first
        cached_result = await self.redis.get_cached_aggregated_signal(cache_key)
        if cached_result:
            self.cache_hits += 1
            processing_time = (time.perf_counter() - start_time) * 1000
            
            # Reconstruct AggregatedSignal from cached data
            aggregated = AggregatedSignal(**cached_result)
            aggregated.cache_hit = True
            aggregated.processing_time_ms = processing_time
            
            logger.debug(f"Cache hit for signal aggregation: {processing_time:.2f}ms")
            return aggregated
        
        # Cache miss - perform aggregation
        self.cache_misses += 1
        
        # Perform signal aggregation
        aggregated = await self._perform_aggregation(
            strategy_signals, market_snapshot, current_weights, signals_hash
        )
        
        # Cache the result
        await self.redis.cache_aggregated_signal(
            asdict(aggregated), cache_key, self.cache_ttl
        )
        
        processing_time = (time.perf_counter() - start_time) * 1000
        aggregated.processing_time_ms = processing_time
        self.aggregation_times.append(processing_time)
        
        logger.debug(f"Signal aggregation completed: {processing_time:.2f}ms")
        
        # Log to Supabase if available
        if self.supabase:
            asyncio.create_task(self._log_aggregation_metrics(aggregated, market_snapshot))
        
        return aggregated
    
    async def _perform_aggregation(
        self,
        strategy_signals: Dict[str, StrategySignal],
        market_snapshot: MarketSnapshot,
        weights: Dict[str, float],
        signals_hash: str
    ) -> AggregatedSignal:
        """
        Perform the actual signal aggregation logic.
        
        Args:
            strategy_signals: Strategy signals
            market_snapshot: Market conditions
            weights: Strategy weights
            signals_hash: Hash for identification
            
        Returns:
            Aggregated signal
        """
        if not strategy_signals:
            return AggregatedSignal(
                final_action="HOLD",
                confidence=0.0,
                weighted_quantity=0.0,
                contributing_strategies=[],
                signal_hash=signals_hash,
                aggregation_timestamp=datetime.now()
            )
        
        # Filter signals by confidence threshold
        valid_signals = {
            name: signal for name, signal in strategy_signals.items()
            if signal.confidence >= self.min_confidence_threshold
        }
        
        if not valid_signals:
            return AggregatedSignal(
                final_action="HOLD",
                confidence=0.0,
                weighted_quantity=0.0,
                contributing_strategies=[],
                signal_hash=signals_hash,
                aggregation_timestamp=datetime.now()
            )
        
        # Calculate weighted actions and confidence
        action_weights = {"BUY": 0.0, "SELL": 0.0, "HOLD": 0.0}
        total_confidence = 0.0
        total_quantity = 0.0
        contributing_strategies = []
        
        for strategy_name, signal in valid_signals.items():
            strategy_weight = weights.get(strategy_name, 0.0)
            if strategy_weight <= 0:
                continue
                
            # Weight the signal by strategy weight and confidence
            weighted_influence = strategy_weight * signal.confidence
            action_weights[signal.action] += weighted_influence
            
            total_confidence += weighted_influence
            total_quantity += signal.quantity * strategy_weight
            contributing_strategies.append(strategy_name)
        
        # Determine final action
        final_action = max(action_weights, key=action_weights.get)
        
        # Handle conflicting signals
        if self._has_signal_conflict(action_weights):
            final_action = await self._resolve_conflict(
                valid_signals, market_snapshot, weights
            )
        
        # Calculate final confidence
        max_possible_confidence = sum(weights.get(name, 0.0) for name in valid_signals.keys())
        final_confidence = (total_confidence / max_possible_confidence) if max_possible_confidence > 0 else 0.0
        
        # Adjust quantity based on market volatility
        volatility_adjusted_quantity = self._adjust_quantity_for_volatility(
            total_quantity, market_snapshot.volatility
        )
        
        return AggregatedSignal(
            final_action=final_action,
            confidence=final_confidence,
            weighted_quantity=volatility_adjusted_quantity,
            contributing_strategies=contributing_strategies,
            signal_hash=signals_hash,
            aggregation_timestamp=datetime.now()
        )
    
    def _has_signal_conflict(self, action_weights: Dict[str, float]) -> bool:
        """
        Detect if there are conflicting signals.
        
        Args:
            action_weights: Weighted action scores
            
        Returns:
            True if conflict detected
        """
        sorted_weights = sorted(action_weights.values(), reverse=True)
        if len(sorted_weights) < 2:
            return False
        
        # Check if top two actions are too close
        top_weight = sorted_weights[0]
        second_weight = sorted_weights[1]
        
        if top_weight == 0:
            return False
            
        ratio = second_weight / top_weight
        return ratio > self.conflict_resolution_threshold
    
    async def _resolve_conflict(
        self,
        signals: Dict[str, StrategySignal],
        market_snapshot: MarketSnapshot,
        weights: Dict[str, float]
    ) -> str:
        """
        Resolve conflicting signals using additional logic.
        
        Args:
            signals: Valid strategy signals
            market_snapshot: Current market conditions
            weights: Strategy weights
            
        Returns:
            Resolved action
        """
        # Strategy 1: Use highest confidence signal
        highest_confidence_signal = max(signals.values(), key=lambda s: s.confidence)
        
        # Strategy 2: Consider market volatility
        if market_snapshot.volatility > 0.03:  # High volatility
            # In high volatility, prefer HOLD unless very confident
            if highest_confidence_signal.confidence > 0.85:
                return highest_confidence_signal.action
            else:
                return "HOLD"
        
        # Strategy 3: Use recency and confidence combination
        recent_signals = [
            signal for signal in signals.values()
            if (datetime.now() - signal.timestamp).total_seconds() < 60
        ]
        
        if recent_signals:
            # Weight by both confidence and recency
            best_signal = max(
                recent_signals,
                key=lambda s: s.confidence * (1 - (datetime.now() - s.timestamp).total_seconds() / 60)
            )
            return best_signal.action
        
        return highest_confidence_signal.action
    
    def _adjust_quantity_for_volatility(self, base_quantity: float, volatility: float) -> float:
        """
        Adjust position quantity based on market volatility.
        
        Args:
            base_quantity: Base quantity from signal aggregation
            volatility: Market volatility
            
        Returns:
            Volatility-adjusted quantity
        """
        # Reduce quantity in high volatility environments
        volatility_factor = 1.0 - min(volatility * 10, 0.5)  # Max 50% reduction
        return base_quantity * volatility_factor
    
    def _generate_signals_hash(
        self, 
        signals: Dict[str, StrategySignal], 
        weights: Dict[str, float]
    ) -> str:
        """
        Generate hash for signal combination.
        
        Args:
            signals: Strategy signals
            weights: Strategy weights
            
        Returns:
            Hash string
        """
        hash_data = {
            'signals': {
                name: {
                    'action': signal.action,
                    'confidence': round(signal.confidence, 3),
                    'quantity': round(signal.quantity, 4)
                }
                for name, signal in signals.items()
            },
            'weights': {k: round(v, 3) for k, v in weights.items()}
        }
        
        return self.redis.generate_hash(hash_data)
    
    async def batch_process_signals(
        self,
        signal_batches: List[Tuple[Dict[str, StrategySignal], MarketSnapshot]],
        weights: Optional[Dict[str, float]] = None
    ) -> List[AggregatedSignal]:
        """
        Process multiple signal batches concurrently.
        
        Args:
            signal_batches: List of (signals, market_snapshot) tuples
            weights: Optional strategy weights
            
        Returns:
            List of aggregated signals
        """
        tasks = [
            self.aggregate_signals(signals, market, weights)
            for signals, market in signal_batches
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log them
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch signal processing failed for index {i}: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def update_strategy_weights(self, new_weights: Dict[str, float]) -> None:
        """
        Update strategy weights and clear related cache.
        
        Args:
            new_weights: New strategy weights
        """
        self.strategy_weights = new_weights.copy()
        
        # Clear aggregated signal cache since weights changed
        await self.redis.clear_cache("ensemble:signals:aggregated:*")
        
        logger.info(f"Updated strategy weights: {new_weights}")
    
    async def _log_aggregation_metrics(
        self, 
        aggregated: AggregatedSignal, 
        market_snapshot: MarketSnapshot
    ) -> None:
        """
        Log aggregation metrics to Supabase for analytics.
        
        Args:
            aggregated: Aggregated signal result
            market_snapshot: Market conditions
        """
        try:
            metrics_data = {
                'timestamp': aggregated.aggregation_timestamp.isoformat(),
                'final_action': aggregated.final_action,
                'confidence': aggregated.confidence,
                'weighted_quantity': aggregated.weighted_quantity,
                'contributing_strategies': aggregated.contributing_strategies,
                'processing_time_ms': aggregated.processing_time_ms,
                'cache_hit': aggregated.cache_hit,
                'market_symbol': market_snapshot.symbol,
                'market_price': market_snapshot.price,
                'market_volatility': market_snapshot.volatility
            }
            
            await self.supabase.store_aggregation_metrics(metrics_data)
            
        except Exception as e:
            logger.error(f"Failed to log aggregation metrics: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get aggregation performance statistics.
        
        Returns:
            Performance metrics dictionary
        """
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        avg_time = np.mean(self.aggregation_times) if self.aggregation_times else 0
        p95_time = np.percentile(self.aggregation_times, 95) if self.aggregation_times else 0
        
        return {
            'total_aggregations': total_requests,
            'cache_hit_rate': round(hit_rate, 2),
            'avg_processing_time_ms': round(avg_time, 2),
            'p95_processing_time_ms': round(p95_time, 2),
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on signal aggregation system.
        
        Returns:
            Health status dictionary
        """
        try:
            # Test Redis connectivity
            redis_stats = await self.redis.get_cache_stats()
            redis_healthy = bool(redis_stats)
            
            # Test aggregation with dummy data
            dummy_signal = StrategySignal(
                strategy_name="test",
                action="HOLD",
                confidence=0.5,
                quantity=0.0,
                price=100.0,
                timestamp=datetime.now()
            )
            
            dummy_market = MarketSnapshot(
                symbol="TEST",
                price=100.0,
                volume=1000000,
                volatility=0.02,
                timestamp=datetime.now()
            )
            
            start_time = time.perf_counter()
            await self.aggregate_signals({"test": dummy_signal}, dummy_market)
            test_time = (time.perf_counter() - start_time) * 1000
            
            performance_stats = self.get_performance_stats()
            
            return {
                'status': 'healthy' if redis_healthy and test_time < 100 else 'degraded',
                'redis_healthy': redis_healthy,
                'test_aggregation_time_ms': round(test_time, 2),
                'performance_stats': performance_stats,
                'redis_stats': redis_stats
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }