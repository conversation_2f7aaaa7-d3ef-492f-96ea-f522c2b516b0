# Market Regime Detection

## Overview

This improvement will implement explicit market regime detection to enhance strategy selection. Currently, the system analyzes market conditions (volatility, trend, range, volume) but doesn't explicitly classify market regimes. By implementing a regime detection system that identifies bull markets, bear markets, sideways markets, and high volatility regimes, we can better tailor strategy selection to the current market environment.

## Technical Specification

### Architecture

The system will consist of the following components:

1. **Regime Detector**: Identifies current market regime using multiple indicators
2. **Regime Classifier**: Classifies market into distinct regimes (bull, bear, sideways, volatile)
3. **Regime History Tracker**: Tracks regime changes and transitions
4. **Regime-Based Strategy Mapper**: Maps strategies to their optimal regimes
5. **Regime Transition Handler**: Manages strategy transitions during regime changes

### Technology Stack

- **Python Libraries**:
  - `pandas` and `numpy`: For data manipulation
  - `scikit-learn`: For classification algorithms
  - `statsmodels`: For statistical tests and time series analysis
  - `ta-lib`: For technical indicators
  - `hmmlearn`: For Hidden Markov Models (optional)

- **Database**:
  - Use existing PostgreSQL database for storing regime data
  - Add new tables for regime history and strategy-regime mappings

### Implementation Details

1. **Regime Classification Approach**:
   - **Bull Market**: Strong uptrend, low-to-moderate volatility, high volume
   - **Bear Market**: Strong downtrend, moderate-to-high volatility, high volume
   - **Sideways Market**: No clear trend, low volatility, low volume
   - **Volatile Market**: No clear trend, high volatility, variable volume

2. **Detection Methods**:
   - **Moving Average Convergence/Divergence (MACD)**: For trend detection
   - **Average True Range (ATR)**: For volatility measurement
   - **Bollinger Bands Width**: For volatility and range-bound detection
   - **Volume Profile**: For volume analysis
   - **Hidden Markov Models**: For regime state transitions (optional)

3. **Regime Transition Logic**:
   - Implement hysteresis to prevent frequent regime switching
   - Require confirmation periods for regime changes
   - Use probability-based regime classification

4. **Strategy-Regime Mapping**:
   - Map each strategy to its optimal regime(s)
   - Adjust strategy scores based on regime compatibility
   - Implement regime-specific strategy parameters

### Database Schema

```sql
CREATE TABLE market_regimes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    characteristics JSONB
);

CREATE TABLE regime_history (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    regime_id INTEGER REFERENCES market_regimes(id),
    confidence FLOAT,
    indicators JSONB
);

CREATE TABLE strategy_regime_mapping (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(50) NOT NULL,
    regime_id INTEGER REFERENCES market_regimes(id),
    compatibility_score FLOAT,
    optimal_parameters JSONB
);

CREATE TABLE regime_transitions (
    id SERIAL PRIMARY KEY,
    from_regime_id INTEGER REFERENCES market_regimes(id),
    to_regime_id INTEGER REFERENCES market_regimes(id),
    transition_count INTEGER DEFAULT 0,
    avg_duration FLOAT,
    last_seen TIMESTAMP WITH TIME ZONE
);
```

### File Structure

```
app/
├── regimes/
│   ├── __init__.py
│   ├── detector.py
│   ├── classifier.py
│   ├── history.py
│   ├── mapper.py
│   └── transitions.py
├── strategies/
│   ├── strategy_selector.py (modified)
│   └── ...
└── ...
```

### Configuration

Add the following to `app/config/settings.py`:

```python
# Market Regime Detection Settings
regime_detection_enabled: bool = Field(True, env='REGIME_DETECTION_ENABLED')
regime_confirmation_periods: int = Field(3, env='REGIME_CONFIRMATION_PERIODS')
regime_transition_threshold: float = Field(0.7, env='REGIME_TRANSITION_THRESHOLD')
regime_history_length: int = Field(100, env='REGIME_HISTORY_LENGTH')
regime_detection_timeframes: List[str] = Field(["1h", "4h", "1d"], env='REGIME_DETECTION_TIMEFRAMES')
hmm_enabled: bool = Field(False, env='HMM_ENABLED')
regime_score_weight: float = Field(0.4, env='REGIME_SCORE_WEIGHT')
```

### Modified Strategy Selector

The `StrategySelector` class will be modified to incorporate regime detection:

```python
async def select_strategy(self) -> Optional[str]:
    """Select the best strategy based on market conditions and regime.
    
    Returns:
        Optional[str]: The name of the selected strategy, or None if no strategy meets the criteria
    """
    # Get current market conditions
    market_conditions = await self.market_analyzer.analyze_market(self.symbol, self.timeframe)
    
    # Detect current market regime
    if settings.regime_detection_enabled:
        current_regime = await self.regime_detector.detect_regime(self.symbol, self.timeframe)
        self.logger.info(f"Current market regime: {current_regime['name']} (confidence: {current_regime['confidence']:.2f})")
    else:
        current_regime = None
    
    # Score each strategy
    strategy_scores = {}
    for strategy_name in self.available_strategies:
        # Get base score from market conditions
        base_score = self.strategy_scorer.score_strategy(strategy_name, market_conditions)
        
        # Adjust score based on regime compatibility if regime detection is enabled
        if settings.regime_detection_enabled and current_regime:
            regime_compatibility = await self.strategy_regime_mapper.get_compatibility(
                strategy_name, current_regime['id'])
            
            # Weighted average of base score and regime compatibility
            adjusted_score = (
                base_score * (1 - settings.regime_score_weight) + 
                regime_compatibility * settings.regime_score_weight
            )
        else:
            adjusted_score = base_score
        
        strategy_scores[strategy_name] = adjusted_score
    
    # Log strategy scores
    self.logger.info(f"Strategy scores: {', '.join([f'{k}={v:.2f}' for k, v in strategy_scores.items()])}")
    
    # Select the strategy with the highest score above the threshold
    selected_strategy = None
    highest_score = 0
    
    for strategy_name, score in strategy_scores.items():
        if score > highest_score and score >= self.min_strategy_score:
            highest_score = score
            selected_strategy = strategy_name
    
    return selected_strategy
```

### API Endpoints

Add the following endpoints to the API:

```python
@router.get("/regimes/current", response_model=RegimeResponse)
async def get_current_regime(symbol: str, timeframe: str = "1h"):
    """Get the current market regime for a symbol."""
    # Implementation

@router.get("/regimes/history", response_model=RegimeHistoryResponse)
async def get_regime_history(
    symbol: str,
    timeframe: str = "1h",
    limit: int = 100
):
    """Get the regime history for a symbol."""
    # Implementation

@router.get("/regimes/strategies", response_model=StrategyRegimeMappingResponse)
async def get_strategy_regime_mapping():
    """Get the mapping between strategies and regimes."""
    # Implementation

@router.get("/regimes/transitions", response_model=RegimeTransitionsResponse)
async def get_regime_transitions():
    """Get statistics about regime transitions."""
    # Implementation
```

## Potential Challenges and Mitigations

1. **Regime Classification Accuracy**:
   - **Challenge**: Accurately identifying market regimes is difficult
   - **Mitigation**: Use multiple indicators and confirmation periods

2. **Frequent Regime Switching**:
   - **Challenge**: Rapid switching between regimes can lead to excessive strategy changes
   - **Mitigation**: Implement hysteresis and confirmation periods

3. **Regime Transition Handling**:
   - **Challenge**: Strategy performance may suffer during regime transitions
   - **Mitigation**: Implement special handling for transition periods

4. **Historical Data Requirements**:
   - **Challenge**: Regime detection requires sufficient historical data
   - **Mitigation**: Use adaptive methods that work with varying data availability

5. **Parameter Sensitivity**:
   - **Challenge**: Regime detection may be sensitive to parameter choices
   - **Mitigation**: Implement adaptive parameter selection and validation

## Testing Strategy

1. **Unit Tests**:
   - Test regime detection algorithms
   - Verify regime classification logic
   - Test regime transition handling

2. **Integration Tests**:
   - Test integration with strategy selection
   - Verify database operations for regime history

3. **Backtesting**:
   - Test regime detection on historical data
   - Analyze regime transitions and durations
   - Compare strategy performance across different regimes

4. **Simulation Testing**:
   - Simulate different market regimes
   - Test system behavior during regime transitions
   - Verify strategy selection in each regime

## Deployment Strategy

1. **Phase 1: Development and Testing**
   - Implement regime detection algorithms
   - Test on historical data
   - Validate regime classifications

2. **Phase 2: Shadow Mode**
   - Run regime detection alongside current system
   - Log regime classifications without affecting trading
   - Analyze accuracy and stability

3. **Phase 3: Partial Integration**
   - Use regime detection for informational purposes
   - Gradually incorporate into strategy selection
   - Monitor impact on performance

4. **Phase 4: Full Deployment**
   - Fully integrate regime detection with strategy selection
   - Implement regime-specific strategy parameters
   - Continuously monitor and refine

## Task Checklist

- [ ] **Setup and Infrastructure**
  - [ ] Create regime module structure
  - [ ] Set up database tables for regimes
  - [ ] Configure environment variables and settings

- [ ] **Regime Detection Implementation**
  - [ ] Implement trend detection algorithms
  - [ ] Implement volatility measurement
  - [ ] Implement volume analysis
  - [ ] Create regime classification logic
  - [ ] Develop regime confidence calculation

- [ ] **Regime History and Tracking**
  - [ ] Implement regime history storage
  - [ ] Create regime transition detection
  - [ ] Develop regime statistics calculation
  - [ ] Implement regime visualization tools

- [ ] **Strategy-Regime Mapping**
  - [ ] Define optimal regimes for each strategy
  - [ ] Implement compatibility scoring
  - [ ] Create regime-specific strategy parameters
  - [ ] Develop adaptive mapping based on performance

- [ ] **Integration with Strategy Selection**
  - [ ] Modify strategy selector to use regime information
  - [ ] Implement regime-based score adjustment
  - [ ] Create regime transition handling
  - [ ] Develop fallback mechanisms

- [ ] **API and Dashboard Integration**
  - [ ] Create API endpoints for regime information
  - [ ] Develop dashboard visualizations for regimes
  - [ ] Implement regime history charts
  - [ ] Create regime transition alerts

- [ ] **Testing and Validation**
  - [ ] Develop unit tests for regime detection
  - [ ] Create integration tests for regime-based selection
  - [ ] Implement backtesting with regime detection
  - [ ] Conduct regime simulation testing

- [ ] **Deployment and Monitoring**
  - [ ] Set up monitoring for regime detection
  - [ ] Create alerting for regime transitions
  - [ ] Implement gradual rollout strategy
  - [ ] Develop regime accuracy metrics

- [ ] **Documentation and Knowledge Transfer**
  - [ ] Create technical documentation for regime detection
  - [ ] Develop user guide for regime interpretation
  - [ ] Document strategy-regime relationships
  - [ ] Create training materials for regime analysis

## Performance Metrics

The success of this improvement will be measured by:

1. **Strategy Selection Accuracy**: Improved selection of optimal strategies for each regime
2. **Regime Transition Handling**: Reduced performance degradation during regime transitions
3. **Overall Returns**: Increase in risk-adjusted returns across all market conditions
4. **Drawdown Reduction**: Lower maximum drawdowns, especially during bear markets
5. **Regime Prediction Accuracy**: Accuracy of regime classifications compared to expert analysis
6. **Adaptability**: Faster adaptation to changing market regimes
