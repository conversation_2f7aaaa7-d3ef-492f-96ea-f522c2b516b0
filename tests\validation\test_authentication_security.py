#!/usr/bin/env python3
"""
Authentication Security Test Suite
Tests the complete authentication flow and security improvements.
"""

import asyncio
import requests
import threading
import time
import json
import os
from datetime import datetime, timedelta

# Set required environment variables
os.environ['SECRET_KEY'] = 'development_secret_key_at_least_32_chars_long_for_security_test'
os.environ['BINANCE_API_KEY'] = 'test_key'
os.environ['BINANCE_API_SECRET'] = 'test_secret'

import uvicorn
from app.dashboard.main import app

class AuthenticationTester:
    def __init__(self):
        self.base_url = 'http://127.0.0.1:8002'
        self.server_thread = None
        
    def start_server(self):
        """Start the FastAPI server in a background thread."""
        def run_server():
            uvicorn.run(app, host='127.0.0.1', port=8002, log_level='warning')
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # Wait for server to start
        for _ in range(10):
            try:
                response = requests.get(f'{self.base_url}/')
                if response.status_code == 200:
                    print('✅ Server started successfully')
                    return True
            except:
                time.sleep(1)
        
        print('❌ Failed to start server')
        return False
    
    def test_login_flow(self):
        """Test the complete login flow."""
        print('\n🔐 Testing Login Flow...')
        
        # Test successful login
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = requests.post(f'{self.base_url}/api/token', data=login_data)
        
        if response.status_code != 200:
            print(f'❌ Login failed: {response.status_code} - {response.text}')
            return None
        
        token_data = response.json()
        print(f'✅ Login successful')
        print(f'  - Access token: {len(token_data.get("access_token", ""))} chars')
        print(f'  - Refresh token: {len(token_data.get("refresh_token", ""))} chars')
        print(f'  - Token type: {token_data.get("token_type")}')
        
        return token_data
    
    def test_refresh_token(self, refresh_token):
        """Test refresh token functionality."""
        print('\n🔄 Testing Refresh Token...')
        
        refresh_data = {'refresh_token': refresh_token}
        response = requests.post(f'{self.base_url}/api/refresh-token', json=refresh_data)
        
        if response.status_code != 200:
            print(f'❌ Refresh token failed: {response.status_code} - {response.text}')
            return None
        
        new_token_data = response.json()
        print('✅ Refresh token successful')
        print(f'  - New access token: {len(new_token_data.get("access_token", ""))} chars')
        print(f'  - New refresh token: {len(new_token_data.get("refresh_token", ""))} chars')
        
        return new_token_data
    
    def test_protected_endpoints(self, access_token):
        """Test protected endpoints with token."""
        print('\n🛡️ Testing Protected Endpoints...')
        
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test trading status endpoint
        response = requests.get(f'{self.base_url}/api/trading/status', headers=headers)
        if response.status_code == 200:
            print('✅ Trading status endpoint accessible')
        else:
            print(f'❌ Trading status failed: {response.status_code}')
        
        # Test account statistics endpoint
        response = requests.get(f'{self.base_url}/api/account/statistics', headers=headers)
        if response.status_code in [200, 404, 503]:  # 404/503 expected if no data
            print('✅ Account statistics endpoint accessible')
        else:
            print(f'❌ Account statistics failed: {response.status_code}')
    
    def test_unauthorized_access(self):
        """Test that protected endpoints reject unauthorized requests."""
        print('\n🚫 Testing Unauthorized Access...')
        
        # Test without token
        response = requests.get(f'{self.base_url}/api/trading/status')
        if response.status_code == 401:
            print('✅ Unauthorized request properly rejected')
        else:
            print(f'❌ Expected 401, got: {response.status_code}')
        
        # Test with invalid token
        headers = {'Authorization': 'Bearer invalid_token'}
        response = requests.get(f'{self.base_url}/api/trading/status', headers=headers)
        if response.status_code == 401:
            print('✅ Invalid token properly rejected')
        else:
            print(f'❌ Expected 401 for invalid token, got: {response.status_code}')
    
    def test_rate_limiting(self):
        """Test rate limiting on login attempts."""
        print('\n⏰ Testing Rate Limiting...')
        
        # Try multiple failed logins
        login_data = {'username': 'admin', 'password': 'wrong_password'}
        
        success_count = 0
        rate_limited = False
        
        for i in range(7):  # Try 7 attempts (limit is 5)
            response = requests.post(f'{self.base_url}/api/token', data=login_data)
            
            if response.status_code == 429:  # Too Many Requests
                rate_limited = True
                print(f'✅ Rate limiting triggered after {i} attempts')
                break
            elif response.status_code == 401:
                success_count += 1
            
            time.sleep(0.1)  # Small delay between attempts
        
        if rate_limited:
            print('✅ Rate limiting is working correctly')
        else:
            print('⚠️ Rate limiting not triggered (may be working with longer window)')
    
    def test_cors_headers(self):
        """Test CORS configuration."""
        print('\n🌐 Testing CORS Configuration...')
        
        # Test OPTIONS request
        response = requests.options(f'{self.base_url}/api/token')
        
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers'
        ]
        
        cors_ok = True
        for header in cors_headers:
            if header in response.headers:
                print(f'✅ {header}: {response.headers[header]}')
            else:
                print(f'❌ Missing CORS header: {header}')
                cors_ok = False
        
        if cors_ok:
            print('✅ CORS headers configured correctly')
    
    def test_invalid_credentials(self):
        """Test invalid credential handling."""
        print('\n🔒 Testing Invalid Credentials...')
        
        test_cases = [
            ('admin', 'wrong_password'),
            ('nonexistent', 'password'),
            ('', ''),
            ('admin', '')
        ]
        
        for username, password in test_cases:
            login_data = {'username': username, 'password': password}
            response = requests.post(f'{self.base_url}/api/token', data=login_data)
            
            if response.status_code == 401:
                print(f'✅ Invalid credentials rejected: {username}/*****')
            else:
                print(f'❌ Expected 401 for {username}, got: {response.status_code}')
    
    def run_all_tests(self):
        """Run all authentication security tests."""
        print('🚀 Starting Authentication Security Test Suite')
        print('=' * 50)
        
        if not self.start_server():
            return False
        
        try:
            # Test login flow
            token_data = self.test_login_flow()
            if not token_data:
                return False
            
            # Test refresh token
            new_token_data = self.test_refresh_token(token_data['refresh_token'])
            
            # Test protected endpoints
            self.test_protected_endpoints(token_data['access_token'])
            
            # Test unauthorized access
            self.test_unauthorized_access()
            
            # Test invalid credentials
            self.test_invalid_credentials()
            
            # Test rate limiting
            self.test_rate_limiting()
            
            # Test CORS
            self.test_cors_headers()
            
            print('\n' + '=' * 50)
            print('✅ Authentication Security Test Suite Completed')
            print('🔐 Key Security Features Validated:')
            print('  - JWT token authentication and validation')
            print('  - Refresh token implementation')
            print('  - Protected endpoint authorization')
            print('  - Rate limiting on login attempts')
            print('  - CORS security configuration')
            print('  - Invalid credential rejection')
            
            return True
            
        except Exception as e:
            print(f'❌ Test suite failed with error: {e}')
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    tester = AuthenticationTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)