# TradeStateService Implementation Plan

## Overview
Implement backend trade state service for real-time dashboard as per PRD (docs/next_phase_prd.md).

## Architecture
```mermaid
graph TD
    A[TradeStateService] -->|Aggregates| B[Open Positions]
    A -->|Calculates| C[P&L Metrics]
    A -->|Tracks| D[Strategy Performance]
    A -->|Monitors| E[Market Conditions]
    A -->|Persists| F[PostgreSQL]
    A -->|Broadcasts| G[/ws/dashboard WebSocket]
    
    B --> H[ManagedTrade Model]
    D --> I[StrategySelector]
    E --> J[MarketData API]
    F --> K[TradeRepository]
    G --> L[WebSocketHandler]
```

## Implementation Details

### 1. TradeStateService Class
**File**: `app/services/dashboard/trade_state_service.py`
```python
class TradeStateService:
    def __init__(self, db_session, exchange_client):
        self.db = TradeRepository(db_session)
        self.exchange_client = exchange_client
        self.connected_clients = set()
        
    async def aggregate_trade_state(self):
        return {
            "open_positions": await self.get_open_positions(),
            "strategy_performance": await self.get_strategy_performance(),
            "market_conditions": await self.get_market_conditions()
        }
    
    async def get_open_positions(self):
        # Get all open positions with P&L calculations
        pass
    
    async def get_strategy_performance(self):
        # Calculate performance metrics for each strategy
        pass
    
    async def get_market_conditions(self):
        # Collect market indicators (volatility, trend, volume)
        pass
    
    async def persist_state(self):
        # Save current state snapshot to database
        state = await self.aggregate_trade_state()
        pass
    
    async def broadcast_update(self):
        # Send state update to all connected WebSocket clients
        state = await self.aggregate_trade_state()
        for client in self.connected_clients:
            await client.send_json(state)
```

### 2. WebSocket Integration
**File**: `app/dashboard/api/websocket.py`
```python
from fastapi import WebSocket
from app.services.dashboard.trade_state_service import TradeStateService

class DashboardWebSocket:
    def __init__(self, db_session, exchange_client):
        self.service = TradeStateService(db_session, exchange_client)
        
    async def websocket_endpoint(self, websocket: WebSocket):
        await websocket.accept()
        self.service.connected_clients.add(websocket)
        try:
            while True:
                await self.service.broadcast_update()
                await asyncio.sleep(1)
        except WebSocketDisconnect:
            self.service.connected_clients.remove(websocket)
```

### 3. Database Persistence
**Modify**: `app/repositories/trade_repository.py`
```python
class TradeRepository:
    async def save_trade_state_snapshot(self, snapshot: dict):
        """Save trade state snapshot to database"""
        # Implementation to save JSON snapshot
        pass
```

### 4. Integration Tests
**File**: `tests/services/dashboard/test_trade_state_service.py`
```python
# Tests for:
# - P&L calculations
# - Strategy performance metrics
# - Market condition indicators
# - WebSocket broadcasting
# - Database persistence
```

## Implementation Checklist
1. Create `TradeStateService` core class
2. Implement WebSocket endpoint at `/ws/dashboard`
3. Add database persistence methods to `TradeRepository`
4. Create integration tests
5. Add periodic state persistence (every 5 minutes)
6. Add real-time broadcasting (every 1 second)