"""Exchange client interface for interacting with cryptocurrency exchanges."""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from enum import Enum

# Minimal Enums defined here since app/models was removed
class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    STOP_LOSS_LIMIT = "STOP_LOSS_LIMIT"
    TAKE_PROFIT_LIMIT = "TAKE_PROFIT_LIMIT"

class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"

class ExchangeClient(ABC):
    """Abstract base class for exchange clients.
    
    This class defines the interface that all exchange clients must implement.
    """
    
    @abstractmethod
    def get_account_balance(self) -> Dict[str, float]:
        """Get account balance for all assets.
        
        Returns:
            Dictionary mapping asset symbols to their balances.
        """
        pass
    
    @abstractmethod
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get current ticker information for a symbol.
        
        Args:
            symbol: The trading pair symbol.
            
        Returns:
            Dictionary with ticker information.
        """
        pass
    
    @abstractmethod
    def get_order_book(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get order book for a symbol.
        
        Args:
            symbol: The trading pair symbol.
            limit: The number of orders to retrieve.
            
        Returns:
            Dictionary with order book information.
        """
        pass
    
    @abstractmethod
    def get_recent_trades(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent trades for a symbol.
        
        Args:
            symbol: The trading pair symbol.
            limit: The number of trades to retrieve.
            
        Returns:
            List of dictionaries with trade information.
        """
        pass
    
    @abstractmethod
    def get_historical_klines(self,
                             symbol: str,
                             interval: str,
                             start_time: Optional[int] = None,
                             end_time: Optional[int] = None,
                             limit: int = 500) -> List[Dict[str, Any]]:
        """Get historical klines (candlesticks) for a symbol.
        
        Args:
            symbol: The trading pair symbol.
            interval: The interval (e.g., '1m', '1h', '1d').
            start_time: The start time in milliseconds.
            end_time: The end time in milliseconds.
            limit: The number of klines to retrieve.
            
        Returns:
            List of dictionaries with kline information.
        """
        pass
    
    @abstractmethod
    def place_order(self,
                   symbol: str,
                   order_type: Union[OrderType, str],
                   side: Union[OrderSide, str],
                   quantity: float,
                   price: Optional[float] = None,
                   stop_price: Optional[float] = None,
                   take_profit_price: Optional[float] = None, # Added for SL/TP orders
                   client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Place an order on the exchange.
        
        Args:
            symbol: The trading pair symbol.
            order_type: The order type.
            side: The order side.
            quantity: The order quantity.
            price: The order price (required for LIMIT orders).
            stop_price: The stop price (for STOP_LOSS*, TAKE_PROFIT* orders).
            take_profit_price: The take profit price (for TAKE_PROFIT* orders).
            client_order_id: The client order ID.
            
        Returns:
            Dictionary with order information.
        """
        pass
    
    @abstractmethod
    def cancel_order(self,
                    symbol: str,
                    order_id: Optional[str] = None,
                    client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Cancel an order on the exchange.
        
        Args:
            symbol: The trading pair symbol.
            order_id: The exchange order ID.
            client_order_id: The client order ID.
            
        Returns:
            Dictionary with cancellation information.
        """
        pass
    
    @abstractmethod
    def get_order(self,
                 order_id: Optional[str] = None,
                 client_order_id: Optional[str] = None,
                 symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get order information.
        
        Args:
            order_id: The exchange order ID.
            client_order_id: The client order ID.
            symbol: The trading pair symbol.
            
        Returns:
            Dictionary with order information.
        """
        pass
    
    @abstractmethod
    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all open orders.
        
        Args:
            symbol: The trading pair symbol (optional).
            
        Returns:
            List of dictionaries with order information.
        """
        pass
    
    @abstractmethod
    def get_all_orders(self,
                      symbol: str,
                      order_id: Optional[str] = None,
                      start_time: Optional[int] = None,
                      end_time: Optional[int] = None,
                      limit: int = 500) -> List[Dict[str, Any]]:
        """Get all orders for a symbol.
        
        Args:
            symbol: The trading pair symbol.
            order_id: The order ID to start from.
            start_time: The start time in milliseconds.
            end_time: The end time in milliseconds.
            limit: The number of orders to retrieve.
            
        Returns:
            List of dictionaries with order information.
        """
        pass
    
    @abstractmethod
    def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information.
        
        Returns:
            Dictionary with exchange information.
        """
        pass
    
    @abstractmethod
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get information for a specific symbol.
        
        Args:
            symbol: The trading pair symbol.
            
        Returns:
            Dictionary with symbol information.
        """
        pass 