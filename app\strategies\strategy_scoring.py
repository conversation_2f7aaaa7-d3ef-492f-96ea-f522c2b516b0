"""
Strategy scoring module for the Strategy Selector.

This module contains functions for scoring and selecting the optimal
trading strategy based on current market conditions.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd

logger = logging.getLogger(__name__)

# Default weights for strategy scoring
DEFAULT_WEIGHTS = {
    'grid': {
        'range_bound': 0.5,
        'volatility': 0.3,
        'trend': 0.2
    },
    'technical_analysis': {
        'volatility': 0.4,
        'volume': 0.3,
        'adaptability': 0.3
    },
    'trend_following': {
        'trend_strength': 0.5,
        'volatility': 0.2,
        'range_bound_penalty': 0.3
    }
}

class StrategyScorer:
    """Scores and selects trading strategies based on market conditions."""

    @staticmethod
    def score_grid_strategy(market_conditions: Dict[str, float], weights: Optional[Dict[str, float]] = None) -> float:
        """Score the Grid Trading strategy based on market conditions.

        Args:
            market_conditions: Dictionary of market condition scores
            weights: Optional dictionary of weights for different factors

        Returns:
            float: Strategy score (0.0 to 1.0)
        """
        try:
            # Use default weights if none provided
            if weights is None:
                weights = DEFAULT_WEIGHTS['grid']

            # Grid strategy works best in range-bound markets with moderate volatility
            range_bound_score = market_conditions.get('range_bound', 0.0)
            volatility_score = 1.0 - min(market_conditions.get('volatility', 0.0) / 5.0, 1.0)  # Lower volatility is better, but not too low
            trend_score = 1.0 - abs(market_conditions.get('trend', 0.0))  # Less trend is better

            # Weighted scoring
            grid_score = (
                range_bound_score * weights.get('range_bound', 0.5) +
                volatility_score * weights.get('volatility', 0.3) +
                trend_score * weights.get('trend', 0.2)
            )

            return grid_score
        except Exception as e:
            logger.error(f"Error scoring grid strategy: {e}")
            return 0.0

    @staticmethod
    def score_technical_analysis_strategy(market_conditions: Dict[str, float], weights: Optional[Dict[str, float]] = None) -> float:
        """Score the Technical Analysis strategy based on market conditions.

        Args:
            market_conditions: Dictionary of market condition scores
            weights: Optional dictionary of weights for different factors

        Returns:
            float: Strategy score (0.0 to 1.0)
        """
        try:
            # Use default weights if none provided
            if weights is None:
                weights = DEFAULT_WEIGHTS['technical_analysis']

            # TA strategy works well with moderate volatility and clear patterns
            volatility_score = min(market_conditions.get('volatility', 0.0) / 3.0, 1.0)  # Moderate volatility is good
            if volatility_score > 0.8:  # Too much volatility is bad
                volatility_score = 0.8 - (volatility_score - 0.8)

            volume_score = market_conditions.get('volume', 0.0)  # Higher volume is better

            # TA works in both trending and range-bound markets
            adaptability_score = 0.7  # Base score for adaptability

            # Weighted scoring
            ta_score = (
                volatility_score * weights.get('volatility', 0.4) +
                volume_score * weights.get('volume', 0.3) +
                adaptability_score * weights.get('adaptability', 0.3)
            )

            return ta_score
        except Exception as e:
            logger.error(f"Error scoring technical analysis strategy: {e}")
            return 0.0

    @staticmethod
    def score_trend_following_strategy(market_conditions: Dict[str, float], weights: Optional[Dict[str, float]] = None) -> float:
        """Score the Trend Following strategy based on market conditions.

        Args:
            market_conditions: Dictionary of market condition scores
            weights: Optional dictionary of weights for different factors

        Returns:
            float: Strategy score (0.0 to 1.0)
        """
        try:
            # Use default weights if none provided
            if weights is None:
                weights = DEFAULT_WEIGHTS['trend_following']

            # Trend following works best in strong trending markets
            trend_strength = abs(market_conditions.get('trend', 0.0))
            trend_direction = market_conditions.get('trend', 0.0)  # Positive for uptrend, negative for downtrend

            # Volatility should be moderate
            volatility_score = min(market_conditions.get('volatility', 0.0) / 4.0, 1.0)
            if volatility_score > 0.7:  # Too much volatility is bad
                volatility_score = 0.7 - (volatility_score - 0.7)

            # Range-bound is bad for trend following
            range_bound_penalty = 1.0 - market_conditions.get('range_bound', 0.0)

            # Weighted scoring
            trend_score = (
                trend_strength * weights.get('trend_strength', 0.5) +
                volatility_score * weights.get('volatility', 0.2) +
                range_bound_penalty * weights.get('range_bound_penalty', 0.3)
            )

            return trend_score
        except Exception as e:
            logger.error(f"Error scoring trend following strategy: {e}")
            return 0.0

    @staticmethod
    def select_best_strategy(market_conditions: Dict[str, float],
                            min_score_threshold: float = 0.4,
                            strategy_weights: Optional[Dict[str, Dict[str, float]]] = None) -> Tuple[str, Dict[str, float]]:
        """Select the best trading strategy based on market conditions.

        Args:
            market_conditions: Dictionary of market condition scores
            min_score_threshold: Minimum score required to select a strategy
            strategy_weights: Optional dictionary of weights for each strategy

        Returns:
            Tuple[str, Dict[str, float]]: Selected strategy name and all strategy scores
        """
        try:
            # Use default weights if none provided
            if strategy_weights is None:
                strategy_weights = {
                    'grid': None,
                    'technical_analysis': None,
                    'trend_following': None
                }

            # Score each strategy
            grid_score = StrategyScorer.score_grid_strategy(
                market_conditions,
                weights=strategy_weights.get('grid')
            )

            ta_score = StrategyScorer.score_technical_analysis_strategy(
                market_conditions,
                weights=strategy_weights.get('technical_analysis')
            )

            trend_score = StrategyScorer.score_trend_following_strategy(
                market_conditions,
                weights=strategy_weights.get('trend_following')
            )

            # Compile scores
            scores = {
                'grid': grid_score,
                'technical_analysis': ta_score,
                'trend_following': trend_score
            }

            # Find the highest scoring strategy
            best_strategy = max(scores, key=scores.get)
            best_score = scores[best_strategy]

            # Check if the best score meets the minimum threshold
            if best_score < min_score_threshold:
                logger.warning(f"Best strategy score ({best_score:.2f}) below threshold ({min_score_threshold})")
                return 'none', scores

            return best_strategy, scores
        except Exception as e:
            logger.error(f"Error selecting best strategy: {e}")
            return 'none', {'grid': 0.0, 'technical_analysis': 0.0, 'trend_following': 0.0}

    @staticmethod
    def get_strategy_parameters(strategy_name: str,
                               market_conditions: Dict[str, float],
                               base_params: Dict[str, Any]) -> Dict[str, Any]:
        """Get optimized parameters for the selected strategy.

        Args:
            strategy_name: Name of the selected strategy
            market_conditions: Dictionary of market condition scores
            base_params: Base parameters for the strategy

        Returns:
            Dict[str, Any]: Optimized strategy parameters
        """
        try:
            # Start with base parameters
            params = base_params.copy()

            # Adjust parameters based on market conditions
            volatility = market_conditions.get('volatility', 0.0)
            trend = market_conditions.get('trend', 0.0)

            if strategy_name == 'grid':
                # Adjust grid parameters based on volatility
                grid_size = params.get('grid_size', 10)
                if volatility > 3.0:
                    # Wider grid for higher volatility
                    params['grid_size'] = max(5, grid_size - 2)
                    params['grid_spacing'] = params.get('grid_spacing', 1.0) * 1.5
                elif volatility < 1.0:
                    # Tighter grid for lower volatility
                    params['grid_size'] = min(20, grid_size + 2)
                    params['grid_spacing'] = params.get('grid_spacing', 1.0) * 0.8

            elif strategy_name == 'technical_analysis':
                # Adjust TA parameters based on market conditions
                if abs(trend) > 0.7:
                    # Strong trend - focus on trend indicators
                    params['use_macd'] = True
                    params['use_rsi'] = True
                    params['rsi_threshold'] = 25 if trend < 0 else 75  # Adjust for trend direction
                else:
                    # Weaker trend - use more oscillators
                    params['use_stoch'] = True
                    params['use_rsi'] = True
                    params['rsi_threshold'] = 30 if trend < 0 else 70

            elif strategy_name == 'trend_following':
                # Adjust trend following parameters
                if abs(trend) > 0.8:
                    # Strong trend - be more aggressive
                    params['ma_fast'] = 10
                    params['ma_slow'] = 30
                    params['stop_loss_pct'] = params.get('stop_loss_pct', 2.0) * 0.8
                else:
                    # Weaker trend - be more conservative
                    params['ma_fast'] = 15
                    params['ma_slow'] = 50
                    params['stop_loss_pct'] = params.get('stop_loss_pct', 2.0) * 1.2

            return params
        except Exception as e:
            logger.error(f"Error getting strategy parameters: {e}")
            return base_params
