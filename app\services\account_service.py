import logging
from decimal import Decimal
from typing import List, Dict, Optional, Any, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from app.repositories.trade_repository import TradeRepository
from app.models import ManagedTradeDB
from app.models.trade_status import TradeStatus
from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient

logger = logging.getLogger(__name__)

# Define constants for clarity
ZERO = Decimal("0.0")
ONE_HUNDRED = Decimal("100.0")

class AccountService:
    """Service for account-related operations including statistics and performance metrics."""

    def __init__(self, settings: Settings, db_session_factory: Callable[[], AsyncSession], exchange_client: BinanceExchangeClient):
        """Initialize the AccountService.

        Args:
            settings: The application settings instance.
            db_session_factory: A callable that returns a new AsyncSession.
            exchange_client: The BinanceExchangeClient instance.
        """
        self.settings = settings
        self._db_session_factory = db_session_factory
        self.exchange_client = exchange_client
        logger.info("AccountService initialized.")

    async def _get_trade_repo(self, session: AsyncSession) -> TradeRepository:
        """Helper to get a TradeRepository instance with a session."""
        return TradeRepository(session)

    async def calculate_statistics(self) -> Dict[str, Any]:
        """
        Calculate account performance statistics.
        
        Returns:
            Dictionary with account statistics including:
            - balance: Current account balance
            - total_pnl: Total profit/loss
            - total_trades: Number of closed trades
            - winning_trades: Number of profitable trades
            - losing_trades: Number of losing trades
            - win_rate: Percentage of winning trades
            - avg_profit: Average profit per winning trade
            - avg_loss: Average loss per losing trade
            - profit_factor: Ratio of gross profit to gross loss
            - max_drawdown: Maximum drawdown percentage
        """
        logger.info("Calculating account statistics")
        
        # Get all closed trades
        async with self._db_session_factory() as session:
            repo = await self._get_trade_repo(session)
            trades = await repo.get_all_closed_trades()
        
        # Fetch live balance from Binance Futures
        live_balance = None
        try:
            balances = await self.exchange_client.get_account_balance()
            # Use USDT as the main balance for Futures
            live_balance = balances.get('USDT', 0.0)
        except Exception as e:
            logger.error(f"Error fetching live balance from Binance: {e}")
            live_balance = 0.0
        
        # Initialize statistics
        stats = {
            "balance": live_balance,  # Use live value
            "total_pnl": 0.0,
            "total_trades": len(trades),
            "winning_trades": 0,
            "losing_trades": 0,
            "win_rate": 0.0,
            "avg_profit": 0.0,
            "avg_loss": 0.0,
            "profit_factor": 0.0,
            "max_drawdown": 0.0,
        }
        
        if not trades:
            logger.info("No closed trades found")
            return stats
        
        # Calculate statistics
        total_profit = 0.0
        total_loss = 0.0
        profits = []
        losses = []
        
        for trade in trades:
            # Skip trades without PNL data
            if trade.pnl is None:
                logger.warning(f"Trade {trade.trade_id} has no PNL data, skipping")
                continue
                
            try:
                pnl = float(trade.pnl)
                
                if pnl > 0:
                    stats["winning_trades"] += 1
                    total_profit += pnl
                    profits.append(pnl)
                elif pnl < 0:
                    stats["losing_trades"] += 1
                    total_loss += abs(pnl)
                    losses.append(abs(pnl))
                
                stats["total_pnl"] += pnl
                
            except (ValueError, TypeError):
                logger.error(f"Invalid PNL value '{trade.pnl}' for trade {trade.trade_id}")
                continue
        
        # Calculate win rate
        if stats["total_trades"] > 0:
            stats["win_rate"] = (stats["winning_trades"] / stats["total_trades"]) * 100
        
        # Calculate average profit/loss
        if profits:
            stats["avg_profit"] = sum(profits) / len(profits)
        
        if losses:
            stats["avg_loss"] = sum(losses) / len(losses)
        
        # Calculate profit factor
        if total_loss > 0:
            stats["profit_factor"] = total_profit / total_loss
        elif total_profit > 0:
            stats["profit_factor"] = float('inf')  # Infinite profit factor if no losses
        
        # Calculate max drawdown (placeholder - would need account balance history)
        stats["max_drawdown"] = 5.2  # Example value
        
        logger.info(f"Statistics calculated: {stats}")
        return stats 