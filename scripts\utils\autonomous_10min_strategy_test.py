#!/usr/bin/env python3
"""
Autonomous 10-Minute Ensemble Strategy Test
Comprehensive test that runs independently for 10 minutes, manages all positions,
and generates a complete performance report.
"""

import asyncio
import os
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from binance.client import Client
from binance.enums import *
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('autonomous_trading_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutonomousEnsembleTest:
    """
    Autonomous 10-minute ensemble strategy test with comprehensive reporting
    """
    
    def __init__(self, client: Client):
        self.client = client
        self.start_time = time.time()
        self.test_duration = 6000  # 10 minutes
        self.position_size = 0.01  # Increased from 0.002 to overcome fee drag
        
        # Initialize tracking
        self.trades = []
        self.positions = {}
        self.signals_generated = []
        self.performance_metrics = {
            'start_time': datetime.now().isoformat(),
            'initial_balance': 0.0,
            'final_balance': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'total_fees': 0.0,
            'max_drawdown': 0.0,
            'peak_balance': 0.0,
            'execution_times': [],
            'strategy_performance': {
                'grid': {'signals': 0, 'trades': 0, 'pnl': 0.0, 'win_rate': 0.0},
                'technical': {'signals': 0, 'trades': 0, 'pnl': 0.0, 'win_rate': 0.0},
                'trend': {'signals': 0, 'trades': 0, 'pnl': 0.0, 'win_rate': 0.0}
            },
            'risk_metrics': {
                'max_position_size': 0.0,
                'avg_position_size': 0.0,
                'portfolio_heat': 0.0,
                'var_95': 0.0
            }
        }
        
        # Market data storage
        self.price_history = []
        self.indicators = {}
        
        # Risk parameters
        self.max_open_positions = 3
        self.stop_loss_pct = 0.02  # 2%
        self.take_profit_pct = 0.04  # 4% (2:1 R:R)
        self.max_portfolio_risk = 0.15  # 15%

    async def initialize_test(self):
        """Initialize the test environment"""
        logger.info("=" * 80)
        logger.info("🚀 INITIALIZING 10-MINUTE AUTONOMOUS ENSEMBLE TEST")
        logger.info("=" * 80)
        
        try:
            # Get initial account state
            account = self.client.futures_account()
            self.performance_metrics['initial_balance'] = float(account['availableBalance'])
            self.performance_metrics['peak_balance'] = self.performance_metrics['initial_balance']
            
            # Get current BTC price
            ticker = self.client.futures_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Log test parameters
            logger.info(f"Initial Balance: ${self.performance_metrics['initial_balance']:,.2f} USDT")
            logger.info(f"Position Size: {self.position_size} BTC (~${self.position_size * current_price:,.2f})")
            logger.info(f"Max Open Positions: {self.max_open_positions}")
            logger.info(f"Test Duration: {self.test_duration/60:.0f} minutes")
            logger.info(f"Stop Loss: {self.stop_loss_pct*100:.1f}%")
            logger.info(f"Take Profit: {self.take_profit_pct*100:.1f}%")
            
            # Close any existing positions
            await self.close_all_existing_positions()
            
            logger.info("✅ Test environment initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize test: {e}")
            return False

    async def close_all_existing_positions(self):
        """Close any existing positions before starting test"""
        try:
            positions = self.client.futures_position_information(symbol='BTCUSDT')
            
            for position in positions:
                position_amt = float(position['positionAmt'])
                if position_amt != 0:
                    side = SIDE_SELL if position_amt > 0 else SIDE_BUY
                    
                    self.client.futures_create_order(
                        symbol='BTCUSDT',
                        side=side,
                        type=ORDER_TYPE_MARKET,
                        quantity=abs(position_amt)
                    )
                    
                    logger.info(f"🔄 Closed existing position: {position_amt} BTC")
            
        except Exception as e:
            logger.error(f"Error closing existing positions: {e}")

    async def get_market_data(self):
        """Get market data and calculate indicators"""
        try:
            # Get recent klines
            klines = self.client.futures_klines(symbol='BTCUSDT', interval='1m', limit=100)
            
            prices = [float(k[4]) for k in klines]  # Close prices
            volumes = [float(k[5]) for k in klines]  # Volumes
            
            self.price_history = prices
            current_price = prices[-1]
            
            # Calculate indicators
            await self.calculate_technical_indicators(prices)
            
            return current_price
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return None

    async def calculate_technical_indicators(self, prices):
        """Calculate comprehensive technical indicators"""
        try:
            prices_array = np.array(prices)
            
            # Moving averages
            sma_20 = np.mean(prices_array[-20:]) if len(prices_array) >= 20 else prices_array[-1]
            sma_50 = np.mean(prices_array[-50:]) if len(prices_array) >= 50 else prices_array[-1]
            ema_12 = self.calculate_ema(prices_array, 12)
            ema_26 = self.calculate_ema(prices_array, 26)
            
            # RSI
            rsi = self.calculate_rsi(prices_array)
            
            # MACD
            macd = ema_12 - ema_26
            macd_signal = self.calculate_ema([macd], 9)
            
            # Bollinger Bands
            bb_middle = sma_20
            bb_std = np.std(prices_array[-20:]) if len(prices_array) >= 20 else 0
            bb_upper = bb_middle + (bb_std * 2)
            bb_lower = bb_middle - (bb_std * 2)
            
            # ATR for volatility
            atr = self.calculate_atr(prices_array)
            
            # Volume indicators
            avg_volume = np.mean(self.price_history[-20:]) if len(self.price_history) >= 20 else 0
            
            self.indicators = {
                'current_price': prices_array[-1],
                'sma_20': sma_20,
                'sma_50': sma_50,
                'ema_12': ema_12,
                'ema_26': ema_26,
                'rsi': rsi,
                'macd': macd,
                'macd_signal': macd_signal,
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'bb_middle': bb_middle,
                'atr': atr,
                'avg_volume': avg_volume
            }
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")

    def calculate_ema(self, prices, period):
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return prices[-1]
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI"""
        if len(prices) < period + 1:
            return 50
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_atr(self, prices, period=14):
        """Calculate Average True Range"""
        if len(prices) < 2:
            return prices[-1] * 0.02
        
        ranges = [abs(prices[i] - prices[i-1]) for i in range(1, len(prices))]
        return np.mean(ranges[-period:]) if len(ranges) >= period else np.mean(ranges)

    async def generate_ensemble_signal(self):
        """Generate trading signal from ensemble strategies"""
        try:
            if not self.indicators:
                return None, 0, "none"
            
            current_price = await self.get_market_data()
            if not current_price:
                return None, 0, "none"
            
            # Grid Strategy
            grid_signal, grid_confidence = self.grid_strategy()
            
            # Technical Analysis Strategy  
            tech_signal, tech_confidence = self.technical_strategy()
            
            # Trend Following Strategy
            trend_signal, trend_confidence = self.trend_strategy()
            
            # Record signals
            self.performance_metrics['strategy_performance']['grid']['signals'] += 1
            self.performance_metrics['strategy_performance']['technical']['signals'] += 1  
            self.performance_metrics['strategy_performance']['trend']['signals'] += 1
            
            # Ensemble decision with equal weighting
            signals = [grid_signal, tech_signal, trend_signal]
            confidences = [grid_confidence, tech_confidence, trend_confidence]
            
            # Weighted average
            weighted_signal = sum(s * c for s, c in zip(signals, confidences)) / sum(confidences) if sum(confidences) > 0 else 0
            avg_confidence = sum(confidences) / len(confidences)
            
            # Determine final signal
            if weighted_signal > 0.6 and avg_confidence > 0.65:
                return 'BUY', avg_confidence, 'ensemble'
            elif weighted_signal < -0.6 and avg_confidence > 0.65:
                return 'SELL', avg_confidence, 'ensemble'
            else:
                return 'HOLD', avg_confidence, 'ensemble'
                
        except Exception as e:
            logger.error(f"Error generating ensemble signal: {e}")
            return None, 0, "error"

    def grid_strategy(self):
        """Grid trading strategy logic"""
        try:
            current_price = self.indicators['current_price']
            bb_upper = self.indicators['bb_upper']
            bb_lower = self.indicators['bb_lower']
            bb_middle = self.indicators['bb_middle']
            
            # Calculate position within Bollinger Bands
            if bb_upper != bb_lower:
                position = (current_price - bb_lower) / (bb_upper - bb_lower)
            else:
                position = 0.5
            
            if position < 0.25:  # Near lower band - buy
                return 1.0, 0.8
            elif position > 0.75:  # Near upper band - sell
                return -1.0, 0.8
            elif 0.4 < position < 0.6:  # Near middle - neutral
                return 0.0, 0.3
            else:
                return 0.0, 0.5
                
        except Exception:
            return 0.0, 0.0

    def technical_strategy(self):
        """Technical analysis strategy logic"""
        try:
            rsi = self.indicators['rsi']
            macd = self.indicators['macd']
            macd_signal = self.indicators['macd_signal']
            current_price = self.indicators['current_price']
            sma_20 = self.indicators['sma_20']
            sma_50 = self.indicators['sma_50']
            
            signals = []
            
            # RSI signals
            if rsi < 30:
                signals.append(1.0)  # Oversold
            elif rsi > 70:
                signals.append(-1.0)  # Overbought
            else:
                signals.append(0.0)
            
            # MACD signals
            if macd > macd_signal:
                signals.append(0.6)  # Bullish momentum
            else:
                signals.append(-0.6)  # Bearish momentum
            
            # Moving average signals
            if current_price > sma_20 > sma_50:
                signals.append(0.8)  # Strong uptrend
            elif current_price < sma_20 < sma_50:
                signals.append(-0.8)  # Strong downtrend
            else:
                signals.append(0.0)
            
            avg_signal = np.mean(signals)
            confidence = min(0.9, abs(avg_signal) + 0.2)
            
            return avg_signal, confidence
            
        except Exception:
            return 0.0, 0.0

    def trend_strategy(self):
        """Trend following strategy logic"""
        try:
            current_price = self.indicators['current_price']
            ema_12 = self.indicators['ema_12']
            ema_26 = self.indicators['ema_26']
            atr = self.indicators['atr']
            
            # Trend strength
            trend_strength = abs(ema_12 - ema_26) / atr if atr > 0 else 0
            
            # Trend direction
            if current_price > ema_12 > ema_26:
                trend_signal = min(1.0, trend_strength / 2)
                confidence = min(0.9, trend_strength / 3 + 0.4)
            elif current_price < ema_12 < ema_26:
                trend_signal = -min(1.0, trend_strength / 2)
                confidence = min(0.9, trend_strength / 3 + 0.4)
            else:
                trend_signal = 0.0
                confidence = 0.3
            
            return trend_signal, confidence
            
        except Exception:
            return 0.0, 0.0

    async def execute_trade(self, signal: str, confidence: float):
        """Execute trade with comprehensive error handling"""
        try:
            if signal == 'HOLD' or len(self.positions) >= self.max_open_positions:
                return None
            
            start_time = time.time()
            current_price = self.indicators['current_price']
            
            # Calculate position size
            position_size = self.calculate_position_size(current_price, confidence)
            
            side = SIDE_BUY if signal == 'BUY' else SIDE_SELL
            
            logger.info(f"📊 Executing {signal}: {position_size} BTC at ${current_price:,.2f} (confidence: {confidence:.2f})")
            
            # Place order
            order = self.client.futures_create_order(
                symbol='BTCUSDT',
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=position_size
            )
            
            execution_time = (time.time() - start_time) * 1000
            self.performance_metrics['execution_times'].append(execution_time)
            
            # Calculate stop loss and take profit
            if signal == 'BUY':
                stop_loss = current_price * (1 - self.stop_loss_pct)
                take_profit = current_price * (1 + self.take_profit_pct)
            else:
                stop_loss = current_price * (1 + self.stop_loss_pct)
                take_profit = current_price * (1 - self.take_profit_pct)
            
            # Store trade
            trade_info = {
                'timestamp': datetime.now().isoformat(),
                'order_id': order['orderId'],
                'signal': signal,
                'quantity': position_size,
                'entry_price': float(order.get('avgPrice', current_price)),
                'confidence': confidence,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'execution_time_ms': execution_time,
                'status': 'OPEN'
            }
            
            self.trades.append(trade_info)
            self.positions[order['orderId']] = trade_info
            self.performance_metrics['total_trades'] += 1
            
            logger.info(f"✅ Trade executed: {order['orderId']} in {execution_time:.1f}ms")
            
            return trade_info
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None

    def calculate_position_size(self, current_price: float, confidence: float):
        """Calculate optimal position size based on risk management"""
        try:
            # Base position size adjusted by confidence
            base_size = self.position_size * confidence
            
            # Ensure minimum size
            return max(0.001, base_size)
            
        except Exception:
            return 0.001

    async def monitor_positions(self):
        """Monitor and manage open positions"""
        try:
            if not self.positions:
                return
            
            current_price = self.indicators.get('current_price')
            if not current_price:
                return
            
            positions_to_close = []
            
            for order_id, trade in self.positions.items():
                entry_price = trade['entry_price']
                signal = trade['signal']
                stop_loss = trade['stop_loss']
                take_profit = trade['take_profit']
                
                should_close = False
                close_reason = ""
                
                # Check stop loss and take profit
                if signal == 'BUY':
                    if current_price <= stop_loss:
                        should_close = True
                        close_reason = "STOP_LOSS"
                    elif current_price >= take_profit:
                        should_close = True
                        close_reason = "TAKE_PROFIT"
                else:  # SELL
                    if current_price >= stop_loss:
                        should_close = True
                        close_reason = "STOP_LOSS"
                    elif current_price <= take_profit:
                        should_close = True
                        close_reason = "TAKE_PROFIT"
                
                if should_close:
                    positions_to_close.append((order_id, close_reason))
            
            # Close positions that hit targets
            for order_id, reason in positions_to_close:
                await self.close_position(order_id, reason)
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")

    async def close_position(self, order_id: str, reason: str):
        """Close a specific position"""
        try:
            if order_id not in self.positions:
                return
            
            trade = self.positions[order_id]
            quantity = trade['quantity']
            signal = trade['signal']
            
            # Determine close side
            close_side = SIDE_SELL if signal == 'BUY' else SIDE_BUY
            
            logger.info(f"🔄 Closing position {order_id}: {reason}")
            
            # Place close order
            close_order = self.client.futures_create_order(
                symbol='BTCUSDT',
                side=close_side,
                type=ORDER_TYPE_MARKET,
                quantity=quantity
            )
            
            # Calculate PnL
            entry_price = trade['entry_price']
            exit_price = float(close_order.get('avgPrice', self.indicators.get('current_price', entry_price)))
            
            if signal == 'BUY':
                pnl = (exit_price - entry_price) * quantity
            else:
                pnl = (entry_price - exit_price) * quantity
            
            # Update trade record
            trade.update({
                'close_timestamp': datetime.now().isoformat(),
                'exit_price': exit_price,
                'close_reason': reason,
                'realized_pnl': pnl,
                'status': 'CLOSED'
            })
            
            # Update performance metrics
            self.performance_metrics['total_pnl'] += pnl
            
            if pnl > 0:
                self.performance_metrics['winning_trades'] += 1
            else:
                self.performance_metrics['losing_trades'] += 1
            
            # Remove from active positions
            del self.positions[order_id]
            
            logger.info(f"✅ Position closed: {reason}, PnL: ${pnl:.6f}")
            
        except Exception as e:
            logger.error(f"Error closing position {order_id}: {e}")

    async def close_all_positions_end_test(self):
        """Close all positions at end of test"""
        try:
            logger.info("🔄 Closing all positions - Test ending")
            
            for order_id in list(self.positions.keys()):
                await self.close_position(order_id, "TEST_END")
            
            # Also close any positions not tracked
            positions = self.client.futures_position_information(symbol='BTCUSDT')
            
            for position in positions:
                position_amt = float(position['positionAmt'])
                if position_amt != 0:
                    side = SIDE_SELL if position_amt > 0 else SIDE_BUY
                    
                    self.client.futures_create_order(
                        symbol='BTCUSDT',
                        side=side,
                        type=ORDER_TYPE_MARKET,
                        quantity=abs(position_amt)
                    )
                    
                    logger.info(f"🔄 Force closed untracked position: {position_amt} BTC")
            
        except Exception as e:
            logger.error(f"Error closing positions at test end: {e}")

    async def update_performance_metrics(self):
        """Update comprehensive performance metrics"""
        try:
            # Get current account balance
            account = self.client.futures_account()
            current_balance = float(account['availableBalance'])
            self.performance_metrics['final_balance'] = current_balance
            
            # Update peak balance and drawdown
            if current_balance > self.performance_metrics['peak_balance']:
                self.performance_metrics['peak_balance'] = current_balance
            
            drawdown = (self.performance_metrics['peak_balance'] - current_balance) / self.performance_metrics['peak_balance']
            self.performance_metrics['max_drawdown'] = max(self.performance_metrics['max_drawdown'], drawdown)
            
            # Calculate strategy-specific metrics
            for strategy in ['grid', 'technical', 'trend']:
                strategy_trades = [t for t in self.trades if t.get('status') == 'CLOSED']
                if strategy_trades:
                    strategy_pnl = sum(t.get('realized_pnl', 0) for t in strategy_trades)
                    winning_trades = sum(1 for t in strategy_trades if t.get('realized_pnl', 0) > 0)
                    
                    self.performance_metrics['strategy_performance'][strategy].update({
                        'trades': len(strategy_trades),
                        'pnl': strategy_pnl,
                        'win_rate': winning_trades / len(strategy_trades) if strategy_trades else 0
                    })
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        try:
            logger.info("=" * 80)
            logger.info("📊 GENERATING COMPREHENSIVE TEST REPORT")
            logger.info("=" * 80)
            
            # Update final metrics
            await self.update_performance_metrics()
            
            # Get real trade history from exchange
            trade_history = self.client.futures_account_trades(symbol='BTCUSDT', limit=100)
            
            # Calculate actual fees and PnL
            recent_trades = [t for t in trade_history if int(t['time']) > (self.start_time * 1000)]
            total_fees = sum(float(t['commission']) for t in recent_trades)
            total_exchange_pnl = sum(float(t['realizedPnl']) for t in recent_trades)
            
            self.performance_metrics['total_fees'] = total_fees
            
            # Calculate returns
            initial_balance = self.performance_metrics['initial_balance']
            final_balance = self.performance_metrics['final_balance']
            total_return = final_balance - initial_balance
            return_percentage = (total_return / initial_balance * 100) if initial_balance > 0 else 0
            
            # Generate report
            report = {
                'test_metadata': {
                    'start_time': self.performance_metrics['start_time'],
                    'end_time': datetime.now().isoformat(),
                    'duration_minutes': (time.time() - self.start_time) / 60,
                    'position_size': self.position_size,
                    'test_type': 'autonomous_ensemble_10min'
                },
                'financial_results': {
                    'initial_balance': initial_balance,
                    'final_balance': final_balance,
                    'total_return_usd': total_return,
                    'return_percentage': return_percentage,
                    'total_fees_paid': total_fees,
                    'net_trading_pnl': total_exchange_pnl,
                    'fee_adjusted_pnl': total_exchange_pnl - total_fees
                },
                'trading_statistics': {
                    'total_trades': len(self.trades),
                    'winning_trades': self.performance_metrics['winning_trades'],
                    'losing_trades': self.performance_metrics['losing_trades'],
                    'win_rate': self.performance_metrics['winning_trades'] / max(1, len([t for t in self.trades if t.get('status') == 'CLOSED'])),
                    'max_drawdown': self.performance_metrics['max_drawdown'],
                    'avg_execution_time': np.mean(self.performance_metrics['execution_times']) if self.performance_metrics['execution_times'] else 0
                },
                'strategy_performance': self.performance_metrics['strategy_performance'],
                'trades': self.trades,
                'exchange_trades': recent_trades
            }
            
            # Save report
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'autonomous_test_report_{timestamp}.json'
            
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            # Print summary
            self.print_test_summary(report)
            
            logger.info(f"📄 Comprehensive report saved: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return None

    def print_test_summary(self, report):
        """Print test summary to console"""
        print("\n" + "=" * 100)
        print("🏆 AUTONOMOUS 10-MINUTE ENSEMBLE STRATEGY TEST RESULTS")
        print("=" * 100)
        
        # Test metadata
        duration = report['test_metadata']['duration_minutes']
        print(f"\n⏱️  TEST SUMMARY:")
        print(f"{'Duration:':<25} {duration:.1f} minutes")
        print(f"{'Position Size:':<25} {self.position_size} BTC")
        print(f"{'Total Trades:':<25} {report['trading_statistics']['total_trades']}")
        
        # Financial results
        financial = report['financial_results']
        print(f"\n💰 FINANCIAL RESULTS:")
        print(f"{'Initial Balance:':<25} ${financial['initial_balance']:,.2f} USDT")
        print(f"{'Final Balance:':<25} ${financial['final_balance']:,.2f} USDT")
        print(f"{'Total Return:':<25} ${financial['total_return_usd']:,.6f} USDT")
        print(f"{'Return Percentage:':<25} {financial['return_percentage']:,.4f}%")
        print(f"{'Trading Fees:':<25} ${financial['total_fees_paid']:,.6f} USDT")
        print(f"{'Net Trading PnL:':<25} ${financial['net_trading_pnl']:,.6f} USDT")
        
        # Trading statistics
        stats = report['trading_statistics']
        print(f"\n📊 TRADING STATISTICS:")
        print(f"{'Win Rate:':<25} {stats['win_rate']*100:.1f}%")
        print(f"{'Winning Trades:':<25} {stats['winning_trades']}")
        print(f"{'Losing Trades:':<25} {stats['losing_trades']}")
        print(f"{'Max Drawdown:':<25} {stats['max_drawdown']*100:.2f}%")
        print(f"{'Avg Execution:':<25} {stats['avg_execution_time']:.1f}ms")
        
        # Strategy performance
        print(f"\n🎲 STRATEGY PERFORMANCE:")
        for strategy, perf in report['strategy_performance'].items():
            print(f"  {strategy.upper()}:")
            print(f"    Signals: {perf['signals']}, Trades: {perf['trades']}")
            print(f"    PnL: ${perf['pnl']:.6f}, Win Rate: {perf['win_rate']*100:.1f}%")
        
        print("=" * 100)

    async def run_autonomous_test(self):
        """Run the complete 10-minute autonomous test"""
        try:
            # Initialize test
            if not await self.initialize_test():
                return False
            
            logger.info("🚀 Starting 10-minute autonomous trading test...")
            
            # Main trading loop
            last_signal_time = 0
            signal_interval = 30  # Generate signal every 30 seconds
            
            while (time.time() - self.start_time) < self.test_duration:
                try:
                    # Update market data
                    current_price = await self.get_market_data()
                    
                    if not current_price:
                        await asyncio.sleep(5)
                        continue
                    
                    # Generate signals periodically
                    if time.time() - last_signal_time > signal_interval:
                        signal, confidence, strategy = await self.generate_ensemble_signal()
                        
                        if signal and signal != 'HOLD':
                            await self.execute_trade(signal, confidence)
                        
                        last_signal_time = time.time()
                    
                    # Monitor existing positions
                    await self.monitor_positions()
                    
                    # Update metrics
                    await self.update_performance_metrics()
                    
                    # Log progress
                    elapsed = (time.time() - self.start_time) / 60
                    remaining = (self.test_duration - (time.time() - self.start_time)) / 60
                    
                    if int(elapsed) % 2 == 0 and elapsed != getattr(self, '_last_log', 0):  # Log every 2 minutes
                        logger.info(f"⏱️  Progress: {elapsed:.1f}min elapsed, {remaining:.1f}min remaining, {len(self.positions)} open positions")
                        self._last_log = int(elapsed)
                    
                    await asyncio.sleep(5)  # Check every 5 seconds
                    
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    await asyncio.sleep(5)
            
            # Test completed - close all positions and generate report
            logger.info("⏰ 10-minute test completed - closing all positions")
            await self.close_all_positions_end_test()
            
            # Generate final report
            report_file = await self.generate_comprehensive_report()
            
            logger.info("✅ Autonomous test completed successfully")
            return report_file
            
        except Exception as e:
            logger.error(f"Critical error in autonomous test: {e}")
            traceback.print_exc()
            return False

async def main():
    """Main execution function"""
    try:
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        if not api_key or not api_secret:
            print("❌ API credentials not found")
            return False
        
        # Initialize client
        client = Client(api_key, api_secret, testnet=True)
        
        # Run autonomous test
        test = AutonomousEnsembleTest(client)
        result = await test.run_autonomous_test()
        
        if result:
            print(f"\n🎉 Autonomous test completed successfully!")
            print(f"📄 Report saved: {result}")
        else:
            print("❌ Autonomous test failed")
        
        return bool(result)
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)