#!/usr/bin/env python3
"""
Local MLflow MCP Server
Provides MLflow integration through Model Context Protocol
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional

import mlflow
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Resource, Tool


class MLflowMCPServer:
    def __init__(self):
        self.app = Server("mlflow-local")
        self.setup_tools()
        self.setup_resources()
    
    def setup_tools(self):
        @self.app.list_tools()
        async def list_tools() -> List[Tool]:
            return [
                Tool(
                    name="mlflow_list_experiments",
                    description="List all MLflow experiments",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                ),
                Tool(
                    name="mlflow_get_experiment",
                    description="Get MLflow experiment details",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "experiment_id": {"type": "string", "description": "Experiment ID"}
                        },
                        "required": ["experiment_id"]
                    }
                ),
                Tool(
                    name="mlflow_log_metric",
                    description="Log a metric to MLflow",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "run_id": {"type": "string", "description": "Run ID"},
                            "key": {"type": "string", "description": "Metric name"},
                            "value": {"type": "number", "description": "Metric value"},
                            "step": {"type": "integer", "description": "Step number", "default": 0}
                        },
                        "required": ["run_id", "key", "value"]
                    }
                )
            ]
        
        @self.app.call_tool()
        async def call_tool(name: str, arguments: Optional[Dict[str, Any]] = None) -> List[Any]:
            if arguments is None:
                arguments = {}
                
            try:
                if name == "mlflow_list_experiments":
                    experiments = mlflow.search_experiments()
                    return [{"experiments": [exp.to_dict() for exp in experiments]}]
                
                elif name == "mlflow_get_experiment":
                    exp_id = arguments["experiment_id"]
                    experiment = mlflow.get_experiment(exp_id)
                    return [{"experiment": experiment.to_dict() if experiment else None}]
                
                elif name == "mlflow_log_metric":
                    run_id = arguments["run_id"]
                    key = arguments["key"]
                    value = arguments["value"]
                    step = arguments.get("step", 0)
                    
                    with mlflow.start_run(run_id=run_id):
                        mlflow.log_metric(key, value, step)
                    
                    return [{"success": True, "message": f"Logged {key}={value} at step {step}"}]
                
                else:
                    return [{"error": f"Unknown tool: {name}"}]
                    
            except Exception as e:
                return [{"error": str(e)}]
    
    def setup_resources(self):
        @self.app.list_resources()
        async def list_resources() -> List[Resource]:
            return [
                Resource(
                    uri="mlflow://experiments",
                    name="MLflow Experiments",
                    description="List of all MLflow experiments"
                )
            ]


async def main():
    server = MLflowMCPServer()
    
    async with stdio_server() as (read_stream, write_stream):
        await server.app.run(
            read_stream,
            write_stream,
            server.app.create_initialization_options()
        )


if __name__ == "__main__":
    asyncio.run(main())