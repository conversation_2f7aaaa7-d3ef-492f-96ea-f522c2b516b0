#!/bin/bash

# Redis Trading MCP Installation Script
set -e

echo "🚀 Installing Redis Trading MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ required. Current version: $(node -v)"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the project
echo "🔨 Building TypeScript..."
npm run build

# Make executable
chmod +x dist/index.js

# Test Redis connection
echo "🔍 Testing Redis connection..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis is running and accessible"
    else
        echo "⚠️  Redis is not running. Please start Redis server:"
        echo "   docker run -d -p 6379:6379 redis:7-alpine"
        echo "   or"
        echo "   redis-server"
    fi
else
    echo "⚠️  redis-cli not found. Please ensure Redis is installed and running."
fi

# Create symlink for global access (optional)
if [ "$1" = "--global" ]; then
    echo "🔗 Creating global symlink..."
    npm link
    echo "✅ MCP server available globally as 'mcp-redis-trading'"
fi

echo "✅ Installation complete!"
echo ""
echo "📋 Next steps:"
echo "1. Ensure Redis is running: redis-server"
echo "2. Test the MCP server: npm run dev"
echo "3. Add to Claude configuration (see config-example.json)"
echo ""
echo "🔧 Configuration:"
echo "   Set REDIS_URL environment variable (default: redis://localhost:6379)"
echo "   Set REDIS_DB environment variable (default: 0)"