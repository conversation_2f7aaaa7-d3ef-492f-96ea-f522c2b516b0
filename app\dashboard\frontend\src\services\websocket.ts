/**
 * WebSocket service for real-time trade updates
 */

import { WS_URL } from '../config';

// Define event types
export enum WebSocketEventType {
  TRADE_UPDATE = 'trade_update',
  SYSTEM_STATUS = 'system_status',
  ERROR = 'error',
  // Auto Trading Events
  AUTO_TRADING_SESSION_STARTED = 'auto_trading_session_started',
  AUTO_TRADING_SESSION_STOPPED = 'auto_trading_session_stopped',
  AUTO_TRADING_SESSION_PAUSED = 'auto_trading_session_paused',
  AUTO_TRADING_SESSION_RESUMED = 'auto_trading_session_resumed',
  AUTO_TRADING_PERFORMANCE_UPDATE = 'auto_trading_performance_update',
  AUTO_TRADING_ALERT = 'auto_trading_alert',
  AUTO_TRADING_EMERGENCY_STOP = 'auto_trading_emergency_stop',
  AUTO_TRADING_CONNECTION_ESTABLISHED = 'auto_trading_connection_established',
  AUTO_TRADING_INITIAL_STATUS = 'auto_trading_initial_status',
  // Session Reports Events
  LIVE_SESSION_METRICS = 'live_session_metrics',
  SESSION_RISK_ALERT = 'session_risk_alert',
  SESSION_REPORT_UPDATE = 'session_report_update',
}

// Event type interfaces
export interface LiveSessionMetricsEvent {
  session_id: string;
  live_metrics: {
    current_pnl: number;
    unrealized_pnl: number;
    win_rate: number;
    active_trades: number;
    sharpe_ratio: number;
  };
  active_alerts?: Array<{
    level: string;
    message: string;
    timestamp: string;
  }>;
}

export interface SessionRiskAlertEvent {
  alert: {
    level: string;
    message: string;
  };
  timestamp: string;
}

export interface SessionReportUpdateEvent {
  session_id: string;
  status: string;
}

// Define event data interfaces
export interface TradeUpdateEvent {
  trade_id: string;
  status: string;
  symbol: string;
  entry_side: string;
  entry_price: number | null;
  entry_qty: number | null;
  sl_price: number | null;
  tp_price: number | null;
  timestamp: string;
  exit_price?: number | null;
  current_price?: number;
}

export interface SystemStatusEvent {
  status: 'online' | 'offline' | 'degraded';
  message: string;
}

export interface ErrorEvent {
  code: string;
  message: string;
}

// Define event handler types
export type TradeUpdateHandler = (data: TradeUpdateEvent) => void;
export type SystemStatusHandler = (data: SystemStatusEvent) => void;
export type ErrorHandler = (data: ErrorEvent) => void;
export type LiveSessionMetricsHandler = (data: LiveSessionMetricsEvent) => void;
export type SessionRiskAlertHandler = (data: SessionRiskAlertEvent) => void;
export type SessionReportUpdateHandler = (data: SessionReportUpdateEvent) => void;

// WebSocket service class
export class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second delay
  private reconnectTimer: number | null = null;
  private isConnecting = false;
  private isConnected = false;
  private eventHandlers: Record<string, Function[]> = {};

  /**
   * Connect to the WebSocket server
   */
  public connect(): void {
    if (this.socket || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      // Use the WebSocket URL from config
      const wsUrl = `${WS_URL}/trades`;

      console.log(`Connecting to WebSocket at ${wsUrl}`);
      this.socket = new WebSocket(wsUrl);

      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Add an event handler
   */
  public on(eventType: string, handler: Function): void {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  /**
   * Remove an event handler
   */
  public off(eventType: string, handler: Function): void {
    if (!this.eventHandlers[eventType]) {
      return;
    }
    const index = this.eventHandlers[eventType].indexOf(handler);
    if (index !== -1) {
      this.eventHandlers[eventType].splice(index, 1);
    }
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    console.log('WebSocket connection established');
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;

    // Notify listeners of system status
    const statusEvent: SystemStatusEvent = {
      status: 'online',
      message: 'Connected to server',
    };
    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      if (data.type === 'trade_update') {
        this.notifyHandlers(WebSocketEventType.TRADE_UPDATE, data.data);
      } else if (data.type === 'system_status') {
        this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, data.data);
      } else if (data.type === 'error') {
        this.notifyHandlers(WebSocketEventType.ERROR, data.data);
      } else {
        console.warn('Unknown WebSocket message type:', data.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error, event.data);
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
    this.socket = null;
    this.isConnected = false;
    this.isConnecting = false;

    // Notify listeners of system status
    const statusEvent: SystemStatusEvent = {
      status: 'offline',
      message: `Disconnected from server: ${event.reason || 'Connection closed'}`,
    };
    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);

    // Attempt to reconnect if the close was unexpected
    if (event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(event: Event): void {
    console.error('WebSocket error:', event);

    // Notify listeners of error
    const errorEvent: ErrorEvent = {
      code: 'connection_error',
      message: 'WebSocket connection error',
    };
    this.notifyHandlers(WebSocketEventType.ERROR, errorEvent);
  }

  /**
   * Schedule a reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Maximum reconnection attempts reached');

      // Notify listeners of system status
      const statusEvent: SystemStatusEvent = {
        status: 'offline',
        message: 'Failed to reconnect to server after multiple attempts',
      };
      this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);

      return;
    }

    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
    }

    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);
    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);

    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  /**
   * Notify all handlers of an event
   */
  private notifyHandlers(eventType: string, data: any): void {
    if (!this.eventHandlers[eventType]) {
      return;
    }
    for (const handler of this.eventHandlers[eventType]) {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in ${eventType} handler:`, error);
      }
    }
  }
}

// Create a singleton instance
export const websocketService = new WebSocketService();

// Export the singleton instance as default
export default websocketService;
