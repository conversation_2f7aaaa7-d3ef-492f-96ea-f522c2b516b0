# app/services/mcp/mlflow_service.py
import mlflow
import mlflow.sklearn
import mlflow.pytorch
import joblib
import pickle
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime
from dataclasses import dataclass, asdict
import logging
import os
import tempfile
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ModelDeploymentConfig:
    """Configuration for model deployment"""
    model_name: str
    model_version: str
    stage: str  # "staging", "production", "archived"
    description: str
    metrics: Dict[str, float]
    hyperparameters: Dict[str, Any]
    artifacts: List[str]

@dataclass
class ModelPerformanceMetrics:
    """Model performance tracking metrics"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    model_version: str
    evaluation_date: datetime

class MLflowService:
    """MLflow integration service for model deployment and lifecycle management"""
    
    def __init__(
        self,
        tracking_uri: str = "http://localhost:5000",
        experiment_name: str = "ensemble-weight-optimization",
        model_registry_name: str = "EnsembleWeightOptimizer"
    ):
        self.tracking_uri = tracking_uri
        self.experiment_name = experiment_name
        self.model_registry_name = model_registry_name
        self.current_run = None
        
        # Set MLflow tracking URI
        mlflow.set_tracking_uri(tracking_uri)
        
        # Set or create experiment
        try:
            mlflow.set_experiment(experiment_name)
        except Exception:
            mlflow.create_experiment(experiment_name)
            mlflow.set_experiment(experiment_name)
    
    async def start_run(
        self,
        run_name: Optional[str] = None,
        tags: Dict[str, str] = None
    ) -> str:
        """Start a new MLflow run"""
        try:
            self.current_run = mlflow.start_run(
                run_name=run_name or f"ensemble_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                tags=tags or {}
            )
            
            logger.info(f"Started MLflow run: {self.current_run.info.run_id}")
            return self.current_run.info.run_id
            
        except Exception as e:
            logger.error(f"Failed to start MLflow run: {e}")
            return None
    
    async def log_model_with_signature(
        self,
        model: Any,
        model_version: str,
        input_example: np.ndarray,
        metrics: Dict[str, float],
        hyperparameters: Dict[str, Any],
        model_path: str = "model"
    ) -> Optional[str]:
        """Log ML model with signature and metadata"""
        try:
            if not self.current_run:
                await self.start_run()
            
            # Log hyperparameters
            mlflow.log_params(hyperparameters)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Create model signature
            from mlflow.models.signature import infer_signature
            signature = infer_signature(input_example, model.predict(input_example))
            
            # Log model based on type
            if hasattr(model, 'save'):  # Stable Baselines3 model
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_model_path = os.path.join(temp_dir, "model.zip")
                    model.save(temp_model_path)
                    
                    mlflow.log_artifact(temp_model_path, "model")
                    
                    # Create custom model class for SB3
                    import mlflow.pyfunc
                    
                    class EnsembleWeightModel(mlflow.pyfunc.PythonModel):
                        def load_context(self, context):
                            from stable_baselines3 import PPO
                            self.model = PPO.load(context.artifacts["model"])
                        
                        def predict(self, context, model_input):
                            # Ensure input is numpy array
                            if isinstance(model_input, pd.DataFrame):
                                model_input = model_input.values
                            
                            # Get action probabilities (strategy weights)
                            actions, _ = self.model.predict(model_input, deterministic=True)
                            
                            # Normalize to ensure weights sum to 1
                            if len(actions.shape) == 1:
                                actions = actions.reshape(1, -1)
                            
                            normalized_weights = actions / np.sum(actions, axis=1, keepdims=True)
                            return normalized_weights
                    
                    # Log as PyFunc model
                    mlflow.pyfunc.log_model(
                        artifact_path=model_path,
                        python_model=EnsembleWeightModel(),
                        artifacts={"model": temp_model_path},
                        signature=signature
                    )
            
            else:  # Scikit-learn compatible model
                mlflow.sklearn.log_model(
                    model,
                    model_path,
                    signature=signature,
                    input_example=input_example
                )
            
            # Log model version and deployment info
            mlflow.log_param("model_version", model_version)
            mlflow.log_param("deployment_timestamp", datetime.now().isoformat())
            
            logger.info(f"Logged model version {model_version}")
            return self.current_run.info.run_id
            
        except Exception as e:
            logger.error(f"Failed to log model: {e}")
            return None
    
    async def register_model(
        self,
        model_version: str,
        run_id: Optional[str] = None,
        description: str = None
    ) -> Optional[str]:
        """Register model in MLflow Model Registry"""
        try:
            if not run_id and self.current_run:
                run_id = self.current_run.info.run_id
            
            if not run_id:
                logger.error("No run ID available for model registration")
                return None
            
            # Register model
            model_uri = f"runs:/{run_id}/model"
            
            registered_model = mlflow.register_model(
                model_uri=model_uri,
                name=self.model_registry_name,
                tags={
                    "version": model_version,
                    "type": "ensemble_weight_optimizer",
                    "framework": "stable_baselines3"
                }
            )
            
            # Update model version description
            if description:
                mlflow.update_model_version(
                    name=self.model_registry_name,
                    version=registered_model.version,
                    description=description
                )
            
            logger.info(f"Registered model version {registered_model.version}")
            return registered_model.version
            
        except Exception as e:
            logger.error(f"Failed to register model: {e}")
            return None
    
    async def deploy_model_to_stage(
        self,
        model_version: str,
        stage: str = "staging",
        archive_existing: bool = True
    ) -> bool:
        """Deploy model to specified stage"""
        try:
            # Archive existing production models if requested
            if stage == "production" and archive_existing:
                await self._archive_existing_production_models()
            
            # Transition model to new stage
            mlflow.transition_model_version_stage(
                name=self.model_registry_name,
                version=model_version,
                stage=stage.title()
            )
            
            logger.info(f"Deployed model version {model_version} to {stage}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to deploy model to {stage}: {e}")
            return False
    
    async def load_production_model(self) -> Optional[Any]:
        """Load current production model"""
        try:
            # Get production model
            model_uri = f"models:/{self.model_registry_name}/production"
            
            # Load model
            model = mlflow.pyfunc.load_model(model_uri)
            
            logger.info("Loaded production model")
            return model
            
        except Exception as e:
            logger.error(f"Failed to load production model: {e}")
            return None
    
    async def get_model_info(self, stage: str = "production") -> Optional[Dict]:
        """Get information about model in specified stage"""
        try:
            client = mlflow.tracking.MlflowClient()
            
            # Get model version in specified stage
            model_versions = client.get_latest_versions(
                name=self.model_registry_name,
                stages=[stage.title()]
            )
            
            if not model_versions:
                return None
            
            latest_version = model_versions[0]
            
            # Get run information
            run = client.get_run(latest_version.run_id)
            
            model_info = {
                "version": latest_version.version,
                "stage": latest_version.current_stage,
                "run_id": latest_version.run_id,
                "creation_timestamp": latest_version.creation_timestamp,
                "last_updated_timestamp": latest_version.last_updated_timestamp,
                "description": latest_version.description,
                "metrics": run.data.metrics,
                "params": run.data.params,
                "tags": latest_version.tags
            }
            
            return model_info
            
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
            return None
    
    async def compare_model_performance(
        self,
        baseline_version: str,
        candidate_version: str
    ) -> Dict[str, Any]:
        """Compare performance between two model versions"""
        try:
            client = mlflow.tracking.MlflowClient()
            
            # Get run information for both versions
            baseline_version_info = client.get_model_version(self.model_registry_name, baseline_version)
            candidate_version_info = client.get_model_version(self.model_registry_name, candidate_version)
            
            baseline_run = client.get_run(baseline_version_info.run_id)
            candidate_run = client.get_run(candidate_version_info.run_id)
            
            # Compare metrics
            baseline_metrics = baseline_run.data.metrics
            candidate_metrics = candidate_run.data.metrics
            
            comparison = {
                "baseline_version": baseline_version,
                "candidate_version": candidate_version,
                "metric_comparison": {},
                "improvement_summary": {}
            }
            
            # Calculate improvements
            for metric_name in baseline_metrics:
                if metric_name in candidate_metrics:
                    baseline_value = baseline_metrics[metric_name]
                    candidate_value = candidate_metrics[metric_name]
                    
                    improvement = candidate_value - baseline_value
                    improvement_pct = (improvement / baseline_value * 100) if baseline_value != 0 else 0
                    
                    comparison["metric_comparison"][metric_name] = {
                        "baseline": baseline_value,
                        "candidate": candidate_value,
                        "improvement": improvement,
                        "improvement_pct": improvement_pct
                    }
                    
                    # Determine if this is a significant improvement
                    if metric_name in ["accuracy", "sharpe_ratio", "win_rate", "profit_factor"]:
                        is_better = improvement > 0
                    else:  # For metrics like max_drawdown, lower is better
                        is_better = improvement < 0
                    
                    comparison["improvement_summary"][metric_name] = is_better
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare model performance: {e}")
            return {}
    
    async def automated_model_validation(
        self,
        candidate_version: str,
        validation_criteria: Dict[str, float]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Automatically validate candidate model against criteria"""
        try:
            # Get candidate model metrics
            client = mlflow.tracking.MlflowClient()
            candidate_version_info = client.get_model_version(self.model_registry_name, candidate_version)
            candidate_run = client.get_run(candidate_version_info.run_id)
            candidate_metrics = candidate_run.data.metrics
            
            validation_results = {
                "passed": True,
                "criteria_results": {},
                "overall_score": 0
            }
            
            total_criteria = len(validation_criteria)
            passed_criteria = 0
            
            for metric_name, min_threshold in validation_criteria.items():
                if metric_name in candidate_metrics:
                    metric_value = candidate_metrics[metric_name]
                    
                    # For negative metrics (like max_drawdown), invert the comparison
                    if metric_name in ["max_drawdown"]:
                        passed = abs(metric_value) <= abs(min_threshold)
                    else:
                        passed = metric_value >= min_threshold
                    
                    validation_results["criteria_results"][metric_name] = {
                        "value": metric_value,
                        "threshold": min_threshold,
                        "passed": passed
                    }
                    
                    if passed:
                        passed_criteria += 1
                    else:
                        validation_results["passed"] = False
                else:
                    validation_results["criteria_results"][metric_name] = {
                        "value": None,
                        "threshold": min_threshold,
                        "passed": False,
                        "error": "Metric not found"
                    }
                    validation_results["passed"] = False
            
            validation_results["overall_score"] = passed_criteria / total_criteria if total_criteria > 0 else 0
            
            return validation_results["passed"], validation_results
            
        except Exception as e:
            logger.error(f"Failed to validate model: {e}")
            return False, {"error": str(e)}
    
    async def rollback_to_previous_version(self) -> bool:
        """Rollback to previous production version"""
        try:
            client = mlflow.tracking.MlflowClient()
            
            # Get all production versions
            model_versions = client.search_model_versions(
                filter_string=f"name='{self.model_registry_name}'"
            )
            
            # Find current production and previous versions
            production_versions = [v for v in model_versions if v.current_stage == "Production"]
            archived_versions = [v for v in model_versions if v.current_stage == "Archived"]
            
            if not production_versions:
                logger.error("No production model found to rollback from")
                return False
            
            current_production = max(production_versions, key=lambda x: x.creation_timestamp)
            
            # Find most recent archived version (previous production)
            if archived_versions:
                previous_version = max(archived_versions, key=lambda x: x.creation_timestamp)
                
                # Move current production to archived
                mlflow.transition_model_version_stage(
                    name=self.model_registry_name,
                    version=current_production.version,
                    stage="Archived"
                )
                
                # Move previous version to production
                mlflow.transition_model_version_stage(
                    name=self.model_registry_name,
                    version=previous_version.version,
                    stage="Production"
                )
                
                logger.info(f"Rolled back from version {current_production.version} to {previous_version.version}")
                return True
            else:
                logger.error("No previous version available for rollback")
                return False
                
        except Exception as e:
            logger.error(f"Failed to rollback model: {e}")
            return False
    
    async def cleanup_old_models(self, keep_versions: int = 5) -> int:
        """Clean up old model versions, keeping specified number"""
        try:
            client = mlflow.tracking.MlflowClient()
            
            # Get all model versions
            model_versions = client.search_model_versions(
                filter_string=f"name='{self.model_registry_name}'"
            )
            
            # Sort by creation timestamp (newest first)
            sorted_versions = sorted(
                model_versions, 
                key=lambda x: x.creation_timestamp, 
                reverse=True
            )
            
            # Keep production, staging, and latest N versions
            protected_versions = set()
            
            for version in sorted_versions:
                if version.current_stage in ["Production", "Staging"]:
                    protected_versions.add(version.version)
            
            # Add latest N versions
            for i, version in enumerate(sorted_versions[:keep_versions]):
                protected_versions.add(version.version)
            
            # Delete unprotected versions
            deleted_count = 0
            for version in sorted_versions:
                if version.version not in protected_versions and version.current_stage == "Archived":
                    try:
                        client.delete_model_version(
                            name=self.model_registry_name,
                            version=version.version
                        )
                        deleted_count += 1
                        logger.info(f"Deleted model version {version.version}")
                    except Exception as e:
                        logger.warning(f"Failed to delete version {version.version}: {e}")
            
            logger.info(f"Cleaned up {deleted_count} old model versions")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old models: {e}")
            return 0
    
    async def end_run(self) -> None:
        """End current MLflow run"""
        try:
            if self.current_run:
                mlflow.end_run()
                self.current_run = None
                logger.info("Ended MLflow run")
                
        except Exception as e:
            logger.error(f"Failed to end MLflow run: {e}")
    
    async def _archive_existing_production_models(self) -> None:
        """Archive existing production models"""
        try:
            client = mlflow.tracking.MlflowClient()
            
            # Get current production models
            production_versions = client.get_latest_versions(
                name=self.model_registry_name,
                stages=["Production"]
            )
            
            for version in production_versions:
                mlflow.transition_model_version_stage(
                    name=self.model_registry_name,
                    version=version.version,
                    stage="Archived"
                )
                logger.info(f"Archived production model version {version.version}")
                
        except Exception as e:
            logger.error(f"Failed to archive existing production models: {e}")

# Integration utilities

async def deploy_trained_model(
    model,
    model_version: str,
    training_metrics: Dict[str, float],
    hyperparameters: Dict[str, Any],
    input_example: np.ndarray,
    validation_criteria: Dict[str, float] = None
) -> Tuple[bool, str]:
    """Complete model deployment workflow"""
    
    mlflow_service = MLflowService()
    
    try:
        # Start MLflow run
        run_id = await mlflow_service.start_run(
            run_name=f"ensemble_deployment_{model_version}"
        )
        
        # Log model
        await mlflow_service.log_model_with_signature(
            model=model,
            model_version=model_version,
            input_example=input_example,
            metrics=training_metrics,
            hyperparameters=hyperparameters
        )
        
        # Register model
        registered_version = await mlflow_service.register_model(
            model_version=model_version,
            description=f"Ensemble weight optimizer v{model_version}"
        )
        
        if not registered_version:
            return False, "Failed to register model"
        
        # Deploy to staging first
        staging_success = await mlflow_service.deploy_model_to_stage(
            model_version=registered_version,
            stage="staging"
        )
        
        if not staging_success:
            return False, "Failed to deploy to staging"
        
        # Validate model if criteria provided
        if validation_criteria:
            validation_passed, validation_results = await mlflow_service.automated_model_validation(
                candidate_version=registered_version,
                validation_criteria=validation_criteria
            )
            
            if validation_passed:
                # Deploy to production
                production_success = await mlflow_service.deploy_model_to_stage(
                    model_version=registered_version,
                    stage="production"
                )
                
                if production_success:
                    await mlflow_service.end_run()
                    return True, f"Model v{registered_version} deployed to production"
                else:
                    await mlflow_service.end_run()
                    return False, "Failed to deploy to production"
            else:
                await mlflow_service.end_run()
                return False, f"Model validation failed: {validation_results}"
        else:
            # Deploy directly to production without validation
            production_success = await mlflow_service.deploy_model_to_stage(
                model_version=registered_version,
                stage="production"
            )
            
            await mlflow_service.end_run()
            return production_success, f"Model v{registered_version} deployed to production" if production_success else "Failed to deploy to production"
    
    except Exception as e:
        await mlflow_service.end_run()
        logger.error(f"Model deployment failed: {e}")
        return False, f"Deployment error: {e}"