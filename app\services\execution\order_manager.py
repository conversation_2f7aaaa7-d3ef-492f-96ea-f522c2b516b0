"""Module for managing order placement and updates"""
import logging
from decimal import Decimal
from datetime import datetime
from typing import Dict, Any, Optional

from app.services.execution.models import Order
from app.repositories import OrderRepository
from app.services.exchange import ExchangeClient
from app.services.execution.models import OrderStatus

logger = logging.getLogger(__name__)


class OrderManager:
    """
    Manages the lifecycle of orders, including placement, updates, and cancellations.
    """

    def __init__(self, order_repo: OrderRepository, exchange_service: ExchangeClient):
        """
        Initializes the OrderManager.

        Args:
            order_repo: The repository for accessing order data.
            exchange_service: The service for interacting with the exchange.
        """
        self.order_repo = order_repo
        self.exchange_service = exchange_service
        
    async def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: Decimal,
        price: Optional[Decimal] = None,
        stop_price: Optional[Decimal] = None,
    ) -> Optional[Order]:
        """
        Places a new order with the exchange and records it in the database.
        """
        logger.info(f"Placing {order_type} order for {quantity} of {symbol} at price {price}")
        try:
            # Place the order via the exchange service
            exchange_order = await self.exchange_service.place_order(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
            )

            if not exchange_order:
                logger.error("Failed to place order with the exchange.")
                return None

            # Create an Order object from the exchange response
            order = Order(
                order_id=str(exchange_order["orderId"]),
                client_order_id=exchange_order["clientOrderId"],
                symbol=exchange_order["symbol"],
                price=Decimal(exchange_order["price"]),
                quantity=Decimal(exchange_order["origQty"]),
                side=exchange_order["side"],
                order_type=exchange_order["type"],
                status=OrderStatus(exchange_order["status"]),
                created_at=datetime.fromtimestamp(exchange_order["transactTime"] / 1000),
            )

            # Save the order to the database
            await self.order_repo.add_order(order)
            logger.info(f"Successfully placed and recorded order {order.order_id}")
            return order

        except Exception as e:
            logger.error(f"Error placing order for {symbol}: {e}", exc_info=True)
            return None

    async def update_order_status(self, order_id: str, new_status: OrderStatus) -> Optional[Order]:
        """
        Updates the status of an existing order.
        """
        logger.info(f"Updating status for order {order_id} to {new_status.value}")
        order = await self.order_repo.get_order_by_id(order_id)
        if order:
            order.status = new_status
            await self.order_repo.update_order(order)
            logger.info(f"Successfully updated status for order {order_id}")
            return order
        else:
            logger.warning(f"Order {order_id} not found for status update.")
            return None

    async def cancel_order(self, order_id: str) -> bool:
        """
        Cancels an order on the exchange and updates its status in the database.
        """
        logger.info(f"Cancelling order {order_id}")
        try:
            # Cancel the order via the exchange service
            # Assuming the exchange service returns True on success
            success = await self.exchange_service.cancel_order(order_id)
            if success:
                await self.update_order_status(order_id, OrderStatus.CANCELLED)
                logger.info(f"Successfully cancelled order {order_id}")
                return True
            else:
                logger.error(f"Failed to cancel order {order_id} on the exchange.")
                return False
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}", exc_info=True)
            return False

    async def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """
        Retrieves an order by its ID.
        """
        return await self.order_repo.get_order_by_id(order_id)

    async def get_all_orders_for_symbol(self, symbol: str) -> list[Order]:
        """
        Retrieves all orders for a given symbol.
        """
        return await self.order_repo.get_all_orders_for_symbol(symbol)