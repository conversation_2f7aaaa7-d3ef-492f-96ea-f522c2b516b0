#!/usr/bin/env python3
"""
ML Pipeline Integration Test Runner
Created: June 17, 2025

Simple runner script to execute the comprehensive ML pipeline integration test.
This script provides easy execution with proper environment setup and clear output.
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def print_banner():
    """Print test banner"""
    print("=" * 80)
    print("🧠 COMPREHENSIVE ML PIPELINE INTEGRATION TEST")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing complete ML-enhanced trading system integration...")
    print()

def print_test_info():
    """Print test information"""
    print("📋 TEST COVERAGE:")
    print("  ✓ ML Pipeline Components (WeightOptimizer, learning, monitoring)")
    print("  ✓ Auto Trading Controller ML Integration") 
    print("  ✓ Dashboard Integration (API endpoints, WebSocket)")
    print("  ✓ End-to-End Workflow (complete ML trading session)")
    print("  ✓ Error Handling and Recovery Scenarios")
    print("  ✓ Performance Benchmarks and Validation")
    print()

async def run_test():
    """Run the comprehensive ML pipeline test"""
    try:
        # Import the test module
        from test_complete_ml_pipeline_integration import MLPipelineTestManager
        
        print("🔄 Initializing ML Pipeline Test Manager...")
        test_manager = MLPipelineTestManager()
        
        print("🚀 Running comprehensive ML pipeline integration test...")
        results = await test_manager.run_comprehensive_test()
        
        return results
        
    except ImportError as e:
        logger.error(f"Failed to import test module: {e}")
        print("❌ Test import failed. Ensure all dependencies are installed.")
        return None
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        print(f"❌ Test execution failed: {e}")
        return None

def print_results_summary(results):
    """Print a summary of test results"""
    if not results:
        print("❌ No test results available")
        return False
    
    final_report = results.get('final_report', {})
    test_summary = final_report.get('test_summary', {})
    ml_validation = final_report.get('ml_system_validation', {})
    performance = final_report.get('performance_summary', {})
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    # Overall status
    overall_success = test_summary.get('overall_success', False)
    print(f"Overall Status: {'✅ PASSED' if overall_success else '❌ FAILED'}")
    print(f"Total Duration: {test_summary.get('total_duration_seconds', 0):.2f} seconds")
    
    # Component status
    print("\n🧩 COMPONENT STATUS:")
    component_tests = results.get('component_tests', {})
    for component, result in component_tests.items():
        status = "✅" if result.get('success', False) else "❌"
        print(f"  {component.replace('_', ' ').title()}: {status}")
    
    # Integration status
    print("\n🔗 INTEGRATION STATUS:")
    integration_tests = results.get('integration_tests', {})
    for integration, result in integration_tests.items():
        status = "✅" if result.get('success', False) else "❌"
        print(f"  {integration.replace('_', ' ').title()}: {status}")
    
    # Performance metrics
    print("\n⚡ PERFORMANCE METRICS:")
    if performance:
        print(f"  Prediction Latency: {performance.get('prediction_latency_ms', 0):.2f}ms")
        print(f"  Throughput: {performance.get('prediction_throughput_per_second', 0):.2f} pred/sec")
        print(f"  Memory Usage: {performance.get('memory_usage_mb', 0):.2f}MB")
        print(f"  SLA Compliance: {'✅' if performance.get('meets_sla_requirements', False) else '❌'}")
    
    # Recommendations
    recommendations = final_report.get('recommendations', [])
    if recommendations:
        print("\n💡 RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations[:5], 1):  # Show top 5
            print(f"  {i}. {rec}")
    
    print("\n" + "=" * 60)
    return overall_success

def main():
    """Main execution function"""
    print_banner()
    print_test_info()
    
    try:
        # Run the test
        results = asyncio.run(run_test())
        
        # Print results
        success = print_results_summary(results)
        
        if success:
            print("🎉 All tests passed! ML Pipeline integration is working correctly.")
            return 0
        else:
            print("⚠️  Some tests failed. Check the detailed report for more information.")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)