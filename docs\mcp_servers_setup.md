# MCP Servers Setup Guide

## Overview

This document provides instructions for setting up and configuring Model Context Protocol (MCP) servers for the Crypto Trading Bot project. MCP servers provide various functionalities to the application, such as cryptocurrency exchange access, market data, database access, and more.

## MCP Servers

The following MCP servers are used in the project:

1. **CCXT MCP Server**: Provides access to cryptocurrency exchanges through the CCXT library
2. **CoinCap MCP Server**: Provides cryptocurrency market data
3. **PostgreSQL MCP Server**: Provides database access
4. **Memory MCP Server**: Provides knowledge graph storage
5. **Time MCP Server**: Provides time-related functionality
6. **Sequential Thinking MCP Server**: Provides problem-solving capabilities
7. **Binance MCP Server**: Provides direct Binance API integration
8. **Git MCP Server**: Provides version control functionality

## Installation

### Prerequisites

- Node.js (v14 or later)
- Python 3.12 or later
- PostgreSQL database
- Git

### Automated Installation

The project includes an automated installation script that installs all the required MCP packages. To use it, run the following command in PowerShell:

```powershell
.\install_mcp_servers.ps1
```

This script will:
1. Install all Node.js MCP packages
2. Install all Python MCP packages
3. Clone and build the Binance MCP server
4. Create the necessary wrapper scripts

You can customize the installation by using the following parameters:

```powershell
.\install_mcp_servers.ps1 -Global -PythonPath "C:\Python312\python.exe"
```

- `-Global`: Install Node.js packages globally
- `-PythonPath`: Specify the path to the Python executable
- `-NodePath`: Specify the path to the Node.js executable
- `-BinanceMCPRepo`: Specify the URL of the Binance MCP repository

### Manual Installation

#### Node.js MCP Servers

Install the Node.js MCP servers using npm:

```bash
npm install -g @modelcontextprotocol/server-ccxt
npm install -g @modelcontextprotocol/server-coincap
npm install -g @modelcontextprotocol/server-postgres
npm install -g @modelcontextprotocol/server-memory
npm install -g @modelcontextprotocol/server-sequential-thinking
```

#### Python MCP Servers

Install the Python MCP servers using pip:

```bash
pip install mcp mcp-server-time mcp-server-git
```

#### Binance MCP Server

Clone and build the Binance MCP server:

```bash
git clone https://github.com/modelcontextprotocol/server-binance.git binance-mcp
cd binance-mcp
npm install
npm run build
```

## Configuration

### Automatic MCP Server Startup

The project is configured to automatically start all MCP servers when Augment Code is launched. This is achieved through two mechanisms:

1. **Autostart Configuration**: Each MCP server in the `.vscode/settings.json` file has the `autostart: true` property, which tells Augment Code to automatically start these servers when it launches.

2. **VS Code Task**: A VS Code task is configured to run automatically when the project folder is opened. This task runs the `start_mcp_servers.ps1` script with the `-Background` flag to start all MCP servers in the background.

To disable automatic startup, you can:

1. Set the `autostart` property to `false` for each MCP server in the `.vscode/settings.json` file.
2. Remove or disable the VS Code task for automatic startup in the `.vscode/tasks.json` file.

### Automated Configuration

The project includes a configuration generator script that creates the MCP server configuration based on your environment. To use it, run the following command in PowerShell:

```powershell
.\generate_mcp_config.ps1
```

This script will:
1. Generate the MCP server configuration based on your environment
2. Create or update the `.vscode/settings.json` file
3. Merge with existing settings if requested

You can customize the configuration by using the following parameters:

```powershell
.\generate_mcp_config.ps1 -PythonPath "C:\Python312\python.exe" -NodeModulesPath "C:\Users\<USER>\AppData\Roaming\npm\node_modules" -OutputFile ".vscode\settings.json" -Merge
```

- `-PythonPath`: Specify the path to the Python executable
- `-NodeModulesPath`: Specify the path to the Node.js modules
- `-OutputFile`: Specify the output file path
- `-Merge`: Merge with existing settings (default: true)

### Manual Configuration

The MCP servers are configured in the `.vscode/settings.json` file. Here's an example configuration:

```json
{
  "augment.advanced.mcpServers": [
    {
      "name": "ccxt",
      "command": "node",
      "args": [".vscode/ccxt-mcp.js"],
      "autostart": true
    },
    {
      "name": "coincap",
      "command": "node",
      "args": [".vscode/coincap-mcp.js"]
    },
    {
      "name": "postgres",
      "command": "node",
      "args": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/postgres-mcp.js"]
    },
    {
      "name": "memory",
      "command": "node",
      "args": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/memory-mcp.js"]
    },
    {
      "name": "time",
      "command": "C:\\Python312\\python.exe",
      "args": ["-m", "mcp_server_time", "--local-timezone=UTC"]
    },
    {
      "name": "sequential-thinking",
      "command": "node",
      "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@modelcontextprotocol\\server-sequential-thinking\\dist\\index.js"]
    },
    {
      "name": "binance",
      "command": "node",
      "args": ["binance-mcp/build/index.js"]
    },
    {
      "name": "git",
      "command": "C:\\Python312\\python.exe",
      "args": ["-m", "mcp_server_git"]
    }
  ]
}
```

### MCP Server Wrapper Scripts

The Node.js MCP servers use wrapper scripts in the `.vscode` folder. Here's an example of the CCXT MCP server wrapper script:

```javascript
// .vscode/ccxt-mcp.js
const { spawn } = require('child_process');
const path = require('path');

const npmBin = process.platform === 'win32' ? 'npm.cmd' : 'npm';
const args = ['exec', '--', 'mcp-server-ccxt'];

const child = spawn(npmBin, args, {
  stdio: 'inherit',
  shell: true
});

child.on('error', (err) => {
  console.error('Failed to start MCP server:', err);
});

process.on('SIGINT', () => {
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  child.kill('SIGTERM');
});
```

## Starting MCP Servers

### Using VS Code Tasks

The project includes VS Code tasks for starting and stopping MCP servers. To use them, press `Ctrl+Shift+P` and type `Tasks: Run Task`, then select one of the following tasks:

- **Start MCP Servers on Startup**: Automatically runs when the folder is opened
- **Start MCP Servers**: Starts all MCP servers in a new terminal
- **Start MCP Servers (Background)**: Starts all MCP servers in the background
- **Stop MCP Servers**: Stops all MCP servers
- **Force Stop MCP Servers**: Forces all MCP servers to stop
- **Check MCP Server Status**: Shows the status of all MCP servers
- **Generate MCP Server Configuration**: Generates the MCP server configuration
- **Install MCP Servers**: Installs all MCP servers
- **Start App with MCP Servers**: Starts the application with MCP servers

### Using PowerShell Scripts

#### Starting MCP Servers

To start all MCP servers, run the following command in PowerShell:

```powershell
.\start_mcp_servers.ps1
```

Alternatively, you can use the dedicated startup script that includes error handling and logging:

```powershell
.\start_all_mcps.ps1
```

This script will start all MCP servers in the background and log the output to the `mcp_startup.log` file.

You can customize the startup by using the following parameters:

```powershell
.\start_mcp_servers.ps1 -Servers "ccxt","time","git" -PythonPath "C:\Python312\python.exe" -Background -NoHealthCheck
```

- `-Servers`: Specify which servers to start (default: "all")
- `-PythonPath`: Specify the path to the Python executable
- `-NodePath`: Specify the path to the Node.js executable
- `-NodeModulesPath`: Specify the path to the Node.js modules
- `-Background`: Run in the background
- `-LogFile`: Specify the log file path
- `-HealthCheckInterval`: Specify the health check interval in seconds
- `-NoHealthCheck`: Disable health checks

#### Stopping MCP Servers

To stop all MCP servers, run the following command in PowerShell:

```powershell
.\stop_mcp_servers.ps1
```

You can customize the shutdown by using the following parameters:

```powershell
.\stop_mcp_servers.ps1 -Servers "ccxt","time","git" -Force
```

- `-Servers`: Specify which servers to stop (default: "all")
- `-Force`: Force stop all processes

### Manual Startup

You can also start each MCP server manually:

1. **CCXT MCP Server**:
   ```bash
   node .vscode/ccxt-mcp.js
   ```

2. **CoinCap MCP Server**:
   ```bash
   node .vscode/coincap-mcp.js
   ```

3. **PostgreSQL MCP Server**:
   ```bash
   node C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/postgres-mcp.js
   ```

4. **Memory MCP Server**:
   ```bash
   node C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/memory-mcp.js
   ```

5. **Time MCP Server**:
   ```bash
   python -m mcp_server_time --local-timezone=UTC
   ```

6. **Sequential Thinking MCP Server**:
   ```bash
   node C:\Users\<USER>\AppData\Roaming\npm\node_modules\@modelcontextprotocol\server-sequential-thinking\dist\index.js
   ```

7. **Binance MCP Server**:
   ```bash
   node binance-mcp/build/index.js
   ```

8. **Git MCP Server**:
   ```bash
   python -m mcp_server_git
   ```

## Troubleshooting

### Common Issues

1. **"Not connected" error**:
   - Make sure all MCP servers are running
   - Check if the correct Python path is being used
   - Restart Augment Code after starting the MCP servers
   - Check the MCP server logs in the `mcp_servers.log` file

2. **"spawn npx ENOENT" error**:
   - Make sure npx is installed and in the PATH
   - Use the full path to the npm executable in the wrapper scripts
   - Run the `install_mcp_servers.ps1` script to create the wrapper scripts

3. **"Module not found" error**:
   - Make sure all required packages are installed
   - Check if the correct Python environment is being used
   - Run the `install_mcp_servers.ps1` script to install all required packages

4. **Health check failures**:
   - Check if the MCP server is running
   - Check if the MCP server is responding on the specified port
   - Check the MCP server logs in the `mcp_servers.log` file
   - Disable health checks with the `-NoHealthCheck` parameter

5. **"Client closed" or "spawn C:\\Program ENOENT" error on Windows (PIF/Playwright MCPs)**:
   - **Issue**: When configuring MCP servers in `.vscode/settings.json` or `mcp.json` on Windows that use an executable located in a path with spaces (like `C:\Program Files\nodejs\node.exe`), Cursor might fail to launch the server correctly. Errors like `spawn C:\Program ENOENT` (path truncated at space) or `Client closed` (process launched but stdio communication failed) may occur.
   - **Attempts**: Initial troubleshooting involved direct path execution, quoting the path, and using `cmd.exe /c`, none of which fully resolved the issue due to path interpretation or stdio interference.
   - **Solution**: Use the Windows short path name (8.3 format) for the executable in the `command` property within the JSON configuration. For Node.js installed in the default location, this is typically `C:\\Progra~1\\nodejs\\node.exe`.
   - **Example (`mcp.json`)**:
     ```json
     {
       "playwright": {
         "command": "C:\\Progra~1\\nodejs\\node.exe",
         "args": ["C:\\Users\\<USER>\\AppData\\...\\index.js"]
       },
       "pif": {
         "command": "C:\\Progra~1\\nodejs\\node.exe",
         "args": ["...\\MCP-PIF\\build\\index.js"],
         "cwd": "...\\MCP-PIF",
         "env": { ... }
       }
     }
     ```
   - **Note**: Also ensure necessary `cwd` (e.g., for PIF server) and `env` properties are set correctly.

### Python Path Issues

If you've changed your Python path, you can use the provided scripts to update the MCP server configurations:

1. Run the `generate_mcp_config.ps1` script with the new Python path:
   ```powershell
   .\generate_mcp_config.ps1 -PythonPath "C:\Python312\python.exe"
   ```

2. Update the `start_mcp_servers.ps1` script parameters:
   ```powershell
   .\start_mcp_servers.ps1 -PythonPath "C:\Python312\python.exe"
   ```

3. Make sure the MCP Python packages are installed in the new Python environment:
   ```powershell
   .\install_mcp_servers.ps1 -PythonPath "C:\Python312\python.exe"
   ```

### Checking MCP Server Status

To check the status of all MCP servers, run the following command in PowerShell:

```powershell
.\start_mcp_servers.ps1 -Servers "all" -NoHealthCheck
```

Or use the VS Code task "Check MCP Server Status".

## Conclusion

By following this guide, you should be able to set up and configure all the MCP servers required for the Crypto Trading Bot project. The provided scripts and tools make it easy to install, configure, start, and stop the MCP servers, even when your Python path or environment changes.

If you encounter any issues, refer to the troubleshooting section or check the MCP server logs in the `mcp_servers.log` file. You can also use the VS Code tasks to manage the MCP servers.
