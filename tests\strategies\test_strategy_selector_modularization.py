"""
Tests for the modularized Strategy Selector.

These tests verify that the modularization of the Strategy Selector was successful
and that all the components work correctly together.
"""

import unittest
import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock, AsyncMock
import asyncio

from app.strategies.strategy_selector import StrategySelector
from app.services.execution.service import ExecutionService
from app.services.execution.models import Order, OrderSide, OrderStatus, OrderType
from app.services.exchange.binance_client import BinanceExchangeClient
from app.strategies.strategy_scoring import StrategyScorer
from app.strategies.execution_handler import ExecutionHandler
from app.strategies.utils import (
    process_klines_data,
    calculate_position_size,
    calculate_order_quantity,
    format_number,
    calculate_next_candle_time
)


class TestStrategyModularization(unittest.IsolatedAsyncioTestCase):
    """Test the modularization of the Strategy Selector."""

    async def asyncSetUp(self):
        """Set up test environment."""
        # Create mock objects
        self.mock_settings = MagicMock()
        self.mock_execution_service = MagicMock()
        self.mock_exchange_client = MagicMock()

        # Set up mock settings
        self.mock_settings.kline_limit = 100

        # Create a sample DataFrame for testing
        self.sample_df = pd.DataFrame({
            'open': [50000.0, 51000.0, 52000.0, 51500.0, 51800.0],
            'high': [51000.0, 52000.0, 52500.0, 52000.0, 52200.0],
            'low': [49500.0, 50800.0, 51000.0, 51000.0, 51500.0],
            'close': [51000.0, 52000.0, 51500.0, 51800.0, 52000.0],
            'volume': [100.0, 120.0, 80.0, 90.0, 110.0]
        })

        # Sample klines data for testing
        self.sample_klines = [
            [1617000000000, "50000.0", "51000.0", "49500.0", "51000.0", "100.0", 1617003600000, "5100000.0", 1000, "50.0", "2550000.0", "0"],
            [1617003600000, "51000.0", "52000.0", "50800.0", "52000.0", "120.0", 1617007200000, "6240000.0", 1200, "60.0", "3120000.0", "0"],
            [1617007200000, "52000.0", "52500.0", "51000.0", "51500.0", "80.0", 1617010800000, "4120000.0", 800, "40.0", "2060000.0", "0"],
            [1617010800000, "51500.0", "52000.0", "51000.0", "51800.0", "90.0", 1617014400000, "4662000.0", 900, "45.0", "2331000.0", "0"],
            [1617014400000, "51800.0", "52200.0", "51500.0", "52000.0", "110.0", 1617018000000, "5720000.0", 1100, "55.0", "2860000.0", "0"]
        ]

    def test_process_klines_data(self):
        """Test the process_klines_data utility function."""
        # Process the sample klines data
        df = process_klines_data(self.sample_klines)

        # Verify the result
        self.assertIsInstance(df, pd.DataFrame)
        self.assertEqual(len(df), 5)
        self.assertIn('open', df.columns)
        self.assertIn('high', df.columns)
        self.assertIn('low', df.columns)
        self.assertIn('close', df.columns)
        self.assertIn('volume', df.columns)

        # Check data types
        self.assertTrue(pd.api.types.is_numeric_dtype(df['open']))
        self.assertTrue(pd.api.types.is_numeric_dtype(df['high']))
        self.assertTrue(pd.api.types.is_numeric_dtype(df['low']))
        self.assertTrue(pd.api.types.is_numeric_dtype(df['close']))
        self.assertTrue(pd.api.types.is_numeric_dtype(df['volume']))

        # Check values
        self.assertEqual(df['open'].iloc[0], 50000.0)
        self.assertEqual(df['close'].iloc[-1], 52000.0)

    def test_calculate_position_size(self):
        """Test the calculate_position_size utility function."""
        # Test with different parameters
        position_size = calculate_position_size(
            account_balance=10000.0,
            risk_per_trade=1.0,
            stop_loss_pct=2.0,
            leverage=1.0
        )

        # Verify the result
        self.assertEqual(position_size, 500.0)  # 10000 * 0.01 / 0.02 * 1

        # Test with leverage
        position_size = calculate_position_size(
            account_balance=10000.0,
            risk_per_trade=1.0,
            stop_loss_pct=2.0,
            leverage=5.0
        )

        # Verify the result
        self.assertEqual(position_size, 2500.0)  # 10000 * 0.01 / 0.02 * 5

    def test_calculate_order_quantity(self):
        """Test the calculate_order_quantity utility function."""
        # Test with different parameters
        quantity = calculate_order_quantity(
            position_size=5000.0,
            current_price=50000.0
        )

        # Verify the result
        self.assertEqual(quantity, 0.1)  # 5000 / 50000

        # Test with zero price
        quantity = calculate_order_quantity(
            position_size=5000.0,
            current_price=0.0
        )

        # Verify the result
        self.assertEqual(quantity, 0.0)  # Should handle division by zero

    def test_format_number(self):
        """Test the format_number utility function."""
        # Test with different parameters
        formatted = format_number(123.456789, precision=2)
        self.assertEqual(formatted, "123.46")

        formatted = format_number(123.456789, precision=4)
        self.assertEqual(formatted, "123.4568")

        formatted = format_number(123.456789, precision=8)
        self.assertEqual(formatted, "123.45678900")

    def test_strategy_scorer(self):
        """Test the StrategyScorer class."""
        # Create sample market conditions
        market_conditions = {
            'volatility': 2.5,
            'trend': 0.7,
            'range_bound': 0.3,
            'volume': 0.8
        }

        # Test score_grid_strategy
        grid_score = StrategyScorer.score_grid_strategy(market_conditions)
        self.assertIsInstance(grid_score, float)
        self.assertTrue(0.0 <= grid_score <= 1.0)

        # Test score_technical_analysis_strategy
        ta_score = StrategyScorer.score_technical_analysis_strategy(market_conditions)
        self.assertIsInstance(ta_score, float)
        self.assertTrue(0.0 <= ta_score <= 1.0)

        # Test score_trend_following_strategy
        trend_score = StrategyScorer.score_trend_following_strategy(market_conditions)
        self.assertIsInstance(trend_score, float)
        self.assertTrue(0.0 <= trend_score <= 1.0)

        # Test select_best_strategy
        best_strategy, scores = StrategyScorer.select_best_strategy(market_conditions)
        self.assertIsInstance(best_strategy, str)
        self.assertIsInstance(scores, dict)
        self.assertIn('grid', scores)
        self.assertIn('technical_analysis', scores)
        self.assertIn('trend_following', scores)

        # Test get_strategy_parameters
        params = StrategyScorer.get_strategy_parameters(
            'grid',
            market_conditions,
            {'grid_size': 10, 'grid_spacing': 1.0}
        )
        self.assertIsInstance(params, dict)
        self.assertIn('grid_size', params)
        self.assertIn('grid_spacing', params)

    @patch('app.strategies.execution_handler.ExecutionHandler.execute_strategy_entry')
    async def test_execution_handler(self, mock_execute_entry):
        """Test the ExecutionHandler class."""
        # Set up mock
        mock_execute_entry.return_value = AsyncMock()

        # Create an instance
        handler = ExecutionHandler(self.mock_execution_service)

        # Test execute_strategy_entry
        await handler.execute_strategy_entry(
            symbol='BTCUSDT',
            strategy_name='grid',
            side='BUY',
            quantity=0.1,
            price=50000.0
        )

        # Verify the mock was called
        mock_execute_entry.assert_called_once()

    @patch('app.strategies.strategy_selector.StrategySelector._switch_strategy')
    @patch('app.strategies.strategy_selector.MarketAnalyzer.analyze_market_conditions')
    @patch('app.strategies.strategy_selector.StrategyScorer.select_best_strategy')
    @patch('app.strategies.strategy_selector.StrategySelector.process_klines_data')
    async def test_strategy_selector(self, mock_process_klines, mock_select_strategy,
                                   mock_analyze_conditions, mock_switch_strategy):
        """Test the StrategySelector class."""
        # Set up mocks
        mock_process_klines.return_value = self.sample_df
        mock_analyze_conditions.return_value = {
            'volatility': 2.5,
            'trend': 0.7,
            'range_bound': 0.3,
            'volume': 0.8
        }
        mock_select_strategy.return_value = ('technical_analysis', {
            'grid': 0.4,
            'technical_analysis': 0.8,
            'trend_following': 0.6
        })
        mock_switch_strategy.return_value = AsyncMock()

        # Set up exchange client mock
        self.mock_exchange_client.get_historical_klines = AsyncMock(return_value=self.sample_klines)
        self.mock_exchange_client.get_account_balance = AsyncMock(return_value={'USDT': 10000.0})

        # Create an instance
        selector = StrategySelector(
            settings=self.mock_settings,
            execution_service=self.mock_execution_service,
            exchange_client=self.mock_exchange_client,
            params={'symbol': 'BTCUSDT', 'timeframe': '1h'}
        )

        # Test is_running
        self.assertFalse(selector.is_running())

        # Test get_status
        status = selector.get_status()
        self.assertIsInstance(status, dict)
        self.assertIn('is_running', status)
        self.assertIn('symbol', status)
        self.assertIn('timeframe', status)

        # Start the selector in a task that we'll cancel after a short time
        task = asyncio.create_task(selector.start())

        # Give it a moment to run
        await asyncio.sleep(0.1)

        # Stop the selector
        await selector.stop()

        # Wait for the task to complete
        try:
            await asyncio.wait_for(task, timeout=0.5)
        except asyncio.TimeoutError:
            # Cancel the task if it's still running
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # Verify the mocks were called
        self.mock_exchange_client.get_historical_klines.assert_called()
        mock_process_klines.assert_called()
        mock_analyze_conditions.assert_called()
        mock_select_strategy.assert_called()


if __name__ == '__main__':
    unittest.main()
