"""WebSocket API for real-time trade and dashboard updates."""
import logging
import asyncio
import json
from typing import Dict, List, Any, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from starlette.websockets import WebSocketState

from app.services.execution.trade_state import TradeStatus
from app.dependencies import get_execution_service

# TODO: This will be needed for handling state updates
# from app.services.trade_state_service import TradeStateService

logger = logging.getLogger(__name__)

router = APIRouter()

# Store active WebSocket connections
active_connections: Set[WebSocket] = set()

@router.websocket("/ws/trades")
async def websocket_trades(websocket: WebSocket, execution_service = Depends(get_execution_service)):
    """WebSocket endpoint for real-time trade updates."""
    await websocket.accept()
    active_connections.add(websocket)
    
    # Send initial system status
    try:
        await websocket.send_json({
            "type": "system_status",
            "data": {
                "status": "online",
                "message": "Connected to trade update stream"
            }
        })
        
        # Start listening to the trade update queue
        trade_update_queue = execution_service.websocket_handler.trade_update_queue
        
        # Keep the connection alive and process messages
        while True:
            # Check if the WebSocket is still connected
            if websocket.client_state == WebSocketState.DISCONNECTED:
                break
                
            # Try to get a message from the queue with a timeout
            try:
                # Use asyncio.wait_for to implement a timeout
                trade_update = await asyncio.wait_for(trade_update_queue.get(), timeout=30.0)
                
                # Process the trade update
                if trade_update:
                    # Format the message for the client
                    message = {
                        "type": "trade_update",
                        "data": trade_update
                    }
                    
                    # Send the message to the client
                    await websocket.send_json(message)
                    logger.debug(f"Sent trade update to client: {trade_update}")
            
            except asyncio.TimeoutError:
                # Send a ping to keep the connection alive
                try:
                    await websocket.send_json({
                        "type": "ping",
                        "data": {"timestamp": asyncio.get_event_loop().time()}
                    })
                except Exception as e:
                    logger.error(f"Error sending ping: {e}")
                    break
            
            except Exception as e:
                logger.error(f"Error processing trade update: {e}")
                try:
                    await websocket.send_json({
                        "type": "error",
                        "data": {
                            "code": "processing_error",
                            "message": "Error processing trade update"
                        }
                    })
                except Exception:
                    # If we can't send the error, the connection is probably dead
                    break
    
    except WebSocketDisconnect:
        logger.info("Client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        # Clean up the connection
        if websocket in active_connections:
            active_connections.remove(websocket)

async def broadcast_trade_update(trade_update: Dict[str, Any]):
    """Broadcast a trade update to all connected clients."""
    if not active_connections:
        return
        
    # Format the message
    message = {
        "type": "trade_update",
        "data": trade_update
    }
    
    # Send to all active connections
    disconnected = set()
    for connection in active_connections:
        try:
            await connection.send_json(message)
        except Exception as e:
            logger.error(f"Error broadcasting to client: {e}")
            disconnected.add(connection)
    
    # Clean up disconnected clients
    for connection in disconnected:
        if connection in active_connections:
            active_connections.remove(connection)

async def broadcast_system_status(status: str, message: str):
    """Broadcast a system status update to all connected clients."""
    if not active_connections:
        return
        
    # Format the message
    message = {
        "type": "system_status",
        "data": {
            "status": status,
            "message": message
        }
    }
    
    # Send to all active connections
    disconnected = set()
    for connection in active_connections:
        try:
            await connection.send_json(message)
        except Exception as e:
            logger.error(f"Error broadcasting system status: {e}")
            disconnected.add(connection)
    
    # Clean up disconnected clients
    for connection in disconnected:
        if connection in active_connections:
            active_connections.remove(connection)

class DashboardWebSocket:
    """Class to manage dashboard WebSocket connections."""
    
    def __init__(self, websocket: WebSocket, trade_state_service):
        self.websocket = websocket
        self.trade_state_service = trade_state_service

    async def websocket_endpoint(self):
        """Handle WebSocket connections for dashboard updates."""
        await self.websocket.accept()
        active_connections.add(self.websocket)
        
        try:
            # Send initial system status
            await self.websocket.send_json({
                "type": "system_status",
                "data": {
                    "status": "online",
                    "message": "Connected to dashboard stream"
                }
            })
            
            # Subscribe to trade state updates
            async for update in self.trade_state_service.subscribe_to_updates():
                try:
                    await self.websocket.send_json({
                        "type": "dashboard_update",
                        "data": update
                    })
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error(f"Error sending dashboard update: {e}")
                    
        except WebSocketDisconnect:
            logger.info("Dashboard client disconnected")
        except Exception as e:
            logger.error(f"Dashboard WebSocket error: {e}")
        finally:
            if self.websocket in active_connections:
                active_connections.remove(self.websocket)

@router.websocket("/ws/dashboard")
async def websocket_dashboard(websocket: WebSocket, trade_state_service = Depends(get_execution_service)):
    """WebSocket endpoint for real-time dashboard updates."""
    dashboard_ws = DashboardWebSocket(websocket, trade_state_service)
    await dashboard_ws.websocket_endpoint()
