import asyncio
import logging
from typing import Dict, List, Optional, Any, Union

from binance import AsyncClient, BinanceSocketManager
from binance.enums import * # Import enums for order types, sides etc.
from binance.exceptions import BinanceAPIException, BinanceRequestException

from app.config.settings import Settings # Import the Settings class
from app.services.exchange.exchange_client import ExchangeClient, OrderType, OrderSide
# Import the retry decorator
from app.services.execution.retry import retry_binance_operation

logger = logging.getLogger(__name__)

class BinanceExchangeClient(ExchangeClient):
    """Concrete implementation of ExchangeClient for Binance using AsyncClient."""

    def __init__(self, settings: Settings): # Inject Settings
        self.settings: Settings = settings
        self.client: Optional[AsyncClient] = None
        self.bsm: Optional[BinanceSocketManager] = None
        # Initialize client in an async context or dedicated method
        # asyncio.create_task(self._initialize_client())

    async def _initialize_client(self):
        """Initializes the AsyncClient."""
        try:
            self.client = await AsyncClient.create(
                api_key=self.settings.binance_api_key,
                api_secret=self.settings.binance_api_secret,
                testnet=self.settings.use_testnet
            )
            self.bsm = BinanceSocketManager(self.client)
            # Perform a test connection if needed
            await self.client.ping()
            logger.info("Binance AsyncClient initialized successfully.")
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Failed to initialize Binance AsyncClient: {e}")
            self.client = None
            self.bsm = None
        except Exception as e:
            logger.error(f"An unexpected error occurred during Binance client initialization: {e}")
            self.client = None
            self.bsm = None

    async def close_client(self):
        """Closes the AsyncClient session."""
        if self.client:
            await self.client.close_connection()
            logger.info("Binance AsyncClient connection closed.")

    def get_socket_manager(self):
        """Returns the BinanceSocketManager instance.

        Returns:
            BinanceSocketManager: The socket manager instance.
        """
        return self.bsm

    # --- Implementation of Abstract Methods ---

    @retry_binance_operation() # Apply decorator
    async def get_account_balance(self) -> Dict[str, float]:
        """Gets account balance for futures account."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
            # For futures, use futures_account_balance
            balances = await self.client.futures_account_balance()
            # Return a simplified dict: asset -> availableBalance
            # Note: You might want 'balance' (total) instead of 'availableBalance'
            return {item['asset']: float(item['availableBalance']) for item in balances}
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting account balance: {e}")
            raise # Re-raise or handle appropriately
        except Exception as e:
            logger.error(f"Unexpected error getting account balance: {e}")
            raise

    @retry_binance_operation()
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Gets latest price ticker for a symbol."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
            # For futures, use futures_symbol_ticker
            ticker = await self.client.futures_symbol_ticker(symbol=symbol)
            return ticker
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting ticker for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting ticker for {symbol}: {e}")
            raise

    @retry_binance_operation()
    async def get_order_book(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Gets the order book for a symbol."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
            # For futures, use futures_order_book
            order_book = await self.client.futures_order_book(symbol=symbol, limit=limit)
            return order_book
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting order book for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting order book for {symbol}: {e}")
            raise

    @retry_binance_operation()
    async def get_recent_trades(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Gets recent trades for a symbol."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
             # For futures, use futures_recent_trades
            trades = await self.client.futures_recent_trades(symbol=symbol, limit=limit)
            return trades
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting recent trades for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting recent trades for {symbol}: {e}")
            raise

    @retry_binance_operation()
    async def get_historical_klines(self,
                                   symbol: str,
                                   interval: str, # Use KLINE_INTERVAL enums from binance.enums if desired
                                   start_time: Optional[Union[int, str]] = None, # Allow str for easier use
                                   end_time: Optional[Union[int, str]] = None,
                                   limit: int = 500) -> List[Dict[str, Any]]:
        """Gets historical klines (candlesticks) for a symbol."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
            # For futures, use futures_klines
            # Convert start/end times if they are provided as strings
            start_str = str(start_time) if start_time else None
            end_str = str(end_time) if end_time else None

            klines = await self.client.futures_klines(
                symbol=symbol,
                interval=interval,
                startTime=start_str, # API expects string timestamps
                endTime=end_str,
                limit=limit
            )
            # Optional: Convert klines to a more usable format (e.g., pandas DataFrame) if needed downstream
            # For now, return the raw list of lists/dicts from the API
            return klines
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting klines for {symbol} ({interval}): {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting klines for {symbol} ({interval}): {e}")
            raise

    # Implementation was already added in the previous step, keeping it.
    @retry_binance_operation()
    async def place_order(self,
                         symbol: str,
                         order_type: Union[OrderType, str], # Use ORDER_TYPE enums
                         side: Union[OrderSide, str], # Use SIDE enums
                         quantity: float,
                         price: Optional[float] = None,
                         stop_price: Optional[float] = None,
                         take_profit_price: Optional[float] = None, # Added from interface
                         time_in_force: Optional[str] = None, # e.g., TIME_IN_FORCE_GTC
                         client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Places a standard order (MARKET, LIMIT, STOP_LOSS_LIMIT, TAKE_PROFIT_LIMIT)."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")

        # Convert enums to strings if necessary
        order_type_str = order_type.value if isinstance(order_type, OrderType) else order_type
        side_str = side.value if isinstance(side, OrderSide) else side

        params = {
            'symbol': symbol,
            'side': side_str,
            'type': order_type_str,
            'quantity': quantity,
        }
        # Use client's formatting helpers if available, otherwise manual format
        # Assuming manual formatting for now
        if price is not None:
             # TODO: Fetch precision dynamically from get_symbol_info
            params['price'] = f"{price:.8f}"
        # Add stopPrice ONLY if it's a STOP_LOSS* or TAKE_PROFIT* order type
        # The main entry order (MARKET/LIMIT) should not have these directly
        if order_type_str in [ORDER_TYPE_STOP_LOSS, ORDER_TYPE_STOP_LOSS_LIMIT, ORDER_TYPE_TAKE_PROFIT, ORDER_TYPE_TAKE_PROFIT_LIMIT, "STOP_MARKET", "TAKE_PROFIT_MARKET"]:
            if stop_price is None:
                 raise ValueError(f"Stop price (trigger) is required for {order_type_str} orders.")
            # TODO: Fetch precision dynamically from get_symbol_info
            params['stopprice'] = f"{stop_price:.8f}" # Use lowercase key based on API error

        # Remove the direct addition of takeProfitPrice for MARKET/LIMIT entry orders,
        # as this is likely incorrect for the futures_create_order endpoint when type is MARKET/LIMIT.
        # ExecutionService should handle placing separate TP orders if needed.
        # if take_profit_price is not None:
        #     params['takeProfitPrice'] = f"{take_profit_price:.8f}"
        if time_in_force is not None:
             params['timeInForce'] = time_in_force
        if client_order_id is not None:
            params['newClientOrderId'] = client_order_id

        # Add required params based on order type
        if order_type_str in [ORDER_TYPE_LIMIT, ORDER_TYPE_STOP_LOSS_LIMIT, ORDER_TYPE_TAKE_PROFIT_LIMIT]:
            if price is None:
                raise ValueError(f"Price is required for {order_type_str} orders.")
            if time_in_force is None:
                 params['timeInForce'] = TIME_IN_FORCE_GTC # Default GTC for limit orders

        # Check required params for stop/take profit orders
        if order_type_str in [ORDER_TYPE_STOP_LOSS, ORDER_TYPE_STOP_LOSS_LIMIT]:
             if stop_price is None:
                 raise ValueError(f"Stop price is required for {order_type_str} orders.")
        if order_type_str in [ORDER_TYPE_TAKE_PROFIT, ORDER_TYPE_TAKE_PROFIT_LIMIT]:
             # Check if take_profit_price is needed based on API (might use stopPrice instead for TP Market)
             if take_profit_price is None and order_type_str == ORDER_TYPE_TAKE_PROFIT_LIMIT : # TP Limit needs a price
                  raise ValueError(f"Take profit price is required for {order_type_str} orders.")
             elif stop_price is None: # TP Market uses stopPrice as trigger
                  raise ValueError(f"Stop price (trigger) is required for {order_type_str} orders.")

        # Removed redundant check for stop_price as it's handled within the type-specific logic above
        # if order_type_str in [ORDER_TYPE_STOP_LOSS, ORDER_TYPE_STOP_LOSS_LIMIT, ORDER_TYPE_TAKE_PROFIT, ORDER_TYPE_TAKE_PROFIT_LIMIT]:
        #      if stop_price is None: # Redundant check, kept for clarity
        #          raise ValueError(f"Stop price is required for {order_type_str} orders.")

        try:
            logger.info(f"Placing order with params: {params}")
            # For futures, use futures_create_order
            order = await self.client.futures_create_order(**params)
            return order
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error placing order for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error placing order for {symbol}: {e}")
            raise

    # Implementation was already added in the previous step, keeping it.
    @retry_binance_operation()
    async def cancel_order(self,
                          symbol: str,
                          order_id: Optional[str] = None, # Use string for orderId
                          client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """Cancels an active order."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        if not order_id and not client_order_id:
            raise ValueError("Either order_id or client_order_id must be provided.")

        params = {'symbol': symbol}
        if order_id:
            params['orderId'] = int(order_id) # API expects integer orderId
        if client_order_id:
            params['origClientOrderId'] = client_order_id

        try:
            # For futures, use futures_cancel_order
            result = await self.client.futures_cancel_order(**params)
            return result
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error cancelling order for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error cancelling order for {symbol}: {e}")
            raise

    @retry_binance_operation()
    async def get_order(self,
                       symbol: str, # Symbol is required for futures
                       order_id: Optional[str] = None, # Use string for orderId
                       client_order_id: Optional[str] = None
                       ) -> Dict[str, Any]:
        """Retrieves details of a specific order."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        if not order_id and not client_order_id:
            raise ValueError("Either order_id or client_order_id must be provided.")

        params = {'symbol': symbol}
        if order_id:
            params['orderId'] = int(order_id) # API expects integer orderId
        if client_order_id:
            params['origClientOrderId'] = client_order_id

        try:
             # For futures, use futures_get_order
            order = await self.client.futures_get_order(**params)
            return order
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting order for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting order for {symbol}: {e}")
            raise

    # Implementation was already added in the previous step, keeping it.
    @retry_binance_operation()
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieves all open orders for a symbol or all symbols."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        params = {}
        if symbol:
            params['symbol'] = symbol
        try:
            # For futures, use futures_get_open_orders
            open_orders = await self.client.futures_get_open_orders(**params)
            return open_orders
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting open orders for {symbol or 'all symbols'}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting open orders for {symbol or 'all symbols'}: {e}")
            raise

    @retry_binance_operation()
    # Implementation was already added in the previous step, keeping it.
    # @retry_binance_operation() # Already applied above at line 293
    async def get_all_orders(self,
                            symbol: str,
                            order_id: Optional[str] = None, # Use string for orderId
                            start_time: Optional[int] = None,
                            end_time: Optional[int] = None,
                            limit: int = 500) -> List[Dict[str, Any]]:
        """Retrieves all orders for a specific symbol."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        params = {
            'symbol': symbol,
            'limit': limit
        }
        if order_id:
            params['orderId'] = int(order_id) # API expects integer orderId
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time

        try:
            # For futures, use futures_get_all_orders
            all_orders = await self.client.futures_get_all_orders(**params)
            return all_orders
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting all orders for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting all orders for {symbol}: {e}")
            raise

    @retry_binance_operation()
    # Implementation was already added in the previous step, keeping it.
    # @retry_binance_operation() # Already applied above at line 327
    async def get_exchange_info(self) -> Dict[str, Any]:
        """Retrieves general exchange information."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
            # For futures, use futures_exchange_info
            info = await self.client.futures_exchange_info()
            return info
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting exchange info: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting exchange info: {e}")
            raise

    @retry_binance_operation()
    # Implementation was already added in the previous step, keeping it.
    # @retry_binance_operation() # Already applied above at line 345
    async def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Retrieves information about a specific symbol from exchange info."""
        try:
            exchange_info = await self.get_exchange_info()
            for symbol_data in exchange_info.get('symbols', []):
                if symbol_data.get('symbol') == symbol:
                    return symbol_data
            logger.warning(f"Symbol info not found for {symbol}")
            return None
        except Exception as e:
            # Error already logged in get_exchange_info
            logger.error(f"Could not retrieve symbol info for {symbol} due to underlying error.")
            return None

    # --- Additional Methods specific to Binance or needed by ExecutionService ---

    # Example: Method for OCO order placement (Implementation deferred to place_order or ExecutionService)
    # @retry_binance_operation() # Apply if implemented here
    async def create_oco_order(self,
                              symbol: str,
                              side: Union[OrderSide, str],
                              quantity: float,
                              price: float, # Entry price for the LIMIT order part
                              stop_price: float, # Trigger price for the Stop Loss order
                              stop_limit_price: Optional[float] = None, # Execution price for Stop Loss Limit
                              stop_limit_time_in_force: Optional[str] = TIME_IN_FORCE_GTC
                              ) -> Dict[str, Any]:
        """Places a futures OCO order (One Take Profit, One Stop Loss)."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")

        # Convert enums to strings if necessary
        side_str = side.value if isinstance(side, OrderSide) else side

        # OCO requires stopLimitPrice if stopPrice is provided for STOP_LOSS_LIMIT
        if stop_limit_price is None:
            stop_limit_price = stop_price # For STOP_LOSS, stopLimitPrice is same as stopPrice

        params = {
            'symbol': symbol,
            'side': side_str, # Side of the main order (TP is limit, SL is stop)
            'quantity': quantity,
            'price': f"{price:.8f}", # TP Limit price
            'stopPrice': f"{stop_price:.8f}", # SL trigger price
            'stopLimitPrice': f"{stop_limit_price:.8f}", # SL execution price (if STOP_LOSS_LIMIT)
            'stopLimitTimeInForce': stop_limit_time_in_force,
        }

        try:
            logger.info(f"Placing OCO order with params: {params}")
            # For futures, use futures_create_order with specific type?
            # Let's re-check python-binance docs.
            # It seems futures_create_order handles TP/SL market/limit orders directly.
            # OCO might be for spot or specific pairs. Let's adjust place_order instead.
            # For now, this method might not be directly usable as intended via a single call.
            # We might need place_order + separate SL/TP orders, or use place_order with TP/SL params.
            # Reverting to placeholder. We'll implement SL/TP logic in ExecutionService using place_order.
            logger.warning("Direct futures_create_oco_order might not be available or work as expected. SL/TP logic should use place_order with stopPrice/takeProfitPrice params or separate orders.")
            raise NotImplementedError("OCO order placement needs review based on python-binance futures capabilities.")
            # order = await self.client.futures_create_oco_order(**params) # This might not exist or work for futures
            # return order
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error placing OCO order for {symbol}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error placing OCO order for {symbol}: {e}")
            raise

    # Example: Method to get open positions (Futures specific)
    @retry_binance_operation()
    async def get_open_positions(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Gets open futures positions."""
        if not self.client: await self._initialize_client()
        if not self.client: raise ConnectionError("Binance client not initialized")
        try:
            # Note: python-binance uses account() for futures positions,
            # or get_position_risk() for more detailed risk info.
            # Adjust based on required data. Let's use get_position_risk for now.
            params = {}
            if symbol:
                params['symbol'] = symbol
            positions = await self.client.futures_position_information(**params)
            # Filter for positions with non-zero amount
            open_positions = [p for p in positions if float(p.get('positionAmt', 0)) != 0]
            return open_positions
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Binance API error getting positions: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting positions: {e}")
            raise