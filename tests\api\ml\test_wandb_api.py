#!/usr/bin/env python3
"""
Test Real W&B (Weights & Biases) API Integration
This script tests the actual W&B API connection using credentials from .env.mcp
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
import time
from datetime import datetime

# Load environment variables
load_dotenv('.env.mcp')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_wandb_api():
    """Test real W&B API connection and logging capabilities"""
    print("=" * 60)
    print("TESTING REAL W&B API CONNECTION")
    print("=" * 60)
    
    try:
        # Get W&B API key
        wandb_api_key = os.getenv('WANDB_API_KEY')
        wandb_entity = os.getenv('WANDB_ENTITY', 'samadeptc-elohim-tech-dynamics')
        
        print(f"✓ API Key: {wandb_api_key[:8]}..." if wandb_api_key else "✗ No API key")
        print(f"✓ Entity: {wandb_entity}")
        
        if not wandb_api_key:
            raise ValueError("WANDB_API_KEY must be set in .env.mcp file")
        
        # Import wandb after setting the API key
        import wandb
        
        # Set API key
        wandb.login(key=wandb_api_key)
        print("✓ W&B authentication successful")
        
        # Test 1: Initialize a run
        print("\n--- Test 1: Initialize W&B Run ---")
        run = wandb.init(
            project="strategy-ensemble-test",
            entity=wandb_entity,
            name=f"api-test-{int(time.time())}",
            tags=["api-test", "real-integration"],
            config={
                "test_type": "api_integration",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        )
        print(f"✓ W&B run initialized: {run.name}")
        print(f"  Run ID: {run.id}")
        print(f"  Project: {run.project}")
        print(f"  URL: {run.url}")
        
        # Test 2: Log metrics
        print("\n--- Test 2: Log Metrics ---")
        test_metrics = {
            "accuracy": 0.95,
            "loss": 0.05,
            "precision": 0.93,
            "recall": 0.97,
            "f1_score": 0.95,
            "epoch": 1
        }
        
        for metric, value in test_metrics.items():
            wandb.log({metric: value})
            print(f"  ✓ Logged {metric}: {value}")
        
        # Test 3: Log system metrics
        print("\n--- Test 3: Log System Metrics ---")
        system_metrics = {
            "cpu_usage": 65.2,
            "memory_usage": 78.5,
            "gpu_usage": 45.0,
            "training_time": 125.3
        }
        
        wandb.log(system_metrics)
        print(f"✓ Logged {len(system_metrics)} system metrics")
        
        # Test 4: Log strategy performance
        print("\n--- Test 4: Log Strategy Performance ---")
        strategy_metrics = {
            "portfolio_value": 105000.0,
            "total_return": 5.0,
            "sharpe_ratio": 1.85,
            "max_drawdown": -2.3,
            "win_rate": 0.68,
            "total_trades": 45
        }
        
        wandb.log(strategy_metrics)
        print(f"✓ Logged {len(strategy_metrics)} strategy metrics")
        
        # Test 5: Log ensemble metrics
        print("\n--- Test 5: Log Ensemble Metrics ---")
        ensemble_metrics = {
            "ensemble_confidence": 0.87,
            "strategy_agreement": 0.73,
            "signal_strength": 0.92,
            "execution_time_ms": 23.5,
            "cross_validation_score": 0.89
        }
        
        wandb.log(ensemble_metrics)
        print(f"✓ Logged {len(ensemble_metrics)} ensemble metrics")
        
        # Test 6: Log custom table
        print("\n--- Test 6: Log Strategy Comparison Table ---")
        strategy_data = [
            ["Technical Analysis", 0.72, 1.45, -3.2, 42],
            ["Grid Strategy", 0.68, 1.23, -2.8, 38],
            ["ML Strategy", 0.75, 1.67, -4.1, 35],
            ["Ensemble", 0.78, 1.85, -2.3, 45]
        ]
        
        strategy_table = wandb.Table(
            columns=["Strategy", "Win Rate", "Sharpe Ratio", "Max Drawdown", "Trades"],
            data=strategy_data
        )
        wandb.log({"strategy_comparison": strategy_table})
        print("✓ Logged strategy comparison table")
        
        # Test 7: Log configuration
        print("\n--- Test 7: Update Configuration ---")
        wandb.config.update({
            "risk_per_trade": 1.0,
            "max_position_size": 20.0,
            "stop_loss_pct": 3.0,
            "take_profit_pct": 6.0,
            "strategy_weights": {
                "technical_analysis": 0.25,
                "grid_strategy": 0.25,
                "ml_strategy": 0.25,
                "ensemble": 0.25
            }
        })
        print("✓ Updated run configuration")
        
        # Test 8: Add run summary
        print("\n--- Test 8: Add Run Summary ---")
        run.summary.update({
            "final_accuracy": 0.95,
            "best_sharpe_ratio": 1.85,
            "total_profit": 5000.0,
            "test_status": "passed",
            "api_integration": "successful"
        })
        print("✓ Updated run summary")
        
        # Finish the run
        wandb.finish()
        print("✓ W&B run finished successfully")
        
        print("\n" + "=" * 60)
        print("✅ ALL W&B API TESTS PASSED")
        print("Real W&B API is working correctly!")
        print("Strategy ensemble logging is operational.")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ W&B API test failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Try to finish the run if it was started
        try:
            if 'wandb' in locals():
                wandb.finish()
        except:
            pass
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_real_wandb_api())
    exit(0 if success else 1)