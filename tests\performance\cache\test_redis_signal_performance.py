#!/usr/bin/env python3
"""
Redis Signal Processing Performance Tests
Validates that cache operations complete in under 100ms for Task 1.2.1
"""

import asyncio
import time
import statistics
import os
import sys
from datetime import datetime
from typing import Dict, List, Tuple

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_redis_mcp_connection():
    """Test Redis MCP server connectivity."""
    print("🔍 Testing Redis MCP Connection...")
    
    try:
        from app.services.mcp.redis_service import RedisService
        
        redis_service = RedisService()
        connected = await redis_service.connect()
        
        if connected:
            print("✅ Redis MCP connection successful")
            
            # Test basic operations
            start_time = time.perf_counter()
            await redis_service.redis.ping()
            ping_time = (time.perf_counter() - start_time) * 1000
            
            print(f"📊 Redis ping: {ping_time:.2f}ms")
            
            await redis_service.disconnect()
            return True, ping_time
        else:
            print("❌ Redis MCP connection failed")
            return False, 0
            
    except Exception as e:
        print(f"❌ Redis MCP test failed: {e}")
        return False, 0

async def test_signal_caching_performance():
    """Test strategy signal caching performance."""
    print("\n📊 Testing Signal Caching Performance...")
    
    try:
        from app.services.mcp.redis_service import RedisService
        from app.strategies.ensemble.signal_aggregator import StrategySignal, MarketSnapshot
        
        redis_service = RedisService()
        await redis_service.connect()
        
        # Test data
        test_signals = [
            {
                'strategy_name': 'GridStrategy',
                'signal_data': {
                    'action': 'BUY',
                    'confidence': 0.85,
                    'quantity': 0.05,
                    'price': 43250.0
                },
                'market_hash': 'test_hash_1'
            },
            {
                'strategy_name': 'TechnicalAnalysisStrategy',
                'signal_data': {
                    'action': 'SELL',
                    'confidence': 0.75,
                    'quantity': 0.03,
                    'price': 43250.0
                },
                'market_hash': 'test_hash_2'
            },
            {
                'strategy_name': 'TrendFollowingStrategy',
                'signal_data': {
                    'action': 'HOLD',
                    'confidence': 0.65,
                    'quantity': 0.0,
                    'price': 43250.0
                },
                'market_hash': 'test_hash_3'
            }
        ]
        
        # Test signal caching (write operations)
        cache_times = []
        for signal in test_signals:
            start_time = time.perf_counter()
            await redis_service.cache_strategy_signals(
                signal['strategy_name'],
                signal['signal_data'],
                signal['market_hash']
            )
            cache_time = (time.perf_counter() - start_time) * 1000
            cache_times.append(cache_time)
        
        # Test signal retrieval (read operations)
        retrieval_times = []
        for signal in test_signals:
            start_time = time.perf_counter()
            cached_signal = await redis_service.get_cached_signal(
                signal['strategy_name'],
                signal['market_hash']
            )
            retrieval_time = (time.perf_counter() - start_time) * 1000
            retrieval_times.append(retrieval_time)
            
            if cached_signal:
                print(f"✅ Retrieved signal for {signal['strategy_name']}: {retrieval_time:.2f}ms")
            else:
                print(f"❌ Failed to retrieve signal for {signal['strategy_name']}")
        
        # Performance statistics
        avg_cache_time = statistics.mean(cache_times)
        max_cache_time = max(cache_times)
        avg_retrieval_time = statistics.mean(retrieval_times)
        max_retrieval_time = max(retrieval_times)
        
        print(f"\n📈 Signal Caching Performance:")
        print(f"   Average cache time: {avg_cache_time:.2f}ms")
        print(f"   Max cache time: {max_cache_time:.2f}ms")
        print(f"   Average retrieval time: {avg_retrieval_time:.2f}ms")
        print(f"   Max retrieval time: {max_retrieval_time:.2f}ms")
        
        # Validate 100ms requirement
        cache_performance_ok = max_cache_time < 100 and max_retrieval_time < 100
        
        if cache_performance_ok:
            print("✅ Signal caching performance: PASSED (<100ms)")
        else:
            print("❌ Signal caching performance: FAILED (>=100ms)")
        
        await redis_service.disconnect()
        return cache_performance_ok, {
            'avg_cache_time': avg_cache_time,
            'max_cache_time': max_cache_time,
            'avg_retrieval_time': avg_retrieval_time,
            'max_retrieval_time': max_retrieval_time
        }
        
    except Exception as e:
        print(f"❌ Signal caching test failed: {e}")
        return False, {}

async def test_batch_signal_operations():
    """Test batch signal operations performance."""
    print("\n🔄 Testing Batch Signal Operations...")
    
    try:
        from app.services.mcp.redis_service import RedisService
        
        redis_service = RedisService()
        await redis_service.connect()
        
        # Generate test data for batch operations
        num_signals = 10
        strategy_names = [f'Strategy_{i}' for i in range(num_signals)]
        market_hash = 'batch_test_hash'
        
        # Cache signals first
        cache_updates = []
        for i, strategy_name in enumerate(strategy_names):
            signal_data = {
                'action': 'BUY' if i % 2 == 0 else 'SELL',
                'confidence': 0.5 + (i * 0.05),
                'quantity': 0.01 * (i + 1),
                'price': 43000.0 + i * 10
            }
            
            cache_updates.append({
                'key': f"ensemble:signals:{strategy_name}:{market_hash}",
                'value': json.dumps({
                    'signal': signal_data,
                    'timestamp': datetime.now().isoformat(),
                    'strategy': strategy_name
                }),
                'ttl': 30
            })
        
        # Test batch cache update
        start_time = time.perf_counter()
        batch_success = await redis_service.pipeline_cache_update(cache_updates)
        batch_cache_time = (time.perf_counter() - start_time) * 1000
        
        print(f"📦 Batch cache update ({num_signals} signals): {batch_cache_time:.2f}ms")
        
        # Test batch retrieval
        start_time = time.perf_counter()
        retrieved_signals = await redis_service.batch_get_signals(strategy_names, market_hash)
        batch_retrieval_time = (time.perf_counter() - start_time) * 1000
        
        print(f"📦 Batch retrieval ({len(retrieved_signals)} signals): {batch_retrieval_time:.2f}ms")
        
        # Validate batch performance
        batch_performance_ok = batch_cache_time < 100 and batch_retrieval_time < 100
        
        if batch_performance_ok:
            print("✅ Batch operations performance: PASSED (<100ms)")
        else:
            print("❌ Batch operations performance: FAILED (>=100ms)")
        
        await redis_service.disconnect()
        return batch_performance_ok, {
            'batch_cache_time': batch_cache_time,
            'batch_retrieval_time': batch_retrieval_time,
            'signals_processed': num_signals,
            'signals_retrieved': len(retrieved_signals)
        }
        
    except Exception as e:
        print(f"❌ Batch operations test failed: {e}")
        return False, {}

async def test_weight_allocation_performance():
    """Test weight allocation caching performance."""
    print("\n⚖️ Testing Weight Allocation Performance...")
    
    try:
        from app.services.mcp.redis_service import RedisService
        
        redis_service = RedisService()
        await redis_service.connect()
        
        # Test weight caching
        test_weights = {
            'GridStrategy': 0.4,
            'TechnicalAnalysisStrategy': 0.35,
            'TrendFollowingStrategy': 0.25
        }
        
        confidence = 0.85
        
        # Test cache write
        start_time = time.perf_counter()
        await redis_service.cache_strategy_weights(test_weights, confidence)
        cache_time = (time.perf_counter() - start_time) * 1000
        
        # Test cache read
        start_time = time.perf_counter()
        cached_weights = await redis_service.get_cached_weights()
        retrieval_time = (time.perf_counter() - start_time) * 1000
        
        print(f"⚖️ Weight cache time: {cache_time:.2f}ms")
        print(f"⚖️ Weight retrieval time: {retrieval_time:.2f}ms")
        
        # Validate weights
        if cached_weights and cached_weights['weights'] == test_weights:
            print("✅ Weight data integrity: PASSED")
            data_integrity_ok = True
        else:
            print("❌ Weight data integrity: FAILED")
            data_integrity_ok = False
        
        # Validate performance
        weight_performance_ok = cache_time < 100 and retrieval_time < 100
        
        if weight_performance_ok:
            print("✅ Weight allocation performance: PASSED (<100ms)")
        else:
            print("❌ Weight allocation performance: FAILED (>=100ms)")
        
        await redis_service.disconnect()
        return weight_performance_ok and data_integrity_ok, {
            'cache_time': cache_time,
            'retrieval_time': retrieval_time,
            'data_integrity': data_integrity_ok
        }
        
    except Exception as e:
        print(f"❌ Weight allocation test failed: {e}")
        return False, {}

async def test_signal_aggregation_integration():
    """Test signal aggregation with caching integration."""
    print("\n🔗 Testing Signal Aggregation Integration...")
    
    try:
        from app.services.mcp.redis_service import RedisService
        from app.strategies.ensemble.signal_aggregator import (
            HighPerformanceSignalAggregator, StrategySignal, MarketSnapshot
        )
        
        redis_service = RedisService()
        await redis_service.connect()
        
        # Create signal aggregator
        aggregator = HighPerformanceSignalAggregator(redis_service)
        
        # Create test signals
        test_signals = {
            'GridStrategy': StrategySignal(
                strategy_name='GridStrategy',
                action='BUY',
                confidence=0.85,
                quantity=0.05,
                price=43250.0,
                timestamp=datetime.now()
            ),
            'TechnicalAnalysisStrategy': StrategySignal(
                strategy_name='TechnicalAnalysisStrategy',
                action='BUY',
                confidence=0.75,
                quantity=0.03,
                price=43250.0,
                timestamp=datetime.now()
            ),
            'TrendFollowingStrategy': StrategySignal(
                strategy_name='TrendFollowingStrategy',
                action='HOLD',
                confidence=0.65,
                quantity=0.0,
                price=43250.0,
                timestamp=datetime.now()
            )
        }
        
        # Create market snapshot
        market_snapshot = MarketSnapshot(
            symbol='BTCUSDT',
            price=43250.0,
            volume=1000000,
            volatility=0.025,
            timestamp=datetime.now()
        )
        
        # Test aggregation (first call - cache miss)
        start_time = time.perf_counter()
        aggregated_1 = await aggregator.aggregate_signals(test_signals, market_snapshot)
        first_call_time = (time.perf_counter() - start_time) * 1000
        
        print(f"🔗 First aggregation (cache miss): {first_call_time:.2f}ms")
        print(f"   Final action: {aggregated_1.final_action}")
        print(f"   Confidence: {aggregated_1.confidence:.3f}")
        print(f"   Cache hit: {aggregated_1.cache_hit}")
        
        # Test aggregation (second call - cache hit)
        start_time = time.perf_counter()
        aggregated_2 = await aggregator.aggregate_signals(test_signals, market_snapshot)
        second_call_time = (time.perf_counter() - start_time) * 1000
        
        print(f"🔗 Second aggregation (cache hit): {second_call_time:.2f}ms")
        print(f"   Cache hit: {aggregated_2.cache_hit}")
        
        # Test performance stats
        performance_stats = aggregator.get_performance_stats()
        print(f"📊 Aggregation Performance Stats:")
        print(f"   Total aggregations: {performance_stats['total_aggregations']}")
        print(f"   Cache hit rate: {performance_stats['cache_hit_rate']}%")
        print(f"   Average processing time: {performance_stats['avg_processing_time_ms']:.2f}ms")
        
        # Validate performance
        cache_speedup = first_call_time / second_call_time if second_call_time > 0 else 1
        integration_performance_ok = (
            first_call_time < 100 and 
            second_call_time < 50 and  # Cache hits should be faster
            aggregated_2.cache_hit and
            cache_speedup > 1.5  # Cache should provide speedup
        )
        
        if integration_performance_ok:
            print("✅ Signal aggregation integration: PASSED")
        else:
            print("❌ Signal aggregation integration: FAILED")
        
        await redis_service.disconnect()
        return integration_performance_ok, {
            'first_call_time': first_call_time,
            'second_call_time': second_call_time,
            'cache_speedup': cache_speedup,
            'cache_hit_rate': performance_stats['cache_hit_rate']
        }
        
    except Exception as e:
        print(f"❌ Signal aggregation integration test failed: {e}")
        return False, {}

async def test_redis_performance_under_load():
    """Test Redis performance under concurrent load."""
    print("\n🚀 Testing Redis Performance Under Load...")
    
    try:
        from app.services.mcp.redis_service import RedisService
        
        redis_service = RedisService()
        await redis_service.connect()
        
        # Create concurrent operations
        num_concurrent = 20
        operations_per_task = 5
        
        async def concurrent_cache_operations(task_id: int):
            """Perform cache operations for load testing."""
            task_times = []
            
            for i in range(operations_per_task):
                # Cache operation
                start_time = time.perf_counter()
                await redis_service.cache_strategy_signals(
                    f'TestStrategy_{task_id}',
                    {
                        'action': 'BUY',
                        'confidence': 0.5 + (i * 0.1),
                        'quantity': 0.01,
                        'price': 43000 + i
                    },
                    f'hash_{task_id}_{i}'
                )
                cache_time = (time.perf_counter() - start_time) * 1000
                
                # Retrieval operation
                start_time = time.perf_counter()
                await redis_service.get_cached_signal(
                    f'TestStrategy_{task_id}',
                    f'hash_{task_id}_{i}'
                )
                retrieval_time = (time.perf_counter() - start_time) * 1000
                
                total_operation_time = cache_time + retrieval_time
                task_times.append(total_operation_time)
            
            return task_times
        
        # Run concurrent operations
        start_time = time.perf_counter()
        
        tasks = [
            concurrent_cache_operations(task_id) 
            for task_id in range(num_concurrent)
        ]
        
        results = await asyncio.gather(*tasks)
        
        total_load_test_time = (time.perf_counter() - start_time) * 1000
        
        # Analyze results
        all_times = [time for task_times in results for time in task_times]
        
        avg_operation_time = statistics.mean(all_times)
        max_operation_time = max(all_times)
        p95_operation_time = statistics.quantiles(all_times, n=20)[18]  # 95th percentile
        
        total_operations = num_concurrent * operations_per_task * 2  # cache + retrieval
        throughput = total_operations / (total_load_test_time / 1000)  # ops per second
        
        print(f"🚀 Load Test Results:")
        print(f"   Concurrent tasks: {num_concurrent}")
        print(f"   Total operations: {total_operations}")
        print(f"   Total time: {total_load_test_time:.2f}ms")
        print(f"   Throughput: {throughput:.1f} ops/sec")
        print(f"   Average operation time: {avg_operation_time:.2f}ms")
        print(f"   Max operation time: {max_operation_time:.2f}ms")
        print(f"   95th percentile: {p95_operation_time:.2f}ms")
        
        # Validate load performance
        load_performance_ok = (
            max_operation_time < 200 and  # Allow higher limit under load
            p95_operation_time < 100 and
            throughput > 100  # At least 100 ops/sec
        )
        
        if load_performance_ok:
            print("✅ Load performance: PASSED")
        else:
            print("❌ Load performance: FAILED")
        
        await redis_service.disconnect()
        return load_performance_ok, {
            'avg_operation_time': avg_operation_time,
            'max_operation_time': max_operation_time,
            'p95_operation_time': p95_operation_time,
            'throughput': throughput,
            'total_operations': total_operations
        }
        
    except Exception as e:
        print(f"❌ Load test failed: {e}")
        return False, {}

async def main():
    """Run all Redis signal processing performance tests."""
    print("🚀 Starting Redis Signal Processing Performance Tests")
    print("=" * 70)
    print("📋 Task 1.2.1: Implement Redis signal caching")
    print("🎯 Target: All cache operations complete in <100ms")
    print("=" * 70)
    
    tests = [
        ("Redis MCP Connection", test_redis_mcp_connection),
        ("Signal Caching Performance", test_signal_caching_performance),
        ("Batch Signal Operations", test_batch_signal_operations),
        ("Weight Allocation Performance", test_weight_allocation_performance),
        ("Signal Aggregation Integration", test_signal_aggregation_integration),
        ("Redis Performance Under Load", test_redis_performance_under_load)
    ]
    
    results = {}
    performance_data = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            result, data = await test_func()
            results[test_name] = result
            performance_data[test_name] = data
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
            performance_data[test_name] = {}
    
    # Print comprehensive summary
    print("\n" + "=" * 70)
    print("📋 REDIS SIGNAL PROCESSING TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    failed = 0
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(tests)} tests, {passed} passed, {failed} failed")
    
    # Performance requirements validation
    print("\n🎯 PERFORMANCE REQUIREMENTS VALIDATION:")
    
    # Check 100ms requirement for critical operations
    critical_operations = []
    
    if 'Signal Caching Performance' in performance_data:
        data = performance_data['Signal Caching Performance']
        if data:
            critical_operations.extend([
                ("Signal Cache Write", data.get('max_cache_time', 0)),
                ("Signal Cache Read", data.get('max_retrieval_time', 0))
            ])
    
    if 'Weight Allocation Performance' in performance_data:
        data = performance_data['Weight Allocation Performance']
        if data:
            critical_operations.extend([
                ("Weight Cache Write", data.get('cache_time', 0)),
                ("Weight Cache Read", data.get('retrieval_time', 0))
            ])
    
    if 'Batch Signal Operations' in performance_data:
        data = performance_data['Batch Signal Operations']
        if data:
            critical_operations.extend([
                ("Batch Cache Update", data.get('batch_cache_time', 0)),
                ("Batch Signal Retrieval", data.get('batch_retrieval_time', 0))
            ])
    
    all_under_100ms = True
    for operation_name, operation_time in critical_operations:
        status = "✅" if operation_time < 100 else "❌"
        print(f"   {status} {operation_name}: {operation_time:.2f}ms")
        if operation_time >= 100:
            all_under_100ms = False
    
    print(f"\n🏆 FINAL RESULT:")
    if failed == 0 and all_under_100ms:
        print("✅ Task 1.2.1 - COMPLETED SUCCESSFULLY")
        print("🎉 All Redis cache operations complete in <100ms")
        print("🚀 Signal aggregation with sub-second response achieved")
    else:
        print("❌ Task 1.2.1 - REQUIREMENTS NOT MET")
        if failed > 0:
            print(f"   {failed} test(s) failed")
        if not all_under_100ms:
            print("   Some operations exceed 100ms requirement")
    
    # Additional performance insights
    if 'Signal Aggregation Integration' in performance_data:
        data = performance_data['Signal Aggregation Integration']
        if data:
            cache_speedup = data.get('cache_speedup', 1)
            hit_rate = data.get('cache_hit_rate', 0)
            print(f"\n📈 CACHE EFFECTIVENESS:")
            print(f"   Cache speedup: {cache_speedup:.1f}x")
            print(f"   Cache hit rate: {hit_rate}%")
    
    if 'Redis Performance Under Load' in performance_data:
        data = performance_data['Redis Performance Under Load']
        if data:
            throughput = data.get('throughput', 0)
            print(f"   Throughput under load: {throughput:.1f} ops/sec")

if __name__ == "__main__":
    import json
    asyncio.run(main())