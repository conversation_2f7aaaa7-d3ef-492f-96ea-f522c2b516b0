[tool:pytest]
markers =
    integration: marks tests as integration tests that require real sandbox environments
    unit: marks tests as unit tests that use mocking
    slow: marks tests as slow running
    e2b: marks tests that require E2B_API_KEY
    pyodide: marks tests that require Deno for Pyodide

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Default to running unit tests only
addopts = -v --tb=short -m "not integration"

# Minimum version requirements
minversion = 6.0

# Configure logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Configure pytest-asyncio
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function 