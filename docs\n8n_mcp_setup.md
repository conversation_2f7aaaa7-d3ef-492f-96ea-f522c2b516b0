# n8n MCP Server Setup Guide

This guide explains how to set up and configure the n8n MCP server for the Crypto_App_V2 project.

## Overview

The n8n MCP server allows AI assistants to interact with n8n workflows through natural language. It provides tools to:

- List, create, update, and delete workflows
- Activate and deactivate workflows
- Execute workflows and monitor their status
- Access workflow information and execution statistics

## Prerequisites

- n8n instance running (typically on http://localhost:5678)
- API key for your n8n instance

## Configuration

### 1. Update Environment Variables

Edit the `.vscode/n8n-mcp.js` file to update the environment variables with your n8n instance details:

```javascript
const env = {
  ...process.env,
  N8N_API_URL: 'http://localhost:5678/api/v1',  // Update with your n8n instance URL
  N8N_API_KEY: 'your_actual_api_key',           // Update with your actual API key
  N8N_WEBHOOK_USERNAME: 'username',             // Optional: for webhook authentication
  N8N_WEBHOOK_PASSWORD: 'password',             // Optional: for webhook authentication
  DEBUG: 'true'
};
```

### 2. Generating an n8n API Key

1. Open your n8n instance in a browser
2. Go to Settings > API > API Keys
3. Create a new API key with appropriate permissions
4. Copy the key to the `N8N_API_KEY` environment variable in the `.vscode/n8n-mcp.js` file

### 3. Starting the Server

You can start the n8n MCP server using the following command:

```powershell
.\start_mcp_servers.ps1 -Servers n8n
```

Or start all MCP servers including n8n:

```powershell
.\start_mcp_servers.ps1
```

## Available Tools

The n8n MCP server provides the following tools:

### Workflow Management

- `workflow_list`: List all workflows
- `workflow_get`: Get details of a specific workflow
- `workflow_create`: Create a new workflow
- `workflow_update`: Update an existing workflow
- `workflow_delete`: Delete a workflow
- `workflow_activate`: Activate a workflow
- `workflow_deactivate`: Deactivate a workflow

### Execution Management

- `execution_run`: Execute a workflow via the API
- `run_webhook`: Execute a workflow via a webhook
- `execution_get`: Get details of a specific execution
- `execution_list`: List executions for a workflow
- `execution_stop`: Stop a running execution

## Using Webhooks

To use webhook functionality:

1. Create a webhook-triggered workflow in n8n
2. Set up Basic Authentication on your webhook node
3. Use the `run_webhook` tool to trigger the workflow, passing the workflow name

The webhook authentication is handled automatically using the `N8N_WEBHOOK_USERNAME` and `N8N_WEBHOOK_PASSWORD` environment variables.

## Troubleshooting

If you encounter issues with the n8n MCP server:

1. Check that your n8n instance is running
2. Verify that the API URL is correct
3. Ensure your API key has the necessary permissions
4. Check the logs for any error messages

## Additional Resources

- [n8n Documentation](https://docs.n8n.io/)
- [n8n API Documentation](https://docs.n8n.io/api/)
- [n8n MCP Server GitHub Repository](https://github.com/leonardsellem/n8n-mcp-server)
