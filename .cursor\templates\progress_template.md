# Project Progress: [Project Name] - Last Updated: YYYY-MM-DD

## 1. Overall Status
- **Current Phase**: (e.g., Development, Alpha, Beta, MVP Release)
- **Overall Health**: (e.g., <PERSON>, Yellow, Red - with brief explanation)
- **Next Major Milestone**: (Name and target date if known)

## 2. Completed Milestones / Key Features
- **Milestone 1**: [Name] - Completed: YYYY-MM-DD
  - Key achievements/features delivered.
- **Feature A**: [Name] - Completed: YYYY-MM-DD
  - Description of functionality.

## 3. Current Sprint / Iteration Goals (if applicable)
- **Sprint Name/Number**:
- **Start Date**: YYYY-MM-DD, **End Date**: YYYY-MM-DD
- **Goals**:
  - Goal 1:
  - Goal 2:
- **Progress**: (e.g., X% complete, specific tasks done/remaining)

## 4. Ongoing Tasks
- **Task 1**: [Description] - Assignee: [Name/Cascade], Status: [e.g., In Progress, Blocked]
- **Task 2**: [Description] - Assignee: [Name/Cascade], Status: [e.g., In Progress, Blocked]

## 5. Upcoming Tasks / Backlog Highlights
- **Task A**: [Description] - Priority: [High/Medium/Low]
- **Feature B**: [Description] - Priority: [High/Medium/Low]

## 6. Risks & Issues Impacting Progress
- **Risk/Issue 1**: [Description] - Mitigation/Action:
- **Risk/Issue 2**: [Description] - Mitigation/Action:

## 7. Key Decisions Log (Relevant to Progress)
- Decision made on YYYY-MM-DD regarding X, impact on Y.
