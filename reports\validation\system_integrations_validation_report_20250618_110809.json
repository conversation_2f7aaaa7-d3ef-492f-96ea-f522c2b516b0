{"timestamp": "2025-06-18T11:07:47.399457", "mcp_servers": {}, "external_apis": {"binance_connection": {"success": false, "details": {}, "error": "BinanceExchangeClient.__init__() missing 1 required positional argument: 'settings'", "timestamp": "2025-06-18T11:07:53.448381"}, "wandb_api_key": {"success": false, "details": {}, "error": "WANDB_API_KEY not found in environment", "timestamp": "2025-06-18T11:08:09.229484"}}, "database_connections": {"supabase_service": {"success": false, "details": {}, "error": "No module named 'app.services.database'", "timestamp": "2025-06-18T11:08:09.231645"}, "redis_service": {"success": false, "details": {}, "error": "'RedisService' object has no attribute 'set_value'", "timestamp": "2025-06-18T11:08:09.878288"}}, "websocket_connections": {"binance_websocket": {"success": false, "details": {}, "error": "No module named 'app.services.websocket'", "timestamp": "2025-06-18T11:08:09.880222"}}, "configuration_checks": {"env_var_binance_api_key": {"success": true, "details": {"length": 64, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:07:47.401798"}, "env_var_binance_api_secret": {"success": true, "details": {"length": 64, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:07:47.402297"}, "env_var_supabase_url": {"success": true, "details": {"length": 40, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:07:47.403221"}, "env_var_supabase_key": {"success": true, "details": {"length": 208, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:07:47.404146"}, "env_var_wandb_api_key": {"success": false, "details": {}, "error": "Environment variable WANDB_API_KEY not set", "timestamp": "2025-06-18T11:07:47.404711"}, "file_access_.env": {"success": true, "details": {}, "error": null, "timestamp": "2025-06-18T11:07:47.407650"}, "file_access_app_config_settings.py": {"success": true, "details": {}, "error": null, "timestamp": "2025-06-18T11:07:47.411184"}, "file_access_requirements.txt": {"success": true, "details": {}, "error": null, "timestamp": "2025-06-18T11:07:47.413434"}}, "summary": {"total_tests": 13, "passed": 7, "failed": 6, "errors": ["configuration_checks - env_var_wandb_api_key: Environment variable WANDB_API_KEY not set", "external_apis - binance_connection: BinanceExchangeClient.__init__() missing 1 required positional argument: 'settings'", "external_apis - wandb_api_key: WANDB_API_KEY not found in environment", "database_connections - supabase_service: No module named 'app.services.database'", "database_connections - redis_service: 'RedisService' object has no attribute 'set_value'", "websocket_connections - binance_websocket: No module named 'app.services.websocket'"]}}