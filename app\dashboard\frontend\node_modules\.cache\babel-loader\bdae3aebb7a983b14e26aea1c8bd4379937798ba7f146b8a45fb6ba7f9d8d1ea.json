{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Container,Typography,Box,Paper,Grid,CircularProgress,Alert,Snackbar,Badge,Tabs,Tab}from'@mui/material';import{apiClient}from'../services/api';import websocketService,{WebSocketEventType}from'../services/websocket';import analyticsWebsocketService,{AnalyticsEventType}from'../services/analyticsWebsocket';import AccountStatistics from'./account/AccountStatistics';import TradesTable from'./common/TradesTable';// TabPanel helper component\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function TabPanel(props){const{children,value,index,...other}=props;return/*#__PURE__*/_jsx(\"div\",{role:\"tabpanel\",hidden:value!==index,id:`simple-tabpanel-${index}`,\"aria-labelledby\":`simple-tab-${index}`,...other,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{pt:3},children:children})});}function a11yProps(index){return{id:`simple-tab-${index}`,'aria-controls':`simple-tabpanel-${index}`};}const TradeDashboard=()=>{const[activeTrades,setActiveTrades]=useState([]);const[recentTrades,setRecentTrades]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[notification,setNotification]=useState({open:false,message:'',severity:'info'});const[wsStatus,setWsStatus]=useState('disconnected');const[currentTab,setCurrentTab]=useState(0);const[analyticsWsStatus,setAnalyticsWsStatus]=useState('disconnected');const[marketState,setMarketState]=useState(null);const[strategyWeights,setStrategyWeights]=useState(null);const[performanceMetrics,setPerformanceMetrics]=useState(null);const handleTabChange=(event,newValue)=>{setCurrentTab(newValue);};// Generate mock trades for demo purposes\nconst generateMockActiveTrades=()=>{return[{trade_id:'trade_001',symbol:'BTCUSDT',entry_fill_price:42500.50,entry_fill_qty:0.025,entry_side:'BUY',sl_price:41000.00,tp_price:45000.00,status:'SLTP_PLACED',created_at:new Date(Date.now()-3600000).toISOString(),updated_at:new Date(Date.now()-1800000).toISOString()},{trade_id:'trade_002',symbol:'ETHUSDT',entry_fill_price:2650.25,entry_fill_qty:0.5,entry_side:'SELL',sl_price:2750.00,tp_price:2550.00,status:'ENTRY_FILLED',created_at:new Date(Date.now()-7200000).toISOString(),updated_at:new Date(Date.now()-3600000).toISOString()}];};const generateMockRecentTrades=()=>{return[{trade_id:'trade_003',symbol:'ADAUSDT',entry_fill_price:0.485,entry_fill_qty:2000,entry_side:'BUY',sl_price:null,tp_price:0.52,status:'CLOSED_TP',created_at:new Date(Date.now()-86400000).toISOString(),updated_at:new Date(Date.now()-82800000).toISOString()},{trade_id:'trade_004',symbol:'SOLUSDT',entry_fill_price:95.75,entry_fill_qty:10,entry_side:'BUY',sl_price:90.00,tp_price:null,status:'CLOSED_SL',created_at:new Date(Date.now()-172800000).toISOString(),updated_at:new Date(Date.now()-169200000).toISOString()}];};// Fetch active trades\nconst fetchActiveTrades=useCallback(async()=>{try{const response=await apiClient.get('/api/trading/active-trades');setActiveTrades(response.data);}catch(err){console.error('Error fetching active trades:',err);// Use mock data instead of showing error\nsetActiveTrades(generateMockActiveTrades());}},[]);// Fetch recent trades\nconst fetchRecentTrades=useCallback(async()=>{try{const response=await apiClient.get('/api/trading/recent-trades');setRecentTrades(response.data);}catch(err){console.error('Error fetching recent trades:',err);// Use mock data instead of showing error\nsetRecentTrades(generateMockRecentTrades());}},[]);// Fetch all trade data\nuseEffect(()=>{const fetchTrades=async()=>{try{setLoading(true);await Promise.all([fetchActiveTrades(),fetchRecentTrades()]);}catch(err){console.error('Error in fetchTrades:',err);setError('Failed to fetch trades. Using demo data.');// Fallback to mock data\nsetActiveTrades(generateMockActiveTrades());setRecentTrades(generateMockRecentTrades());}finally{setLoading(false);}};fetchTrades();// WebSocket event handlers\nconst handleTradeUpdate=data=>{const updatedTrade={trade_id:data.trade_id,symbol:data.symbol,entry_side:data.entry_side,entry_fill_price:data.entry_price||0,entry_fill_qty:data.entry_qty||0,sl_price:data.sl_price,tp_price:data.tp_price,exit_price:data.exit_price||undefined,current_price:data.current_price,status:data.status,created_at:data.timestamp,updated_at:data.timestamp};setActiveTrades(prev=>prev.map(t=>t.trade_id===updatedTrade.trade_id?updatedTrade:t));setNotification({open:true,message:`Trade ${updatedTrade.trade_id} updated: ${updatedTrade.status}`,severity:'info'});};const handleSystemStatus=data=>{setNotification({open:true,message:`System: ${data.message}`,severity:data.status==='offline'?'error':'info'});};// Setup WebSocket connections with error handling\ntry{// Main WebSocket for trades\nwebsocketService.on(WebSocketEventType.TRADE_UPDATE,handleTradeUpdate);websocketService.on(WebSocketEventType.SYSTEM_STATUS,handleSystemStatus);websocketService.connect();setWsStatus('connecting');}catch(error){console.error('Failed to setup main WebSocket:',error);setWsStatus('disconnected');}// Analytics WebSocket event handlers\nconst handleMarketState=data=>setMarketState(data);const handleStrategyWeights=data=>setStrategyWeights(data);const handlePerformanceMetrics=data=>setPerformanceMetrics(data);// Setup analytics WebSocket with error handling\ntry{analyticsWebsocketService.connect();setAnalyticsWsStatus('connecting');analyticsWebsocketService.addEventListener(AnalyticsEventType.MARKET_STATE,handleMarketState);analyticsWebsocketService.addEventListener(AnalyticsEventType.STRATEGY_WEIGHTS,handleStrategyWeights);analyticsWebsocketService.addEventListener(AnalyticsEventType.PERFORMANCE_METRICS,handlePerformanceMetrics);}catch(error){console.error('Failed to setup analytics WebSocket:',error);setAnalyticsWsStatus('disconnected');}return()=>{// Cleanup with error handling\ntry{// Trades WS cleanup\nwebsocketService.off(WebSocketEventType.TRADE_UPDATE,handleTradeUpdate);websocketService.off(WebSocketEventType.SYSTEM_STATUS,handleSystemStatus);websocketService.disconnect();}catch(error){console.error('Error cleaning up main WebSocket:',error);}try{// Analytics WS cleanup\nanalyticsWebsocketService.removeEventListener(AnalyticsEventType.MARKET_STATE,handleMarketState);analyticsWebsocketService.removeEventListener(AnalyticsEventType.STRATEGY_WEIGHTS,handleStrategyWeights);analyticsWebsocketService.removeEventListener(AnalyticsEventType.PERFORMANCE_METRICS,handlePerformanceMetrics);analyticsWebsocketService.disconnect();}catch(error){console.error('Error cleaning up analytics WebSocket:',error);}};},[fetchActiveTrades,fetchRecentTrades]);const handleCloseNotification=()=>{setNotification({...notification,open:false});};return/*#__PURE__*/_jsxs(Container,{maxWidth:\"xl\",sx:{mt:4},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h4\",gutterBottom:true,children:[\"Trading Dashboard\",/*#__PURE__*/_jsx(Badge,{badgeContent:wsStatus,color:wsStatus==='connected'?'success':wsStatus==='connecting'?'warning':'error',sx:{ml:2}}),/*#__PURE__*/_jsx(Badge,{badgeContent:`Analytics: ${analyticsWsStatus}`,color:analyticsWsStatus==='connected'?'success':'error',sx:{ml:2}})]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider'},children:/*#__PURE__*/_jsxs(Tabs,{value:currentTab,onChange:handleTabChange,\"aria-label\":\"dashboard tabs\",children:[/*#__PURE__*/_jsx(Tab,{label:\"Trades\",...a11yProps(0)}),/*#__PURE__*/_jsx(Tab,{label:\"Strategy Monitor\",...a11yProps(1)})]})}),/*#__PURE__*/_jsx(TabPanel,{value:currentTab,index:0,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:4,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(AccountStatistics,{})}),loading&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sx:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CircularProgress,{})}),!loading&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TradesTable,{trades:activeTrades,title:\"Active Trades\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TradesTable,{trades:recentTrades,title:\"Recent Trades\"})})]})]})}),/*#__PURE__*/_jsx(TabPanel,{value:currentTab,index:1,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:'100%'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Market Conditions\"}),marketState?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Typography,{children:[\"Regime: \",marketState.regime]}),/*#__PURE__*/_jsxs(Typography,{children:[\"Volatility: \",marketState.volatility.toFixed(4)]}),/*#__PURE__*/_jsxs(Typography,{children:[\"Trend Strength: \",marketState.trend_strength.toFixed(2)]})]}):/*#__PURE__*/_jsx(Typography,{children:\"Waiting for market data...\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:'100%'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Strategy Weights\"}),strategyWeights?/*#__PURE__*/_jsx(\"pre\",{children:JSON.stringify(strategyWeights.weights,null,2)}):/*#__PURE__*/_jsx(Typography,{children:\"Waiting for strategy data...\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:'100%'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Performance Metrics\"}),performanceMetrics?/*#__PURE__*/_jsx(\"pre\",{children:JSON.stringify(performanceMetrics.metrics,null,2)}):/*#__PURE__*/_jsx(Typography,{children:\"Waiting for performance data...\"})]})})]})}),/*#__PURE__*/_jsx(Snackbar,{open:notification.open,autoHideDuration:6000,onClose:handleCloseNotification,anchorOrigin:{vertical:'bottom',horizontal:'right'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseNotification,severity:notification.severity,sx:{width:'100%'},children:notification.message})})]});};export default TradeDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Typography", "Box", "Paper", "Grid", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "Badge", "Tabs", "Tab", "apiClient", "websocketService", "WebSocketEventType", "analyticsWebsocketService", "AnalyticsEventType", "AccountStatistics", "TradesTable", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "pt", "a11yProps", "TradeDashboard", "activeTrades", "setActiveTrades", "recentTrades", "setRecentTrades", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "wsStatus", "setWsStatus", "currentTab", "setCurrentTab", "analyticsWsStatus", "setAnalyticsWsStatus", "marketState", "setMarketState", "strategyWeights", "setStrategyWeights", "performanceMetrics", "setPerformanceMetrics", "handleTabChange", "event", "newValue", "generateMockActiveTrades", "trade_id", "symbol", "entry_fill_price", "entry_fill_qty", "entry_side", "sl_price", "tp_price", "status", "created_at", "Date", "now", "toISOString", "updated_at", "generateMockRecentTrades", "fetchActiveTrades", "response", "get", "data", "err", "console", "fetchRecentTrades", "fetchTrades", "Promise", "all", "handleTradeUpdate", "updatedTrade", "entry_price", "entry_qty", "exit_price", "undefined", "current_price", "timestamp", "prev", "map", "t", "handleSystemStatus", "on", "TRADE_UPDATE", "SYSTEM_STATUS", "connect", "handleMarketState", "handleStrategyWeights", "handlePerformanceMetrics", "addEventListener", "MARKET_STATE", "STRATEGY_WEIGHTS", "PERFORMANCE_METRICS", "off", "disconnect", "removeEventListener", "handleCloseNotification", "max<PERSON><PERSON><PERSON>", "mt", "variant", "gutterBottom", "badgeContent", "color", "ml", "mb", "borderBottom", "borderColor", "onChange", "label", "container", "spacing", "item", "xs", "display", "justifyContent", "trades", "title", "md", "p", "height", "regime", "volatility", "toFixed", "trend_strength", "JSON", "stringify", "weights", "metrics", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  Grid,\n  CircularProgress,\n  Alert,\n  Snackbar,\n  Badge,\n  Tabs,\n  Tab,\n} from '@mui/material';\nimport { apiClient } from '../services/api';\nimport websocketService, { WebSocketEventType, TradeUpdateEvent, SystemStatusEvent } from '../services/websocket';\nimport analyticsWebsocketService, { AnalyticsEventType, MarketStateEvent, StrategyWeightsEvent, PerformanceMetricsEvent } from '../services/analyticsWebsocket';\nimport AccountStatistics from './account/AccountStatistics';\nimport TradesTable from './common/TradesTable';\nimport { Trade } from '../types';\n\n// TabPanel helper component\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`simple-tabpanel-${index}`}\n      aria-labelledby={`simple-tab-${index}`}\n      {...other}\n    >\n      {value === index && (\n        <Box sx={{ pt: 3 }}>\n          {children}\n        </Box>\n      )}\n    </div>\n  );\n}\n\nfunction a11yProps(index: number) {\n  return {\n    id: `simple-tab-${index}`,\n    'aria-controls': `simple-tabpanel-${index}`,\n  };\n}\n\nconst TradeDashboard: React.FC = () => {\n  const [activeTrades, setActiveTrades] = useState<Trade[]>([]);\n  const [recentTrades, setRecentTrades] = useState<Trade[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [notification, setNotification] = useState<{\n    open: boolean;\n    message: string;\n    severity: 'success' | 'error' | 'info' | 'warning';\n  }>({\n    open: false,\n    message: '',\n    severity: 'info',\n  });\n  const [wsStatus, setWsStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');\n  const [currentTab, setCurrentTab] = useState(0);\n  const [analyticsWsStatus, setAnalyticsWsStatus] = useState<string>('disconnected');\n  const [marketState, setMarketState] = useState<MarketStateEvent | null>(null);\n  const [strategyWeights, setStrategyWeights] = useState<StrategyWeightsEvent | null>(null);\n  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetricsEvent | null>(null);\n\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setCurrentTab(newValue);\n  };\n\n  // Generate mock trades for demo purposes\n  const generateMockActiveTrades = (): Trade[] => {\n    return [\n      {\n        trade_id: 'trade_001',\n        symbol: 'BTCUSDT',\n        entry_fill_price: 42500.50,\n        entry_fill_qty: 0.025,\n        entry_side: 'BUY',\n        sl_price: 41000.00,\n        tp_price: 45000.00,\n        status: 'SLTP_PLACED',\n        created_at: new Date(Date.now() - 3600000).toISOString(),\n        updated_at: new Date(Date.now() - 1800000).toISOString(),\n      },\n      {\n        trade_id: 'trade_002',\n        symbol: 'ETHUSDT',\n        entry_fill_price: 2650.25,\n        entry_fill_qty: 0.5,\n        entry_side: 'SELL',\n        sl_price: 2750.00,\n        tp_price: 2550.00,\n        status: 'ENTRY_FILLED',\n        created_at: new Date(Date.now() - 7200000).toISOString(),\n        updated_at: new Date(Date.now() - 3600000).toISOString(),\n      },\n    ];\n  };\n\n  const generateMockRecentTrades = (): Trade[] => {\n    return [\n      {\n        trade_id: 'trade_003',\n        symbol: 'ADAUSDT',\n        entry_fill_price: 0.485,\n        entry_fill_qty: 2000,\n        entry_side: 'BUY',\n        sl_price: null,\n        tp_price: 0.52,\n        status: 'CLOSED_TP',\n        created_at: new Date(Date.now() - 86400000).toISOString(),\n        updated_at: new Date(Date.now() - 82800000).toISOString(),\n      },\n      {\n        trade_id: 'trade_004',\n        symbol: 'SOLUSDT',\n        entry_fill_price: 95.75,\n        entry_fill_qty: 10,\n        entry_side: 'BUY',\n        sl_price: 90.00,\n        tp_price: null,\n        status: 'CLOSED_SL',\n        created_at: new Date(Date.now() - 172800000).toISOString(),\n        updated_at: new Date(Date.now() - 169200000).toISOString(),\n      },\n    ];\n  };\n\n  // Fetch active trades\n  const fetchActiveTrades = useCallback(async () => {\n    try {\n      const response = await apiClient.get('/api/trading/active-trades');\n      setActiveTrades(response.data);\n    } catch (err: any) {\n      console.error('Error fetching active trades:', err);\n      // Use mock data instead of showing error\n      setActiveTrades(generateMockActiveTrades());\n    }\n  }, []);\n\n  // Fetch recent trades\n  const fetchRecentTrades = useCallback(async () => {\n    try {\n      const response = await apiClient.get('/api/trading/recent-trades');\n      setRecentTrades(response.data);\n    } catch (err: any) {\n      console.error('Error fetching recent trades:', err);\n      // Use mock data instead of showing error\n      setRecentTrades(generateMockRecentTrades());\n    }\n  }, []);\n\n  // Fetch all trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setLoading(true);\n        await Promise.all([\n          fetchActiveTrades(),\n          fetchRecentTrades(),\n        ]);\n      } catch (err: any) {\n        console.error('Error in fetchTrades:', err);\n        setError('Failed to fetch trades. Using demo data.');\n        // Fallback to mock data\n        setActiveTrades(generateMockActiveTrades());\n        setRecentTrades(generateMockRecentTrades());\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTrades();\n\n    // WebSocket event handlers\n    const handleTradeUpdate = (data: TradeUpdateEvent) => {\n      const updatedTrade: Trade = {\n        trade_id: data.trade_id,\n        symbol: data.symbol,\n        entry_side: data.entry_side,\n        entry_fill_price: data.entry_price || 0,\n        entry_fill_qty: data.entry_qty || 0,\n        sl_price: data.sl_price,\n        tp_price: data.tp_price,\n        exit_price: data.exit_price || undefined,\n        current_price: data.current_price,\n        status: data.status,\n        created_at: data.timestamp,\n        updated_at: data.timestamp,\n      };\n      setActiveTrades(prev =>\n        prev.map(t => (t.trade_id === updatedTrade.trade_id ? updatedTrade : t))\n      );\n      setNotification({\n        open: true,\n        message: `Trade ${updatedTrade.trade_id} updated: ${updatedTrade.status}`,\n        severity: 'info',\n      });\n    };\n\n    const handleSystemStatus = (data: SystemStatusEvent) => {\n      setNotification({\n        open: true,\n        message: `System: ${data.message}`,\n        severity: data.status === 'offline' ? 'error' : 'info',\n      });\n    };\n\n\n    // Setup WebSocket connections with error handling\n    try {\n      // Main WebSocket for trades\n      websocketService.on(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);\n      websocketService.on(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);\n      websocketService.connect();\n      setWsStatus('connecting');\n    } catch (error) {\n      console.error('Failed to setup main WebSocket:', error);\n      setWsStatus('disconnected');\n    }\n\n    // Analytics WebSocket event handlers\n    const handleMarketState = (data: MarketStateEvent) => setMarketState(data);\n    const handleStrategyWeights = (data: StrategyWeightsEvent) => setStrategyWeights(data);\n    const handlePerformanceMetrics = (data: PerformanceMetricsEvent) => setPerformanceMetrics(data);\n\n    // Setup analytics WebSocket with error handling\n    try {\n      analyticsWebsocketService.connect();\n      setAnalyticsWsStatus('connecting');\n      \n      analyticsWebsocketService.addEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);\n      analyticsWebsocketService.addEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);\n    } catch (error) {\n      console.error('Failed to setup analytics WebSocket:', error);\n      setAnalyticsWsStatus('disconnected');\n    }\n\n    return () => {\n      // Cleanup with error handling\n      try {\n        // Trades WS cleanup\n        websocketService.off(WebSocketEventType.TRADE_UPDATE, handleTradeUpdate);\n        websocketService.off(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);\n        websocketService.disconnect();\n      } catch (error) {\n        console.error('Error cleaning up main WebSocket:', error);\n      }\n\n      try {\n        // Analytics WS cleanup\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.MARKET_STATE, handleMarketState);\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.STRATEGY_WEIGHTS, handleStrategyWeights);\n        analyticsWebsocketService.removeEventListener(AnalyticsEventType.PERFORMANCE_METRICS, handlePerformanceMetrics);\n        analyticsWebsocketService.disconnect();\n      } catch (error) {\n        console.error('Error cleaning up analytics WebSocket:', error);\n      }\n    };\n  }, [fetchActiveTrades, fetchRecentTrades]);\n\n  const handleCloseNotification = () => {\n    setNotification({ ...notification, open: false });\n  };\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ mt: 4 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Trading Dashboard\n        <Badge\n          badgeContent={wsStatus}\n          color={wsStatus === 'connected' ? 'success' : wsStatus === 'connecting' ? 'warning' : 'error'}\n          sx={{ ml: 2 }}\n        />\n         <Badge\n          badgeContent={`Analytics: ${analyticsWsStatus}`}\n          color={analyticsWsStatus === 'connected' ? 'success' : 'error'}\n          sx={{ ml: 2 }}\n        />\n      </Typography>\n\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n\n      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n        <Tabs value={currentTab} onChange={handleTabChange} aria-label=\"dashboard tabs\">\n          <Tab label=\"Trades\" {...a11yProps(0)} />\n          <Tab label=\"Strategy Monitor\" {...a11yProps(1)} />\n        </Tabs>\n      </Box>\n\n      <TabPanel value={currentTab} index={0}>\n        <Grid container spacing={4}>\n          <Grid item xs={12}>\n            <AccountStatistics />\n          </Grid>\n          {loading && (\n            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center' }}>\n              <CircularProgress />\n            </Grid>\n          )}\n          {!loading && (\n            <>\n              <Grid item xs={12}>\n                <TradesTable trades={activeTrades} title=\"Active Trades\" />\n              </Grid>\n              <Grid item xs={12}>\n                <TradesTable trades={recentTrades} title=\"Recent Trades\" />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </TabPanel>\n\n      <TabPanel value={currentTab} index={1}>\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"h6\">Market Conditions</Typography>\n              {marketState ? (\n                <>\n                  <Typography>Regime: {marketState.regime}</Typography>\n                  <Typography>Volatility: {marketState.volatility.toFixed(4)}</Typography>\n                  <Typography>Trend Strength: {marketState.trend_strength.toFixed(2)}</Typography>\n                </>\n              ) : (\n                <Typography>Waiting for market data...</Typography>\n              )}\n            </Paper>\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"h6\">Strategy Weights</Typography>\n              {strategyWeights ? (\n                <pre>{JSON.stringify(strategyWeights.weights, null, 2)}</pre>\n              ) : (\n                <Typography>Waiting for strategy data...</Typography>\n              )}\n            </Paper>\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"h6\">Performance Metrics</Typography>\n              {performanceMetrics ? (\n                <pre>{JSON.stringify(performanceMetrics.metrics, null, 2)}</pre>\n              ) : (\n                <Typography>Waiting for performance data...</Typography>\n              )}\n            </Paper>\n          </Grid>\n        </Grid>\n      </TabPanel>\n\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Container>\n  );\n};\n\nexport default TradeDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,CACRC,KAAK,CACLC,IAAI,CACJC,GAAG,KACE,eAAe,CACtB,OAASC,SAAS,KAAQ,iBAAiB,CAC3C,MAAO,CAAAC,gBAAgB,EAAIC,kBAAkB,KAA6C,uBAAuB,CACjH,MAAO,CAAAC,yBAAyB,EAAIC,kBAAkB,KAAyE,gCAAgC,CAC/J,MAAO,CAAAC,iBAAiB,KAAM,6BAA6B,CAC3D,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAG9C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAOA,QAAS,CAAAC,QAAQA,CAACC,KAAoB,CAAE,CACtC,KAAM,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,GAAGC,KAAM,CAAC,CAAGJ,KAAK,CAElD,mBACEN,IAAA,QACEW,IAAI,CAAC,UAAU,CACfC,MAAM,CAAEJ,KAAK,GAAKC,KAAM,CACxBI,EAAE,CAAE,mBAAmBJ,KAAK,EAAG,CAC/B,kBAAiB,cAAcA,KAAK,EAAG,IACnCC,KAAK,CAAAH,QAAA,CAERC,KAAK,GAAKC,KAAK,eACdT,IAAA,CAACjB,GAAG,EAAC+B,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAChBA,QAAQ,CACN,CACN,CACE,CAAC,CAEV,CAEA,QAAS,CAAAS,SAASA,CAACP,KAAa,CAAE,CAChC,MAAO,CACLI,EAAE,CAAE,cAAcJ,KAAK,EAAE,CACzB,eAAe,CAAE,mBAAmBA,KAAK,EAC3C,CAAC,CACH,CAEA,KAAM,CAAAQ,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGzC,QAAQ,CAAU,EAAE,CAAC,CAC7D,KAAM,CAAC0C,YAAY,CAAEC,eAAe,CAAC,CAAG3C,QAAQ,CAAU,EAAE,CAAC,CAC7D,KAAM,CAAC4C,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAU,IAAI,CAAC,CACrD,KAAM,CAAC8C,KAAK,CAAEC,QAAQ,CAAC,CAAG/C,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACgD,YAAY,CAAEC,eAAe,CAAC,CAAGjD,QAAQ,CAI7C,CACDkD,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,MACZ,CAAC,CAAC,CACF,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAA8C,cAAc,CAAC,CACrG,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACyD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1D,QAAQ,CAAS,cAAc,CAAC,CAClF,KAAM,CAAC2D,WAAW,CAAEC,cAAc,CAAC,CAAG5D,QAAQ,CAA0B,IAAI,CAAC,CAC7E,KAAM,CAAC6D,eAAe,CAAEC,kBAAkB,CAAC,CAAG9D,QAAQ,CAA8B,IAAI,CAAC,CACzF,KAAM,CAAC+D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGhE,QAAQ,CAAiC,IAAI,CAAC,CAElG,KAAM,CAAAiE,eAAe,CAAGA,CAACC,KAA2B,CAAEC,QAAgB,GAAK,CACzEX,aAAa,CAACW,QAAQ,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAGA,CAAA,GAAe,CAC9C,MAAO,CACL,CACEC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,gBAAgB,CAAE,QAAQ,CAC1BC,cAAc,CAAE,KAAK,CACrBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,QAAQ,CAClBC,MAAM,CAAE,aAAa,CACrBC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC,CACxDC,UAAU,CAAE,GAAI,CAAAH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CACzD,CAAC,CACD,CACEX,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,gBAAgB,CAAE,OAAO,CACzBC,cAAc,CAAE,GAAG,CACnBC,UAAU,CAAE,MAAM,CAClBC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,cAAc,CACtBC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC,CACxDC,UAAU,CAAE,GAAI,CAAAH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CACzD,CAAC,CACF,CACH,CAAC,CAED,KAAM,CAAAE,wBAAwB,CAAGA,CAAA,GAAe,CAC9C,MAAO,CACL,CACEb,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,gBAAgB,CAAE,KAAK,CACvBC,cAAc,CAAE,IAAI,CACpBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,WAAW,CACnBC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,QAAQ,CAAC,CAACC,WAAW,CAAC,CAAC,CACzDC,UAAU,CAAE,GAAI,CAAAH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,QAAQ,CAAC,CAACC,WAAW,CAAC,CAC1D,CAAC,CACD,CACEX,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,gBAAgB,CAAE,KAAK,CACvBC,cAAc,CAAE,EAAE,CAClBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,WAAW,CACnBC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC,CAC1DC,UAAU,CAAE,GAAI,CAAAH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,SAAS,CAAC,CAACC,WAAW,CAAC,CAC3D,CAAC,CACF,CACH,CAAC,CAED;AACA,KAAM,CAAAG,iBAAiB,CAAGjF,WAAW,CAAC,SAAY,CAChD,GAAI,CACF,KAAM,CAAAkF,QAAQ,CAAG,KAAM,CAAAtE,SAAS,CAACuE,GAAG,CAAC,4BAA4B,CAAC,CAClE5C,eAAe,CAAC2C,QAAQ,CAACE,IAAI,CAAC,CAChC,CAAE,MAAOC,GAAQ,CAAE,CACjBC,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,CAAEyC,GAAG,CAAC,CACnD;AACA9C,eAAe,CAAC2B,wBAAwB,CAAC,CAAC,CAAC,CAC7C,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqB,iBAAiB,CAAGvF,WAAW,CAAC,SAAY,CAChD,GAAI,CACF,KAAM,CAAAkF,QAAQ,CAAG,KAAM,CAAAtE,SAAS,CAACuE,GAAG,CAAC,4BAA4B,CAAC,CAClE1C,eAAe,CAACyC,QAAQ,CAACE,IAAI,CAAC,CAChC,CAAE,MAAOC,GAAQ,CAAE,CACjBC,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,CAAEyC,GAAG,CAAC,CACnD;AACA5C,eAAe,CAACuC,wBAAwB,CAAC,CAAC,CAAC,CAC7C,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACAjF,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyF,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF7C,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAA8C,OAAO,CAACC,GAAG,CAAC,CAChBT,iBAAiB,CAAC,CAAC,CACnBM,iBAAiB,CAAC,CAAC,CACpB,CAAC,CACJ,CAAE,MAAOF,GAAQ,CAAE,CACjBC,OAAO,CAAC1C,KAAK,CAAC,uBAAuB,CAAEyC,GAAG,CAAC,CAC3CxC,QAAQ,CAAC,0CAA0C,CAAC,CACpD;AACAN,eAAe,CAAC2B,wBAAwB,CAAC,CAAC,CAAC,CAC3CzB,eAAe,CAACuC,wBAAwB,CAAC,CAAC,CAAC,CAC7C,CAAC,OAAS,CACRrC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED6C,WAAW,CAAC,CAAC,CAEb;AACA,KAAM,CAAAG,iBAAiB,CAAIP,IAAsB,EAAK,CACpD,KAAM,CAAAQ,YAAmB,CAAG,CAC1BzB,QAAQ,CAAEiB,IAAI,CAACjB,QAAQ,CACvBC,MAAM,CAAEgB,IAAI,CAAChB,MAAM,CACnBG,UAAU,CAAEa,IAAI,CAACb,UAAU,CAC3BF,gBAAgB,CAAEe,IAAI,CAACS,WAAW,EAAI,CAAC,CACvCvB,cAAc,CAAEc,IAAI,CAACU,SAAS,EAAI,CAAC,CACnCtB,QAAQ,CAAEY,IAAI,CAACZ,QAAQ,CACvBC,QAAQ,CAAEW,IAAI,CAACX,QAAQ,CACvBsB,UAAU,CAAEX,IAAI,CAACW,UAAU,EAAIC,SAAS,CACxCC,aAAa,CAAEb,IAAI,CAACa,aAAa,CACjCvB,MAAM,CAAEU,IAAI,CAACV,MAAM,CACnBC,UAAU,CAAES,IAAI,CAACc,SAAS,CAC1BnB,UAAU,CAAEK,IAAI,CAACc,SACnB,CAAC,CACD3D,eAAe,CAAC4D,IAAI,EAClBA,IAAI,CAACC,GAAG,CAACC,CAAC,EAAKA,CAAC,CAAClC,QAAQ,GAAKyB,YAAY,CAACzB,QAAQ,CAAGyB,YAAY,CAAGS,CAAE,CACzE,CAAC,CACDtD,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,SAAS2C,YAAY,CAACzB,QAAQ,aAAayB,YAAY,CAAClB,MAAM,EAAE,CACzExB,QAAQ,CAAE,MACZ,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAoD,kBAAkB,CAAIlB,IAAuB,EAAK,CACtDrC,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,WAAWmC,IAAI,CAACnC,OAAO,EAAE,CAClCC,QAAQ,CAAEkC,IAAI,CAACV,MAAM,GAAK,SAAS,CAAG,OAAO,CAAG,MAClD,CAAC,CAAC,CACJ,CAAC,CAGD;AACA,GAAI,CACF;AACA7D,gBAAgB,CAAC0F,EAAE,CAACzF,kBAAkB,CAAC0F,YAAY,CAAEb,iBAAiB,CAAC,CACvE9E,gBAAgB,CAAC0F,EAAE,CAACzF,kBAAkB,CAAC2F,aAAa,CAAEH,kBAAkB,CAAC,CACzEzF,gBAAgB,CAAC6F,OAAO,CAAC,CAAC,CAC1BtD,WAAW,CAAC,YAAY,CAAC,CAC3B,CAAE,MAAOR,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDQ,WAAW,CAAC,cAAc,CAAC,CAC7B,CAEA;AACA,KAAM,CAAAuD,iBAAiB,CAAIvB,IAAsB,EAAK1B,cAAc,CAAC0B,IAAI,CAAC,CAC1E,KAAM,CAAAwB,qBAAqB,CAAIxB,IAA0B,EAAKxB,kBAAkB,CAACwB,IAAI,CAAC,CACtF,KAAM,CAAAyB,wBAAwB,CAAIzB,IAA6B,EAAKtB,qBAAqB,CAACsB,IAAI,CAAC,CAE/F;AACA,GAAI,CACFrE,yBAAyB,CAAC2F,OAAO,CAAC,CAAC,CACnClD,oBAAoB,CAAC,YAAY,CAAC,CAElCzC,yBAAyB,CAAC+F,gBAAgB,CAAC9F,kBAAkB,CAAC+F,YAAY,CAAEJ,iBAAiB,CAAC,CAC9F5F,yBAAyB,CAAC+F,gBAAgB,CAAC9F,kBAAkB,CAACgG,gBAAgB,CAAEJ,qBAAqB,CAAC,CACtG7F,yBAAyB,CAAC+F,gBAAgB,CAAC9F,kBAAkB,CAACiG,mBAAmB,CAAEJ,wBAAwB,CAAC,CAC9G,CAAE,MAAOjE,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5DY,oBAAoB,CAAC,cAAc,CAAC,CACtC,CAEA,MAAO,IAAM,CACX;AACA,GAAI,CACF;AACA3C,gBAAgB,CAACqG,GAAG,CAACpG,kBAAkB,CAAC0F,YAAY,CAAEb,iBAAiB,CAAC,CACxE9E,gBAAgB,CAACqG,GAAG,CAACpG,kBAAkB,CAAC2F,aAAa,CAAEH,kBAAkB,CAAC,CAC1EzF,gBAAgB,CAACsG,UAAU,CAAC,CAAC,CAC/B,CAAE,MAAOvE,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CAC3D,CAEA,GAAI,CACF;AACA7B,yBAAyB,CAACqG,mBAAmB,CAACpG,kBAAkB,CAAC+F,YAAY,CAAEJ,iBAAiB,CAAC,CACjG5F,yBAAyB,CAACqG,mBAAmB,CAACpG,kBAAkB,CAACgG,gBAAgB,CAAEJ,qBAAqB,CAAC,CACzG7F,yBAAyB,CAACqG,mBAAmB,CAACpG,kBAAkB,CAACiG,mBAAmB,CAAEJ,wBAAwB,CAAC,CAC/G9F,yBAAyB,CAACoG,UAAU,CAAC,CAAC,CACxC,CAAE,MAAOvE,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,wCAAwC,CAAEA,KAAK,CAAC,CAChE,CACF,CAAC,CACH,CAAC,CAAE,CAACqC,iBAAiB,CAAEM,iBAAiB,CAAC,CAAC,CAE1C,KAAM,CAAA8B,uBAAuB,CAAGA,CAAA,GAAM,CACpCtE,eAAe,CAAC,CAAE,GAAGD,YAAY,CAAEE,IAAI,CAAE,KAAM,CAAC,CAAC,CACnD,CAAC,CAED,mBACE1B,KAAA,CAACrB,SAAS,EAACqH,QAAQ,CAAC,IAAI,CAACpF,EAAE,CAAE,CAAEqF,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,eACrCL,KAAA,CAACpB,UAAU,EAACsH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAA9F,QAAA,EAAC,mBAEpC,cAAAP,IAAA,CAACX,KAAK,EACJiH,YAAY,CAAEvE,QAAS,CACvBwE,KAAK,CAAExE,QAAQ,GAAK,WAAW,CAAG,SAAS,CAAGA,QAAQ,GAAK,YAAY,CAAG,SAAS,CAAG,OAAQ,CAC9FjB,EAAE,CAAE,CAAE0F,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACDxG,IAAA,CAACX,KAAK,EACLiH,YAAY,CAAE,cAAcnE,iBAAiB,EAAG,CAChDoE,KAAK,CAAEpE,iBAAiB,GAAK,WAAW,CAAG,SAAS,CAAG,OAAQ,CAC/DrB,EAAE,CAAE,CAAE0F,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACQ,CAAC,CAEZhF,KAAK,eAAIxB,IAAA,CAACb,KAAK,EAAC2C,QAAQ,CAAC,OAAO,CAAChB,EAAE,CAAE,CAAE2F,EAAE,CAAE,CAAE,CAAE,CAAAlG,QAAA,CAAEiB,KAAK,CAAQ,CAAC,cAEhExB,IAAA,CAACjB,GAAG,EAAC+B,EAAE,CAAE,CAAE4F,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAApG,QAAA,cACnDL,KAAA,CAACZ,IAAI,EAACkB,KAAK,CAAEyB,UAAW,CAAC2E,QAAQ,CAAEjE,eAAgB,CAAC,aAAW,gBAAgB,CAAApC,QAAA,eAC7EP,IAAA,CAACT,GAAG,EAACsH,KAAK,CAAC,QAAQ,IAAK7F,SAAS,CAAC,CAAC,CAAC,CAAG,CAAC,cACxChB,IAAA,CAACT,GAAG,EAACsH,KAAK,CAAC,kBAAkB,IAAK7F,SAAS,CAAC,CAAC,CAAC,CAAG,CAAC,EAC9C,CAAC,CACJ,CAAC,cAENhB,IAAA,CAACK,QAAQ,EAACG,KAAK,CAAEyB,UAAW,CAACxB,KAAK,CAAE,CAAE,CAAAF,QAAA,cACpCL,KAAA,CAACjB,IAAI,EAAC6H,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxG,QAAA,eACzBP,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA1G,QAAA,cAChBP,IAAA,CAACH,iBAAiB,GAAE,CAAC,CACjB,CAAC,CACNyB,OAAO,eACNtB,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAACnG,EAAE,CAAE,CAAEoG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAA5G,QAAA,cACnEP,IAAA,CAACd,gBAAgB,GAAE,CAAC,CAChB,CACP,CACA,CAACoC,OAAO,eACPpB,KAAA,CAAAE,SAAA,EAAAG,QAAA,eACEP,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA1G,QAAA,cAChBP,IAAA,CAACF,WAAW,EAACsH,MAAM,CAAElG,YAAa,CAACmG,KAAK,CAAC,eAAe,CAAE,CAAC,CACvD,CAAC,cACPrH,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA1G,QAAA,cAChBP,IAAA,CAACF,WAAW,EAACsH,MAAM,CAAEhG,YAAa,CAACiG,KAAK,CAAC,eAAe,CAAE,CAAC,CACvD,CAAC,EACP,CACH,EACG,CAAC,CACC,CAAC,cAEXrH,IAAA,CAACK,QAAQ,EAACG,KAAK,CAAEyB,UAAW,CAACxB,KAAK,CAAE,CAAE,CAAAF,QAAA,cACpCL,KAAA,CAACjB,IAAI,EAAC6H,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxG,QAAA,eACzBP,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAACK,EAAE,CAAE,CAAE,CAAA/G,QAAA,cACvBL,KAAA,CAAClB,KAAK,EAAC8B,EAAE,CAAE,CAAEyG,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAAjH,QAAA,eAClCP,IAAA,CAAClB,UAAU,EAACsH,OAAO,CAAC,IAAI,CAAA7F,QAAA,CAAC,mBAAiB,CAAY,CAAC,CACtD8B,WAAW,cACVnC,KAAA,CAAAE,SAAA,EAAAG,QAAA,eACEL,KAAA,CAACpB,UAAU,EAAAyB,QAAA,EAAC,UAAQ,CAAC8B,WAAW,CAACoF,MAAM,EAAa,CAAC,cACrDvH,KAAA,CAACpB,UAAU,EAAAyB,QAAA,EAAC,cAAY,CAAC8B,WAAW,CAACqF,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,EAAa,CAAC,cACxEzH,KAAA,CAACpB,UAAU,EAAAyB,QAAA,EAAC,kBAAgB,CAAC8B,WAAW,CAACuF,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,EAAa,CAAC,EAChF,CAAC,cAEH3H,IAAA,CAAClB,UAAU,EAAAyB,QAAA,CAAC,4BAA0B,CAAY,CACnD,EACI,CAAC,CACJ,CAAC,cACPP,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAACK,EAAE,CAAE,CAAE,CAAA/G,QAAA,cACvBL,KAAA,CAAClB,KAAK,EAAC8B,EAAE,CAAE,CAAEyG,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAAjH,QAAA,eAClCP,IAAA,CAAClB,UAAU,EAACsH,OAAO,CAAC,IAAI,CAAA7F,QAAA,CAAC,kBAAgB,CAAY,CAAC,CACrDgC,eAAe,cACdvC,IAAA,QAAAO,QAAA,CAAMsH,IAAI,CAACC,SAAS,CAACvF,eAAe,CAACwF,OAAO,CAAE,IAAI,CAAE,CAAC,CAAC,CAAM,CAAC,cAE7D/H,IAAA,CAAClB,UAAU,EAAAyB,QAAA,CAAC,8BAA4B,CAAY,CACrD,EACI,CAAC,CACJ,CAAC,cACPP,IAAA,CAACf,IAAI,EAAC+H,IAAI,MAACC,EAAE,CAAE,EAAG,CAACK,EAAE,CAAE,CAAE,CAAA/G,QAAA,cACvBL,KAAA,CAAClB,KAAK,EAAC8B,EAAE,CAAE,CAAEyG,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAAjH,QAAA,eAClCP,IAAA,CAAClB,UAAU,EAACsH,OAAO,CAAC,IAAI,CAAA7F,QAAA,CAAC,qBAAmB,CAAY,CAAC,CACxDkC,kBAAkB,cACjBzC,IAAA,QAAAO,QAAA,CAAMsH,IAAI,CAACC,SAAS,CAACrF,kBAAkB,CAACuF,OAAO,CAAE,IAAI,CAAE,CAAC,CAAC,CAAM,CAAC,cAEhEhI,IAAA,CAAClB,UAAU,EAAAyB,QAAA,CAAC,iCAA+B,CAAY,CACxD,EACI,CAAC,CACJ,CAAC,EACH,CAAC,CACC,CAAC,cAEXP,IAAA,CAACZ,QAAQ,EACPwC,IAAI,CAAEF,YAAY,CAACE,IAAK,CACxBqG,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEjC,uBAAwB,CACjCkC,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAA9H,QAAA,cAE1DP,IAAA,CAACb,KAAK,EAAC+I,OAAO,CAAEjC,uBAAwB,CAACnE,QAAQ,CAAEJ,YAAY,CAACI,QAAS,CAAChB,EAAE,CAAE,CAAEwH,KAAK,CAAE,MAAO,CAAE,CAAA/H,QAAA,CAC7FmB,YAAY,CAACG,OAAO,CAChB,CAAC,CACA,CAAC,EACF,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}