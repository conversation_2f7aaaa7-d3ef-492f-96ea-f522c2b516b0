"""Trade management functionality for the execution service."""
import logging
import uuid
from typing import TYPE_CHECKING, Dict, Optional, List, Any
from decimal import Decimal
from datetime import datetime

from app.services.execution.trade_state import ManagedTrade, TradeStatus
from app.services.execution.models import Order, OrderSide, OrderType, OrderStatus
from app.services.execution.order_management import OrderManager

logger = logging.getLogger(__name__)

class TradeManager:
    """Manages trades, including entry, SL/TP, and state tracking."""

    def __init__(self, order_manager: OrderManager, db_session_factory: Any):
        """Initialize the trade manager.

        Args:
            order_manager: The order manager to use for order operations.
            db_session_factory: A callable that returns a new SQLAlchemy AsyncSession.
        """
        self.order_manager = order_manager
        self.db_session_factory = db_session_factory
        self.active_trades: Dict[str, ManagedTrade] = {}

    async def place_entry_order_with_sl_tp(self, symbol: str, side: str, quantity: float,
                                         entry_price: Optional[float] = None,
                                         stop_loss_price: Optional[float] = None,
                                         take_profit_price: Optional[float] = None,
                                         client_order_id: Optional[str] = None) -> Optional[ManagedTrade]:
        """Place an entry order with optional stop loss and take profit orders.

        Args:
            symbol: The trading symbol.
            side: BUY or SELL.
            quantity: The order quantity.
            entry_price: The entry price for limit orders, None for market orders.
            stop_loss_price: The stop loss price.
            take_profit_price: The take profit price.
            client_order_id: Optional client order ID.

        Returns:
            The created ManagedTrade, or None if the order failed.
        """
        logger.info(f"Placing entry order with SL/TP: {symbol} {side} {quantity} @ {entry_price or 'MARKET'}, SL={stop_loss_price}, TP={take_profit_price}")

        # Create a new ManagedTrade
        trade = ManagedTrade(
            trade_id=uuid.uuid4(),
            symbol=symbol,
            entry_side=side,
            status=TradeStatus.PENDING_ENTRY
        )

        try:
            # Place the entry order
            if entry_price:
                entry_order = await self.order_manager.place_limit_order(
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    price=entry_price,
                    client_order_id=client_order_id
                )
            else:
                entry_order = await self.order_manager.place_market_order(
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    client_order_id=client_order_id
                )

            if not entry_order or not entry_order.exchange_order_id:
                logger.error("Failed to place entry order.")
                return None

            # Update the trade with entry order details
            trade.entry_order_id = entry_order.exchange_order_id

            # Check if entry order has the necessary attributes
            if not hasattr(entry_order, 'status') or not hasattr(entry_order, 'filled_quantity'):
                logger.error(f"Entry order doesn't have required attributes: {entry_order}")
                return None

            # Save the trade to the database
            if hasattr(self, 'trade_repository') and self.trade_repository is not None:
                repo = self.trade_repository
            else:
                async with self.db_session_factory() as session:
                    from app.repositories.trade_repository import TradeRepository
                    repo = TradeRepository(session)
            await repo.create_trade(trade)

            # Add to active trades cache
            self.active_trades[str(trade.trade_id)] = trade

            # If entry order is filled, place SL/TP orders
            if (entry_order.status == OrderStatus.FILLED or (hasattr(entry_order, 'filled_quantity') and entry_order.filled_quantity > 0)) and stop_loss_price and take_profit_price:
                trade.entry_fill_price = Decimal(str(entry_order.average_price))
                trade.entry_fill_qty = Decimal(str(entry_order.filled_quantity))
                trade.status = TradeStatus.ENTRY_FILLED

                # Place SL/TP orders
                sl_tp_success = await self._place_sl_tp_orders(trade, stop_loss_price, take_profit_price)
                if not sl_tp_success:
                    logger.error(f"Failed to place SL/TP orders for trade {trade.trade_id}. Manual intervention may be required.")
                    # Note: Decide on rollback logic here. For now, we proceed.
            
            return trade

        except Exception as e:
            logger.error(f"Error placing entry order with SL/TP: {e}", exc_info=True)
            return None

    async def _place_sl_tp_orders(self, trade: ManagedTrade,
                                stop_loss_price: float,
                                take_profit_price: float) -> bool:
        """Place stop loss and take profit orders for a trade.

        Args:
            trade: The trade to place SL/TP orders for.
            stop_loss_price: The stop loss price.
            take_profit_price: The take profit price.

        Returns:
            True if both orders were placed successfully, False otherwise.
        """
        if not trade.entry_fill_qty or not trade.entry_fill_price:
            logger.error(f"Cannot place SL/TP orders for trade {trade.trade_id}: Missing entry fill details.")
            return False

        # Determine the side for SL/TP orders (opposite of entry)
        sl_tp_side = "SELL" if trade.entry_side == "BUY" else "BUY"
        quantity = float(trade.entry_fill_qty)

        try:
            # Place stop loss order
            sl_order = await self.order_manager.place_stop_market_order(
                symbol=trade.symbol,
                side=sl_tp_side,
                quantity=quantity,
                stop_price=stop_loss_price
            )

            # Check if SL order was placed successfully
            if not sl_order:
                logger.error(f"Failed to place SL order for trade {trade.trade_id}.")
                return False

            # Place take profit order
            tp_order = await self.order_manager.place_take_profit_market_order(
                symbol=trade.symbol,
                side=sl_tp_side,
                quantity=quantity,
                stop_price=take_profit_price
            )

            # Check if TP order was placed successfully
            if not tp_order:
                logger.error(f"Failed to place TP order for trade {trade.trade_id}.")
                return False

            # Update the trade with SL/TP order details
            trade.sl_order_id = sl_order.exchange_order_id
            trade.tp_order_id = tp_order.exchange_order_id
            trade.sl_price = Decimal(str(stop_loss_price))
            trade.tp_price = Decimal(str(take_profit_price))
            trade.status = TradeStatus.SLTP_PLACED

            # Update the trade in the database
            if hasattr(self, 'trade_repository') and self.trade_repository is not None:
                repo = self.trade_repository
            else:
                async with self.db_session_factory() as session:
                    from app.repositories.trade_repository import TradeRepository
                    repo = TradeRepository(session)
            await repo.update_trade(trade)

            # Update the active trades cache
            self.active_trades[str(trade.trade_id)] = trade

            logger.info(f"Successfully placed SL/TP orders for trade {trade.trade_id}: SL={sl_order.exchange_order_id}, TP={tp_order.exchange_order_id}")
            return True

        except Exception as e:
            logger.error(f"Error placing SL/TP orders for trade {trade.trade_id}: {e}", exc_info=True)
            return False

    def _calculate_dynamic_risk_params(self, entry_price: float, position_type: str, market_conditions: Dict[str, Any]) -> Dict[str, float]:
        """Calculate dynamic risk parameters based on market conditions.

        Args:
            entry_price: The entry price.
            position_type: 'buy' (long) or 'sell' (short).
            market_conditions: Dictionary containing market conditions.

        Returns:
            Dictionary containing stop loss and take profit levels.
        """
        # Extract market conditions
        volatility = market_conditions.get("volatility", 0.01)  # Default to 1%
        atr = market_conditions.get("atr", entry_price * 0.01)  # Default to 1% of price
        adx = market_conditions.get("adx", 20.0)  # Default to moderate trend
        support = market_conditions.get("support", 0.0)
        resistance = market_conditions.get("resistance", 0.0)
        trend_direction = market_conditions.get("trend_direction", 0.0)
        all_support_levels = market_conditions.get("all_support_levels", [])
        all_resistance_levels = market_conditions.get("all_resistance_levels", [])

        # Base ATR multiplier
        base_atr_multiplier = 1.5

        # Adjust ATR multiplier based on market conditions
        adjusted_atr_multiplier = base_atr_multiplier
        if adx > 30:  # Strong trend - wider stops
            adjusted_atr_multiplier *= 1.2
            logger.info(f"Strong trend (ADX={adx:.1f}): Increasing ATR multiplier to {adjusted_atr_multiplier:.2f}")
        elif adx < 15:  # Very weak trend - tighter stops
            adjusted_atr_multiplier *= 0.8
            logger.info(f"Weak trend (ADX={adx:.1f}): Decreasing ATR multiplier to {adjusted_atr_multiplier:.2f}")

        if volatility > 0.02:  # High volatility - wider stops
            adjusted_atr_multiplier *= 1.1
            logger.info(f"High volatility ({volatility*100:.1f}%): Increasing ATR multiplier to {adjusted_atr_multiplier:.2f}")

        # Determine stop loss based on ATR
        if position_type.lower() == "buy":
            stop_loss = entry_price - (atr * adjusted_atr_multiplier)
            # Use support level if it's closer to the price and provides better protection
            if support > stop_loss and support < entry_price:
                stop_loss = support * 0.995  # Place slightly below support
                logger.info(f"Adjusting SL based on support level to: {stop_loss}")

        else:  # Sell position
            stop_loss = entry_price + (atr * adjusted_atr_multiplier)
            # Use resistance level if it's closer and provides better protection
            if resistance < stop_loss and resistance > entry_price:
                stop_loss = resistance * 1.005  # Place slightly above resistance
                logger.info(f"Adjusting SL based on resistance level to: {stop_loss}")

        # Calculate take profit based on risk-reward ratio
        risk_reward_ratio = self._calculate_dynamic_rr(market_conditions)
        risk_per_share = abs(entry_price - stop_loss)

        if position_type.lower() == "buy":
            take_profit = entry_price + (risk_per_share * risk_reward_ratio)
            logger.info(f"Buy position: TP calculated as {entry_price} + ({risk_per_share} * {risk_reward_ratio}) = {take_profit}")
        else:  # Sell position
            take_profit = entry_price - (risk_per_share * risk_reward_ratio)
            logger.info(f"Sell position: TP calculated as {entry_price} - ({risk_per_share} * {risk_reward_ratio}) = {take_profit}")

        # Ensure take profit is not between entry and stop loss
        if position_type.lower() == "buy" and take_profit < entry_price:
            take_profit = entry_price + risk_per_share  # Fallback to 1:1 RR
            logger.warning(f"TP was below entry, adjusted to {take_profit}")
        elif position_type.lower() == "sell" and take_profit > entry_price:
            take_profit = entry_price - risk_per_share  # Fallback to 1:1 RR
            logger.warning(f"TP was above entry, adjusted to {take_profit}")

        return {
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "atr_multiplier_used": adjusted_atr_multiplier
        }

    def _calculate_dynamic_rr(self, market_conditions: Dict[str, Any], strategy_win_rate: float = 0.5) -> float:
        """Calculate a dynamic risk-reward ratio based on market conditions and strategy performance.

        Args:
            market_conditions: Dictionary containing market analysis results.
            strategy_win_rate: Historical win rate for the strategy (0.0-1.0).

        Returns:
            Dynamic risk-reward ratio.
        """
        # Base risk-reward ratio - adjusted based on win rate
        # For a 50% win rate, we need at least 1:1 RR to break even
        # For lower win rates, we need higher RR
        if strategy_win_rate > 0.6:  # High win rate strategy
            base_rr = 1.0
            logger.info(f"High win rate strategy ({strategy_win_rate:.2f}): Base RR set to 1.0")
        elif strategy_win_rate > 0.5:  # Above average win rate
            base_rr = 1.2
            logger.info(f"Above average win rate strategy ({strategy_win_rate:.2f}): Base RR set to 1.2")
        elif strategy_win_rate > 0.4:  # Below average win rate
            base_rr = 1.5
            logger.info(f"Below average win rate strategy ({strategy_win_rate:.2f}): Base RR set to 1.5")
        else:  # Low win rate strategy
            base_rr = 2.0
            logger.info(f"Low win rate strategy ({strategy_win_rate:.2f}): Base RR set to 2.0")

        # Adjust based on ADX (trend strength)
        adx = market_conditions.get("adx", 25)
        if adx < 15:  # Very ranging market - lower RR
            base_rr *= 0.9
            logger.info(f"Very ranging market (ADX={adx:.1f}): Decreasing RR by 10%")
        elif adx > 40:  # Very strong trend - higher RR
            base_rr *= 1.3
            logger.info(f"Very strong trend (ADX={adx:.1f}): Increasing RR by 30%")
        elif adx > 30:  # Strong trend - higher RR
            base_rr *= 1.2
            logger.info(f"Strong trend (ADX={adx:.1f}): Increasing RR by 20%")
        elif adx > 20:  # Moderate trend - slightly higher RR
            base_rr *= 1.1
            logger.info(f"Moderate trend (ADX={adx:.1f}): Increasing RR by 10%")

        # Adjust based on volatility
        volatility = market_conditions.get("volatility", 0.01)
        if volatility > 0.03:  # Extremely high volatility
            base_rr *= 1.3
            logger.info(f"Extremely high volatility ({volatility*100:.1f}%): Increasing RR by 30%")
        elif volatility > 0.02:  # High volatility
            base_rr *= 1.2
            logger.info(f"High volatility ({volatility*100:.1f}%): Increasing RR by 20%")
        elif volatility > 0.015:  # Above average volatility
            base_rr *= 1.1
            logger.info(f"Above average volatility ({volatility*100:.1f}%): Increasing RR by 10%")

        # Consider trend direction
        trend_direction = market_conditions.get("trend_direction", 0.0)
        if abs(trend_direction) > 0.02:  # Strong trend
            base_rr *= 1.1
            logger.info(f"Strong trend direction ({trend_direction:.4f}): Increasing RR by 10%")

        # Ensure RR is within reasonable bounds
        final_rr = max(1.0, min(3.0, base_rr))
        logger.info(f"Final risk-reward ratio: {final_rr:.2f}")
        return final_rr

    async def get_active_trades(self) -> List[ManagedTrade]:
        """Get all active trades.

        Returns:
            A list of all active trades.
        """
        return list(self.active_trades.values())

    async def get_trade_by_id(self, trade_id: str) -> Optional[ManagedTrade]:
        """Get a trade by its ID."""
        try:
            async with self.db_session_factory() as session:
                from app.repositories.trade_repository import TradeRepository
                repo = TradeRepository(session)
                trade = await repo.get_trade_by_id(uuid.UUID(trade_id))
                return trade
        except Exception as e:
            logger.error(f"Error getting trade by ID {trade_id}: {e}", exc_info=True)
            return None

    async def close_trade(self, trade_id: str, reason: str = "MANUAL") -> bool:
        """Close a trade by cancelling all open orders."""
        trade = await self.get_trade_by_id(trade_id)
        if not trade:
            logger.error(f"Trade {trade_id} not found.")
            return False

        cancellation_success = True
        try:
            if trade.sl_order_id:
                sl_cancelled = await self.order_manager.cancel_order(trade.symbol, trade.sl_order_id)
                if not sl_cancelled:
                    logger.error(f"Failed to cancel SL order {trade.sl_order_id} for trade {trade.trade_id}")
                    cancellation_success = False

            if trade.tp_order_id:
                tp_cancelled = await self.order_manager.cancel_order(trade.symbol, trade.tp_order_id)
                if not tp_cancelled:
                    logger.error(f"Failed to cancel TP order {trade.tp_order_id} for trade {trade.trade_id}")
                    cancellation_success = False

        except Exception as e:
            logger.error(f"Error during order cancellation for trade {trade_id}: {e}", exc_info=True)
            cancellation_success = False
        
        # Always update the trade status, even if cancellation fails, to prevent re-attempts.
        try:
            trade.status = TradeStatus.CLOSED
            trade.exit_reason = reason
            trade.update_timestamp()

            # Update in database
            if hasattr(self, 'trade_repository') and self.trade_repository is not None:
                repo = self.trade_repository
            else:
                async with self.db_session_factory() as session:
                    from app.repositories.trade_repository import TradeRepository
                    repo = TradeRepository(session)
            await repo.update_trade(trade)

            return cancellation_success
            
        except Exception as e:
            logger.error(f"Error updating trade {trade_id} after closing: {e}", exc_info=True)
            return False
