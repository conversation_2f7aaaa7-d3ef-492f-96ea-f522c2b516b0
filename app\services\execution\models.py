"""Models for the execution service."""
from typing import Optional
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict

class OrderType(Enum):
    """Enum for order types."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"  # Maps to STOP_MARKET
    TAKE_PROFIT = "TAKE_PROFIT"  # Maps to TAKE_PROFIT_MARKET

class OrderSide(Enum):
    """Enum for order sides."""
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(Enum):
    """Enum for order statuses."""
    OPEN = "OPEN"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    FAILED = "FAILED"
    UNKNOWN = "UNKNOWN"

class Order(BaseModel):
    """Model for an order."""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    client_order_id: Optional[str] = None
    exchange_order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.UNKNOWN
    timestamp: datetime = Field(default_factory=datetime.now)
    filled_quantity: float = 0.0
    average_price: Optional[float] = None
    commission: Optional[float] = None
    commission_asset: Optional[str] = None

    model_config = ConfigDict(extra='ignore')

    def update_execution(self, filled_quantity: float, average_price: float,
                         commission: Optional[float] = None,
                         commission_asset: Optional[str] = None):
        """Update order execution details.

        Args:
            filled_quantity: The quantity that was filled.
            average_price: The average price at which the order was filled.
            commission: The commission paid for the order.
            commission_asset: The asset in which the commission was paid.
        """
        self.filled_quantity = filled_quantity
        self.average_price = average_price
        self.commission = commission
        self.commission_asset = commission_asset

    def to_dict(self):
        """Convert the order to a dictionary.

        Returns:
            A dictionary representation of the order.
        """
        return self.model_dump(exclude_none=True)
