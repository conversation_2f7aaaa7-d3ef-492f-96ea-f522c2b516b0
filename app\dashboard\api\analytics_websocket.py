# app/dashboard/api/analytics_websocket.py
"""
Enhanced WebSocket API for real-time analytics and ensemble monitoring.
Integrates with the existing dashboard to provide live performance metrics.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Set, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, BackgroundTasks
from starlette.websockets import WebSocketState

from app.services.mcp.supabase_realtime_analytics import SupabaseRealTimeAnalytics, RealTimeMetrics
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

router = APIRouter()

# Store active connections by type
analytics_connections: Set[WebSocket] = set()
ensemble_connections: Set[WebSocket] = set()

# Global analytics service (will be initialized)
analytics_service: Optional[SupabaseRealTimeAnalytics] = None

async def initialize_analytics_service():
    """Initialize the real-time analytics service."""
    global analytics_service
    
    if analytics_service is None:
        try:
            # Initialize Supabase service
            supabase_service = SupabaseService()
            
            # Create analytics service
            analytics_service = SupabaseRealTimeAnalytics(supabase_service)
            
            # Start analytics monitoring in background
            asyncio.create_task(analytics_service.start_real_time_monitoring())
            
            logger.info("Real-time analytics service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize analytics service: {e}")
            analytics_service = None

@router.websocket("/ws/analytics")
async def websocket_analytics(websocket: WebSocket):
    """WebSocket endpoint for real-time analytics updates."""
    await websocket.accept()
    analytics_connections.add(websocket)
    
    # Initialize analytics service if needed
    if analytics_service is None:
        await initialize_analytics_service()
    
    try:
        # Send initial connection status
        await websocket.send_json({
            "type": "analytics_connected",
            "data": {
                "status": "connected",
                "timestamp": datetime.now().isoformat(),
                "message": "Connected to real-time analytics stream"
            }
        })
        
        # Subscribe to analytics updates
        def analytics_callback(metrics: RealTimeMetrics):
            """Callback for analytics updates."""
            asyncio.create_task(send_analytics_update(websocket, metrics))
        
        if analytics_service:
            analytics_service.subscribe_to_updates("performance_update", str(id(websocket)), analytics_callback)
        
        # Keep connection alive and handle client messages
        while True:
            if websocket.client_state == WebSocketState.DISCONNECTED:
                break
            
            try:
                # Wait for client messages or timeout for heartbeat
                data = await asyncio.wait_for(websocket.receive_json(), timeout=30.0)
                
                # Handle client requests
                await handle_analytics_message(websocket, data)
                
            except asyncio.TimeoutError:
                # Send heartbeat
                await websocket.send_json({
                    "type": "heartbeat",
                    "data": {"timestamp": datetime.now().isoformat()}
                })
                
            except Exception as e:
                logger.error(f"Error in analytics WebSocket: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info("Analytics client disconnected")
    except Exception as e:
        logger.error(f"Analytics WebSocket error: {e}")
    finally:
        # Clean up
        if websocket in analytics_connections:
            analytics_connections.remove(websocket)
        
        if analytics_service:
            analytics_service.unsubscribe_from_updates("performance_update", str(id(websocket)))

@router.websocket("/ws/ensemble")
async def websocket_ensemble(websocket: WebSocket):
    """WebSocket endpoint for real-time ensemble strategy monitoring."""
    await websocket.accept()
    ensemble_connections.add(websocket)
    
    # Initialize analytics service if needed
    if analytics_service is None:
        await initialize_analytics_service()
    
    try:
        # Send initial ensemble status
        await websocket.send_json({
            "type": "ensemble_connected",
            "data": {
                "status": "connected",
                "timestamp": datetime.now().isoformat(),
                "message": "Connected to ensemble monitoring stream"
            }
        })
        
        # Subscribe to ensemble-specific updates
        def ensemble_callback(data):
            """Callback for ensemble updates."""
            asyncio.create_task(send_ensemble_update(websocket, data))
        
        if analytics_service:
            analytics_service.subscribe_to_updates("dashboard_update", str(id(websocket)), ensemble_callback)
            analytics_service.subscribe_to_updates("new_alerts", str(id(websocket)), ensemble_callback)
        
        # Keep connection alive
        while True:
            if websocket.client_state == WebSocketState.DISCONNECTED:
                break
            
            try:
                # Wait for client messages
                data = await asyncio.wait_for(websocket.receive_json(), timeout=45.0)
                
                # Handle ensemble-specific requests
                await handle_ensemble_message(websocket, data)
                
            except asyncio.TimeoutError:
                # Send heartbeat
                await websocket.send_json({
                    "type": "heartbeat",
                    "data": {"timestamp": datetime.now().isoformat()}
                })
                
            except Exception as e:
                logger.error(f"Error in ensemble WebSocket: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info("Ensemble client disconnected")
    except Exception as e:
        logger.error(f"Ensemble WebSocket error: {e}")
    finally:
        # Clean up
        if websocket in ensemble_connections:
            ensemble_connections.remove(websocket)
        
        if analytics_service:
            analytics_service.unsubscribe_from_updates("dashboard_update", str(id(websocket)))
            analytics_service.unsubscribe_from_updates("new_alerts", str(id(websocket)))

async def send_analytics_update(websocket: WebSocket, metrics: RealTimeMetrics):
    """Send analytics update to a specific WebSocket."""
    try:
        message = {
            "type": "analytics_update",
            "data": {
                "timestamp": metrics.timestamp.isoformat(),
                "portfolio_value": metrics.portfolio_value,
                "total_pnl": metrics.total_pnl,
                "hourly_pnl": metrics.hourly_pnl,
                "daily_pnl": metrics.daily_pnl,
                "sharpe_ratio": metrics.sharpe_ratio,
                "max_drawdown": metrics.max_drawdown,
                "win_rate": metrics.win_rate,
                "cache_hit_rate": metrics.cache_hit_rate,
                "avg_execution_time_ms": metrics.avg_execution_time_ms,
                "active_positions": metrics.active_positions,
                "alerts_count": metrics.alerts_count,
                "market_regime": metrics.market_regime
            }
        }
        
        await websocket.send_json(message)
        
    except Exception as e:
        logger.error(f"Failed to send analytics update: {e}")

async def send_ensemble_update(websocket: WebSocket, data: Any):
    """Send ensemble update to a specific WebSocket."""
    try:
        message = {
            "type": "ensemble_update",
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        await websocket.send_json(message)
        
    except Exception as e:
        logger.error(f"Failed to send ensemble update: {e}")

async def handle_analytics_message(websocket: WebSocket, data: Dict):
    """Handle incoming analytics WebSocket messages."""
    try:
        message_type = data.get("type")
        
        if message_type == "get_summary":
            # Send dashboard summary
            if analytics_service:
                summary = await analytics_service.get_dashboard_summary()
                await websocket.send_json({
                    "type": "dashboard_summary",
                    "data": summary
                })
        
        elif message_type == "get_strategy_performance":
            # Send strategy performance comparison
            if analytics_service:
                performance = await analytics_service.supabase.get_strategy_performance_comparison()
                await websocket.send_json({
                    "type": "strategy_performance",
                    "data": performance
                })
        
        elif message_type == "acknowledge_alert":
            # Acknowledge an alert
            alert_id = data.get("alert_id")
            if alert_id and analytics_service:
                success = await analytics_service.supabase.acknowledge_alert(alert_id)
                await websocket.send_json({
                    "type": "alert_acknowledged",
                    "data": {"alert_id": alert_id, "success": success}
                })
        
        else:
            logger.warning(f"Unknown analytics message type: {message_type}")
    
    except Exception as e:
        logger.error(f"Error handling analytics message: {e}")

async def handle_ensemble_message(websocket: WebSocket, data: Dict):
    """Handle incoming ensemble WebSocket messages."""
    try:
        message_type = data.get("type")
        
        if message_type == "get_weights_history":
            # Get strategy weights history
            if analytics_service:
                # This would fetch from Supabase - simplified for now
                await websocket.send_json({
                    "type": "weights_history",
                    "data": {"message": "Weights history would be fetched here"}
                })
        
        elif message_type == "get_correlation_matrix":
            # Send current correlation matrix
            if analytics_service and analytics_service.performance_buffer:
                latest_metrics = analytics_service.performance_buffer[-1]
                await websocket.send_json({
                    "type": "correlation_matrix",
                    "data": latest_metrics.correlation_matrix
                })
        
        elif message_type == "trigger_rebalance":
            # Trigger portfolio rebalancing (would integrate with portfolio manager)
            await websocket.send_json({
                "type": "rebalance_triggered",
                "data": {"status": "scheduled", "timestamp": datetime.now().isoformat()}
            })
        
        else:
            logger.warning(f"Unknown ensemble message type: {message_type}")
    
    except Exception as e:
        logger.error(f"Error handling ensemble message: {e}")

async def broadcast_analytics_update(metrics: RealTimeMetrics):
    """Broadcast analytics update to all connected analytics clients."""
    if not analytics_connections:
        return
    
    disconnected = set()
    for connection in analytics_connections:
        try:
            await send_analytics_update(connection, metrics)
        except Exception as e:
            logger.error(f"Error broadcasting analytics update: {e}")
            disconnected.add(connection)
    
    # Clean up disconnected clients
    for connection in disconnected:
        if connection in analytics_connections:
            analytics_connections.remove(connection)

async def broadcast_ensemble_update(data: Any):
    """Broadcast ensemble update to all connected ensemble clients."""
    if not ensemble_connections:
        return
    
    disconnected = set()
    for connection in ensemble_connections:
        try:
            await send_ensemble_update(connection, data)
        except Exception as e:
            logger.error(f"Error broadcasting ensemble update: {e}")
            disconnected.add(connection)
    
    # Clean up disconnected clients
    for connection in disconnected:
        if connection in ensemble_connections:
            ensemble_connections.remove(connection)

async def broadcast_alert(alert_data: Dict):
    """Broadcast alert to all connected clients."""
    message = {
        "type": "new_alert",
        "data": alert_data,
        "timestamp": datetime.now().isoformat()
    }
    
    # Send to both analytics and ensemble connections
    all_connections = analytics_connections | ensemble_connections
    
    disconnected = set()
    for connection in all_connections:
        try:
            await connection.send_json(message)
        except Exception as e:
            logger.error(f"Error broadcasting alert: {e}")
            disconnected.add(connection)
    
    # Clean up disconnected clients
    for connection in disconnected:
        if connection in analytics_connections:
            analytics_connections.remove(connection)
        if connection in ensemble_connections:
            ensemble_connections.remove(connection)

# Startup function to initialize analytics
async def startup_analytics():
    """Initialize analytics service on startup."""
    await initialize_analytics_service()
    logger.info("Analytics WebSocket service ready")

# Shutdown function
async def shutdown_analytics():
    """Shutdown analytics service."""
    global analytics_service
    
    if analytics_service:
        await analytics_service.stop_real_time_monitoring()
        analytics_service = None
    
    logger.info("Analytics WebSocket service shutdown successfully")

# Helper function to check if analytics service is running
def is_analytics_running() -> bool:
    """Check if analytics service is running."""
    return analytics_service is not None and analytics_service.is_running

# Helper function to get current analytics status
async def get_analytics_status() -> Dict[str, Any]:
    """Get current analytics service status."""
    return {
        "service_running": is_analytics_running(),
        "analytics_connections": len(analytics_connections),
        "ensemble_connections": len(ensemble_connections),
        "total_connections": len(analytics_connections) + len(ensemble_connections),
        "last_update": datetime.now().isoformat()
    }