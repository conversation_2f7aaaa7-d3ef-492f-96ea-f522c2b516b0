# tests/strategies/test_ensemble_portfolio_manager.py
"""
Comprehensive test suite for Enhanced Portfolio Manager with Redis caching and MCP integration.
Tests the real-time signal aggregation, caching strategies, and sub-second performance.
"""

import pytest
import asyncio
import json
import numpy as np
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from dataclasses import asdict

import sys
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

from app.strategies.ensemble_portfolio_manager import (
    EnsemblePortfolioManager,
    CachedSignal,
    AggregatedSignal,
    PerformanceMetrics,
    create_ensemble_portfolio_manager,
    benchmark_portfolio_manager
)

# Mock classes for testing
class MockMarketData:
    def __init__(self, symbol="BTCUSDT", price=50000.0, volume=1000000, timestamp=None):
        self.symbol = symbol
        self.price = price
        self.volume = volume
        self.timestamp = timestamp or datetime.now()
        self.volatility = 0.02
        self.rsi = 55
        self.macd = 0.01
        self.price_change = 0.005

class MockTradeState:
    def __init__(self, action="BUY", quantity=100.0, price=50000.0, pnl=100.0):
        self.action = action
        self.quantity = quantity
        self.price = price
        self.pnl = pnl
        self.timestamp = datetime.now()

class MockBaseStrategy:
    def __init__(self, name):
        self.__class__.__name__ = name
    
    async def generate_signal(self, market_data):
        return MockSignal()

class MockSignal:
    def __init__(self, action="BUY", quantity=100.0, price=50000.0, confidence=0.7):
        self.action = action
        self.quantity = quantity
        self.price = price
        self.confidence = confidence

class MockRedisService:
    def __init__(self):
        self.cache = {}
    
    async def get(self, key):
        return self.cache.get(key)
    
    async def setex(self, key, ttl, value):
        self.cache[key] = value
        return True

class MockWeightOptimizer:
    async def predict_weights(self, market_conditions):
        return np.array([0.33, 0.34, 0.33])

class TestCachedSignal:
    """Test the CachedSignal dataclass."""
    
    def test_cached_signal_creation(self):
        """Test creating a cached signal."""
        timestamp = datetime.now()
        signal = CachedSignal(
            strategy_name="GridStrategy",
            action="BUY",
            quantity=100.0,
            price=50000.0,
            confidence=0.8,
            timestamp=timestamp,
            market_conditions_hash="abc123",
            ttl_seconds=30
        )
        
        assert signal.strategy_name == "GridStrategy"
        assert signal.action == "BUY"
        assert signal.quantity == 100.0
        assert signal.price == 50000.0
        assert signal.confidence == 0.8
        assert signal.timestamp == timestamp
        assert signal.market_conditions_hash == "abc123"
        assert signal.ttl_seconds == 30

class TestAggregatedSignal:
    """Test the AggregatedSignal dataclass."""
    
    def test_aggregated_signal_creation(self):
        """Test creating an aggregated signal."""
        timestamp = datetime.now()
        signal = AggregatedSignal(
            action="BUY",
            quantity=150.0,
            price=50100.0,
            confidence=0.75,
            contributing_strategies=["GridStrategy", "TechnicalAnalysisStrategy"],
            strategy_weights={"GridStrategy": 0.6, "TechnicalAnalysisStrategy": 0.4},
            execution_priority=1,
            timestamp=timestamp,
            correlation_risk=0.3
        )
        
        assert signal.action == "BUY"
        assert signal.quantity == 150.0
        assert signal.price == 50100.0
        assert signal.confidence == 0.75
        assert len(signal.contributing_strategies) == 2
        assert signal.strategy_weights["GridStrategy"] == 0.6
        assert signal.execution_priority == 1
        assert signal.correlation_risk == 0.3

class TestPerformanceMetrics:
    """Test the PerformanceMetrics dataclass."""
    
    def test_performance_metrics_creation(self):
        """Test creating performance metrics."""
        metrics = PerformanceMetrics(
            signal_generation_ms=50.0,
            cache_hit_rate=0.85,
            aggregation_time_ms=25.0,
            total_execution_time_ms=150.0,
            cache_operations=10,
            successful_predictions=8,
            failed_predictions=2
        )
        
        assert metrics.signal_generation_ms == 50.0
        assert metrics.cache_hit_rate == 0.85
        assert metrics.aggregation_time_ms == 25.0
        assert metrics.total_execution_time_ms == 150.0
        assert metrics.cache_operations == 10
        assert metrics.successful_predictions == 8
        assert metrics.failed_predictions == 2

@pytest.mark.asyncio
class TestEnsemblePortfolioManager:
    """Test the Enhanced Portfolio Manager with Redis caching."""
    
    @pytest.fixture
    def mock_strategies(self):
        """Create mock strategies."""
        strategies = [
            MockBaseStrategy("GridStrategy"),
            MockBaseStrategy("TechnicalAnalysisStrategy"), 
            MockBaseStrategy("TrendFollowingStrategy")
        ]
        return strategies
    
    @pytest.fixture
    def mock_redis_service(self):
        """Create mock Redis service."""
        return MockRedisService()
    
    @pytest.fixture
    def mock_weight_optimizer(self):
        """Create mock weight optimizer."""
        return MockWeightOptimizer()
    
    @pytest.fixture
    def mock_wandb_service(self):
        """Create mock W&B service."""
        service = AsyncMock()
        service.log_strategy_performance = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_supabase_service(self):
        """Create mock Supabase service."""
        service = AsyncMock()
        service.store_trade_execution = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_execution_service(self):
        """Create mock execution service."""
        service = AsyncMock()
        service.execute_trade = AsyncMock(return_value=MockTradeState())
        return service
    
    @pytest.fixture
    def portfolio_manager(self, mock_strategies, mock_weight_optimizer, mock_redis_service, 
                         mock_wandb_service, mock_supabase_service, mock_execution_service):
        """Create portfolio manager instance for testing."""
        return EnsemblePortfolioManager(
            strategies=mock_strategies,
            weight_optimizer=mock_weight_optimizer,
            redis_service=mock_redis_service,
            supabase_service=mock_supabase_service,
            wandb_service=mock_wandb_service,
            execution_service=mock_execution_service
        )
    
    def test_portfolio_manager_initialization(self, portfolio_manager):
        """Test portfolio manager initialization."""
        assert len(portfolio_manager.strategies) == 3
        assert "GridStrategy" in portfolio_manager.strategies
        assert "TechnicalAnalysisStrategy" in portfolio_manager.strategies
        assert "TrendFollowingStrategy" in portfolio_manager.strategies
        assert portfolio_manager.config["signal_cache_ttl"] == 30
        assert portfolio_manager.config["weights_cache_ttl"] == 300
        assert portfolio_manager.config["min_confidence_threshold"] == 0.6
    
    async def test_generate_market_hash(self, portfolio_manager):
        """Test market conditions hash generation."""
        market_data = MockMarketData()
        hash1 = portfolio_manager._generate_market_hash(market_data)
        
        # Hash should be consistent for same data
        hash2 = portfolio_manager._generate_market_hash(market_data)
        assert hash1 == hash2
        
        # Hash should be different for different data
        market_data.price = 51000.0
        hash3 = portfolio_manager._generate_market_hash(market_data)
        assert hash1 != hash3
        
        # Hash should be reasonable length
        assert len(hash1) == 12
    
    async def test_cached_strategy_weights_cache_miss(self, portfolio_manager):
        """Test getting strategy weights when cache is empty."""
        market_data = MockMarketData()
        market_hash = portfolio_manager._generate_market_hash(market_data)
        
        # Cache should be empty
        weights = await portfolio_manager._get_cached_strategy_weights(market_data, market_hash)
        
        assert len(weights) == 3
        assert "GridStrategy" in weights
        assert "TechnicalAnalysisStrategy" in weights
        assert "TrendFollowingStrategy" in weights
        
        # Weights should sum to approximately 1.0
        total_weight = sum(weights.values())
        assert abs(total_weight - 1.0) < 0.001
    
    async def test_cached_strategy_weights_cache_hit(self, portfolio_manager):
        """Test getting strategy weights from cache."""
        market_data = MockMarketData()
        market_hash = portfolio_manager._generate_market_hash(market_data)
        
        # Pre-populate cache
        cached_weights = {
            "GridStrategy": 0.4,
            "TechnicalAnalysisStrategy": 0.35,
            "TrendFollowingStrategy": 0.25
        }
        cache_data = {
            'weights': cached_weights,
            'timestamp': datetime.now().isoformat(),
            'market_hash': market_hash
        }
        cache_key = f"{portfolio_manager.WEIGHTS_CACHE_KEY}:{market_hash}"
        await portfolio_manager.redis_service.setex(cache_key, 300, json.dumps(cache_data))
        
        # Get weights from cache
        weights = await portfolio_manager._get_cached_strategy_weights(market_data, market_hash)
        
        assert weights["GridStrategy"] == 0.4
        assert weights["TechnicalAnalysisStrategy"] == 0.35
        assert weights["TrendFollowingStrategy"] == 0.25
    
    async def test_cached_strategy_signals_parallel_execution(self, portfolio_manager):
        """Test parallel strategy signal generation."""
        market_data = MockMarketData()
        market_hash = portfolio_manager._generate_market_hash(market_data)
        
        # Should generate signals for all strategies
        signals = await portfolio_manager._get_cached_strategy_signals(market_data, market_hash)
        
        assert len(signals) == 3
        assert "GridStrategy" in signals
        assert "TechnicalAnalysisStrategy" in signals
        assert "TrendFollowingStrategy" in signals
        
        # Each signal should have required fields
        for strategy_name, signal in signals.items():
            assert "action" in signal
            assert "quantity" in signal
            assert "price" in signal
            assert "confidence" in signal
    
    async def test_signal_aggregation_buy_consensus(self, portfolio_manager):
        """Test signal aggregation with BUY consensus."""
        strategy_signals = {
            "GridStrategy": {"action": "BUY", "quantity": 100, "price": 50000, "confidence": 0.8},
            "TechnicalAnalysisStrategy": {"action": "BUY", "quantity": 120, "price": 50100, "confidence": 0.7},
            "TrendFollowingStrategy": {"action": "HOLD", "quantity": 0, "price": 0, "confidence": 0.3}
        }
        
        strategy_weights = {
            "GridStrategy": 0.4,
            "TechnicalAnalysisStrategy": 0.4,
            "TrendFollowingStrategy": 0.2
        }
        
        market_hash = "test_hash"
        
        aggregated = await portfolio_manager._aggregate_signals_with_caching(
            strategy_signals, strategy_weights, market_hash
        )
        
        assert aggregated.action == "BUY"
        assert aggregated.quantity > 0
        assert aggregated.price > 0
        assert aggregated.confidence > 0
        assert "GridStrategy" in aggregated.contributing_strategies
        assert "TechnicalAnalysisStrategy" in aggregated.contributing_strategies
    
    async def test_signal_aggregation_conflict_resolution(self, portfolio_manager):
        """Test signal aggregation with conflicting signals."""
        strategy_signals = {
            "GridStrategy": {"action": "BUY", "quantity": 100, "price": 50000, "confidence": 0.8},
            "TechnicalAnalysisStrategy": {"action": "SELL", "quantity": 80, "price": 49900, "confidence": 0.6},
            "TrendFollowingStrategy": {"action": "HOLD", "quantity": 0, "price": 0, "confidence": 0.5}
        }
        
        strategy_weights = {
            "GridStrategy": 0.5,  # Higher weight
            "TechnicalAnalysisStrategy": 0.3,
            "TrendFollowingStrategy": 0.2
        }
        
        market_hash = "test_hash"
        
        aggregated = await portfolio_manager._aggregate_signals_with_caching(
            strategy_signals, strategy_weights, market_hash
        )
        
        # BUY should win due to higher weight and confidence
        assert aggregated.action == "BUY"
        assert aggregated.confidence > 0
    
    async def test_correlation_risk_calculation(self, portfolio_manager):
        """Test correlation risk calculation."""
        contributing_strategies = ["GridStrategy", "TechnicalAnalysisStrategy"]
        
        # Mock correlation calculation
        with patch.object(portfolio_manager, '_calculate_strategy_correlations') as mock_corr:
            mock_corr.return_value = {
                "GridStrategy": {"TechnicalAnalysisStrategy": 0.6},
                "TechnicalAnalysisStrategy": {"GridStrategy": 0.6}
            }
            
            correlation_risk = await portfolio_manager._calculate_correlation_risk(contributing_strategies)
            
            assert correlation_risk == 0.6  # Average correlation
    
    async def test_should_execute_signal_high_confidence(self, portfolio_manager):
        """Test signal execution decision with high confidence."""
        signal = AggregatedSignal(
            action="BUY",
            quantity=100.0,
            price=50000.0,
            confidence=0.8,  # Above threshold
            contributing_strategies=["GridStrategy"],
            strategy_weights={"GridStrategy": 1.0},
            execution_priority=1,
            timestamp=datetime.now(),
            correlation_risk=0.3  # Below threshold
        )
        
        should_execute = portfolio_manager._should_execute_signal(signal)
        assert should_execute is True
    
    async def test_should_execute_signal_low_confidence(self, portfolio_manager):
        """Test signal execution decision with low confidence."""
        signal = AggregatedSignal(
            action="BUY",
            quantity=100.0,
            price=50000.0,
            confidence=0.5,  # Below threshold
            contributing_strategies=["GridStrategy"],
            strategy_weights={"GridStrategy": 1.0},
            execution_priority=1,
            timestamp=datetime.now(),
            correlation_risk=0.3
        )
        
        should_execute = portfolio_manager._should_execute_signal(signal)
        assert should_execute is False
    
    async def test_should_execute_signal_high_correlation_risk(self, portfolio_manager):
        """Test signal execution decision with high correlation risk."""
        signal = AggregatedSignal(
            action="BUY",
            quantity=100.0,
            price=50000.0,
            confidence=0.8,
            contributing_strategies=["GridStrategy"],
            strategy_weights={"GridStrategy": 1.0},
            execution_priority=1,
            timestamp=datetime.now(),
            correlation_risk=0.9  # Above threshold
        )
        
        should_execute = portfolio_manager._should_execute_signal(signal)
        assert should_execute is False
    
    async def test_execution_priority_calculation(self, portfolio_manager):
        """Test execution priority calculation."""
        # High confidence
        priority_high = portfolio_manager._calculate_execution_priority("BUY", 0.9)
        assert priority_high == 1
        
        # Medium confidence
        priority_medium = portfolio_manager._calculate_execution_priority("BUY", 0.7)
        assert priority_medium == 2
        
        # Low confidence
        priority_low = portfolio_manager._calculate_execution_priority("BUY", 0.5)
        assert priority_low == 3
        
        # HOLD action
        priority_hold = portfolio_manager._calculate_execution_priority("HOLD", 0.9)
        assert priority_hold == 0
    
    async def test_full_ensemble_execution_high_confidence(self, portfolio_manager):
        """Test full ensemble execution with high confidence signal."""
        market_data = MockMarketData()
        
        # Execute ensemble
        trades, metrics = await portfolio_manager.execute_ensemble_with_caching(market_data)
        
        # Should execute trade due to high confidence
        assert len(trades) == 1
        assert trades[0].action == "BUY"
        
        # Performance metrics should be tracked
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.total_execution_time_ms > 0
        assert metrics.successful_predictions >= 0
    
    async def test_full_ensemble_execution_performance_tracking(self, portfolio_manager):
        """Test ensemble execution with performance tracking."""
        market_data = MockMarketData()
        
        # Configure for performance tracking
        portfolio_manager.config["performance_tracking_enabled"] = True
        
        # Execute ensemble multiple times to build metrics
        for _ in range(3):
            trades, metrics = await portfolio_manager.execute_ensemble_with_caching(market_data)
        
        # Performance should improve with caching
        assert metrics.cache_operations > 0
        assert metrics.total_execution_time_ms > 0
    
    async def test_performance_summary(self, portfolio_manager):
        """Test performance summary generation."""
        # Execute some operations to build metrics
        market_data = MockMarketData()
        await portfolio_manager.execute_ensemble_with_caching(market_data)
        
        summary = await portfolio_manager.get_performance_summary()
        
        assert "cache_performance" in summary
        assert "prediction_performance" in summary
        assert "configuration" in summary
        assert "strategies" in summary
        
        # Cache performance metrics
        assert "hit_rate" in summary["cache_performance"]
        assert "operations" in summary["cache_performance"]
        assert "avg_total_time_ms" in summary["cache_performance"]
        
        # Strategy list
        assert len(summary["strategies"]) == 3
        assert "GridStrategy" in summary["strategies"]

class TestEnsemblePortfolioManagerIntegration:
    """Integration tests for the portfolio manager."""
    
    @pytest.mark.asyncio
    async def test_create_ensemble_portfolio_manager_factory(self):
        """Test factory function for creating portfolio manager."""
        mock_strategies = [MockBaseStrategy("GridStrategy")]
        mock_weight_optimizer = MockWeightOptimizer()
        redis_url = "redis://localhost:6379"
        
        with patch('app.strategies.ensemble_portfolio_manager.RedisService') as mock_redis_class, \
             patch('app.strategies.ensemble_portfolio_manager.SupabaseService') as mock_supabase_class, \
             patch('app.strategies.ensemble_portfolio_manager.WandBService') as mock_wandb_class:
            
            mock_redis_class.return_value = MockRedisService()
            mock_supabase_class.return_value = AsyncMock()
            mock_wandb_class.return_value = AsyncMock()
            
            portfolio_manager = await create_ensemble_portfolio_manager(
                strategies=mock_strategies,
                redis_url=redis_url,
                weight_optimizer=mock_weight_optimizer,
                supabase_url="https://test.supabase.co",
                supabase_key="test_key",
                wandb_api_key="test_wandb_key"
            )
            
            assert isinstance(portfolio_manager, EnsemblePortfolioManager)
            assert len(portfolio_manager.strategies) == 1
    
    @pytest.mark.asyncio
    async def test_benchmark_portfolio_manager(self):
        """Test portfolio manager benchmarking."""
        # Create mock portfolio manager
        mock_portfolio_manager = AsyncMock()
        mock_portfolio_manager.execute_ensemble_with_caching = AsyncMock(
            return_value=([], PerformanceMetrics(
                signal_generation_ms=50.0,
                cache_hit_rate=0.8,
                aggregation_time_ms=25.0,
                total_execution_time_ms=100.0,
                cache_operations=1,
                successful_predictions=1,
                failed_predictions=0
            ))
        )
        
        # Create test market data
        test_market_data = [MockMarketData() for _ in range(5)]
        
        # Run benchmark
        results = await benchmark_portfolio_manager(
            portfolio_manager=mock_portfolio_manager,
            test_market_data=test_market_data,
            num_iterations=5
        )
        
        assert "avg_execution_time_ms" in results
        assert "min_execution_time_ms" in results
        assert "max_execution_time_ms" in results
        assert "p95_execution_time_ms" in results
        assert "avg_cache_hit_rate" in results
        assert "sub_second_rate" in results
        assert "performance_target_met" in results
        assert results["total_iterations"] == 5

class TestCacheOptimizations:
    """Test caching optimizations and performance."""
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self):
        """Test cache key generation for different scenarios."""
        portfolio_manager = EnsemblePortfolioManager(
            strategies=[MockBaseStrategy("TestStrategy")],
            weight_optimizer=MockWeightOptimizer(),
            redis_service=MockRedisService()
        )
        
        market_data = MockMarketData()
        market_hash = portfolio_manager._generate_market_hash(market_data)
        
        # Test different cache key patterns
        signal_key = f"{portfolio_manager.SIGNAL_CACHE_PREFIX}TestStrategy:{market_hash}"
        weights_key = f"{portfolio_manager.WEIGHTS_CACHE_KEY}:{market_hash}"
        aggregation_key = f"{portfolio_manager.AGGREGATED_SIGNAL_KEY}:{market_hash}"
        
        assert signal_key.startswith("signal:")
        assert weights_key.startswith("ensemble:weights:")
        assert aggregation_key.startswith("ensemble:aggregated_signal:")
    
    @pytest.mark.asyncio
    async def test_cache_ttl_configurations(self):
        """Test different TTL configurations."""
        config = {
            "signal_cache_ttl": 15,
            "weights_cache_ttl": 600,
            "aggregation_cache_ttl": 5,
            "correlation_cache_ttl": 3600
        }
        
        portfolio_manager = EnsemblePortfolioManager(
            strategies=[MockBaseStrategy("TestStrategy")],
            weight_optimizer=MockWeightOptimizer(),
            redis_service=MockRedisService(),
            config=config
        )
        
        assert portfolio_manager.config["signal_cache_ttl"] == 15
        assert portfolio_manager.config["weights_cache_ttl"] == 600
        assert portfolio_manager.config["aggregation_cache_ttl"] == 5
        assert portfolio_manager.config["correlation_cache_ttl"] == 3600

if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-x"])