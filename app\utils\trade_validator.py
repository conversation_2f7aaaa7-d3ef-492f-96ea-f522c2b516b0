"""
Trade validation utilities for the Multi-Strategy Crypto Auto Trader.
Validates trade parameters, quantities, prices, and business rules.
"""
from typing import Dict, Optional, Union
from decimal import Decimal
from datetime import datetime
from enum import Enum

from app.models.trade_status import TradeStatus


class TradeSide(Enum):
    """Trade side enumeration."""
    BUY = "BUY"
    SELL = "SELL"


class TradeValidationError(Exception):
    """Exception raised when trade validation fails."""
    pass


class TradeValidator:
    """Validates trade parameters and business rules."""
    
    # Binance minimum notional value (USD)
    MIN_NOTIONAL_VALUE = 10.0
    
    # Common minimum quantities for major trading pairs
    MIN_QUANTITIES = {
        'BTCUSDT': 0.00001,
        'ETHUSDT': 0.0001,
        'ADAUSDT': 1.0,
        'SOLUSDT': 0.01,
        'DOTUSDT': 0.1,
    }
    
    # Price precision for major trading pairs
    PRICE_PRECISIONS = {
        'BTCUSDT': 2,
        'ETHUSDT': 2,
        'ADAUSDT': 5,
        'SOLUSDT': 3,
        'DOTUSDT': 4,
    }
    
    # Valid state transitions
    VALID_TRANSITIONS = {
        TradeStatus.PENDING_ENTRY: [TradeStatus.ENTRY_FILLED, TradeStatus.ERROR],
        TradeStatus.ENTRY_FILLED: [TradeStatus.SLTP_PLACED, TradeStatus.CLOSED_MANUAL, TradeStatus.ERROR],
        TradeStatus.SLTP_PLACED: [TradeStatus.ACTIVE, TradeStatus.ERROR],
        TradeStatus.ACTIVE: [TradeStatus.CLOSED_TP, TradeStatus.CLOSED_SL, TradeStatus.CLOSED_MANUAL, TradeStatus.ERROR],
        TradeStatus.CLOSED_TP: [],  # Terminal state
        TradeStatus.CLOSED_SL: [],  # Terminal state
        TradeStatus.CLOSED_MANUAL: [],  # Terminal state
        TradeStatus.ERROR: []  # Terminal state
    }
    
    @classmethod
    def validate_trade_data(cls, trade_data: Dict) -> None:
        """
        Validate complete trade data structure.
        
        Args:
            trade_data: Dictionary containing trade information
            
        Raises:
            TradeValidationError: If validation fails
        """
        required_fields = ['symbol', 'side', 'quantity', 'price', 'timestamp']
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in trade_data]
        if missing_fields:
            raise TradeValidationError(f"Missing required fields: {', '.join(missing_fields)}")
        
        # Validate individual components
        cls.validate_symbol(trade_data['symbol'])
        cls.validate_side(trade_data['side'])
        cls.validate_quantity(trade_data['symbol'], trade_data['quantity'])
        cls.validate_price(trade_data['symbol'], trade_data['price'])
        cls.validate_notional_value(trade_data['quantity'], trade_data['price'])
        cls.validate_timestamp(trade_data['timestamp'])
    
    @classmethod
    def validate_symbol(cls, symbol: str) -> None:
        """
        Validate trading symbol format.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            
        Raises:
            TradeValidationError: If symbol is invalid
        """
        if not symbol or not isinstance(symbol, str):
            raise TradeValidationError("Symbol must be a non-empty string")
        
        if len(symbol) < 6:
            raise TradeValidationError("Symbol must be at least 6 characters long")
        
        if not symbol.isupper():
            raise TradeValidationError("Symbol must be uppercase")
    
    @classmethod
    def validate_side(cls, side: Union[str, TradeSide]) -> None:
        """
        Validate trade side.
        
        Args:
            side: Trade side ('BUY' or 'SELL')
            
        Raises:
            TradeValidationError: If side is invalid
        """
        if side is None:
            raise TradeValidationError("Side cannot be None")
        
        if isinstance(side, str):
            if side.upper() not in ['BUY', 'SELL']:
                raise TradeValidationError("Side must be 'BUY' or 'SELL'")
        elif not isinstance(side, TradeSide):
            raise TradeValidationError("Side must be TradeSide enum or string")
    
    @classmethod
    def validate_quantity(cls, symbol: str, quantity: Union[float, Decimal]) -> None:
        """
        Validate trade quantity against minimum requirements.
        
        Args:
            symbol: Trading pair symbol
            quantity: Trade quantity
            
        Raises:
            TradeValidationError: If quantity is invalid
        """
        if quantity <= 0:
            raise TradeValidationError("Quantity must be positive")
        
        min_quantity = cls.MIN_QUANTITIES.get(symbol, 0.00001)
        if quantity < min_quantity:
            raise TradeValidationError(
                f"Quantity {quantity} below minimum {min_quantity} for {symbol}"
            )
    
    @classmethod
    def validate_price(cls, symbol: str, price: Union[float, Decimal]) -> None:
        """
        Validate price format and precision.
        
        Args:
            symbol: Trading pair symbol
            price: Trade price
            
        Raises:
            TradeValidationError: If price is invalid
        """
        if price <= 0:
            raise TradeValidationError("Price must be positive")
        
        # Check price precision
        precision = cls.PRICE_PRECISIONS.get(symbol, 8)
        price_str = str(price)
        if '.' in price_str:
            decimal_places = len(price_str.split('.')[1])
            if decimal_places > precision:
                raise TradeValidationError(
                    f"Price precision {decimal_places} exceeds maximum {precision} for {symbol}"
                )
    
    @classmethod
    def validate_notional_value(cls, quantity: Union[float, Decimal], price: Union[float, Decimal]) -> None:
        """
        Validate minimum notional value requirement.
        
        Args:
            quantity: Trade quantity
            price: Trade price
            
        Raises:
            TradeValidationError: If notional value is too low
        """
        notional = float(quantity) * float(price)
        if notional < cls.MIN_NOTIONAL_VALUE:
            raise TradeValidationError(
                f"Notional value ${notional:.2f} below minimum ${cls.MIN_NOTIONAL_VALUE}"
            )
    
    @classmethod
    def validate_timestamp(cls, timestamp: Union[datetime, str, int]) -> None:
        """
        Validate trade timestamp.
        
        Args:
            timestamp: Trade timestamp
            
        Raises:
            TradeValidationError: If timestamp is invalid
        """
        if timestamp is None:
            raise TradeValidationError("Timestamp cannot be None")
        
        # For now, just ensure timestamp exists
        # Could add more sophisticated validation like market hours, etc.
    
    @classmethod
    def validate_stop_loss_take_profit(
        cls, 
        side: Union[str, TradeSide], 
        entry_price: float, 
        stop_loss: Optional[float] = None, 
        take_profit: Optional[float] = None
    ) -> None:
        """
        Validate stop-loss and take-profit levels.
        
        Args:
            side: Trade side ('BUY' or 'SELL')
            entry_price: Entry price
            stop_loss: Stop-loss price (optional)
            take_profit: Take-profit price (optional)
            
        Raises:
            TradeValidationError: If SL/TP levels are invalid
        """
        side_str = side.value if isinstance(side, TradeSide) else side.upper()
        
        if stop_loss is not None:
            if side_str == 'BUY' and stop_loss >= entry_price:
                raise TradeValidationError("Stop-loss for long position must be below entry price")
            elif side_str == 'SELL' and stop_loss <= entry_price:
                raise TradeValidationError("Stop-loss for short position must be above entry price")
        
        if take_profit is not None:
            if side_str == 'BUY' and take_profit <= entry_price:
                raise TradeValidationError("Take-profit for long position must be above entry price")
            elif side_str == 'SELL' and take_profit >= entry_price:
                raise TradeValidationError("Take-profit for short position must be below entry price")
    
    @classmethod
    def validate_status_transition(cls, current_status: TradeStatus, new_status: TradeStatus) -> None:
        """
        Validate trade status transition.
        
        Args:
            current_status: Current trade status
            new_status: Proposed new status
            
        Raises:
            TradeValidationError: If transition is invalid
        """
        valid_transitions = cls.VALID_TRANSITIONS.get(current_status, [])
        
        if new_status not in valid_transitions:
            raise TradeValidationError(
                f"Invalid status transition from {current_status.value} to {new_status.value}"
            )
    
    @classmethod
    def is_terminal_status(cls, status: TradeStatus) -> bool:
        """
        Check if trade status is terminal (no further transitions allowed).
        
        Args:
            status: Trade status to check
            
        Returns:
            bool: True if status is terminal
        """
        return len(cls.VALID_TRANSITIONS.get(status, [])) == 0