#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import { createClient, RedisClientType } from 'redis';
import { z } from 'zod';

// Zod schemas for type validation
const StrategyWeightSchema = z.object({
  strategy_name: z.string(),
  weight: z.number().min(0).max(1),
  confidence: z.number().min(0).max(1),
  last_updated: z.string(),
});

const PortfolioMetricsSchema = z.object({
  total_pnl: z.number(),
  sharpe_ratio: z.number(),
  max_drawdown: z.number(),
  win_rate: z.number(),
  strategy_contributions: z.record(z.number()),
  correlation_matrix: z.record(z.record(z.number())),
  timestamp: z.string(),
});

const KellyStatsSchema = z.object({
  win_rate: z.number(),
  avg_win: z.number(),
  avg_loss: z.number(),
  kelly_fraction: z.number(),
  confidence: z.number(),
  data_sources: z.array(z.string()),
});

const AggregatedSignalSchema = z.object({
  action: z.enum(['BUY', 'SELL', 'HOLD']),
  quantity: z.number(),
  price: z.number(),
  confidence: z.number(),
  contributing_strategies: z.array(z.string()),
  timestamp: z.string(),
});

// Redis cache key patterns
const CACHE_KEYS = {
  STRATEGY_WEIGHTS: 'ensemble:weights',
  SIGNALS: 'ensemble:signals',
  METRICS: 'ensemble:metrics',
  CORRELATION: 'ensemble:correlation',
  KELLY_STATS: 'position:kelly_stats',
  VOLATILITY: 'position:volatility',
  POSITION_CALC: 'position:calculation',
};

class RedisTradingMCP {
  private server: Server;
  private redis!: RedisClientType;

  constructor() {
    this.server = new Server(
      {
        name: 'redis-trading',
        version: '1.0.0',
      }
    );

    this.setupToolHandlers();
  }

  private async setupRedisClient() {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const redisDb = process.env.REDIS_DB || '0';
    
    this.redis = createClient({
      url: redisUrl,
      database: parseInt(redisDb),
    });

    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    await this.redis.connect();
    console.log('Connected to Redis');
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          // Strategy Weight Operations
          {
            name: 'get_strategy_weights',
            description: 'Get cached strategy weights with market conditions hash',
            inputSchema: {
              type: 'object',
              properties: {
                market_conditions_hash: {
                  type: 'string',
                  description: 'Hash of current market conditions for cache key',
                },
              },
            },
          },
          {
            name: 'cache_strategy_weights',
            description: 'Cache strategy weights with automatic TTL',
            inputSchema: {
              type: 'object',
              properties: {
                weights: {
                  type: 'object',
                  description: 'Strategy weights object to cache',
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds (default: 300)',
                  default: 300,
                },
              },
              required: ['weights'],
            },
          },

          // Signal Aggregation Operations
          {
            name: 'get_aggregated_signals',
            description: 'Get cached aggregated signals by hash',
            inputSchema: {
              type: 'object',
              properties: {
                signals_hash: {
                  type: 'string',
                  description: 'Hash of strategy signals for cache lookup',
                },
              },
              required: ['signals_hash'],
            },
          },
          {
            name: 'cache_aggregated_signals',
            description: 'Cache aggregated signals with short TTL',
            inputSchema: {
              type: 'object',
              properties: {
                signals_hash: {
                  type: 'string',
                  description: 'Hash key for the signals',
                },
                aggregated_signal: {
                  type: 'object',
                  description: 'Aggregated signal data to cache',
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds (default: 30)',
                  default: 30,
                },
              },
              required: ['signals_hash', 'aggregated_signal'],
            },
          },

          // Portfolio Metrics Operations
          {
            name: 'get_portfolio_metrics',
            description: 'Get cached portfolio performance metrics',
            inputSchema: {
              type: 'object',
              properties: {
                max_age_minutes: {
                  type: 'number',
                  description: 'Maximum age of cached metrics in minutes (default: 1)',
                  default: 1,
                },
              },
            },
          },
          {
            name: 'cache_portfolio_metrics',
            description: 'Cache portfolio metrics with timestamp validation',
            inputSchema: {
              type: 'object',
              properties: {
                metrics: {
                  type: 'object',
                  description: 'Portfolio metrics to cache',
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds (default: 60)',
                  default: 60,
                },
              },
              required: ['metrics'],
            },
          },

          // Kelly Statistics Operations
          {
            name: 'get_kelly_stats',
            description: 'Get cached Kelly Criterion statistics for a strategy',
            inputSchema: {
              type: 'object',
              properties: {
                strategy_name: {
                  type: 'string',
                  description: 'Name of the trading strategy',
                },
              },
              required: ['strategy_name'],
            },
          },
          {
            name: 'cache_kelly_stats',
            description: 'Cache Kelly statistics with long TTL',
            inputSchema: {
              type: 'object',
              properties: {
                strategy_name: {
                  type: 'string',
                  description: 'Strategy name for cache key',
                },
                kelly_stats: {
                  type: 'object',
                  description: 'Kelly statistics data to cache',
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds (default: 3600)',
                  default: 3600,
                },
              },
              required: ['strategy_name', 'kelly_stats'],
            },
          },

          // Volatility Operations
          {
            name: 'get_volatility_adjustment',
            description: 'Get cached volatility adjustment factors',
            inputSchema: {
              type: 'object',
              properties: {
                symbol: {
                  type: 'string',
                  description: 'Trading symbol for volatility cache',
                },
              },
              required: ['symbol'],
            },
          },
          {
            name: 'cache_volatility_adjustment',
            description: 'Cache volatility adjustment with medium TTL',
            inputSchema: {
              type: 'object',
              properties: {
                symbol: {
                  type: 'string',
                  description: 'Trading symbol',
                },
                adjustment_factor: {
                  type: 'number',
                  description: 'Volatility adjustment factor',
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds (default: 900)',
                  default: 900,
                },
              },
              required: ['symbol', 'adjustment_factor'],
            },
          },

          // Correlation Operations
          {
            name: 'get_correlation_matrix',
            description: 'Get cached strategy correlation matrix',
            inputSchema: {
              type: 'object',
              properties: {
                max_age_minutes: {
                  type: 'number',
                  description: 'Maximum age in minutes (default: 30)',
                  default: 30,
                },
              },
            },
          },
          {
            name: 'cache_correlation_matrix',
            description: 'Cache strategy correlation matrix',
            inputSchema: {
              type: 'object',
              properties: {
                correlation_matrix: {
                  type: 'object',
                  description: 'Strategy correlation matrix',
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds (default: 1800)',
                  default: 1800,
                },
              },
              required: ['correlation_matrix'],
            },
          },

          // Audit and Analytics Operations
          {
            name: 'cache_position_calculation',
            description: 'Cache position calculation for audit trail',
            inputSchema: {
              type: 'object',
              properties: {
                calculation_data: {
                  type: 'object',
                  description: 'Position calculation details for audit',
                },
                strategy_name: {
                  type: 'string',
                  description: 'Strategy name for grouping',
                },
              },
              required: ['calculation_data', 'strategy_name'],
            },
          },
          {
            name: 'get_calculation_history',
            description: 'Get recent position calculation history',
            inputSchema: {
              type: 'object',
              properties: {
                strategy_name: {
                  type: 'string',
                  description: 'Strategy name to filter by',
                },
                limit: {
                  type: 'number',
                  description: 'Number of recent calculations (default: 10)',
                  default: 10,
                },
              },
            },
          },

          // Utility Operations
          {
            name: 'clear_cache_by_pattern',
            description: 'Clear cache entries matching a pattern',
            inputSchema: {
              type: 'object',
              properties: {
                pattern: {
                  type: 'string',
                  description: 'Redis key pattern to clear (e.g., "ensemble:*")',
                },
              },
              required: ['pattern'],
            },
          },
          {
            name: 'get_cache_stats',
            description: 'Get cache statistics and health metrics',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
        ] as Tool[],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      // Type-safe argument extraction
      const getArg = (key: string): any => {
        return args && typeof args === 'object' && key in args ? (args as any)[key] : undefined;
      };

      try {
        switch (name) {
          case 'get_strategy_weights':
            return await this.getStrategyWeights(getArg('market_conditions_hash'));

          case 'cache_strategy_weights':
            return await this.cacheStrategyWeights(getArg('weights'), getArg('ttl'));

          case 'get_aggregated_signals':
            return await this.getAggregatedSignals(getArg('signals_hash'));

          case 'cache_aggregated_signals':
            return await this.cacheAggregatedSignals(
              getArg('signals_hash'),
              getArg('aggregated_signal'),
              getArg('ttl')
            );

          case 'get_portfolio_metrics':
            return await this.getPortfolioMetrics(getArg('max_age_minutes'));

          case 'cache_portfolio_metrics':
            return await this.cachePortfolioMetrics(getArg('metrics'), getArg('ttl'));

          case 'get_kelly_stats':
            return await this.getKellyStats(getArg('strategy_name'));

          case 'cache_kelly_stats':
            return await this.cacheKellyStats(
              getArg('strategy_name'),
              getArg('kelly_stats'),
              getArg('ttl')
            );

          case 'get_volatility_adjustment':
            return await this.getVolatilityAdjustment(getArg('symbol'));

          case 'cache_volatility_adjustment':
            return await this.cacheVolatilityAdjustment(
              getArg('symbol'),
              getArg('adjustment_factor'),
              getArg('ttl')
            );

          case 'get_correlation_matrix':
            return await this.getCorrelationMatrix(getArg('max_age_minutes'));

          case 'cache_correlation_matrix':
            return await this.cacheCorrelationMatrix(
              getArg('correlation_matrix'),
              getArg('ttl')
            );

          case 'cache_position_calculation':
            return await this.cachePositionCalculation(
              getArg('calculation_data'),
              getArg('strategy_name')
            );

          case 'get_calculation_history':
            return await this.getCalculationHistory(
              getArg('strategy_name'),
              getArg('limit')
            );

          case 'clear_cache_by_pattern':
            return await this.clearCacheByPattern(getArg('pattern'));

          case 'get_cache_stats':
            return await this.getCacheStats();

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error executing ${name}: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  // Strategy Weight Operations
  private async getStrategyWeights(marketConditionsHash?: string) {
    try {
      const cacheKey = marketConditionsHash 
        ? `${CACHE_KEYS.STRATEGY_WEIGHTS}:${marketConditionsHash}`
        : CACHE_KEYS.STRATEGY_WEIGHTS;
      
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        const data = JSON.parse(cached);
        const cacheTime = new Date(data.timestamp);
        const ageMinutes = (Date.now() - cacheTime.getTime()) / 60000;
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                found: true,
                weights: data.weights,
                cached_at: data.timestamp,
                age_minutes: ageMinutes.toFixed(2),
              }),
            },
          ],
        };
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({ found: false }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get strategy weights: ${error}`);
    }
  }

  private async cacheStrategyWeights(weights: any, ttl: number = 300) {
    try {
      // Validate weights structure
      const weightsWithMeta = {
        weights,
        timestamp: new Date().toISOString(),
      };
      
      const cacheKey = CACHE_KEYS.STRATEGY_WEIGHTS;
      await this.redis.setEx(cacheKey, ttl, JSON.stringify(weightsWithMeta));
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              cached_at: weightsWithMeta.timestamp,
              ttl_seconds: ttl,
              key: cacheKey,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache strategy weights: ${error}`);
    }
  }

  // Signal Aggregation Operations
  private async getAggregatedSignals(signalsHash: string) {
    try {
      const cacheKey = `${CACHE_KEYS.SIGNALS}:${signalsHash}`;
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        const signal = JSON.parse(cached);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                found: true,
                signal,
                cache_key: cacheKey,
              }),
            },
          ],
        };
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({ found: false }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get aggregated signals: ${error}`);
    }
  }

  private async cacheAggregatedSignals(
    signalsHash: string,
    aggregatedSignal: any,
    ttl: number = 30
  ) {
    try {
      // Validate signal structure
      AggregatedSignalSchema.parse(aggregatedSignal);
      
      const cacheKey = `${CACHE_KEYS.SIGNALS}:${signalsHash}`;
      await this.redis.setEx(cacheKey, ttl, JSON.stringify(aggregatedSignal));
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              cache_key: cacheKey,
              ttl_seconds: ttl,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache aggregated signals: ${error}`);
    }
  }

  // Portfolio Metrics Operations
  private async getPortfolioMetrics(maxAgeMinutes: number = 1) {
    try {
      const cached = await this.redis.get(CACHE_KEYS.METRICS);
      
      if (cached) {
        const data = JSON.parse(cached);
        const cacheTime = new Date(data.timestamp);
        const ageMinutes = (Date.now() - cacheTime.getTime()) / 60000;
        
        if (ageMinutes <= maxAgeMinutes) {
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  found: true,
                  metrics: data,
                  age_minutes: ageMinutes.toFixed(2),
                }),
              },
            ],
          };
        }
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({ found: false }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get portfolio metrics: ${error}`);
    }
  }

  private async cachePortfolioMetrics(metrics: any, ttl: number = 60) {
    try {
      // Validate metrics structure
      PortfolioMetricsSchema.parse(metrics);
      
      await this.redis.setEx(CACHE_KEYS.METRICS, ttl, JSON.stringify(metrics));
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              cached_at: metrics.timestamp,
              ttl_seconds: ttl,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache portfolio metrics: ${error}`);
    }
  }

  // Kelly Statistics Operations
  private async getKellyStats(strategyName: string) {
    try {
      const cacheKey = `${CACHE_KEYS.KELLY_STATS}:${strategyName}`;
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        const stats = JSON.parse(cached);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                found: true,
                kelly_stats: stats,
                strategy: strategyName,
              }),
            },
          ],
        };
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({ found: false }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get Kelly stats: ${error}`);
    }
  }

  private async cacheKellyStats(
    strategyName: string,
    kellyStats: any,
    ttl: number = 3600
  ) {
    try {
      // Validate Kelly stats structure
      KellyStatsSchema.parse(kellyStats);
      
      const cacheKey = `${CACHE_KEYS.KELLY_STATS}:${strategyName}`;
      await this.redis.setEx(cacheKey, ttl, JSON.stringify(kellyStats));
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              strategy: strategyName,
              ttl_seconds: ttl,
              cache_key: cacheKey,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache Kelly stats: ${error}`);
    }
  }

  // Volatility Operations
  private async getVolatilityAdjustment(symbol: string) {
    try {
      const cacheKey = `${CACHE_KEYS.VOLATILITY}:${symbol}`;
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        const adjustment = JSON.parse(cached);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                found: true,
                adjustment_factor: adjustment,
                symbol,
              }),
            },
          ],
        };
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({ found: false }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get volatility adjustment: ${error}`);
    }
  }

  private async cacheVolatilityAdjustment(
    symbol: string,
    adjustmentFactor: number,
    ttl: number = 900
  ) {
    try {
      const cacheKey = `${CACHE_KEYS.VOLATILITY}:${symbol}`;
      await this.redis.setEx(cacheKey, ttl, JSON.stringify(adjustmentFactor));
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              symbol,
              adjustment_factor: adjustmentFactor,
              ttl_seconds: ttl,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache volatility adjustment: ${error}`);
    }
  }

  // Correlation Operations
  private async getCorrelationMatrix(maxAgeMinutes: number = 30) {
    try {
      const cached = await this.redis.get(CACHE_KEYS.CORRELATION);
      
      if (cached) {
        const data = JSON.parse(cached);
        const cacheTime = new Date(data.timestamp);
        const ageMinutes = (Date.now() - cacheTime.getTime()) / 60000;
        
        if (ageMinutes <= maxAgeMinutes) {
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  found: true,
                  correlation_matrix: data.matrix,
                  age_minutes: ageMinutes.toFixed(2),
                }),
              },
            ],
          };
        }
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({ found: false }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get correlation matrix: ${error}`);
    }
  }

  private async cacheCorrelationMatrix(correlationMatrix: any, ttl: number = 1800) {
    try {
      const dataWithTimestamp = {
        matrix: correlationMatrix,
        timestamp: new Date().toISOString(),
      };
      
      await this.redis.setEx(
        CACHE_KEYS.CORRELATION,
        ttl,
        JSON.stringify(dataWithTimestamp)
      );
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              cached_at: dataWithTimestamp.timestamp,
              ttl_seconds: ttl,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache correlation matrix: ${error}`);
    }
  }

  // Audit Operations
  private async cachePositionCalculation(calculationData: any, strategyName: string) {
    try {
      const timestamp = new Date().toISOString();
      const calculationWithMeta = {
        ...calculationData,
        strategy: strategyName,
        timestamp,
      };
      
      // Store in a list for the strategy
      const listKey = `${CACHE_KEYS.POSITION_CALC}:${strategyName}`;
      await this.redis.lPush(listKey, JSON.stringify(calculationWithMeta));
      
      // Keep only last 100 calculations per strategy
      await this.redis.lTrim(listKey, 0, 99);
      
      // Set expiry on the list (24 hours)
      await this.redis.expire(listKey, 86400);
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              strategy: strategyName,
              timestamp,
              list_key: listKey,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to cache position calculation: ${error}`);
    }
  }

  private async getCalculationHistory(strategyName?: string, limit: number = 10) {
    try {
      if (strategyName) {
        const listKey = `${CACHE_KEYS.POSITION_CALC}:${strategyName}`;
        const calculations = await this.redis.lRange(listKey, 0, limit - 1);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                strategy: strategyName,
                calculations: calculations.map(calc => JSON.parse(calc)),
                count: calculations.length,
              }),
            },
          ],
        };
      } else {
        // Get all strategy calculation keys
        const pattern = `${CACHE_KEYS.POSITION_CALC}:*`;
        const keys = await this.redis.keys(pattern);
        
        const allCalculations: any[] = [];
        for (const key of keys) {
          const calculations = await this.redis.lRange(key, 0, limit - 1);
          allCalculations.push(
            ...calculations.map(calc => JSON.parse(calc))
          );
        }
        
        // Sort by timestamp and limit
        allCalculations.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                calculations: allCalculations.slice(0, limit),
                total_found: allCalculations.length,
              }),
            },
          ],
        };
      }
    } catch (error) {
      throw new Error(`Failed to get calculation history: ${error}`);
    }
  }

  // Utility Operations
  private async clearCacheByPattern(pattern: string) {
    try {
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(keys);
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              pattern,
              cleared_keys: keys.length,
              keys_cleared: keys,
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to clear cache by pattern: ${error}`);
    }
  }

  private async getCacheStats() {
    try {
      const info = await this.redis.info('memory');
      const dbSize = await this.redis.dbSize();
      
      // Get key counts by pattern
      const keyPatterns = Object.values(CACHE_KEYS);
      const keyStats: Record<string, number> = {};
      
      for (const pattern of keyPatterns) {
        const keys = await this.redis.keys(`${pattern}*`);
        keyStats[pattern] = keys.length;
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              redis_info: info,
              total_keys: dbSize,
              key_stats: keyStats,
              timestamp: new Date().toISOString(),
            }),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get cache stats: ${error}`);
    }
  }

  async run() {
    // Setup Redis client before starting the server
    await this.setupRedisClient();
    
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Redis Trading MCP server running on stdio');
  }
}

// Start the server
const server = new RedisTradingMCP();
server.run().catch(console.error);