import { test, expect } from '@playwright/test';

test.describe('Frontend Interactions Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login and navigate to trading page
    await page.goto('http://localhost:3000');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*\/trading/);
  });

  test('should toggle auto trading on/off', async ({ page }) => {
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    // Find auto trading toggle
    const autoTradingToggle = page.getByRole('checkbox', { name: /Auto Trading/ });
    await expect(autoTradingToggle).toBeVisible();
    
    // Check initial state (should be disabled)
    const initialState = await autoTradingToggle.isChecked();
    
    // Toggle auto trading
    await autoTradingToggle.click();
    
    // Verify state changed
    const newState = await autoTradingToggle.isChecked();
    expect(newState).toBe(!initialState);
    
    // Check for notification/alert
    if (newState) {
      await expect(page.getByText('Auto trading enabled')).toBeVisible({ timeout: 5000 });
    }
    
    // Toggle back
    await autoTradingToggle.click();
    const finalState = await autoTradingToggle.isChecked();
    expect(finalState).toBe(initialState);
  });

  test('should interact with Train ML Model button', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    const trainButton = page.getByRole('button', { name: 'Train ML Model' });
    await expect(trainButton).toBeVisible();
    
    // Click train button (may show error or loading state)
    await trainButton.click();
    
    // Button might become disabled or show loading state
    // This is expected behavior in test environment
  });

  test('should display trading tables correctly', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Verify Active Trades table
    await expect(page.getByText('Active Trades')).toBeVisible();
    const activeTradesTable = page.locator('table').first();
    await expect(activeTradesTable).toBeVisible();
    
    // Check table headers
    await expect(page.getByRole('columnheader', { name: 'Symbol' })).toBeVisible();
    await expect(page.getByRole('columnheader', { name: 'Side' })).toBeVisible();
    await expect(page.getByRole('columnheader', { name: 'Entry Price' })).toBeVisible();
    await expect(page.getByRole('columnheader', { name: 'Status' })).toBeVisible();
    await expect(page.getByRole('columnheader', { name: 'PnL' })).toBeVisible();
    
    // Verify Recent Trades table
    await expect(page.getByText('Recent Trades')).toBeVisible();
    const recentTradesTable = page.locator('table').nth(1);
    await expect(recentTradesTable).toBeVisible();
  });

  test('should show connection status correctly', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Check trading dashboard connection status
    const connectionStatus = page.getByText('Trading Dashboard');
    await expect(connectionStatus).toBeVisible();
    
    // Should show either connected or disconnected
    const statusElement = page.locator('text=/connected|disconnected/');
    await expect(statusElement).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Check for any error messages displayed
    const errorMessage = page.getByText(/Error:/);
    if (await errorMessage.isVisible()) {
      // Error messages should be clearly visible and informative
      await expect(errorMessage).toBeVisible();
      
      // Error text should contain meaningful information
      const errorText = await errorMessage.textContent();
      expect(errorText).toBeTruthy();
      expect(errorText?.length).toBeGreaterThan(10);
    }
  });

  test('should maintain UI state during interactions', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Test that UI elements remain stable during interactions
    const mainHeading = page.locator('h1', { hasText: 'Automated Trading Control' });
    await expect(mainHeading).toBeVisible();
    
    // Interact with toggle
    const autoTradingToggle = page.getByRole('checkbox', { name: /Auto Trading/ });
    if (await autoTradingToggle.isVisible()) {
      await autoTradingToggle.click();
      
      // Main UI should remain stable
      await expect(mainHeading).toBeVisible();
      await expect(page.getByText('Trading Dashboard')).toBeVisible();
      await expect(page.getByText('Active Trades')).toBeVisible();
    }
  });
});