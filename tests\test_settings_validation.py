"""
Test suite for configuration settings validation and environment handling.
This test identifies gaps in configuration management and edge cases.
"""
import pytest
import os
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_settings_with_missing_env_variables():
    """Test settings behavior when required environment variables are missing."""
    from app.config.settings import Settings
    from pydantic import ValidationError
    import tempfile
    import shutil
    from pathlib import Path
    
    # Save original env vars and .env file
    original_secret = os.environ.get('SECRET_KEY')
    project_root = Path(__file__).parent.parent
    env_file = project_root / '.env'
    
    # Create temporary backup if .env exists
    backup_path = None
    if env_file.exists():
        backup_path = tempfile.mktemp(suffix='.env.backup')
        shutil.copy2(env_file, backup_path)
        env_file.unlink()  # Remove .env file
    
    try:
        # Remove required env var
        if 'SECRET_KEY' in os.environ:
            del os.environ['SECRET_KEY']
        
        # This should fail since SECRET_KEY is required and no .env file exists
        with pytest.raises((ValidationError, ValueError)):
            Settings()
            
    finally:
        # Restore original env var
        if original_secret:
            os.environ['SECRET_KEY'] = original_secret
        
        # Restore .env file
        if backup_path and Path(backup_path).exists():
            shutil.copy2(backup_path, env_file)
            Path(backup_path).unlink()


def test_settings_validation_trading_parameters():
    """Test validation of trading parameter ranges and types."""
    from app.config.settings import Settings
    
    # Set minimal required env vars with secure length
    os.environ['SECRET_KEY'] = 'test_secret_key_12345_secure_32_chars_minimum'
    
    settings = Settings()
    
    # Test trading parameter validation
    assert settings.trading_symbol is not None
    assert settings.timeframe in ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d']
    assert settings.kline_limit > 0
    assert settings.portfolio_value > 0
    assert 0 <= settings.min_score_threshold <= 1


def test_settings_binance_api_configuration():
    """Test Binance API configuration settings."""
    from app.config.settings import Settings
    
    # Set minimal required env vars with secure length
    os.environ['SECRET_KEY'] = 'test_secret_key_12345_secure_32_chars_minimum'
    
    settings = Settings()
    
    # Test Binance configuration
    assert hasattr(settings, 'binance_api_key')
    assert hasattr(settings, 'binance_api_secret')
    assert hasattr(settings, 'use_testnet')
    assert isinstance(settings.use_testnet, bool)


def test_settings_ml_configuration():
    """Test ML-related configuration settings."""
    from app.config.settings import Settings
    
    # Set minimal required env vars with secure length
    os.environ['SECRET_KEY'] = 'test_secret_key_12345_secure_32_chars_minimum'
    
    settings = Settings()
    
    # Test ML configuration
    assert hasattr(settings, 'ml_weight_optimization_enabled')
    assert hasattr(settings, 'ml_model_path')
    assert hasattr(settings, 'ml_training_interval_hours')
    assert settings.ml_training_interval_hours > 0
    assert settings.ml_lookback_days > 0
    assert settings.ml_window_size > 0


def test_strategy_parameters_validation():
    """Test strategy-specific parameter validation."""
    from app.config.settings import Settings
    
    # Set minimal required env vars with secure length
    os.environ['SECRET_KEY'] = 'test_secret_key_12345_secure_32_chars_minimum'
    
    settings = Settings()
    
    # Grid strategy parameters
    assert settings.grid_levels > 0
    assert 0 < settings.grid_range < 1
    assert settings.min_level_distance > 0
    
    # Technical analysis parameters
    assert settings.ta_rsi_period > 0
    assert 0 < settings.ta_rsi_oversold < 50
    assert 50 < settings.ta_rsi_overbought < 100
    
    # Trend following parameters
    assert settings.tf_adx_period > 0
    assert settings.tf_adx_threshold > 0


def test_jwt_configuration():
    """Test JWT authentication configuration."""
    from app.config.settings import Settings
    
    # Set minimal required env vars with secure length
    secure_key = 'test_secret_key_12345_secure_32_chars_minimum'
    os.environ['SECRET_KEY'] = secure_key
    
    settings = Settings()
    
    # Test JWT settings
    assert settings.secret_key is not None
    assert len(settings.secret_key) >= 32  # Minimum secure length
    assert settings.algorithm == 'HS256'
    assert settings.access_token_expire_minutes > 0
    assert settings.refresh_token_expire_days > 0


def test_jwt_configuration_weak_key():
    """Test JWT configuration rejects weak secret keys."""
    from app.config.settings import Settings
    from pydantic import ValidationError
    
    # Set weak secret key
    os.environ['SECRET_KEY'] = 'weak_key'
    
    with pytest.raises(ValidationError) as exc_info:
        Settings()
    
    assert "at least 32 characters" in str(exc_info.value)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])