# Refactoring Plan: Crypto Auto Trader Core

**Objective:** Strip the project down to its essential core: the three primary trading strategies, the automatic strategy selection logic, and the dashboard component exclusively for enabling/disabling automated trading.

## Core Components Identified:

1.  **Strategies:**
    *   `app/strategies/grid_strategy.py`
    *   `app/strategies/technical_analysis_strategy.py`
    *   `app/strategies/trend_following_strategy.py`
    *   `app/strategies/base_strategy.py`
2.  **Selection Logic:**
    *   `app/strategies/strategy_selector.py`
3.  **Dashboard Toggle:**
    *   Logic within `app/dashboard/frontend/src/pages/Trading.tsx` (specifically the `Switch` component lines 730-734, state `autoTradingEnabled` line 90, handler `handleToggleAutoTrading` lines 157-172).

## Proposed Plan:

### Phase 1: Identification & Dependency Analysis

1.  **Confirm Core Backend Files:** Verify roles of `base_strategy.py` and `strategy_selector.py`.
2.  **Trace Backend Dependencies:** Analyze imports in core strategy/selector files to identify necessary modules from `app/services/`, `app/utils/`, `app/models/`, `app/config/`, and the required API client (e.g., Binance). Pinpoint essential API endpoints (get status, enable, disable auto-trade).
3.  **Trace Frontend Dependencies:** Analyze imports in `Trading.tsx` to identify minimal components/hooks/services for the toggle (`Switch`, state, API calls, minimal layout/auth).
4.  **Identify Essential Config & Root Files:** Determine necessary config (`config.py`, `.env`), root (`main.py`, `requirements.txt`, `Dockerfile` if kept), and frontend build files (`package.json`, etc.).

### Phase 2: Code Stripping & Deletion

1.  **Backend Refactoring:** Delete non-essential files/dirs in `app/`. Remove unused code within retained files. Refactor API endpoints.
2.  **Frontend Refactoring:** Delete non-essential files/dirs in `app/dashboard/frontend/src/`. Heavily refactor `Trading.tsx` to *only* contain the toggle and its dependencies, renaming it (e.g., `AutoTradeControl.tsx`). Refactor `api.ts` and routing (`App.tsx`).
3.  **Root & Configuration Cleanup:** Delete non-essential root files/dirs (`docs/`, `tests/`, etc.). Remove unused dependencies (`requirements.txt`, `package.json`). Update `Dockerfile` (if kept). Clean config files.

### Phase 3: Verification

1.  **Build & Run:** Ensure the stripped application builds and runs.
2.  **Core Functionality Test:** Verify strategy selection logic works. Test the frontend toggle (fetch status, call API on click, update state).

## Visual Plan (Mermaid Diagram):

```mermaid
graph TD
    subgraph Core Backend
        A[Strategy Selector (strategy_selector.py)] --> B(Grid Strategy);
        A --> C(Technical Analysis Strategy);
        A --> D(Trend Following Strategy);
        B --> E{Base Strategy};
        C --> E;
        D --> E;
        A --> F[Market Data Service];
        A --> G[Config Service];
        B --> H[Order Execution Service];
        C --> H;
        D --> H;
        F --> I[API Client (e.g., Binance)];
        H --> I;
        J[API Endpoints (FastAPI)] --> A;
        J --> K(Auto-Trade Status);
        J --> L(Enable Auto-Trade);
        J --> M(Disable Auto-Trade);
    end

    subgraph Core Frontend (React)
        N[AutoTradeControl.tsx] --> O{Switch Component};
        N --> P[API Service (api.ts)];
        P --> K;
        P --> L;
        P --> M;
    end

    J --> P;

    style K fill:#f9f,stroke:#333,stroke-width:2px
    style L fill:#f9f,stroke:#333,stroke-width:2px
    style M fill:#f9f,stroke:#333,stroke-width:2px
    style O fill:#ccf,stroke:#333,stroke-width:2px