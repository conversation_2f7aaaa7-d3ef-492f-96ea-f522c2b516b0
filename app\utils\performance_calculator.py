"""
Advanced Performance Calculator for Portfolio Performance Analysis
Implements Sharpe ratio, Sortino ratio, Information ratio, Alpha/Beta, and attribution analysis
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
import redis.asyncio as redis
import logging
from scipy import stats

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    information_ratio: float
    alpha: float
    beta: float
    tracking_error: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    recovery_factor: float
    var_95: float
    var_99: float
    skewness: float
    kurtosis: float
    timestamp: datetime

@dataclass
class AttributionMetrics:
    """Performance attribution metrics"""
    strategy_name: str
    total_contribution: float
    active_return: float
    selection_effect: float
    interaction_effect: float
    allocation_effect: float
    weight: float
    return_contribution: float
    risk_contribution: float
    
@dataclass
class RollingMetrics:
    """Rolling performance metrics"""
    period_days: int
    rolling_sharpe: List[float]
    rolling_volatility: List[float]
    rolling_returns: List[float]
    rolling_max_drawdown: List[float]
    timestamps: List[str]

class PerformanceCalculator:
    """
    Advanced performance calculator with comprehensive portfolio analysis
    """
    
    def __init__(
        self,
        redis_client: redis.Redis,
        risk_free_rate: float = 0.02,  # 2% annual risk-free rate
        benchmark_symbol: str = "BTC",  # Default benchmark
        rolling_window: int = 30  # 30-day rolling window
    ):
        self.redis = redis_client
        self.risk_free_rate = risk_free_rate
        self.benchmark_symbol = benchmark_symbol
        self.rolling_window = rolling_window
        
        # Cache keys
        self.PERFORMANCE_CACHE = "performance:portfolio_metrics"
        self.RETURNS_CACHE = "performance:returns_history"
        self.BENCHMARK_CACHE = "performance:benchmark_returns"
        self.ATTRIBUTION_CACHE = "performance:attribution"
        self.ROLLING_CACHE = "performance:rolling_metrics"
        
    async def calculate_performance_metrics(
        self,
        portfolio_returns: List[float],
        benchmark_returns: Optional[List[float]] = None,
        positions: Optional[Dict[str, float]] = None
    ) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics for portfolio
        """
        try:
            # Check cache first
            cache_key = f"{self.PERFORMANCE_CACHE}:{hash(str(portfolio_returns[-10:]))}"
            cached_metrics = await self.redis.get(cache_key)
            
            if cached_metrics:
                cached_data = json.loads(cached_metrics)
                cache_time = datetime.fromisoformat(cached_data['timestamp'])
                
                # Use cached if less than 5 minutes old
                if datetime.now() - cache_time < timedelta(minutes=5):
                    return PerformanceMetrics(**cached_data)
            
            if len(portfolio_returns) < 10:
                return self._get_default_performance_metrics()
            
            # Get benchmark returns if not provided
            if benchmark_returns is None:
                benchmark_returns = await self._get_benchmark_returns(len(portfolio_returns))
            
            # Ensure same length
            min_length = min(len(portfolio_returns), len(benchmark_returns))
            portfolio_returns = portfolio_returns[:min_length]
            benchmark_returns = benchmark_returns[:min_length]
            
            returns_array = np.array(portfolio_returns)
            benchmark_array = np.array(benchmark_returns)
            
            # Basic return metrics
            total_return = np.prod(1 + returns_array) - 1
            annualized_return = (1 + total_return) ** (252 / len(returns_array)) - 1
            
            # Risk metrics
            volatility = np.std(returns_array) * np.sqrt(252)  # Annualized
            
            # Risk-adjusted metrics
            sharpe_ratio = self._calculate_sharpe_ratio(returns_array)
            sortino_ratio = self._calculate_sortino_ratio(returns_array)
            information_ratio = self._calculate_information_ratio(returns_array, benchmark_array)
            
            # Alpha and Beta
            alpha, beta = self._calculate_alpha_beta(returns_array, benchmark_array)
            
            # Tracking error
            tracking_error = np.std(returns_array - benchmark_array) * np.sqrt(252)
            
            # Drawdown metrics
            max_drawdown = self._calculate_max_drawdown(returns_array)
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Win/Loss metrics
            win_rate, avg_win, avg_loss = self._calculate_win_loss_metrics(returns_array)
            profit_factor = (win_rate * avg_win) / ((1 - win_rate) * abs(avg_loss)) if avg_loss != 0 else 0
            
            # Recovery factor
            recovery_factor = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # VaR metrics
            var_95 = -np.percentile(returns_array, 5)
            var_99 = -np.percentile(returns_array, 1)
            
            # Distribution metrics
            skewness = stats.skew(returns_array)
            kurtosis = stats.kurtosis(returns_array)
            
            performance_metrics = PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                information_ratio=information_ratio,
                alpha=alpha,
                beta=beta,
                tracking_error=tracking_error,
                max_drawdown=max_drawdown,
                calmar_ratio=calmar_ratio,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                recovery_factor=recovery_factor,
                var_95=var_95,
                var_99=var_99,
                skewness=skewness,
                kurtosis=kurtosis,
                timestamp=datetime.now()
            )
            
            # Cache the results
            await self._cache_performance_metrics(cache_key, performance_metrics)
            
            return performance_metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return self._get_default_performance_metrics()
    
    async def calculate_attribution_analysis(
        self,
        strategy_returns: Dict[str, List[float]],
        strategy_weights: Dict[str, List[float]],
        benchmark_returns: Optional[List[float]] = None
    ) -> List[AttributionMetrics]:
        """
        Calculate performance attribution analysis
        """
        try:
            attribution_metrics = []
            
            if benchmark_returns is None:
                benchmark_returns = await self._get_benchmark_returns(100)
            
            # Calculate portfolio return
            portfolio_returns = self._calculate_weighted_returns(strategy_returns, strategy_weights)
            benchmark_array = np.array(benchmark_returns[:len(portfolio_returns)])
            portfolio_array = np.array(portfolio_returns)
            
            # Calculate active return
            active_returns = portfolio_array - benchmark_array
            total_active_return = np.sum(active_returns)
            
            for strategy_name in strategy_returns.keys():
                if strategy_name not in strategy_weights:
                    continue
                
                strategy_ret = np.array(strategy_returns[strategy_name])
                strategy_wgt = np.array(strategy_weights[strategy_name])
                
                # Ensure same length
                min_length = min(len(strategy_ret), len(strategy_wgt), len(benchmark_array))
                strategy_ret = strategy_ret[:min_length]
                strategy_wgt = strategy_wgt[:min_length]
                bench_ret = benchmark_array[:min_length]
                
                # Calculate attribution components
                avg_weight = np.mean(strategy_wgt)
                
                # Selection effect: (strategy_return - benchmark_return) * avg_weight
                selection_effect = np.sum((strategy_ret - bench_ret) * avg_weight)
                
                # Allocation effect: (weight - benchmark_weight) * benchmark_return
                # Assuming equal benchmark weights
                benchmark_weight = 1.0 / len(strategy_returns)
                allocation_effect = np.sum((strategy_wgt - benchmark_weight) * bench_ret)
                
                # Interaction effect
                interaction_effect = np.sum((strategy_wgt - benchmark_weight) * (strategy_ret - bench_ret))
                
                # Total contribution
                total_contribution = selection_effect + allocation_effect + interaction_effect
                
                # Return contribution
                return_contribution = np.sum(strategy_ret * strategy_wgt)
                
                # Risk contribution (simplified)
                strategy_vol = np.std(strategy_ret) * np.sqrt(252)
                portfolio_vol = np.std(portfolio_array) * np.sqrt(252)
                risk_contribution = (strategy_vol * avg_weight) / portfolio_vol if portfolio_vol > 0 else 0
                
                attribution = AttributionMetrics(
                    strategy_name=strategy_name,
                    total_contribution=total_contribution,
                    active_return=np.sum(strategy_ret - bench_ret),
                    selection_effect=selection_effect,
                    interaction_effect=interaction_effect,
                    allocation_effect=allocation_effect,
                    weight=avg_weight,
                    return_contribution=return_contribution,
                    risk_contribution=risk_contribution
                )
                
                attribution_metrics.append(attribution)
            
            # Sort by total contribution
            attribution_metrics.sort(key=lambda x: x.total_contribution, reverse=True)
            
            return attribution_metrics
            
        except Exception as e:
            logger.error(f"Error calculating attribution analysis: {e}")
            return []
    
    async def calculate_rolling_metrics(
        self,
        portfolio_returns: List[float],
        window_size: Optional[int] = None
    ) -> RollingMetrics:
        """
        Calculate rolling performance metrics
        """
        try:
            if window_size is None:
                window_size = self.rolling_window
            
            if len(portfolio_returns) < window_size:
                return RollingMetrics(
                    period_days=window_size,
                    rolling_sharpe=[],
                    rolling_volatility=[],
                    rolling_returns=[],
                    rolling_max_drawdown=[],
                    timestamps=[]
                )
            
            returns_array = np.array(portfolio_returns)
            rolling_sharpe = []
            rolling_volatility = []
            rolling_returns = []
            rolling_max_drawdown = []
            timestamps = []
            
            for i in range(window_size, len(returns_array) + 1):
                window_returns = returns_array[i-window_size:i]
                
                # Calculate metrics for this window
                window_vol = np.std(window_returns) * np.sqrt(252)
                window_return = np.mean(window_returns) * 252
                window_sharpe = (window_return - self.risk_free_rate) / window_vol if window_vol > 0 else 0
                window_drawdown = self._calculate_max_drawdown(window_returns)
                
                rolling_volatility.append(window_vol)
                rolling_returns.append(window_return)
                rolling_sharpe.append(window_sharpe)
                rolling_max_drawdown.append(window_drawdown)
                timestamps.append((datetime.now() - timedelta(days=len(returns_array)-i)).isoformat())
            
            return RollingMetrics(
                period_days=window_size,
                rolling_sharpe=rolling_sharpe,
                rolling_volatility=rolling_volatility,
                rolling_returns=rolling_returns,
                rolling_max_drawdown=rolling_max_drawdown,
                timestamps=timestamps
            )
            
        except Exception as e:
            logger.error(f"Error calculating rolling metrics: {e}")
            return RollingMetrics(
                period_days=window_size or self.rolling_window,
                rolling_sharpe=[],
                rolling_volatility=[],
                rolling_returns=[],
                rolling_max_drawdown=[],
                timestamps=[]
            )
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) == 0:
            return 0.0
        
        mean_return = np.mean(returns) * 252  # Annualized
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        
        if volatility == 0:
            return 0.0
        
        return (mean_return - self.risk_free_rate) / volatility
    
    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sortino ratio (downside deviation)"""
        if len(returns) == 0:
            return 0.0
        
        mean_return = np.mean(returns) * 252  # Annualized
        
        # Calculate downside deviation
        downside_returns = returns[returns < 0]
        if len(downside_returns) == 0:
            return float('inf') if mean_return > self.risk_free_rate else 0.0
        
        downside_deviation = np.std(downside_returns) * np.sqrt(252)
        
        if downside_deviation == 0:
            return 0.0
        
        return (mean_return - self.risk_free_rate) / downside_deviation
    
    def _calculate_information_ratio(self, portfolio_returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
        """Calculate Information ratio"""
        if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
            return 0.0
        
        active_returns = portfolio_returns - benchmark_returns
        tracking_error = np.std(active_returns) * np.sqrt(252)
        
        if tracking_error == 0:
            return 0.0
        
        active_return = np.mean(active_returns) * 252  # Annualized
        
        return active_return / tracking_error
    
    def _calculate_alpha_beta(self, portfolio_returns: np.ndarray, benchmark_returns: np.ndarray) -> Tuple[float, float]:
        """Calculate alpha and beta vs benchmark"""
        if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) < 2:
            return 0.0, 1.0
        
        try:
            # Calculate beta using linear regression
            covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            
            if benchmark_variance == 0:
                beta = 1.0
            else:
                beta = covariance / benchmark_variance
            
            # Calculate alpha
            portfolio_mean = np.mean(portfolio_returns) * 252  # Annualized
            benchmark_mean = np.mean(benchmark_returns) * 252  # Annualized
            
            alpha = portfolio_mean - (self.risk_free_rate + beta * (benchmark_mean - self.risk_free_rate))
            
            return alpha, beta
            
        except Exception as e:
            logger.error(f"Error calculating alpha/beta: {e}")
            return 0.0, 1.0
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calculate maximum drawdown"""
        if len(returns) == 0:
            return 0.0
        
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / running_max
        
        return np.min(drawdowns)
    
    def _calculate_win_loss_metrics(self, returns: np.ndarray) -> Tuple[float, float, float]:
        """Calculate win rate, average win, average loss"""
        if len(returns) == 0:
            return 0.0, 0.0, 0.0
        
        wins = returns[returns > 0]
        losses = returns[returns < 0]
        
        win_rate = len(wins) / len(returns)
        avg_win = np.mean(wins) if len(wins) > 0 else 0.0
        avg_loss = np.mean(losses) if len(losses) > 0 else 0.0
        
        return win_rate, avg_win, avg_loss
    
    def _calculate_weighted_returns(
        self,
        strategy_returns: Dict[str, List[float]],
        strategy_weights: Dict[str, List[float]]
    ) -> List[float]:
        """Calculate portfolio returns from strategy returns and weights"""
        if not strategy_returns or not strategy_weights:
            return []
        
        # Find minimum length
        min_length = float('inf')
        for strategy in strategy_returns:
            if strategy in strategy_weights:
                min_length = min(min_length, len(strategy_returns[strategy]), len(strategy_weights[strategy]))
        
        if min_length == float('inf') or min_length == 0:
            return []
        
        portfolio_returns = []
        
        for i in range(int(min_length)):
            period_return = 0.0
            for strategy in strategy_returns:
                if strategy in strategy_weights and i < len(strategy_returns[strategy]) and i < len(strategy_weights[strategy]):
                    period_return += strategy_weights[strategy][i] * strategy_returns[strategy][i]
            
            portfolio_returns.append(period_return)
        
        return portfolio_returns
    
    async def _get_benchmark_returns(self, length: int) -> List[float]:
        """Get benchmark returns"""
        try:
            cache_key = f"{self.BENCHMARK_CACHE}:{self.benchmark_symbol}"
            cached_returns = await self.redis.get(cache_key)
            
            if cached_returns:
                benchmark_returns = json.loads(cached_returns)
                return benchmark_returns[:length]
            
            # Generate synthetic benchmark returns (BTC-like)
            np.random.seed(42)
            benchmark_returns = np.random.normal(0.0008, 0.025, max(length, 252))
            
            # Cache for 30 minutes
            await self.redis.setex(cache_key, 1800, json.dumps(benchmark_returns.tolist()))
            
            return benchmark_returns[:length].tolist()
            
        except Exception as e:
            logger.error(f"Error getting benchmark returns: {e}")
            return [0.0] * length
    
    async def _cache_performance_metrics(self, cache_key: str, metrics: PerformanceMetrics):
        """Cache performance metrics"""
        try:
            cache_data = asdict(metrics)
            cache_data['timestamp'] = metrics.timestamp.isoformat()
            
            await self.redis.setex(cache_key, 300, json.dumps(cache_data))  # 5 minutes
            
        except Exception as e:
            logger.error(f"Error caching performance metrics: {e}")
    
    def _get_default_performance_metrics(self) -> PerformanceMetrics:
        """Return default performance metrics when calculation fails"""
        return PerformanceMetrics(
            total_return=0.0,
            annualized_return=0.0,
            volatility=0.25,
            sharpe_ratio=0.0,
            sortino_ratio=0.0,
            information_ratio=0.0,
            alpha=0.0,
            beta=1.0,
            tracking_error=0.0,
            max_drawdown=0.0,
            calmar_ratio=0.0,
            win_rate=0.5,
            avg_win=0.01,
            avg_loss=-0.01,
            profit_factor=1.0,
            recovery_factor=0.0,
            var_95=0.02,
            var_99=0.035,
            skewness=0.0,
            kurtosis=0.0,
            timestamp=datetime.now()
        )
    
    async def generate_performance_report(
        self,
        portfolio_returns: List[float],
        strategy_returns: Optional[Dict[str, List[float]]] = None,
        strategy_weights: Optional[Dict[str, List[float]]] = None
    ) -> Dict[str, any]:
        """
        Generate comprehensive performance report
        """
        try:
            # Calculate main performance metrics
            performance_metrics = await self.calculate_performance_metrics(portfolio_returns)
            
            # Calculate rolling metrics
            rolling_metrics = await self.calculate_rolling_metrics(portfolio_returns)
            
            # Calculate attribution if strategy data provided
            attribution_metrics = []
            if strategy_returns and strategy_weights:
                attribution_metrics = await self.calculate_attribution_analysis(
                    strategy_returns, strategy_weights
                )
            
            # Create comprehensive report
            report = {
                "performance_summary": asdict(performance_metrics),
                "rolling_analysis": asdict(rolling_metrics),
                "attribution_analysis": [asdict(attr) for attr in attribution_metrics],
                "risk_analysis": {
                    "var_95_confidence": f"{performance_metrics.var_95:.2%}",
                    "var_99_confidence": f"{performance_metrics.var_99:.2%}",
                    "max_drawdown": f"{performance_metrics.max_drawdown:.2%}",
                    "volatility": f"{performance_metrics.volatility:.2%}",
                    "beta": f"{performance_metrics.beta:.2f}",
                    "tracking_error": f"{performance_metrics.tracking_error:.2%}"
                },
                "return_analysis": {
                    "total_return": f"{performance_metrics.total_return:.2%}",
                    "annualized_return": f"{performance_metrics.annualized_return:.2%}",
                    "sharpe_ratio": f"{performance_metrics.sharpe_ratio:.2f}",
                    "sortino_ratio": f"{performance_metrics.sortino_ratio:.2f}",
                    "information_ratio": f"{performance_metrics.information_ratio:.2f}",
                    "alpha": f"{performance_metrics.alpha:.2%}"
                },
                "trade_analysis": {
                    "win_rate": f"{performance_metrics.win_rate:.2%}",
                    "avg_win": f"{performance_metrics.avg_win:.2%}",
                    "avg_loss": f"{performance_metrics.avg_loss:.2%}",
                    "profit_factor": f"{performance_metrics.profit_factor:.2f}"
                },
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "data_points": len(portfolio_returns),
                    "analysis_period_days": len(portfolio_returns),
                    "benchmark": self.benchmark_symbol
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {
                "error": str(e),
                "generated_at": datetime.now().isoformat()
            }