#!/usr/bin/env python3
"""
Quick Analytics Integration Test
Tests core functionality without hanging operations.
"""

import asyncio
import os
import sys
from datetime import datetime

# Add project root to path  
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_supabase_service():
    """Test Supabase service basic functionality."""
    print("🔍 Testing Supabase Service...")
    
    try:
        from app.services.mcp.supabase_service import SupabaseService
        
        # Test service initialization
        service = SupabaseService()
        print("✅ Supabase service initialized")
        
        # Test basic data storage (should work with mock)
        test_metrics = {
            "portfolio_value": 100000.0,
            "total_pnl": 5000.0,
            "timestamp": datetime.now().isoformat()
        }
        
        result_id = await service.store_portfolio_metrics(test_metrics)
        print(f"✅ Portfolio metrics stored with ID: {result_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Supabase service test failed: {e}")
        return False

async def test_real_time_analytics_basic():
    """Test real-time analytics service basic initialization."""
    print("\n📊 Testing Real-time Analytics Service (Basic)...")
    
    try:
        from app.services.mcp.supabase_service import SupabaseService
        from app.services.mcp.supabase_realtime_analytics import SupabaseRealTimeAnalytics
        
        # Initialize services
        supabase_service = SupabaseService()
        analytics = SupabaseRealTimeAnalytics(supabase_service)
        
        print("✅ Real-time analytics service initialized")
        
        # Test basic functionality without database-dependent operations
        print(f"⚠️ Alert rules configured: {len(analytics.alert_rules)}")
        
        # Test subscription mechanism
        def test_callback(data):
            print(f"📢 Callback received: {type(data)}")
        
        analytics.subscribe_to_updates("test_event", "test_subscriber", test_callback)
        print("✅ Subscription mechanism working")
        
        return True
        
    except Exception as e:
        print(f"❌ Real-time analytics test failed: {e}")
        return False

async def test_websocket_integration():
    """Test WebSocket integration components."""
    print("\n🔌 Testing WebSocket Integration...")
    
    try:
        from app.dashboard.api.analytics_websocket import get_analytics_status, is_analytics_running
        
        # Test status functions
        print(f"🔍 Analytics running: {is_analytics_running()}")
        
        status = await get_analytics_status()
        print(f"📊 Analytics status: {status}")
        
        print("✅ WebSocket integration components working")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket integration test failed: {e}")
        return False

async def test_performance_metrics():
    """Test performance metrics dataclass."""
    print("\n⚡ Testing Performance Metrics...")
    
    try:
        from app.services.mcp.supabase_realtime_analytics import RealTimeMetrics
        from datetime import datetime
        
        # Create test metrics
        metrics = RealTimeMetrics(
            timestamp=datetime.now(),
            portfolio_value=105000.0,
            total_pnl=5000.0,
            hourly_pnl=250.0,
            daily_pnl=500.0,
            sharpe_ratio=1.5,
            max_drawdown=-0.05,
            win_rate=0.67,
            active_positions=3,
            cache_hit_rate=0.87,
            avg_execution_time_ms=145.0,
            strategy_weights={"momentum": 0.4, "mean_reversion": 0.3, "volatility": 0.3},
            strategy_pnl={"momentum": 2000.0, "mean_reversion": 1500.0, "volatility": 1500.0},
            correlation_matrix={"momentum": {"mean_reversion": -0.2, "volatility": 0.1}},
            alerts_count=2,
            market_regime="trending"
        )
        
        print("✅ RealTimeMetrics dataclass working")
        print(f"📊 Portfolio Value: ${metrics.portfolio_value:,.2f}")
        print(f"💰 Total PnL: ${metrics.total_pnl:,.2f}")
        print(f"📈 Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
        print(f"⚡ Execution Time: {metrics.avg_execution_time_ms}ms")
        print(f"🎯 Cache Hit Rate: {metrics.cache_hit_rate*100:.1f}%")
        print(f"📍 Active Positions: {metrics.active_positions}")
        print(f"🌍 Market Regime: {metrics.market_regime}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance metrics test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Quick Analytics Integration Tests")
    print("=" * 60)
    
    tests = [
        ("Supabase Service", test_supabase_service),
        ("Real-time Analytics (Basic)", test_real_time_analytics_basic),
        ("WebSocket Integration", test_websocket_integration),
        ("Performance Metrics", test_performance_metrics)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(tests)} tests, {passed} passed, {failed} failed")
    
    if failed > 0:
        print(f"\n⚠️ {failed} test(s) failed. Check the output above for details.")
    else:
        print("\n🎉 All tests passed!")

if __name__ == "__main__":
    asyncio.run(main())