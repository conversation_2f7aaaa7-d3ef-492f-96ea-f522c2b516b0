"""
Enhanced Volatility Calculator for Dynamic Position Sizing
Implements EWMA, regime detection, and cross-validated volatility estimation
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import redis.asyncio as redis
import logging

logger = logging.getLogger(__name__)

@dataclass
class VolatilityMetrics:
    """Comprehensive volatility metrics"""
    ewma_volatility: float
    realized_volatility: float
    garch_volatility: float
    regime_volatility: float
    confidence_level: float
    regime: str  # 'low', 'medium', 'high'
    timestamp: datetime

@dataclass
class MarketRegime:
    """Market regime classification"""
    regime_type: str
    volatility_level: float
    persistence: float
    transition_probability: float

class VolatilityCalculator:
    """
    Advanced volatility calculator with multiple models and real-time caching
    """
    
    def __init__(
        self,
        redis_client: redis.Redis,
        ewma_alpha: float = 0.94,
        lookback_window: int = 252,
        regime_threshold_low: float = 0.15,
        regime_threshold_high: float = 0.35
    ):
        self.redis = redis_client
        self.ewma_alpha = ewma_alpha
        self.lookback_window = lookback_window
        self.regime_threshold_low = regime_threshold_low
        self.regime_threshold_high = regime_threshold_high
        
        # Cache keys
        self.VOLATILITY_CACHE_KEY = "volatility:metrics"
        self.PRICE_HISTORY_KEY = "volatility:price_history"
        self.REGIME_CACHE_KEY = "volatility:regime"
        
    async def calculate_comprehensive_volatility(
        self,
        symbol: str,
        price_data: Optional[List[float]] = None
    ) -> VolatilityMetrics:
        """
        Calculate comprehensive volatility metrics with caching
        """
        try:
            # Check cache first
            cache_key = f"{self.VOLATILITY_CACHE_KEY}:{symbol}"
            cached_metrics = await self.redis.get(cache_key)
            
            if cached_metrics:
                cached_data = json.loads(cached_metrics)
                cache_time = datetime.fromisoformat(cached_data['timestamp'])
                
                # Use cached if less than 5 minutes old
                if datetime.now() - cache_time < timedelta(minutes=5):
                    return VolatilityMetrics(**cached_data)
            
            # Get price data if not provided
            if price_data is None:
                price_data = await self._get_price_history(symbol)
            
            if len(price_data) < 20:
                # Not enough data, return default
                return self._get_default_volatility_metrics()
            
            # Calculate returns
            returns = np.diff(np.log(price_data))
            
            # Calculate different volatility measures
            ewma_vol = self._calculate_ewma_volatility(returns)
            realized_vol = self._calculate_realized_volatility(returns)
            garch_vol = self._calculate_garch_volatility(returns)
            
            # Determine market regime
            regime = await self._determine_market_regime(returns, symbol)
            regime_vol = self._get_regime_volatility(regime.regime_type)
            
            # Calculate confidence based on data quality
            confidence = self._calculate_confidence(returns, regime.persistence)
            
            # Create comprehensive metrics
            volatility_metrics = VolatilityMetrics(
                ewma_volatility=ewma_vol,
                realized_volatility=realized_vol,
                garch_volatility=garch_vol,
                regime_volatility=regime_vol,
                confidence_level=confidence,
                regime=regime.regime_type,
                timestamp=datetime.now()
            )
            
            # Cache the results
            await self._cache_volatility_metrics(symbol, volatility_metrics)
            
            return volatility_metrics
            
        except Exception as e:
            logger.error(f"Error calculating volatility for {symbol}: {e}")
            return self._get_default_volatility_metrics()
    
    def _calculate_ewma_volatility(self, returns: np.ndarray) -> float:
        """Calculate EWMA volatility"""
        if len(returns) < 2:
            return 0.2  # Default volatility
        
        # Initialize with first return squared
        ewma_var = returns[0] ** 2
        
        # Apply EWMA formula
        for i in range(1, len(returns)):
            ewma_var = (1 - self.ewma_alpha) * (returns[i] ** 2) + self.ewma_alpha * ewma_var
        
        return np.sqrt(ewma_var * 252)  # Annualized volatility
    
    def _calculate_realized_volatility(self, returns: np.ndarray) -> float:
        """Calculate realized volatility (standard deviation)"""
        if len(returns) < 2:
            return 0.2
        
        return np.std(returns) * np.sqrt(252)  # Annualized
    
    def _calculate_garch_volatility(self, returns: np.ndarray) -> float:
        """
        Simplified GARCH(1,1) volatility estimation
        """
        if len(returns) < 10:
            return self._calculate_realized_volatility(returns)
        
        # Simplified GARCH parameters (in practice, use MLE estimation)
        omega = 0.0001
        alpha = 0.05
        beta = 0.9
        
        # Initialize variance
        var = np.var(returns)
        
        # Apply GARCH formula for last 50 observations
        recent_returns = returns[-50:] if len(returns) > 50 else returns
        
        for ret in recent_returns:
            var = omega + alpha * (ret ** 2) + beta * var
        
        return np.sqrt(var * 252)  # Annualized
    
    async def _determine_market_regime(self, returns: np.ndarray, symbol: str) -> MarketRegime:
        """
        Determine current market regime using volatility clustering
        """
        try:
            # Check cached regime
            cache_key = f"{self.REGIME_CACHE_KEY}:{symbol}"
            cached_regime = await self.redis.get(cache_key)
            
            if cached_regime:
                regime_data = json.loads(cached_regime)
                return MarketRegime(**regime_data)
            
            # Calculate rolling volatility
            window_size = min(30, len(returns) // 2)
            if window_size < 5:
                window_size = len(returns)
            
            rolling_vol = []
            for i in range(window_size, len(returns)):
                window_returns = returns[i-window_size:i]
                vol = np.std(window_returns) * np.sqrt(252)
                rolling_vol.append(vol)
            
            if not rolling_vol:
                current_vol = np.std(returns) * np.sqrt(252)
            else:
                current_vol = rolling_vol[-1]
            
            # Classify regime
            if current_vol < self.regime_threshold_low:
                regime_type = "low"
            elif current_vol > self.regime_threshold_high:
                regime_type = "high"
            else:
                regime_type = "medium"
            
            # Calculate persistence (simplified)
            persistence = min(0.9, len(rolling_vol) / 100) if rolling_vol else 0.5
            
            # Estimate transition probability (simplified)
            transition_prob = 0.1 if persistence > 0.7 else 0.3
            
            regime = MarketRegime(
                regime_type=regime_type,
                volatility_level=current_vol,
                persistence=persistence,
                transition_probability=transition_prob
            )
            
            # Cache regime for 15 minutes
            await self.redis.setex(
                cache_key,
                900,
                json.dumps({
                    'regime_type': regime.regime_type,
                    'volatility_level': regime.volatility_level,
                    'persistence': regime.persistence,
                    'transition_probability': regime.transition_probability
                })
            )
            
            return regime
            
        except Exception as e:
            logger.error(f"Error determining market regime: {e}")
            return MarketRegime("medium", 0.25, 0.5, 0.2)
    
    def _get_regime_volatility(self, regime_type: str) -> float:
        """Get volatility adjustment based on regime"""
        regime_multipliers = {
            "low": 0.8,
            "medium": 1.0,
            "high": 1.3
        }
        base_vol = 0.25
        return base_vol * regime_multipliers.get(regime_type, 1.0)
    
    def _calculate_confidence(self, returns: np.ndarray, persistence: float) -> float:
        """Calculate confidence in volatility estimate"""
        # Base confidence on data quantity and regime persistence
        data_confidence = min(1.0, len(returns) / 100)
        regime_confidence = persistence
        
        return (data_confidence * 0.6 + regime_confidence * 0.4)
    
    async def _get_price_history(self, symbol: str) -> List[float]:
        """Get cached price history or return default"""
        try:
            cache_key = f"{self.PRICE_HISTORY_KEY}:{symbol}"
            cached_prices = await self.redis.get(cache_key)
            
            if cached_prices:
                return json.loads(cached_prices)
            
            # Return synthetic price data for testing
            # In production, this would fetch from exchange API
            np.random.seed(42)
            base_price = 50000
            returns = np.random.normal(0, 0.02, 100)
            prices = [base_price]
            
            for ret in returns:
                prices.append(prices[-1] * (1 + ret))
            
            return prices
            
        except Exception as e:
            logger.error(f"Error getting price history: {e}")
            return [50000.0] * 20  # Fallback data
    
    async def _cache_volatility_metrics(self, symbol: str, metrics: VolatilityMetrics):
        """Cache volatility metrics"""
        try:
            cache_key = f"{self.VOLATILITY_CACHE_KEY}:{symbol}"
            cache_data = {
                'ewma_volatility': metrics.ewma_volatility,
                'realized_volatility': metrics.realized_volatility,
                'garch_volatility': metrics.garch_volatility,
                'regime_volatility': metrics.regime_volatility,
                'confidence_level': metrics.confidence_level,
                'regime': metrics.regime,
                'timestamp': metrics.timestamp.isoformat()
            }
            
            await self.redis.setex(
                cache_key,
                300,  # 5 minutes
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.error(f"Error caching volatility metrics: {e}")
    
    def _get_default_volatility_metrics(self) -> VolatilityMetrics:
        """Return default volatility metrics when calculation fails"""
        return VolatilityMetrics(
            ewma_volatility=0.25,
            realized_volatility=0.25,
            garch_volatility=0.25,
            regime_volatility=0.25,
            confidence_level=0.3,
            regime="medium",
            timestamp=datetime.now()
        )
    
    async def get_portfolio_volatility(
        self,
        positions: Dict[str, float],
        correlation_matrix: Optional[Dict[str, Dict[str, float]]] = None
    ) -> float:
        """
        Calculate portfolio volatility considering correlations
        """
        try:
            if not positions:
                return 0.0
            
            symbols = list(positions.keys())
            weights = np.array(list(positions.values()))
            
            # Normalize weights
            weights = weights / np.sum(np.abs(weights))
            
            # Get individual volatilities
            volatilities = []
            for symbol in symbols:
                vol_metrics = await self.calculate_comprehensive_volatility(symbol)
                volatilities.append(vol_metrics.ewma_volatility)
            
            vol_array = np.array(volatilities)
            
            # If no correlation matrix provided, assume moderate correlation
            if correlation_matrix is None:
                correlation_matrix = {}
                for i, sym1 in enumerate(symbols):
                    correlation_matrix[sym1] = {}
                    for j, sym2 in enumerate(symbols):
                        if i == j:
                            correlation_matrix[sym1][sym2] = 1.0
                        else:
                            correlation_matrix[sym1][sym2] = 0.3  # Default correlation
            
            # Build correlation matrix
            corr_matrix = np.zeros((len(symbols), len(symbols)))
            for i, sym1 in enumerate(symbols):
                for j, sym2 in enumerate(symbols):
                    corr_matrix[i, j] = correlation_matrix.get(sym1, {}).get(sym2, 0.3)
            
            # Calculate portfolio volatility: sqrt(w'Cw) where C is covariance matrix
            cov_matrix = np.outer(vol_array, vol_array) * corr_matrix
            portfolio_var = np.dot(weights, np.dot(cov_matrix, weights))
            
            return np.sqrt(max(0, portfolio_var))
            
        except Exception as e:
            logger.error(f"Error calculating portfolio volatility: {e}")
            return 0.25  # Default portfolio volatility
    
    async def get_volatility_scaling_factor(
        self,
        symbol: str,
        target_volatility: float = 0.20
    ) -> float:
        """
        Get volatility scaling factor for position sizing
        """
        try:
            vol_metrics = await self.calculate_comprehensive_volatility(symbol)
            current_vol = vol_metrics.ewma_volatility
            
            if current_vol <= 0:
                return 1.0
            
            # Scale inversely with volatility
            scaling_factor = target_volatility / current_vol
            
            # Cap the scaling factor to reasonable bounds
            scaling_factor = max(0.1, min(5.0, scaling_factor))
            
            return scaling_factor
            
        except Exception as e:
            logger.error(f"Error calculating volatility scaling factor: {e}")
            return 1.0