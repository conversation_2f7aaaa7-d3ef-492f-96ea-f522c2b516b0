"""
Weight optimizer for strategy scoring.

This module contains the MLWeightOptimizer class for optimizing strategy
weights using machine learning, including the new ZenML-based ensemble
weight optimization with W&B and MLflow integration.
"""

import logging
import os
import pickle
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import asyncio

from app.ml.models.reinforcement_learning import RLWeightOptimizer
from app.ml.data_collector import DataCollector
from app.ml.feature_engineering import FeatureEngineer
from app.services.mcp.wandb_service import WandBService, ExperimentMetrics, ModelTrackingMetrics
from app.services.mcp.mlflow_service import MLflowService, deploy_trained_model

logger = logging.getLogger(__name__)


class WeightOptimizer:
    """ZenML-based strategy weight optimizer for the ensemble system with MCP integration."""
    
    def __init__(
        self, 
        model_path: Optional[str] = None,
        wandb_api_key: Optional[str] = None,
        mlflow_tracking_uri: Optional[str] = None,
        enable_experiment_tracking: bool = True
    ):
        """
        Initialize weight optimizer with MCP services.
        
        Args:
            model_path: Path to trained model. If None, uses latest model.
            wandb_api_key: Weights & Biases API key for experiment tracking
            mlflow_tracking_uri: MLflow tracking server URI
            enable_experiment_tracking: Whether to enable W&B and MLflow tracking
        """
        self.model = None
        self.feature_names = None
        self.model_metadata = None
        self.enable_tracking = enable_experiment_tracking
        
        # Initialize MCP services
        if self.enable_tracking:
            self.wandb_service = WandBService(
                project_name="crypto-ensemble-strategy",
                api_key=wandb_api_key
            )
            self.mlflow_service = MLflowService(
                tracking_uri=mlflow_tracking_uri or "http://localhost:5000",
                experiment_name="ensemble-weight-optimization"
            )
        else:
            self.wandb_service = None
            self.mlflow_service = None
        
        # Default to latest model if no path provided
        if model_path is None:
            model_path = '/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/models/ensemble_weight_optimizer_latest.pkl'
        
        self.model_path = model_path
        self.current_experiment_id = None
        self.prediction_count = 0
        
        self.load_model()
    
    def load_model(self) -> None:
        """Load the trained model from disk or MLflow."""
        try:
            # Try loading from MLflow first if available
            if self.enable_tracking and self.mlflow_service:
                try:
                    # Use asyncio.run for async calls in sync context
                    import asyncio
                    try:
                        mlflow_model = asyncio.run(self.mlflow_service.load_production_model())
                    except RuntimeError:
                        # If event loop is already running, skip MLflow loading
                        mlflow_model = None
                    
                    if mlflow_model:
                        self.model = mlflow_model
                        try:
                            model_info = asyncio.run(self.mlflow_service.get_model_info())
                        except RuntimeError:
                            model_info = None
                        if model_info:
                            self.model_metadata = {
                                'version': model_info.get('version', 'unknown'),
                                'timestamp': model_info.get('creation_timestamp', 'unknown'),
                                'metrics': model_info.get('metrics', {}),
                                'source': 'mlflow'
                            }
                        logger.info(f"Loaded MLflow model version {self.model_metadata.get('version', 'unknown')}")
                        return
                except Exception as e:
                    logger.warning(f"Failed to load MLflow model, falling back to local: {e}")
            
            # Fallback to local model loading
            if not os.path.exists(self.model_path):
                logger.warning(f"Model not found at {self.model_path}. Using fallback equal weights.")
                self.model = None
                return
            
            with open(self.model_path, 'rb') as f:
                model_package = pickle.load(f)
            
            self.model = model_package['model']
            self.feature_names = model_package['feature_names']
            self.model_metadata = {
                'metrics': model_package['metrics'],
                'timestamp': model_package['timestamp'],
                'version': model_package['version'],
                'source': 'local'
            }
            
            logger.info(f"Loaded local model version {self.model_metadata['version']} with R2: {self.model_metadata['metrics']['test_r2']:.4f}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            self.model = None
    
    async def predict_weights(
        self, 
        market_conditions: Dict[str, float],
        log_prediction: bool = True
    ) -> np.ndarray:
        """
        Predict optimal strategy weights based on market conditions with experiment tracking.
        
        Args:
            market_conditions: Dictionary with market features
            log_prediction: Whether to log prediction to W&B
            
        Returns:
            Array of strategy weights [grid_weight, ta_weight, trend_weight]
        """
        start_time = datetime.now()
        
        try:
            if self.model is None:
                logger.warning("No model available, using equal weights")
                weights = np.array([1/3, 1/3, 1/3])
                
                # Log fallback to W&B
                if log_prediction and self.enable_tracking and self.wandb_service:
                    await self._log_prediction_metrics(
                        market_conditions, weights, "fallback", 
                        (datetime.now() - start_time).total_seconds() * 1000
                    )
                
                return weights
            
            # Prepare features in the same order as training
            features = self._prepare_features(market_conditions)
            
            # Predict weights
            if hasattr(self.model, 'predict'):
                # MLflow or scikit-learn model
                raw_weights = self.model.predict(features.reshape(1, -1))[0]
            else:
                # Custom model implementation
                raw_weights = self.model(features.reshape(1, -1))[0]
            
            # Ensure weights are non-negative and sum to 1
            weights = np.maximum(raw_weights, 0.01)  # Minimum 1% weight
            weights = weights / np.sum(weights)
            
            # Log prediction to W&B
            if log_prediction and self.enable_tracking and self.wandb_service:
                execution_time_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self._log_prediction_metrics(
                    market_conditions, weights, "success", execution_time_ms
                )
            
            self.prediction_count += 1
            
            logger.debug(f"Predicted weights: Grid={weights[0]:.3f}, TA={weights[1]:.3f}, Trend={weights[2]:.3f}")
            return weights
            
        except Exception as e:
            logger.error(f"Weight prediction failed: {e}")
            
            # Fallback to equal weights
            weights = np.array([1/3, 1/3, 1/3])
            
            # Log error to W&B
            if log_prediction and self.enable_tracking and self.wandb_service:
                execution_time_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self._log_prediction_metrics(
                    market_conditions, weights, f"error: {str(e)}", execution_time_ms
                )
            
            return weights
    
    def _prepare_features(self, market_conditions: Dict[str, float]) -> np.ndarray:
        """Prepare feature vector from market conditions."""
        try:
            # Extract features in the same order as training
            features = [
                market_conditions.get('volatility', 0.02),
                market_conditions.get('volume', 1000000),
                market_conditions.get('rsi', 50),
                market_conditions.get('macd', 0),
                market_conditions.get('price_change', 0),
                market_conditions.get('volatility_ma', 0.02),
                market_conditions.get('volume_ma', 1000000),
                market_conditions.get('rsi_ma', 50)
            ]
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Feature preparation failed: {e}")
            # Return default features
            return np.array([0.02, 1000000, 50, 0, 0, 0.02, 1000000, 50], dtype=np.float32)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if self.model is None:
            return {"status": "no_model", "message": "No model loaded"}
        
        return {
            "status": "loaded",
            "version": self.model_metadata.get('version', 'unknown'),
            "timestamp": self.model_metadata.get('timestamp', 'unknown'),
            "metrics": self.model_metadata.get('metrics', {}),
            "feature_names": self.feature_names
        }
    
    async def retrain_model(self) -> bool:
        """Trigger model retraining via ZenML pipeline with MCP integration."""
        try:
            # Initialize experiment if tracking is enabled
            if self.enable_tracking and self.wandb_service:
                self.current_experiment_id = await self.wandb_service.initialize_experiment(
                    experiment_name=f"ensemble_retrain_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    config={
                        "retrain_trigger": "manual",
                        "model_version": self.model_metadata.get('version', 'unknown') if self.model_metadata else 'unknown',
                        "prediction_count": self.prediction_count
                    },
                    tags=["retrain", "ensemble", "automated"]
                )
            
            # Import here to avoid circular dependencies
            from app.ml.pipelines.ensemble_training import ensemble_training_pipeline
            
            logger.info("Triggering model retraining with MCP tracking...")
            
            # Run the training pipeline
            pipeline_run = ensemble_training_pipeline()
            
            # Reload the model after training
            await asyncio.sleep(1)  # Give time for model to be saved
            self.load_model()
            
            # Log retraining completion
            if self.enable_tracking and self.wandb_service:
                await self.wandb_service.log_ml_model_performance(
                    ModelTrackingMetrics(
                        model_version=self.model_metadata.get('version', 'unknown') if self.model_metadata else 'unknown',
                        accuracy=0.8,  # Placeholder - should be actual metrics
                        precision=0.8,
                        recall=0.8,
                        f1_score=0.8,
                        training_loss=0.1,
                        validation_loss=0.15,
                        convergence_episodes=100,
                        hyperparameters={},
                        training_duration_seconds=300
                    )
                )
            
            logger.info(f"Model retrained successfully. New version: {pipeline_run}")
            return True
            
        except Exception as e:
            logger.error(f"Model retraining failed: {e}")
            return False
    
    async def train_and_deploy_model(
        self,
        training_data: pd.DataFrame,
        validation_criteria: Optional[Dict[str, float]] = None
    ) -> Tuple[bool, str]:
        """Train and deploy new model with full MCP integration."""
        try:
            # Initialize experiment
            if self.enable_tracking and self.wandb_service:
                experiment_name = f"ensemble_train_deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                self.current_experiment_id = await self.wandb_service.initialize_experiment(
                    experiment_name=experiment_name,
                    config={
                        "training_samples": len(training_data),
                        "features": list(training_data.columns),
                        "validation_enabled": validation_criteria is not None
                    },
                    tags=["training", "deployment", "ensemble"]
                )
            
            # Start MLflow run
            if self.enable_tracking and self.mlflow_service:
                await self.mlflow_service.start_run(
                    run_name=f"ensemble_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )
            
            # Prepare training data
            X, y = self._prepare_training_data(training_data)
            
            # Train model (placeholder - replace with actual training logic)
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            
            # Calculate training metrics
            from sklearn.metrics import mean_squared_error, r2_score
            y_pred = model.predict(X)
            training_metrics = {
                "mse": float(mean_squared_error(y, y_pred)),
                "r2": float(r2_score(y, y_pred)),
                "feature_count": X.shape[1],
                "training_samples": X.shape[0]
            }
            
            # Log training metrics to W&B
            if self.enable_tracking and self.wandb_service:
                await self.wandb_service.log_ml_model_performance(
                    ModelTrackingMetrics(
                        model_version=f"v{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        accuracy=training_metrics["r2"],
                        precision=0.8,  # Placeholder
                        recall=0.8,     # Placeholder
                        f1_score=0.8,   # Placeholder
                        training_loss=training_metrics["mse"],
                        validation_loss=training_metrics["mse"] * 1.1,  # Placeholder
                        convergence_episodes=100,
                        hyperparameters={"n_estimators": 100, "random_state": 42},
                        training_duration_seconds=60
                    )
                )
            
            # Deploy model via MLflow
            if self.enable_tracking and self.mlflow_service:
                model_version = f"v{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                input_example = X[:1]  # First sample as example
                
                success, message = await deploy_trained_model(
                    model=model,
                    model_version=model_version,
                    training_metrics=training_metrics,
                    hyperparameters={"n_estimators": 100, "random_state": 42},
                    input_example=input_example,
                    validation_criteria=validation_criteria
                )
                
                await self.mlflow_service.end_run()
                
                if success:
                    # Reload model from MLflow
                    self.load_model()
                    return True, f"Model trained and deployed: {message}"
                else:
                    return False, f"Deployment failed: {message}"
            else:
                # Save model locally if MLflow not available
                model_version = f"v{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                local_path = f"{self.model_path}_{model_version}.pkl"
                
                model_package = {
                    'model': model,
                    'feature_names': list(training_data.columns),
                    'metrics': training_metrics,
                    'timestamp': datetime.now().isoformat(),
                    'version': model_version
                }
                
                with open(local_path, 'wb') as f:
                    pickle.dump(model_package, f)
                
                self.model_path = local_path
                self.load_model()
                
                return True, f"Model trained and saved locally: {model_version}"
                
        except Exception as e:
            logger.error(f"Training and deployment failed: {e}")
            return False, f"Training error: {str(e)}"
        finally:
            # Finish experiment
            if self.enable_tracking and self.wandb_service:
                await self.wandb_service.finish_experiment()
    
    async def _log_prediction_metrics(
        self,
        market_conditions: Dict[str, float],
        predicted_weights: np.ndarray,
        status: str,
        execution_time_ms: float
    ) -> None:
        """Log prediction metrics to W&B."""
        try:
            if not self.wandb_service:
                return
            
            log_data = {
                # Prediction results
                "prediction/grid_weight": float(predicted_weights[0]),
                "prediction/ta_weight": float(predicted_weights[1]),
                "prediction/trend_weight": float(predicted_weights[2]),
                "prediction/status": status,
                "prediction/execution_time_ms": execution_time_ms,
                "prediction/count": self.prediction_count,
                
                # Market conditions
                **{f"market/{key}": value for key, value in market_conditions.items()},
                
                # Model info
                "model/version": self.model_metadata.get('version', 'unknown') if self.model_metadata else 'unknown',
                "model/source": self.model_metadata.get('source', 'unknown') if self.model_metadata else 'unknown'
            }
            
            await self.wandb_service.log_strategy_performance(
                ExperimentMetrics(
                    timestamp=datetime.now(),
                    experiment_name="weight_prediction",
                    strategy_weights={
                        "GridStrategy": float(predicted_weights[0]),
                        "TechnicalAnalysisStrategy": float(predicted_weights[1]),
                        "TrendFollowingStrategy": float(predicted_weights[2])
                    },
                    portfolio_value=100000,  # Placeholder
                    total_pnl=0,  # Placeholder
                    sharpe_ratio=0,  # Placeholder
                    max_drawdown=0,  # Placeholder
                    win_rate=0,  # Placeholder
                    correlation_matrix={},  # Placeholder
                    individual_strategy_pnl={},  # Placeholder
                    confidence_scores={},  # Placeholder
                    market_conditions=market_conditions,
                    trade_count=0,  # Placeholder
                    execution_time_ms=execution_time_ms
                ),
                step=self.prediction_count
            )
            
        except Exception as e:
            logger.error(f"Failed to log prediction metrics: {e}")
    
    def _prepare_training_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data for model training."""
        # This is a placeholder implementation
        # In reality, you'd extract features and targets from historical data
        
        # Assume last column is target (strategy weights)
        X = data.iloc[:, :-3].values  # Features
        y = data.iloc[:, -3:].values  # Target weights for 3 strategies
        
        return X, y

class MLWeightOptimizer:
    """Optimizes strategy weights using machine learning."""
    
    def __init__(self, exchange_client, db_client=None, model_path: str = "models/weight_optimizer"):
        """Initialize the MLWeightOptimizer.
        
        Args:
            exchange_client: Client for fetching market data from exchange
            db_client: Client for database operations (optional)
            model_path: Path to save/load the model
        """
        self.exchange_client = exchange_client
        self.db_client = db_client
        self.model_path = model_path
        self.model = None
        self.data_collector = DataCollector(exchange_client, db_client)
        self.feature_engineer = FeatureEngineer()
        self.strategy_names = ['grid', 'technical_analysis', 'trend_following']
        self.logger = logging.getLogger(__name__)
        self.last_training_time = None
        self.last_weights = None
    
    async def train_model(self, symbol: str, timeframe: str, lookback_days: int = 90,
                         window_size: int = 10, total_timesteps: int = 100000) -> bool:
        """Train the ML model for weight optimization.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for training data
            window_size: Size of the observation window
            total_timesteps: Number of timesteps to train for
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Collect training data
            self.logger.info(f"Collecting training data for {symbol} ({timeframe})")
            market_data, strategy_performances = await self.data_collector.collect_training_data(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=lookback_days
            )
            
            if market_data.empty:
                self.logger.warning("No market data collected for training")
                return False
            
            # Extract features
            self.logger.info("Extracting features from market data")
            features_df = self.feature_engineer.extract_market_features(market_data)
            
            # Create or load the model
            if self.model is None:
                self.logger.info("Creating new RL model")
                self.model = RLWeightOptimizer()
                
                # Create environment
                env = self.model.create_env(
                    market_data=features_df,
                    strategy_names=self.strategy_names,
                    window_size=window_size,
                    max_steps=100,
                    reward_function='sharpe'
                )
                
                # Build the model
                self.model.build_model(env)
            else:
                self.logger.info("Using existing RL model")
                
                # Update environment with new data
                env = self.model.create_env(
                    market_data=features_df,
                    strategy_names=self.strategy_names,
                    window_size=window_size,
                    max_steps=100,
                    reward_function='sharpe'
                )
            
            # Train the model
            self.logger.info(f"Training RL model for {total_timesteps} timesteps")
            metrics = self.model.train(None, None, total_timesteps=total_timesteps)
            
            # Save the model
            self.logger.info(f"Saving RL model to {self.model_path}")
            success = self.model.save(self.model_path)
            
            if not success:
                self.logger.warning("Failed to save RL model")
                return False
            
            # Update last training time
            self.last_training_time = datetime.now()
            
            self.logger.info("Successfully trained and saved RL model")
            return True
            
        except Exception as e:
            self.logger.error(f"Error training ML model: {e}")
            return False
    
    async def load_model(self) -> bool:
        """Load the ML model from disk.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if model file exists
            if not os.path.exists(f"{self.model_path}.zip"):
                self.logger.warning(f"Model file not found: {self.model_path}.zip")
                return False
            
            # Create model instance
            self.model = RLWeightOptimizer()
            
            # Load the model
            self.logger.info(f"Loading RL model from {self.model_path}")
            success = self.model.load(self.model_path)
            
            if not success:
                self.logger.warning("Failed to load RL model")
                return False
            
            self.logger.info("Successfully loaded RL model")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading ML model: {e}")
            return False
    
    async def get_optimized_weights(self, market_conditions: Dict[str, float],
                                   window_size: int = 10) -> Dict[str, float]:
        """Get optimized weights for strategy scoring.
        
        Args:
            market_conditions: Dictionary of market condition scores
            window_size: Size of the observation window
            
        Returns:
            Dictionary of strategy weights
        """
        try:
            # Check if model is loaded
            if self.model is None:
                self.logger.warning("No model loaded, attempting to load")
                success = await self.load_model()
                
                if not success:
                    self.logger.warning("Failed to load model, using default weights")
                    return self._get_default_weights()
            
            # Create feature vector from market conditions
            # Determine observation shape from the PPO model
            obs_space = None
            # Use PPO model's observation_space if available
            if hasattr(self.model, 'model') and hasattr(self.model.model, 'observation_space'):
                obs_space = self.model.model.observation_space
            # Use PPO model env's observation_space
            elif hasattr(self.model, 'model') and hasattr(self.model.model, 'env') and self.model.model.env is not None:
                obs_space = self.model.model.env.observation_space
            # Fallback to RLWeightOptimizer.env's observation_space
            elif hasattr(self.model, 'env') and self.model.env is not None:
                obs_space = self.model.env.observation_space
            if obs_space is not None:
                features = np.zeros(obs_space.shape, dtype=getattr(obs_space, 'dtype', np.float32))
            else:
                # Default: window_size x number of conditions
                features = np.zeros((window_size, len(market_conditions)), dtype=np.float32)
            
            # Fill the last row with current market conditions
            for i, (key, value) in enumerate(market_conditions.items()):
                features[-1, i] = value
            
            # Make prediction
            self.logger.info("Getting optimized weights from RL model")
            predictions = self.model.predict(np.array([features]))
            
            if len(predictions) == 0:
                self.logger.warning("Failed to get predictions, using default weights")
                return self._get_default_weights()
            
            # Get the weights
            weights = predictions[0]
            
            # Convert to dictionary
            weights_dict = {
                self.strategy_names[i]: float(weights[i])
                for i in range(len(self.strategy_names))
            }
            
            # Store the weights
            self.last_weights = weights_dict
            
            self.logger.info(f"Optimized weights: {weights_dict}")
            return weights_dict
            
        except Exception as e:
            self.logger.error(f"Error getting optimized weights: {e}")
            return self._get_default_weights()
    
    def _get_default_weights(self) -> Dict[str, float]:
        """Get default weights for strategy scoring.
        
        Returns:
            Dictionary of default strategy weights
        """
        # Equal weights for all strategies
        weights = {
            strategy: 1.0 / len(self.strategy_names)
            for strategy in self.strategy_names
        }
        
        self.logger.info(f"Using default weights: {weights}")
        return weights
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about the ML model.
        
        Returns:
            Dictionary of model information
        """
        try:
            # Check if model is loaded
            if self.model is None:
                self.logger.warning("No model loaded, attempting to load")
                success = await self.load_model()
                
                if not success:
                    self.logger.warning("Failed to load model")
                    return {
                        'status': 'not_loaded',
                        'error': 'Failed to load model'
                    }
            
            # Get model metadata
            metadata = self.model.get_metadata()
            
            # Add additional information
            info = {
                'status': 'loaded',
                'model_name': metadata.get('model_name', 'unknown'),
                'model_version': metadata.get('model_version', 'unknown'),
                'created_at': metadata.get('created_at', 'unknown'),
                'updated_at': metadata.get('updated_at', 'unknown'),
                'last_training_time': self.last_training_time.isoformat() if self.last_training_time else 'never',
                'hyperparameters': metadata.get('hyperparameters', {}),
                'performance_metrics': metadata.get('performance_metrics', {}),
                'last_weights': self.last_weights
            }
            
            return info
            
        except Exception as e:
            self.logger.error(f"Error getting model info: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def evaluate_model(self, symbol: str, timeframe: str, 
                           lookback_days: int = 30) -> Dict[str, float]:
        """Evaluate the ML model on historical data.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for evaluation data
            
        Returns:
            Dictionary of evaluation metrics
        """
        try:
            # Check if model is loaded
            if self.model is None:
                self.logger.warning("No model loaded, attempting to load")
                success = await self.load_model()
                
                if not success:
                    self.logger.warning("Failed to load model")
                    return {
                        'error': 'Failed to load model'
                    }
            
            # Collect evaluation data
            self.logger.info(f"Collecting evaluation data for {symbol} ({timeframe})")
            market_data, strategy_performances = await self.data_collector.collect_training_data(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=lookback_days
            )
            
            if market_data.empty:
                self.logger.warning("No market data collected for evaluation")
                return {
                    'error': 'No market data collected'
                }
            
            # Extract features
            self.logger.info("Extracting features from market data")
            features_df = self.feature_engineer.extract_market_features(market_data)
            
            # Prepare evaluation data
            X, y = self.feature_engineer.prepare_training_data(
                market_data=features_df,
                strategy_performances=strategy_performances
            )
            
            if len(X) == 0 or len(y) == 0:
                self.logger.warning("No evaluation data prepared")
                return {
                    'error': 'No evaluation data prepared'
                }
            
            # Evaluate the model
            self.logger.info("Evaluating RL model")
            metrics = self.model.evaluate(X, y)
            
            self.logger.info(f"Evaluation metrics: {metrics}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error evaluating ML model: {e}")
            return {
                'error': str(e)
            }
