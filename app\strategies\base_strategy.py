"""
Base Strategy class for the Multi-Strategy Crypto Auto Trader.
All trading strategies should inherit from this class.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
import pandas as pd
import numpy as np
from app.config.settings import Settings


class BaseStrategy(ABC):
    """Base class for all trading strategies.
    
    This abstract class defines the interface that all strategies must implement.
    It provides common functionality and ensures that all strategies follow
    the same structure.
    """
    
    def __init__(self, symbol: str, timeframe: str, settings: Settings):
        """Initialize the strategy.
        
        Args:
            symbol: The trading pair symbol (e.g., 'BTCUSDT')
            timeframe: The candlestick timeframe (e.g., '1h', '4h', '1d')
            settings: Strategy-specific settings
        """
        self.symbol = symbol
        self.timeframe = timeframe
        self.settings = settings
        self.name = self.__class__.__name__
        self.logger = logging.getLogger(self.name)
        self._is_running = False
    
    @abstractmethod
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market data to determine conditions.
        
        Args:
            data: DataFrame containing OHLCV data
            
        Returns:
            Dictionary containing market analysis results
        """
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on the strategy logic.
        
        Args:
            data: DataFrame containing OHLCV data
            
        Returns:
            DataFrame with signals added (1 for buy, -1 for sell, 0 for hold)
        """
        pass
    
    @abstractmethod
    def calculate_risk_params(self, 
                             data: pd.DataFrame, 
                             entry_price: float, 
                             position_type: str) -> Dict[str, float]:
        """Calculate risk parameters for a trade.
        
        Args:
            data: DataFrame containing OHLCV data
            entry_price: The entry price for the trade
            position_type: 'long' or 'short'
            
        Returns:
            Dictionary containing stop loss and take profit levels
        """
        pass
    
    @abstractmethod
    def calculate_score(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate the strategy score based on current market conditions.
        
        Args:
            market_conditions: Dictionary containing market conditions
            
        Returns:
            Strategy score (0-100)
        """
        pass
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get the current strategy parameters.
        
        Returns:
            Dictionary of strategy parameters
        """
        return self.params
    
    def set_parameters(self, params: Dict[str, Any]) -> None:
        """Update strategy parameters by updating the settings object."""
        self.logger.debug(f"Updating {self.name} parameters: {params}")
        for key, value in params.items():
            if hasattr(self.settings, key):
                setattr(self.settings, key, value)
            else:
                self.logger.warning(f"Parameter '{key}' not found in settings.")

    def update_params(self, params: Dict[str, Any]) -> None:
        """Update strategy parameters (alias for set_parameters)."""
        self.set_parameters(params)

    def __str__(self) -> str:
        """String representation of the strategy.
        
        Returns:
            String describing the strategy
        """
        return f"{self.name} ({self.symbol}, {self.timeframe})"

    async def start(self):
        """Start the strategy."""
        self.logger.info(f"Starting {self.name} strategy for {self.symbol}")
        self._is_running = True

    async def stop(self):
        """Stop the strategy."""
        self.logger.info(f"Stopping {self.name} strategy for {self.symbol}")
        self._is_running = False

    def is_running(self) -> bool:
        """Check if the strategy is running."""
        return self._is_running