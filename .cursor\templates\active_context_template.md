# Active Context: [Brief Task/Feature Name] - YYYY-MM-DD HH:MM

## 1. Current Goal / Objective
- (What is the immediate goal of the current work session or task?)

## 2. Key Files / Modules Involved
- `path/to/file1.ext` (Brief reason for focus)
- `path/to/module2/` (Brief reason for focus)

## 3. Current Cascade Mode
- `[MODE: MODE_NAME]` (e.g., PLAN, EXECUTE, RESEARCH)

## 4. Recent Findings / Decisions
- (Bullet list of important discoveries or decisions made recently relevant to this task.)
- (e.g., "Decided to use X library for Y feature.")
- (e.g., "Identified bug Z in module A.")

## 5. Blockers / Open Questions
- (Any obstacles preventing progress?)
- (Specific questions that need answers to proceed?)

## 6. Immediate Next Steps (for Cascade)
1. (Actionable step 1)
2. (Actionable step 2)

## 7. User's Last Instruction/Query
- (Quote or paraphrase the user's last significant request or question leading to this context.)

## 8. Relevant Memory Bank Snippets (Optional - For Cascade's internal use or if requested)
- (Key snippets from memory bank files or `create_memory` entries that are highly relevant to the current task.)
