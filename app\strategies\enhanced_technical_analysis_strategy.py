#!/usr/bin/env python3
"""
Enhanced Technical Analysis Strategy for Ensemble Execution
Optimized for concurrent Redis-cached execution with real-time position tracking.
"""

import asyncio
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import logging

from app.models.market_data import MarketData
from app.strategies.enhanced_base_strategy import EnhancedBaseStrategy
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

class EnhancedTechnicalAnalysisStrategy(EnhancedBaseStrategy):
    """
    Enhanced Technical Analysis Strategy optimized for ensemble execution.
    
    Features:
    - Redis-cached technical indicator calculations
    - Multi-timeframe analysis with caching
    - Concurrent execution optimization
    - Real-time signal confidence scoring
    """
    
    def __init__(
        self,
        symbol: str,
        timeframe: str,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        # Technical analysis specific config
        ta_config = {
            # RSI Parameters
            "rsi_period": 14,
            "rsi_overbought": 70,
            "rsi_oversold": 30,
            "rsi_extreme_overbought": 80,
            "rsi_extreme_oversold": 20,
            
            # MACD Parameters
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            
            # Moving Averages
            "sma_short": 20,
            "sma_long": 50,
            "ema_period": 21,
            
            # Bollinger Bands
            "bb_period": 20,
            "bb_std": 2,
            
            # Volume Analysis
            "volume_ma_period": 20,
            "volume_surge_threshold": 2.0,
            
            # Signal Generation
            "min_signal_strength": 0.6,
            "confluence_required": 2,  # Minimum indicators in agreement
            "max_divergence_periods": 5,
            
            # Caching
            "cache_ttl_indicators": 300,    # 5 minutes
            "cache_ttl_analysis": 180,      # 3 minutes
            "enable_multi_timeframe": True,
            
            **self._enhanced_config()
        }
        
        if config:
            ta_config.update(config)
        
        super().__init__(symbol, timeframe, redis_service, supabase_service, ta_config)
        
        # TA-specific cache keys
        self.INDICATORS_KEY = f"ta:{self.strategy_id}:indicators"
        self.SIGNALS_KEY = f"ta:{self.strategy_id}:signals"
        self.CONFLUENCE_KEY = f"ta:{self.strategy_id}:confluence"
        self.DIVERGENCE_KEY = f"ta:{self.strategy_id}:divergence"
        
        # Technical state
        self.cached_indicators = {}
        self.signal_history = []
        self.confluence_score = 0.0
        
        logger.info(f"Enhanced Technical Analysis Strategy initialized: {self.strategy_id}")
    
    def _enhanced_config(self) -> Dict[str, Any]:
        """Enhanced configuration for technical analysis"""
        return {
            "enable_divergence_detection": True,
            "enable_confluence_scoring": True,
            "enable_adaptive_periods": True,
            "enable_volume_confirmation": True,
            "min_confidence_for_execution": 0.65,
            "signal_decay_periods": 3,
            "max_signal_age_minutes": 15
        }
    
    async def _perform_market_analysis(self, market_data: MarketData) -> Dict[str, Any]:
        """
        Perform comprehensive technical analysis with caching.
        """
        try:
            # Get cached indicators or calculate fresh ones
            indicators = await self._get_cached_indicators(market_data)
            
            # Calculate confluence score
            confluence = await self._calculate_confluence_score(indicators, market_data)
            
            # Detect divergences
            divergences = await self._detect_divergences(indicators, market_data)
            
            # Analyze volume
            volume_analysis = await self._analyze_volume(indicators, market_data)
            
            # Calculate overall trend strength
            trend_analysis = await self._analyze_trend(indicators)
            
            # Generate signal strength assessment
            signal_strength = await self._assess_signal_strength(
                indicators, confluence, divergences, volume_analysis, trend_analysis
            )
            
            analysis = {
                'indicators': indicators,
                'confluence_score': confluence,
                'divergences': divergences,
                'volume_analysis': volume_analysis,
                'trend_analysis': trend_analysis,
                'signal_strength': signal_strength,
                'market_regime': self._determine_market_regime(indicators),
                'confidence': signal_strength,  # Use signal strength as confidence
                'timestamp': datetime.now().isoformat()
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Technical analysis failed: {e}")
            return self._default_market_analysis()
    
    async def _generate_raw_signal(
        self,
        market_data: MarketData,
        market_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate trading signal based on technical analysis.
        """
        try:
            indicators = market_analysis.get('indicators', {})
            confluence = market_analysis.get('confluence_score', 0)
            signal_strength = market_analysis.get('signal_strength', 0)
            trend_analysis = market_analysis.get('trend_analysis', {})
            volume_analysis = market_analysis.get('volume_analysis', {})
            
            current_price = market_data.price
            
            # Don't trade if signal strength is too low
            if signal_strength < self.config["min_signal_strength"]:
                return {
                    'action': 'HOLD',
                    'quantity': 0,
                    'price': current_price,
                    'confidence': signal_strength,
                    'reason': 'weak_signal'
                }
            
            # Generate primary signal based on indicator confluence
            primary_signal = await self._generate_primary_signal(indicators, current_price)
            
            # Apply volume confirmation
            if self.config["enable_volume_confirmation"]:
                volume_confirmed = volume_analysis.get('confirms_signal', True)
                if not volume_confirmed:
                    # Reduce confidence but don't completely reject
                    signal_strength *= 0.7
            
            # Apply trend confirmation
            trend_direction = trend_analysis.get('direction', 'neutral')
            if primary_signal['action'] == 'BUY' and trend_direction == 'bearish':
                signal_strength *= 0.8  # Reduce confidence for counter-trend trades
            elif primary_signal['action'] == 'SELL' and trend_direction == 'bullish':
                signal_strength *= 0.8
            
            # Calculate position size based on signal strength
            base_position_size = 0.05  # 5% base position
            adjusted_position_size = base_position_size * signal_strength
            
            signal = {
                'action': primary_signal['action'],
                'quantity': adjusted_position_size,
                'price': primary_signal['price'],
                'confidence': signal_strength,
                'confluence_score': confluence,
                'trend_direction': trend_direction,
                'volume_confirmed': volume_analysis.get('confirms_signal', True),
                'indicators_summary': self._summarize_indicators(indicators),
                'signal_type': primary_signal.get('signal_type', 'confluence')
            }
            
            logger.debug(f"TA signal: {primary_signal['action']} {adjusted_position_size:.4f} @ {primary_signal['price']:.2f} (conf={signal_strength:.3f})")
            return signal
            
        except Exception as e:
            logger.error(f"TA signal generation failed: {e}")
            return {
                'action': 'HOLD',
                'quantity': 0,
                'price': market_data.price,
                'confidence': 0,
                'reason': 'error'
            }
    
    async def _get_cached_indicators(self, market_data: MarketData) -> Dict[str, Any]:
        """Get cached technical indicators or calculate fresh ones"""
        try:
            market_hash = self._generate_market_hash(market_data)
            cache_key = f"{self.INDICATORS_KEY}:{market_hash}"
            
            cached_indicators = await self.redis_service.get(cache_key)
            
            if cached_indicators:
                indicators_data = json.loads(cached_indicators)
                cache_time = datetime.fromisoformat(indicators_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_indicators"]):
                    return indicators_data['indicators']
            
            # Calculate fresh indicators
            indicators = await self._calculate_technical_indicators(market_data)
            
            # Cache indicators
            indicators_data = {
                'indicators': indicators,
                'timestamp': datetime.now().isoformat(),
                'market_hash': market_hash
            }
            
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_indicators"],
                json.dumps(indicators_data, default=str)
            )
            
            self.cached_indicators = indicators
            return indicators
            
        except Exception as e:
            logger.error(f"Indicators calculation failed: {e}")
            return {}
    
    async def _calculate_technical_indicators(self, market_data: MarketData) -> Dict[str, Any]:
        """Calculate technical indicators (simplified implementation)"""
        try:
            current_price = market_data.price
            volume = market_data.volume
            
            # In a real implementation, you'd fetch historical OHLCV data
            # For now, simulate indicator calculations
            
            # RSI (simplified calculation)
            rsi = 50 + np.random.normal(0, 15)  # Simulate RSI around 50
            rsi = max(0, min(100, rsi))
            
            # MACD (simplified)
            macd_line = np.random.normal(0, 50)
            macd_signal_line = macd_line + np.random.normal(0, 20)
            macd_histogram = macd_line - macd_signal_line
            
            # Moving Averages (simplified)
            sma_20 = current_price * (1 + np.random.normal(0, 0.01))
            sma_50 = current_price * (1 + np.random.normal(0, 0.02))
            ema_21 = current_price * (1 + np.random.normal(0, 0.015))
            
            # Bollinger Bands (simplified)
            bb_middle = sma_20
            bb_std = current_price * 0.02  # 2% standard deviation
            bb_upper = bb_middle + (2 * bb_std)
            bb_lower = bb_middle - (2 * bb_std)
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            
            # Volume indicators
            volume_ma = volume * (1 + np.random.normal(0, 0.1))
            volume_ratio = volume / volume_ma if volume_ma > 0 else 1.0
            
            indicators = {
                'rsi': rsi,
                'macd': {
                    'line': macd_line,
                    'signal': macd_signal_line,
                    'histogram': macd_histogram
                },
                'moving_averages': {
                    'sma_20': sma_20,
                    'sma_50': sma_50,
                    'ema_21': ema_21
                },
                'bollinger_bands': {
                    'upper': bb_upper,
                    'middle': bb_middle,
                    'lower': bb_lower,
                    'position': bb_position
                },
                'volume': {
                    'current': volume,
                    'ma': volume_ma,
                    'ratio': volume_ratio
                },
                'price': current_price,
                'timestamp': datetime.now().isoformat()
            }
            
            return indicators
            
        except Exception as e:
            logger.error(f"Technical indicators calculation error: {e}")
            return {}
    
    async def _calculate_confluence_score(
        self,
        indicators: Dict[str, Any],
        market_data: MarketData
    ) -> float:
        """Calculate confluence score based on indicator agreement"""
        try:
            if not indicators:
                return 0.0
            
            scores = []
            
            # RSI scoring
            rsi = indicators.get('rsi', 50)
            if rsi > self.config["rsi_overbought"]:
                scores.append(-0.8)  # Strong sell signal
            elif rsi > 65:
                scores.append(-0.4)  # Weak sell signal
            elif rsi < self.config["rsi_oversold"]:
                scores.append(0.8)   # Strong buy signal
            elif rsi < 35:
                scores.append(0.4)   # Weak buy signal
            else:
                scores.append(0.0)   # Neutral
            
            # MACD scoring
            macd = indicators.get('macd', {})
            macd_line = macd.get('line', 0)
            macd_signal = macd.get('signal', 0)
            macd_histogram = macd.get('histogram', 0)
            
            if macd_line > macd_signal and macd_histogram > 0:
                scores.append(0.6)   # Bullish
            elif macd_line < macd_signal and macd_histogram < 0:
                scores.append(-0.6)  # Bearish
            else:
                scores.append(0.0)   # Neutral
            
            # Moving Average scoring
            ma = indicators.get('moving_averages', {})
            current_price = indicators.get('price', market_data.price)
            sma_20 = ma.get('sma_20', current_price)
            sma_50 = ma.get('sma_50', current_price)
            
            if current_price > sma_20 > sma_50:
                scores.append(0.7)   # Strong uptrend
            elif current_price > sma_20 and current_price > sma_50:
                scores.append(0.4)   # Uptrend
            elif current_price < sma_20 < sma_50:
                scores.append(-0.7)  # Strong downtrend
            elif current_price < sma_20 and current_price < sma_50:
                scores.append(-0.4)  # Downtrend
            else:
                scores.append(0.0)   # Neutral
            
            # Bollinger Bands scoring
            bb = indicators.get('bollinger_bands', {})
            bb_position = bb.get('position', 0.5)
            
            if bb_position > 0.9:
                scores.append(-0.5)  # Overbought
            elif bb_position < 0.1:
                scores.append(0.5)   # Oversold
            else:
                scores.append(0.0)   # Neutral
            
            # Calculate weighted average (confluence score)
            if scores:
                confluence = np.mean(scores)
                # Normalize to [0, 1] for confidence
                confidence = (confluence + 1) / 2
                return max(0.0, min(1.0, confidence))
            
            return 0.5  # Neutral
            
        except Exception as e:
            logger.error(f"Confluence calculation failed: {e}")
            return 0.5
    
    async def _detect_divergences(
        self,
        indicators: Dict[str, Any],
        market_data: MarketData
    ) -> Dict[str, Any]:
        """Detect price/indicator divergences"""
        try:
            # Simplified divergence detection
            # In production, this would analyze historical price and indicator data
            
            divergences = {
                'rsi_divergence': False,
                'macd_divergence': False,
                'volume_divergence': False,
                'divergence_strength': 0.0,
                'divergence_type': 'none'  # 'bullish', 'bearish', 'none'
            }
            
            # Placeholder for divergence detection logic
            # Would need historical data to implement properly
            
            return divergences
            
        except Exception as e:
            logger.error(f"Divergence detection failed: {e}")
            return {'divergence_strength': 0.0, 'divergence_type': 'none'}
    
    async def _analyze_volume(
        self,
        indicators: Dict[str, Any],
        market_data: MarketData
    ) -> Dict[str, Any]:
        """Analyze volume patterns"""
        try:
            volume_data = indicators.get('volume', {})
            volume_ratio = volume_data.get('ratio', 1.0)
            
            analysis = {
                'volume_surge': volume_ratio > self.config["volume_surge_threshold"],
                'volume_ratio': volume_ratio,
                'confirms_signal': True,  # Default confirmation
                'volume_trend': 'normal'
            }
            
            if volume_ratio > 2.0:
                analysis['volume_trend'] = 'high'
                analysis['confirms_signal'] = True
            elif volume_ratio < 0.5:
                analysis['volume_trend'] = 'low'
                analysis['confirms_signal'] = False  # Low volume reduces signal confidence
            
            return analysis
            
        except Exception as e:
            logger.error(f"Volume analysis failed: {e}")
            return {'confirms_signal': True, 'volume_trend': 'normal'}
    
    async def _analyze_trend(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze overall trend direction and strength"""
        try:
            ma = indicators.get('moving_averages', {})
            current_price = indicators.get('price', 0)
            sma_20 = ma.get('sma_20', current_price)
            sma_50 = ma.get('sma_50', current_price)
            
            if current_price > sma_20 > sma_50:
                direction = 'bullish'
                strength = 0.8
            elif current_price > sma_20 and sma_20 > sma_50 * 0.99:  # Slight uptrend
                direction = 'bullish'
                strength = 0.6
            elif current_price < sma_20 < sma_50:
                direction = 'bearish'
                strength = 0.8
            elif current_price < sma_20 and sma_20 < sma_50 * 1.01:  # Slight downtrend
                direction = 'bearish'
                strength = 0.6
            else:
                direction = 'neutral'
                strength = 0.3
            
            return {
                'direction': direction,
                'strength': strength,
                'ma_alignment': sma_20 > sma_50 if direction != 'neutral' else False
            }
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            return {'direction': 'neutral', 'strength': 0.3}
    
    async def _assess_signal_strength(
        self,
        indicators: Dict[str, Any],
        confluence: float,
        divergences: Dict[str, Any],
        volume_analysis: Dict[str, Any],
        trend_analysis: Dict[str, Any]
    ) -> float:
        """Assess overall signal strength"""
        try:
            strength = confluence  # Start with confluence score
            
            # Apply trend strength modifier
            trend_strength = trend_analysis.get('strength', 0.3)
            strength *= (0.7 + 0.3 * trend_strength)  # Boost for strong trends
            
            # Apply volume confirmation
            if volume_analysis.get('confirms_signal', True):
                strength *= 1.1  # Boost for volume confirmation
            else:
                strength *= 0.9  # Slight reduction for weak volume
            
            # Apply divergence modifier
            divergence_strength = divergences.get('divergence_strength', 0)
            if divergence_strength > 0.5:
                strength *= 1.2  # Boost for strong divergences
            
            return max(0.0, min(1.0, strength))
            
        except Exception as e:
            logger.error(f"Signal strength assessment failed: {e}")
            return confluence
    
    async def _generate_primary_signal(
        self,
        indicators: Dict[str, Any],
        current_price: float
    ) -> Dict[str, Any]:
        """Generate primary trading signal based on indicators"""
        try:
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', {})
            bb = indicators.get('bollinger_bands', {})
            ma = indicators.get('moving_averages', {})
            
            # Decision matrix
            buy_signals = 0
            sell_signals = 0
            
            # RSI signals
            if rsi < self.config["rsi_oversold"]:
                buy_signals += 2
            elif rsi < 40:
                buy_signals += 1
            elif rsi > self.config["rsi_overbought"]:
                sell_signals += 2
            elif rsi > 60:
                sell_signals += 1
            
            # MACD signals
            if macd.get('histogram', 0) > 0 and macd.get('line', 0) > macd.get('signal', 0):
                buy_signals += 1
            elif macd.get('histogram', 0) < 0 and macd.get('line', 0) < macd.get('signal', 0):
                sell_signals += 1
            
            # Bollinger Bands signals
            bb_position = bb.get('position', 0.5)
            if bb_position < 0.2:
                buy_signals += 1
            elif bb_position > 0.8:
                sell_signals += 1
            
            # Moving Average signals
            sma_20 = ma.get('sma_20', current_price)
            if current_price > sma_20:
                buy_signals += 1
            else:
                sell_signals += 1
            
            # Determine action
            if buy_signals > sell_signals and buy_signals >= self.config["confluence_required"]:
                action = 'BUY'
                target_price = current_price
            elif sell_signals > buy_signals and sell_signals >= self.config["confluence_required"]:
                action = 'SELL'
                target_price = current_price
            else:
                action = 'HOLD'
                target_price = current_price
            
            return {
                'action': action,
                'price': target_price,
                'signal_type': 'confluence',
                'buy_signals': buy_signals,
                'sell_signals': sell_signals
            }
            
        except Exception as e:
            logger.error(f"Primary signal generation failed: {e}")
            return {
                'action': 'HOLD',
                'price': current_price,
                'signal_type': 'error'
            }
    
    def _determine_market_regime(self, indicators: Dict[str, Any]) -> str:
        """Determine current market regime"""
        try:
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bollinger_bands', {}).get('position', 0.5)
            volume_ratio = indicators.get('volume', {}).get('ratio', 1.0)
            
            if rsi > 70 and bb_position > 0.8 and volume_ratio > 1.5:
                return 'overbought_trending'
            elif rsi < 30 and bb_position < 0.2 and volume_ratio > 1.5:
                return 'oversold_trending'
            elif 40 < rsi < 60 and 0.3 < bb_position < 0.7 and volume_ratio < 1.2:
                return 'ranging'
            elif volume_ratio > 2.0:
                return 'volatile'
            else:
                return 'normal'
                
        except Exception as e:
            logger.error(f"Market regime determination failed: {e}")
            return 'normal'
    
    def _summarize_indicators(self, indicators: Dict[str, Any]) -> Dict[str, str]:
        """Create summary of indicator states"""
        try:
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bollinger_bands', {}).get('position', 0.5)
            
            summary = {
                'rsi_state': 'neutral',
                'bb_state': 'neutral',
                'trend_state': 'neutral',
                'volume_state': 'normal'
            }
            
            # RSI state
            if rsi > 70:
                summary['rsi_state'] = 'overbought'
            elif rsi < 30:
                summary['rsi_state'] = 'oversold'
            
            # Bollinger Bands state
            if bb_position > 0.8:
                summary['bb_state'] = 'upper_band'
            elif bb_position < 0.2:
                summary['bb_state'] = 'lower_band'
            
            return summary
            
        except Exception as e:
            logger.error(f"Indicator summary failed: {e}")
            return {'state': 'error'}

# Example usage
if __name__ == "__main__":
    # This would be used for testing the enhanced technical analysis strategy
    pass