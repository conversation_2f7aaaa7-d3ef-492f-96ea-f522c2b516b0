#!/usr/bin/env python3
"""
Telegram Performance Monitor for Strategy Ensemble System
Implements Task 2.2.3: Complete Telegram monitoring system deployment

Features:
- Real-time performance notifications
- Ensemble weight change alerts  
- System reliability monitoring
- Performance threshold tracking
- Health check automation
- Alert delivery verification
"""

import asyncio
import json
import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import aiohttp

from app.monitoring.risk_monitor import RiskMonitor, RiskAlert, RiskLevel, AlertType
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

class PerformanceAlertType(Enum):
    """Types of performance alerts"""
    EXECUTION_TIME = "execution_time"
    CACHE_PERFORMANCE = "cache_performance" 
    WEIGHT_CHANGE = "weight_change"
    SYSTEM_HEALTH = "system_health"
    API_RESPONSE = "api_response"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    MEMORY_USAGE = "memory_usage"

@dataclass
class PerformanceMetrics:
    """System performance metrics"""
    avg_execution_time_ms: float
    cache_hit_rate_pct: float
    api_response_time_ms: float
    error_rate_pct: float
    memory_usage_pct: float
    cpu_usage_pct: float
    throughput_ops_sec: float
    active_connections: int
    last_updated: datetime

@dataclass
class WeightChangeAlert:
    """Ensemble weight change alert data"""
    previous_weights: Dict[str, float]
    new_weights: Dict[str, float]
    weight_changes: Dict[str, float]
    max_change: float
    model_version: str
    market_regime: str
    confidence: float
    timestamp: datetime

@dataclass
class TelegramConfig:
    """Telegram notification configuration"""
    bot_token: str
    chat_id: str
    performance_alerts_enabled: bool = True
    weight_change_alerts_enabled: bool = True
    health_check_alerts_enabled: bool = True
    alert_throttle_minutes: int = 5
    performance_threshold_alerts: bool = True

class TelegramPerformanceMonitor:
    """
    Comprehensive Telegram monitoring for Strategy Ensemble System.
    
    Features:
    - Real-time performance tracking and alerts
    - Ensemble weight change monitoring
    - System health verification  
    - Alert delivery performance tracking
    - Comprehensive monitoring dashboard
    """
    
    def __init__(
        self,
        telegram_config: TelegramConfig,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        risk_monitor: Optional[RiskMonitor] = None
    ):
        self.config = telegram_config
        self.redis = redis_service
        self.supabase = supabase_service
        self.risk_monitor = risk_monitor
        
        # Performance thresholds
        self.performance_thresholds = {
            'execution_time_ms': 100,      # <100ms target
            'cache_hit_rate_pct': 70,      # >70% hit rate  
            'api_response_time_ms': 1000,  # <1s API response
            'error_rate_pct': 5,           # <5% error rate
            'memory_usage_pct': 80,        # <80% memory usage
            'cpu_usage_pct': 70,           # <70% CPU usage
            'throughput_ops_sec': 10       # >10 ops/sec minimum
        }
        
        # Weight change thresholds  
        self.weight_change_thresholds = {
            'significant_change': 0.1,     # 10% weight change
            'major_change': 0.2,           # 20% weight change
            'extreme_change': 0.3          # 30% weight change
        }
        
        # Cache keys
        self.PERFORMANCE_METRICS_KEY = "monitor:performance_metrics"
        self.WEIGHT_HISTORY_KEY = "monitor:weight_history"
        self.ALERT_THROTTLE_KEY = "monitor:alert_throttle"
        self.HEALTH_STATUS_KEY = "monitor:health_status"
        
        # State tracking
        self.last_weights = {}
        self.last_performance_alert = {}
        self.performance_history = []
        self.alert_delivery_stats = []
        self.monitoring_active = False
        
        # Performance tracking
        self.notification_times = []
        self.health_check_results = []
        
        logger.info("TelegramPerformanceMonitor initialized")
    
    async def start_monitoring(
        self,
        monitoring_interval: float = 30.0  # 30 seconds
    ) -> None:
        """
        Start comprehensive performance monitoring.
        """
        logger.info(f"Starting Telegram performance monitoring (interval: {monitoring_interval}s)")
        self.monitoring_active = True
        
        # Start monitoring tasks concurrently
        tasks = [
            self._monitor_performance_metrics(monitoring_interval),
            self._monitor_weight_changes(monitoring_interval * 2),  # Check weights every minute
            self._monitor_system_health(monitoring_interval * 4),   # Health check every 2 minutes
            self._cleanup_old_data(monitoring_interval * 20)        # Cleanup every 10 minutes
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Monitoring tasks failed: {e}")
            self.monitoring_active = False
            raise
    
    async def stop_monitoring(self) -> None:
        """Stop monitoring."""
        logger.info("Stopping Telegram performance monitoring")
        self.monitoring_active = False
    
    async def _monitor_performance_metrics(self, interval: float) -> None:
        """Monitor system performance metrics continuously."""
        
        while self.monitoring_active:
            try:
                # Collect performance metrics
                metrics = await self._collect_performance_metrics()
                
                # Check for threshold violations
                await self._check_performance_thresholds(metrics)
                
                # Cache metrics
                await self._cache_performance_metrics(metrics)
                
                # Store analytics  
                if self.supabase:
                    asyncio.create_task(self._store_performance_analytics(metrics))
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Performance monitoring cycle failed: {e}")
                await asyncio.sleep(interval * 2)
    
    async def _monitor_weight_changes(self, interval: float) -> None:
        """Monitor ensemble weight changes."""
        
        while self.monitoring_active:
            try:
                # Get current weights from cache
                current_weights = await self.redis.get_cached_weights()
                
                if current_weights and self.last_weights:
                    # Check for significant weight changes
                    weight_change = await self._analyze_weight_changes(
                        self.last_weights, current_weights
                    )
                    
                    if weight_change:
                        await self._send_weight_change_alert(weight_change)
                
                # Update last weights
                if current_weights:
                    self.last_weights = current_weights.copy()
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Weight monitoring cycle failed: {e}")
                await asyncio.sleep(interval * 2)
    
    async def _monitor_system_health(self, interval: float) -> None:
        """Monitor overall system health."""
        
        while self.monitoring_active:
            try:
                # Perform comprehensive health check
                health_status = await self._perform_health_check()
                
                # Check for health degradation
                await self._check_health_status(health_status)
                
                # Cache health status
                await self._cache_health_status(health_status)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Health monitoring cycle failed: {e}")
                await asyncio.sleep(interval * 2)
    
    async def _collect_performance_metrics(self) -> PerformanceMetrics:
        """Collect comprehensive performance metrics."""
        
        # Get Redis performance stats
        redis_stats = await self.redis.get_cache_stats()
        
        # Get risk monitor performance stats if available
        risk_stats = {}
        if self.risk_monitor:
            risk_stats = self.risk_monitor.get_performance_stats()
        
        # Calculate metrics
        avg_execution_time = risk_stats.get('avg_monitoring_cycle_time_ms', 0)
        cache_hit_rate = redis_stats.get('cache_hit_rate', 0)
        api_response_time = await self._measure_api_response_time()
        error_rate = await self._calculate_error_rate()
        memory_usage, cpu_usage = await self._get_system_resources()
        throughput = await self._calculate_throughput()
        active_connections = redis_stats.get('connected_clients', 0)
        
        return PerformanceMetrics(
            avg_execution_time_ms=avg_execution_time,
            cache_hit_rate_pct=cache_hit_rate,
            api_response_time_ms=api_response_time,
            error_rate_pct=error_rate,
            memory_usage_pct=memory_usage,
            cpu_usage_pct=cpu_usage,
            throughput_ops_sec=throughput,
            active_connections=active_connections,
            last_updated=datetime.now()
        )
    
    async def _check_performance_thresholds(self, metrics: PerformanceMetrics) -> None:
        """Check performance metrics against thresholds."""
        
        violations = []
        
        # Check execution time
        if metrics.avg_execution_time_ms > self.performance_thresholds['execution_time_ms']:
            violations.append({
                'type': PerformanceAlertType.EXECUTION_TIME,
                'message': f"Execution time ({metrics.avg_execution_time_ms:.1f}ms) exceeds target ({self.performance_thresholds['execution_time_ms']}ms)",
                'current': metrics.avg_execution_time_ms,
                'threshold': self.performance_thresholds['execution_time_ms'],
                'severity': 'high' if metrics.avg_execution_time_ms > 200 else 'medium'
            })
        
        # Check cache performance
        if metrics.cache_hit_rate_pct < self.performance_thresholds['cache_hit_rate_pct']:
            violations.append({
                'type': PerformanceAlertType.CACHE_PERFORMANCE,
                'message': f"Cache hit rate ({metrics.cache_hit_rate_pct:.1f}%) below target ({self.performance_thresholds['cache_hit_rate_pct']}%)",
                'current': metrics.cache_hit_rate_pct,
                'threshold': self.performance_thresholds['cache_hit_rate_pct'],
                'severity': 'medium'
            })
        
        # Check API response time
        if metrics.api_response_time_ms > self.performance_thresholds['api_response_time_ms']:
            violations.append({
                'type': PerformanceAlertType.API_RESPONSE,
                'message': f"API response time ({metrics.api_response_time_ms:.1f}ms) exceeds target ({self.performance_thresholds['api_response_time_ms']}ms)",
                'current': metrics.api_response_time_ms,
                'threshold': self.performance_thresholds['api_response_time_ms'],
                'severity': 'high' if metrics.api_response_time_ms > 2000 else 'medium'
            })
        
        # Check error rate
        if metrics.error_rate_pct > self.performance_thresholds['error_rate_pct']:
            violations.append({
                'type': PerformanceAlertType.ERROR_RATE,
                'message': f"Error rate ({metrics.error_rate_pct:.1f}%) exceeds threshold ({self.performance_thresholds['error_rate_pct']}%)",
                'current': metrics.error_rate_pct,
                'threshold': self.performance_thresholds['error_rate_pct'],
                'severity': 'critical' if metrics.error_rate_pct > 10 else 'high'
            })
        
        # Check memory usage
        if metrics.memory_usage_pct > self.performance_thresholds['memory_usage_pct']:
            violations.append({
                'type': PerformanceAlertType.MEMORY_USAGE,
                'message': f"Memory usage ({metrics.memory_usage_pct:.1f}%) exceeds threshold ({self.performance_thresholds['memory_usage_pct']}%)",
                'current': metrics.memory_usage_pct,
                'threshold': self.performance_thresholds['memory_usage_pct'],
                'severity': 'critical' if metrics.memory_usage_pct > 90 else 'high'
            })
        
        # Check throughput
        if metrics.throughput_ops_sec < self.performance_thresholds['throughput_ops_sec']:
            violations.append({
                'type': PerformanceAlertType.THROUGHPUT,
                'message': f"Throughput ({metrics.throughput_ops_sec:.1f} ops/sec) below minimum ({self.performance_thresholds['throughput_ops_sec']} ops/sec)",
                'current': metrics.throughput_ops_sec,
                'threshold': self.performance_thresholds['throughput_ops_sec'],
                'severity': 'medium'
            })
        
        # Send alerts for violations
        for violation in violations:
            if await self._should_send_alert(violation['type']):
                await self._send_performance_alert(violation, metrics)
    
    async def _analyze_weight_changes(
        self,
        previous_weights: Dict[str, float],
        current_weights: Dict[str, float]
    ) -> Optional[WeightChangeAlert]:
        """Analyze weight changes for significant modifications."""
        
        # Calculate weight changes
        weight_changes = {}
        for strategy in current_weights.get('weights', {}):
            old_weight = previous_weights.get('weights', {}).get(strategy, 0)
            new_weight = current_weights.get('weights', {}).get(strategy, 0)
            change = abs(new_weight - old_weight)
            weight_changes[strategy] = change
        
        # Find maximum change
        max_change = max(weight_changes.values()) if weight_changes else 0
        
        # Check if change is significant
        if max_change >= self.weight_change_thresholds['significant_change']:
            return WeightChangeAlert(
                previous_weights=previous_weights.get('weights', {}),
                new_weights=current_weights.get('weights', {}),
                weight_changes=weight_changes,
                max_change=max_change,
                model_version=current_weights.get('model_version', 'unknown'),
                market_regime=current_weights.get('market_regime', 'unknown'),
                confidence=current_weights.get('confidence', 0.0),
                timestamp=datetime.now()
            )
        
        return None
    
    async def _perform_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive system health check."""
        
        health_status = {
            'timestamp': datetime.now(),
            'overall_status': 'healthy',
            'components': {},
            'performance_score': 100,
            'alerts_active': 0
        }
        
        # Check Redis connectivity
        try:
            redis_stats = await self.redis.get_cache_stats()
            health_status['components']['redis'] = {
                'status': 'healthy' if redis_stats else 'unhealthy',
                'response_time_ms': await self._measure_redis_response_time(),
                'connected_clients': redis_stats.get('connected_clients', 0)
            }
        except Exception as e:
            health_status['components']['redis'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['overall_status'] = 'degraded'
            health_status['performance_score'] -= 25
        
        # Check Telegram connectivity
        try:
            telegram_healthy = await self._test_telegram_connectivity()
            health_status['components']['telegram'] = {
                'status': 'healthy' if telegram_healthy else 'unhealthy',
                'bot_responsive': telegram_healthy
            }
            if not telegram_healthy:
                health_status['overall_status'] = 'degraded'
                health_status['performance_score'] -= 20
        except Exception as e:
            health_status['components']['telegram'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
        
        # Check Supabase if available
        if self.supabase:
            try:
                supabase_healthy = await self._test_supabase_connectivity()
                health_status['components']['supabase'] = {
                    'status': 'healthy' if supabase_healthy else 'unhealthy',
                    'database_responsive': supabase_healthy
                }
                if not supabase_healthy:
                    health_status['overall_status'] = 'degraded'
                    health_status['performance_score'] -= 15
            except Exception as e:
                health_status['components']['supabase'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        
        # Check risk monitor if available
        if self.risk_monitor:
            try:
                risk_health = await self.risk_monitor.health_check()
                health_status['components']['risk_monitor'] = risk_health
                health_status['alerts_active'] = risk_health.get('performance_stats', {}).get('active_alerts_count', 0)
                
                if risk_health.get('status') != 'healthy':
                    health_status['overall_status'] = 'degraded'
                    health_status['performance_score'] -= 20
            except Exception as e:
                health_status['components']['risk_monitor'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        
        # Calculate final health score
        if health_status['performance_score'] < 50:
            health_status['overall_status'] = 'unhealthy'
        elif health_status['performance_score'] < 80:
            health_status['overall_status'] = 'degraded'
        
        return health_status
    
    async def _send_performance_alert(
        self,
        violation: Dict[str, Any],
        metrics: PerformanceMetrics
    ) -> None:
        """Send performance threshold violation alert."""
        
        if not self.config.performance_alerts_enabled:
            return
        
        # Format severity emoji
        severity_emojis = {
            'low': '🟢',
            'medium': '🟡', 
            'high': '🟠',
            'critical': '🔴'
        }
        
        emoji = severity_emojis.get(violation['severity'], '⚠️')
        
        # Create alert message
        message = f"{emoji} *Performance Alert*\n\n"
        message += f"{violation['message']}\n\n"
        message += f"Current: `{violation['current']:.2f}`\n"
        message += f"Threshold: `{violation['threshold']:.2f}`\n"
        message += f"Time: `{datetime.now().strftime('%H:%M:%S')}`\n\n"
        
        # Add system overview
        message += "*System Overview:*\n"
        message += f"• Execution Time: `{metrics.avg_execution_time_ms:.1f}ms`\n"
        message += f"• Cache Hit Rate: `{metrics.cache_hit_rate_pct:.1f}%`\n"
        message += f"• API Response: `{metrics.api_response_time_ms:.1f}ms`\n"
        message += f"• Error Rate: `{metrics.error_rate_pct:.1f}%`\n"
        message += f"• Throughput: `{metrics.throughput_ops_sec:.1f} ops/sec`\n"
        
        # Add recommendations
        recommendations = self._get_performance_recommendations(violation['type'])
        if recommendations:
            message += "\n*Recommendations:*\n"
            for i, rec in enumerate(recommendations, 1):
                message += f"{i}. {rec}\n"
        
        # Send alert
        await self._send_telegram_message(message)
        
        # Update alert throttle
        await self._update_alert_throttle(violation['type'])
    
    async def _send_weight_change_alert(self, weight_change: WeightChangeAlert) -> None:
        """Send ensemble weight change alert."""
        
        if not self.config.weight_change_alerts_enabled:
            return
        
        # Determine severity based on max change
        if weight_change.max_change >= self.weight_change_thresholds['extreme_change']:
            emoji = "🚨"
            severity = "EXTREME"
        elif weight_change.max_change >= self.weight_change_thresholds['major_change']:
            emoji = "🟠"
            severity = "MAJOR"
        else:
            emoji = "🟡"
            severity = "SIGNIFICANT"
        
        # Create alert message
        message = f"{emoji} *{severity} Weight Change Alert*\n\n"
        message += f"Model: `{weight_change.model_version}`\n"
        message += f"Market Regime: `{weight_change.market_regime}`\n"
        message += f"Confidence: `{weight_change.confidence:.1%}`\n"
        message += f"Max Change: `{weight_change.max_change:.1%}`\n\n"
        
        # Show weight changes
        message += "*Weight Changes:*\n"
        for strategy, change in weight_change.weight_changes.items():
            old_weight = weight_change.previous_weights.get(strategy, 0)
            new_weight = weight_change.new_weights.get(strategy, 0)
            direction = "↗️" if new_weight > old_weight else "↘️"
            message += f"{direction} {strategy}: `{old_weight:.1%}` → `{new_weight:.1%}` ({change:+.1%})\n"
        
        message += f"\nTime: `{weight_change.timestamp.strftime('%H:%M:%S')}`"
        
        # Send alert
        await self._send_telegram_message(message)
    
    async def _send_health_status_alert(self, health_status: Dict[str, Any]) -> None:
        """Send system health status alert."""
        
        if not self.config.health_check_alerts_enabled:
            return
        
        status = health_status['overall_status']
        score = health_status['performance_score']
        
        # Only send alerts for degraded/unhealthy status
        if status == 'healthy':
            return
        
        # Format status emoji
        status_emojis = {
            'healthy': '✅',
            'degraded': '⚠️',
            'unhealthy': '❌'
        }
        
        emoji = status_emojis.get(status, '⚠️')
        
        # Create alert message
        message = f"{emoji} *System Health Alert*\n\n"
        message += f"Status: `{status.upper()}`\n"
        message += f"Health Score: `{score}/100`\n"
        message += f"Active Alerts: `{health_status['alerts_active']}`\n\n"
        
        # Show component status
        message += "*Component Status:*\n"
        for component, details in health_status['components'].items():
            comp_status = details.get('status', 'unknown')
            comp_emoji = '✅' if comp_status == 'healthy' else '❌'
            message += f"{comp_emoji} {component.title()}: `{comp_status}`\n"
            
            if 'error' in details:
                message += f"   Error: `{details['error'][:50]}...`\n"
        
        message += f"\nTime: `{health_status['timestamp'].strftime('%H:%M:%S')}`"
        
        # Send alert
        await self._send_telegram_message(message)
    
    async def _send_telegram_message(self, message: str) -> bool:
        """Send message via Telegram bot."""
        
        try:
            start_time = time.perf_counter()
            
            url = f"https://api.telegram.org/bot{self.config.bot_token}/sendMessage"
            payload = {
                'chat_id': self.config.chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    delivery_time = (time.perf_counter() - start_time) * 1000
                    
                    if response.status == 200:
                        self.notification_times.append(delivery_time)
                        logger.info(f"Telegram message sent successfully in {delivery_time:.1f}ms")
                        return True
                    else:
                        logger.error(f"Telegram message failed: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    # Helper methods
    
    async def _should_send_alert(self, alert_type: PerformanceAlertType) -> bool:
        """Check if alert should be sent based on throttling."""
        
        throttle_key = f"{self.ALERT_THROTTLE_KEY}:{alert_type.value}"
        last_alert = await self.redis.get(throttle_key)
        
        if last_alert:
            last_time = datetime.fromisoformat(last_alert)
            if datetime.now() - last_time < timedelta(minutes=self.config.alert_throttle_minutes):
                return False
        
        return True
    
    async def _update_alert_throttle(self, alert_type: PerformanceAlertType) -> None:
        """Update alert throttle timestamp."""
        
        throttle_key = f"{self.ALERT_THROTTLE_KEY}:{alert_type.value}"
        await self.redis.setex(
            throttle_key,
            self.config.alert_throttle_minutes * 60,
            datetime.now().isoformat()
        )
    
    async def _measure_api_response_time(self) -> float:
        """Measure API response time."""
        try:
            start_time = time.perf_counter()
            # Test with Telegram API
            url = f"https://api.telegram.org/bot{self.config.bot_token}/getMe"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return (time.perf_counter() - start_time) * 1000
        except:
            return 9999.0  # Very high value to indicate failure
    
    async def _measure_redis_response_time(self) -> float:
        """Measure Redis response time."""
        try:
            start_time = time.perf_counter()
            await self.redis.ping()
            return (time.perf_counter() - start_time) * 1000
        except:
            return 9999.0
    
    async def _calculate_error_rate(self) -> float:
        """Calculate system error rate."""
        # This would typically look at application logs or error counters
        # For now, return a mock value based on system health
        return 0.0  # Placeholder
    
    async def _get_system_resources(self) -> Tuple[float, float]:
        """Get system memory and CPU usage."""
        try:
            import psutil
            memory = psutil.virtual_memory().percent
            cpu = psutil.cpu_percent(interval=1)
            return memory, cpu
        except:
            return 50.0, 50.0  # Default values if psutil not available
    
    async def _calculate_throughput(self) -> float:
        """Calculate system throughput."""
        # This would typically measure operations per second
        # For now, return a mock value
        return 25.0  # Placeholder
    
    async def _test_telegram_connectivity(self) -> bool:
        """Test Telegram bot connectivity."""
        try:
            url = f"https://api.telegram.org/bot{self.config.bot_token}/getMe"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return response.status == 200
        except:
            return False
    
    async def _test_supabase_connectivity(self) -> bool:
        """Test Supabase connectivity."""
        try:
            # Simple test query
            result = await self.supabase.test_connection()
            return bool(result)
        except:
            return False
    
    def _get_performance_recommendations(self, alert_type: PerformanceAlertType) -> List[str]:
        """Get recommendations based on alert type."""
        
        recommendations = {
            PerformanceAlertType.EXECUTION_TIME: [
                "Check for blocking operations",
                "Review algorithm efficiency",
                "Consider optimizing database queries"
            ],
            PerformanceAlertType.CACHE_PERFORMANCE: [
                "Check Redis connectivity",
                "Review cache TTL settings",
                "Monitor cache memory usage"
            ],
            PerformanceAlertType.API_RESPONSE: [
                "Check network connectivity",
                "Review API rate limits",
                "Consider implementing retry logic"
            ],
            PerformanceAlertType.ERROR_RATE: [
                "Review application logs",
                "Check input validation",
                "Monitor external dependencies"
            ],
            PerformanceAlertType.MEMORY_USAGE: [
                "Check for memory leaks",
                "Review data structure usage",
                "Consider garbage collection tuning"
            ]
        }
        
        return recommendations.get(alert_type, ["Review system performance"])
    
    # Cache and analytics methods
    
    async def _cache_performance_metrics(self, metrics: PerformanceMetrics) -> None:
        """Cache performance metrics."""
        try:
            await self.redis.setex(
                self.PERFORMANCE_METRICS_KEY,
                300,  # 5 minutes
                json.dumps(asdict(metrics), default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache performance metrics: {e}")
    
    async def _cache_health_status(self, health_status: Dict[str, Any]) -> None:
        """Cache health status."""
        try:
            await self.redis.setex(
                self.HEALTH_STATUS_KEY,
                600,  # 10 minutes
                json.dumps(health_status, default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache health status: {e}")
    
    async def _store_performance_analytics(self, metrics: PerformanceMetrics) -> None:
        """Store performance analytics in Supabase."""
        if not self.supabase:
            return
            
        try:
            analytics_data = {
                'timestamp': metrics.last_updated.isoformat(),
                'avg_execution_time_ms': metrics.avg_execution_time_ms,
                'cache_hit_rate_pct': metrics.cache_hit_rate_pct,
                'api_response_time_ms': metrics.api_response_time_ms,
                'error_rate_pct': metrics.error_rate_pct,
                'memory_usage_pct': metrics.memory_usage_pct,
                'cpu_usage_pct': metrics.cpu_usage_pct,
                'throughput_ops_sec': metrics.throughput_ops_sec,
                'active_connections': metrics.active_connections
            }
            
            # Store in performance_analytics table
            await self.supabase.store_performance_analytics(analytics_data)
            
        except Exception as e:
            logger.error(f"Failed to store performance analytics: {e}")
    
    async def _cleanup_old_data(self, interval: float) -> None:
        """Clean up old monitoring data."""
        
        while self.monitoring_active:
            try:
                # Clean up old performance history
                if len(self.performance_history) > 1000:
                    self.performance_history = self.performance_history[-500:]
                
                # Clean up old notification times
                if len(self.notification_times) > 1000:
                    self.notification_times = self.notification_times[-500:]
                
                # Clean up old health check results
                if len(self.health_check_results) > 100:
                    self.health_check_results = self.health_check_results[-50:]
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Data cleanup failed: {e}")
                await asyncio.sleep(interval * 2)
    
    async def _check_health_status(self, health_status: Dict[str, Any]) -> None:
        """Check health status and send alerts if needed."""
        
        # Send alert for degraded/unhealthy status
        if health_status['overall_status'] != 'healthy':
            # Check throttling
            if await self._should_send_alert(PerformanceAlertType.SYSTEM_HEALTH):
                await self._send_health_status_alert(health_status)
                await self._update_alert_throttle(PerformanceAlertType.SYSTEM_HEALTH)
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get monitoring performance statistics."""
        
        avg_notification_time = np.mean(self.notification_times) if self.notification_times else 0
        
        return {
            'monitoring_active': self.monitoring_active,
            'notifications_sent': len(self.notification_times),
            'avg_notification_time_ms': round(avg_notification_time, 2),
            'health_checks_performed': len(self.health_check_results),
            'telegram_enabled': self.config.performance_alerts_enabled,
            'weight_monitoring_enabled': self.config.weight_change_alerts_enabled,
            'health_monitoring_enabled': self.config.health_check_alerts_enabled,
            'notification_target_met': (
                sum(1 for t in self.notification_times if t < 5000) /
                max(1, len(self.notification_times)) * 100
            ) if self.notification_times else 100
        }

# Factory function for easy initialization
async def create_telegram_performance_monitor(
    telegram_bot_token: str,
    telegram_chat_id: str,
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    risk_monitor: Optional[RiskMonitor] = None
) -> TelegramPerformanceMonitor:
    """Factory function to create Telegram performance monitor."""
    
    from app.services.mcp.redis_service import RedisService
    
    # Initialize Redis service
    redis_service = RedisService(redis_url)
    await redis_service.connect()
    
    # Initialize Supabase service if credentials provided
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    # Create Telegram configuration
    telegram_config = TelegramConfig(
        bot_token=telegram_bot_token,
        chat_id=telegram_chat_id,
        performance_alerts_enabled=True,
        weight_change_alerts_enabled=True,
        health_check_alerts_enabled=True
    )
    
    return TelegramPerformanceMonitor(
        telegram_config=telegram_config,
        redis_service=redis_service,
        supabase_service=supabase_service,
        risk_monitor=risk_monitor
    )

# Example usage and testing
if __name__ == "__main__":
    async def test_telegram_monitor():
        """Test the Telegram performance monitor."""
        
        # Initialize monitor
        monitor = await create_telegram_performance_monitor(
            telegram_bot_token="**********************************************",
            telegram_chat_id="**********",
            redis_url="redis://localhost:6379"
        )
        
        # Test performance monitoring
        await monitor.start_monitoring(monitoring_interval=10.0)
    
    # Run test
    asyncio.run(test_telegram_monitor())