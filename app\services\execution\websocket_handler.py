"""Websocket handling functionality for the execution service."""
import logging
import asyncio
from decimal import Decimal
from typing import Dict, Optional, Any, Callable, TYPE_CHECKING

from app.database.session_manager import get_managed_session
from app.services.execution.trade_state import ManagedTrade, TradeStatus
from app.services.exchange.binance_client import BinanceExchangeClient

if TYPE_CHECKING:
    from app.repositories.trade_repository import TradeRepository

logger = logging.getLogger(__name__)

class WebsocketHandler:
    """Handles websocket connections and event processing."""

    def __init__(self, exchange_client: BinanceExchangeClient, db_session_factory: Any):
        """Initialize the websocket handler.

        Args:
            exchange_client: The exchange client to use for websocket operations.
            db_session_factory: A callable that returns a new SQLAlchemy AsyncSession.
        """
        self.exchange_client = exchange_client
        self.db_session_factory = db_session_factory
        self.socket_manager = self.exchange_client.get_socket_manager()
        self.listen_key: Optional[str] = None
        self.websocket_task: Optional[asyncio.Task] = None
        self.keepalive_task: Optional[asyncio.Task] = None
        self.trade_update_queue: asyncio.Queue = asyncio.Queue()
        self.active_trades: Dict[str, ManagedTrade] = {}

    async def start_monitoring(self):
        """Start the websocket monitoring and keep-alive tasks."""
        logger.info("Starting websocket monitoring...")
        try:
            # 1. Sync state first
            await self._sync_state_on_startup()

            # 2. Get listen key
            self.listen_key = await self.exchange_client.get_listen_key()
            if not self.listen_key:
                logger.error("Failed to get listen key. Cannot start user stream.")
                return
            logger.info(f"Obtained listen key: {self.listen_key[:5]}...")

            # 3. Start user data stream
            if not self.socket_manager:
                logger.error("Socket manager not initialized.")
                return

            logger.info("Starting Binance User Data Stream...")
            # The start_user_socket method returns the socket connection object
            # We need to run it as a task
            self.websocket_task = asyncio.create_task(
                self.socket_manager.start_user_socket(self._handle_user_stream_message)
            )
            logger.info("User data stream task created.")

            # 4. Start keep-alive task
            self.keepalive_task = asyncio.create_task(self._keep_listen_key_alive())
            logger.info("Listen key keep-alive task created.")

        except Exception as e:
            logger.error(f"Error starting websocket monitoring: {e}", exc_info=True)

    async def stop_monitoring(self):
        """Stop the websocket monitoring and keep-alive tasks."""
        logger.info("Stopping websocket monitoring...")

        # Cancel the websocket task
        if self.websocket_task:
            self.websocket_task.cancel()
            try:
                await self.websocket_task
            except asyncio.CancelledError:
                logger.info("Websocket task cancelled.")
            except Exception as e:
                logger.error(f"Error cancelling websocket task: {e}")
            self.websocket_task = None

        # Cancel the keep-alive task
        if self.keepalive_task:
            self.keepalive_task.cancel()
            try:
                await self.keepalive_task
            except asyncio.CancelledError:
                logger.info("Keep-alive task cancelled.")
            except Exception as e:
                logger.error(f"Error cancelling keep-alive task: {e}")
            self.keepalive_task = None

        logger.info("Websocket monitoring stopped.")

    async def _sync_state_on_startup(self):
        """Synchronize state from database and exchange on startup."""
        logger.info("Synchronizing state on startup...")
        try:
            # 1. Load active trades from DB
            async with get_managed_session(self.db_session_factory) as session:
                    from app.repositories.trade_repository import TradeRepository
                    repo = TradeRepository(session)
                    active_trades_db = await repo.get_active_trades()

            # Convert to dictionary keyed by trade_id
            for trade in active_trades_db:
                try:
                    # Convert SQLAlchemy model to Pydantic model if needed
                    if hasattr(trade, '__table__'):
                        # This is a SQLAlchemy model, convert to dict then to Pydantic
                        trade_dict = {c.name: getattr(trade, c.name) for c in trade.__table__.columns}
                        pydantic_trade = ManagedTrade(**trade_dict)
                        self.active_trades[str(pydantic_trade.trade_id)] = pydantic_trade
                    else:
                        # Already a Pydantic model or other object
                        self.active_trades[str(trade.trade_id)] = trade
                except Exception as e:
                    logger.error(f"Error processing trade from database: {e}")
                    continue

            logger.info(f"Loaded {len(self.active_trades)} active trades from database.")

            # 2. Get open orders from exchange
            open_orders = await self.exchange_client.get_open_orders()
            logger.info(f"Found {len(open_orders)} open orders on exchange.")

            # 3. Reconcile state
            # Process each active trade to check its state
            for trade_id, trade in list(self.active_trades.items()):
                # Only process trades with SL/TP orders placed
                if trade.status == TradeStatus.SLTP_PLACED:
                    await self._reconcile_trade_state(trade)

            # 4. Handle orphaned orders (orders on exchange but not in our active trades)
            exchange_order_ids = {str(order.get('orderId')) for order in open_orders}
            tracked_order_ids = set()

            # Collect all order IDs we're tracking
            for trade in self.active_trades.values():
                if trade.sl_order_id:
                    tracked_order_ids.add(trade.sl_order_id)
                if trade.tp_order_id:
                    tracked_order_ids.add(trade.tp_order_id)

            orphaned_orders = exchange_order_ids - tracked_order_ids

            if orphaned_orders:
                logger.warning(f"Found {len(orphaned_orders)} orphaned orders on exchange: {orphaned_orders}")
                # Optionally cancel orphaned orders
                for order_id in orphaned_orders:
                    # Find the order details in the open_orders list
                    order_details = next((order for order in open_orders if str(order.get('orderId')) == order_id), None)
                    if order_details:
                        symbol = order_details.get('symbol')
                        logger.info(f"Cancelling orphaned order {order_id} for {symbol}")
                        try:
                            await self.exchange_client.cancel_order(symbol=symbol, order_id=order_id)
                            logger.info(f"Successfully cancelled orphaned order {order_id}")
                        except Exception as cancel_e:
                            logger.error(f"Failed to cancel orphaned order {order_id}: {cancel_e}")

            logger.info("State synchronization completed.")

        except Exception as e:
            logger.error(f"Error synchronizing state: {e}", exc_info=True)

    async def _reconcile_trade_state(self, trade: ManagedTrade) -> None:
        """Reconcile the state of a trade with the exchange.

        Args:
            trade: The trade to reconcile.
        """
        try:
            # Skip trades that are already closed
            if trade.status in [TradeStatus.CLOSED_SL, TradeStatus.CLOSED_TP, TradeStatus.CLOSED_MANUAL, TradeStatus.ERROR]:
                return

            # Get open orders for this symbol
            open_orders = await self.exchange_client.get_open_orders(symbol=trade.symbol)
            open_order_ids = {str(order.get('orderId')) for order in open_orders}

            # Check if SL and TP orders are still open
            sl_open = trade.sl_order_id in open_order_ids if trade.sl_order_id else False
            tp_open = trade.tp_order_id in open_order_ids if trade.tp_order_id else False

            # Case 1: Both SL and TP are still open - nothing to do
            if sl_open and tp_open:
                logger.debug(f"Trade {trade.trade_id} has both SL and TP orders open - no action needed")
                return

            # Case 2: SL is missing but TP is open - SL might have been filled
            if not sl_open and tp_open and trade.sl_order_id:
                logger.info(f"Trade {trade.trade_id} has missing SL order {trade.sl_order_id} - checking if filled")
                try:
                    sl_order = await self.exchange_client.get_order(symbol=trade.symbol, order_id=trade.sl_order_id)
                    if sl_order.get('status') == 'FILLED':
                        logger.info(f"SL order {trade.sl_order_id} was filled - updating trade status and cancelling TP")
                        trade.status = TradeStatus.CLOSED_SL
                        await self._cancel_sibling_order_with_retry(
                            symbol=trade.symbol, order_id=trade.tp_order_id, trade_id=str(trade.trade_id)
                        )
                        await self._update_trade_in_db(trade)
                        if str(trade.trade_id) in self.active_trades:
                            del self.active_trades[str(trade.trade_id)]
                        # Notify listeners with enhanced trade details
                        notification = {
                            'trade_id': str(trade.trade_id),
                            'status': TradeStatus.CLOSED_SL.value,
                            'symbol': trade.symbol,
                            'entry_side': trade.entry_side,
                            'entry_price': float(trade.entry_fill_price) if trade.entry_fill_price else None,
                            'entry_qty': float(trade.entry_fill_qty) if trade.entry_fill_qty else None,
                            'sl_price': float(trade.sl_price) if trade.sl_price else None,
                            'tp_price': float(trade.tp_price) if trade.tp_price else None,
                            'timestamp': trade.updated_at.isoformat() if hasattr(trade.updated_at, 'isoformat') else str(trade.updated_at),
                            'exit_price': float(trade.sl_price) if trade.sl_price else None
                        }

                        # Try to get current market price for the symbol
                        try:
                            ticker = await self.exchange_client.get_ticker(trade.symbol)
                            if ticker and 'lastPrice' in ticker:
                                notification['current_price'] = float(ticker['lastPrice'])
                        except Exception as price_e:
                            logger.warning(f"Could not get current price for {trade.symbol}: {price_e}")

                        self.trade_update_queue.put_nowait(notification)
                    else:
                        logger.warning(f"SL order {trade.sl_order_id} is not open but not filled: {sl_order.get('status')}")
                        # Handle other statuses (CANCELED, REJECTED, etc.)
                        trade.status = TradeStatus.ERROR
                        await self._update_trade_in_db(trade)
                except Exception as e:
                    logger.error(f"Error checking SL order {trade.sl_order_id}: {e}")

            # Case 3: TP is missing but SL is open - TP might have been filled
            if not tp_open and sl_open and trade.tp_order_id:
                logger.info(f"Trade {trade.trade_id} has missing TP order {trade.tp_order_id} - checking if filled")
                try:
                    tp_order = await self.exchange_client.get_order(symbol=trade.symbol, order_id=trade.tp_order_id)
                    if tp_order.get('status') == 'FILLED':
                        logger.info(f"TP order {trade.tp_order_id} was filled - updating trade status and cancelling SL")
                        trade.status = TradeStatus.CLOSED_TP
                        await self._cancel_sibling_order_with_retry(
                            symbol=trade.symbol, order_id=trade.sl_order_id, trade_id=str(trade.trade_id)
                        )
                        await self._update_trade_in_db(trade)
                        if str(trade.trade_id) in self.active_trades:
                            del self.active_trades[str(trade.trade_id)]
                        # Notify listeners with enhanced trade details
                        notification = {
                            'trade_id': str(trade.trade_id),
                            'status': TradeStatus.CLOSED_TP.value,
                            'symbol': trade.symbol,
                            'entry_side': trade.entry_side,
                            'entry_price': float(trade.entry_fill_price) if trade.entry_fill_price else None,
                            'entry_qty': float(trade.entry_fill_qty) if trade.entry_fill_qty else None,
                            'sl_price': float(trade.sl_price) if trade.sl_price else None,
                            'tp_price': float(trade.tp_price) if trade.tp_price else None,
                            'timestamp': trade.updated_at.isoformat() if hasattr(trade.updated_at, 'isoformat') else str(trade.updated_at),
                            'exit_price': float(trade.tp_price) if trade.tp_price else None
                        }

                        # Try to get current market price for the symbol
                        try:
                            ticker = await self.exchange_client.get_ticker(trade.symbol)
                            if ticker and 'lastPrice' in ticker:
                                notification['current_price'] = float(ticker['lastPrice'])
                        except Exception as price_e:
                            logger.warning(f"Could not get current price for {trade.symbol}: {price_e}")

                        self.trade_update_queue.put_nowait(notification)
                    else:
                        logger.warning(f"TP order {trade.tp_order_id} is not open but not filled: {tp_order.get('status')}")
                        # Handle other statuses (CANCELED, REJECTED, etc.)
                        trade.status = TradeStatus.ERROR
                        await self._update_trade_in_db(trade)
                except Exception as e:
                    logger.error(f"Error checking TP order {trade.tp_order_id}: {e}")

            # Case 4: Both SL and TP are missing - something went wrong
            if not sl_open and not tp_open and (trade.sl_order_id or trade.tp_order_id):
                logger.warning(f"Trade {trade.trade_id} has both SL and TP orders missing - marking as ERROR")
                trade.status = TradeStatus.ERROR
                await self._update_trade_in_db(trade)

        except Exception as e:
            logger.error(f"Error reconciling trade {trade.trade_id}: {e}", exc_info=True)

    async def _keep_listen_key_alive(self):
        """Keep the listen key alive by sending periodic requests."""
        logger.info("Starting listen key keep-alive task...")

        while True:
            try:
                if not self.listen_key:
                    logger.error("No listen key to keep alive.")
                    return

                logger.debug(f"Keeping listen key alive: {self.listen_key[:5]}...")
                await self.exchange_client.keep_alive_listen_key(self.listen_key)
                logger.debug("Listen key keep-alive request sent successfully.")

                # Binance listen key expires every 60 minutes, keep alive every 30-45 mins
                await asyncio.sleep(30 * 60)  # Sleep for 30 minutes
            except asyncio.CancelledError:
                logger.info("Keep-alive task has been cancelled.")
                break
            except Exception as e:
                logger.error(f"An error occurred in the keep-alive task: {e}", exc_info=True)
                # Wait longer before retrying after an error
                await asyncio.sleep(5 * 60)  # Sleep for 5 minutes before retrying

    async def _handle_user_stream_message(self, msg: Dict[str, Any]):
        """
        Main handler for incoming user stream messages.
        Delegates processing to specific methods based on event type.
        """
        event_type = msg.get('e')
        
        if event_type == 'executionReport':
            await self._process_execution_report(msg)
        elif event_type == 'outboundAccountPosition':
            await self._process_outbound_account_position(msg)
        elif event_type:
            logger.debug(f"Received unhandled event type: {event_type}")

    async def _process_execution_report(self, msg: Dict[str, Any]):
        """Process an execution report message from the user stream."""
        try:
            order_id = str(msg.get('i'))
            trade_to_process = self.active_trades.get(order_id)

            if not trade_to_process:
                # Could be an SL or TP order, find the parent trade
                async with get_managed_session(self.db_session_factory) as session:
                    from app.repositories.trade_repository import TradeRepository
                    repo = TradeRepository(session)
                    trade_to_process = await repo.get_trade_by_sl_or_tp_order_id(order_id)

            if not trade_to_process:
                logger.debug(f"Received execution report for untracked order ID {order_id}. Ignoring.")
                return

            order_status = msg.get('X')
            
            if order_status == 'FILLED':
                is_sl_filled = order_id == trade_to_process.sl_order_id
                is_tp_filled = order_id == trade_to_process.tp_order_id

                if is_sl_filled:
                    logger.info(f"SL order {order_id} for trade {trade_to_process.trade_id} was filled.")
                    trade_to_process.status = TradeStatus.CLOSED_SL
                    sibling_order_id = trade_to_process.tp_order_id
                elif is_tp_filled:
                    logger.info(f"TP order {order_id} for trade {trade_to_process.trade_id} was filled.")
                    trade_to_process.status = TradeStatus.CLOSED_TP
                    sibling_order_id = trade_to_process.sl_order_id
                else: # Entry order filled
                    logger.info(f"Entry order {order_id} for trade {trade_to_process.trade_id} was filled.")
                    # Logic for handling entry fill if needed
                    return
                
                # Cancel the other order
                if sibling_order_id:
                        await self._cancel_sibling_order_with_retry(
                            symbol=trade_to_process.symbol,
                        order_id=sibling_order_id,
                        trade_id=str(trade_to_process.trade_id)
                    )
                
                await self._update_trade_in_db(trade_to_process)
                if str(trade_to_process.trade_id) in self.active_trades:
                    del self.active_trades[str(trade_to_process.trade_id)]

                # Create and queue notification
                notification = {
                    'trade_id': str(trade_to_process.trade_id),
                    'status': trade_to_process.status.value,
                    'symbol': trade_to_process.symbol,
                    'entry_side': trade_to_process.entry_side,
                    'entry_price': float(trade_to_process.entry_fill_price) if trade_to_process.entry_fill_price else None,
                    'entry_qty': float(trade_to_process.entry_fill_qty) if trade_to_process.entry_fill_qty else None,
                    'sl_price': float(trade_to_process.sl_price) if trade_to_process.sl_price else None,
                    'tp_price': float(trade_to_process.tp_price) if trade_to_process.tp_price else None,
                    'timestamp': trade_to_process.updated_at.isoformat() if hasattr(trade_to_process.updated_at, 'isoformat') else str(trade_to_process.updated_at),
                    'exit_price': float(trade_to_process.sl_price if is_sl_filled else trade_to_process.tp_price) if (trade_to_process.sl_price or trade_to_process.tp_price) else None
                }
                self.trade_update_queue.put_nowait(notification)

            elif order_status in ['CANCELED', 'REJECTED', 'EXPIRED']:
                logger.warning(f"Order {order_id} for trade {trade_to_process.trade_id} is {order_status}. Marking trade as ERROR.")
                trade_to_process.status = TradeStatus.ERROR
                await self._update_trade_in_db(trade_to_process)

        except Exception as e:
            logger.error(f"Error processing execution report: {e}", exc_info=True)

    async def _process_outbound_account_position(self, msg: Dict[str, Any]):
        """Process an outbound account position message."""
        try:
            balances = msg.get('B', [])
            for balance in balances:
                asset = balance.get('a')
                free = balance.get('f')
                locked = balance.get('l')
                logger.info(f"Balance update for {asset}: Free={free}, Locked={locked}")
        except Exception as e:
            logger.error(f"Error processing account position: {e}", exc_info=True)

    def get_trade_update_queue(self) -> asyncio.Queue:
        """Get the trade update queue.

        Returns:
            The trade update queue.
        """
        return self.trade_update_queue

    async def _update_trade_in_db(self, trade: ManagedTrade):
        """Updates a trade in the database using the session manager."""
        logger.debug(f"Updating trade {trade.trade_id} in database.")
        try:
            async with get_managed_session(self.db_session_factory) as session:
                    from app.repositories.trade_repository import TradeRepository
                    repo = TradeRepository(session)
                    await repo.update_trade(trade)
            logger.info(f"Successfully updated trade {trade.trade_id} in DB.")
        except Exception as e:
            logger.error(f"Failed to update trade {trade.trade_id} in DB: {e}", exc_info=True)

    def _classify_cancellation_error(self, error: Exception) -> str:
        """Classify cancellation errors into categories for specific handling.

        Args:
            error: The exception that occurred.

        Returns:
            Error category: 'already_done', 'rate_limit', 'network', or 'other'.
        """
        error_message = str(error).lower()

        if any(msg in error_message for msg in ["filled", "canceled", "does not exist", "not found", "already closed"]):
            return "already_done"
        elif any(msg in error_message for msg in ["rate limit", "too many requests"]):
            return "rate_limit"
        elif any(msg in error_message for msg in ["network", "timeout", "connection", "refused", "reset"]):
            return "network"
        else:
            return "other"

    def _update_cancellation_metrics(self, success: bool, attempts: int, duration: float, error_category: str):
        """Update metrics for sibling order cancellation.

        Args:
            success: Whether the cancellation was successful.
            attempts: Number of attempts made.
            duration: Total duration in seconds.
            error_category: Category of the last error encountered.
        """
        # In a real implementation, these would be sent to a metrics service
        # For now, we'll just log them
        if success:
            logger.info(f"Cancellation metrics: success=True, attempts={attempts}, duration={duration:.3f}s")
        else:
            logger.warning(f"Cancellation metrics: success=False, attempts={attempts}, duration={duration:.3f}s, error_category={error_category}")

    async def _cancel_sibling_order_with_retry(self, symbol: str, order_id: str, trade_id: str, max_retries: int = 3, retry_delay: float = 1.0) -> bool:
        """Cancel a sibling order with retry logic and exponential backoff with jitter.

        Args:
            symbol: The trading symbol.
            order_id: The order ID to cancel.
            trade_id: The trade ID for logging.
            max_retries: Maximum number of retry attempts.
            retry_delay: Initial delay between retries in seconds.

        Returns:
            True if cancellation was successful, False otherwise.
        """
        import random
        import time

        # Add metrics tracking
        start_time = time.time()
        attempts = 0
        success = False
        error_category = "none"

        while attempts <= max_retries:
            try:
                logger.info(f"Attempting cancellation of sibling order {order_id} for trade {trade_id} (attempt {attempts + 1}/{max_retries + 1})")
                result = await self.exchange_client.cancel_order(symbol=symbol, order_id=order_id)

                # Check if cancellation was successful
                if result and result.get('status') == 'CANCELED':
                    logger.info(f"Successfully cancelled sibling order {order_id} for trade {trade_id}")
                    success = True
                    duration = time.time() - start_time
                    self._update_cancellation_metrics(success, attempts + 1, duration, error_category)
                    return True
                else:
                    logger.warning(f"Cancellation returned unexpected result for order {order_id}: {result}")
                    # If the order is already filled or doesn't exist, consider it a success
                    if result and (result.get('status') == 'FILLED' or 'not found' in str(result).lower()):
                        logger.info(f"Order {order_id} already filled or not found, considering cancellation successful")
                        success = True
                        duration = time.time() - start_time
                        self._update_cancellation_metrics(success, attempts + 1, duration, "already_done")
                        return True
            except Exception as e:
                # Classify the error
                error_category = self._classify_cancellation_error(e)

                if error_category == "already_done":
                    logger.info(f"Order {order_id} already filled/cancelled: {e}")
                    success = True
                    duration = time.time() - start_time
                    self._update_cancellation_metrics(success, attempts + 1, duration, error_category)
                    return True

                logger.error(f"Failed to cancel sibling order {order_id} for trade {trade_id} (attempt {attempts + 1}/{max_retries + 1}): {e}")

            # Increment retry counter and wait before retrying
            attempts += 1
            if attempts <= max_retries:
                # Calculate exponential backoff with jitter
                # Formula: delay = base_delay * (2 ^ attempt) * (0.5 + random(0, 0.5))
                jitter = 0.5 + random.random() * 0.5  # Random value between 0.5 and 1.0

                # Adjust wait time based on error category
                if error_category == "rate_limit":
                    # For rate limit errors, use longer delays
                    wait_time = retry_delay * (3 ** attempts) * jitter
                    logger.warning(f"Rate limit hit when cancelling order {order_id}. Retrying in {wait_time:.2f}s...")
                elif error_category == "network":
                    # For network errors, use standard exponential backoff
                    wait_time = retry_delay * (2 ** attempts) * jitter
                    logger.warning(f"Network error when cancelling order {order_id}. Retrying in {wait_time:.2f}s...")
                else:
                    # For other errors, use shorter delays
                    wait_time = retry_delay * (1.5 ** attempts) * jitter
                    logger.warning(f"Error cancelling order {order_id}. Retrying in {wait_time:.2f}s...")

                await asyncio.sleep(wait_time)

        logger.error(f"Failed to cancel sibling order {order_id} for trade {trade_id} after {max_retries + 1} attempts")
        duration = time.time() - start_time
        self._update_cancellation_metrics(False, attempts, duration, error_category)
        return False
