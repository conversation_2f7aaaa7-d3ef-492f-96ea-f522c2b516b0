"""Main execution service class."""
import logging
import asyncio
from typing import Dict, Any

from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient
from app.services.execution.order_management import OrderManager
from app.services.execution.trade_management import TradeManager
from app.services.execution.websocket_handler import WebsocketHandler

logger = logging.getLogger(__name__)

class ExecutionService:
    """Service for executing orders on exchanges.

    This service handles the execution of orders, including order placement,
    cancellation, and monitoring. It integrates order management, trade management,
    and websocket handling components.
    """

    def __init__(self, exchange_client: BinanceExchangeClient, settings: Settings, db_session_factory: Any = None):
        """Initialize the execution service with dependencies.

        Args:
            exchange_client: The concrete Binance exchange client instance.
            settings: The application settings instance.
            db_session_factory: A callable that returns a new SQLAlchemy AsyncSession.
        """
        self.exchange_client = exchange_client
        self.settings = settings
        self.db_session_factory = db_session_factory

        # Initialize component managers
        self.order_manager = OrderManager(exchange_client)

        # Initialize WebsocketHandler and TradeManager only if db_session_factory is provided
        if db_session_factory:
            self.websocket_handler = WebsocketHandler(exchange_client, db_session_factory)
            self.trade_manager = TradeManager(self.order_manager, db_session_factory)

            # For backward compatibility, expose these attributes directly
            self.active_trades = self.trade_manager.active_trades
            self.trade_update_queue = self.websocket_handler.get_trade_update_queue()
            self.socket_manager = self.websocket_handler.socket_manager
        else:
            # Create dummy attributes for backward compatibility when db_session_factory is not provided
            logger.warning("ExecutionService initialized without db_session_factory. Some features will be limited.")
            self.websocket_handler = None
            self.trade_manager = None
            self.active_trades = {}
            self.trade_update_queue = asyncio.Queue()
            self.socket_manager = None

        # For backward compatibility, expose these attributes directly
        self.active_orders = self.order_manager.active_orders

        # Set these attributes based on whether websocket_handler is available
        if self.websocket_handler:
            self.trade_update_queue = self.websocket_handler.get_trade_update_queue()
            self.socket_manager = self.websocket_handler.socket_manager
        # trade_update_queue and socket_manager are already set in the else branch above if websocket_handler is None

        self.websocket_task = None
        self.keepalive_task = None

        # Initialize empty positions dictionary for backward compatibility
        self.positions = {}
