---
description: 
globs: 
alwaysApply: true
---

# Cascade AI Assistant - Development Methodology: Test-Driven Development (TDD)
# Version 1.0

## I. Core Principle: Test-Driven Development (TDD)

For all new feature development, bug fixing, and refactoring tasks within this project, Cascade and the USER will adhere to a Test-Driven Development (TDD) approach.

## II. TDD Workflow

The standard TDD cycle (Red-Green-Refactor) MUST be followed:

1.  **RED - Write a Failing Test:**
    *   Before writing any implementation code, write an automated test case that defines a new function or improvement of an existing function.
    *   This test MUST initially fail because the underlying code does not yet exist or is not yet correct.
    *   The test should be specific, testing one piece of functionality.

2.  **GREEN - Write Code to Pass the Test:**
    *   Write the simplest, most straightforward code possible to make the failing test pass.
    *   At this stage, the focus is on getting the test to pass, not on perfect code design.

3.  **REFACTOR - Improve the Code:**
    *   Once the test is passing, refactor the newly written code to improve its structure, readability, and maintainability without changing its external behavior (i.e., all tests should continue to pass).
    *   Remove duplication and ensure the code adheres to project coding standards.

## III. Test Scope and Quality

-   **Unit Tests:** Focus primarily on unit tests for individual functions, methods, and classes.
-   **Integration Tests:** Where appropriate, write integration tests to ensure different parts of the system work together correctly.
-   **Coverage:** Strive for high test coverage, ensuring that all critical paths, edge cases, and common error conditions are tested.
-   **Clarity:** Tests should be clear, readable, and serve as documentation for the code's behavior.
-   **Automation:** All tests should be automated and easily runnable (e.g., via a command like `npm test` or `pytest`).

## IV. Application

-   This TDD approach applies to all languages used in the project (e.g., Python for backend/data processing, TypeScript/JavaScript for frontend).
-   When proposing code changes or implementing features, Cascade should also propose or create the necessary tests as per this TDD protocol.
-   During the PLAN mode, test cases and testing strategy should be explicitly outlined.
-   During the EXECUTE mode, tests should be implemented alongside the functional code.
-   During the REVIEW mode, test results and coverage should be part of the validation.
