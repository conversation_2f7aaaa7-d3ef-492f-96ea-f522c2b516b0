#!/usr/bin/env python3
"""
Comprehensive Binance Testnet API Connectivity and Setup Validation
This script performs full validation of Binance Testnet API for live trading.
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BinanceTestnetValidator:
    """Comprehensive Binance Testnet API validation class"""
    
    def __init__(self):
        self.settings = None
        self.client = None
        self.test_results = {
            'connection_tests': {},
            'api_tests': {},
            'trading_tests': {},
            'performance_metrics': {},
            'errors': [],
            'warnings': []
        }
        self.start_time = None
        
    async def initialize(self):
        """Initialize the validator and settings"""
        try:
            # Load settings
            self.settings = Settings()
            
            # Verify environment variables
            if not self.settings.binance_api_key:
                raise ValueError("BINANCE_API_KEY environment variable not set")
            if not self.settings.binance_api_secret:
                raise ValueError("BINANCE_API_SECRET environment variable not set")
                
            # Create Binance client
            self.client = BinanceExchangeClient(self.settings)
            
            print("=" * 80)
            print("🚀 BINANCE TESTNET API COMPREHENSIVE VALIDATION")
            print("=" * 80)
            print(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
            print(f"Using Testnet: {self.settings.use_testnet}")
            print(f"API Key: {self.settings.binance_api_key[:8]}...")
            print(f"Trading Symbol: {self.settings.trading_symbol}")
            print("=" * 80)
            
            return True
            
        except Exception as e:
            self.test_results['errors'].append(f"Initialization failed: {e}")
            logger.error(f"Initialization failed: {e}")
            return False
    
    async def test_connection_setup(self):
        """Test basic connection and client initialization"""
        print("\n📡 TESTING CONNECTION SETUP")
        print("-" * 50)
        
        test_name = "client_initialization"
        start_time = time.time()
        
        try:
            # Initialize client connection
            await self.client._initialize_client()
            
            if self.client.client:
                elapsed = time.time() - start_time
                self.test_results['connection_tests'][test_name] = {
                    'status': 'PASS',
                    'response_time_ms': round(elapsed * 1000, 2),
                    'details': 'Client initialized successfully'
                }
                print(f"✅ Client initialization: {elapsed:.3f}s")
                return True
            else:
                self.test_results['connection_tests'][test_name] = {
                    'status': 'FAIL',
                    'error': 'Client not initialized'
                }
                print("❌ Client initialization failed")
                return False
                
        except Exception as e:
            elapsed = time.time() - start_time
            self.test_results['connection_tests'][test_name] = {
                'status': 'FAIL',
                'response_time_ms': round(elapsed * 1000, 2),
                'error': str(e)
            }
            print(f"❌ Client initialization failed: {e}")
            return False
    
    async def test_api_connectivity(self):
        """Test basic API connectivity and permissions"""
        print("\n🔗 TESTING API CONNECTIVITY")
        print("-" * 50)
        
        tests = [
            ('ping', self._test_ping),
            ('server_time', self._test_server_time),
            ('exchange_info', self._test_exchange_info),
            ('account_balance', self._test_account_balance)
        ]
        
        passed = 0
        for test_name, test_func in tests:
            try:
                start_time = time.time()
                result = await test_func()
                elapsed = time.time() - start_time
                
                if result['success']:
                    self.test_results['api_tests'][test_name] = {
                        'status': 'PASS',
                        'response_time_ms': round(elapsed * 1000, 2),
                        'details': result.get('details', '')
                    }
                    print(f"✅ {test_name.replace('_', ' ').title()}: {elapsed:.3f}s")
                    passed += 1
                else:
                    self.test_results['api_tests'][test_name] = {
                        'status': 'FAIL',
                        'response_time_ms': round(elapsed * 1000, 2),
                        'error': result.get('error', 'Unknown error')
                    }
                    print(f"❌ {test_name.replace('_', ' ').title()}: {result.get('error', 'Failed')}")
                    
            except Exception as e:
                self.test_results['api_tests'][test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
                print(f"💥 {test_name.replace('_', ' ').title()}: {e}")
        
        print(f"\n📊 API Connectivity: {passed}/{len(tests)} tests passed")
        return passed == len(tests)
    
    async def _test_ping(self):
        """Test basic ping connectivity"""
        try:
            await self.client.client.ping()
            return {'success': True, 'details': 'Ping successful'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_server_time(self):
        """Test server time retrieval"""
        try:
            server_time = await self.client.client.get_server_time()
            server_ts = server_time['serverTime']
            local_ts = int(time.time() * 1000)
            time_diff = abs(server_ts - local_ts)
            
            if time_diff > 5000:  # More than 5 seconds difference
                self.test_results['warnings'].append(f"Large time difference: {time_diff}ms")
            
            return {
                'success': True, 
                'details': f'Server time: {datetime.fromtimestamp(server_ts/1000)}, diff: {time_diff}ms'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_exchange_info(self):
        """Test exchange info retrieval"""
        try:
            exchange_info = await self.client.get_exchange_info()
            symbols = exchange_info.get('symbols', [])
            return {
                'success': True, 
                'details': f'{len(symbols)} symbols available'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_account_balance(self):
        """Test account balance retrieval"""
        try:
            balance = await self.client.get_account_balance()
            non_zero_assets = {k: v for k, v in balance.items() if float(v) > 0}
            return {
                'success': True, 
                'details': f'{len(balance)} total assets, {len(non_zero_assets)} with balance'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_market_data(self):
        """Test real-time market data retrieval"""
        print("\n📈 TESTING MARKET DATA")
        print("-" * 50)
        
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        passed = 0
        
        for symbol in symbols:
            tests = [
                ('ticker', self._test_ticker, symbol),
                ('order_book', self._test_order_book, symbol),
                ('recent_trades', self._test_recent_trades, symbol),
                ('klines', self._test_klines, symbol)
            ]
            
            print(f"\n💰 Testing {symbol}:")
            symbol_passed = 0
            
            for test_name, test_func, test_symbol in tests:
                try:
                    start_time = time.time()
                    result = await test_func(test_symbol)
                    elapsed = time.time() - start_time
                    
                    test_key = f"{symbol}_{test_name}"
                    
                    if result['success']:
                        self.test_results['api_tests'][test_key] = {
                            'status': 'PASS',
                            'response_time_ms': round(elapsed * 1000, 2),
                            'details': result.get('details', '')
                        }
                        print(f"  ✅ {test_name.replace('_', ' ').title()}: {elapsed:.3f}s - {result.get('details', '')}")
                        symbol_passed += 1
                    else:
                        self.test_results['api_tests'][test_key] = {
                            'status': 'FAIL',
                            'response_time_ms': round(elapsed * 1000, 2),
                            'error': result.get('error', 'Unknown error')
                        }
                        print(f"  ❌ {test_name.replace('_', ' ').title()}: {result.get('error', 'Failed')}")
                        
                except Exception as e:
                    test_key = f"{symbol}_{test_name}"
                    self.test_results['api_tests'][test_key] = {
                        'status': 'ERROR',
                        'error': str(e)
                    }
                    print(f"  💥 {test_name.replace('_', ' ').title()}: {e}")
            
            if symbol_passed == len(tests):
                passed += 1
            
            print(f"  📊 {symbol}: {symbol_passed}/{len(tests)} tests passed")
        
        print(f"\n📊 Market Data: {passed}/{len(symbols)} symbols fully tested")
        return passed > 0
    
    async def _test_ticker(self, symbol: str):
        """Test ticker price retrieval"""
        try:
            ticker = await self.client.get_ticker(symbol)
            price = float(ticker['price'])
            return {
                'success': True, 
                'details': f'${price:,.2f}'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_order_book(self, symbol: str):
        """Test order book retrieval"""
        try:
            order_book = await self.client.get_order_book(symbol, limit=10)
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if bids and asks:
                spread = float(asks[0][0]) - float(bids[0][0])
                spread_pct = (spread / float(bids[0][0])) * 100
                return {
                    'success': True, 
                    'details': f'{len(bids)}b/{len(asks)}a, spread: {spread_pct:.4f}%'
                }
            else:
                return {'success': False, 'error': 'Empty order book'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_recent_trades(self, symbol: str):
        """Test recent trades retrieval"""
        try:
            trades = await self.client.get_recent_trades(symbol, limit=10)
            if trades:
                latest_price = float(trades[0]['price'])
                return {
                    'success': True, 
                    'details': f'{len(trades)} trades, latest: ${latest_price:,.2f}'
                }
            else:
                return {'success': False, 'error': 'No recent trades'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_klines(self, symbol: str):
        """Test klines (candlestick) data retrieval"""
        try:
            klines = await self.client.get_historical_klines(
                symbol=symbol, 
                interval='5m', 
                limit=10
            )
            if klines:
                latest_close = float(klines[-1][4])  # Close price
                return {
                    'success': True, 
                    'details': f'{len(klines)} candles, close: ${latest_close:,.2f}'
                }
            else:
                return {'success': False, 'error': 'No klines data'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_trading_environment(self):
        """Test trading environment and permissions"""
        print("\n🎯 TESTING TRADING ENVIRONMENT")
        print("-" * 50)
        
        tests = [
            ('symbol_info', self._test_symbol_info),
            ('open_orders', self._test_open_orders),
            ('open_positions', self._test_open_positions),
            ('trading_permissions', self._test_trading_permissions)
        ]
        
        passed = 0
        for test_name, test_func in tests:
            try:
                start_time = time.time()
                result = await test_func()
                elapsed = time.time() - start_time
                
                if result['success']:
                    self.test_results['trading_tests'][test_name] = {
                        'status': 'PASS',
                        'response_time_ms': round(elapsed * 1000, 2),
                        'details': result.get('details', '')
                    }
                    print(f"✅ {test_name.replace('_', ' ').title()}: {elapsed:.3f}s - {result.get('details', '')}")
                    passed += 1
                else:
                    self.test_results['trading_tests'][test_name] = {
                        'status': 'FAIL',
                        'response_time_ms': round(elapsed * 1000, 2),
                        'error': result.get('error', 'Unknown error')
                    }
                    print(f"❌ {test_name.replace('_', ' ').title()}: {result.get('error', 'Failed')}")
                    
            except Exception as e:
                self.test_results['trading_tests'][test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
                print(f"💥 {test_name.replace('_', ' ').title()}: {e}")
        
        print(f"\n📊 Trading Environment: {passed}/{len(tests)} tests passed")
        return passed > 0
    
    async def _test_symbol_info(self):
        """Test trading symbol information"""
        try:
            symbol_info = await self.client.get_symbol_info(self.settings.trading_symbol)
            if symbol_info:
                status = symbol_info.get('status', 'UNKNOWN')
                base_asset = symbol_info.get('baseAsset', 'UNKNOWN')
                quote_asset = symbol_info.get('quoteAsset', 'UNKNOWN')
                
                # Check trading permissions
                permissions = symbol_info.get('permissions', [])
                can_trade = 'SPOT' in permissions or 'TRD_GRP_004' in permissions
                
                return {
                    'success': True, 
                    'details': f'{base_asset}/{quote_asset}, status: {status}, trading: {can_trade}'
                }
            else:
                return {'success': False, 'error': 'Symbol info not found'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_open_orders(self):
        """Test open orders retrieval"""
        try:
            open_orders = await self.client.get_open_orders(self.settings.trading_symbol)
            return {
                'success': True, 
                'details': f'{len(open_orders)} open orders'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_open_positions(self):
        """Test open positions retrieval (futures specific)"""
        try:
            positions = await self.client.get_open_positions(self.settings.trading_symbol)
            total_positions = len(positions) if positions else 0
            return {
                'success': True, 
                'details': f'{total_positions} open positions'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_trading_permissions(self):
        """Test if API keys have trading permissions"""
        try:
            # Try to get account status
            account_info = await self.client.client.futures_account()
            can_trade = account_info.get('canTrade', False)
            can_withdraw = account_info.get('canWithdraw', False)
            can_deposit = account_info.get('canDeposit', False)
            
            permissions = []
            if can_trade:
                permissions.append('trade')
            if can_withdraw:
                permissions.append('withdraw')
            if can_deposit:
                permissions.append('deposit')
            
            return {
                'success': True, 
                'details': f'Permissions: {", ".join(permissions) if permissions else "none"}'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_performance_baseline(self):
        """Test API performance and establish baseline metrics"""
        print("\n⚡ TESTING PERFORMANCE BASELINE")
        print("-" * 50)
        
        # Test response times for key operations
        operations = [
            ('ticker_btc', lambda: self.client.get_ticker('BTCUSDT')),
            ('ticker_eth', lambda: self.client.get_ticker('ETHUSDT')),
            ('orderbook_btc', lambda: self.client.get_order_book('BTCUSDT', limit=20)),
            ('balance', lambda: self.client.get_account_balance()),
            ('open_orders', lambda: self.client.get_open_orders('BTCUSDT'))
        ]
        
        # Run each operation multiple times to get average
        iterations = 5
        results = {}
        
        for op_name, operation in operations:
            times = []
            success_count = 0
            
            print(f"🔄 Testing {op_name.replace('_', ' ').title()}...")
            
            for i in range(iterations):
                try:
                    start_time = time.time()
                    await operation()
                    elapsed = time.time() - start_time
                    times.append(elapsed * 1000)  # Convert to milliseconds
                    success_count += 1
                    
                    # Small delay between requests to be respectful
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    print(f"  ⚠️  Iteration {i+1} failed: {e}")
            
            if times:
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                success_rate = (success_count / iterations) * 100
                
                results[op_name] = {
                    'avg_response_time_ms': round(avg_time, 2),
                    'min_response_time_ms': round(min_time, 2),
                    'max_response_time_ms': round(max_time, 2),
                    'success_rate': round(success_rate, 1),
                    'total_requests': iterations
                }
                
                print(f"  📈 Avg: {avg_time:.1f}ms, Min: {min_time:.1f}ms, Max: {max_time:.1f}ms, Success: {success_rate:.1f}%")
            else:
                results[op_name] = {
                    'error': 'All requests failed',
                    'success_rate': 0,
                    'total_requests': iterations
                }
                print(f"  ❌ All requests failed")
        
        self.test_results['performance_metrics'] = results
        
        # Calculate overall performance score
        successful_ops = [r for r in results.values() if 'avg_response_time_ms' in r]
        if successful_ops:
            avg_response_time = sum(r['avg_response_time_ms'] for r in successful_ops) / len(successful_ops)
            avg_success_rate = sum(r['success_rate'] for r in successful_ops) / len(successful_ops)
            
            print(f"\n📊 Performance Summary:")
            print(f"  • Average Response Time: {avg_response_time:.1f}ms")
            print(f"  • Average Success Rate: {avg_success_rate:.1f}%")
            
            # Performance assessment
            if avg_response_time < 200 and avg_success_rate > 95:
                performance_grade = "EXCELLENT"
            elif avg_response_time < 500 and avg_success_rate > 90:
                performance_grade = "GOOD"
            elif avg_response_time < 1000 and avg_success_rate > 80:
                performance_grade = "ACCEPTABLE"
            else:
                performance_grade = "POOR"
            
            print(f"  • Performance Grade: {performance_grade}")
            self.test_results['performance_metrics']['overall'] = {
                'avg_response_time_ms': round(avg_response_time, 2),
                'avg_success_rate': round(avg_success_rate, 1),
                'grade': performance_grade
            }
            
            return performance_grade in ['EXCELLENT', 'GOOD', 'ACCEPTABLE']
        
        return False
    
    async def test_rate_limits(self):
        """Test rate limit handling"""
        print("\n🚦 TESTING RATE LIMIT HANDLING")
        print("-" * 50)
        
        try:
            # Make rapid requests to test rate limiting
            rapid_requests = 10
            start_time = time.time()
            success_count = 0
            rate_limit_errors = 0
            
            print(f"🔄 Making {rapid_requests} rapid requests...")
            
            for i in range(rapid_requests):
                try:
                    await self.client.get_ticker('BTCUSDT')
                    success_count += 1
                except Exception as e:
                    if '429' in str(e) or 'rate limit' in str(e).lower():
                        rate_limit_errors += 1
                        print(f"  ⚠️  Rate limit hit on request {i+1}")
                    else:
                        print(f"  ❌ Request {i+1} failed: {e}")
            
            elapsed = time.time() - start_time
            requests_per_second = rapid_requests / elapsed
            
            self.test_results['performance_metrics']['rate_limit_test'] = {
                'total_requests': rapid_requests,
                'successful_requests': success_count,
                'rate_limit_errors': rate_limit_errors,
                'elapsed_time_seconds': round(elapsed, 2),
                'requests_per_second': round(requests_per_second, 2)
            }
            
            print(f"  📈 {success_count}/{rapid_requests} successful")
            print(f"  🚦 {rate_limit_errors} rate limit errors")
            print(f"  ⏱️  {requests_per_second:.1f} requests/second")
            
            if rate_limit_errors > 0:
                self.test_results['warnings'].append(f"Rate limits encountered during rapid testing")
            
            return True
            
        except Exception as e:
            print(f"❌ Rate limit testing failed: {e}")
            return False
    
    async def generate_report(self):
        """Generate comprehensive validation report"""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        print("\n" + "=" * 80)
        print("📋 VALIDATION REPORT")
        print("=" * 80)
        
        # Count test results
        connection_passed = sum(1 for t in self.test_results['connection_tests'].values() if t.get('status') == 'PASS')
        connection_total = len(self.test_results['connection_tests'])
        
        api_passed = sum(1 for t in self.test_results['api_tests'].values() if t.get('status') == 'PASS')
        api_total = len(self.test_results['api_tests'])
        
        trading_passed = sum(1 for t in self.test_results['trading_tests'].values() if t.get('status') == 'PASS')
        trading_total = len(self.test_results['trading_tests'])
        
        total_passed = connection_passed + api_passed + trading_passed
        total_tests = connection_total + api_total + trading_total
        
        # Overall status
        if total_passed == total_tests and len(self.test_results['errors']) == 0:
            overall_status = "✅ PASS"
        elif total_passed > total_tests * 0.8:  # 80% pass rate
            overall_status = "⚠️  PARTIAL"
        else:
            overall_status = "❌ FAIL"
        
        print(f"Overall Status: {overall_status}")
        print(f"Total Tests: {total_passed}/{total_tests} passed")
        print(f"Total Time: {total_time:.2f} seconds")
        print(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
        
        print(f"\n📊 Test Categories:")
        print(f"  • Connection Tests: {connection_passed}/{connection_total}")
        print(f"  • API Tests: {api_passed}/{api_total}")
        print(f"  • Trading Tests: {trading_passed}/{trading_total}")
        
        # Performance summary
        if 'overall' in self.test_results['performance_metrics']:
            perf = self.test_results['performance_metrics']['overall']
            print(f"\n⚡ Performance:")
            print(f"  • Average Response Time: {perf['avg_response_time_ms']}ms")
            print(f"  • Success Rate: {perf['avg_success_rate']}%")
            print(f"  • Grade: {perf['grade']}")
        
        # Warnings and errors
        if self.test_results['warnings']:
            print(f"\n⚠️  Warnings ({len(self.test_results['warnings'])}):")
            for warning in self.test_results['warnings']:
                print(f"  • {warning}")
        
        if self.test_results['errors']:
            print(f"\n❌ Errors ({len(self.test_results['errors'])}):")
            for error in self.test_results['errors']:
                print(f"  • {error}")
        
        print("\n" + "=" * 80)
        
        # Save detailed report to file
        report_filename = f"binance_testnet_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'overall_status': overall_status,
                'total_passed': total_passed,
                'total_tests': total_tests,
                'total_time_seconds': total_time,
                'test_results': self.test_results,
                'settings': {
                    'use_testnet': self.settings.use_testnet,
                    'trading_symbol': self.settings.trading_symbol,
                    'api_key_prefix': self.settings.binance_api_key[:8] if self.settings.binance_api_key else None
                }
            }, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: {report_filename}")
        
        return overall_status == "✅ PASS"
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.client and self.client.client:
                await self.client.close_client()
                print("\n🧹 Client connection closed successfully")
        except Exception as e:
            print(f"\n⚠️  Error during cleanup: {e}")

async def main():
    """Main validation function"""
    validator = BinanceTestnetValidator()
    
    try:
        validator.start_time = time.time()
        
        # Initialize
        if not await validator.initialize():
            print("❌ Initialization failed. Exiting.")
            return False
        
        # Run all validation tests
        tests = [
            validator.test_connection_setup(),
            validator.test_api_connectivity(),
            validator.test_market_data(),
            validator.test_trading_environment(),
            validator.test_performance_baseline(),
            validator.test_rate_limits()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # Check for exceptions
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                validator.test_results['errors'].append(f"Test {i} failed with exception: {result}")
        
        # Generate final report
        success = await validator.generate_report()
        
        return success
        
    except Exception as e:
        print(f"\n💥 Validation failed with critical error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await validator.cleanup()

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)