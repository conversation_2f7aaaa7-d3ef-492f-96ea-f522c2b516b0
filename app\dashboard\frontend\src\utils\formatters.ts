type StatusColor = 'default' | 'primary' | 'info' | 'error' | 'success' | 'warning';

export const getStatusColor = (status: string): StatusColor => {
  const colors: { [key: string]: StatusColor } = {
    PENDING_ENTRY: 'default',
    ENTRY_FILLED: 'primary',
    SLTP_PLACED: 'info',
    CLOSED_SL: 'warning',
    CLOSED_TP: 'success',
    CLOSED_MANUAL: 'warning',
    ERROR: 'error',
  };
  return colors[status] || 'default';
};

export const formatPrice = (price: number | null | undefined): string => {
  if (price === null || price === undefined) return 'N/A';
  return price.toFixed(Math.max(2, (price.toString().split('.')[1] || '').length));
};

export const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
}; 