"""Utility functions for Binance API client."""
import hmac
import hashlib
import time
import requests
from collections import OrderedDict
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def generate_signature(api_secret: str, query_string: str) -> str:
    """Generate a signature for the API request.
    
    Args:
        api_secret: API secret key
        query_string: Query string to sign
        
    Returns:
        Signature as a hex string
    """
    # Handle both string and bytes api_secret
    secret = api_secret
    if isinstance(secret, str):
        secret = secret.encode('utf-8')
    
    return hmac.new(
        secret,
        query_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

def prepare_params(params: Optional[OrderedDict] = None, 
                  time_offset: int = 0, 
                  recv_window: int = 60000) -> OrderedDict:
    """Prepare parameters for API requests, adding timestamp and recvWindow.
    
    Args:
        params: Optional OrderedDict of parameters
        time_offset: Time offset in milliseconds
        recv_window: Receive window in milliseconds
        
    Returns:
        OrderedDict with required parameters added
    """
    if params is None:
        params = OrderedDict()
    
    # Convert all parameter values to strings
    for key, value in list(params.items()):
        if value is not None:
            params[key] = str(value)
    
    # Add timestamp with time offset adjustment
    if 'timestamp' not in params:
        params['timestamp'] = str(int(time.time() * 1000) + time_offset)
        
    # Add recvWindow if not already present
    if 'recvWindow' not in params:
        params['recvWindow'] = str(recv_window)
        
    return params

def get_server_time(base_url: str) -> int:
    """Get server time from Binance.
    
    Args:
        base_url: Base URL for the Binance API
        
    Returns:
        int: Server time in milliseconds
    """
    response = requests.get(f"{base_url}/fapi/v1/time")
    response.raise_for_status()
    return response.json()["serverTime"]

def sync_time(base_url: str) -> int:
    """Synchronize client time with Binance server time.
    
    Args:
        base_url: Base URL for the Binance API
        
    Returns:
        int: Time offset in milliseconds
    """
    response = requests.get(f"https://api.binance.com/api/v3/time")
    response.raise_for_status()
    
    server_time = response.json()['serverTime']
    local_time = int(time.time() * 1000)
    time_offset = server_time - local_time
    
    logger.info(f"Time synchronized with Binance server. Offset: {time_offset} ms")
    return time_offset
