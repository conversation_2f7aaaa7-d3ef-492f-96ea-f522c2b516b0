#!/usr/bin/env python
"""
Database initialization script.

This script initializes the PostgreSQL database and creates all the necessary tables.
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import init_db, init_async_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

logger = logging.getLogger(__name__)

def main():
    """Initialize the database."""
    # Load environment variables
    load_dotenv()
    
    logger.info("Initializing database...")
    
    # Initialize the database
    init_db()
    
    logger.info("Database initialization complete.")

async def async_main():
    """Initialize the database asynchronously."""
    # Load environment variables
    load_dotenv()
    
    logger.info("Initializing database asynchronously...")
    
    # Initialize the database asynchronously
    await init_async_db()
    
    logger.info("Async database initialization complete.")

if __name__ == "__main__":
    # Check if we should use async initialization
    use_async = "--async" in sys.argv
    
    if use_async:
        asyncio.run(async_main())
    else:
        main()
