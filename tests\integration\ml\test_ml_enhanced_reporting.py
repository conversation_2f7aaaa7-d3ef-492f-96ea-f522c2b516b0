#!/usr/bin/env python3
"""
Test ML-Enhanced Session Reporting Implementation
Tests the comprehensive ML-enhanced session reporting functionality.
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.abspath('.'))

from app.services.auto_trading_controller import AutoTradingController, SessionPerformance, TradingParameters, Trade
from app.utils.ml_analytics import MLAnalyticsEngine

async def test_ml_enhanced_session_performance():
    """Test the enhanced SessionPerformance class with ML metrics"""
    print("=== Testing Enhanced SessionPerformance Class ===")
    
    try:
        # Create enhanced session performance instance
        performance = SessionPerformance()
        
        # Test ML metrics initialization
        assert hasattr(performance, 'ml_model_accuracy')
        assert hasattr(performance, 'ml_model_confidence')
        assert hasattr(performance, 'ml_vs_traditional_performance')
        assert hasattr(performance, 'feature_importance_current')
        assert hasattr(performance, 'ml_training_cost')
        assert hasattr(performance, 'ml_decisions_count')
        
        print("✅ SessionPerformance class enhanced with ML metrics")
        
        # Test default values
        assert performance.ml_model_accuracy == 0.0
        assert performance.ml_vs_traditional_performance == {"ml": 0.0, "traditional": 0.0, "combined": 0.0}
        assert performance.ml_confidence_buckets == {"high": 0, "medium": 0, "low": 0}
        
        print("✅ Default ML metric values initialized correctly")
        
        # Test setting ML metrics
        performance.ml_model_accuracy = 0.85
        performance.ml_model_confidence = 0.75
        performance.ml_training_cost = 100.0
        performance.ml_inference_cost = 50.0
        performance.ml_total_cost = 150.0
        
        assert performance.ml_model_accuracy == 0.85
        assert performance.ml_total_cost == 150.0
        
        print("✅ ML metrics can be set and retrieved correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing SessionPerformance: {e}")
        return False

async def test_ml_analytics_engine():
    """Test the MLAnalyticsEngine functionality"""
    print("\n=== Testing MLAnalyticsEngine ===")
    
    try:
        # Create ML analytics engine
        ml_engine = MLAnalyticsEngine()
        
        # Create mock session data
        mock_session = {
            "id": "test_session_001",
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "status": "active",
            "performance": {
                "ml_model_accuracy": 0.82,
                "ml_model_confidence": 0.78,
                "ml_decisions_count": 100,
                "ml_decisions_correct": 82,
                "ml_decisions_profitable": 75,
                "ml_training_cost": 500.0,
                "ml_inference_cost": 200.0,
                "ml_total_cost": 700.0,
                "ml_roi": 0.15,
                "ml_vs_traditional_performance": {"ml": 0.12, "traditional": 0.08, "combined": 0.10},
                "feature_importance_current": {"feature1": 0.3, "feature2": 0.25, "feature3": 0.2},
                "ml_confidence_buckets": {"high": 40, "medium": 35, "low": 25}
            },
            "trades": [
                {"id": "trade1", "strategy": "ml_strategy", "pnl": 50.0, "confidence": 0.85, "timestamp": datetime.now().isoformat()},
                {"id": "trade2", "strategy": "traditional_strategy", "pnl": 30.0, "confidence": 0.6, "timestamp": datetime.now().isoformat()},
                {"id": "trade3", "strategy": "ml_ensemble", "pnl": 75.0, "confidence": 0.9, "timestamp": datetime.now().isoformat()}
            ]
        }
        
        # Test ML performance analysis
        ml_analysis = await ml_engine.analyze_ml_performance(mock_session)
        
        assert "ml_model_metrics" in ml_analysis
        assert "confidence_analysis" in ml_analysis
        assert "cost_analysis" in ml_analysis
        
        print("✅ ML performance analysis completed successfully")
        
        # Test strategy correlation analysis
        correlation_analysis = await ml_engine.analyze_strategy_correlation(mock_session)
        
        assert "correlation_matrix" in correlation_analysis or "placeholder" in correlation_analysis
        
        print("✅ Strategy correlation analysis completed")
        
        # Test market impact analysis
        market_analysis = await ml_engine.analyze_market_impact(mock_session)
        
        assert "market_regime_analysis" in market_analysis or "placeholder" in market_analysis
        
        print("✅ Market impact analysis completed")
        
        # Test cost-benefit analysis
        cost_benefit_analysis = await ml_engine.analyze_cost_benefit(mock_session)
        
        assert "cost_summary" in cost_benefit_analysis or "placeholder" in cost_benefit_analysis
        
        print("✅ Cost-benefit analysis completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing MLAnalyticsEngine: {e}")
        return False

async def test_auto_trading_controller_ml_methods():
    """Test the AutoTradingController ML methods"""
    print("\n=== Testing AutoTradingController ML Methods ===")
    
    try:
        # Create controller instance
        controller = AutoTradingController()
        
        # Create a mock session for testing
        await controller.start_session(
            symbols=["BTCUSDT"],
            initial_balance=10000.0,
            risk_per_trade=0.02
        )
        
        print("✅ Trading session started successfully")
        
        # Test updating ML performance metrics
        result = await controller.update_ml_performance_metrics(
            model_accuracy=0.85,
            model_confidence=0.78,
            model_version="v1.2.3",
            drift_score=0.15,
            feature_importance={"price_trend": 0.3, "volume": 0.25, "volatility": 0.2},
            training_cost=250.0,
            inference_cost=100.0,
            wandb_run_id="run_123",
            wandb_experiment_name="crypto_ml_experiment"
        )
        
        assert result["success"] == True
        assert "updated_metrics" in result
        
        print("✅ ML performance metrics updated successfully")
        
        # Test tracking ML decisions
        decision_result = await controller.track_ml_decision(
            prediction="BUY",
            confidence=0.85,
            actual_outcome="BUY",
            profitable=True,
            strategy_name="ml_ensemble"
        )
        
        assert decision_result["success"] == True
        assert "decision_id" in decision_result
        
        print("✅ ML decision tracked successfully")
        
        # Test updating ML vs traditional performance
        perf_result = await controller.update_ml_vs_traditional_performance(
            ml_return=0.15,
            traditional_return=0.08,
            combined_return=0.12
        )
        
        assert perf_result["success"] == True
        assert "performance_comparison" in perf_result
        
        print("✅ ML vs traditional performance updated successfully")
        
        # Test getting ML session analytics
        analytics_result = await controller.get_ml_session_analytics()
        
        assert analytics_result["success"] == True
        assert "ml_performance_analysis" in analytics_result
        assert "summary" in analytics_result
        
        print("✅ ML session analytics retrieved successfully")
        
        # Stop the session
        await controller.stop_session()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing AutoTradingController ML methods: {e}")
        return False

async def test_session_reports_routes():
    """Test the enhanced session reports functionality"""
    print("\n=== Testing Session Reports Routes ===")
    
    try:
        # Import the enhanced analytics functions
        from app.api.routes.session_reports_routes import (
            _generate_ml_performance_analysis,
            _generate_strategy_correlation_analysis,
            _generate_market_impact_analysis,
            _generate_cost_benefit_analysis
        )
        
        # Create mock session data
        mock_session = {
            "id": "test_session_routes",
            "start_time": datetime.now().isoformat(),
            "performance": {
                "ml_model_accuracy": 0.82,
                "ml_model_confidence": 0.78,
                "ml_decisions_count": 150,
                "ml_training_cost": 300.0,
                "ml_inference_cost": 150.0,
                "ml_total_cost": 450.0,
                "strategy_performance": {"ml_strategy": 0.15, "grid_strategy": 0.08},
                "strategy_weights": {"ml_strategy": 0.6, "grid_strategy": 0.4}
            },
            "trades": [
                {"strategy": "ml_strategy", "pnl": 100.0, "confidence": 0.85},
                {"strategy": "grid_strategy", "pnl": 50.0, "confidence": 0.6}
            ]
        }
        
        # Test ML performance analysis
        ml_analysis = await _generate_ml_performance_analysis(mock_session)
        assert "model_performance" in ml_analysis or "error" in ml_analysis
        print("✅ ML performance analysis route works")
        
        # Test strategy correlation analysis
        correlation_analysis = await _generate_strategy_correlation_analysis(mock_session)
        assert "strategy_statistics" in correlation_analysis or "error" in correlation_analysis
        print("✅ Strategy correlation analysis route works")
        
        # Test market impact analysis
        market_analysis = await _generate_market_impact_analysis(mock_session)
        assert "temporal_analysis" in market_analysis or "error" in market_analysis
        print("✅ Market impact analysis route works")
        
        # Test cost-benefit analysis
        cost_analysis = await _generate_cost_benefit_analysis(mock_session)
        assert "cost_breakdown" in cost_analysis or "error" in cost_analysis
        print("✅ Cost-benefit analysis route works")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing session reports routes: {e}")
        return False

async def run_comprehensive_test():
    """Run comprehensive test of ML-enhanced session reporting"""
    print("🚀 Starting Comprehensive ML-Enhanced Session Reporting Test")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("=" * 70)
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Enhanced SessionPerformance Class", test_ml_enhanced_session_performance),
        ("MLAnalyticsEngine", test_ml_analytics_engine),
        ("AutoTradingController ML Methods", test_auto_trading_controller_ml_methods),
        ("Session Reports Routes", test_session_reports_routes)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 Running: {test_name}")
            result = await test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            test_results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! ML-enhanced session reporting is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    # Run the comprehensive test
    success = asyncio.run(run_comprehensive_test())
    sys.exit(0 if success else 1)