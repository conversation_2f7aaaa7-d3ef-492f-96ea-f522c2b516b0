import { test, expect } from '@playwright/test';

test.describe('Login Flow Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the login page
    await page.goto('http://localhost:3000');
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // Verify we're on login page
    await expect(page).toHaveURL(/.*\/login/);
    await expect(page).toHaveTitle('Crypto Trading Bot Dashboard');
    
    // Check login form elements
    await expect(page.locator('h1')).toContainText('Crypto Trading Bot');
    await expect(page.locator('h2')).toContainText('Login');
    
    // Fill in credentials (should be pre-filled)
    const usernameInput = page.getByRole('textbox', { name: 'Username' });
    const passwordInput = page.getByRole('textbox', { name: 'Password' });
    
    await expect(usernameInput).toHaveValue('admin');
    await expect(passwordInput).toHaveValue('admin123');
    
    // Click login button
    await page.getByRole('button', { name: 'Login' }).click();
    
    // Verify redirect to trading dashboard
    await expect(page).toHaveURL(/.*\/trading/);
    
    // Verify trading dashboard elements are visible
    await expect(page.locator('h1')).toContainText('Automated Trading Control');
    await expect(page.getByText('Trading Dashboard')).toBeVisible();
    await expect(page.getByText('Auto Trading')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Train ML Model' })).toBeVisible();
    
    // Verify trading tables are present
    await expect(page.getByText('Active Trades')).toBeVisible();
    await expect(page.getByText('Recent Trades')).toBeVisible();
  });

  test('should show password toggle functionality', async ({ page }) => {
    await expect(page).toHaveURL(/.*\/login/);
    
    // Check password visibility toggle
    const passwordToggle = page.getByRole('button', { name: 'toggle password visibility' });
    await expect(passwordToggle).toBeVisible();
    
    // Test password toggle (implementation may vary)
    await passwordToggle.click();
  });

  test('should have responsive login form', async ({ page }) => {
    await expect(page).toHaveURL(/.*\/login/);
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('h1')).toContainText('Crypto Trading Bot');
    await expect(page.getByRole('button', { name: 'Login' })).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toContainText('Crypto Trading Bot');
    await expect(page.getByRole('button', { name: 'Login' })).toBeVisible();
  });
});