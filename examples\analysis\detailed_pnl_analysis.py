#!/usr/bin/env python3
"""
Detailed PnL Analysis with Full Precision
Check the actual profit/loss from trades with full decimal precision.
"""

import json
import asyncio
from binance.client import Client
from dotenv import load_dotenv
import os

async def analyze_detailed_pnl():
    """Analyze PnL with full precision"""
    print("=" * 80)
    print("🔍 DETAILED PnL ANALYSIS WITH FULL PRECISION")
    print("=" * 80)
    
    # Load trading results
    try:
        with open('ensemble_demo_results.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ Results file not found")
        return
    
    # Extract balance information
    initial_balance = data['demo_summary']['initial_balance']
    final_balance = data['demo_summary']['final_balance']
    balance_change = final_balance - initial_balance
    
    print(f"💰 ACCOUNT BALANCE ANALYSIS:")
    print(f"{'Initial Balance:':<25} ${initial_balance:.8f} USDT")
    print(f"{'Final Balance:':<25} ${final_balance:.8f} USDT")
    print(f"{'Net Balance Change:':<25} ${balance_change:.8f} USDT")
    
    if balance_change < 0:
        print(f"📉 Loss: ${abs(balance_change):.8f} USDT ({abs(balance_change/initial_balance)*100:.6f}%)")
    elif balance_change > 0:
        print(f"📈 Profit: ${balance_change:.8f} USDT ({balance_change/initial_balance*100:.6f}%)")
    else:
        print("📊 No net change")
    
    # Analyze individual trade PnL
    print(f"\n📋 INDIVIDUAL TRADE PnL (Full Precision):")
    print("-" * 80)
    print(f"{'Trade#':<8} {'Strategy':<12} {'Side':<6} {'Entry':<12} {'Exit':<12} {'PnL (USDT)':<15}")
    print("-" * 80)
    
    total_realized_pnl = 0
    profitable_trades = 0
    losing_trades = 0
    
    for trade in data['trades']:
        if trade.get('status') == 'CLOSED':
            trade_num = trade.get('trade_number', 0)
            strategy = trade.get('strategy', '').title()
            signal = trade.get('signal', '')
            entry_price = trade.get('price', 0)
            exit_price = trade.get('close_price', 0)
            pnl = trade.get('realized_pnl', 0)
            
            total_realized_pnl += pnl
            
            if pnl > 0:
                profitable_trades += 1
                pnl_str = f"+${pnl:.8f}"
            elif pnl < 0:
                losing_trades += 1
                pnl_str = f"-${abs(pnl):.8f}"
            else:
                pnl_str = f"${pnl:.8f}"
            
            print(f"{trade_num:<8} {strategy:<12} {signal:<6} ${entry_price:<11.2f} ${exit_price:<11.2f} {pnl_str:<15}")
    
    print("-" * 80)
    print(f"{'TOTAL REALIZED PnL:':<65} ${total_realized_pnl:.8f}")
    
    # Trade statistics
    total_closed = profitable_trades + losing_trades
    win_rate = (profitable_trades / total_closed * 100) if total_closed > 0 else 0
    
    print(f"\n📊 TRADE STATISTICS:")
    print(f"{'Total Closed Trades:':<25} {total_closed}")
    print(f"{'Profitable Trades:':<25} {profitable_trades}")
    print(f"{'Losing Trades:':<25} {losing_trades}")
    print(f"{'Win Rate:':<25} {win_rate:.2f}%")
    
    # Check for discrepancies
    print(f"\n🔍 VERIFICATION:")
    discrepancy = abs(total_realized_pnl - balance_change)
    print(f"{'Total Realized PnL:':<25} ${total_realized_pnl:.8f} USDT")
    print(f"{'Account Balance Change:':<25} ${balance_change:.8f} USDT")
    print(f"{'Discrepancy:':<25} ${discrepancy:.8f} USDT")
    
    if discrepancy > 0.********:
        print(f"⚠️  DISCREPANCY DETECTED: ${discrepancy:.8f} USDT")
        print("   This could be due to trading fees not captured in PnL calculation")
    else:
        print("✅ PnL matches account balance change")
    
    # Now get real account balance to verify
    print(f"\n💳 REAL ACCOUNT VERIFICATION:")
    try:
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        client = Client(api_key, api_secret, testnet=True)
        current_account = client.futures_account()
        current_balance = float(current_account['availableBalance'])
        
        print(f"{'Current Live Balance:':<25} ${current_balance:.8f} USDT")
        
        # Calculate the actual total change
        actual_change = current_balance - initial_balance
        print(f"{'Actual Total Change:':<25} ${actual_change:.8f} USDT")
        
        if abs(actual_change) > abs(balance_change):
            additional_change = actual_change - balance_change
            print(f"{'Additional Change:':<25} ${additional_change:.8f} USDT")
            print("   (Possibly from fees or other trades)")
        
    except Exception as e:
        print(f"❌ Could not verify live balance: {e}")

async def main():
    """Main analysis function"""
    await analyze_detailed_pnl()

if __name__ == "__main__":
    asyncio.run(main())