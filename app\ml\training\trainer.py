"""
Trainer module for ML weight optimization.

This module contains functions for training ML models for strategy weight
optimization.
"""

import logging
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import optuna
from datetime import datetime

from app.ml.models.reinforcement_learning import RLWeightOptimizer
from app.ml.data_collector import DataCollector
from app.ml.feature_engineering import FeatureEngineer
from app.ml.training.environment import WeightOptimizationEnv

logger = logging.getLogger(__name__)

class ModelTrainer:
    """Trains ML models for strategy weight optimization."""

    def __init__(self, exchange_client, db_client=None, model_path: str = "models/weight_optimizer"):
        """Initialize the ModelTrainer.

        Args:
            exchange_client: Client for fetching market data from exchange
            db_client: Client for database operations (optional)
            model_path: Path to save/load the model
        """
        self.exchange_client = exchange_client
        self.db_client = db_client
        self.model_path = model_path
        self.data_collector = DataCollector(exchange_client, db_client)
        self.feature_engineer = FeatureEngineer()
        self.strategy_names = ['grid', 'technical_analysis', 'trend_following']
        self.logger = logging.getLogger(__name__)

    async def train_model(self, symbol: str, timeframe: str, lookback_days: int = 90,
                         window_size: int = 10, total_timesteps: int = 100000,
                         hyperparameters: Optional[Dict[str, Any]] = None) -> Tuple[RLWeightOptimizer, Dict[str, float]]:
        """Train an ML model for weight optimization.

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for training data
            window_size: Size of the observation window
            total_timesteps: Number of timesteps to train for
            hyperparameters: Dictionary of hyperparameters (optional)

        Returns:
            Tuple of (trained_model, training_metrics)
        """
        try:
            # Automated retry loop for data sufficiency
            min_required = window_size + 100 + 2  # 100 is default max_steps
            max_lookback_days = 365
            attempt = 0
            current_lookback = lookback_days
            max_retries = 5
            while attempt < max_retries:
                attempt += 1
                self.logger.info(f"[Attempt {attempt}] Collecting training data for {symbol} ({timeframe}) with lookback_days={current_lookback}")
                market_data, strategy_performances = await self.data_collector.collect_training_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    lookback_days=current_lookback
                )
                self.logger.info(f"Raw market_data rows: {len(market_data)}")
                if market_data.empty:
                    self.logger.warning("No market data collected for training")
                    return None, {'error': 'No market data collected'}
                self.logger.info("Extracting features from market data")
                features_df = self.feature_engineer.extract_market_features(market_data)
                self.logger.info(f"Rows after feature engineering: {len(features_df)}")

                # --- Data cleaning and validation to prevent NaN/inf issues in RL training ---
                # Replace inf/-inf with NaN, then handle missing values
                features_df = features_df.replace([np.inf, -np.inf], np.nan)
                # Drop rows missing critical features
                critical_features = ['close', 'returns', 'log_returns', 'rsi']
                features_df = features_df.dropna(subset=critical_features)
                # Fill other NaNs
                features_df = features_df.ffill().bfill()
                self.logger.info(f"Rows after fill: {len(features_df)}")
                # Abort if any NaNs remain
                if features_df.isna().any().any():
                    error_msg = (
                        "Feature DataFrame still contains NaN after cleaning. Aborting RL training. "
                        "See https://pandas.pydata.org/docs/user_guide/missing_data.html for best practices."
                    )
                    self.logger.error(error_msg)
                    return None, {'error': error_msg}
                # Check if enough data remains after cleaning
                if len(features_df) < min_required:
                    error_msg = (
                        f"Not enough rows after cleaning for RL training: {len(features_df)}. "
                        f"window_size={window_size}, max_steps=100, required={min_required}. "
                        f"Try reducing window_size or max_steps, or check data quality."
                    )
                    self.logger.error(error_msg)
                    return None, {'error': error_msg}
                self.logger.info(f"Rows after cleaning: {len(features_df)}. Data is clean. Proceeding to RL training.")
                # --- End data cleaning and validation ---

                if len(features_df) >= min_required:
                    break
                elif current_lookback >= max_lookback_days:
                    error_msg = (
                        f"Not enough rows after feature engineering: {len(features_df)}. "
                        f"window_size={window_size}, max_steps=100, required={min_required}. "
                        f"Tried up to lookback_days={max_lookback_days}. "
                        f"Try reducing window_size or max_steps."
                    )
                    self.logger.error(error_msg)
                    return None, {'error': error_msg}
                else:
                    # Double lookback_days for next attempt, but do not exceed max
                    current_lookback = min(current_lookback * 2, max_lookback_days)
            else:
                error_msg = f"Maximum retry count ({max_retries}) reached while collecting training data. Aborting."
                self.logger.error(error_msg)
                return None, {'error': error_msg}

            # Create model
            self.logger.info("Creating new RL model")
            model = RLWeightOptimizer()

            # Create environment
            env = model.create_env(
                market_data=features_df,
                strategy_names=self.strategy_names,
                window_size=window_size,
                max_steps=100,
                reward_function='sharpe'
            )

            # Set default hyperparameters
            default_hyperparameters = {
                'learning_rate': 0.0003,
                'n_steps': 2048,
                'batch_size': 64,
                'n_epochs': 10,
                'gamma': 0.99,
                'gae_lambda': 0.95,
                'clip_range': 0.2,
                'ent_coef': 0.01,
                'vf_coef': 0.5,
                'max_grad_norm': 0.5
            }

            # Use provided hyperparameters if available
            if hyperparameters is not None:
                default_hyperparameters.update(hyperparameters)

            # Build the model
            model.build_model(env, **default_hyperparameters)

            # Train the model
            self.logger.info(f"Training RL model for {total_timesteps} timesteps")
            metrics = model.train(None, None, total_timesteps=total_timesteps)

            # Save the model
            self.logger.info(f"Saving RL model to {self.model_path}")
            success = model.save(self.model_path)

            if not success:
                self.logger.warning("Failed to save RL model")

            self.logger.info("Successfully trained RL model")
            return model, metrics

        except Exception as e:
            self.logger.error(f"Error training ML model: {e}")
            return None, {'error': str(e)}

    async def optimize_hyperparameters(self, symbol: str, timeframe: str, lookback_days: int = 90,
                                     window_size: int = 10, n_trials: int = 10,
                                     total_timesteps: int = 10000) -> Dict[str, Any]:
        """Optimize hyperparameters for the ML model.

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for training data
            window_size: Size of the observation window
            n_trials: Number of optimization trials
            total_timesteps: Number of timesteps to train for each trial

        Returns:
            Dictionary of best hyperparameters
        """
        try:
            # Collect training data
            self.logger.info(f"Collecting training data for {symbol} ({timeframe})")
            market_data, strategy_performances = await self.data_collector.collect_training_data(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=lookback_days
            )

            if market_data.empty:
                self.logger.warning("No market data collected for hyperparameter optimization")
                return {'error': 'No market data collected'}

            # Extract features
            self.logger.info("Extracting features from market data")
            features_df = self.feature_engineer.extract_market_features(market_data)

            # Define the objective function for Optuna
            def objective(trial):
                # Suggest hyperparameters
                hyperparameters = {
                    'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
                    'n_steps': trial.suggest_int('n_steps', 64, 2048, log=True),
                    'batch_size': trial.suggest_int('batch_size', 32, 256, log=True),
                    'n_epochs': trial.suggest_int('n_epochs', 3, 30),
                    'gamma': trial.suggest_float('gamma', 0.9, 0.9999),
                    'gae_lambda': trial.suggest_float('gae_lambda', 0.9, 0.999),
                    'clip_range': trial.suggest_float('clip_range', 0.1, 0.3),
                    'ent_coef': trial.suggest_float('ent_coef', 0.0, 0.1),
                    'vf_coef': trial.suggest_float('vf_coef', 0.1, 1.0),
                    'max_grad_norm': trial.suggest_float('max_grad_norm', 0.1, 1.0)
                }

                # Create model
                model = RLWeightOptimizer()

                # Create environment
                env = model.create_env(
                    market_data=features_df,
                    strategy_names=self.strategy_names,
                    window_size=window_size,
                    max_steps=100,
                    reward_function='sharpe'
                )

                # Build the model
                model.build_model(env, **hyperparameters)

                # Train the model
                metrics = model.train(None, None, total_timesteps=total_timesteps)

                # Evaluate the model
                X, y = self.feature_engineer.prepare_training_data(
                    market_data=features_df,
                    strategy_performances=strategy_performances
                )

                if len(X) == 0 or len(y) == 0:
                    return 0.0

                eval_metrics = model.evaluate(X, y)

                # Return the negative MSE (to maximize)
                return -eval_metrics.get('mse', float('inf'))

            # Create Optuna study
            self.logger.info(f"Starting hyperparameter optimization with {n_trials} trials")
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=n_trials)

            # Get best hyperparameters
            best_hyperparameters = study.best_params
            best_value = study.best_value

            self.logger.info(f"Best hyperparameters: {best_hyperparameters}")
            self.logger.info(f"Best value: {best_value}")

            # Add best value to the result
            best_hyperparameters['best_value'] = best_value

            return best_hyperparameters

        except Exception as e:
            self.logger.error(f"Error optimizing hyperparameters: {e}")
            return {'error': str(e)}

    async def train_with_best_hyperparameters(self, symbol: str, timeframe: str,
                                            lookback_days: int = 90, window_size: int = 10,
                                            n_trials: int = 10, total_timesteps: int = 100000) -> Tuple[RLWeightOptimizer, Dict[str, float]]:
        """Train a model with optimized hyperparameters.

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for training data
            window_size: Size of the observation window
            n_trials: Number of optimization trials
            total_timesteps: Number of timesteps to train for

        Returns:
            Tuple of (trained_model, training_metrics)
        """
        try:
            # Optimize hyperparameters
            self.logger.info("Optimizing hyperparameters")
            best_hyperparameters = await self.optimize_hyperparameters(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=lookback_days,
                window_size=window_size,
                n_trials=n_trials,
                total_timesteps=total_timesteps // 10  # Use fewer timesteps for optimization
            )

            if 'error' in best_hyperparameters:
                self.logger.warning(f"Hyperparameter optimization failed: {best_hyperparameters['error']}")
                return None, {'error': best_hyperparameters['error']}

            # Remove best_value from hyperparameters
            best_value = best_hyperparameters.pop('best_value', None)

            # Train model with best hyperparameters
            self.logger.info("Training model with best hyperparameters")
            model, metrics = await self.train_model(
                symbol=symbol,
                timeframe=timeframe,
                lookback_days=lookback_days,
                window_size=window_size,
                total_timesteps=total_timesteps,
                hyperparameters=best_hyperparameters
            )

            # Add best value to metrics
            if best_value is not None:
                metrics['best_value'] = best_value

            return model, metrics

        except Exception as e:
            self.logger.error(f"Error training with best hyperparameters: {e}")
            return None, {'error': str(e)}

    async def create_training_session(self, symbol: str, timeframe: str,
                                    lookback_days: int = 90, optimize: bool = False,
                                    n_trials: int = 10, total_timesteps: int = 100000) -> Dict[str, Any]:
        """Create a training session and store the results.

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for klines (e.g., '1h', '4h', '1d')
            lookback_days: Number of days to look back for training data
            optimize: Whether to optimize hyperparameters
            n_trials: Number of optimization trials
            total_timesteps: Number of timesteps to train for

        Returns:
            Dictionary of training session information
        """
        try:
            # Create session ID
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            start_time = datetime.now()

            # Create session information
            session_info = {
                'id': session_id,
                'symbol': symbol,
                'timeframe': timeframe,
                'lookback_days': lookback_days,
                'optimize': optimize,
                'n_trials': n_trials,
                'total_timesteps': total_timesteps,
                'start_time': start_time.isoformat(),
                'end_time': None,
                'status': 'running',
                'model_path': self.model_path,
                'metrics': {},
                'hyperparameters': {},
                'error': None
            }

            # Store initial session information in database if available
            if self.db_client is not None:
                try:
                    # Check if we're using SQLAlchemy or raw queries
                    if hasattr(self.db_client, 'execute') and callable(self.db_client.execute):
                        # Using SQLAlchemy
                        from app.models import TrainingSessionDB

                        # Create TrainingSessionDB object
                        session_db = TrainingSessionDB(
                            id=session_id,
                            symbol=symbol,
                            timeframe=timeframe,
                            lookback_days=lookback_days,
                            total_timesteps=total_timesteps,
                            optimize=optimize,
                            n_trials=n_trials if optimize else None,
                            status='running',
                            start_time=start_time,
                            model_path=self.model_path
                        )

                        # Add to database
                        self.db_client.add(session_db)
                        await self.db_client.commit()
                    else:
                        # Using raw queries
                        await self.db_client.execute_query(
                            """INSERT INTO training_sessions
                               (id, symbol, timeframe, lookback_days, total_timesteps, optimize, n_trials, status, start_time, model_path)
                               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)""",
                            session_id, symbol, timeframe, lookback_days, total_timesteps, optimize,
                            n_trials if optimize else None, 'running', start_time, self.model_path
                        )
                except Exception as e:
                    self.logger.error(f"Error storing initial training session: {e}")

            # Train the model
            if optimize:
                self.logger.info("Training with optimized hyperparameters")
                model, metrics = await self.train_with_best_hyperparameters(
                    symbol=symbol,
                    timeframe=timeframe,
                    lookback_days=lookback_days,
                    n_trials=n_trials,
                    total_timesteps=total_timesteps
                )
            else:
                self.logger.info("Training with default hyperparameters")
                model, metrics = await self.train_model(
                    symbol=symbol,
                    timeframe=timeframe,
                    lookback_days=lookback_days,
                    total_timesteps=total_timesteps
                )

            # Update session information
            end_time = datetime.now()
            session_info['end_time'] = end_time.isoformat()

            if model is None:
                status = 'failed'
                error = metrics.get('error', 'Unknown error')
                session_info['status'] = status
                session_info['error'] = error
                metrics_json = None
                hyperparameters_json = None
            else:
                status = 'completed'
                error = None
                metrics_json = metrics
                hyperparameters_json = model.get_metadata().get('hyperparameters', {})
                session_info['status'] = status
                session_info['metrics'] = metrics_json
                session_info['hyperparameters'] = hyperparameters_json

            # Update session information in database if available
            if self.db_client is not None:
                try:
                    # Check if we're using SQLAlchemy or raw queries
                    if hasattr(self.db_client, 'execute') and callable(self.db_client.execute):
                        # Using SQLAlchemy
                        from sqlalchemy import update
                        from app.models import TrainingSessionDB

                        # Update TrainingSessionDB object
                        stmt = update(TrainingSessionDB).where(TrainingSessionDB.id == session_id).values(
                            status=status,
                            end_time=end_time,
                            error=error,
                            metrics=metrics_json,
                            hyperparameters=hyperparameters_json
                        )
                        await self.db_client.execute(stmt)
                        await self.db_client.commit()
                    else:
                        # Using raw queries
                        await self.db_client.execute_query(
                            """UPDATE training_sessions
                               SET status = $1, end_time = $2, error = $3, metrics = $4, hyperparameters = $5
                               WHERE id = $6""",
                            status, end_time, error, metrics_json, hyperparameters_json, session_id
                        )
                except Exception as e:
                    self.logger.error(f"Error updating training session: {e}")

            return session_info

        except Exception as e:
            self.logger.error(f"Error creating training session: {e}")
            end_time = datetime.now()
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Try to store error in database
            if self.db_client is not None:
                try:
                    # Check if we're using SQLAlchemy or raw queries
                    if hasattr(self.db_client, 'execute') and callable(self.db_client.execute):
                        # Using SQLAlchemy
                        from app.models import TrainingSessionDB

                        # Create TrainingSessionDB object with error
                        session_db = TrainingSessionDB(
                            id=session_id,
                            symbol=symbol,
                            timeframe=timeframe,
                            lookback_days=lookback_days,
                            total_timesteps=total_timesteps,
                            optimize=optimize,
                            n_trials=n_trials if optimize else None,
                            status='failed',
                            start_time=datetime.now(),
                            end_time=end_time,
                            error=str(e)
                        )

                        # Add to database
                        self.db_client.add(session_db)
                        await self.db_client.commit()
                    else:
                        # Using raw queries
                        await self.db_client.execute_query(
                            """INSERT INTO training_sessions
                               (id, symbol, timeframe, lookback_days, total_timesteps, optimize, n_trials, status, start_time, end_time, error)
                               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)""",
                            session_id, symbol, timeframe, lookback_days, total_timesteps, optimize,
                            n_trials if optimize else None, 'failed', datetime.now(), end_time, str(e)
                        )
                except Exception as db_error:
                    self.logger.error(f"Error storing failed training session: {db_error}")

            return {
                'id': session_id,
                'start_time': datetime.now().isoformat(),
                'end_time': end_time.isoformat(),
                'status': 'failed',
                'error': str(e)
            }

    async def get_training_sessions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent training sessions.

        Args:
            limit: Maximum number of sessions to return

        Returns:
            List of training session information
        """
        try:
            if self.db_client is None:
                self.logger.warning("No database client available")
                return []

            # Check if we're using SQLAlchemy or raw queries
            if hasattr(self.db_client, 'execute') and callable(self.db_client.execute):
                # Using SQLAlchemy
                from sqlalchemy import select
                from app.models import TrainingSessionDB

                # Query the database
                stmt = select(TrainingSessionDB).order_by(TrainingSessionDB.start_time.desc()).limit(limit)
                result = await self.db_client.execute(stmt)
                rows = result.scalars().all()

                # Convert to dictionaries
                sessions = []
                for row in rows:
                    session = {
                        'id': row.id,
                        'symbol': row.symbol,
                        'timeframe': row.timeframe,
                        'lookback_days': row.lookback_days,
                        'total_timesteps': row.total_timesteps,
                        'optimize': row.optimize,
                        'n_trials': row.n_trials,
                        'status': row.status,
                        'start_time': row.start_time.isoformat() if row.start_time else None,
                        'end_time': row.end_time.isoformat() if row.end_time else None,
                        'error': row.error,
                        'model_path': row.model_path,
                        'metrics': row.metrics,
                        'hyperparameters': row.hyperparameters
                    }
                    sessions.append(session)
            else:
                # Using raw queries
                # Make sure we're not awaiting a list
                if callable(getattr(self.db_client.execute_query, '__await__', None)):
                    result = await self.db_client.execute_query(
                        """SELECT id, symbol, timeframe, lookback_days, total_timesteps, optimize, n_trials,
                           status, start_time, end_time, error, model_path, metrics, hyperparameters
                           FROM training_sessions ORDER BY start_time DESC LIMIT $1""",
                        limit
                    )
                else:
                    result = self.db_client.execute_query(
                        """SELECT id, symbol, timeframe, lookback_days, total_timesteps, optimize, n_trials,
                           status, start_time, end_time, error, model_path, metrics, hyperparameters
                           FROM training_sessions ORDER BY start_time DESC LIMIT $1""",
                        limit
                    )

                if not result:
                    return []

                # Extract session information
                sessions = []
                for row in result:
                    if row:
                        session = {
                            'id': row[0],
                            'symbol': row[1],
                            'timeframe': row[2],
                            'lookback_days': row[3],
                            'total_timesteps': row[4],
                            'optimize': row[5],
                            'n_trials': row[6],
                            'status': row[7],
                            'start_time': row[8].isoformat() if row[8] else None,
                            'end_time': row[9].isoformat() if row[9] else None,
                            'error': row[10],
                            'model_path': row[11],
                            'metrics': row[12],
                            'hyperparameters': row[13]
                        }
                        sessions.append(session)

            return sessions

        except Exception as e:
            self.logger.error(f"Error getting training sessions: {e}")
            return []
