import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>T<PERSON>le, Button, Box, Typography } from '@mui/material';
import { Refresh as RefreshIcon, CloudOff as CloudOffIcon } from '@mui/icons-material';

interface DataUnavailableProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showMockDataInfo?: boolean;
}

const DataUnavailable: React.FC<DataUnavailableProps> = ({
  title = 'Service Unavailable',
  message = 'Unable to connect to the service. Please check your connection and try again.',
  onRetry,
  showMockDataInfo = false,
}) => {
  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Alert severity={showMockDataInfo ? 'info' : 'warning'} icon={<CloudOffIcon />}>
        <AlertTitle>{title}</AlertTitle>
        <Typography variant="body2" sx={{ mb: 2 }}>
          {message}
        </Typography>
        {showMockDataInfo && (
          <Typography variant="body2" sx={{ mb: 2, fontStyle: 'italic' }}>
            Displaying sample data for demonstration purposes.
          </Typography>
        )}
        {onRetry && (
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={onRetry}
            size="small"
          >
            Try Again
          </Button>
        )}
      </Alert>
    </Box>
  );
};

export default DataUnavailable;