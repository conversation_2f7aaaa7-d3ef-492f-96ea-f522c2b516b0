"""
Reward functions for reinforcement learning.

This module contains reward functions for training reinforcement learning
agents to optimize strategy weights.
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any

logger = logging.getLogger(__name__)

class RewardFunctions:
    """Collection of reward functions for RL training."""

    @staticmethod
    def sharpe_ratio(returns: List[float], risk_free_rate: float = 0.0) -> float:
        """Calculate Sharpe ratio.

        Args:
            returns: List of returns
            risk_free_rate: Risk-free rate

        Returns:
            Sharpe ratio
        """
        try:
            if len(returns) < 2:
                return 0.0

            returns_array = np.array(returns)
            excess_returns = returns_array - risk_free_rate
            mean_excess_return = np.mean(excess_returns)
            std_excess_return = np.std(excess_returns)

            # Avoid division by zero
            if std_excess_return == 0:
                return 0.0

            sharpe = mean_excess_return / std_excess_return

            # Annualize (assuming daily returns)
            sharpe_annualized = sharpe * np.sqrt(252)

            return sharpe_annualized

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    @staticmethod
    def sortino_ratio(returns: List[float], risk_free_rate: float = 0.0,
                     target_return: float = 0.0) -> float:
        """Calculate Sortino ratio.

        Args:
            returns: List of returns
            risk_free_rate: Risk-free rate
            target_return: Target return

        Returns:
            Sortino ratio
        """
        try:
            if len(returns) < 2:
                return 0.0

            returns_array = np.array(returns)
            excess_returns = returns_array - risk_free_rate
            mean_excess_return = np.mean(excess_returns)

            # Calculate downside deviation
            downside_returns = np.minimum(returns_array - target_return, 0)
            downside_deviation = np.sqrt(np.mean(np.square(downside_returns)))

            # Avoid division by zero
            if downside_deviation == 0:
                # If all returns are above target, return a positive value
                if np.all(returns_array >= target_return):
                    return 10.0  # Return a high positive value
                return 0.0

            sortino = mean_excess_return / downside_deviation

            # Annualize (assuming daily returns)
            sortino_annualized = sortino * np.sqrt(252)

            return sortino_annualized

        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0

    @staticmethod
    def calmar_ratio(returns: List[float], max_drawdown: Optional[float] = None) -> float:
        """Calculate Calmar ratio.

        Args:
            returns: List of returns
            max_drawdown: Maximum drawdown (optional)

        Returns:
            Calmar ratio
        """
        try:
            if len(returns) < 2:
                return 0.0

            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)

            # Calculate maximum drawdown if not provided
            if max_drawdown is None:
                # Calculate cumulative returns
                cumulative_returns = np.cumprod(1 + returns_array) - 1

                # Calculate running maximum
                running_max = np.maximum.accumulate(cumulative_returns)

                # Calculate drawdown
                drawdown = (cumulative_returns - running_max) / (1 + running_max)

                # Calculate maximum drawdown
                max_drawdown = abs(np.min(drawdown))

            # Avoid division by zero
            if max_drawdown == 0:
                # If all returns are positive, return a positive value
                if np.all(returns_array >= 0):
                    return 10.0  # Return a high positive value
                return 0.0

            # Calculate Calmar ratio
            calmar = mean_return / max_drawdown

            # Annualize (assuming daily returns)
            calmar_annualized = calmar * 252

            return calmar_annualized

        except Exception as e:
            logger.error(f"Error calculating Calmar ratio: {e}")
            return 0.0

    @staticmethod
    def omega_ratio(returns: List[float], threshold: float = 0.0) -> float:
        """Calculate Omega ratio.

        Args:
            returns: List of returns
            threshold: Return threshold

        Returns:
            Omega ratio
        """
        try:
            if len(returns) < 2:
                return 0.0

            returns_array = np.array(returns)

            # Calculate returns above and below threshold
            returns_above = returns_array[returns_array > threshold] - threshold
            returns_below = threshold - returns_array[returns_array < threshold]

            # Calculate sum of returns above and below threshold
            sum_above = np.sum(returns_above)
            sum_below = np.sum(returns_below)

            # Avoid division by zero
            if sum_below == 0:
                return float('inf')

            # Calculate Omega ratio
            omega = sum_above / sum_below

            return omega

        except Exception as e:
            logger.error(f"Error calculating Omega ratio: {e}")
            return 0.0

    @staticmethod
    def max_drawdown(returns: List[float]) -> float:
        """Calculate maximum drawdown.

        Args:
            returns: List of returns

        Returns:
            Maximum drawdown
        """
        try:
            if len(returns) < 2:
                return 0.0

            returns_array = np.array(returns)

            # Calculate cumulative returns
            cumulative_returns = np.cumprod(1 + returns_array) - 1

            # Calculate running maximum
            running_max = np.maximum.accumulate(cumulative_returns)

            # Calculate drawdown
            drawdown = (cumulative_returns - running_max) / (1 + running_max)

            # Calculate maximum drawdown
            max_drawdown = abs(np.min(drawdown))

            return max_drawdown

        except Exception as e:
            logger.error(f"Error calculating maximum drawdown: {e}")
            return 0.0

    @staticmethod
    def win_rate(returns: List[float]) -> float:
        """Calculate win rate.

        Args:
            returns: List of returns

        Returns:
            Win rate
        """
        try:
            if len(returns) < 1:
                return 0.0

            returns_array = np.array(returns)

            # Calculate number of winning trades
            num_wins = np.sum(returns_array > 0)

            # Calculate win rate
            win_rate = num_wins / len(returns_array)

            return win_rate

        except Exception as e:
            logger.error(f"Error calculating win rate: {e}")
            return 0.0

    @staticmethod
    def profit_factor(returns: List[float]) -> float:
        """Calculate profit factor.

        Args:
            returns: List of returns

        Returns:
            Profit factor
        """
        try:
            if len(returns) < 1:
                return 0.0

            returns_array = np.array(returns)

            # Calculate gross profit and gross loss
            gross_profit = np.sum(returns_array[returns_array > 0])
            gross_loss = abs(np.sum(returns_array[returns_array < 0]))

            # Avoid division by zero
            if gross_loss == 0:
                return float('inf') if gross_profit > 0 else 0.0

            # Calculate profit factor
            profit_factor = gross_profit / gross_loss

            return profit_factor

        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0.0

    @staticmethod
    def get_reward_function(name: str) -> callable:
        """Get a reward function by name.

        Args:
            name: Name of the reward function

        Returns:
            Reward function
        """
        reward_functions = {
            'sharpe': RewardFunctions.sharpe_ratio,
            'sortino': RewardFunctions.sortino_ratio,
            'calmar': RewardFunctions.calmar_ratio,
            'omega': RewardFunctions.omega_ratio,
            'max_drawdown': RewardFunctions.max_drawdown,
            'win_rate': RewardFunctions.win_rate,
            'profit_factor': RewardFunctions.profit_factor
        }

        if name in reward_functions:
            return reward_functions[name]
        else:
            logger.warning(f"Unknown reward function: {name}, using sharpe ratio")
            return RewardFunctions.sharpe_ratio
