{"description": "Updated MCP configuration for Crypto Trading Ensemble with custom Redis server", "mcpServers": {"redis-trading": {"command": "node", "args": ["/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/mcp-redis-trading/dist/index.js"], "env": {"REDIS_URL": "redis://localhost:6379", "REDIS_DB": "0"}}, "zenml": {"command": "npx", "args": ["-y", "@zenml-io/mcp-zenml"], "env": {"ZENML_STORE_URL": "postgresql://localhost:5432/zenml", "ZENML_ANALYTICS_OPT_IN": "false"}}, "wandb": {"command": "npx", "args": ["-y", "@wandb/wandb-mcp-server"], "env": {"WANDB_API_KEY": "${WANDB_API_KEY}", "WANDB_PROJECT": "crypto-ensemble-strategy"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "${SUPABASE_ACCESS_TOKEN}"]}, "telegram": {"command": "npx", "args": ["-y", "@qpd-v/mcp-communicator-telegram"], "env": {"TELEGRAM_BOT_TOKEN": "${TELEGRAM_BOT_TOKEN}", "TELEGRAM_CHAT_ID": "${TELEGRAM_CHAT_ID}"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp-server"], "env": {"PLAYWRIGHT_BROWSERS_PATH": "0"}}}, "integrationFlow": {"description": "How the Redis Trading MCP integrates with other MCP servers", "dataFlow": [{"step": 1, "description": "Portfolio Manager gets market data and calculates strategy weights", "mcpServers": ["redis-trading"], "operations": ["get_strategy_weights", "cache_strategy_weights"]}, {"step": 2, "description": "ML optimization pipeline trains and deploys models", "mcpServers": ["zenml", "wandb"], "operations": ["ML pipeline execution", "experiment tracking"]}, {"step": 3, "description": "Position sizing calculations with cross-validation", "mcpServers": ["redis-trading"], "operations": ["get_kelly_stats", "cache_kelly_stats", "get_volatility_adjustment"]}, {"step": 4, "description": "Store performance metrics and audit trails", "mcpServers": ["supabase", "redis-trading"], "operations": ["database storage", "cache_position_calculation"]}, {"step": 5, "description": "Send alerts and notifications", "mcpServers": ["telegram", "redis-trading"], "operations": ["alert delivery", "get_cache_stats"]}, {"step": 6, "description": "Automated testing and deployment", "mcpServers": ["github", "playwright"], "operations": ["CI/CD pipeline", "automated testing"]}]}, "performanceOptimizations": {"redis-trading": {"cacheStrategy": "Multi-tier caching with different TTLs", "responseTime": "<100ms for cache hits", "memoryUsage": "~50MB with active cache", "concurrent": "100+ operations per second"}, "zenml": {"purpose": "ML pipeline orchestration", "integration": "Automated model training and deployment"}, "wandb": {"purpose": "Experiment tracking and hyperparameter optimization", "integration": "Model performance monitoring"}, "supabase": {"purpose": "Real-time portfolio analytics and storage", "integration": "Performance metrics and audit storage"}, "telegram": {"purpose": "Real-time alerts and monitoring", "integration": "Risk limit violations and system alerts"}}, "environmentVariables": {"redis": {"REDIS_URL": "redis://localhost:6379", "REDIS_DB": "0"}, "ml": {"WANDB_API_KEY": "your_wandb_api_key", "WANDB_PROJECT": "crypto-ensemble-strategy", "ZENML_STORE_URL": "postgresql://localhost:5432/zenml"}, "database": {"SUPABASE_ACCESS_TOKEN": "your_supabase_access_token", "SUPABASE_URL": "https://your-project.supabase.co"}, "notifications": {"TELEGRAM_BOT_TOKEN": "your_telegram_bot_token", "TELEGRAM_CHAT_ID": "your_telegram_chat_id"}, "deployment": {"GITHUB_TOKEN": "your_github_token"}}, "startupSequence": ["1. Start Redis server (docker run -d -p 6379:6379 redis:7-alpine)", "2. Build custom Redis MCP (cd mcp-redis-trading && npm run build)", "3. Start <PERSON> with this MCP configuration", "4. Verify all MCP servers are connected", "5. Run integration tests (npm run test in each MCP directory)"]}