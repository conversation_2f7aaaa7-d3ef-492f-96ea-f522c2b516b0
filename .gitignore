# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local configuration
.env
.env.local

# Database
*.sqlite3
*.db

# OS specific
.DS_Store
Thumbs.db

# Project specific
.cursorrules/
binance-mcp/

# Ignore embedded Git repositories
.cursorrules/.git/
binance-mcp/.git/
.git/objects/

# Redis
dump.rdb

# Testing
.coverage
htmlcov/
.pytest_cache/

# Distribution / packaging
.Python
mcp-servers/mcp-crypto-price

# W&B
wandb/
mcp-servers/mcp-crypto-price
mcp-servers/mcp-zenml
