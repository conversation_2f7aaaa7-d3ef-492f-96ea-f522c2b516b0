"""
Trend-Following strategy implementation for the Multi-Strategy Crypto Auto Trader.

This strategy identifies and follows market trends to capitalize on directional
price movements, optimal for strong trending markets with high momentum.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Any # Removed List, Tuple
# import talib # No longer needed directly for most indicators
import logging
# import logging # Duplicate import removed
# from talib import func # No longer needed directly for most indicators
import app.utils.technical_analysis as ta
from app.config.settings import Settings # Import Settings

from app.strategies.base_strategy import BaseStrategy


class TrendFollowingStrategy(BaseStrategy):
    """Trend-Following strategy implementation.
    
    This strategy identifies market trends and generates trading signals
    to follow the trend direction, using ADX for trend strength confirmation,
    moving averages for direction, and ATR for volatility-based position sizing.
    """
    
    def __init__(self, symbol: str, timeframe: str, settings: Settings):
        """Initialize the Trend-Following strategy."""
        super().__init__(symbol, timeframe, settings)
        self.logger = logging.getLogger(__name__)
    
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market data to determine trend conditions.
        
        Args:
            data: DataFrame containing OHLCV data
            
        Returns:
            Dictionary containing market analysis results
        """
        result = {}
        df = data.copy()
        
        # Calculate ADX
        df['ADX'] = ta.calculate_adx(df['high'], df['low'], df['close'], timeperiod=self.settings.tf_adx_period)
        result['adx'] = df['ADX'].iloc[-1]
        result['is_strong_trend'] = result['adx'] >= self.settings.tf_adx_threshold
        
        # Calculate ATR
        df['ATR'] = ta.calculate_atr_volatility(df, window=self.settings.atr_periods)
        result['atr'] = df['ATR'].iloc[-1]
        result['volatility_ratio'] = result['atr'] / df['close'].iloc[-1] if df['close'].iloc[-1] != 0 else 0
        result['is_suitable_volatility'] = result['volatility_ratio'] >= 0.01
        
        # Calculate EMAs
        df['EMA_SHORT'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_short)
        df['EMA_MEDIUM'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_medium)
        df['EMA_LONG'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_long)
        
        # Determine trend direction based on EMA alignment
        if df['EMA_SHORT'].iloc[-1] > df['EMA_MEDIUM'].iloc[-1] > df['EMA_LONG'].iloc[-1]:
            result['trend_direction'] = 'bullish'
        elif df['EMA_SHORT'].iloc[-1] < df['EMA_MEDIUM'].iloc[-1] < df['EMA_LONG'].iloc[-1]:
            result['trend_direction'] = 'bearish'
        else:
            result['trend_direction'] = 'mixed'
            
        # Calculate RSI
        df['RSI'] = ta.calculate_rsi(df['close'], timeperiod=self.settings.tf_rsi_period)
        result['rsi'] = df['RSI'].iloc[-1]
        
        # Check if RSI confirms trend direction
        if result['trend_direction'] == 'bullish' and result['rsi'] > 50:
            result['momentum_confirms_trend'] = True
        elif result['trend_direction'] == 'bearish' and result['rsi'] < 50:
            result['momentum_confirms_trend'] = True
        else:
            result['momentum_confirms_trend'] = False
            
        # Calculate MACD
        macd_results = ta.calculate_macd(
            df['close'],
            fastperiod=self.settings.tf_macd_fast,
            slowperiod=self.settings.tf_macd_slow,
            signalperiod=self.settings.tf_macd_signal
        )
        df['MACD'] = macd_results['macd']
        df['MACD_SIGNAL'] = macd_results['macd_signal']
        df['MACD_HIST'] = macd_results['macd_hist']
        
        result['macd'] = df['MACD'].iloc[-1]
        result['macd_signal'] = df['MACD_SIGNAL'].iloc[-1]
        result['macd_hist'] = df['MACD_HIST'].iloc[-1]
        
        if result['trend_direction'] == 'bullish' and result['macd'] > 0:
            result['macd_confirms_trend'] = True
        elif result['trend_direction'] == 'bearish' and result['macd'] < 0:
            result['macd_confirms_trend'] = True
        else:
            result['macd_confirms_trend'] = False
            
        # Identify breakouts
        if len(df) > 20:
            lookback = self.settings.tf_breakout_periods
            highest_high = df['high'].rolling(window=lookback).max()
            lowest_low = df['low'].rolling(window=lookback).min()
            current_price = df['close'].iloc[-1]
            
            if 'volume' in df.columns:
                df['volume_ma'] = df['volume'].rolling(window=20).mean()
                current_volume = df['volume'].iloc[-1]
                avg_volume = df['volume_ma'].iloc[-1]
                result['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 0
                result['is_high_volume'] = result['volume_ratio'] > 1.5
            else:
                result['volume_ratio'] = 1.0
                result['is_high_volume'] = False
                
            result['is_bullish_breakout'] = (current_price > highest_high.iloc[-2] and result['is_high_volume'] and result['trend_direction'] == 'bullish') if len(df) > 1 and not np.isnan(highest_high.iloc[-2]) else False
            result['is_bearish_breakout'] = (current_price < lowest_low.iloc[-2] and result['is_high_volume'] and result['trend_direction'] == 'bearish') if len(df) > 1 and not np.isnan(lowest_low.iloc[-2]) else False
        else:
            result['is_bullish_breakout'] = False
            result['is_bearish_breakout'] = False
        
        # Calculate trend duration
        if result['trend_direction'] == 'bullish':
            condition = df['EMA_SHORT'] > df['EMA_LONG']
        elif result['trend_direction'] == 'bearish':
            condition = df['EMA_SHORT'] < df['EMA_LONG']
        else:
            condition = pd.Series([False] * len(df))
            
        trend_periods = 0
        for i in range(len(condition)-1, -1, -1):
            if not pd.isna(condition.iloc[i]) and condition.iloc[i]:
                trend_periods += 1
            else:
                break
        result['trend_duration'] = trend_periods
        
        result['is_suitable'] = (result['is_strong_trend'] and result['is_suitable_volatility'] and result['trend_direction'] != 'mixed' and result['momentum_confirms_trend'])
        
        return result
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on trend following logic.
        
        Args:
            data: DataFrame containing OHLCV data
            
        Returns:
            DataFrame with signals added
        """
        # Make a copy of the data to avoid modifying the original
        df = data.copy()
        
        # Ensure correct types for TA-Lib
        df['open'] = df['open'].astype(np.float64)
        df['high'] = df['high'].astype(np.float64)
        df['low'] = df['low'].astype(np.float64)
        df['close'] = df['close'].astype(np.float64)
        if 'volume' in df.columns:
            df['volume'] = df['volume'].astype(np.float64)

        # Initialize signal column
        df['signal'] = 0
        
        # Create input arrays for talib functions
        high_prices = df['high'].values
        low_prices = df['low'].values
        close_prices = df['close'].values
        
        # Calculate EMAs (Keeping direct talib import for now)
        # Keep direct EMA import as it's not in utils
        from talib import EMA
        df['EMA_SHORT'] = EMA(close_prices, timeperiod=self.settings.tf_ema_short)
        df['EMA_MEDIUM'] = EMA(close_prices, timeperiod=self.settings.tf_ema_medium)
        df['EMA_LONG'] = EMA(close_prices, timeperiod=self.settings.tf_ema_long)
        
        # Calculate ADX
        df['ADX'] = ta.calculate_adx(df['high'], df['low'], df['close'], timeperiod=self.settings.tf_adx_period)
        
        # Calculate RSI
        df['RSI'] = ta.calculate_rsi(df['close'], timeperiod=self.settings.tf_rsi_period)
        
        # Generate signals for each candle
        # Start from a point where indicators are likely calculated
        start_index = max(50, self.settings.tf_adx_period, self.settings.tf_ema_long) # Use settings
        for i in range(start_index, len(df)):  
            # Check for NaN values before accessing iloc[i]
            if np.isnan(df['EMA_SHORT'].iloc[i]) or np.isnan(df['EMA_MEDIUM'].iloc[i]) or \
               np.isnan(df['EMA_LONG'].iloc[i]) or np.isnan(df['ADX'].iloc[i]) or \
               np.isnan(df['RSI'].iloc[i]) or np.isnan(df['signal'].iloc[i-1]):
                df.loc[df.index[i], 'signal'] = df['signal'].iloc[i-1] # Maintain previous if NaN
                continue # Skip this candle if indicators are NaN

            # Get indicator values
            ema_short = df['EMA_SHORT'].iloc[i]
            ema_medium = df['EMA_MEDIUM'].iloc[i]
            ema_long = df['EMA_LONG'].iloc[i]
            adx = df['ADX'].iloc[i]
            rsi = df['RSI'].iloc[i]
            price = close_prices[i]
            
            # Previous signal
            prev_signal = df['signal'].iloc[i-1]
            
            # Check for trend strength
            strong_trend = adx >= self.settings.tf_adx_threshold # Use settings
            
            # Determine trend direction
            if ema_short > ema_medium > ema_long:
                trend_direction = 'bullish'
            elif ema_short < ema_medium < ema_long:
                trend_direction = 'bearish'
            else:
                trend_direction = 'mixed'
            
            # Check if price confirms trend
            price_confirms_bull = price > ema_short
            price_confirms_bear = price < ema_short
            
            # Check if RSI confirms trend
            rsi_confirms_bull = rsi > 50
            rsi_confirms_bear = rsi < 50
            
            # Generate buy signal
            if (
                trend_direction == 'bullish' and 
                strong_trend and 
                price_confirms_bull and 
                rsi_confirms_bull and 
                prev_signal <= 0
            ):
                df.loc[df.index[i], 'signal'] = 1
            
            # Generate sell signal
            elif (
                trend_direction == 'bearish' and 
                strong_trend and 
                price_confirms_bear and 
                rsi_confirms_bear and 
                prev_signal >= 0
            ):
                df.loc[df.index[i], 'signal'] = -1
            
            # Maintain position if trend continues
            else:
                df.loc[df.index[i], 'signal'] = prev_signal
                
            # Exit on trend change
            if (prev_signal > 0 and trend_direction != 'bullish') or \
               (prev_signal < 0 and trend_direction != 'bearish'):
                df.loc[df.index[i], 'signal'] = 0
        
        return df
    
    async def execute(self, data: pd.DataFrame) -> None:
        """Execute the strategy on the given data."""
        # Skip if not started
        if not getattr(self, '_is_running', False):
            self.logger.debug(f"{self.name} strategy is not running, skipping execution")
            return
        try:
            self.logger.info(f"Executing {self.name} strategy for {self.symbol}")
            signals_df = self.generate_signals(data)
            latest_signal = signals_df['signal'].iloc[-1] if not signals_df.empty else 0
            self.logger.info(f"{self.name} signal for {self.symbol}: {latest_signal}")
            # TODO: integrate execution handler to act on signals
        except Exception as e:
            self.logger.error(f"Error executing {self.name} strategy: {e}", exc_info=True)
    
    def calculate_risk_params(self, 
                             data: pd.DataFrame, 
                             entry_price: float, 
                             position_type: str) -> Dict[str, float]:
        """Calculate risk parameters for a trade.
        
        Args:
            data: DataFrame containing OHLCV data
            entry_price: The entry price for the trade
            position_type: 'long' or 'short'
            
        Returns:
            Dictionary containing stop loss and take profit levels
        """
        # Ensure correct types
        high_prices = data['high'].values.astype(np.float64)
        low_prices = data['low'].values.astype(np.float64)
        close_prices = data['close'].values.astype(np.float64)

        # Calculate ATR for dynamic stop loss
        if 'ATR' in data.columns and not data['ATR'].isnull().all():
             # Use already calculated ATR if available and not NaN
             atr = data['ATR'].iloc[-1]
             if np.isnan(atr):
                 # Recalculate using utility function if last value is NaN
                 atr_series = ta.calculate_atr(data['high'], data['low'], data['close'], timeperiod=self.settings.atr_periods) # Use settings
                 atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0
        else:
            # Calculate using utility function if not present in input data
            atr_series = ta.calculate_atr(data['high'], data['low'], data['close'], timeperiod=self.settings.atr_periods) # Use settings (Corrected attribute)
            atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0
        
        # Get risk multiple based on strategy and volatility
        atr_multiplier = self.settings.atr_multiplier # Use settings
        
        # For trend following, we use volatility-based stops
        if position_type == 'long':
            # Use trailing stop once in profit
            stop_loss = entry_price - (atr * atr_multiplier)
            # For take profit, we use a higher multiple since trend following aims to capture larger moves
            take_profit = entry_price + (atr * atr_multiplier * 3)
        else:  # short
            # Use trailing stop once in profit
            stop_loss = entry_price + (atr * atr_multiplier)
            # For take profit, we use a higher multiple since trend following aims to capture larger moves
            take_profit = entry_price - (atr * atr_multiplier * 3)
        
        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_reward_ratio': 3.0,  # Trend following typically uses higher risk-reward
            'position_size_factor': 1.0,  # Full position size
            'use_trailing_stop': True  # Trend following should use trailing stops
        }
    
    def calculate_score(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate the strategy score based on current market conditions.
        
        Args:
            market_conditions: Dictionary containing market conditions
            
        Returns:
            Strategy score (0-100)
        """
        # Start with base score
        score = 50
        self.logger.debug(f"TF Score Start: {score}")
        
        # ADX factor: higher is better for trend following
        adx = market_conditions.get('adx', 0)
        adx_threshold = self.settings.tf_adx_threshold # Use settings
        
        if adx >= adx_threshold:
            score += min(30, (adx - adx_threshold) * 1.5)
        else:
            score -= min(30, (adx_threshold - adx) * 1.5)
        self.logger.debug(f"TF Score after ADX ({adx:.2f}): {score}")
        
        # Trend direction clarity
        trend_direction = market_conditions.get('trend_direction', 'mixed')
        if trend_direction in ['bullish', 'bearish']:
            score += 15
        else:
            score -= 15
        self.logger.debug(f"TF Score after Trend Direction ({trend_direction}): {score}")
        
        # Momentum confirmation
        if market_conditions.get('momentum_confirms_trend', False):
            score += 15
        else:
            score -= 15
        self.logger.debug(f"TF Score after Momentum Confirm ({market_conditions.get('momentum_confirms_trend', False)}): {score}")
        
        # Trend duration factor
        trend_duration = market_conditions.get('trend_duration', 0)
        score += min(20, trend_duration / 2)  # Max +20 for trends lasting 40+ periods
        self.logger.debug(f"TF Score after Trend Duration ({trend_duration}): {score}")
        
        # Volume confirmation
        volume_ratio = market_conditions.get('volume_ratio', 1.0)
        if volume_ratio > 1.5:
            score += min(15, (volume_ratio - 1.5) * 10)
        self.logger.debug(f"TF Score after Volume ({volume_ratio:.2f}): {score}")
        
        # Breakout bonus
        if (market_conditions.get('is_bullish_breakout', False) or 
            market_conditions.get('is_bearish_breakout', False)):
            score += 10
        self.logger.debug(f"TF Score after Breakout ({market_conditions.get('is_bullish_breakout', False) or market_conditions.get('is_bearish_breakout', False)}): {score}")
        
        # Ensure score is between 0 and 100
        return max(0, min(100, score))