"""
ML API router for the dashboard.

This module contains API endpoints for ML model training and weight retrieval.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, ConfigDict

from app.config.settings import settings
from app.ml.models.weight_optimizer import MLW<PERSON>ghtOptimizer
from app.ml.training.trainer import ModelTrainer
from app.ml.evaluation.backtesting import Backtester
from app.dashboard.api_router import get_current_active_user, User
from app.api.binance.client import BinanceClient

logger = logging.getLogger(__name__)

# Create ML router
router = APIRouter(
    prefix="/ml",
    tags=["ml"],
    responses={404: {"description": "Not found"}},
)

# --- Models ---

class TrainingRequest(BaseModel):
    """Request model for training the ML model."""

    symbol: str = settings.trading_symbol
    timeframe: str = settings.timeframe
    lookback_days: int = settings.ml_lookback_days
    window_size: int = settings.ml_window_size
    total_timesteps: int = settings.ml_training_timesteps
    optimize_hyperparameters: bool = settings.ml_hyperparameter_optimization
    optimization_trials: int = settings.ml_optimization_trials


class TrainingResponse(BaseModel):
    """Response model for training the ML model."""

    success: bool
    message: str
    training_id: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class WeightsRequest(BaseModel):
    """Request model for getting ML-optimized weights."""

    market_conditions: Dict[str, float]


class WeightsResponse(BaseModel):
    """Response model for getting ML-optimized weights."""

    success: bool
    weights: Optional[Dict[str, Dict[str, float]]] = None
    strategy_weights: Optional[Dict[str, float]] = None
    timestamp: str
    model_info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BacktestRequest(BaseModel):
    """Request model for backtesting with ML weights."""

    symbol: str = settings.trading_symbol
    timeframe: str = settings.timeframe
    start_date: str
    end_date: str
    fixed_weights: Optional[Dict[str, float]] = None


class BacktestResponse(BaseModel):
    """Response model for backtesting with ML weights."""

    success: bool
    comparison: Optional[Dict[str, Any]] = None
    fixed_metrics: Optional[Dict[str, float]] = None
    ml_metrics: Optional[Dict[str, float]] = None
    error: Optional[str] = None


class ModelInfoResponse(BaseModel):
    """Response model for getting ML model information."""

    success: bool
    model_info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class ToggleRequest(BaseModel):
    """Request model for toggling ML weight optimization."""

    enabled: bool


# --- Dependencies ---

def get_exchange_client():
    """Get the exchange client."""
    try:
        api_key = settings.binance_api_key
        api_secret = settings.binance_api_secret
        testnet = settings.use_testnet

        client = BinanceClient(
            api_key=api_key,
            api_secret=api_secret,
            testnet=testnet
        )

        return client
    except Exception as e:
        logger.error(f"Error creating exchange client: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Could not create exchange client: {str(e)}"
        )


def get_weight_optimizer():
    """Get the ML weight optimizer."""
    try:
        exchange_client = get_exchange_client()

        optimizer = MLWeightOptimizer(
            exchange_client=exchange_client,
            model_path=settings.ml_model_path
        )

        return optimizer
    except Exception as e:
        logger.error(f"Error creating weight optimizer: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Could not create weight optimizer: {str(e)}"
        )


def get_model_trainer():
    """Get the ML model trainer."""
    try:
        exchange_client = get_exchange_client()

        trainer = ModelTrainer(
            exchange_client=exchange_client,
            model_path=settings.ml_model_path
        )

        return trainer
    except Exception as e:
        logger.error(f"Error creating model trainer: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Could not create model trainer: {str(e)}"
        )


def get_backtester():
    """Get the backtester."""
    try:
        exchange_client = get_exchange_client()

        backtester = Backtester(
            exchange_client=exchange_client
        )

        return backtester
    except Exception as e:
        logger.error(f"Error creating backtester: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Could not create backtester: {str(e)}"
        )


# --- Endpoints ---

@router.post("/train", response_model=TrainingResponse)
async def train_ml_model(
    request: TrainingRequest,
    trainer: ModelTrainer = Depends(get_model_trainer)
):
    """Train the ML model for weight optimization."""
    if not settings.ml_weight_optimization_enabled:
        return TrainingResponse(
            success=False,
            message="ML weight optimization is disabled",
            error="ML weight optimization is disabled in settings"
        )

    try:
        logger.info(f"Starting ML model training for {request.symbol} ({request.timeframe})")

        # Create training session
        session_info = await trainer.create_training_session(
            symbol=request.symbol,
            timeframe=request.timeframe,
            lookback_days=request.lookback_days,
            optimize=request.optimize_hyperparameters,
            n_trials=request.optimization_trials,
            total_timesteps=request.total_timesteps
        )

        if session_info.get('status') == 'completed':
            return TrainingResponse(
                success=True,
                message="ML model training completed successfully",
                training_id=session_info.get('id'),
                start_time=session_info.get('start_time'),
                end_time=session_info.get('end_time'),
                metrics=session_info.get('metrics')
            )
        else:
            return TrainingResponse(
                success=False,
                message="ML model training failed",
                training_id=session_info.get('id'),
                start_time=session_info.get('start_time'),
                end_time=session_info.get('end_time'),
                error=session_info.get('error')
            )
    except Exception as e:
        logger.error(f"Error training ML model: {e}")
        return TrainingResponse(
            success=False,
            message=f"Error training ML model: {str(e)}",
            error=str(e)
        )


@router.post("/weights", response_model=WeightsResponse)
async def get_optimized_weights(
    request: WeightsRequest,
    optimizer: MLWeightOptimizer = Depends(get_weight_optimizer)
):
    """Get ML-optimized weights for strategy scoring."""
    if not settings.ml_weight_optimization_enabled:
        return WeightsResponse(
            success=False,
            timestamp=datetime.now().isoformat(),
            error="ML weight optimization is disabled in settings"
        )

    try:
        logger.info("Getting ML-optimized weights")

        # Get optimized weights
        strategy_weights = await optimizer.get_optimized_weights(request.market_conditions)

        # Get model info
        model_info = await optimizer.get_model_info()

        return WeightsResponse(
            success=True,
            strategy_weights=strategy_weights,
            timestamp=datetime.now().isoformat(),
            model_info=model_info
        )
    except Exception as e:
        logger.error(f"Error getting ML-optimized weights: {e}")
        return WeightsResponse(
            success=False,
            timestamp=datetime.now().isoformat(),
            error=str(e)
        )


@router.post("/backtest", response_model=BacktestResponse)
async def backtest_ml_weights(
    request: BacktestRequest,
    backtester: Backtester = Depends(get_backtester)
):
    """Backtest with ML-optimized weights."""
    if not settings.ml_weight_optimization_enabled:
        return BacktestResponse(
            success=False,
            error="ML weight optimization is disabled in settings"
        )

    try:
        logger.info(f"Starting ML weights backtest for {request.symbol} ({request.timeframe})")

        # Parse dates
        start_date = datetime.fromisoformat(request.start_date)
        end_date = datetime.fromisoformat(request.end_date)

        # Use default weights if none provided
        fixed_weights = request.fixed_weights
        if fixed_weights is None:
            fixed_weights = {
                'grid': 1/3,
                'technical_analysis': 1/3,
                'trend_following': 1/3
            }

        # Run comparison backtest
        result = await backtester.run_comparison_backtest(
            symbol=request.symbol,
            timeframe=request.timeframe,
            start_date=start_date,
            end_date=end_date,
            fixed_weights=fixed_weights,
            model_path=settings.ml_model_path
        )

        if 'error' in result:
            return BacktestResponse(
                success=False,
                error=result['error']
            )

        # Extract comparison results
        comparison = result.get('comparison', {})
        fixed_metrics = result.get('fixed_result', {}).get('metrics', {})
        ml_metrics = result.get('ml_result', {}).get('metrics', {})

        return BacktestResponse(
            success=True,
            comparison=comparison,
            fixed_metrics=fixed_metrics,
            ml_metrics=ml_metrics
        )
    except Exception as e:
        logger.error(f"Error backtesting ML weights: {e}")
        return BacktestResponse(
            success=False,
            error=str(e)
        )


@router.get("/info", response_model=ModelInfoResponse)
async def get_model_info(
    optimizer: MLWeightOptimizer = Depends(get_weight_optimizer)
):
    """Get information about the ML model."""
    if not settings.ml_weight_optimization_enabled:
        return ModelInfoResponse(
            success=False,
            error="ML weight optimization is disabled in settings"
        )

    try:
        logger.info("Getting ML model information")

        # Get model info
        model_info = await optimizer.get_model_info()

        return ModelInfoResponse(
            success=True,
            model_info=model_info
        )
    except Exception as e:
        logger.error(f"Error getting ML model information: {e}")
        return ModelInfoResponse(
            success=False,
            error=str(e)
        )


@router.get("/status", response_model=Dict[str, Any])
async def get_ml_status():
    """Get the status of ML weight optimization."""
    return {
        "enabled": settings.ml_weight_optimization_enabled,
        "model_path": settings.ml_model_path,
        "training_interval_hours": settings.ml_training_interval_hours,
        "lookback_days": settings.ml_lookback_days,
        "window_size": settings.ml_window_size,
        "training_timesteps": settings.ml_training_timesteps,
        "hyperparameter_optimization": settings.ml_hyperparameter_optimization,
        "optimization_trials": settings.ml_optimization_trials,
        "reward_function": settings.ml_reward_function,
        "trading_symbol": settings.trading_symbol,
        "timeframe": settings.timeframe
    }


@router.post("/toggle", response_model=Dict[str, Any])
async def toggle_ml_optimization(
    request: ToggleRequest
):
    """Toggle ML weight optimization."""
    try:
        # Update the setting
        settings.ml_weight_optimization_enabled = request.enabled

        # Return the updated status
        return {
            "success": True,
            "enabled": settings.ml_weight_optimization_enabled
        }
    except Exception as e:
        logger.error(f"Error toggling ML optimization: {e}")
        return {
            "success": False,
            "error": str(e)
        }
