#!/usr/bin/env python3
"""
Fix Supabase Schema Issues
This script addresses the missing metadata column and other schema issues.
Date: June 16, 2025
"""

import asyncio
import aiohttp
import os
from dotenv import load_dotenv
import json
from typing import List, Dict, Any

# Load environment variables
load_dotenv('.env')

class SupabaseSchemaPatcher:
    """Fix Supabase schema issues"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        
        self.api_url = f"{self.supabase_url}/rest/v1"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
    
    async def check_column_exists(self, session: aiohttp.ClientSession, table_name: str, column_name: str) -> bool:
        """Check if a column exists in a table"""
        try:
            # Try to query the specific column
            async with session.get(
                f"{self.api_url}/{table_name}?select={column_name}&limit=1",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    return True
                elif response.status == 400:
                    # Column doesn't exist
                    return False
                else:
                    print(f"Unexpected response checking {table_name}.{column_name}: {response.status}")
                    return False
        except Exception as e:
            print(f"Error checking column {table_name}.{column_name}: {e}")
            return False
    
    async def add_missing_column(self, session: aiohttp.ClientSession, table_name: str, column_definition: str) -> bool:
        """Add a missing column to a table"""
        try:
            # Use SQL to add the column
            sql_command = f"ALTER TABLE {table_name} ADD COLUMN IF NOT EXISTS {column_definition};"
            
            # Create a simple RPC function to execute SQL
            async with session.post(
                f"{self.supabase_url}/rest/v1/rpc/exec_sql",
                headers=self.headers,
                json={"sql": sql_command}
            ) as response:
                if response.status == 200:
                    print(f"✓ Added column to {table_name}: {column_definition}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"✗ Failed to add column: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"✗ Error adding column to {table_name}: {e}")
            return False
    
    async def verify_table_structure(self, session: aiohttp.ClientSession):
        """Verify and fix table structures"""
        print("Verifying table structures...")
        
        # Define expected columns for each table
        table_schema_fixes = {
            'portfolio_metrics': [
                ('metadata', 'metadata JSONB DEFAULT \'{}\''),
                ('portfolio_value', 'portfolio_value DECIMAL(20, 2)'),
                ('total_return', 'total_return DECIMAL(10, 6) DEFAULT 0'),
                ('sharpe_ratio', 'sharpe_ratio DECIMAL(10, 6) DEFAULT 0'),
                ('max_drawdown', 'max_drawdown DECIMAL(10, 6) DEFAULT 0'),
                ('win_rate', 'win_rate DECIMAL(5, 4) DEFAULT 0'),
                ('symbol', 'symbol TEXT DEFAULT \'PORTFOLIO\''),
                ('timestamp', 'timestamp TIMESTAMPTZ NOT NULL'),
                ('created_at', 'created_at TIMESTAMPTZ DEFAULT NOW()'),
                ('updated_at', 'updated_at TIMESTAMPTZ DEFAULT NOW()')
            ],
            'trade_executions': [
                ('metadata', 'metadata JSONB DEFAULT \'{}\''),
                ('strategy_name', 'strategy_name TEXT NOT NULL'),
                ('symbol', 'symbol TEXT NOT NULL'),
                ('action', 'action TEXT NOT NULL'),
                ('quantity', 'quantity DECIMAL(20, 8) NOT NULL'),
                ('price', 'price DECIMAL(20, 8) NOT NULL'),
                ('timestamp', 'timestamp TIMESTAMPTZ NOT NULL'),
                ('created_at', 'created_at TIMESTAMPTZ DEFAULT NOW()'),
                ('updated_at', 'updated_at TIMESTAMPTZ DEFAULT NOW()')
            ]
        }
        
        fixes_applied = 0
        
        for table_name, columns in table_schema_fixes.items():
            print(f"\nChecking table: {table_name}")
            
            for column_name, column_definition in columns:
                exists = await self.check_column_exists(session, table_name, column_name)
                
                if exists:
                    print(f"  ✓ {column_name}: EXISTS")
                else:
                    print(f"  ✗ {column_name}: MISSING - Adding...")
                    success = await self.add_missing_column(session, table_name, column_definition)
                    if success:
                        fixes_applied += 1
        
        return fixes_applied
    
    async def test_database_operations(self, session: aiohttp.ClientSession):
        """Test database operations after fixes"""
        print("\nTesting database operations...")
        
        # Test 1: Insert portfolio metrics with metadata
        try:
            test_portfolio_data = {
                'portfolio_value': 100000.0,
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'symbol': 'TEST_PORTFOLIO',
                'timestamp': '2025-06-16T01:24:11.181Z',
                'metadata': {
                    'test': True,
                    'schema_fix': 'applied',
                    'timestamp': '2025-06-16T01:24:11.181Z'
                }
            }
            
            async with session.post(
                f"{self.api_url}/portfolio_metrics",
                headers=self.headers,
                json=test_portfolio_data
            ) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    print("✓ Portfolio metrics insertion test passed")
                    
                    # Clean up test data
                    if result and len(result) > 0:
                        test_id = result[0]['id']
                        async with session.delete(
                            f"{self.api_url}/portfolio_metrics?id=eq.{test_id}",
                            headers=self.headers
                        ) as del_response:
                            if del_response.status in [200, 204]:
                                print("✓ Test data cleanup successful")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"✗ Portfolio metrics test failed: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"✗ Database operation test failed: {e}")
            return False
    
    async def fix_schema_issues(self):
        """Main function to fix all schema issues"""
        print("=" * 60)
        print("FIXING SUPABASE SCHEMA ISSUES")
        print("=" * 60)
        
        async with aiohttp.ClientSession() as session:
            # Step 1: Verify and fix table structures
            fixes_applied = await self.verify_table_structure(session)
            
            # Step 2: Test database operations
            if fixes_applied > 0:
                print(f"\n{fixes_applied} schema fixes applied. Testing operations...")
                await asyncio.sleep(2)  # Give database time to process changes
            
            test_success = await self.test_database_operations(session)
            
            if test_success:
                print("\n" + "=" * 60)
                print("✅ SCHEMA FIXES SUCCESSFUL")
                print("Supabase database schema is now correctly configured!")
                print("Paper trading tests should now work without hanging.")
                print("=" * 60)
                return True
            else:
                print("\n" + "=" * 60)
                print("❌ SCHEMA FIXES INCOMPLETE")
                print("Some issues remain. Check error messages above.")
                print("=" * 60)
                return False

async def main():
    """Main function"""
    try:
        patcher = SupabaseSchemaPatcher()
        success = await patcher.fix_schema_issues()
        return success
        
    except Exception as e:
        print(f"\n❌ Schema fix failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)