/**
 * API Service
 *
 * This service handles API requests with automatic token refresh.
 */
import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import tokenService from './tokenService';
import authService from './authService';

// API URL from environment variable
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add authorization header
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add authorization header if token exists
    const token = tokenService.getAccessToken();
    const tokenType = tokenService.getTokenType();

    if (token && tokenType && config.headers) {
      config.headers.Authorization = `${tokenType} ${token}`;
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    // Get the original request
    const originalRequest = error.config as InternalAxiosRequestConfig;

    // Check if the error is 401 Unauthorized and we haven't already tried to refresh the token
    if (
      error.response?.status === 401 &&
      originalRequest &&
      !(originalRequest as any)._retry
    ) {
      // Mark the request as retried
      (originalRequest as any)._retry = true;

      try {
        // Try to refresh the token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          // Update the authorization header with the new token
          const token = tokenService.getAccessToken();
          const tokenType = tokenService.getTokenType();

          if (token && tokenType && originalRequest.headers) {
            originalRequest.headers.Authorization = `${tokenType} ${token}`;
          }

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // If refresh failed, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // If refresh failed, redirect to login
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // If not a 401 error or we've already tried to refresh, reject the promise
    return Promise.reject(error);
  }
);

// Export the API client
export default apiClient;
