# Claude AI Assistant Guidelines

## Python Virtual Environment Usage

**CRITICAL REQUIREMENT**: When accessing or installing any Python package, the virtual environment must always be activated first in the same command line using `source venv/bin/activate &&`.

Examples:
- `source venv/bin/activate && pip install package_name`
- `source venv/bin/activate && python script.py`
- `source venv/bin/activate && pytest tests/`
- `source venv/bin/activate && uv add package_name`

This ensures all Python operations use the project's isolated environment and prevents conflicts with system packages.

## Command Execution Rules

**CRITICAL REQUIREMENT**: Always run commands from the project directory unless absolutely necessary to run from elsewhere.

### Directory Management Protocol
1. **Default Location**: All commands should be executed from `/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/`
2. **Error Handling**: When encountering "No such file or directory" errors:
   - First navigate back to the project directory: `cd /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2`
   - Then re-run the command from the project root
   - Alternatively, use complete absolute paths to files

### Examples
```bash
# Preferred approach - from project directory
cd /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2
source venv/bin/activate && python script.py

# Alternative - using absolute paths
source /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/venv/bin/activate && python /path/to/script.py
```

### Why This Matters
- Virtual environment activation relies on relative paths
- MCP server configurations use project-relative paths
- Many scripts expect to run from the project root
- Prevents path resolution errors and missing file issues

### Timeout Handling Rules
**CRITICAL REQUIREMENT**: When a command or test is failing or erroring due to timeout, increase the timeout significantly to allow the process to finish.

Examples:
- Use `timeout 60s` instead of `timeout 30s` for complex operations
- For integration tests with database operations, use `timeout 120s` or more
- For ML training or data processing, use `timeout 300s` or remove timeout entirely
- When debugging hanging processes, add debug prints to identify bottlenecks

```bash
# Examples of appropriate timeout usage
source venv/bin/activate && timeout 60s python test_analytics_integration.py
source venv/bin/activate && timeout 120s python test_ensemble_standalone.py
source venv/bin/activate && timeout 300s python scripts/train_model.py
```

This prevents premature termination of legitimate long-running operations and ensures tests have adequate time to complete.

## MCP Server Configuration

The following MCP servers have been configured for the strategy ensemble system:

### Currently Installed
- **Supabase**: Portfolio analytics and real-time data storage
- **Telegram**: Real-time alerts and notifications  
- **Redis Trading**: Custom trading-specific caching (local)
- **Playwright**: Automated testing
- **GitHub**: Repository management
- **Jupyter**: Interactive development
- **Time**: Timestamp utilities

### Recently Added
- **Redis**: General real-time caching (`@modelcontextprotocol/server-redis`)
- **Weights & Biases**: ML experiment tracking (`wandb-mcp-server`)
- **MLflow**: Model deployment and lifecycle management
- **ZenML**: ML pipeline orchestration (local: `mcp-servers/mcp-zenml/`)
- **CoinCap**: Cryptocurrency data and cross-exchange validation (local: `mcp-servers/mcp-crypto-price/`)

### MCP Server Management Rules
**CRITICAL**: When fixing or dealing with MCPs, always refer to `claude mcp help` as the primary guide. There are no config files for MCPs - they are managed directly through Claude's MCP system.

### Testing MCP Servers
After installing any MCP server, test it using:
```bash
claude mcp list
```

For troubleshooting MCP issues:
```bash
claude mcp help
```

## Timestamp and Documentation Rules

**CRITICAL REQUIREMENT**: Always use the Time MCP to get the correct current timestamp before adding any dates to files or documentation.

### Timestamp Protocol
1. **Before Adding Timestamps**: Always call `mcp__Time-MCP__get_current_time` first
2. **Documentation Updates**: When updating task completion dates, milestones, or any time-sensitive information, use the accurate current time from the Time MCP
3. **Consistency**: Ensure all timestamps across files use the same format and accurate time

### Examples
```bash
# Before adding completion dates to documentation:
# 1. Get current time via MCP
# 2. Then update files with accurate timestamp

# ✅ Correct approach:
# Call mcp__Time-MCP__get_current_time first
# Then use returned timestamp: "2025-06-14T07:45:50.113Z" 
# Format as: "June 14, 2025" for human-readable dates

# ❌ Incorrect approach:
# Never guess or assume dates without checking Time MCP first
```

### Why This Matters
- Ensures accurate project timeline documentation
- Prevents confusion about when tasks were actually completed
- Maintains consistency across all project documentation
- Provides reliable audit trail for development progress

## Test File Organization Rules

**CRITICAL REQUIREMENT**: All test files must be properly categorized and placed in the appropriate directory under `/tests/` based on their testing purpose and scope.

### Test Directory Structure

The following test directory structure must be maintained:

```
tests/
├── api/                  # External API integration tests
│   ├── database/         # Database API tests (Supabase, PostgreSQL)
│   ├── data/            # Data source API tests (CoinCap, market data)
│   ├── exchanges/       # Exchange API tests (Binance, testnet)
│   └── ml/              # ML service API tests (W&B, MLflow)
├── e2e/                 # End-to-end system tests
│   └── exchanges/       # Exchange-specific E2E tests
├── features/            # Specific feature functionality tests
│   ├── ml/              # ML feature tests (cost tracking, rewards)
│   ├── monitoring/      # Monitoring feature tests (Telegram, alerts)
│   ├── risk/           # Risk management feature tests (Kelly criterion)
│   └── trading/        # Trading feature tests (paper trading, slippage)
├── integration/         # Multi-component integration tests
│   ├── analytics/       # Analytics system integration
│   ├── api/            # Cross-API integration tests
│   ├── ensemble/       # Strategy ensemble integration
│   ├── ml/             # ML pipeline integration
│   └── system/         # Full system integration
├── performance/         # Performance and benchmark tests
│   ├── cache/          # Cache performance tests (Redis)
│   └── mcp/            # MCP service performance tests
├── services/           # Service-specific unit tests
│   └── execution/      # Execution service tests
├── strategies/         # Strategy-specific tests
├── unit/               # Individual component unit tests
│   ├── analytics/      # Analytics component tests
│   ├── components/     # General component tests
│   └── strategies/     # Strategy component tests
└── validation/         # Verification and compliance tests
    ├── exchanges/      # Exchange validation tests
    ├── mcp/           # MCP service validation
    └── performance/   # Performance validation tests
```

### Test File Categorization Rules

**When creating a new test file, determine its category using these guidelines:**

#### 1. **API Tests** (`/tests/api/`)
- **Purpose**: Test external API connections and integrations
- **Examples**: Binance API, Supabase API, W&B API, CoinCap API
- **Naming**: `test_[service]_api.py` or `test_[service]_connection.py`
- **Subdirectories**:
  - `database/` - Database APIs (Supabase, PostgreSQL)
  - `data/` - Data source APIs (CoinCap, market feeds)
  - `exchanges/` - Trading platform APIs (Binance, testnet)
  - `ml/` - ML service APIs (W&B, MLflow, ZenML)

#### 2. **E2E Tests** (`/tests/e2e/`)
- **Purpose**: Test complete workflows end-to-end
- **Examples**: Full trading cycle, complete system validation
- **Naming**: `test_[workflow]_e2e.py` or `test_[system]_comprehensive.py`
- **Subdirectories**: Group by major system area (exchanges, analytics, etc.)

#### 3. **Feature Tests** (`/tests/features/`)
- **Purpose**: Test specific features or capabilities
- **Examples**: Cost-aware rewards, paper trading, slippage estimation
- **Naming**: `test_[feature_name].py`
- **Subdirectories**:
  - `ml/` - ML features (cost tracking, strategy tracking)
  - `monitoring/` - Monitoring features (Telegram, alerts)
  - `risk/` - Risk management features (Kelly criterion, position sizing)
  - `trading/` - Trading features (paper trading, slippage, execution)

#### 4. **Integration Tests** (`/tests/integration/`)
- **Purpose**: Test interactions between multiple components
- **Examples**: Strategy ensemble coordination, multi-service workflows
- **Naming**: `test_[components]_integration.py`
- **Subdirectories**:
  - `analytics/` - Analytics system integration
  - `api/` - Multi-API integration tests
  - `ensemble/` - Strategy ensemble integration
  - `ml/` - ML pipeline integration
  - `system/` - Full system integration

#### 5. **Performance Tests** (`/tests/performance/`)
- **Purpose**: Test speed, load, and benchmark performance
- **Examples**: Position sizing speed, Redis latency, API response times
- **Naming**: `test_[component]_performance.py`
- **Subdirectories**:
  - `cache/` - Cache performance (Redis, memory)
  - `mcp/` - MCP service performance

#### 6. **Unit Tests** (`/tests/unit/`)
- **Purpose**: Test individual components in isolation
- **Examples**: Portfolio manager, strategy components, calculators
- **Naming**: `test_[component].py`
- **Subdirectories**:
  - `analytics/` - Analytics components
  - `components/` - General components
  - `strategies/` - Strategy components

#### 7. **Validation Tests** (`/tests/validation/`)
- **Purpose**: Verify compliance, correctness, and validation
- **Examples**: Baseline comparisons, cross-exchange validation
- **Naming**: `test_[validation_type].py`
- **Subdirectories**:
  - `exchanges/` - Exchange data validation
  - `mcp/` - MCP service validation
  - `performance/` - Performance validation

### Test File Naming Conventions

**CRITICAL REQUIREMENTS**:

1. **File Names**: Always use `test_[descriptive_name].py` format
2. **No Task Numbers**: Remove task number prefixes (e.g., `test_task_3_1_1_` → `test_`)
3. **Descriptive Names**: Use clear, descriptive names that indicate what's being tested
4. **Consistent Patterns**:
   - API tests: `test_[service]_api.py`
   - Performance tests: `test_[component]_performance.py`
   - Integration tests: `test_[components]_integration.py`
   - Feature tests: `test_[feature_name].py`

### Import Path Management

**CRITICAL REQUIREMENT**: Ensure correct import paths when creating test files in subdirectories.

#### For Files 2-3 Levels Deep (Most Common)
```python
import sys
import os
# Add project root to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../..'))

# Now you can import from app modules
from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient
```

#### For Files at Different Depths
- **1 level deep** (`/tests/unit/`): Use `'../..'`
- **2 levels deep** (`/tests/api/`): Use `'../../..'`
- **3 levels deep** (`/tests/api/database/`): Use `'../../..'`

#### Alternative: Use Absolute Paths (Recommended)
```python
import sys
# Add absolute project root path
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

from app.config.settings import Settings
```

### Test File Creation Workflow

**When creating a new test file, follow these steps:**

1. **Determine Category**: Use the categorization rules above
2. **Choose Location**: Select appropriate subdirectory under `/tests/`
3. **Name File**: Follow naming conventions
4. **Set Up Imports**: Use correct path setup for the subdirectory depth
5. **Verify**: Ensure the test can import required modules

### Examples

#### Creating an API Test
```python
# File: /tests/api/exchanges/test_new_exchange_api.py
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../..'))

from app.services.exchange.new_exchange_client import NewExchangeClient
```

#### Creating a Feature Test
```python
# File: /tests/features/trading/test_new_trading_feature.py
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../..'))

from app.strategies.new_trading_feature import NewTradingFeature
```

#### Creating a Performance Test
```python
# File: /tests/performance/test_new_component_performance.py
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../..'))

from app.services.new_component import NewComponent
```

### Maintenance Rules

1. **Never Create Tests in Project Root**: All new test files must go in appropriate `/tests/` subdirectories
2. **Clean Up Old Tests**: Move any test files found in the root to appropriate categories
3. **Update Documentation**: Update this guide when adding new test categories
4. **Verify Organization**: Regularly check that test files are in correct locations

### Running Tests by Category

With this organization, you can run specific test categories:

```bash
# Run all API tests
source venv/bin/activate && pytest tests/api/

# Run performance tests only
source venv/bin/activate && pytest tests/performance/

# Run specific feature tests
source venv/bin/activate && pytest tests/features/trading/

# Run all tests
source venv/bin/activate && pytest tests/
```

## Project Structure Notes

- Virtual environment located at `./venv/`
- MCP servers stored in `./mcp-servers/` for local implementations
- Requirements managed in `requirements.txt` and `requirements_current.txt`
- Test files organized in `/tests/` with category-based subdirectories