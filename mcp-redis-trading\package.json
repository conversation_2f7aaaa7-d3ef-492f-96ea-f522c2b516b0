{"name": "mcp-redis-trading", "version": "1.0.0", "description": "Custom Redis MCP server for crypto trading ensemble operations", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "node test-mcp.js", "test:interactive": "node test-mcp.js --interactive", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "install:global": "./install.sh --global", "install:local": "./install.sh"}, "keywords": ["mcp", "redis", "trading", "crypto", "portfolio", "ensemble"], "author": "Crypto Trading Ensemble", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1", "redis": "^4.6.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0"}, "bin": {"mcp-redis-trading": "dist/index.js"}, "files": ["dist"]}