#!/usr/bin/env node

/**
 * Simple test script for Redis Trading MCP Server
 * Usage: node test-mcp.js
 */

import { spawn } from 'child_process';
import readline from 'readline';

class MCPTester {
  constructor() {
    this.process = null;
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async startMCPServer() {
    console.log('🚀 Starting Redis Trading MCP Server...\n');
    
    this.process = spawn('node', ['dist/index.js'], {
      stdio: ['pipe', 'pipe', 'inherit']
    });

    this.process.on('error', (error) => {
      console.error('❌ Failed to start MCP server:', error.message);
      process.exit(1);
    });

    // Wait a moment for the server to start
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('✅ MCP Server started\n');
  }

  async sendMCPRequest(request) {
    return new Promise((resolve, reject) => {
      let response = '';
      
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 5000);

      const onData = (data) => {
        response += data.toString();
        try {
          const jsonResponse = JSON.parse(response);
          clearTimeout(timeout);
          this.process.stdout.off('data', onData);
          resolve(jsonResponse);
        } catch (e) {
          // Continue collecting data
        }
      };

      this.process.stdout.on('data', onData);
      this.process.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async testListTools() {
    console.log('🔧 Testing list_tools...');
    
    const request = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list'
    };

    try {
      const response = await this.sendMCPRequest(request);
      if (response.result && response.result.tools) {
        console.log(`✅ Found ${response.result.tools.length} tools:`);
        response.result.tools.forEach(tool => {
          console.log(`   • ${tool.name}: ${tool.description}`);
        });
        console.log();
        return true;
      } else {
        console.log('❌ Invalid response format');
        return false;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      return false;
    }
  }

  async testCacheOperations() {
    console.log('💾 Testing cache operations...');

    // Test strategy weights caching
    const cacheRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'cache_strategy_weights',
        arguments: {
          weights: {
            'GridStrategy': { weight: 0.4, confidence: 0.8 },
            'TechnicalAnalysisStrategy': { weight: 0.35, confidence: 0.75 },
            'TrendFollowingStrategy': { weight: 0.25, confidence: 0.7 }
          },
          ttl: 60
        }
      }
    };

    try {
      const cacheResponse = await this.sendMCPRequest(cacheRequest);
      console.log('✅ Cache operation successful');

      // Test retrieval
      const getRequest = {
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'get_strategy_weights',
          arguments: {}
        }
      };

      const getResponse = await this.sendMCPRequest(getRequest);
      console.log('✅ Cache retrieval successful');
      console.log();
      return true;
    } catch (error) {
      console.log(`❌ Cache test failed: ${error.message}`);
      return false;
    }
  }

  async testCacheStats() {
    console.log('📊 Testing cache statistics...');

    const request = {
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'get_cache_stats',
        arguments: {}
      }
    };

    try {
      const response = await this.sendMCPRequest(request);
      if (response.result) {
        console.log('✅ Cache stats retrieved successfully');
        console.log();
        return true;
      } else {
        console.log('❌ Failed to get cache stats');
        return false;
      }
    } catch (error) {
      console.log(`❌ Cache stats test failed: ${error.message}`);
      return false;
    }
  }

  async runTests() {
    console.log('🧪 Redis Trading MCP Server Test Suite\n');
    console.log('=' .repeat(50));

    const results = [];

    try {
      await this.startMCPServer();

      // Run test suite
      results.push(await this.testListTools());
      results.push(await this.testCacheOperations());
      results.push(await this.testCacheStats());

      // Summary
      console.log('=' .repeat(50));
      const passed = results.filter(r => r).length;
      const total = results.length;
      
      if (passed === total) {
        console.log(`🎉 All tests passed! (${passed}/${total})`);
        console.log('✅ Redis Trading MCP Server is working correctly');
      } else {
        console.log(`⚠️  Some tests failed (${passed}/${total})`);
        console.log('❌ Please check Redis connection and server logs');
      }

    } catch (error) {
      console.error('💥 Test suite failed:', error.message);
    } finally {
      if (this.process) {
        this.process.kill();
      }
      this.rl.close();
      process.exit(0);
    }
  }

  async promptTest() {
    console.log('\n🔧 Interactive Testing Mode');
    console.log('Available commands:');
    console.log('  - list: List all available tools');
    console.log('  - cache: Test cache operations');
    console.log('  - stats: Get cache statistics');
    console.log('  - exit: Exit interactive mode');
    
    const askQuestion = () => {
      this.rl.question('\nEnter command: ', async (command) => {
        switch (command.toLowerCase()) {
          case 'list':
            await this.testListTools();
            askQuestion();
            break;
          case 'cache':
            await this.testCacheOperations();
            askQuestion();
            break;
          case 'stats':
            await this.testCacheStats();
            askQuestion();
            break;
          case 'exit':
            console.log('👋 Goodbye!');
            if (this.process) this.process.kill();
            this.rl.close();
            process.exit(0);
            break;
          default:
            console.log('❓ Unknown command. Try: list, cache, stats, or exit');
            askQuestion();
        }
      });
    };

    askQuestion();
  }
}

// Main execution
async function main() {
  const tester = new MCPTester();
  
  const args = process.argv.slice(2);
  
  if (args.includes('--interactive') || args.includes('-i')) {
    await tester.startMCPServer();
    await tester.promptTest();
  } else {
    await tester.runTests();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

main().catch(console.error);