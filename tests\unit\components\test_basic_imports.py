#!/usr/bin/env python3
"""
Test basic imports without complex dependencies
"""
import sys
import os
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=== Basic Import Test ===")
print(f"Test started at: {datetime.now()}")

try:
    # Test basic Python imports first
    print("\n1. Testing basic Python imports...")
    import asyncio
    import json
    import uuid
    from dataclasses import dataclass
    from enum import Enum
    print("✓ Basic Python imports successful")
    
    # Test basic data structures only
    print("\n2. Testing data structures...")
    
    @dataclass
    class MarketData:
        symbol: str
        timestamp: datetime
        price: float
        volume: float
        high_24h: float
        low_24h: float
        change_24h: float

    class TradingSessionStatus(Enum):
        RUNNING = "running"
        STOPPED = "stopped"
        PAUSED = "paused"
        ERROR = "error"
        INITIALIZING = "initializing"
        STOPPING = "stopping"

    @dataclass
    class TradingParameters:
        max_position_size: float = 0.1
        symbols: list = None
        
        def __post_init__(self):
            if self.symbols is None:
                self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
    
    print("✓ Data structures created successfully")
    
    # Test creating instances
    print("\n3. Testing instance creation...")
    
    params = TradingParameters()
    assert params.max_position_size == 0.1
    assert params.symbols == ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
    print("✓ TradingParameters instance created")
    
    market_data = MarketData(
        symbol="BTCUSDT",
        timestamp=datetime.now(),
        price=50000.0,
        volume=1000000,
        high_24h=51000.0,
        low_24h=49000.0,
        change_24h=0.02
    )
    assert market_data.symbol == "BTCUSDT"
    print("✓ MarketData instance created")
    
    status = TradingSessionStatus.RUNNING
    assert status == TradingSessionStatus.RUNNING
    print("✓ TradingSessionStatus enum working")
    
    print("\n" + "="*50)
    print("🎉 ALL BASIC TESTS PASSED!")
    print("Basic data structures and enums are working correctly")
    print("="*50)
    
    print(f"\nBasic Test Summary:")
    print(f"✅ Python standard library: Working")
    print(f"✅ Dataclasses: Working")
    print(f"✅ Enums: Working")
    print(f"✅ Instance creation: Working")
    
    print(f"\n✅ BASIC FOUNDATION: READY")
    
except Exception as e:
    print(f"\n❌ Basic test failed with error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print(f"\nBasic test completed at: {datetime.now()}")