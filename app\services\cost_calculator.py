#!/usr/bin/env python3
"""
Cost-Aware Trading Cost Calculator for Task 3.1.1
Implements comprehensive trading cost calculation including fees, slippage, funding, and market impact.

Features:
- Trading fee calculation (maker/taker fees for different exchanges)
- Slippage estimation using cross-exchange data
- Funding costs for leveraged positions
- Market impact modeling
- Real-time cost optimization
"""

import asyncio
import json
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging
from enum import Enum

from app.services.mcp.cross_exchange_validator import CrossExchangeValidator, ExchangeDataPoint
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

class OrderType(Enum):
    """Order types for cost calculation"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_MARKET = "stop_market"
    STOP_LIMIT = "stop_limit"

@dataclass
class TradingFees:
    """Trading fees structure for different exchanges"""
    exchange: str
    maker_fee: float  # Percentage (e.g., 0.001 = 0.1%)
    taker_fee: float  # Percentage
    funding_rate: float  # Funding rate for perpetual contracts
    withdrawal_fee: float  # Fixed withdrawal fee
    minimum_fee: float  # Minimum fee in quote currency

@dataclass
class SlippageEstimate:
    """Slippage estimation result"""
    symbol: str
    trade_size_usd: float
    estimated_slippage_bps: float  # Basis points (0.01% = 1 bp)
    price_impact_pct: float  # Percentage price impact
    liquidity_score: float  # 0-1 score indicating market liquidity
    confidence: float  # Confidence in the estimate
    data_sources_used: int
    market_depth_analysis: Dict[str, Any]

@dataclass
class MarketImpact:
    """Market impact calculation result"""
    symbol: str
    trade_size_usd: float
    temporary_impact_pct: float  # Temporary price impact
    permanent_impact_pct: float  # Permanent price impact
    recovery_time_minutes: float  # Expected time for price recovery
    volatility_adjustment: float  # Volatility-based adjustment factor

@dataclass
class TotalTradingCost:
    """Complete trading cost breakdown"""
    symbol: str
    trade_size_usd: float
    order_type: str
    
    # Fee components
    exchange_fees_usd: float
    funding_costs_usd: float
    withdrawal_fees_usd: float
    
    # Market microstructure costs
    slippage_cost_usd: float
    market_impact_cost_usd: float
    bid_ask_spread_cost_usd: float
    
    # Total costs
    total_cost_usd: float
    total_cost_bps: float  # Basis points relative to trade size
    cost_percentage: float  # Percentage of trade value
    
    # Metadata
    calculation_timestamp: datetime
    cost_breakdown: Dict[str, float]
    optimization_suggestions: List[str]
    confidence: float

class CostCalculator:
    """
    Comprehensive trading cost calculator for cost-aware strategy optimization.
    
    Features:
    - Multi-exchange fee calculation
    - Real-time slippage estimation using cross-exchange data
    - Market impact modeling based on order size and liquidity
    - Funding cost calculation for leveraged positions
    - Cost optimization recommendations
    """
    
    def __init__(
        self,
        cross_exchange_validator: CrossExchangeValidator,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        self.cross_exchange_validator = cross_exchange_validator
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.config = config or self._default_config()
        
        # Cache keys
        self.COST_CACHE_KEY = "trading_costs:calculation"
        self.FEES_CACHE_KEY = "trading_costs:fees"
        self.SLIPPAGE_CACHE_KEY = "trading_costs:slippage"
        
        # Exchange configurations for cross-validation
        self.exchange_configs = {
            "binance": {"weight": 0.4, "api_endpoint": "https://fapi.binance.com"},
            "coinbase": {"weight": 0.25, "api_endpoint": "https://api.exchange.coinbase.com"},
            "kraken": {"weight": 0.2, "api_endpoint": "https://api.kraken.com"},
            "ftx": {"weight": 0.15, "api_endpoint": "https://ftx.com/api"}
        }
        
        # Exchange fee structures (real-world data)
        self.exchange_fees = {
            "binance": TradingFees(
                exchange="binance",
                maker_fee=0.001,    # 0.1%
                taker_fee=0.001,    # 0.1%
                funding_rate=0.0001,  # 0.01% per 8 hours
                withdrawal_fee=0.0005,  # 0.0005 BTC for Bitcoin
                minimum_fee=0.001   # $0.001 minimum
            ),
            "coinbase": TradingFees(
                exchange="coinbase",
                maker_fee=0.005,    # 0.5%
                taker_fee=0.005,    # 0.5%
                funding_rate=0.0002,  # Higher funding rate
                withdrawal_fee=0.001,
                minimum_fee=0.01
            ),
            "kraken": TradingFees(
                exchange="kraken",
                maker_fee=0.0016,   # 0.16%
                taker_fee=0.0026,   # 0.26%
                funding_rate=0.00015,
                withdrawal_fee=0.00015,
                minimum_fee=0.002
            ),
            "ftx": TradingFees(
                exchange="ftx",
                maker_fee=0.0002,   # 0.02%
                taker_fee=0.0007,   # 0.07%
                funding_rate=0.0001,
                withdrawal_fee=0.0,  # Free withdrawals
                minimum_fee=0.001
            )
        }
        
        logger.info("CostCalculator initialized with cross-exchange validation and enhanced slippage estimation")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for cost calculation"""
        return {
            "cache_ttl_costs": 60,                 # 1 minute cache for cost calculations
            "cache_ttl_slippage": 30,              # 30 seconds for slippage estimates
            "default_exchange": "binance",         # Default exchange for calculations
            "slippage_confidence_threshold": 0.7,  # Minimum confidence for slippage estimates
            "max_market_impact_threshold": 0.05,  # 5% maximum market impact warning
            "funding_calculation_hours": 8,        # Standard funding interval
            "bid_ask_spread_multiplier": 0.5,     # Typical spread capture rate
            "volatility_impact_multiplier": 1.5,  # Volatility adjustment factor
            "large_trade_threshold_usd": 100000,  # Large trade threshold
            "enable_optimization_suggestions": True,
            "enable_cost_tracking": True
        }
    
    async def calculate_total_trading_cost(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType = OrderType.MARKET,
        exchange: str = None,
        leverage: float = 1.0
    ) -> TotalTradingCost:
        """
        Calculate comprehensive trading costs for a given trade.
        
        Args:
            symbol: Trading symbol (e.g., 'BTC')
            trade_size_usd: Trade size in USD
            order_type: Type of order (market, limit, etc.)
            exchange: Target exchange (uses default if None)
            leverage: Leverage multiplier for funding cost calculations
            
        Returns:
            Complete trading cost breakdown
        """
        start_time = time.perf_counter()
        
        try:
            # Use default exchange if not specified
            if exchange is None:
                exchange = self.config["default_exchange"]
            
            # Check cache first
            cache_key = self._generate_cost_cache_key(
                symbol, trade_size_usd, order_type.value, exchange, leverage
            )
            
            cached_cost = await self._get_cached_cost(cache_key)
            if cached_cost:
                return cached_cost
            
            # Parallel cost component calculations
            fees_task = self._calculate_exchange_fees(
                trade_size_usd, order_type, exchange, leverage
            )
            slippage_task = self._estimate_slippage_cost(
                symbol, trade_size_usd, order_type
            )
            market_impact_task = self._calculate_market_impact_cost(
                symbol, trade_size_usd
            )
            spread_task = self._calculate_bid_ask_spread_cost(
                symbol, trade_size_usd
            )
            
            # Wait for all calculations
            fees_result, slippage_result, impact_result, spread_result = await asyncio.gather(
                fees_task, slippage_task, market_impact_task, spread_task,
                return_exceptions=True
            )
            
            # Handle calculation errors gracefully
            exchange_fees = fees_result if not isinstance(fees_result, Exception) else 0.0
            slippage_cost = slippage_result if not isinstance(slippage_result, Exception) else 0.0
            market_impact_cost = impact_result if not isinstance(impact_result, Exception) else 0.0
            spread_cost = spread_result if not isinstance(spread_result, Exception) else 0.0
            
            # Calculate funding costs (for leveraged positions)
            funding_cost = await self._calculate_funding_cost(
                trade_size_usd, leverage, exchange
            )
            
            # Calculate withdrawal fees (estimated)
            withdrawal_fee = self._calculate_withdrawal_fee(exchange, symbol)
            
            # Total cost calculation
            total_cost_usd = (
                exchange_fees + funding_cost + withdrawal_fee +
                slippage_cost + market_impact_cost + spread_cost
            )
            
            # Convert to basis points and percentage
            total_cost_bps = (total_cost_usd / trade_size_usd) * 10000 if trade_size_usd > 0 else 0
            cost_percentage = (total_cost_usd / trade_size_usd) * 100 if trade_size_usd > 0 else 0
            
            # Cost breakdown for analysis
            cost_breakdown = {
                "exchange_fees": exchange_fees,
                "funding_costs": funding_cost,
                "withdrawal_fees": withdrawal_fee,
                "slippage_cost": slippage_cost,
                "market_impact_cost": market_impact_cost,
                "bid_ask_spread_cost": spread_cost,
                "fees_percentage": (exchange_fees / trade_size_usd) * 100 if trade_size_usd > 0 else 0,
                "market_microstructure_percentage": ((slippage_cost + market_impact_cost + spread_cost) / trade_size_usd) * 100 if trade_size_usd > 0 else 0
            }
            
            # Generate optimization suggestions
            optimization_suggestions = await self._generate_cost_optimization_suggestions(
                symbol, trade_size_usd, cost_breakdown, exchange
            )
            
            # Calculate confidence based on data quality
            confidence = await self._calculate_cost_confidence(
                symbol, trade_size_usd, order_type
            )
            
            result = TotalTradingCost(
                symbol=symbol,
                trade_size_usd=trade_size_usd,
                order_type=order_type.value,
                
                # Fee components
                exchange_fees_usd=exchange_fees,
                funding_costs_usd=funding_cost,
                withdrawal_fees_usd=withdrawal_fee,
                
                # Market microstructure costs
                slippage_cost_usd=slippage_cost,
                market_impact_cost_usd=market_impact_cost,
                bid_ask_spread_cost_usd=spread_cost,
                
                # Total costs
                total_cost_usd=total_cost_usd,
                total_cost_bps=total_cost_bps,
                cost_percentage=cost_percentage,
                
                # Metadata
                calculation_timestamp=datetime.now(),
                cost_breakdown=cost_breakdown,
                optimization_suggestions=optimization_suggestions,
                confidence=confidence
            )
            
            # Cache the result
            await self._cache_cost_result(cache_key, result)
            
            # Store analytics if enabled
            if self.config["enable_cost_tracking"] and self.supabase_service:
                asyncio.create_task(self._store_cost_analytics(result))
            
            calculation_time = (time.perf_counter() - start_time) * 1000
            logger.debug(f"Cost calculation completed for {symbol}: {calculation_time:.2f}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"Cost calculation failed for {symbol}: {e}")
            return self._create_fallback_cost_result(symbol, trade_size_usd, order_type)
    
    async def calculate_slippage_cost(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType = OrderType.MARKET
    ) -> Dict[str, Any]:
        """
        Calculate detailed slippage cost using enhanced multi-exchange estimation.
        
        Args:
            symbol: Trading symbol (e.g., 'BTC')
            trade_size_usd: Trade size in USD
            order_type: Type of order
            
        Returns:
            Detailed slippage cost breakdown
        """
        start_time = time.perf_counter()
        
        try:
            # Use enhanced slippage estimator if available
            try:
                from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator
                
                enhanced_estimator = EnhancedSlippageEstimator(
                    redis_service=self.redis_service,
                    cross_exchange_validator=self.cross_exchange_validator,
                    supabase_service=self.supabase_service
                )
                
                # Get comprehensive slippage analysis
                slippage_analysis = await enhanced_estimator.estimate_multi_exchange_slippage(
                    symbol=symbol,
                    trade_size_usd=trade_size_usd,
                    is_buy_order=True  # Assume buy order for cost calculation
                )
                
                # Convert to cost format
                slippage_cost_usd = (slippage_analysis.consensus_slippage_bps / 10000) * trade_size_usd
                
                calculation_time = (time.perf_counter() - start_time) * 1000
                
                return {
                    "symbol": symbol,
                    "trade_size_usd": trade_size_usd,
                    "order_type": order_type.value,
                    "slippage_cost_usd": slippage_cost_usd,
                    "slippage_bps": slippage_analysis.consensus_slippage_bps,
                    "exchange_breakdown": slippage_analysis.exchange_slippage,
                    "optimal_exchange": slippage_analysis.optimal_exchange,
                    "confidence_score": slippage_analysis.confidence_score,
                    "data_quality": slippage_analysis.data_freshness_score,
                    "liquidity_analysis": {
                        "total_liquidity": slippage_analysis.total_available_liquidity,
                        "market_depth_ratio": slippage_analysis.market_depth_ratio,
                        "liquidity_distribution": slippage_analysis.liquidity_distribution
                    },
                    "market_impact": slippage_analysis.estimated_market_impact,
                    "arbitrage_opportunities": len(slippage_analysis.arbitrage_opportunities),
                    "execution_suggestions": slippage_analysis.execution_suggestions,
                    "calculation_time_ms": calculation_time,
                    "timestamp": datetime.now().isoformat()
                }
                
            except ImportError:
                logger.warning("Enhanced slippage estimator not available, using basic calculation")
                
                # Fallback to basic slippage calculation
                basic_slippage_cost = await self._estimate_slippage_cost(symbol, trade_size_usd, order_type)
                calculation_time = (time.perf_counter() - start_time) * 1000
                
                return {
                    "symbol": symbol,
                    "trade_size_usd": trade_size_usd,
                    "order_type": order_type.value,
                    "slippage_cost_usd": basic_slippage_cost,
                    "slippage_bps": (basic_slippage_cost / trade_size_usd) * 10000 if trade_size_usd > 0 else 0,
                    "method": "basic_cross_exchange",
                    "calculation_time_ms": calculation_time,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Slippage cost calculation failed: {e}")
            calculation_time = (time.perf_counter() - start_time) * 1000
            
            return {
                "symbol": symbol,
                "trade_size_usd": trade_size_usd,
                "order_type": order_type.value,
                "error": str(e),
                "method": "error_fallback",
                "calculation_time_ms": calculation_time,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _calculate_exchange_fees(
        self,
        trade_size_usd: float,
        order_type: OrderType,
        exchange: str,
        leverage: float
    ) -> float:
        """Calculate exchange-specific trading fees"""
        
        if exchange not in self.exchange_fees:
            logger.warning(f"Unknown exchange: {exchange}, using default")
            exchange = self.config["default_exchange"]
        
        fee_structure = self.exchange_fees[exchange]
        
        # Choose appropriate fee rate based on order type
        if order_type in [OrderType.MARKET, OrderType.STOP_MARKET]:
            fee_rate = fee_structure.taker_fee
        else:  # Limit orders
            fee_rate = fee_structure.maker_fee
        
        # Calculate base fee
        base_fee = trade_size_usd * fee_rate
        
        # Apply minimum fee
        final_fee = max(base_fee, fee_structure.minimum_fee)
        
        # Leverage adjustment (higher fees for leveraged trades on some exchanges)
        if leverage > 1.0:
            leverage_multiplier = 1 + (leverage - 1) * 0.1  # 10% increase per leverage unit
            final_fee *= leverage_multiplier
        
        return final_fee
    
    async def _estimate_slippage_cost(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType
    ) -> float:
        """Estimate slippage cost using enhanced multi-exchange slippage estimator"""
        
        try:
            # Import enhanced slippage estimator if available
            try:
                from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator
                
                # Initialize enhanced slippage estimator
                enhanced_estimator = EnhancedSlippageEstimator(
                    redis_service=self.redis_service,
                    cross_exchange_validator=self.cross_exchange_validator,
                    supabase_service=self.supabase_service
                )
                
                # Get real-time slippage estimation
                slippage_result = await enhanced_estimator.estimate_real_time_slippage(
                    symbol=symbol,
                    trade_size_usd=trade_size_usd,
                    order_type=order_type.value
                )
                
                # Convert slippage from basis points to USD cost
                slippage_cost = (slippage_result.estimated_slippage_bps / 10000) * trade_size_usd
                
                logger.debug(f"Enhanced slippage estimation: {slippage_result.estimated_slippage_bps:.2f} bps for {symbol}")
                
                return slippage_cost
                
            except ImportError:
                logger.warning("Enhanced slippage estimator not available, using fallback method")
                # Fall back to original cross-exchange validation method
                pass
            
            # Fallback to original cross-exchange validation method
            validation_result = await self.cross_exchange_validator.validate_cross_exchange_data(symbol)
            
            if validation_result.source_count < 2:
                logger.warning(f"Insufficient data sources for slippage calculation: {symbol}")
                return self._estimate_fallback_slippage(trade_size_usd)
            
            # Calculate liquidity metrics from cross-exchange data
            total_volume = sum(source.volume for source in validation_result.individual_sources)
            avg_volume = total_volume / len(validation_result.individual_sources)
            
            # Volume-based slippage estimation
            volume_ratio = trade_size_usd / max(avg_volume, 1)  # Avoid division by zero
            
            # Base slippage rate (in basis points)
            base_slippage_bps = 1.0  # 1 bp base slippage
            
            # Volume impact on slippage (logarithmic scaling)
            volume_impact = np.log1p(volume_ratio * 100) * 0.5
            
            # Price variance impact (higher variance = higher slippage)
            variance_impact = (validation_result.price_variance / validation_result.consensus_price) * 1000 if validation_result.consensus_price > 0 else 0
            
            # Order type impact
            order_type_multiplier = {
                OrderType.LIMIT: 0.3,        # Lower slippage for limit orders
                OrderType.MARKET: 1.0,       # Base slippage for market orders
                OrderType.STOP_MARKET: 1.5,  # Higher slippage for stop orders
                OrderType.STOP_LIMIT: 0.8    # Moderate slippage for stop-limit
            }
            
            multiplier = order_type_multiplier.get(order_type, 1.0)
            
            # Calculate total slippage in basis points
            total_slippage_bps = (base_slippage_bps + volume_impact + variance_impact) * multiplier
            
            # Convert to USD cost
            slippage_cost = (total_slippage_bps / 10000) * trade_size_usd
            
            # Cap slippage at reasonable maximum (5% of trade value)
            max_slippage = trade_size_usd * 0.05
            slippage_cost = min(slippage_cost, max_slippage)
            
            return slippage_cost
            
        except Exception as e:
            logger.error(f"Slippage estimation failed for {symbol}: {e}")
            return self._estimate_fallback_slippage(trade_size_usd)
    
    async def _calculate_market_impact_cost(
        self,
        symbol: str,
        trade_size_usd: float
    ) -> float:
        """Calculate market impact cost based on trade size and market conditions"""
        
        try:
            # Get market data for impact calculation
            validation_result = await self.cross_exchange_validator.validate_cross_exchange_data(symbol)
            
            if not validation_result.individual_sources:
                return self._estimate_fallback_market_impact(trade_size_usd)
            
            # Calculate average daily volume across exchanges
            total_24h_volume = sum(source.volume for source in validation_result.individual_sources)
            avg_volume = total_24h_volume / len(validation_result.individual_sources)
            
            # Market impact using square-root model (widely used in literature)
            # Impact = sigma * sqrt(trade_size / daily_volume) * trade_size
            
            # Estimate volatility from price variance
            volatility = np.sqrt(validation_result.price_variance) / validation_result.consensus_price if validation_result.consensus_price > 0 else 0.02
            
            # Calculate impact fraction
            volume_fraction = trade_size_usd / max(avg_volume, trade_size_usd)  # Avoid negative impact
            impact_factor = volatility * np.sqrt(volume_fraction) * self.config["volatility_impact_multiplier"]
            
            # Apply impact to trade size
            market_impact_cost = impact_factor * trade_size_usd
            
            # Separate temporary and permanent impact
            temporary_fraction = 0.6  # 60% of impact is temporary
            permanent_fraction = 0.4  # 40% of impact is permanent
            
            # For cost calculation, we use full impact (assuming worst-case scenario)
            total_impact = market_impact_cost
            
            # Cap impact at maximum threshold
            max_impact = trade_size_usd * self.config["max_market_impact_threshold"]
            total_impact = min(total_impact, max_impact)
            
            return total_impact
            
        except Exception as e:
            logger.error(f"Market impact calculation failed for {symbol}: {e}")
            return self._estimate_fallback_market_impact(trade_size_usd)
    
    async def _calculate_bid_ask_spread_cost(
        self,
        symbol: str,
        trade_size_usd: float
    ) -> float:
        """Calculate bid-ask spread cost"""
        
        try:
            # Get cross-exchange data to estimate typical spread
            validation_result = await self.cross_exchange_validator.validate_cross_exchange_data(symbol)
            
            if not validation_result.individual_sources:
                # Fallback spread estimate (10 basis points)
                spread_cost = trade_size_usd * 0.001 * self.config["bid_ask_spread_multiplier"]
                return spread_cost
            
            # Estimate spread from price variance across exchanges
            if validation_result.consensus_price > 0:
                spread_estimate_pct = (validation_result.price_variance / validation_result.consensus_price) * 2  # Rough spread estimate
            else:
                spread_estimate_pct = 0.001  # 10 bp default
            
            # Typical spread capture rate (trader doesn't pay full spread)
            spread_capture_rate = self.config["bid_ask_spread_multiplier"]
            
            # Calculate spread cost
            spread_cost = trade_size_usd * spread_estimate_pct * spread_capture_rate
            
            # Cap spread cost at 50 basis points
            max_spread_cost = trade_size_usd * 0.005
            spread_cost = min(spread_cost, max_spread_cost)
            
            return spread_cost
            
        except Exception as e:
            logger.error(f"Bid-ask spread calculation failed for {symbol}: {e}")
            # Fallback spread cost
            return trade_size_usd * 0.001 * self.config["bid_ask_spread_multiplier"]
    
    async def _calculate_funding_cost(
        self,
        trade_size_usd: float,
        leverage: float,
        exchange: str
    ) -> float:
        """Calculate funding costs for leveraged positions with cross-validation"""
        
        if leverage <= 1.0:
            return 0.0  # No funding cost for non-leveraged positions
        
        try:
            # Calculate notional value of leveraged position
            notional_value = trade_size_usd * leverage
            
            # Get cross-validated funding rates from multiple sources
            funding_rates = await self._get_cross_validated_funding_rates(exchange)
            
            # Use consensus funding rate if available
            if funding_rates and len(funding_rates) > 0:
                consensus_funding_rate = np.median(list(funding_rates.values()))
                
                # Weight by exchange reliability
                weighted_funding_rate = 0
                total_weight = 0
                
                for exch, rate in funding_rates.items():
                    weight = self.exchange_configs.get(exch, {}).get("weight", 0.1)
                    weighted_funding_rate += rate * weight
                    total_weight += weight
                
                if total_weight > 0:
                    final_funding_rate = weighted_funding_rate / total_weight
                else:
                    final_funding_rate = consensus_funding_rate
                    
                logger.debug(f"Cross-validated funding rate: {final_funding_rate:.6f} (from {len(funding_rates)} sources)")
                
            else:
                # Fallback to exchange-specific funding rate
                if exchange not in self.exchange_fees:
                    exchange = self.config["default_exchange"]
                
                fee_structure = self.exchange_fees[exchange]
                final_funding_rate = fee_structure.funding_rate
                
                logger.warning(f"Using fallback funding rate for {exchange}: {final_funding_rate:.6f}")
            
            # Apply market condition adjustments
            funding_adjustment = await self._calculate_funding_rate_adjustment()
            adjusted_funding_rate = final_funding_rate * funding_adjustment
            
            # Calculate funding cost for one period (8 hours)
            funding_cost = notional_value * adjusted_funding_rate
            
            # Add funding volatility premium for large positions
            if trade_size_usd > self.config.get("large_trade_threshold_usd", 100000):
                volatility_premium = funding_cost * 0.1  # 10% premium for large trades
                funding_cost += volatility_premium
            
            return funding_cost
            
        except Exception as e:
            logger.error(f"Funding cost calculation failed: {e}")
            # Fallback calculation
            return await self._calculate_fallback_funding_cost(trade_size_usd, leverage, exchange)
    
    async def _get_cross_validated_funding_rates(self, target_exchange: str) -> Dict[str, float]:
        """Get funding rates from multiple exchanges for cross-validation"""
        
        funding_rates = {}
        
        try:
            # Check cache first
            cache_key = f"{self.FEES_CACHE_KEY}:funding_rates"
            cached_rates = await self.redis_service.get(cache_key)
            
            if cached_rates:
                try:
                    funding_rates = json.loads(cached_rates)
                    logger.debug("Using cached funding rates")
                    return funding_rates
                except json.JSONDecodeError:
                    pass
            
            # Get funding rates from configured exchanges
            for exchange_name, exchange_config in self.exchange_configs.items():
                try:
                    # In production, this would call real APIs
                    # For now, use configured rates with some variation
                    base_rate = self.exchange_fees.get(exchange_name, self.exchange_fees[self.config["default_exchange"]]).funding_rate
                    
                    # Add realistic variation based on market conditions
                    variation = np.random.normal(0, base_rate * 0.1)  # 10% variation
                    market_rate = max(0.00001, base_rate + variation)  # Ensure positive
                    
                    funding_rates[exchange_name] = market_rate
                    
                except Exception as e:
                    logger.warning(f"Failed to get funding rate from {exchange_name}: {e}")
            
            # Cache the results for a short time
            if funding_rates:
                await self.redis_service.setex(
                    cache_key,
                    300,  # 5 minutes cache
                    json.dumps(funding_rates)
                )
            
            return funding_rates
            
        except Exception as e:
            logger.error(f"Cross-validation of funding rates failed: {e}")
            return {}
    
    async def _calculate_funding_rate_adjustment(self) -> float:
        """Calculate funding rate adjustment based on market conditions"""
        
        try:
            # This would analyze market volatility, open interest, etc.
            # For now, return a simple adjustment based on general market conditions
            
            # Simulate market condition analysis
            market_volatility = 0.15  # Would come from real market data
            funding_stress = 1.0  # Normal market conditions
            
            # Adjust funding based on volatility
            if market_volatility > 0.3:
                funding_stress = 1.5  # High volatility increases funding costs
            elif market_volatility > 0.2:
                funding_stress = 1.2  # Medium volatility
            
            return funding_stress
            
        except Exception as e:
            logger.error(f"Funding rate adjustment calculation failed: {e}")
            return 1.0  # No adjustment
    
    async def _calculate_fallback_funding_cost(
        self,
        trade_size_usd: float,
        leverage: float,
        exchange: str
    ) -> float:
        """Fallback funding cost calculation"""
        
        if exchange not in self.exchange_fees:
            exchange = self.config["default_exchange"]
        
        fee_structure = self.exchange_fees[exchange]
        notional_value = trade_size_usd * leverage
        funding_rate_per_period = fee_structure.funding_rate
        
        return notional_value * funding_rate_per_period
    
    def _calculate_withdrawal_fee(self, exchange: str, symbol: str) -> float:
        """Calculate estimated withdrawal fees"""
        
        if exchange not in self.exchange_fees:
            exchange = self.config["default_exchange"]
        
        fee_structure = self.exchange_fees[exchange]
        
        # Withdrawal fees are typically fixed amounts, but we'll use the base rate
        # In practice, this would be asset-specific
        withdrawal_fee = fee_structure.withdrawal_fee * 50000  # Assuming $50k price for estimation
        
        return withdrawal_fee
    
    def _estimate_fallback_slippage(self, trade_size_usd: float) -> float:
        """Fallback slippage estimation when cross-exchange data is unavailable"""
        
        # Conservative slippage estimate based on trade size
        if trade_size_usd < 1000:
            slippage_bps = 2  # 2 basis points for small trades
        elif trade_size_usd < 10000:
            slippage_bps = 5  # 5 basis points for medium trades
        elif trade_size_usd < 100000:
            slippage_bps = 10  # 10 basis points for large trades
        else:
            slippage_bps = 25  # 25 basis points for very large trades
        
        return (slippage_bps / 10000) * trade_size_usd
    
    def _estimate_fallback_market_impact(self, trade_size_usd: float) -> float:
        """Fallback market impact estimation"""
        
        # Simple model based on trade size
        if trade_size_usd < 10000:
            impact_rate = 0.0001  # 1 basis point
        elif trade_size_usd < 100000:
            impact_rate = 0.0005  # 5 basis points
        else:
            impact_rate = 0.002   # 20 basis points
        
        return trade_size_usd * impact_rate
    
    async def _generate_cost_optimization_suggestions(
        self,
        symbol: str,
        trade_size_usd: float,
        cost_breakdown: Dict[str, float],
        exchange: str
    ) -> List[str]:
        """Generate cost optimization suggestions"""
        
        suggestions = []
        
        if not self.config["enable_optimization_suggestions"]:
            return suggestions
        
        try:
            # Analyze cost components
            total_fees = cost_breakdown.get("exchange_fees", 0)
            market_costs = cost_breakdown.get("slippage_cost", 0) + cost_breakdown.get("market_impact_cost", 0)
            
            # Exchange fee optimization
            if total_fees > trade_size_usd * 0.001:  # More than 10 basis points in fees
                suggestions.append("Consider using a lower-fee exchange or upgrading to VIP tier for reduced fees")
            
            # Trade size optimization
            if trade_size_usd > self.config["large_trade_threshold_usd"]:
                suggestions.append("Consider splitting large trade into smaller chunks to reduce market impact")
            
            # Order type optimization
            if market_costs > total_fees:
                suggestions.append("Market microstructure costs exceed fees - consider using limit orders")
            
            # Exchange comparison
            best_exchange = await self._find_best_exchange_for_trade(symbol, trade_size_usd)
            if best_exchange != exchange:
                suggestions.append(f"Consider trading on {best_exchange} for potentially lower costs")
            
            # Timing optimization
            suggestions.append("Monitor market volatility - trade during calmer periods to reduce slippage")
            
            # Leverage optimization
            funding_pct = cost_breakdown.get("funding_costs", 0) / trade_size_usd * 100 if trade_size_usd > 0 else 0
            if funding_pct > 0.1:  # More than 10 basis points
                suggestions.append("High funding costs detected - consider reducing leverage or shorter holding periods")
            
        except Exception as e:
            logger.error(f"Failed to generate optimization suggestions: {e}")
        
        return suggestions
    
    async def _find_best_exchange_for_trade(
        self,
        symbol: str,
        trade_size_usd: float
    ) -> str:
        """Find the exchange with lowest estimated costs for this trade"""
        
        try:
            best_exchange = self.config["default_exchange"]
            lowest_cost = float('inf')
            
            for exchange_name in self.exchange_fees.keys():
                # Quick cost estimate for comparison
                fee_structure = self.exchange_fees[exchange_name]
                estimated_cost = trade_size_usd * fee_structure.taker_fee
                
                if estimated_cost < lowest_cost:
                    lowest_cost = estimated_cost
                    best_exchange = exchange_name
            
            return best_exchange
            
        except Exception as e:
            logger.error(f"Failed to find best exchange: {e}")
            return self.config["default_exchange"]
    
    async def _calculate_cost_confidence(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType
    ) -> float:
        """Calculate confidence in cost estimation"""
        
        try:
            # Get data quality from cross-exchange validator
            validation_result = await self.cross_exchange_validator.validate_cross_exchange_data(symbol)
            
            base_confidence = validation_result.data_quality_score
            
            # Adjust confidence based on trade size (more confident for typical sizes)
            if 1000 <= trade_size_usd <= 50000:
                size_confidence = 1.0
            elif trade_size_usd < 1000:
                size_confidence = 0.8  # Less confident for very small trades
            else:
                size_confidence = 0.6  # Less confident for very large trades
            
            # Adjust for order type (more confident for market orders)
            order_confidence = {
                OrderType.MARKET: 1.0,
                OrderType.LIMIT: 0.9,
                OrderType.STOP_MARKET: 0.8,
                OrderType.STOP_LIMIT: 0.7
            }
            
            type_confidence = order_confidence.get(order_type, 0.8)
            
            # Combined confidence
            overall_confidence = (base_confidence * 0.5 + size_confidence * 0.3 + type_confidence * 0.2)
            
            return max(0.1, min(1.0, overall_confidence))
            
        except Exception as e:
            logger.error(f"Failed to calculate cost confidence: {e}")
            return 0.5  # Default medium confidence
    
    def _create_fallback_cost_result(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType
    ) -> TotalTradingCost:
        """Create fallback cost result when calculation fails"""
        
        # Conservative cost estimates
        exchange_fees = trade_size_usd * 0.001  # 10 basis points
        slippage_cost = trade_size_usd * 0.0005  # 5 basis points
        total_cost = exchange_fees + slippage_cost
        
        return TotalTradingCost(
            symbol=symbol,
            trade_size_usd=trade_size_usd,
            order_type=order_type.value,
            
            exchange_fees_usd=exchange_fees,
            funding_costs_usd=0.0,
            withdrawal_fees_usd=0.0,
            
            slippage_cost_usd=slippage_cost,
            market_impact_cost_usd=0.0,
            bid_ask_spread_cost_usd=0.0,
            
            total_cost_usd=total_cost,
            total_cost_bps=(total_cost / trade_size_usd) * 10000 if trade_size_usd > 0 else 0,
            cost_percentage=(total_cost / trade_size_usd) * 100 if trade_size_usd > 0 else 0,
            
            calculation_timestamp=datetime.now(),
            cost_breakdown={"exchange_fees": exchange_fees, "slippage_cost": slippage_cost},
            optimization_suggestions=["Cost calculation failed - using conservative estimates"],
            confidence=0.3
        )
    
    def _generate_cost_cache_key(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: str,
        exchange: str,
        leverage: float
    ) -> str:
        """Generate cache key for cost calculations"""
        
        # Round trade size to nearest $100 for cache efficiency
        size_bucket = int(trade_size_usd // 100) * 100
        leverage_bucket = round(leverage, 1)
        timestamp_bucket = int(datetime.now().timestamp() // self.config["cache_ttl_costs"])
        
        return f"{self.COST_CACHE_KEY}:{symbol}:{size_bucket}:{order_type}:{exchange}:{leverage_bucket}:{timestamp_bucket}"
    
    async def _get_cached_cost(self, cache_key: str) -> Optional[TotalTradingCost]:
        """Get cached cost calculation result"""
        try:
            cached_data = await self.redis_service.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                return TotalTradingCost(**data)
            return None
        except Exception as e:
            logger.warning(f"Failed to get cached cost: {e}")
            return None
    
    async def _cache_cost_result(self, cache_key: str, result: TotalTradingCost) -> None:
        """Cache cost calculation result"""
        try:
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_costs"],
                json.dumps(asdict(result), default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache cost result: {e}")
    
    async def _store_cost_analytics(self, result: TotalTradingCost) -> None:
        """Store cost analytics in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            analytics_data = {
                'timestamp': result.calculation_timestamp.isoformat(),
                'symbol': result.symbol,
                'trade_size_usd': result.trade_size_usd,
                'order_type': result.order_type,
                'total_cost_usd': result.total_cost_usd,
                'total_cost_bps': result.total_cost_bps,
                'cost_percentage': result.cost_percentage,
                'exchange_fees_usd': result.exchange_fees_usd,
                'slippage_cost_usd': result.slippage_cost_usd,
                'market_impact_cost_usd': result.market_impact_cost_usd,
                'confidence': result.confidence,
                'optimization_suggestion_count': len(result.optimization_suggestions)
            }
            
            # Store in trading_cost_analytics table (would need to be created)
            # await self.supabase_service.store_trading_cost_analytics(analytics_data)
            
        except Exception as e:
            logger.error(f"Failed to store cost analytics: {e}")
    
    async def get_cost_optimization_insights(
        self,
        symbol: str,
        historical_days: int = 7
    ) -> Dict[str, Any]:
        """Get cost optimization insights for a symbol over historical period"""
        
        try:
            # This would analyze historical cost data to provide insights
            # For now, return basic optimization recommendations
            
            validation_result = await self.cross_exchange_validator.validate_cross_exchange_data(symbol)
            
            insights = {
                "symbol": symbol,
                "analysis_period_days": historical_days,
                "current_market_conditions": {
                    "liquidity_score": validation_result.data_quality_score,
                    "price_volatility": validation_result.price_variance,
                    "exchange_count": validation_result.source_count,
                    "price_spread_pct": validation_result.price_spread_pct
                },
                "cost_optimization_recommendations": [
                    "Use limit orders during high volatility periods",
                    "Split large trades into smaller chunks",
                    "Monitor funding rates for leveraged positions",
                    "Consider cross-exchange arbitrage opportunities"
                ],
                "optimal_trade_sizes": {
                    "small_trade_threshold": 1000,
                    "optimal_range_min": 5000,
                    "optimal_range_max": 25000,
                    "large_trade_threshold": 100000
                },
                "best_exchanges_for_costs": await self._rank_exchanges_by_cost_efficiency(symbol)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to get cost optimization insights: {e}")
            return {"error": str(e)}
    
    async def _rank_exchanges_by_cost_efficiency(self, symbol: str) -> List[Dict[str, Any]]:
        """Rank exchanges by cost efficiency for the given symbol"""
        
        rankings = []
        
        for exchange_name, fee_structure in self.exchange_fees.items():
            # Calculate total cost for a standard trade size
            standard_size = 10000  # $10k standard trade
            
            cost_estimate = await self._calculate_exchange_fees(
                standard_size, OrderType.MARKET, exchange_name, 1.0
            )
            
            cost_bps = (cost_estimate / standard_size) * 10000
            
            rankings.append({
                "exchange": exchange_name,
                "estimated_cost_bps": round(cost_bps, 2),
                "maker_fee_pct": fee_structure.maker_fee * 100,
                "taker_fee_pct": fee_structure.taker_fee * 100,
                "funding_rate_daily": fee_structure.funding_rate * 3 * 100  # 3 periods per day
            })
        
        # Sort by cost (lowest first)
        rankings.sort(key=lambda x: x["estimated_cost_bps"])
        
        return rankings

# Factory function for easy initialization
async def create_cost_calculator(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[Dict] = None
) -> CostCalculator:
    """Factory function to create cost calculator with dependencies"""
    
    from app.services.mcp.redis_service import RedisService
    from app.services.mcp.cross_exchange_validator import create_cross_exchange_validator
    
    # Initialize services
    redis_service = RedisService(redis_url)
    await redis_service.connect()
    
    cross_exchange_validator = await create_cross_exchange_validator(
        redis_url, supabase_url, supabase_key
    )
    
    supabase_service = None
    if supabase_url and supabase_key:
        from app.services.mcp.supabase_service import SupabaseService
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return CostCalculator(
        cross_exchange_validator=cross_exchange_validator,
        redis_service=redis_service,
        supabase_service=supabase_service,
        config=config
    )

if __name__ == "__main__":
    async def test_cost_calculator():
        """Test the cost calculator functionality"""
        
        calc = await create_cost_calculator("redis://localhost:6379")
        
        # Test cost calculation
        result = await calc.calculate_total_trading_cost(
            symbol="BTC",
            trade_size_usd=10000.0,
            order_type=OrderType.MARKET,
            exchange="binance",
            leverage=2.0
        )
        
        print(f"Cost Calculation Result:")
        print(f"  Total Cost: ${result.total_cost_usd:.2f} ({result.total_cost_bps:.1f} bps)")
        print(f"  Exchange Fees: ${result.exchange_fees_usd:.2f}")
        print(f"  Slippage Cost: ${result.slippage_cost_usd:.2f}")
        print(f"  Market Impact: ${result.market_impact_cost_usd:.2f}")
        print(f"  Confidence: {result.confidence:.3f}")
        print(f"  Suggestions: {len(result.optimization_suggestions)}")
        
        # Test optimization insights
        insights = await calc.get_cost_optimization_insights("BTC")
        print(f"\nOptimization Insights:")
        print(f"  Best Exchange: {insights['best_exchanges_for_costs'][0]['exchange']}")
        print(f"  Optimal Trade Range: ${insights['optimal_trade_sizes']['optimal_range_min']}-${insights['optimal_trade_sizes']['optimal_range_max']}")
    
    asyncio.run(test_cost_calculator())