#!/usr/bin/env python3
"""
Automated Portfolio Manager for Strategy Ensemble System
Implements Task 1.2.3 requirements:
- Redis integration for sub-second performance
- Automated weight allocation from MLflow
- Conflict resolution with cached signals
- Real-time data processing and execution
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from collections import defaultdict
import hashlib

from app.models.trade_state import TradeState
from app.models.market_data import MarketData
from app.services.execution.execution_service import ExecutionService
from app.strategies.base_strategy import BaseStrategy
from app.ml.models.weight_optimizer import WeightOptimizer

# MCP Services
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.mcp.mlflow_service import MLflowService
from app.services.mcp.wandb_service import WandBService

logger = logging.getLogger(__name__)

@dataclass
class AutomatedConfig:
    """Configuration for automated portfolio manager"""
    # Performance thresholds
    max_execution_time_ms: float = 1000  # Sub-second requirement
    cache_ttl_weights: int = 300  # 5 minutes
    cache_ttl_signals: int = 30   # 30 seconds
    cache_ttl_correlation: int = 1800  # 30 minutes
    
    # Trading parameters
    min_confidence_threshold: float = 0.6
    max_position_size: float = 0.1
    correlation_threshold: float = 0.8
    risk_limit: float = 0.8
    
    # MLflow parameters
    model_refresh_interval: int = 3600  # 1 hour
    weight_validation_threshold: float = 0.05  # 5% change threshold
    
    # Conflict resolution
    enable_parallel_execution: bool = True
    enable_conflict_resolution: bool = True
    enable_automated_failover: bool = True
    
    # Real-time features
    enable_real_time_updates: bool = True
    update_frequency_ms: int = 100  # 100ms refresh rate

@dataclass  
class ConflictResolution:
    """Conflict resolution metadata"""
    strategy_conflicts: List[str]
    resolution_method: str
    confidence_adjustment: float
    timestamp: datetime

@dataclass
class AutomatedMetrics:
    """Comprehensive automated metrics"""
    execution_time_ms: float
    cache_hit_rate: float
    ml_prediction_accuracy: float
    conflict_resolution_count: int
    automated_trades_count: int
    manual_override_count: int
    system_availability: float
    timestamp: datetime

class AutomatedPortfolioManager:
    """
    Automated Portfolio Manager with comprehensive MCP integration.
    
    Features:
    - Sub-second execution with Redis caching
    - Automated ML weight allocation via MLflow
    - Real-time conflict resolution
    - Comprehensive monitoring and alerting
    """
    
    def __init__(
        self,
        config: AutomatedConfig,
        strategies: List[BaseStrategy],
        weight_optimizer: WeightOptimizer,
        redis_service: RedisService,
        mlflow_service: MLflowService,
        supabase_service: Optional[SupabaseService] = None,
        wandb_service: Optional[WandBService] = None,
        execution_service: Optional[ExecutionService] = None
    ):
        self.config = config
        self.strategies = {s.__class__.__name__: s for s in strategies}
        self.weight_optimizer = weight_optimizer
        self.redis_service = redis_service
        self.mlflow_service = mlflow_service
        self.supabase_service = supabase_service
        self.wandb_service = wandb_service
        self.execution_service = execution_service
        
        # Cache keys
        self.WEIGHTS_KEY = "automated:weights"
        self.SIGNALS_KEY = "automated:signals"
        self.CONFLICTS_KEY = "automated:conflicts"
        self.METRICS_KEY = "automated:metrics"
        self.MODEL_VERSION_KEY = "automated:model_version"
        
        # State tracking
        self.last_weight_update = datetime.min
        self.current_model_version = None
        self.conflict_history = []
        self.performance_metrics = AutomatedMetrics(
            execution_time_ms=0,
            cache_hit_rate=0,
            ml_prediction_accuracy=0,
            conflict_resolution_count=0,
            automated_trades_count=0,
            manual_override_count=0,
            system_availability=1.0,
            timestamp=datetime.now()
        )
        
        logger.info(f"Initialized AutomatedPortfolioManager with {len(self.strategies)} strategies")
    
    async def execute_automated_strategy(
        self,
        market_data: MarketData
    ) -> Tuple[List[TradeState], AutomatedMetrics]:
        """
        Execute automated strategy with complete MCP integration.
        Target: Sub-second execution time.
        """
        start_time = datetime.now()
        
        try:
            # Step 1: Get automated weights with MLflow integration
            weights_start = datetime.now()
            strategy_weights = await self._get_automated_weights(market_data)
            weights_time = (datetime.now() - weights_start).total_seconds() * 1000
            
            # Step 2: Get strategy signals with parallel execution and caching
            signals_start = datetime.now()
            strategy_signals = await self._get_cached_strategy_signals(market_data)
            signals_time = (datetime.now() - signals_start).total_seconds() * 1000
            
            # Step 3: Resolve conflicts and aggregate signals
            aggregation_start = datetime.now()
            aggregated_signal, conflicts = await self._resolve_conflicts_and_aggregate(
                strategy_signals, strategy_weights, market_data
            )
            aggregation_time = (datetime.now() - aggregation_start).total_seconds() * 1000
            
            # Step 4: Execute trades with automated monitoring
            execution_start = datetime.now()
            executed_trades = await self._execute_automated_trades(
                aggregated_signal, market_data
            )
            execution_time = (datetime.now() - execution_start).total_seconds() * 1000
            
            # Calculate total execution time
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Update performance metrics
            await self._update_automated_metrics(
                total_time, weights_time, signals_time, aggregation_time,
                len(executed_trades), conflicts
            )
            
            # Log performance to W&B if enabled
            if self.wandb_service:
                await self._log_automated_performance(
                    market_data, strategy_weights, aggregated_signal, 
                    executed_trades, total_time
                )
            
            # Check if we met sub-second target
            if total_time > self.config.max_execution_time_ms:
                logger.warning(f"Execution time {total_time:.1f}ms exceeded target {self.config.max_execution_time_ms}ms")
            else:
                logger.info(f"Automated execution completed in {total_time:.1f}ms ✓")
            
            return executed_trades, self.performance_metrics
            
        except Exception as e:
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Automated execution failed: {e}")
            await self._handle_execution_failure(e, total_time)
            return [], self.performance_metrics
    
    async def _get_automated_weights(
        self,
        market_data: MarketData
    ) -> Dict[str, float]:
        """Get automated weights with MLflow integration and caching."""
        
        try:
            # Check if we need to refresh model
            await self._check_model_refresh()
            
            # Check Redis cache first
            cached_weights = await self.redis_service.get(self.WEIGHTS_KEY)
            if cached_weights:
                weights_data = json.loads(cached_weights)
                cache_time = datetime.fromisoformat(weights_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config.cache_ttl_weights):
                    logger.debug("Using cached weights")
                    return weights_data['weights']
            
            # Get fresh weights from MLflow production model
            production_model = await self.mlflow_service.load_production_model()
            if not production_model:
                logger.warning("No production model available, using fallback weights")
                return await self._get_fallback_weights()
            
            # Prepare features for ML model
            features = await self._prepare_ml_features(market_data)
            
            # Get predictions from production model
            weight_predictions = production_model.predict(features.reshape(1, -1))
            
            # Ensure weights are valid
            weights = self._normalize_weights(weight_predictions[0])
            
            # Validate weight changes
            if await self._should_validate_weights(weights):
                validated_weights = await self._validate_weight_changes(weights)
                if validated_weights:
                    weights = validated_weights
            
            # Create strategy weight mapping
            strategy_names = list(self.strategies.keys())
            weight_dict = {
                strategy_names[i]: float(weights[i]) 
                for i in range(min(len(strategy_names), len(weights)))
            }
            
            # Cache the weights
            cache_data = {
                'weights': weight_dict,
                'timestamp': datetime.now().isoformat(),
                'model_version': self.current_model_version,
                'validation_passed': True
            }
            
            await self.redis_service.setex(
                self.WEIGHTS_KEY,
                self.config.cache_ttl_weights,
                json.dumps(cache_data)
            )
            
            logger.info(f"Generated automated weights: {[f'{name}: {w:.3f}' for name, w in weight_dict.items()]}")
            return weight_dict
            
        except Exception as e:
            logger.error(f"Automated weight generation failed: {e}")
            return await self._get_fallback_weights()
    
    async def _get_cached_strategy_signals(
        self,
        market_data: MarketData
    ) -> Dict[str, Dict]:
        """Get strategy signals with parallel execution and intelligent caching."""
        
        market_hash = self._generate_market_hash(market_data)
        signals = {}
        cache_hits = 0
        
        # Check cache for all strategies
        cached_signals = {}
        missing_strategies = []
        
        for strategy_name in self.strategies.keys():
            cache_key = f"{self.SIGNALS_KEY}:{strategy_name}:{market_hash}"
            
            try:
                cached_signal = await self.redis_service.get(cache_key)
                if cached_signal:
                    signal_data = json.loads(cached_signal)
                    cache_time = datetime.fromisoformat(signal_data['timestamp'])
                    
                    if datetime.now() - cache_time < timedelta(seconds=self.config.cache_ttl_signals):
                        cached_signals[strategy_name] = signal_data['signal']
                        cache_hits += 1
                        continue
                        
            except Exception as e:
                logger.warning(f"Cache retrieval failed for {strategy_name}: {e}")
            
            missing_strategies.append(strategy_name)
        
        # Generate missing signals in parallel
        if missing_strategies:
            if self.config.enable_parallel_execution:
                tasks = [
                    self._generate_and_cache_signal(strategy_name, market_data, market_hash)
                    for strategy_name in missing_strategies
                ]
                
                fresh_signals = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, strategy_name in enumerate(missing_strategies):
                    if isinstance(fresh_signals[i], Exception):
                        logger.error(f"Signal generation failed for {strategy_name}: {fresh_signals[i]}")
                        signals[strategy_name] = self._default_signal()
                    else:
                        signals[strategy_name] = fresh_signals[i]
            else:
                # Sequential fallback
                for strategy_name in missing_strategies:
                    try:
                        signal = await self._generate_and_cache_signal(strategy_name, market_data, market_hash)
                        signals[strategy_name] = signal
                    except Exception as e:
                        logger.error(f"Signal generation failed for {strategy_name}: {e}")
                        signals[strategy_name] = self._default_signal()
        
        # Combine cached and fresh signals
        signals.update(cached_signals)
        
        # Update cache hit rate
        self.performance_metrics.cache_hit_rate = cache_hits / len(self.strategies) if self.strategies else 0
        
        return signals
    
    async def _resolve_conflicts_and_aggregate(
        self,
        strategy_signals: Dict[str, Dict],
        strategy_weights: Dict[str, float],
        market_data: MarketData
    ) -> Tuple[Dict, List[ConflictResolution]]:
        """Resolve conflicts and aggregate signals with advanced conflict resolution."""
        
        conflicts = []
        
        if not self.config.enable_conflict_resolution:
            # Simple aggregation without conflict resolution
            return await self._simple_aggregate(strategy_signals, strategy_weights), conflicts
        
        # Detect conflicts
        actions = [signal.get('action', 'HOLD') for signal in strategy_signals.values()]
        conflicting_actions = [action for action in set(actions) if actions.count(action) == 1 and action != 'HOLD']
        
        if len(set(actions)) > 2:  # More than HOLD and one other action
            # Resolve conflicts
            conflict = ConflictResolution(
                strategy_conflicts=list(strategy_signals.keys()),
                resolution_method="weighted_voting",
                confidence_adjustment=0.9,  # Reduce confidence due to conflict
                timestamp=datetime.now()
            )
            conflicts.append(conflict)
            self.conflict_history.append(conflict)
        
        # Perform weighted aggregation with conflict resolution
        weighted_actions = defaultdict(float)
        weighted_quantities = []
        weighted_prices = []
        contributing_strategies = []
        total_confidence = 0.0
        
        for strategy_name, signal in strategy_signals.items():
            weight = strategy_weights.get(strategy_name, 0.0)
            if weight <= 0:
                continue
                
            action = signal.get('action', 'HOLD')
            quantity = signal.get('quantity', 0)
            price = signal.get('price', 0)
            confidence = signal.get('confidence', 0)
            
            # Apply conflict resolution confidence adjustment
            if conflicts:
                confidence *= conflicts[0].confidence_adjustment
            
            # Weight the action votes
            weighted_actions[action] += weight * confidence
            
            # Accumulate weighted quantities and prices
            if action in ['BUY', 'SELL'] and quantity > 0:
                weighted_quantities.append(quantity * weight)
                weighted_prices.append(price * weight)
                contributing_strategies.append(strategy_name)
            
            total_confidence += confidence * weight
        
        # Determine final action
        final_action = max(weighted_actions.items(), key=lambda x: x[1])[0] if weighted_actions else 'HOLD'
        final_quantity = sum(weighted_quantities) if weighted_quantities else 0
        final_price = (sum(weighted_prices) / len(weighted_prices)) if weighted_prices else 0
        
        # Normalize confidence
        total_weight = sum(strategy_weights.values())
        final_confidence = total_confidence / total_weight if total_weight > 0 else 0
        
        aggregated_signal = {
            'action': final_action,
            'quantity': final_quantity,
            'price': final_price,
            'confidence': final_confidence,
            'contributing_strategies': contributing_strategies,
            'conflicts': len(conflicts),
            'timestamp': datetime.now().isoformat()
        }
        
        # Cache aggregated signal
        market_hash = self._generate_market_hash(market_data)
        cache_key = f"aggregated:{market_hash}"
        await self.redis_service.setex(
            cache_key,
            30,  # 30 seconds
            json.dumps(aggregated_signal, default=str)
        )
        
        return aggregated_signal, conflicts
    
    async def _execute_automated_trades(
        self,
        aggregated_signal: Dict,
        market_data: MarketData
    ) -> List[TradeState]:
        """Execute trades with automated monitoring and safety checks."""
        
        executed_trades = []
        
        try:
            # Safety checks
            if not self._should_execute_signal(aggregated_signal):
                logger.info(f"Signal failed safety checks: {aggregated_signal.get('action', 'UNKNOWN')}")
                return executed_trades
            
            # Position size validation
            validated_quantity = await self._validate_position_size(
                aggregated_signal['quantity'], market_data
            )
            
            if validated_quantity != aggregated_signal['quantity']:
                logger.info(f"Position size adjusted: {aggregated_signal['quantity']} -> {validated_quantity}")
                aggregated_signal['quantity'] = validated_quantity
            
            # Execute trade if execution service is available
            if self.execution_service and validated_quantity > 0:
                trade_result = await self.execution_service.execute_trade(
                    symbol=market_data.symbol,
                    action=aggregated_signal['action'],
                    quantity=validated_quantity,
                    price=aggregated_signal['price']
                )
                
                if trade_result:
                    executed_trades.append(trade_result)
                    self.performance_metrics.automated_trades_count += 1
                    
                    # Store in Supabase if available
                    if self.supabase_service:
                        await self._store_automated_trade(trade_result, aggregated_signal, market_data)
                    
                    logger.info(f"Executed automated trade: {trade_result.action} {trade_result.quantity} @ {trade_result.price}")
            
        except Exception as e:
            logger.error(f"Automated trade execution failed: {e}")
            await self._handle_trade_execution_failure(e, aggregated_signal, market_data)
        
        return executed_trades
    
    # Helper methods
    
    async def _check_model_refresh(self) -> None:
        """Check if we need to refresh the MLflow model."""
        try:
            # Check if enough time has passed since last update
            if (datetime.now() - self.last_weight_update).total_seconds() < self.config.model_refresh_interval:
                return
            
            # Get latest production model info
            model_info = await self.mlflow_service.get_model_info("production")
            if not model_info:
                logger.warning("No production model available")
                return
            
            latest_version = model_info['version']
            
            # Check if we have a newer version
            if self.current_model_version != latest_version:
                logger.info(f"Updating model from v{self.current_model_version} to v{latest_version}")
                self.current_model_version = latest_version
                self.last_weight_update = datetime.now()
                
                # Cache the model version
                await self.redis_service.setex(
                    self.MODEL_VERSION_KEY,
                    3600,  # 1 hour
                    latest_version
                )
                
        except Exception as e:
            logger.error(f"Model refresh check failed: {e}")
    
    async def _prepare_ml_features(self, market_data: MarketData) -> np.ndarray:
        """Prepare features for ML model prediction."""
        features = []
        
        # Market features
        features.extend([
            market_data.price,
            market_data.volume,
            getattr(market_data, 'volatility', 0.02),
            getattr(market_data, 'rsi', 50),
            getattr(market_data, 'macd', 0)
        ])
        
        # Portfolio performance features (if available)
        try:
            recent_performance = await self._get_recent_performance()
            features.extend([
                recent_performance.get('sharpe_ratio', 0),
                recent_performance.get('max_drawdown', 0),
                recent_performance.get('win_rate', 0.5)
            ])
        except:
            features.extend([0, 0, 0.5])
        
        return np.array(features, dtype=np.float32)
    
    def _normalize_weights(self, weights: np.ndarray) -> np.ndarray:
        """Normalize weights to ensure they sum to 1 and are non-negative."""
        weights = np.maximum(weights, 0)  # Ensure non-negative
        total = np.sum(weights)
        if total > 0:
            return weights / total
        else:
            # Equal weights fallback
            return np.ones(len(weights)) / len(weights)
    
    async def _get_fallback_weights(self) -> Dict[str, float]:
        """Get fallback equal weights when ML model fails."""
        equal_weight = 1.0 / len(self.strategies)
        return {name: equal_weight for name in self.strategies.keys()}
    
    def _generate_market_hash(self, market_data: MarketData) -> str:
        """Generate hash for market conditions."""
        market_string = f"{market_data.symbol}:{market_data.timestamp.minute}:{market_data.price:.2f}:{market_data.volume}"
        return hashlib.md5(market_string.encode()).hexdigest()[:12]
    
    async def _generate_and_cache_signal(
        self,
        strategy_name: str,
        market_data: MarketData,
        market_hash: str
    ) -> Dict:
        """Generate signal for strategy and cache it."""
        try:
            strategy = self.strategies[strategy_name]
            signal = await strategy.generate_signal(market_data)
            
            signal_dict = {
                'action': signal.action,
                'quantity': signal.quantity,
                'price': signal.price,
                'confidence': signal.confidence
            }
            
            # Cache the signal
            cache_key = f"{self.SIGNALS_KEY}:{strategy_name}:{market_hash}"
            cache_data = {
                'signal': signal_dict,
                'timestamp': datetime.now().isoformat(),
                'strategy': strategy_name
            }
            
            await self.redis_service.setex(
                cache_key,
                self.config.cache_ttl_signals,
                json.dumps(cache_data)
            )
            
            return signal_dict
            
        except Exception as e:
            logger.error(f"Signal generation failed for {strategy_name}: {e}")
            return self._default_signal()
    
    def _default_signal(self) -> Dict:
        """Default signal for fallback cases."""
        return {
            'action': 'HOLD',
            'quantity': 0,
            'price': 0,
            'confidence': 0
        }
    
    def _should_execute_signal(self, signal: Dict) -> bool:
        """Check if signal meets execution criteria."""
        return (
            signal.get('action') in ['BUY', 'SELL'] and
            signal.get('confidence', 0) >= self.config.min_confidence_threshold and
            signal.get('quantity', 0) > 0
        )
    
    async def _validate_position_size(self, quantity: float, market_data: MarketData) -> float:
        """Validate and adjust position size based on risk limits."""
        # Apply maximum position size limit
        max_quantity = market_data.volume * self.config.max_position_size
        return min(quantity, max_quantity)
    
    async def _update_automated_metrics(
        self,
        total_time: float,
        weights_time: float,
        signals_time: float,
        aggregation_time: float,
        trades_executed: int,
        conflicts: List[ConflictResolution]
    ) -> None:
        """Update automated performance metrics."""
        self.performance_metrics.execution_time_ms = total_time
        self.performance_metrics.conflict_resolution_count += len(conflicts)
        self.performance_metrics.timestamp = datetime.now()
        
        # Cache updated metrics
        await self.redis_service.setex(
            self.METRICS_KEY,
            60,  # 1 minute
            json.dumps(asdict(self.performance_metrics), default=str)
        )
    
    async def _simple_aggregate(
        self,
        strategy_signals: Dict[str, Dict],
        strategy_weights: Dict[str, float]
    ) -> Dict:
        """Simple signal aggregation without conflict resolution."""
        # Implementation similar to _resolve_conflicts_and_aggregate but simpler
        # This is a placeholder - would implement the basic aggregation logic
        return {
            'action': 'HOLD',
            'quantity': 0,
            'price': 0,
            'confidence': 0,
            'contributing_strategies': [],
            'conflicts': 0,
            'timestamp': datetime.now().isoformat()
        }
    
    async def _should_validate_weights(self, weights: np.ndarray) -> bool:
        """Check if weights need validation."""
        # Check for significant changes from previous weights
        return True  # For now, always validate
    
    async def _validate_weight_changes(self, weights: np.ndarray) -> Optional[np.ndarray]:
        """Validate weight changes and return adjusted weights if needed."""
        # Implement weight validation logic
        return weights  # For now, accept all weights
    
    async def _get_recent_performance(self) -> Dict:
        """Get recent portfolio performance metrics."""
        # Mock implementation
        return {
            'sharpe_ratio': 1.0,
            'max_drawdown': -0.05,
            'win_rate': 0.6
        }
    
    async def _store_automated_trade(
        self,
        trade: TradeState,
        signal: Dict,
        market_data: MarketData
    ) -> None:
        """Store automated trade execution details."""
        if not self.supabase_service:
            return
            
        try:
            trade_data = {
                'strategy_name': 'automated_ensemble',
                'symbol': market_data.symbol,
                'action': trade.action,
                'quantity': trade.quantity,
                'price': trade.price,
                'timestamp': datetime.now().isoformat(),
                'confidence': signal.get('confidence', 0),
                'weight': 1.0,
                'position_size': trade.quantity,
                'market_conditions': {
                    'price': market_data.price,
                    'volume': market_data.volume
                },
                'automation_metadata': {
                    'conflicts': signal.get('conflicts', 0),
                    'contributing_strategies': signal.get('contributing_strategies', []),
                    'model_version': self.current_model_version
                }
            }
            
            await self.supabase_service.store_trade_execution(trade_data)
            
        except Exception as e:
            logger.error(f"Failed to store automated trade: {e}")
    
    async def _log_automated_performance(
        self,
        market_data: MarketData,
        strategy_weights: Dict[str, float],
        aggregated_signal: Dict,
        executed_trades: List[TradeState],
        execution_time_ms: float
    ) -> None:
        """Log automated performance to W&B."""
        if not self.wandb_service:
            return
            
        try:
            # Create comprehensive metrics for W&B
            metrics = {
                'timestamp': datetime.now(),
                'execution_time_ms': execution_time_ms,
                'cache_hit_rate': self.performance_metrics.cache_hit_rate,
                'strategy_weights': strategy_weights,
                'signal_confidence': aggregated_signal.get('confidence', 0),
                'trades_executed': len(executed_trades),
                'conflicts_resolved': aggregated_signal.get('conflicts', 0),
                'model_version': self.current_model_version,
                'market_conditions': {
                    'symbol': market_data.symbol,
                    'price': market_data.price,
                    'volume': market_data.volume
                }
            }
            
            # Log to W&B
            await self.wandb_service.log_metrics(metrics)
            
        except Exception as e:
            logger.error(f"Failed to log automated performance: {e}")
    
    async def _handle_execution_failure(self, error: Exception, execution_time_ms: float) -> None:
        """Handle execution failures with automated recovery."""
        logger.error(f"Automated execution failed in {execution_time_ms:.1f}ms: {error}")
        
        # Update system availability
        self.performance_metrics.system_availability *= 0.99  # Slight decrease
        
        # Implement automated recovery if enabled
        if self.config.enable_automated_failover:
            logger.info("Attempting automated recovery...")
            # Could implement fallback to manual mode, alert notifications, etc.
    
    async def _handle_trade_execution_failure(
        self,
        error: Exception,
        signal: Dict,
        market_data: MarketData
    ) -> None:
        """Handle trade execution failures."""
        logger.error(f"Trade execution failed: {error}")
        self.performance_metrics.manual_override_count += 1
        
        # Store failure for analysis
        if self.supabase_service:
            try:
                failure_data = {
                    'error_type': type(error).__name__,
                    'error_message': str(error),
                    'signal': signal,
                    'market_data': {
                        'symbol': market_data.symbol,
                        'price': market_data.price,
                        'volume': market_data.volume
                    },
                    'timestamp': datetime.now().isoformat()
                }
                # Could store in a separate failures table
            except Exception as e:
                logger.error(f"Failed to store execution failure: {e}")

# Integration and testing utilities

async def create_automated_portfolio_manager(
    strategies: List[BaseStrategy],
    redis_url: str,
    mlflow_tracking_uri: str,
    weight_optimizer: WeightOptimizer,
    config: AutomatedConfig = None
) -> AutomatedPortfolioManager:
    """Factory function to create automated portfolio manager."""
    
    # Initialize services
    redis_service = RedisService(redis_url)
    mlflow_service = MLflowService(tracking_uri=mlflow_tracking_uri)
    
    # Use default config if none provided
    if not config:
        config = AutomatedConfig()
    
    return AutomatedPortfolioManager(
        config=config,
        strategies=strategies,
        weight_optimizer=weight_optimizer,
        redis_service=redis_service,
        mlflow_service=mlflow_service
    )

async def test_automated_portfolio_manager_real_time(
    portfolio_manager: AutomatedPortfolioManager,
    market_data_stream: List[MarketData],
    duration_seconds: int = 60
) -> Dict[str, Any]:
    """Test automated portfolio manager with real-time data."""
    
    start_time = datetime.now()
    results = {
        'total_executions': 0,
        'avg_execution_time_ms': 0,
        'sub_second_rate': 0,
        'cache_hit_rate': 0,
        'conflicts_resolved': 0,
        'trades_executed': 0,
        'errors': 0
    }
    
    execution_times = []
    
    logger.info(f"Starting {duration_seconds}s real-time test...")
    
    try:
        while (datetime.now() - start_time).total_seconds() < duration_seconds:
            # Use next market data from stream
            market_data = market_data_stream[results['total_executions'] % len(market_data_stream)]
            
            # Execute automated strategy
            exec_start = datetime.now()
            trades, metrics = await portfolio_manager.execute_automated_strategy(market_data)
            exec_time = (datetime.now() - exec_start).total_seconds() * 1000
            
            # Track results
            execution_times.append(exec_time)
            results['total_executions'] += 1
            results['trades_executed'] += len(trades)
            results['cache_hit_rate'] = metrics.cache_hit_rate
            results['conflicts_resolved'] = metrics.conflict_resolution_count
            
            # Wait for next cycle (simulate real-time frequency)
            await asyncio.sleep(0.1)  # 100ms intervals
        
        # Calculate final metrics
        if execution_times:
            results['avg_execution_time_ms'] = np.mean(execution_times)
            results['sub_second_rate'] = sum(1 for t in execution_times if t < 1000) / len(execution_times)
        
        logger.info(f"Real-time test completed: {results}")
        return results
        
    except Exception as e:
        logger.error(f"Real-time test failed: {e}")
        results['errors'] += 1
        return results