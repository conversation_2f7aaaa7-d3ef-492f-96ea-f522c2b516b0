# Ensemble Strategy Allocation

## Overview

This improvement will implement a weighted ensemble approach to strategy allocation instead of selecting a single strategy. Currently, the system selects the highest-scoring strategy above a threshold. By allocating capital across multiple strategies based on their scores, we can create a more robust trading system that benefits from strategy diversification and reduces the impact of any single strategy's failure.

## Technical Specification

### Architecture

The system will consist of the following components:

1. **Ensemble Manager**: Coordinates the allocation of capital across strategies
2. **Strategy Weighting Calculator**: Determines optimal weights for each strategy
3. **Position Size Allocator**: Calculates position sizes based on strategy weights
4. **Strategy Correlation Analyzer**: Analyzes correlations between strategies
5. **Performance Aggregator**: Aggregates performance metrics across strategies

### Technology Stack

- **Python Libraries**:
  - `pandas` and `numpy`: For data manipulation and calculations
  - `scipy`: For statistical functions and optimization
  - `cvxpy`: For portfolio optimization (optional)
  - `networkx`: For strategy correlation visualization (optional)

- **Database**:
  - Use existing PostgreSQL database for storing ensemble data
  - Add new tables for strategy weights and ensemble performance

### Implementation Details

1. **Ensemble Allocation Methods**:
   - **Score-Proportional Allocation**: Allocate capital proportionally to strategy scores
   - **Optimization-Based Allocation**: Use portfolio optimization techniques to maximize return/risk
   - **Hierarchical Allocation**: Allocate based on strategy hierarchies and correlations
   - **Adaptive Allocation**: Dynamically adjust weights based on recent performance

2. **Strategy Correlation Handling**:
   - Calculate correlation matrix between strategy returns
   - Reduce weights of highly correlated strategies
   - Increase weights of strategies with negative correlations

3. **Position Sizing Logic**:
   - Calculate position sizes based on strategy weights
   - Implement risk-based position sizing within each strategy
   - Ensure total risk remains within acceptable limits

4. **Execution Coordination**:
   - Coordinate order execution across multiple strategies
   - Resolve conflicts between strategy signals
   - Implement priority rules for conflicting signals

### Database Schema

```sql
CREATE TABLE ensemble_configurations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    parameters JSONB
);

CREATE TABLE strategy_weights (
    id SERIAL PRIMARY KEY,
    ensemble_id INTEGER REFERENCES ensemble_configurations(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    strategy_name VARCHAR(50) NOT NULL,
    weight FLOAT NOT NULL,
    allocation_basis VARCHAR(50) NOT NULL
);

CREATE TABLE strategy_correlations (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    strategy_a VARCHAR(50) NOT NULL,
    strategy_b VARCHAR(50) NOT NULL,
    correlation FLOAT,
    sample_size INTEGER
);

CREATE TABLE ensemble_performance (
    id SERIAL PRIMARY KEY,
    ensemble_id INTEGER REFERENCES ensemble_configurations(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    returns FLOAT,
    volatility FLOAT,
    sharpe_ratio FLOAT,
    max_drawdown FLOAT,
    win_rate FLOAT,
    strategy_contributions JSONB
);
```

### File Structure

```
app/
├── ensemble/
│   ├── __init__.py
│   ├── manager.py
│   ├── weighting.py
│   ├── allocation.py
│   ├── correlation.py
│   └── performance.py
├── strategies/
│   ├── strategy_selector.py (modified)
│   └── ...
└── ...
```

### Configuration

Add the following to `app/config/settings.py`:

```python
# Ensemble Strategy Allocation Settings
ensemble_enabled: bool = Field(True, env='ENSEMBLE_ENABLED')
ensemble_allocation_method: str = Field("score_proportional", env='ENSEMBLE_ALLOCATION_METHOD')
ensemble_min_strategy_score: float = Field(0.3, env='ENSEMBLE_MIN_STRATEGY_SCORE')
ensemble_max_strategies: int = Field(3, env='ENSEMBLE_MAX_STRATEGIES')
ensemble_correlation_threshold: float = Field(0.7, env='ENSEMBLE_CORRELATION_THRESHOLD')
ensemble_rebalance_interval: str = Field("1h", env='ENSEMBLE_REBALANCE_INTERVAL')
ensemble_min_weight: float = Field(0.1, env='ENSEMBLE_MIN_WEIGHT')
ensemble_max_weight: float = Field(0.7, env='ENSEMBLE_MAX_WEIGHT')
```

### Modified Strategy Selector

The `StrategySelector` class will be modified to implement ensemble allocation:

```python
async def select_strategies(self) -> Dict[str, float]:
    """Select strategies and their weights based on market conditions.
    
    Returns:
        Dict[str, float]: Dictionary mapping strategy names to their weights
    """
    # Get current market conditions
    market_conditions = await self.market_analyzer.analyze_market(self.symbol, self.timeframe)
    
    # Score each strategy
    strategy_scores = {}
    for strategy_name in self.available_strategies:
        score = self.strategy_scorer.score_strategy(strategy_name, market_conditions)
        if score >= settings.ensemble_min_strategy_score:
            strategy_scores[strategy_name] = score
    
    # Log strategy scores
    self.logger.info(f"Strategy scores: {', '.join([f'{k}={v:.2f}' for k, v in strategy_scores.items()])}")
    
    if not strategy_scores:
        self.logger.warning("No strategies meet the minimum score threshold")
        return {}
    
    # Calculate strategy weights based on the selected allocation method
    if settings.ensemble_allocation_method == "score_proportional":
        weights = self._calculate_score_proportional_weights(strategy_scores)
    elif settings.ensemble_allocation_method == "optimization":
        weights = await self._calculate_optimized_weights(strategy_scores)
    elif settings.ensemble_allocation_method == "hierarchical":
        weights = await self._calculate_hierarchical_weights(strategy_scores)
    elif settings.ensemble_allocation_method == "adaptive":
        weights = await self._calculate_adaptive_weights(strategy_scores)
    else:
        self.logger.warning(f"Unknown allocation method: {settings.ensemble_allocation_method}")
        weights = self._calculate_score_proportional_weights(strategy_scores)
    
    # Apply correlation adjustments if enabled
    if settings.ensemble_correlation_adjustment_enabled:
        weights = await self._adjust_weights_for_correlation(weights)
    
    # Limit to maximum number of strategies
    if len(weights) > settings.ensemble_max_strategies:
        # Keep only the top N strategies by weight
        weights = dict(sorted(weights.items(), key=lambda x: x[1], reverse=True)[:settings.ensemble_max_strategies])
        
        # Renormalize weights
        total_weight = sum(weights.values())
        weights = {k: v / total_weight for k, v in weights.items()}
    
    # Log selected strategies and weights
    self.logger.info(f"Selected strategies: {', '.join([f'{k}={v:.2f}' for k, v in weights.items()])}")
    
    return weights

def _calculate_score_proportional_weights(self, strategy_scores: Dict[str, float]) -> Dict[str, float]:
    """Calculate strategy weights proportionally to their scores.
    
    Args:
        strategy_scores: Dictionary mapping strategy names to their scores
        
    Returns:
        Dict[str, float]: Dictionary mapping strategy names to their weights
    """
    total_score = sum(strategy_scores.values())
    if total_score == 0:
        return {}
    
    # Calculate raw weights
    raw_weights = {name: score / total_score for name, score in strategy_scores.items()}
    
    # Apply min/max weight constraints
    weights = {}
    remaining_weight = 1.0
    remaining_strategies = list(raw_weights.keys())
    
    # First pass: apply minimum weights
    for name in list(remaining_strategies):
        if raw_weights[name] < settings.ensemble_min_weight:
            weights[name] = settings.ensemble_min_weight
            remaining_weight -= settings.ensemble_min_weight
            remaining_strategies.remove(name)
    
    # Second pass: apply maximum weights
    for name in list(remaining_strategies):
        if raw_weights[name] > settings.ensemble_max_weight:
            weights[name] = settings.ensemble_max_weight
            remaining_weight -= settings.ensemble_max_weight
            remaining_strategies.remove(name)
    
    # Third pass: distribute remaining weight proportionally
    if remaining_strategies and remaining_weight > 0:
        remaining_score = sum(strategy_scores[name] for name in remaining_strategies)
        for name in remaining_strategies:
            weights[name] = (strategy_scores[name] / remaining_score) * remaining_weight
    
    return weights
```

### API Endpoints

Add the following endpoints to the API:

```python
@router.get("/ensemble/weights", response_model=StrategyWeightsResponse)
async def get_strategy_weights():
    """Get the current strategy weights in the ensemble."""
    # Implementation

@router.get("/ensemble/performance", response_model=EnsemblePerformanceResponse)
async def get_ensemble_performance(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
):
    """Get the performance of the ensemble."""
    # Implementation

@router.get("/ensemble/correlations", response_model=StrategyCorrelationsResponse)
async def get_strategy_correlations():
    """Get the correlations between strategies."""
    # Implementation

@router.post("/ensemble/configure", response_model=EnsembleConfigurationResponse)
async def configure_ensemble(request: EnsembleConfigurationRequest):
    """Configure the ensemble allocation method and parameters."""
    # Implementation
```

## Potential Challenges and Mitigations

1. **Execution Complexity**:
   - **Challenge**: Managing multiple strategies simultaneously increases execution complexity
   - **Mitigation**: Implement a robust execution coordinator with clear priority rules

2. **Position Sizing**:
   - **Challenge**: Determining appropriate position sizes across multiple strategies
   - **Mitigation**: Implement risk-based position sizing that accounts for total portfolio risk

3. **Strategy Conflicts**:
   - **Challenge**: Different strategies may generate conflicting signals
   - **Mitigation**: Implement conflict resolution rules and signal aggregation methods

4. **Performance Attribution**:
   - **Challenge**: Determining which strategies contribute to performance
   - **Mitigation**: Implement detailed performance tracking and attribution analysis

5. **Rebalancing Overhead**:
   - **Challenge**: Frequent rebalancing can lead to excessive trading and costs
   - **Mitigation**: Implement threshold-based rebalancing and optimize rebalancing frequency

## Testing Strategy

1. **Unit Tests**:
   - Test weight calculation algorithms
   - Verify correlation analysis
   - Test position sizing logic

2. **Integration Tests**:
   - Test integration with strategy execution
   - Verify database operations for ensemble data

3. **Backtesting**:
   - Compare ensemble performance vs. single-strategy approach
   - Test different allocation methods
   - Analyze impact of correlation adjustments

4. **Simulation Testing**:
   - Simulate different market scenarios
   - Test system behavior during strategy conflicts
   - Verify rebalancing logic

## Deployment Strategy

1. **Phase 1: Development and Testing**
   - Implement ensemble allocation methods
   - Test on historical data
   - Compare with single-strategy approach

2. **Phase 2: Shadow Mode**
   - Run ensemble allocation alongside current system
   - Log recommendations without affecting trading
   - Analyze performance differences

3. **Phase 3: Partial Integration**
   - Allocate a small portion of capital using ensemble approach
   - Gradually increase allocation based on performance
   - Monitor and refine

4. **Phase 4: Full Deployment**
   - Fully switch to ensemble allocation
   - Implement continuous monitoring and optimization
   - Periodically review and update allocation methods

## Task Checklist

- [ ] **Setup and Infrastructure**
  - [ ] Create ensemble module structure
  - [ ] Set up database tables for ensemble data
  - [ ] Configure environment variables and settings

- [ ] **Ensemble Manager Implementation**
  - [ ] Implement score-proportional allocation
  - [ ] Implement optimization-based allocation
  - [ ] Implement hierarchical allocation
  - [ ] Implement adaptive allocation

- [ ] **Strategy Correlation Analysis**
  - [ ] Implement correlation calculation
  - [ ] Create correlation visualization
  - [ ] Develop correlation-based weight adjustment
  - [ ] Implement correlation monitoring

- [ ] **Position Sizing and Execution**
  - [ ] Implement position size calculation
  - [ ] Create execution coordination logic
  - [ ] Develop conflict resolution rules
  - [ ] Implement rebalancing logic

- [ ] **Performance Tracking**
  - [ ] Implement ensemble performance metrics
  - [ ] Create strategy contribution analysis
  - [ ] Develop performance attribution reports
  - [ ] Implement performance comparison tools

- [ ] **API and Dashboard Integration**
  - [ ] Create API endpoints for ensemble data
  - [ ] Develop dashboard visualizations for ensemble
  - [ ] Implement ensemble configuration interface
  - [ ] Create performance comparison charts

- [ ] **Testing and Validation**
  - [ ] Develop unit tests for ensemble components
  - [ ] Create integration tests for ensemble allocation
  - [ ] Implement backtesting with ensemble approach
  - [ ] Conduct simulation testing for different scenarios

- [ ] **Deployment and Monitoring**
  - [ ] Set up monitoring for ensemble performance
  - [ ] Create alerting for allocation issues
  - [ ] Implement gradual rollout strategy
  - [ ] Develop ensemble optimization tools

- [ ] **Documentation and Knowledge Transfer**
  - [ ] Create technical documentation for ensemble allocation
  - [ ] Develop user guide for ensemble configuration
  - [ ] Document allocation methods and their trade-offs
  - [ ] Create training materials for ensemble approach

## Performance Metrics

The success of this improvement will be measured by:

1. **Risk-Adjusted Returns**: Improvement in Sharpe and Sortino ratios compared to single-strategy approach
2. **Maximum Drawdown**: Reduction in maximum drawdown
3. **Win Rate**: Increase in percentage of profitable trades
4. **Consistency**: More consistent returns across different market conditions
5. **Diversification Benefit**: Reduced correlation between strategy returns
6. **Recovery Time**: Reduction in time to recover from drawdowns
