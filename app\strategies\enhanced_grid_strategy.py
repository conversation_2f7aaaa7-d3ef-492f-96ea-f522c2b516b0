#!/usr/bin/env python3
"""
Enhanced Grid Strategy for Ensemble Execution
Optimized for concurrent Redis-cached execution with real-time position tracking.
"""

import asyncio
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import logging

from app.models.market_data import MarketData
from app.strategies.enhanced_base_strategy import EnhancedBaseStrategy
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

class EnhancedGridStrategy(EnhancedBaseStrategy):
    """
    Enhanced Grid Strategy optimized for ensemble execution.
    
    Features:
    - Redis-cached grid level calculations
    - Dynamic grid adjustment based on volatility
    - Concurrent execution optimization
    - Real-time position tracking
    """
    
    def __init__(
        self,
        symbol: str,
        timeframe: str,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        # Grid-specific default config
        grid_config = {
            "grid_levels": 5,                    # Number of grid levels above/below current price
            "grid_spacing_pct": 0.005,          # 0.5% spacing between grid levels
            "position_size_per_level": 0.02,    # 2% of portfolio per grid level
            "dynamic_spacing": True,             # Adjust spacing based on volatility
            "min_grid_spacing": 0.002,          # Minimum 0.2% spacing
            "max_grid_spacing": 0.02,           # Maximum 2% spacing
            "volatility_lookback": 24,          # Hours for volatility calculation
            "rebalance_threshold": 0.1,         # 10% price move triggers rebalance
            "max_open_positions": 3,            # Maximum concurrent grid positions
            "grid_efficiency_threshold": 0.7,   # Minimum efficiency for grid activation
            **self._enhanced_config()
        }
        
        if config:
            grid_config.update(config)
        
        super().__init__(symbol, timeframe, redis_service, supabase_service, grid_config)
        
        # Grid-specific cache keys
        self.GRID_LEVELS_KEY = f"grid:{self.strategy_id}:levels"
        self.VOLATILITY_KEY = f"grid:{self.strategy_id}:volatility"
        self.GRID_POSITIONS_KEY = f"grid:{self.strategy_id}:positions"
        self.GRID_EFFICIENCY_KEY = f"grid:{self.strategy_id}:efficiency"
        
        # Grid state
        self.current_grid_levels = []
        self.active_positions = []
        self.last_rebalance_price = 0.0
        
        logger.info(f"Enhanced Grid Strategy initialized: {self.strategy_id}")
    
    def _enhanced_config(self) -> Dict[str, Any]:
        """Enhanced configuration for grid strategy"""
        return {
            "cache_ttl_grid_levels": 300,      # 5 minutes
            "cache_ttl_volatility": 900,       # 15 minutes
            "cache_ttl_positions": 60,         # 1 minute
            "enable_dynamic_rebalancing": True,
            "enable_volatility_adjustment": True,
            "enable_efficiency_tracking": True,
            "min_confidence_for_execution": 0.6,
            "volatility_multiplier": 2.0
        }
    
    async def _perform_market_analysis(self, market_data: MarketData) -> Dict[str, Any]:
        """
        Perform grid-specific market analysis with caching.
        Analyzes market conditions for grid trading suitability.
        """
        try:
            current_price = market_data.price
            current_volume = market_data.volume
            
            # Get cached volatility
            volatility = await self._get_cached_volatility(market_data)
            
            # Calculate grid efficiency
            efficiency = await self._calculate_grid_efficiency(market_data, volatility)
            
            # Determine market conditions for grid trading
            trend_strength = abs(volatility - 0.02) / 0.02  # Normalized to expected volatility
            is_ranging = trend_strength < 0.5  # Low trend strength indicates ranging market
            
            # Calculate grid spacing based on volatility
            if self.config["dynamic_spacing"]:
                grid_spacing = self._calculate_dynamic_spacing(volatility)
            else:
                grid_spacing = self.config["grid_spacing_pct"]
            
            # Determine grid activation confidence
            confidence = 0.5  # Base confidence
            
            # Increase confidence for ranging markets
            if is_ranging:
                confidence += 0.3
            
            # Increase confidence for good efficiency
            if efficiency > self.config["grid_efficiency_threshold"]:
                confidence += 0.2
            
            # Decrease confidence for high volatility
            if volatility > 0.05:  # 5% volatility
                confidence -= 0.2
            
            confidence = max(0.0, min(1.0, confidence))  # Clamp to [0, 1]
            
            analysis = {
                'volatility': volatility,
                'grid_spacing': grid_spacing,
                'trend_strength': trend_strength,
                'is_ranging_market': is_ranging,
                'grid_efficiency': efficiency,
                'current_price': current_price,
                'volume_ratio': current_volume / 1000000,  # Normalized volume
                'confidence': confidence,
                'grid_levels_count': self.config["grid_levels"],
                'position_size_per_level': self.config["position_size_per_level"],
                'max_positions': self.config["max_open_positions"],
                'rebalance_needed': await self._check_rebalance_needed(current_price),
                'timestamp': datetime.now().isoformat()
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Grid market analysis failed: {e}")
            return self._default_market_analysis()
    
    async def _generate_raw_signal(
        self,
        market_data: MarketData,
        market_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate grid trading signal based on analysis.
        Optimized for concurrent execution.
        """
        try:
            current_price = market_data.price
            confidence = market_analysis.get('confidence', 0)
            is_ranging = market_analysis.get('is_ranging_market', False)
            grid_efficiency = market_analysis.get('grid_efficiency', 0)
            rebalance_needed = market_analysis.get('rebalance_needed', False)
            
            # Don't trade if confidence is too low
            if confidence < self.config["min_confidence_for_execution"]:
                return {
                    'action': 'HOLD',
                    'quantity': 0,
                    'price': current_price,
                    'confidence': confidence,
                    'reason': 'low_confidence'
                }
            
            # Don't trade if efficiency is too low
            if grid_efficiency < self.config["grid_efficiency_threshold"]:
                return {
                    'action': 'HOLD',
                    'quantity': 0,
                    'price': current_price,
                    'confidence': confidence,
                    'reason': 'low_efficiency'
                }
            
            # Get current grid levels
            grid_levels = await self._get_cached_grid_levels(market_data, market_analysis)
            
            # Determine action based on current price relative to grid
            action, target_price, quantity = await self._determine_grid_action(
                current_price, grid_levels, market_analysis
            )
            
            # Adjust confidence based on grid position
            if action != 'HOLD':
                # Higher confidence if we're at good grid levels
                grid_position_factor = self._calculate_grid_position_factor(current_price, grid_levels)
                confidence *= grid_position_factor
            
            signal = {
                'action': action,
                'quantity': quantity,
                'price': target_price,
                'confidence': confidence,
                'grid_levels': len(grid_levels),
                'grid_efficiency': grid_efficiency,
                'ranging_market': is_ranging,
                'rebalance_needed': rebalance_needed
            }
            
            logger.debug(f"Grid signal: {action} {quantity:.4f} @ {target_price:.2f} (conf={confidence:.3f})")
            return signal
            
        except Exception as e:
            logger.error(f"Grid signal generation failed: {e}")
            return {
                'action': 'HOLD',
                'quantity': 0,
                'price': market_data.price,
                'confidence': 0,
                'reason': 'error'
            }
    
    async def _get_cached_volatility(self, market_data: MarketData) -> float:
        """Get cached volatility calculation"""
        try:
            cached_volatility = await self.redis_service.get(self.VOLATILITY_KEY)
            
            if cached_volatility:
                vol_data = json.loads(cached_volatility)
                cache_time = datetime.fromisoformat(vol_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_volatility"]):
                    return vol_data['volatility']
            
            # Calculate fresh volatility
            volatility = await self._calculate_volatility(market_data)
            
            # Cache volatility
            vol_data = {
                'volatility': volatility,
                'timestamp': datetime.now().isoformat(),
                'lookback_hours': self.config["volatility_lookback"]
            }
            
            await self.redis_service.setex(
                self.VOLATILITY_KEY,
                self.config["cache_ttl_volatility"],
                json.dumps(vol_data)
            )
            
            return volatility
            
        except Exception as e:
            logger.warning(f"Volatility calculation failed: {e}")
            return 0.02  # Default 2% volatility
    
    async def _calculate_volatility(self, market_data: MarketData) -> float:
        """Calculate volatility based on recent price movements"""
        try:
            # In a real implementation, this would fetch historical price data
            # For now, use a simplified calculation based on current market data
            
            # Simulate historical volatility calculation
            # In production, you'd fetch actual OHLCV data from the exchange
            base_volatility = 0.02  # 2% base volatility
            
            # Adjust based on volume (higher volume = potentially higher volatility)
            volume_factor = min(market_data.volume / 1000000, 3.0)  # Cap at 3x
            adjusted_volatility = base_volatility * (0.5 + 0.5 * volume_factor)
            
            return min(adjusted_volatility, 0.1)  # Cap at 10% volatility
            
        except Exception as e:
            logger.error(f"Volatility calculation error: {e}")
            return 0.02
    
    async def _calculate_grid_efficiency(self, market_data: MarketData, volatility: float) -> float:
        """Calculate grid trading efficiency for current market conditions"""
        try:
            # Check cached efficiency
            cached_efficiency = await self.redis_service.get(self.GRID_EFFICIENCY_KEY)
            
            if cached_efficiency:
                eff_data = json.loads(cached_efficiency)
                cache_time = datetime.fromisoformat(eff_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(minutes=5):
                    return eff_data['efficiency']
            
            # Calculate efficiency based on multiple factors
            efficiency = 0.5  # Base efficiency
            
            # Volatility factor (moderate volatility is good for grids)
            optimal_volatility = 0.015  # 1.5%
            vol_diff = abs(volatility - optimal_volatility)
            vol_factor = max(0, 1 - (vol_diff / optimal_volatility))
            efficiency += 0.3 * vol_factor
            
            # Volume factor (consistent volume is good)
            volume_consistency = min(market_data.volume / 500000, 1.0)  # Normalize volume
            efficiency += 0.2 * volume_consistency
            
            efficiency = max(0.0, min(1.0, efficiency))
            
            # Cache efficiency
            eff_data = {
                'efficiency': efficiency,
                'timestamp': datetime.now().isoformat(),
                'volatility': volatility,
                'volume': market_data.volume
            }
            
            await self.redis_service.setex(
                self.GRID_EFFICIENCY_KEY,
                300,  # 5 minutes
                json.dumps(eff_data)
            )
            
            return efficiency
            
        except Exception as e:
            logger.error(f"Grid efficiency calculation failed: {e}")
            return 0.5
    
    def _calculate_dynamic_spacing(self, volatility: float) -> float:
        """Calculate dynamic grid spacing based on volatility"""
        base_spacing = self.config["grid_spacing_pct"]
        multiplier = self.config["volatility_multiplier"]
        
        # Adjust spacing based on volatility
        dynamic_spacing = base_spacing * (1 + volatility * multiplier)
        
        # Apply limits
        min_spacing = self.config["min_grid_spacing"]
        max_spacing = self.config["max_grid_spacing"]
        
        return max(min_spacing, min(max_spacing, dynamic_spacing))
    
    async def _get_cached_grid_levels(
        self,
        market_data: MarketData,
        market_analysis: Dict[str, Any]
    ) -> List[float]:
        """Get cached grid levels or calculate fresh ones"""
        try:
            cached_levels = await self.redis_service.get(self.GRID_LEVELS_KEY)
            
            if cached_levels:
                levels_data = json.loads(cached_levels)
                cache_time = datetime.fromisoformat(levels_data['timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_grid_levels"]):
                    return levels_data['levels']
            
            # Calculate fresh grid levels
            current_price = market_data.price
            grid_spacing = market_analysis.get('grid_spacing', self.config["grid_spacing_pct"])
            num_levels = self.config["grid_levels"]
            
            levels = []
            
            # Create buy levels (below current price)
            for i in range(1, num_levels + 1):
                buy_level = current_price * (1 - grid_spacing * i)
                levels.append(buy_level)
            
            # Create sell levels (above current price)
            for i in range(1, num_levels + 1):
                sell_level = current_price * (1 + grid_spacing * i)
                levels.append(sell_level)
            
            # Sort levels
            levels.sort()
            
            # Cache grid levels
            levels_data = {
                'levels': levels,
                'timestamp': datetime.now().isoformat(),
                'base_price': current_price,
                'spacing': grid_spacing,
                'num_levels': num_levels
            }
            
            await self.redis_service.setex(
                self.GRID_LEVELS_KEY,
                self.config["cache_ttl_grid_levels"],
                json.dumps(levels_data)
            )
            
            self.current_grid_levels = levels
            return levels
            
        except Exception as e:
            logger.error(f"Grid levels calculation failed: {e}")
            return []
    
    async def _determine_grid_action(
        self,
        current_price: float,
        grid_levels: List[float],
        market_analysis: Dict[str, Any]
    ) -> Tuple[str, float, float]:
        """Determine the best grid action based on current price and levels"""
        try:
            if not grid_levels:
                return 'HOLD', current_price, 0
            
            position_size = market_analysis.get('position_size_per_level', self.config["position_size_per_level"])
            
            # Find closest grid levels
            buy_levels = [level for level in grid_levels if level < current_price]
            sell_levels = [level for level in grid_levels if level > current_price]
            
            if not buy_levels or not sell_levels:
                return 'HOLD', current_price, 0
            
            closest_buy_level = max(buy_levels)  # Highest buy level below current price
            closest_sell_level = min(sell_levels)  # Lowest sell level above current price
            
            # Calculate distances
            buy_distance = (current_price - closest_buy_level) / current_price
            sell_distance = (closest_sell_level - current_price) / current_price
            
            # Decision logic
            min_distance_threshold = self.config["grid_spacing_pct"] * 0.8  # 80% of grid spacing
            
            if buy_distance < min_distance_threshold:
                # Close to buy level, consider buying
                return 'BUY', closest_buy_level, position_size
            elif sell_distance < min_distance_threshold:
                # Close to sell level, consider selling
                return 'SELL', closest_sell_level, position_size
            else:
                # Between levels, hold
                return 'HOLD', current_price, 0
            
        except Exception as e:
            logger.error(f"Grid action determination failed: {e}")
            return 'HOLD', current_price, 0
    
    def _calculate_grid_position_factor(self, current_price: float, grid_levels: List[float]) -> float:
        """Calculate confidence factor based on grid position"""
        if not grid_levels:
            return 0.5
        
        # Find position within grid
        below_levels = [level for level in grid_levels if level < current_price]
        above_levels = [level for level in grid_levels if level > current_price]
        
        if not below_levels or not above_levels:
            return 0.3  # Low confidence at grid edges
        
        # Calculate position as percentage between closest levels
        closest_below = max(below_levels)
        closest_above = min(above_levels)
        
        # Position factor is higher when closer to grid levels
        range_size = closest_above - closest_below
        distance_from_below = current_price - closest_below
        position_ratio = distance_from_below / range_size
        
        # Higher confidence closer to 0.2 or 0.8 (near grid levels)
        # Lower confidence near 0.5 (middle of range)
        if position_ratio < 0.3 or position_ratio > 0.7:
            return 1.0  # High confidence near grid levels
        else:
            return 0.7  # Moderate confidence in middle
    
    async def _check_rebalance_needed(self, current_price: float) -> bool:
        """Check if grid rebalancing is needed"""
        if self.last_rebalance_price == 0:
            self.last_rebalance_price = current_price
            return False
        
        price_change = abs(current_price - self.last_rebalance_price) / self.last_rebalance_price
        
        if price_change > self.config["rebalance_threshold"]:
            self.last_rebalance_price = current_price
            return True
        
        return False

# Example usage and testing
if __name__ == "__main__":
    # This would be used for testing the enhanced grid strategy
    pass