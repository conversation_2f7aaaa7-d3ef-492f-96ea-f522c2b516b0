user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Upstream configurations
    upstream app {
        least_conn;
        server app:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # High-performance upstream for position optimization
    upstream position_optimizer {
        least_conn;
        server position-optimizer:8000 max_fails=2 fail_timeout=10s;
        keepalive 64;
    }

    # Rate limiting zones - Enhanced for Dynamic Position Optimization
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=position:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=volatility:10m rate=200r/s;
    limit_req_zone $binary_remote_addr zone=correlation:10m rate=50r/s;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    server {
        listen 80;
        server_name localhost;

        # Redirect HTTP to HTTPS in production
        # return 301 https://$server_name$request_uri;

        # For development/testing, serve directly
        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeout settings
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # High-performance position optimization routes
        location /api/v1/position/ {
            limit_req zone=position burst=200 nodelay;
            
            proxy_pass http://position_optimizer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Optimized timeouts for sub-second responses
            proxy_connect_timeout 1s;
            proxy_send_timeout 2s;
            proxy_read_timeout 5s;
            
            # Minimal buffering for real-time responses
            proxy_buffering off;
            proxy_cache off;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        # Volatility calculation endpoints
        location /api/v1/volatility/ {
            limit_req zone=volatility burst=400 nodelay;
            
            proxy_pass http://position_optimizer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Ultra-fast timeouts for volatility calculations
            proxy_connect_timeout 500ms;
            proxy_send_timeout 1s;
            proxy_read_timeout 2s;
            
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        # Correlation matrix endpoints
        location /api/v1/correlation/ {
            limit_req zone=correlation burst=100 nodelay;
            
            proxy_pass http://position_optimizer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 1s;
            proxy_send_timeout 2s;
            proxy_read_timeout 5s;
            
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        # Standard API endpoints with rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        # Authentication endpoints with stricter rate limiting
        location /auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket support for real-time updates
        location /ws/ {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://app;
            proxy_connect_timeout 1s;
            proxy_send_timeout 1s;
            proxy_read_timeout 3s;
            access_log off;
        }

        # Position optimizer health check
        location /health/position-optimizer {
            proxy_pass http://position_optimizer/health;
            proxy_connect_timeout 1s;
            proxy_send_timeout 1s;
            proxy_read_timeout 2s;
            access_log off;
        }

        # Performance monitoring endpoints
        location /metrics {
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://app;
            access_log off;
        }

        location /metrics/position-optimizer {
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://position_optimizer/metrics;
            access_log off;
        }

        # Static files (if serving directly)
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Favicon
        location = /favicon.ico {
            alias /app/static/favicon.ico;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Security.txt
        location = /.well-known/security.txt {
            return 200 "Contact: <EMAIL>\nExpires: 2025-12-31T23:59:59.000Z\n";
            add_header Content-Type text/plain;
        }
    }

    # HTTPS configuration (for production)
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/ssl/certs/cert.pem;
    #     ssl_certificate_key /etc/ssl/private/key.pem;
    #     
    #     # SSL configuration
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=63072000" always;
    #     
    #     # Same location blocks as HTTP version
    # }
}