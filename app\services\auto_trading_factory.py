"""
Auto Trading Controller Factory - Service factory for initializing the complete auto trading system.
This factory handles the complex initialization of all services and components required for the
end-to-end ensemble trading pipeline as specified in the PRD.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from app.services.auto_trading_controller import <PERSON>Trading<PERSON>ontroller
from app.strategies.ensemble_portfolio_manager import EnsemblePortfolioManager, create_ensemble_portfolio_manager
from app.services.execution.execution_service import ExecutionService
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.mcp.wandb_service import WandBService
from app.services.mcp.cross_exchange_validator import CrossExchangeValidator
from app.monitoring.risk_monitor import RiskMonitor
from app.monitoring.telegram_performance_monitor import TelegramPerformanceMonitor

# Strategy imports
from app.strategies.enhanced_grid_strategy import EnhancedGridStrategy
from app.strategies.enhanced_technical_analysis_strategy import EnhancedTechnicalAnalysisStrategy
from app.strategies.trend_following_strategy import TrendFollowingStrategy
from app.ml.models.weight_optimizer import WeightOptimizer

from app.config.settings import Settings

logger = logging.getLogger(__name__)

@dataclass
class AutoTradingConfig:
    """Configuration for Auto Trading Controller initialization"""
    # Environment
    environment: str = "development"  # development, staging, production
    enable_real_trading: bool = False  # Safety default
    enable_paper_trading: bool = True
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379"
    redis_db: int = 0
    
    # Supabase Configuration
    supabase_url: Optional[str] = None
    supabase_key: Optional[str] = None
    
    # W&B Configuration
    wandb_api_key: Optional[str] = None
    wandb_project: str = "crypto-auto-trading"
    
    # Telegram Configuration
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None
    
    # Trading Configuration
    default_symbols: List[str] = None
    base_currency: str = "USDT"
    
    # Strategy Configuration
    enable_grid_strategy: bool = True
    enable_ta_strategy: bool = True
    enable_trend_strategy: bool = True
    
    # ML Configuration
    weight_optimizer_model_path: Optional[str] = None
    enable_ml_optimization: bool = True
    
    # Risk Management
    global_position_limit: float = 0.1
    global_drawdown_limit: float = 0.15
    
    def __post_init__(self):
        if self.default_symbols is None:
            self.default_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]

class AutoTradingFactory:
    """
    Factory class for creating and initializing the complete Auto Trading Controller system.
    
    This factory manages the complex initialization process of all components required
    for the end-to-end ensemble trading pipeline:
    
    1. Market Data Services (CoinCap, Binance, cross-validation)
    2. Strategy Ensemble (Grid, TA, Trend strategies)
    3. ML Weight Optimization (W&B, model serving)
    4. Portfolio Management (Redis caching, signal aggregation)
    5. Risk Management (position sizing, monitoring)
    6. Execution Services (Binance API, order management)
    7. Analytics & Monitoring (Supabase, Telegram alerts)
    """
    
    def __init__(self, config: Optional[AutoTradingConfig] = None):
        """
        Initialize the factory with configuration.
        
        Args:
            config: Auto trading configuration
        """
        self.config = config or AutoTradingConfig()
        self.settings = Settings()
        
        # Service instances
        self._redis_service: Optional[RedisService] = None
        self._supabase_service: Optional[SupabaseService] = None
        self._wandb_service: Optional[WandBService] = None
        self._execution_service: Optional[ExecutionService] = None
        self._ensemble_manager: Optional[EnsemblePortfolioManager] = None
        self._risk_monitor: Optional[RiskMonitor] = None
        self._telegram_monitor: Optional[TelegramPerformanceMonitor] = None
        
        logger.info(f"Auto Trading Factory initialized for {self.config.environment} environment")
    
    async def create_auto_trading_controller(self) -> AutoTradingController:
        """
        Create and initialize the complete Auto Trading Controller with all dependencies.
        
        Returns:
            Fully configured AutoTradingController instance
            
        Raises:
            RuntimeError: If initialization fails
        """
        try:
            logger.info("Initializing Auto Trading Controller system...")
            
            # Step 1: Initialize core services
            await self._initialize_core_services()
            
            # Step 2: Initialize strategies and ensemble manager
            await self._initialize_strategy_ensemble()
            
            # Step 3: Initialize execution services
            await self._initialize_execution_services()
            
            # Step 4: Initialize monitoring and risk management
            await self._initialize_monitoring_services()
            
            # Step 5: Create Auto Trading Controller
            controller = AutoTradingController(
                ensemble_manager=self._ensemble_manager,
                execution_service=self._execution_service,
                redis_service=self._redis_service,
                supabase_service=self._supabase_service,
                wandb_service=self._wandb_service,
                risk_monitor=self._risk_monitor,
                telegram_monitor=self._telegram_monitor,
                config=self._get_controller_config()
            )
            
            # Step 6: Perform system health checks
            await self._perform_system_health_checks(controller)
            
            logger.info("Auto Trading Controller system initialized successfully")
            return controller
            
        except Exception as e:
            logger.error(f"Failed to initialize Auto Trading Controller: {e}")
            raise RuntimeError(f"Auto Trading Controller initialization failed: {str(e)}")
    
    async def _initialize_core_services(self):
        """Initialize core infrastructure services."""
        logger.info("Initializing core services...")
        
        # Initialize Redis service
        try:
            self._redis_service = RedisService(
                redis_url=self.config.redis_url,
                db=self.config.redis_db
            )
            await self._redis_service.ping()  # Test connection
            logger.info("Redis service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Redis service: {e}")
            raise
        
        # Initialize Supabase service
        if self.config.supabase_url and self.config.supabase_key:
            try:
                self._supabase_service = SupabaseService(
                    url=self.config.supabase_url,
                    key=self.config.supabase_key
                )
                # Test connection
                await self._supabase_service.test_connection()
                logger.info("Supabase service initialized successfully")
            except Exception as e:
                logger.warning(f"Supabase service initialization failed: {e}")
                self._supabase_service = None
        
        # Initialize W&B service
        if self.config.wandb_api_key:
            try:
                self._wandb_service = WandBService(
                    project_name=self.config.wandb_project,
                    api_key=self.config.wandb_api_key
                )
                logger.info("W&B service initialized successfully")
            except Exception as e:
                logger.warning(f"W&B service initialization failed: {e}")
                self._wandb_service = None
    
    async def _initialize_strategy_ensemble(self):
        """Initialize trading strategies and ensemble manager."""
        logger.info("Initializing strategy ensemble...")
        
        try:
            # Create strategy instances
            strategies = []
            
            if self.config.enable_grid_strategy:
                grid_strategy = EnhancedGridStrategy(
                    symbol=self.config.default_symbols[0],  # Primary symbol
                    grid_levels=10,
                    grid_spacing=0.005,  # 0.5% spacing
                    position_size=0.1
                )
                strategies.append(grid_strategy)
                logger.info("Grid strategy initialized")
            
            if self.config.enable_ta_strategy:
                ta_strategy = EnhancedTechnicalAnalysisStrategy(
                    lookback_period=20,
                    rsi_period=14,
                    macd_fast=12,
                    macd_slow=26,
                    macd_signal=9
                )
                strategies.append(ta_strategy)
                logger.info("Technical Analysis strategy initialized")
            
            if self.config.enable_trend_strategy:
                trend_strategy = TrendFollowingStrategy(
                    fast_period=10,
                    slow_period=30,
                    atr_period=14,
                    risk_factor=2.0
                )
                strategies.append(trend_strategy)
                logger.info("Trend Following strategy initialized")
            
            if not strategies:
                raise RuntimeError("No strategies enabled")
            
            # Initialize Weight Optimizer
            weight_optimizer = WeightOptimizer(
                model_path=self.config.weight_optimizer_model_path
            )
            
            if self.config.enable_ml_optimization:
                try:
                    await weight_optimizer.load_model()
                    logger.info("ML Weight Optimizer loaded successfully")
                except Exception as e:
                    logger.warning(f"ML Weight Optimizer failed to load: {e}")
                    # Continue with equal weights fallback
            
            # Create ensemble portfolio manager
            self._ensemble_manager = await create_ensemble_portfolio_manager(
                strategies=strategies,
                redis_url=self.config.redis_url,
                weight_optimizer=weight_optimizer,
                supabase_url=self.config.supabase_url,
                supabase_key=self.config.supabase_key,
                wandb_api_key=self.config.wandb_api_key,
                config=self._get_ensemble_config()
            )
            
            logger.info(f"Strategy ensemble initialized with {len(strategies)} strategies")
            
        except Exception as e:
            logger.error(f"Failed to initialize strategy ensemble: {e}")
            raise
    
    async def _initialize_execution_services(self):
        """Initialize trade execution services."""
        logger.info("Initializing execution services...")
        
        try:
            # Initialize execution service
            # This would typically connect to Binance API or paper trading system
            self._execution_service = ExecutionService(
                paper_trading=self.config.enable_paper_trading,
                real_trading=self.config.enable_real_trading,
                redis_service=self._redis_service
            )
            
            # Test execution service
            await self._execution_service.initialize()
            
            logger.info("Execution service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize execution service: {e}")
            raise
    
    async def _initialize_monitoring_services(self):
        """Initialize monitoring and risk management services."""
        logger.info("Initializing monitoring services...")
        
        try:
            # Initialize risk monitor
            self._risk_monitor = RiskMonitor(
                max_position_size=self.config.global_position_limit,
                max_drawdown=self.config.global_drawdown_limit,
                supabase_service=self._supabase_service
            )
            
            logger.info("Risk monitor initialized successfully")
            
            # Initialize Telegram monitor
            if self.config.telegram_bot_token and self.config.telegram_chat_id:
                try:
                    self._telegram_monitor = TelegramPerformanceMonitor(
                        bot_token=self.config.telegram_bot_token,
                        chat_id=self.config.telegram_chat_id
                    )
                    
                    # Test Telegram connection
                    await self._telegram_monitor.test_connection()
                    logger.info("Telegram monitor initialized successfully")
                    
                except Exception as e:
                    logger.warning(f"Telegram monitor initialization failed: {e}")
                    self._telegram_monitor = None
            
        except Exception as e:
            logger.error(f"Failed to initialize monitoring services: {e}")
            raise
    
    async def _perform_system_health_checks(self, controller: AutoTradingController):
        """Perform comprehensive system health checks."""
        logger.info("Performing system health checks...")
        
        try:
            # Check Redis connectivity
            if self._redis_service:
                await self._redis_service.ping()
            
            # Check Supabase connectivity
            if self._supabase_service:
                await self._supabase_service.test_connection()
            
            # Check execution service
            if self._execution_service:
                health = await self._execution_service.health_check()
                if not health.get("healthy", False):
                    raise RuntimeError("Execution service health check failed")
            
            # Check ensemble manager
            if self._ensemble_manager:
                performance_summary = await self._ensemble_manager.get_performance_summary()
                logger.info(f"Ensemble manager status: {performance_summary}")
            
            logger.info("All system health checks passed")
            
        except Exception as e:
            logger.error(f"System health check failed: {e}")
            raise RuntimeError(f"System health check failed: {str(e)}")
    
    def _get_controller_config(self) -> Dict[str, Any]:
        """Get configuration for the Auto Trading Controller."""
        return {
            "trading_loop_interval": 5.0,
            "monitoring_interval": 10.0,
            "max_session_duration": 28800,  # 8 hours
            "emergency_stop_enabled": True,
            "auto_restart_on_error": False,
            "max_restart_attempts": 3,
            "health_check_interval": 30.0,
            "performance_logging_interval": 60.0,
            "cache_ttl": 300,
            "enable_paper_trading": self.config.enable_paper_trading,
            "enable_real_trading": self.config.enable_real_trading
        }
    
    def _get_ensemble_config(self) -> Dict[str, Any]:
        """Get configuration for the Ensemble Portfolio Manager."""
        return {
            "signal_cache_ttl": 30,
            "weights_cache_ttl": 300,
            "aggregation_cache_ttl": 10,
            "performance_cache_ttl": 60,
            "correlation_cache_ttl": 1800,
            "min_confidence_threshold": 0.6,
            "max_position_size": self.config.global_position_limit,
            "risk_correlation_threshold": 0.8,
            "enable_parallel_execution": True,
            "cache_warming_enabled": True,
            "performance_tracking_enabled": True
        }
    
    @classmethod
    def create_from_settings(cls, settings: Settings) -> 'AutoTradingFactory':
        """
        Create factory from application settings.
        
        Args:
            settings: Application settings instance
            
        Returns:
            Configured AutoTradingFactory instance
        """
        config = AutoTradingConfig(
            environment=getattr(settings, 'environment', 'development'),
            enable_real_trading=getattr(settings, 'enable_real_trading', False),
            enable_paper_trading=getattr(settings, 'enable_paper_trading', True),
            redis_url=getattr(settings, 'redis_url', 'redis://localhost:6379'),
            supabase_url=getattr(settings, 'supabase_url', None),
            supabase_key=getattr(settings, 'supabase_key', None),
            wandb_api_key=getattr(settings, 'wandb_api_key', None),
            telegram_bot_token=getattr(settings, 'telegram_bot_token', None),
            telegram_chat_id=getattr(settings, 'telegram_chat_id', None)
        )
        
        return cls(config)
    
    @classmethod
    def create_development_instance(cls) -> 'AutoTradingFactory':
        """
        Create a development instance with safe defaults.
        
        Returns:
            Development-configured AutoTradingFactory instance
        """
        config = AutoTradingConfig(
            environment="development",
            enable_real_trading=False,
            enable_paper_trading=True,
            redis_url="redis://localhost:6379",
            wandb_project="crypto-auto-trading-dev"
        )
        
        return cls(config)
    
    @classmethod
    def create_production_instance(
        cls,
        redis_url: str,
        supabase_url: str,
        supabase_key: str,
        wandb_api_key: Optional[str] = None,
        telegram_bot_token: Optional[str] = None,
        telegram_chat_id: Optional[str] = None,
        enable_real_trading: bool = False
    ) -> 'AutoTradingFactory':
        """
        Create a production instance with full configuration.
        
        Args:
            redis_url: Redis connection URL
            supabase_url: Supabase project URL
            supabase_key: Supabase API key
            wandb_api_key: W&B API key (optional)
            telegram_bot_token: Telegram bot token (optional)
            telegram_chat_id: Telegram chat ID (optional)
            enable_real_trading: Enable real trading (default: False for safety)
            
        Returns:
            Production-configured AutoTradingFactory instance
        """
        config = AutoTradingConfig(
            environment="production",
            enable_real_trading=enable_real_trading,
            enable_paper_trading=True,  # Keep paper trading available
            redis_url=redis_url,
            supabase_url=supabase_url,
            supabase_key=supabase_key,
            wandb_api_key=wandb_api_key,
            wandb_project="crypto-auto-trading-prod",
            telegram_bot_token=telegram_bot_token,
            telegram_chat_id=telegram_chat_id
        )
        
        return cls(config)

# Global factory instance for dependency injection
_auto_trading_factory: Optional[AutoTradingFactory] = None
_auto_trading_controller: Optional[AutoTradingController] = None

async def get_auto_trading_controller() -> AutoTradingController:
    """
    Get or create the global Auto Trading Controller instance.
    
    Returns:
        AutoTradingController instance
        
    Raises:
        RuntimeError: If controller initialization fails
    """
    global _auto_trading_controller, _auto_trading_factory
    
    if _auto_trading_controller is None:
        if _auto_trading_factory is None:
            # Create default development factory
            _auto_trading_factory = AutoTradingFactory.create_development_instance()
        
        _auto_trading_controller = await _auto_trading_factory.create_auto_trading_controller()
    
    return _auto_trading_controller

def set_auto_trading_factory(factory: AutoTradingFactory):
    """
    Set the global Auto Trading Factory instance.
    
    Args:
        factory: AutoTradingFactory instance
    """
    global _auto_trading_factory, _auto_trading_controller
    _auto_trading_factory = factory
    _auto_trading_controller = None  # Reset controller to force recreation

async def initialize_auto_trading_system(
    environment: str = "development",
    **config_kwargs
) -> AutoTradingController:
    """
    Initialize the complete Auto Trading system.
    
    Args:
        environment: Environment name (development, staging, production)
        **config_kwargs: Additional configuration parameters
        
    Returns:
        Initialized AutoTradingController instance
        
    Raises:
        RuntimeError: If initialization fails
    """
    if environment == "production":
        # Production requires explicit configuration
        required_keys = ['redis_url', 'supabase_url', 'supabase_key']
        missing_keys = [key for key in required_keys if key not in config_kwargs]
        if missing_keys:
            raise RuntimeError(f"Production environment requires: {missing_keys}")
        
        factory = AutoTradingFactory.create_production_instance(**config_kwargs)
    else:
        # Development/staging can use defaults
        config = AutoTradingConfig(environment=environment, **config_kwargs)
        factory = AutoTradingFactory(config)
    
    set_auto_trading_factory(factory)
    return await get_auto_trading_controller()