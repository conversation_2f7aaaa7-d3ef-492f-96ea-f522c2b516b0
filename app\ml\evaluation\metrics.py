"""
Evaluation metrics for ML weight optimization.

This module contains functions for evaluating the performance of ML models
for strategy weight optimization.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

logger = logging.getLogger(__name__)

class PerformanceMetrics:
    """Calculates performance metrics for ML models."""
    
    @staticmethod
    def calculate_regression_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate regression metrics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            
        Returns:
            Dictionary of regression metrics
        """
        try:
            # Mean Squared Error
            mse = mean_squared_error(y_true, y_pred)
            
            # Root Mean Squared Error
            rmse = np.sqrt(mse)
            
            # Mean Absolute Error
            mae = mean_absolute_error(y_true, y_pred)
            
            # R-squared
            r2 = r2_score(y_true, y_pred)
            
            # Explained Variance
            explained_variance = 1 - np.var(y_true - y_pred) / np.var(y_true)
            
            # Mean Absolute Percentage Error
            mape = np.mean(np.abs((y_true - y_pred) / np.maximum(np.abs(y_true), 1e-10))) * 100
            
            return {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2),
                'explained_variance': float(explained_variance),
                'mape': float(mape)
            }
            
        except Exception as e:
            logger.error(f"Error calculating regression metrics: {e}")
            return {
                'mse': 0.0,
                'rmse': 0.0,
                'mae': 0.0,
                'r2': 0.0,
                'explained_variance': 0.0,
                'mape': 0.0
            }
    
    @staticmethod
    def calculate_portfolio_metrics(returns: np.ndarray) -> Dict[str, float]:
        """Calculate portfolio performance metrics.
        
        Args:
            returns: Array of returns
            
        Returns:
            Dictionary of portfolio metrics
        """
        try:
            # Annualization factor (assuming daily returns)
            annualization_factor = 252
            
            # Total Return
            total_return = np.prod(1 + returns) - 1
            
            # Annualized Return
            annualized_return = (1 + total_return) ** (annualization_factor / len(returns)) - 1
            
            # Volatility
            volatility = np.std(returns) * np.sqrt(annualization_factor)
            
            # Sharpe Ratio (assuming risk-free rate of 0)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # Maximum Drawdown
            cumulative_returns = np.cumprod(1 + returns) - 1
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / (1 + running_max)
            max_drawdown = abs(np.min(drawdown))
            
            # Calmar Ratio
            calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
            
            # Sortino Ratio (assuming target return of 0)
            downside_returns = returns[returns < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(annualization_factor) if len(downside_returns) > 0 else 0
            sortino_ratio = annualized_return / downside_deviation if downside_deviation > 0 else 0
            
            # Win Rate
            win_rate = np.mean(returns > 0)
            
            # Profit Factor
            gross_profit = np.sum(returns[returns > 0])
            gross_loss = abs(np.sum(returns[returns < 0]))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            return {
                'total_return': float(total_return),
                'annualized_return': float(annualized_return),
                'volatility': float(volatility),
                'sharpe_ratio': float(sharpe_ratio),
                'max_drawdown': float(max_drawdown),
                'calmar_ratio': float(calmar_ratio),
                'sortino_ratio': float(sortino_ratio),
                'win_rate': float(win_rate),
                'profit_factor': float(profit_factor)
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio metrics: {e}")
            return {
                'total_return': 0.0,
                'annualized_return': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'calmar_ratio': 0.0,
                'sortino_ratio': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0
            }
    
    @staticmethod
    def calculate_strategy_allocation_metrics(weights: np.ndarray, returns: np.ndarray) -> Dict[str, float]:
        """Calculate metrics for strategy allocation.
        
        Args:
            weights: Array of strategy weights (shape: [n_samples, n_strategies])
            returns: Array of strategy returns (shape: [n_samples, n_strategies])
            
        Returns:
            Dictionary of allocation metrics
        """
        try:
            # Portfolio returns
            portfolio_returns = np.sum(weights * returns, axis=1)
            
            # Calculate portfolio metrics
            portfolio_metrics = PerformanceMetrics.calculate_portfolio_metrics(portfolio_returns)
            
            # Average weight allocation
            avg_weights = np.mean(weights, axis=0)
            
            # Weight volatility
            weight_volatility = np.std(weights, axis=0)
            
            # Weight turnover
            weight_diff = np.abs(weights[1:] - weights[:-1])
            turnover = np.mean(np.sum(weight_diff, axis=1) / 2)
            
            # Concentration
            herfindahl = np.mean(np.sum(weights ** 2, axis=1))
            
            # Diversification ratio
            weighted_volatility = np.sum(weights * np.std(returns, axis=0), axis=1)
            portfolio_volatility = np.std(portfolio_returns)
            diversification_ratio = np.mean(weighted_volatility) / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # Combine metrics
            metrics = {
                'turnover': float(turnover),
                'herfindahl': float(herfindahl),
                'diversification_ratio': float(diversification_ratio)
            }
            
            # Add portfolio metrics
            metrics.update(portfolio_metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating strategy allocation metrics: {e}")
            return {
                'turnover': 0.0,
                'herfindahl': 0.0,
                'diversification_ratio': 0.0,
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0
            }
    
    @staticmethod
    def compare_models(baseline_metrics: Dict[str, float], model_metrics: Dict[str, float]) -> Dict[str, float]:
        """Compare metrics between baseline and model.
        
        Args:
            baseline_metrics: Dictionary of baseline metrics
            model_metrics: Dictionary of model metrics
            
        Returns:
            Dictionary of metric improvements
        """
        try:
            # Calculate improvements
            improvements = {}
            
            for key in model_metrics:
                if key in baseline_metrics:
                    # For metrics where higher is better
                    if key in ['r2', 'explained_variance', 'total_return', 'annualized_return', 
                              'sharpe_ratio', 'calmar_ratio', 'sortino_ratio', 'win_rate', 
                              'profit_factor', 'diversification_ratio']:
                        improvements[key] = model_metrics[key] - baseline_metrics[key]
                    
                    # For metrics where lower is better
                    elif key in ['mse', 'rmse', 'mae', 'mape', 'volatility', 'max_drawdown', 
                                'turnover', 'herfindahl']:
                        improvements[key] = baseline_metrics[key] - model_metrics[key]
            
            # Calculate percentage improvements
            percentage_improvements = {}
            
            for key, value in improvements.items():
                if key in baseline_metrics and baseline_metrics[key] != 0:
                    percentage_improvements[f"{key}_pct"] = value / abs(baseline_metrics[key]) * 100
            
            # Combine absolute and percentage improvements
            result = {**improvements, **percentage_improvements}
            
            return result
            
        except Exception as e:
            logger.error(f"Error comparing models: {e}")
            return {}
