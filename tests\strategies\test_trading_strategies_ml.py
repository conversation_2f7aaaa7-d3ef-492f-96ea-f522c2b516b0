#!/usr/bin/env python3
"""
Test Trading Strategies with ML Integration
Validate enhanced base strategy and automated portfolio manager functionality.
"""

import sys
import os
sys.path.append('/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2')

import asyncio
import numpy as np
from datetime import datetime
from dataclasses import dataclass

def test_trading_strategies_ml():
    """Test trading strategies with ML integration"""
    print('Testing trading strategies with ML integration...')

    try:
        # Test enhanced base strategy
        from app.strategies.enhanced_base_strategy import EnhancedBaseStrategy
        from app.models.market_data import MarketData
        
        # Create sample market data
        market_data = MarketData(
            symbol="BTCUSDT",
            timestamp=datetime.now(),
            price=45000.0,
            volume=1500000.0,
            high_24h=45500.0,
            low_24h=44500.0,
            change_24h=0.015
        )
        
        print(f'✓ Sample market data created: {market_data.symbol} @ ${market_data.price:,.2f}')
        
        # Test automated portfolio manager configuration
        from app.strategies.automated_portfolio_manager import AutomatedConfig, AutomatedMetrics
        
        config = AutomatedConfig(
            max_execution_time_ms=1000,
            min_confidence_threshold=0.6,
            enable_parallel_execution=True,
            enable_conflict_resolution=True
        )
        
        print(f'✓ AutomatedConfig created with {config.max_execution_time_ms}ms execution target')
        
        # Test metrics initialization
        metrics = AutomatedMetrics(
            execution_time_ms=250.0,
            cache_hit_rate=0.85,
            ml_prediction_accuracy=0.78,
            conflict_resolution_count=2,
            automated_trades_count=15,
            manual_override_count=1,
            system_availability=0.995,
            timestamp=datetime.now()
        )
        
        print(f'✓ AutomatedMetrics created: {metrics.execution_time_ms}ms avg execution')
        print(f'  Cache hit rate: {metrics.cache_hit_rate:.1%}')
        print(f'  ML accuracy: {metrics.ml_prediction_accuracy:.1%}')
        print(f'  System availability: {metrics.system_availability:.1%}')
        
        # Test Kelly criterion optimization
        def calculate_kelly_criterion(win_rate: float, avg_win: float, avg_loss: float) -> float:
            """Calculate Kelly criterion fraction for position sizing"""
            if avg_loss == 0 or win_rate == 0:
                return 0.0
            
            # Kelly formula: f = (bp - q) / b
            # where f = fraction, b = odds (avg_win/avg_loss), p = win_rate, q = loss_rate
            b = avg_win / avg_loss
            p = win_rate
            q = 1 - win_rate
            
            kelly_fraction = (b * p - q) / b
            return max(0.0, min(kelly_fraction, 0.25))  # Cap at 25% for safety
        
        win_rate = 0.65
        avg_win = 120.0
        avg_loss = 80.0
        
        kelly_fraction = calculate_kelly_criterion(win_rate, avg_win, avg_loss)
        print(f'✓ Kelly criterion calculated: {kelly_fraction:.3f} fraction')
        print(f'  Win rate: {win_rate:.1%}, Avg win: ${avg_win}, Avg loss: ${avg_loss}')
        
        # Test position sizing with ML inputs
        def calculate_position_size(portfolio_value, risk_per_trade, volatility, confidence, kelly_fraction):
            """Simple position size calculation"""
            base_risk = portfolio_value * risk_per_trade
            kelly_adjusted = base_risk * kelly_fraction
            confidence_adjusted = kelly_adjusted * confidence
            volatility_adjusted = confidence_adjusted / (1 + volatility)
            return volatility_adjusted
        
        portfolio_value = 10000.0
        risk_per_trade = 0.02  # 2%
        volatility = 0.025
        confidence = 0.8
        
        position_size = calculate_position_size(
            portfolio_value=portfolio_value,
            risk_per_trade=risk_per_trade,
            volatility=volatility,
            confidence=confidence,
            kelly_fraction=kelly_fraction
        )
        
        print(f'✓ Position size calculated: ${position_size:,.2f}')
        print(f'  Risk per trade: {risk_per_trade:.1%}, Confidence: {confidence:.1%}')
        
        # Test volatility calculation
        def calculate_volatility(prices):
            """Simple volatility calculation"""
            returns = [abs((prices[i] - prices[i-1]) / prices[i-1]) for i in range(1, len(prices))]
            return np.std(returns) if returns else 0.0
        
        prices = [44800, 45000, 44700, 45200, 45100, 44900, 45300, 45000]
        volatility_calculated = calculate_volatility(prices)
        
        print(f'✓ Volatility calculated: {volatility_calculated:.4f}')
        
        # Test drawdown calculation
        def calculate_max_drawdown(portfolio_values):
            """Simple max drawdown calculation"""
            if not portfolio_values:
                return 0.0
            peak = portfolio_values[0]
            max_dd = 0.0
            for value in portfolio_values[1:]:
                if value > peak:
                    peak = value
                dd = (peak - value) / peak
                max_dd = max(max_dd, dd)
            return max_dd
        
        portfolio_values = [10000, 10150, 9950, 10300, 10100, 9800, 10400, 10200]
        max_drawdown = calculate_max_drawdown(portfolio_values)
        
        print(f'✓ Max drawdown calculated: {max_drawdown:.2%}')
        
        # Test performance calculation
        def calculate_sharpe_ratio(returns, risk_free_rate):
            """Simple Sharpe ratio calculation"""
            if not returns:
                return 0.0
            excess_returns = [r - risk_free_rate/252 for r in returns]  # Daily risk-free rate
            return np.mean(excess_returns) / np.std(excess_returns) if np.std(excess_returns) > 0 else 0.0
        
        returns = [0.015, -0.008, 0.025, -0.012, 0.018, 0.005, -0.003, 0.021]
        risk_free_rate = 0.02  # 2% annual
        
        sharpe_ratio = calculate_sharpe_ratio(returns, risk_free_rate)
        print(f'✓ Sharpe ratio calculated: {sharpe_ratio:.3f}')
        
        # Test ML analytics with strategy performance
        from app.utils.ml_analytics import MLAnalyticsEngine
        
        analytics = MLAnalyticsEngine()
        
        strategy_session_data = {
            'performance': {
                'ml_model_accuracy': 0.75,
                'ml_decisions_count': 50,
                'ml_decisions_correct': 38,
                'ml_decisions_profitable': 35,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate
            },
            'trades': [
                {'strategy': 'ml_enhanced_kelly', 'pnl': 125.50, 'confidence': 0.85},
                {'strategy': 'traditional_grid', 'pnl': 67.20, 'confidence': 0.70},
                {'strategy': 'ml_volatility_scaled', 'pnl': -45.30, 'confidence': 0.60},
                {'strategy': 'enhanced_ta', 'pnl': 89.10, 'confidence': 0.80}
            ]
        }
        
        print('Running strategy correlation analysis...')
        correlation_analysis = asyncio.run(analytics.analyze_strategy_correlation(strategy_session_data))
        
        print('✓ Strategy correlation analysis completed')
        print(f'  Analysis components: {len(correlation_analysis)}')
        
        # Test real-time signal processing simulation
        print('Testing real-time signal processing...')
        
        signals = []
        for i in range(5):
            # Simulate strategy signals with ML confidence
            signal = {
                'timestamp': datetime.now(),
                'action': 'BUY' if i % 2 == 0 else 'SELL',
                'confidence': 0.6 + (i * 0.1),
                'ml_score': 0.7 + (i * 0.05),
                'kelly_position': kelly_fraction * 0.8,
                'volatility_adjusted': True
            }
            signals.append(signal)
        
        avg_confidence = np.mean([s['confidence'] for s in signals])
        avg_ml_score = np.mean([s['ml_score'] for s in signals])
        
        print(f'✓ Real-time signal processing completed: {len(signals)} signals')
        print(f'  Average confidence: {avg_confidence:.2f}')
        print(f'  Average ML score: {avg_ml_score:.2f}')
        
        print('Trading strategies with ML integration tests passed!')
        return True
        
    except Exception as e:
        print(f'Error testing trading strategies: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_trading_strategies_ml()
    sys.exit(0 if success else 1)