"""
Test suite for the Ensemble Configuration and Data Classes.
Standalone tests to avoid circular import issues.
"""

import pytest
from datetime import datetime
import json
import os
import sys

# Add project root to path and import only what we need directly
project_root = '/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import directly without going through app.strategies.__init__
sys.modules['app'] = type(sys)('app')
sys.modules['app.strategies'] = type(sys)('app.strategies')
sys.modules['app.strategies.ensemble'] = type(sys)('app.strategies.ensemble')

# Now import our specific module
import importlib.util
config_spec = importlib.util.spec_from_file_location(
    "config", 
    f"{project_root}/app/strategies/ensemble/config.py"
)
config_module = importlib.util.module_from_spec(config_spec)
config_spec.loader.exec_module(config_module)

# Extract the classes we need
EnsembleConfig = config_module.EnsembleConfig
StrategyWeight = config_module.StrategyWeight
AggregatedSignal = config_module.AggregatedSignal
PortfolioMetrics = config_module.PortfolioMetrics
RiskLimits = config_module.RiskLimits


class TestEnsembleConfig:
    """Test the ensemble configuration management."""
    
    def test_ensemble_config_creation(self):
        """Test creating an ensemble configuration."""
        config = EnsembleConfig(
            strategy_names=['GridStrategy', 'TechnicalAnalysisStrategy', 'TrendFollowingStrategy'],
            default_weights={'GridStrategy': 0.33, 'TechnicalAnalysisStrategy': 0.33, 'TrendFollowingStrategy': 0.34},
            confidence_threshold=0.6,
            max_position_size=0.1,
            max_portfolio_risk=0.8,
            correlation_threshold=0.8,
            redis_ttl_weights=300,
            redis_ttl_signals=30,
            redis_ttl_volatility=900,
            alert_thresholds={'drawdown': 0.15, 'sharpe_ratio': 0.5}
        )
        
        assert len(config.strategy_names) == 3
        assert config.confidence_threshold == 0.6
        assert sum(config.default_weights.values()) == pytest.approx(1.0, rel=1e-9)
        assert config.redis_ttl_weights == 300

    def test_config_from_env(self):
        """Test loading configuration from environment variables."""
        config = EnsembleConfig.from_env()
        
        assert isinstance(config.strategy_names, list)
        assert len(config.strategy_names) == 3
        assert config.confidence_threshold > 0
        assert config.max_position_size > 0
        assert 'GridStrategy' in config.strategy_names
        assert 'TechnicalAnalysisStrategy' in config.strategy_names
        assert 'TrendFollowingStrategy' in config.strategy_names

    def test_config_to_dict(self):
        """Test converting configuration to dictionary."""
        config = EnsembleConfig.from_env()
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert 'strategy_names' in config_dict
        assert 'confidence_threshold' in config_dict
        assert 'alert_thresholds' in config_dict

    def test_config_to_json(self):
        """Test converting configuration to JSON."""
        config = EnsembleConfig.from_env()
        config_json = config.to_json()
        
        assert isinstance(config_json, str)
        parsed_config = json.loads(config_json)
        assert isinstance(parsed_config, dict)
        assert 'strategy_names' in parsed_config


class TestStrategyWeight:
    """Test the strategy weight data class."""
    
    def test_strategy_weight_creation(self):
        """Test creating a strategy weight object."""
        timestamp = datetime.now()
        weight = StrategyWeight(
            strategy_name="GridStrategy",
            weight=0.4,
            confidence=0.8,
            last_updated=timestamp
        )
        
        assert weight.strategy_name == "GridStrategy"
        assert weight.weight == 0.4
        assert weight.confidence == 0.8
        assert weight.last_updated == timestamp

    def test_strategy_weight_validation(self):
        """Test strategy weight validation."""
        timestamp = datetime.now()
        
        # Valid weight
        weight = StrategyWeight(
            strategy_name="GridStrategy",
            weight=0.5,
            confidence=0.7,
            last_updated=timestamp
        )
        assert weight.weight == 0.5
        
        # Test boundary values
        weight_zero = StrategyWeight(
            strategy_name="GridStrategy",
            weight=0.0,
            confidence=0.0,
            last_updated=timestamp
        )
        assert weight_zero.weight == 0.0
        
        weight_one = StrategyWeight(
            strategy_name="GridStrategy", 
            weight=1.0,
            confidence=1.0,
            last_updated=timestamp
        )
        assert weight_one.weight == 1.0

    def test_strategy_weight_to_dict(self):
        """Test converting strategy weight to dictionary."""
        timestamp = datetime.now()
        weight = StrategyWeight(
            strategy_name="GridStrategy",
            weight=0.4,
            confidence=0.8,
            last_updated=timestamp
        )
        
        weight_dict = weight.to_dict()
        assert isinstance(weight_dict, dict)
        assert weight_dict['strategy_name'] == "GridStrategy"
        assert weight_dict['weight'] == 0.4
        assert weight_dict['confidence'] == 0.8


class TestAggregatedSignal:
    """Test the aggregated signal data class."""
    
    def test_aggregated_signal_creation(self):
        """Test creating an aggregated signal."""
        timestamp = datetime.now()
        signal = AggregatedSignal(
            action="BUY",
            quantity=100.0,
            price=50000.0,
            confidence=0.8,
            contributing_strategies=["GridStrategy", "TechnicalAnalysisStrategy"],
            timestamp=timestamp
        )
        
        assert signal.action == "BUY"
        assert signal.quantity == 100.0
        assert signal.price == 50000.0
        assert signal.confidence == 0.8
        assert len(signal.contributing_strategies) == 2
        assert signal.timestamp == timestamp

    def test_signal_action_types(self):
        """Test different signal action types."""
        timestamp = datetime.now()
        
        for action in ["BUY", "SELL", "HOLD"]:
            signal = AggregatedSignal(
                action=action,
                quantity=100.0,
                price=50000.0,
                confidence=0.7,
                contributing_strategies=["GridStrategy"],
                timestamp=timestamp
            )
            assert signal.action == action

    def test_aggregated_signal_to_dict(self):
        """Test converting aggregated signal to dictionary."""
        timestamp = datetime.now()
        signal = AggregatedSignal(
            action="BUY",
            quantity=100.0,
            price=50000.0,
            confidence=0.8,
            contributing_strategies=["GridStrategy", "TechnicalAnalysisStrategy"],
            timestamp=timestamp
        )
        
        signal_dict = signal.to_dict()
        assert isinstance(signal_dict, dict)
        assert signal_dict['action'] == "BUY"
        assert signal_dict['quantity'] == 100.0
        assert signal_dict['confidence'] == 0.8
        assert len(signal_dict['contributing_strategies']) == 2


class TestPortfolioMetrics:
    """Test portfolio performance metrics."""
    
    def test_portfolio_metrics_creation(self):
        """Test creating portfolio metrics object."""
        timestamp = datetime.now()
        metrics = PortfolioMetrics(
            total_pnl=1000.0,
            sharpe_ratio=1.5,
            max_drawdown=-0.1,
            win_rate=0.6,
            strategy_contributions={'GridStrategy': 600.0, 'TechnicalAnalysisStrategy': 400.0},
            correlation_matrix={'GridStrategy': {'TechnicalAnalysisStrategy': 0.3}},
            timestamp=timestamp
        )
        
        assert metrics.total_pnl == 1000.0
        assert metrics.sharpe_ratio == 1.5
        assert metrics.max_drawdown == -0.1
        assert metrics.win_rate == 0.6
        assert len(metrics.strategy_contributions) == 2
        assert metrics.timestamp == timestamp
    
    def test_metrics_validation(self):
        """Test metrics validation and constraints."""
        timestamp = datetime.now()
        
        # Valid metrics
        metrics = PortfolioMetrics(
            total_pnl=-500.0,  # Can be negative
            sharpe_ratio=0.0,  # Can be zero
            max_drawdown=-0.2,  # Should be negative
            win_rate=1.0,  # Can be 100%
            strategy_contributions={},
            correlation_matrix={},
            timestamp=timestamp
        )
        
        assert metrics.total_pnl == -500.0
        assert metrics.win_rate == 1.0
        assert metrics.max_drawdown == -0.2

    def test_portfolio_metrics_to_dict(self):
        """Test converting portfolio metrics to dictionary."""
        timestamp = datetime.now()
        metrics = PortfolioMetrics(
            total_pnl=1000.0,
            sharpe_ratio=1.5,
            max_drawdown=-0.1,
            win_rate=0.6,
            strategy_contributions={'GridStrategy': 600.0},
            correlation_matrix={'GridStrategy': {'TechnicalAnalysisStrategy': 0.3}},
            timestamp=timestamp
        )
        
        metrics_dict = metrics.to_dict()
        assert isinstance(metrics_dict, dict)
        assert metrics_dict['total_pnl'] == 1000.0
        assert metrics_dict['sharpe_ratio'] == 1.5
        assert metrics_dict['win_rate'] == 0.6


class TestRiskLimits:
    """Test risk limits configuration."""
    
    def test_risk_limits_creation(self):
        """Test creating risk limits object."""
        limits = RiskLimits(
            max_position_size=0.1,
            max_portfolio_risk=0.8,
            max_correlation_exposure=0.5,
            max_drawdown_limit=0.2
        )
        
        assert limits.max_position_size == 0.1
        assert limits.max_portfolio_risk == 0.8
        assert limits.max_correlation_exposure == 0.5
        assert limits.max_drawdown_limit == 0.2

    def test_risk_limits_to_dict(self):
        """Test converting risk limits to dictionary."""
        limits = RiskLimits(
            max_position_size=0.1,
            max_portfolio_risk=0.8,
            max_correlation_exposure=0.5,
            max_drawdown_limit=0.2
        )
        
        limits_dict = limits.to_dict()
        assert isinstance(limits_dict, dict)
        assert limits_dict['max_position_size'] == 0.1
        assert limits_dict['max_portfolio_risk'] == 0.8


if __name__ == "__main__":
    pytest.main([__file__])