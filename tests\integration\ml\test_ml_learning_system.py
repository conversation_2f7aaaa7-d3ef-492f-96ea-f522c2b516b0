#!/usr/bin/env python3
"""
Test script for the comprehensive real-time ML prediction and learning system
in the Auto Trading Controller.

This script validates the implementation of:
1. Continuous Learning Loop
2. Real-time Feedback System
3. Online Learning Updates
4. Performance-based Learning
5. Adaptive Learning Rate

Created: June 16, 2025
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from unittest.mock import Mock, AsyncMock

# Add project root to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '.'))

from app.services.auto_trading_controller import (
    AutoTradingController, 
    TradingParameters, 
    TradingSession,
    TradingSessionStatus,
    MarketData,
    Trade
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockWeightOptimizer:
    """Mock weight optimizer for testing"""
    
    def __init__(self):
        self.model = Mock()
        self.model.fit = Mock()
        self.partial_fit = Mock()
    
    async def predict_weights(self, market_conditions, log_prediction=True):
        # Return mock weights
        return [0.4, 0.3, 0.3]
    
    def get_model_info(self):
        return {
            "status": "loaded",
            "version": "test_v1",
            "timestamp": datetime.now().isoformat(),
            "metrics": {"test_r2": 0.85}
        }

class MockRedisService:
    """Mock Redis service for testing"""
    
    def __init__(self):
        self.data = {}
    
    async def setex(self, key, ttl, value):
        self.data[key] = value
        return True
    
    async def get(self, key):
        return self.data.get(key)
    
    async def ping(self):
        return True

async def test_continuous_learning_loop():
    """Test the continuous learning loop functionality"""
    logger.info("Testing continuous learning loop...")
    
    # Create mock services
    mock_ensemble = Mock()
    mock_execution = Mock()
    mock_redis = MockRedisService()
    mock_supabase = Mock()
    
    # Create controller
    controller = AutoTradingController(
        ensemble_manager=mock_ensemble,
        execution_service=mock_execution,
        redis_service=mock_redis,
        supabase_service=mock_supabase
    )
    
    # Initialize ML services with mock
    controller.weight_optimizer = MockWeightOptimizer()
    controller.ml_monitoring_enabled = True
    controller.online_learning_enabled = True
    
    # Create a test session
    controller.current_session = TradingSession(
        id="test_session",
        status=TradingSessionStatus.RUNNING,
        start_time=datetime.now(),
        parameters=TradingParameters()
    )
    controller.is_running = True
    
    # Add some mock feedback data
    controller.ml_feedback_buffer = [
        {
            'timestamp': datetime.now().isoformat(),
            'symbol': 'BTCUSDT',
            'market_conditions': {'volatility': 0.02, 'volume': 1000000, 'rsi': 60},
            'predicted_weights': {'grid_weight': 0.4, 'ta_weight': 0.3, 'trend_weight': 0.3},
            'actual_pnl': 100.0,
            'actual_return': 0.01,
            'accuracy_score': 0.8,
            'trade_count': 2,
            'total_value': 10000
        }
    ]
    
    # Add prediction accuracy history
    controller.prediction_accuracy_history = [0.7, 0.8, 0.6, 0.9, 0.5, 0.8, 0.7]
    
    logger.info("✓ Continuous learning loop setup complete")
    
    # Test learning status
    status = await controller.get_ml_learning_status()
    assert status["learning_system"]["enabled"] == True
    assert len(status["buffer_status"]) > 0
    logger.info("✓ Learning status retrieval working")
    
    # Test should update model logic
    should_update = await controller._should_update_model()
    logger.info(f"✓ Model update check: {should_update}")
    
    # Test prediction accuracy analysis
    await controller._analyze_prediction_accuracy()
    logger.info("✓ Prediction accuracy analysis working")
    
    # Test adaptive learning rate adjustment
    await controller._adjust_learning_rate()
    logger.info("✓ Adaptive learning rate adjustment working")
    
    return True

async def test_ml_feedback_collection():
    """Test ML feedback collection functionality"""
    logger.info("Testing ML feedback collection...")
    
    # Create mock services
    mock_ensemble = Mock()
    mock_execution = Mock()
    mock_redis = MockRedisService()
    mock_supabase = Mock()
    
    # Create controller
    controller = AutoTradingController(
        ensemble_manager=mock_ensemble,
        execution_service=mock_execution,
        redis_service=mock_redis,
        supabase_service=mock_supabase
    )
    
    # Initialize ML services
    controller.weight_optimizer = MockWeightOptimizer()
    controller.online_learning_enabled = True
    
    # Create test market data
    market_data = {
        'BTCUSDT': MarketData(
            symbol='BTCUSDT',
            timestamp=datetime.now(),
            price=50000.0,
            volume=1000000,
            high_24h=51000.0,
            low_24h=49000.0,
            change_24h=2.0
        )
    }
    
    # Create test ML predictions
    ml_predictions = {
        'BTCUSDT': {
            'grid_weight': 0.4,
            'ta_weight': 0.3,
            'trend_weight': 0.3
        }
    }
    
    # Create test executed trades
    executed_trades = [
        Trade(
            id="trade_1",
            timestamp=datetime.now(),
            symbol="BTCUSDT",
            action="BUY",
            quantity=0.01,
            price=50000.0,
            value=500.0,
            strategy="grid",
            confidence=0.8,
            execution_time_ms=100.0,
            slippage=0.001,
            commission=1.0,
            pnl=10.0,
            status="FILLED"
        )
    ]
    
    # Test feedback collection
    initial_buffer_size = len(controller.ml_feedback_buffer)
    await controller._collect_ml_feedback(market_data, ml_predictions, executed_trades)
    
    # Verify feedback was collected
    assert len(controller.ml_feedback_buffer) > initial_buffer_size
    assert len(controller.ml_training_data) > 0
    assert len(controller.prediction_accuracy_history) > 0
    
    logger.info("✓ ML feedback collection working")
    
    # Test the feedback structure
    feedback = controller.ml_feedback_buffer[-1]
    required_keys = ['timestamp', 'symbol', 'market_conditions', 'predicted_weights', 
                    'actual_pnl', 'accuracy_score']
    for key in required_keys:
        assert key in feedback, f"Missing key: {key}"
    
    logger.info("✓ Feedback structure validation passed")
    
    return True

async def test_online_model_update():
    """Test online model update functionality"""
    logger.info("Testing online model update...")
    
    # Create mock services
    mock_ensemble = Mock()
    mock_execution = Mock()
    mock_redis = MockRedisService()
    mock_supabase = Mock()
    
    # Create controller
    controller = AutoTradingController(
        ensemble_manager=mock_ensemble,
        execution_service=mock_execution,
        redis_service=mock_redis,
        supabase_service=mock_supabase
    )
    
    # Initialize ML services
    controller.weight_optimizer = MockWeightOptimizer()
    controller.online_learning_enabled = True
    controller.min_feedback_samples = 2
    
    # Add sufficient feedback data
    controller.ml_feedback_buffer = [
        {
            'timestamp': datetime.now().isoformat(),
            'symbol': 'BTCUSDT',
            'market_conditions': {'volatility': 0.02, 'volume': 1000000, 'rsi': 60, 'macd': 0.1, 
                                'price_change': 0.01, 'volatility_ma': 0.02, 'volume_ma': 1000000, 'rsi_ma': 55},
            'predicted_weights': {'grid_weight': 0.4, 'ta_weight': 0.3, 'trend_weight': 0.3},
            'actual_pnl': 100.0,
            'actual_return': 0.01,
            'accuracy_score': 0.8,
            'trade_count': 2,
            'total_value': 10000
        },
        {
            'timestamp': datetime.now().isoformat(),
            'symbol': 'ETHUSDT',
            'market_conditions': {'volatility': 0.03, 'volume': 800000, 'rsi': 70, 'macd': -0.1, 
                                'price_change': -0.005, 'volatility_ma': 0.025, 'volume_ma': 850000, 'rsi_ma': 65},
            'predicted_weights': {'grid_weight': 0.3, 'ta_weight': 0.4, 'trend_weight': 0.3},
            'actual_pnl': -50.0,
            'actual_return': -0.005,
            'accuracy_score': 0.2,
            'trade_count': 1,
            'total_value': 10000
        }
    ]
    
    # Test online model update
    update_success = await controller._update_model_online()
    
    # Verify update was attempted
    assert update_success == True, "Online model update should succeed with mock"
    
    logger.info("✓ Online model update working")
    
    return True

async def test_ml_learning_configuration():
    """Test ML learning system configuration"""
    logger.info("Testing ML learning configuration...")
    
    # Create mock services
    mock_ensemble = Mock()
    mock_execution = Mock()
    mock_redis = MockRedisService()
    mock_supabase = Mock()
    
    # Create controller
    controller = AutoTradingController(
        ensemble_manager=mock_ensemble,
        execution_service=mock_execution,
        redis_service=mock_redis,
        supabase_service=mock_supabase
    )
    
    # Test configuration
    config_result = await controller.configure_ml_learning(
        online_learning_enabled=True,
        learning_rate_adaptive=False,
        model_update_frequency=15,
        min_feedback_samples=8,
        accuracy_threshold=0.7
    )
    
    assert config_result["success"] == True
    assert len(config_result["changes"]) > 0
    assert controller.online_learning_enabled == True
    assert controller.learning_rate_adaptive == False
    assert controller.model_update_frequency == 15
    assert controller.min_feedback_samples == 8
    assert controller.accuracy_threshold == 0.7
    
    logger.info("✓ ML learning configuration working")
    
    return True

async def test_prediction_accuracy_analysis():
    """Test prediction accuracy analysis functionality"""
    logger.info("Testing prediction accuracy analysis...")
    
    # Create mock services
    mock_ensemble = Mock()
    mock_execution = Mock()
    mock_redis = MockRedisService()
    mock_supabase = Mock()
    
    # Create controller
    controller = AutoTradingController(
        ensemble_manager=mock_ensemble,
        execution_service=mock_execution,
        redis_service=mock_redis,
        supabase_service=mock_supabase
    )
    
    # Add prediction accuracy history
    controller.prediction_accuracy_history = [
        0.8, 0.7, 0.9, 0.6, 0.8, 0.5, 0.7, 0.9, 0.6, 0.8,
        0.4, 0.6, 0.7, 0.8, 0.5, 0.9, 0.7, 0.6, 0.8, 0.7
    ]
    
    # Test analysis
    await controller._analyze_prediction_accuracy()
    
    # Verify metrics were updated
    assert controller.ml_performance_metrics["prediction_accuracy"] > 0
    
    logger.info("✓ Prediction accuracy analysis working")
    
    return True

async def run_comprehensive_tests():
    """Run all ML learning system tests"""
    logger.info("=" * 60)
    logger.info("COMPREHENSIVE ML LEARNING SYSTEM TESTS")
    logger.info("=" * 60)
    
    test_results = []
    
    try:
        # Test 1: Continuous Learning Loop
        result1 = await test_continuous_learning_loop()
        test_results.append(("Continuous Learning Loop", result1))
        
        # Test 2: ML Feedback Collection
        result2 = await test_ml_feedback_collection()
        test_results.append(("ML Feedback Collection", result2))
        
        # Test 3: Online Model Update
        result3 = await test_online_model_update()
        test_results.append(("Online Model Update", result3))
        
        # Test 4: ML Learning Configuration
        result4 = await test_ml_learning_configuration()
        test_results.append(("ML Learning Configuration", result4))
        
        # Test 5: Prediction Accuracy Analysis
        result5 = await test_prediction_accuracy_analysis()
        test_results.append(("Prediction Accuracy Analysis", result5))
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        test_results.append(("Failed Test", False))
    
    # Print results
    logger.info("\n" + "=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    logger.info("-" * 60)
    logger.info(f"Total Tests: {total}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {total - passed}")
    logger.info(f"Success Rate: {(passed/total)*100:.1f}%" if total > 0 else "0.0%")
    
    if passed == total:
        logger.info("\n🎉 ALL TESTS PASSED! ML Learning system is working correctly.")
        return True
    else:
        logger.error(f"\n❌ {total - passed} tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    # Run the comprehensive tests
    success = asyncio.run(run_comprehensive_tests())
    sys.exit(0 if success else 1)