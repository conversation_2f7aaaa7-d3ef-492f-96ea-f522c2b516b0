#!/usr/bin/env python3
"""
Fix Database Performance Issues
This script addresses PostgreSQL connection delays and implements connection pooling.
Date: June 16, 2025
"""

import asyncio
import aiohttp
import os
import json
import time
from dotenv import load_dotenv
from typing import Dict, Any, Optional
import logging

# Load environment variables
load_dotenv('.env')

logger = logging.getLogger(__name__)

class DatabasePerformanceFixer:
    """Fix database performance and connection issues"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        self.performance_metrics = []
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in .env file")
    
    async def benchmark_database_operations(self) -> Dict[str, Any]:
        """Benchmark database operations to identify bottlenecks"""
        logger.info("Benchmarking database operations...")
        
        api_url = f"{self.supabase_url}/rest/v1"
        headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
        
        # Configure connection with optimizations
        connector = aiohttp.TCPConnector(
            limit=20,  # Total connection pool size
            limit_per_host=10,  # Connections per host
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
            keepalive_timeout=30,  # Keep connections alive
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        ) as session:
            
            # Test 1: Simple query performance
            start_time = time.time()
            try:
                async with session.get(f"{api_url}/trade_executions?select=id&limit=1") as response:
                    query_time = (time.time() - start_time) * 1000
                    query_success = response.status == 200
                    logger.info(f"Simple query: {query_time:.1f}ms - {'✓' if query_success else '✗'}")
                    
                    self.performance_metrics.append({
                        'operation': 'simple_query',
                        'time_ms': query_time,
                        'success': query_success
                    })
            except Exception as e:
                logger.error(f"Simple query failed: {e}")
                self.performance_metrics.append({
                    'operation': 'simple_query',
                    'time_ms': 30000,
                    'success': False,
                    'error': str(e)
                })
            
            # Test 2: Insert operation performance
            start_time = time.time()
            try:
                test_data = {
                    'strategy_name': 'performance_test',
                    'symbol': 'BTCUSDT',
                    'action': 'BUY',
                    'quantity': 0.001,
                    'price': 50000.0,
                    'timestamp': '2025-06-16T01:24:11.181Z',
                    'metadata': {'test': 'performance_benchmark'}
                }
                
                async with session.post(f"{api_url}/trade_executions", json=test_data) as response:
                    insert_time = (time.time() - start_time) * 1000
                    insert_success = response.status in [200, 201]
                    logger.info(f"Insert operation: {insert_time:.1f}ms - {'✓' if insert_success else '✗'}")
                    
                    self.performance_metrics.append({
                        'operation': 'insert',
                        'time_ms': insert_time,
                        'success': insert_success
                    })
                    
                    # Clean up test data
                    if insert_success:
                        result = await response.json()
                        if result and len(result) > 0:
                            test_id = result[0]['id']
                            async with session.delete(f"{api_url}/trade_executions?id=eq.{test_id}") as del_response:
                                pass
                                
            except Exception as e:
                logger.error(f"Insert operation failed: {e}")
                self.performance_metrics.append({
                    'operation': 'insert',
                    'time_ms': 30000,
                    'success': False,
                    'error': str(e)
                })
            
            # Test 3: Portfolio metrics insert (the problematic one)
            start_time = time.time()
            try:
                portfolio_data = {
                    'portfolio_value': 100000.0,
                    'total_return': 0.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'win_rate': 0.0,
                    'symbol': 'PERF_TEST',
                    'timestamp': '2025-06-16T01:24:11.181Z',
                    'metadata': {'performance_test': True}
                }
                
                async with session.post(f"{api_url}/portfolio_metrics", json=portfolio_data) as response:
                    portfolio_time = (time.time() - start_time) * 1000
                    portfolio_success = response.status in [200, 201]
                    logger.info(f"Portfolio metrics: {portfolio_time:.1f}ms - {'✓' if portfolio_success else '✗'}")
                    
                    if not portfolio_success:
                        error_text = await response.text()
                        logger.error(f"Portfolio metrics error: {error_text}")
                    
                    self.performance_metrics.append({
                        'operation': 'portfolio_metrics',
                        'time_ms': portfolio_time,
                        'success': portfolio_success
                    })
                    
                    # Clean up test data
                    if portfolio_success:
                        result = await response.json()
                        if result and len(result) > 0:
                            test_id = result[0]['id']
                            async with session.delete(f"{api_url}/portfolio_metrics?id=eq.{test_id}") as del_response:
                                pass
                                
            except Exception as e:
                logger.error(f"Portfolio metrics operation failed: {e}")
                self.performance_metrics.append({
                    'operation': 'portfolio_metrics',
                    'time_ms': 30000,
                    'success': False,
                    'error': str(e)
                })
            
            # Test 4: Concurrent operations
            start_time = time.time()
            try:
                concurrent_tasks = []
                for i in range(5):
                    task = session.get(f"{api_url}/trade_executions?select=id&limit=1")
                    concurrent_tasks.append(task)
                
                responses = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
                concurrent_time = (time.time() - start_time) * 1000
                
                successful_responses = sum(1 for r in responses if hasattr(r, 'status') and r.status == 200)
                concurrent_success = successful_responses >= 3
                
                logger.info(f"Concurrent operations (5x): {concurrent_time:.1f}ms - {successful_responses}/5 successful")
                
                self.performance_metrics.append({
                    'operation': 'concurrent',
                    'time_ms': concurrent_time,
                    'success': concurrent_success,
                    'successful_count': successful_responses
                })
                
                # Close responses
                for response in responses:
                    if hasattr(response, 'close'):
                        response.close()
                        
            except Exception as e:
                logger.error(f"Concurrent operations failed: {e}")
                self.performance_metrics.append({
                    'operation': 'concurrent',
                    'time_ms': 30000,
                    'success': False,
                    'error': str(e)
                })
        
        return self.analyze_performance_metrics()
    
    def analyze_performance_metrics(self) -> Dict[str, Any]:
        """Analyze performance metrics and provide recommendations"""
        
        analysis = {
            'overall_health': 'unknown',
            'average_response_time': 0,
            'slowest_operation': None,
            'failed_operations': [],
            'recommendations': []
        }
        
        if not self.performance_metrics:
            analysis['overall_health'] = 'error'
            analysis['recommendations'].append('No performance data collected')
            return analysis
        
        # Calculate average response time
        successful_operations = [m for m in self.performance_metrics if m['success']]
        if successful_operations:
            analysis['average_response_time'] = sum(m['time_ms'] for m in successful_operations) / len(successful_operations)
        
        # Find slowest operation
        if successful_operations:
            analysis['slowest_operation'] = max(successful_operations, key=lambda x: x['time_ms'])
        
        # Find failed operations
        analysis['failed_operations'] = [m for m in self.performance_metrics if not m['success']]
        
        # Generate recommendations based on metrics
        if analysis['failed_operations']:
            analysis['overall_health'] = 'critical'
            analysis['recommendations'].append('Some database operations are failing - check schema and permissions')
        elif analysis['average_response_time'] > 5000:
            analysis['overall_health'] = 'poor'
            analysis['recommendations'].append('Average response time is too high - implement connection pooling')
        elif analysis['average_response_time'] > 2000:
            analysis['overall_health'] = 'fair'
            analysis['recommendations'].append('Response times could be improved with optimization')
        else:
            analysis['overall_health'] = 'good'
            analysis['recommendations'].append('Database performance is acceptable')
        
        # Specific recommendations
        portfolio_metrics_ops = [m for m in self.performance_metrics if m['operation'] == 'portfolio_metrics']
        if portfolio_metrics_ops and not portfolio_metrics_ops[0]['success']:
            analysis['recommendations'].append('Portfolio metrics table has schema issues - run schema fix script')
        
        if analysis['average_response_time'] > 1000:
            analysis['recommendations'].extend([
                'Implement connection pooling for better performance',
                'Add database indexes for frequently queried columns',
                'Consider using read replicas for query operations',
                'Implement query result caching for static data'
            ])
        
        return analysis
    
    async def create_optimized_connection_config(self) -> Dict[str, Any]:
        """Create optimized connection configuration"""
        
        config = {
            "database_connection": {
                "connection_pool_size": 20,
                "max_connections_per_host": 10,
                "connection_timeout_seconds": 10,
                "read_timeout_seconds": 30,
                "keepalive_timeout_seconds": 30,
                "dns_cache_ttl_seconds": 300,
                "retry_attempts": 3,
                "retry_delay_seconds": 1
            },
            "paper_trading_optimizations": {
                "use_connection_pooling": True,
                "cache_database_responses": True,
                "batch_database_operations": True,
                "async_database_writes": True,
                "connection_validation": True,
                "timeout_configurations": {
                    "simple_query_timeout": 5,
                    "insert_operation_timeout": 10,
                    "complex_query_timeout": 15,
                    "bulk_operation_timeout": 30
                }
            },
            "monitoring": {
                "track_connection_metrics": True,
                "log_slow_queries": True,
                "slow_query_threshold_ms": 1000,
                "connection_health_checks": True,
                "health_check_interval_seconds": 60
            }
        }
        
        # Save configuration
        with open('database_performance_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info("✓ Optimized database connection configuration created")
        return config
    
    async def create_connection_pool_service(self) -> str:
        """Create an optimized connection pool service"""
        
        service_code = '''#!/usr/bin/env python3
"""
Optimized Database Connection Pool Service
Provides connection pooling and performance optimizations for Supabase operations.
Auto-generated on: June 16, 2025
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv('.env')
logger = logging.getLogger(__name__)

class OptimizedSupabaseService:
    """Optimized Supabase service with connection pooling"""
    
    def __init__(self, pool_size: int = 20, max_per_host: int = 10):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")
        
        self.api_url = f"{self.supabase_url}/rest/v1"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
        
        # Connection pool configuration
        self.connector = aiohttp.TCPConnector(
            limit=pool_size,
            limit_per_host=max_per_host,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        self.timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = None
        
        # Performance tracking
        self.metrics = {
            'requests_total': 0,
            'requests_successful': 0,
            'average_response_time': 0,
            'response_times': []
        }
    
    async def connect(self):
        """Initialize connection pool"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=self.timeout,
                headers=self.headers
            )
        logger.info("✓ Optimized Supabase connection pool initialized")
    
    async def disconnect(self):
        """Close connection pool"""
        if self.session:
            await self.session.close()
        logger.info("✓ Connection pool closed")
    
    async def _execute_request(self, method: str, endpoint: str, data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute HTTP request with performance tracking"""
        if not self.session:
            await self.connect()
        
        start_time = time.time()
        self.metrics['requests_total'] += 1
        
        try:
            url = f"{self.api_url}/{endpoint}"
            
            if method.upper() == 'GET':
                async with self.session.get(url, params=params) as response:
                    result = await self._process_response(response, start_time)
            elif method.upper() == 'POST':
                async with self.session.post(url, json=data, params=params) as response:
                    result = await self._process_response(response, start_time)
            elif method.upper() == 'DELETE':
                async with self.session.delete(url, params=params) as response:
                    result = await self._process_response(response, start_time)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            self.metrics['requests_successful'] += 1
            return result
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            self.metrics['response_times'].append(response_time)
            logger.error(f"Request failed after {response_time:.1f}ms: {e}")
            raise
    
    async def _process_response(self, response, start_time: float) -> Dict[str, Any]:
        """Process HTTP response"""
        response_time = (time.time() - start_time) * 1000
        self.metrics['response_times'].append(response_time)
        
        # Update average response time
        if self.metrics['response_times']:
            self.metrics['average_response_time'] = sum(self.metrics['response_times']) / len(self.metrics['response_times'])
        
        if response.status in [200, 201, 204]:
            if response.status == 204:
                return {"success": True}
            else:
                return await response.json()
        else:
            error_text = await response.text()
            raise Exception(f"HTTP {response.status}: {error_text}")
    
    async def store_trade_execution_optimized(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store trade execution with optimizations"""
        return await self._execute_request('POST', 'trade_executions', data=trade_data)
    
    async def store_portfolio_metrics_optimized(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store portfolio metrics with optimizations"""
        return await self._execute_request('POST', 'portfolio_metrics', data=metrics_data)
    
    async def query_with_timeout(self, table: str, params: Optional[Dict] = None, timeout: float = 5.0) -> List[Dict[str, Any]]:
        """Query with configurable timeout"""
        try:
            result = await asyncio.wait_for(
                self._execute_request('GET', table, params=params),
                timeout=timeout
            )
            return result if isinstance(result, list) else [result]
        except asyncio.TimeoutError:
            logger.warning(f"Query to {table} timed out after {timeout}s")
            return []
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        success_rate = (self.metrics['requests_successful'] / max(1, self.metrics['requests_total'])) * 100
        
        return {
            'total_requests': self.metrics['requests_total'],
            'successful_requests': self.metrics['requests_successful'],
            'success_rate_percent': success_rate,
            'average_response_time_ms': self.metrics['average_response_time'],
            'connection_pool_active': self.session is not None
        }

# Global instance for connection pooling
_optimized_service = None

async def get_optimized_supabase_service() -> OptimizedSupabaseService:
    """Get global optimized Supabase service instance"""
    global _optimized_service
    if _optimized_service is None:
        _optimized_service = OptimizedSupabaseService()
        await _optimized_service.connect()
    return _optimized_service

async def close_optimized_service():
    """Close global service"""
    global _optimized_service
    if _optimized_service:
        await _optimized_service.disconnect()
        _optimized_service = None
'''
        
        with open('optimized_supabase_service.py', 'w') as f:
            f.write(service_code)
        
        logger.info("✓ Optimized connection pool service created")
        return "optimized_supabase_service.py"
    
    async def run_performance_fixes(self):
        """Run all performance fixes"""
        logger.info("Running database performance fixes...")
        
        # Step 1: Benchmark current performance
        performance_analysis = await self.benchmark_database_operations()
        
        # Step 2: Create optimized configuration
        config = await self.create_optimized_connection_config()
        
        # Step 3: Create connection pool service
        service_file = await self.create_connection_pool_service()
        
        return {
            'performance_analysis': performance_analysis,
            'config_created': True,
            'service_created': service_file,
            'metrics': self.performance_metrics
        }

async def main():
    """Main function to fix database performance"""
    print("=" * 60)
    print("FIXING DATABASE PERFORMANCE ISSUES")
    print("=" * 60)
    
    try:
        fixer = DatabasePerformanceFixer()
        results = await fixer.run_performance_fixes()
        
        print("\n" + "=" * 60)
        print("PERFORMANCE ANALYSIS RESULTS")
        print("=" * 60)
        
        analysis = results['performance_analysis']
        print(f"Overall Health: {analysis['overall_health'].upper()}")
        print(f"Average Response Time: {analysis['average_response_time']:.1f}ms")
        
        if analysis['slowest_operation']:
            op = analysis['slowest_operation']
            print(f"Slowest Operation: {op['operation']} ({op['time_ms']:.1f}ms)")
        
        if analysis['failed_operations']:
            print(f"Failed Operations: {len(analysis['failed_operations'])}")
            for failed_op in analysis['failed_operations']:
                print(f"  ✗ {failed_op['operation']}: {failed_op.get('error', 'Unknown error')}")
        
        print("\nRecommendations:")
        for rec in analysis['recommendations']:
            print(f"  • {rec}")
        
        print(f"\nFiles Created:")
        print(f"  ✓ database_performance_config.json")
        print(f"  ✓ {results['service_created']}")
        
        # Determine success
        success = analysis['overall_health'] in ['good', 'fair'] and len(analysis['failed_operations']) <= 1
        
        if success:
            print("\n" + "=" * 60)
            print("✅ DATABASE PERFORMANCE FIXES APPLIED")
            print("Use the optimized service for better performance!")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("⚠️  PERFORMANCE ISSUES IDENTIFIED")
            print("Review the recommendations above.")
            print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Database performance fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    success = asyncio.run(main())
    exit(0 if success else 1)