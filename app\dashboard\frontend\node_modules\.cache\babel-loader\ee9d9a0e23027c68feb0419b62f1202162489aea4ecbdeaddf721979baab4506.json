{"ast": null, "code": "/**\n * Token Service\n * \n * This service handles token management for authentication.\n */\n\n// Token storage keys\nconst ACCESS_TOKEN_KEY = 'access_token';\nconst REFRESH_TOKEN_KEY = 'refresh_token';\nconst TOKEN_TYPE_KEY = 'token_type';\n\n/**\n * Save tokens to local storage\n * \n * @param accessToken Access token\n * @param refreshToken Refresh token\n * @param tokenType Token type (e.g., 'bearer')\n */\nexport const saveTokens = (accessToken, refreshToken, tokenType) => {\n  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);\n  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);\n  localStorage.setItem(TOKEN_TYPE_KEY, tokenType);\n};\n\n/**\n * Get access token from local storage\n * \n * @returns Access token or null if not found\n */\nexport const getAccessToken = () => {\n  return localStorage.getItem(ACCESS_TOKEN_KEY);\n};\n\n/**\n * Get refresh token from local storage\n * \n * @returns Refresh token or null if not found\n */\nexport const getRefreshToken = () => {\n  return localStorage.getItem(REFRESH_TOKEN_KEY);\n};\n\n/**\n * Get token type from local storage\n * \n * @returns Token type or null if not found\n */\nexport const getTokenType = () => {\n  return localStorage.getItem(TOKEN_TYPE_KEY);\n};\n\n/**\n * Clear all tokens from local storage\n */\nexport const clearTokens = () => {\n  localStorage.removeItem(ACCESS_TOKEN_KEY);\n  localStorage.removeItem(REFRESH_TOKEN_KEY);\n  localStorage.removeItem(TOKEN_TYPE_KEY);\n};\n\n/**\n * Check if the user is authenticated\n * \n * @returns True if the user is authenticated, false otherwise\n */\nexport const isAuthenticated = () => {\n  return !!getAccessToken();\n};\n\n/**\n * Parse JWT token\n * \n * @param token JWT token\n * @returns Parsed token payload or null if invalid\n */\nexport const parseJwt = token => {\n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error('Error parsing JWT token:', error);\n    return null;\n  }\n};\n\n/**\n * Check if the access token is expired\n * \n * @returns True if the token is expired, false otherwise\n */\nexport const isTokenExpired = () => {\n  const token = getAccessToken();\n  if (!token) return true;\n  try {\n    const payload = parseJwt(token);\n    if (!payload || !payload.exp) return true;\n\n    // exp is in seconds, Date.now() is in milliseconds\n    const expirationTime = payload.exp * 1000;\n    const currentTime = Date.now();\n\n    // Return true if the token is expired or will expire in the next 60 seconds\n    return expirationTime <= currentTime + 60000;\n  } catch (error) {\n    console.error('Error checking token expiration:', error);\n    return true;\n  }\n};\n\n/**\n * Check if the refresh token is expired\n * \n * @returns True if the token is expired, false otherwise\n */\nexport const isRefreshTokenExpired = () => {\n  const token = getRefreshToken();\n  if (!token) return true;\n  try {\n    const payload = parseJwt(token);\n    if (!payload || !payload.exp) return true;\n\n    // exp is in seconds, Date.now() is in milliseconds\n    const expirationTime = payload.exp * 1000;\n    const currentTime = Date.now();\n    return expirationTime <= currentTime;\n  } catch (error) {\n    console.error('Error checking refresh token expiration:', error);\n    return true;\n  }\n};\nconst tokenService = {\n  saveTokens,\n  getAccessToken,\n  getRefreshToken,\n  getTokenType,\n  clearTokens,\n  isAuthenticated,\n  parseJwt,\n  isTokenExpired,\n  isRefreshTokenExpired\n};\nexport default tokenService;", "map": {"version": 3, "names": ["ACCESS_TOKEN_KEY", "REFRESH_TOKEN_KEY", "TOKEN_TYPE_KEY", "saveTokens", "accessToken", "refreshToken", "tokenType", "localStorage", "setItem", "getAccessToken", "getItem", "getRefreshToken", "getTokenType", "clearTokens", "removeItem", "isAuthenticated", "parseJwt", "token", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "toString", "slice", "join", "JSON", "parse", "error", "console", "isTokenExpired", "payload", "exp", "expirationTime", "currentTime", "Date", "now", "isRefreshTokenExpired", "tokenService"], "sources": ["/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/tokenService.ts"], "sourcesContent": ["/**\n * Token Service\n * \n * This service handles token management for authentication.\n */\n\n// Token storage keys\nconst ACCESS_TOKEN_KEY = 'access_token';\nconst REFRESH_TOKEN_KEY = 'refresh_token';\nconst TOKEN_TYPE_KEY = 'token_type';\n\n/**\n * Save tokens to local storage\n * \n * @param accessToken Access token\n * @param refreshToken Refresh token\n * @param tokenType Token type (e.g., 'bearer')\n */\nexport const saveTokens = (accessToken: string, refreshToken: string, tokenType: string): void => {\n  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);\n  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);\n  localStorage.setItem(TOKEN_TYPE_KEY, tokenType);\n};\n\n/**\n * Get access token from local storage\n * \n * @returns Access token or null if not found\n */\nexport const getAccessToken = (): string | null => {\n  return localStorage.getItem(ACCESS_TOKEN_KEY);\n};\n\n/**\n * Get refresh token from local storage\n * \n * @returns Refresh token or null if not found\n */\nexport const getRefreshToken = (): string | null => {\n  return localStorage.getItem(REFRESH_TOKEN_KEY);\n};\n\n/**\n * Get token type from local storage\n * \n * @returns Token type or null if not found\n */\nexport const getTokenType = (): string | null => {\n  return localStorage.getItem(TOKEN_TYPE_KEY);\n};\n\n/**\n * Clear all tokens from local storage\n */\nexport const clearTokens = (): void => {\n  localStorage.removeItem(ACCESS_TOKEN_KEY);\n  localStorage.removeItem(REFRESH_TOKEN_KEY);\n  localStorage.removeItem(TOKEN_TYPE_KEY);\n};\n\n/**\n * Check if the user is authenticated\n * \n * @returns True if the user is authenticated, false otherwise\n */\nexport const isAuthenticated = (): boolean => {\n  return !!getAccessToken();\n};\n\n/**\n * Parse JWT token\n * \n * @param token JWT token\n * @returns Parsed token payload or null if invalid\n */\nexport const parseJwt = (token: string): any => {\n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(\n      atob(base64)\n        .split('')\n        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\n        .join('')\n    );\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error('Error parsing JWT token:', error);\n    return null;\n  }\n};\n\n/**\n * Check if the access token is expired\n * \n * @returns True if the token is expired, false otherwise\n */\nexport const isTokenExpired = (): boolean => {\n  const token = getAccessToken();\n  if (!token) return true;\n\n  try {\n    const payload = parseJwt(token);\n    if (!payload || !payload.exp) return true;\n\n    // exp is in seconds, Date.now() is in milliseconds\n    const expirationTime = payload.exp * 1000;\n    const currentTime = Date.now();\n\n    // Return true if the token is expired or will expire in the next 60 seconds\n    return expirationTime <= currentTime + 60000;\n  } catch (error) {\n    console.error('Error checking token expiration:', error);\n    return true;\n  }\n};\n\n/**\n * Check if the refresh token is expired\n * \n * @returns True if the token is expired, false otherwise\n */\nexport const isRefreshTokenExpired = (): boolean => {\n  const token = getRefreshToken();\n  if (!token) return true;\n\n  try {\n    const payload = parseJwt(token);\n    if (!payload || !payload.exp) return true;\n\n    // exp is in seconds, Date.now() is in milliseconds\n    const expirationTime = payload.exp * 1000;\n    const currentTime = Date.now();\n\n    return expirationTime <= currentTime;\n  } catch (error) {\n    console.error('Error checking refresh token expiration:', error);\n    return true;\n  }\n};\n\nconst tokenService = {\n  saveTokens,\n  getAccessToken,\n  getRefreshToken,\n  getTokenType,\n  clearTokens,\n  isAuthenticated,\n  parseJwt,\n  isTokenExpired,\n  isRefreshTokenExpired,\n};\n\nexport default tokenService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAMA,gBAAgB,GAAG,cAAc;AACvC,MAAMC,iBAAiB,GAAG,eAAe;AACzC,MAAMC,cAAc,GAAG,YAAY;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGA,CAACC,WAAmB,EAAEC,YAAoB,EAAEC,SAAiB,KAAW;EAChGC,YAAY,CAACC,OAAO,CAACR,gBAAgB,EAAEI,WAAW,CAAC;EACnDG,YAAY,CAACC,OAAO,CAACP,iBAAiB,EAAEI,YAAY,CAAC;EACrDE,YAAY,CAACC,OAAO,CAACN,cAAc,EAAEI,SAAS,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,cAAc,GAAGA,CAAA,KAAqB;EACjD,OAAOF,YAAY,CAACG,OAAO,CAACV,gBAAgB,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,eAAe,GAAGA,CAAA,KAAqB;EAClD,OAAOJ,YAAY,CAACG,OAAO,CAACT,iBAAiB,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,YAAY,GAAGA,CAAA,KAAqB;EAC/C,OAAOL,YAAY,CAACG,OAAO,CAACR,cAAc,CAAC;AAC7C,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,WAAW,GAAGA,CAAA,KAAY;EACrCN,YAAY,CAACO,UAAU,CAACd,gBAAgB,CAAC;EACzCO,YAAY,CAACO,UAAU,CAACb,iBAAiB,CAAC;EAC1CM,YAAY,CAACO,UAAU,CAACZ,cAAc,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,eAAe,GAAGA,CAAA,KAAe;EAC5C,OAAO,CAAC,CAACN,cAAc,CAAC,CAAC;AAC3B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,QAAQ,GAAIC,KAAa,IAAU;EAC9C,IAAI;IACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CACpCC,IAAI,CAACJ,MAAM,CAAC,CACTD,KAAK,CAAC,EAAE,CAAC,CACTM,GAAG,CAAEC,CAAC,IAAK,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACjEC,IAAI,CAAC,EAAE,CACZ,CAAC;IACD,OAAOC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;EAChC,CAAC,CAAC,OAAOW,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAAA,KAAe;EAC3C,MAAMlB,KAAK,GAAGR,cAAc,CAAC,CAAC;EAC9B,IAAI,CAACQ,KAAK,EAAE,OAAO,IAAI;EAEvB,IAAI;IACF,MAAMmB,OAAO,GAAGpB,QAAQ,CAACC,KAAK,CAAC;IAC/B,IAAI,CAACmB,OAAO,IAAI,CAACA,OAAO,CAACC,GAAG,EAAE,OAAO,IAAI;;IAEzC;IACA,MAAMC,cAAc,GAAGF,OAAO,CAACC,GAAG,GAAG,IAAI;IACzC,MAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;IAE9B;IACA,OAAOH,cAAc,IAAIC,WAAW,GAAG,KAAK;EAC9C,CAAC,CAAC,OAAON,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,qBAAqB,GAAGA,CAAA,KAAe;EAClD,MAAMzB,KAAK,GAAGN,eAAe,CAAC,CAAC;EAC/B,IAAI,CAACM,KAAK,EAAE,OAAO,IAAI;EAEvB,IAAI;IACF,MAAMmB,OAAO,GAAGpB,QAAQ,CAACC,KAAK,CAAC;IAC/B,IAAI,CAACmB,OAAO,IAAI,CAACA,OAAO,CAACC,GAAG,EAAE,OAAO,IAAI;;IAEzC;IACA,MAAMC,cAAc,GAAGF,OAAO,CAACC,GAAG,GAAG,IAAI;IACzC,MAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE9B,OAAOH,cAAc,IAAIC,WAAW;EACtC,CAAC,CAAC,OAAON,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAChE,OAAO,IAAI;EACb;AACF,CAAC;AAED,MAAMU,YAAY,GAAG;EACnBxC,UAAU;EACVM,cAAc;EACdE,eAAe;EACfC,YAAY;EACZC,WAAW;EACXE,eAAe;EACfC,QAAQ;EACRmB,cAAc;EACdO;AACF,CAAC;AAED,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}