import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  But<PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  Divider, 
  FormControlLabel, 
  Grid, 
  Switch, 
  TextField, 
  Typography,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import axios from 'axios';

const MLControls = () => {
  const [mlStatus, setMlStatus] = useState({});
  const [modelInfo, setModelInfo] = useState({});
  const [loading, setLoading] = useState(false);
  const [trainingLoading, setTrainingLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [trainingParams, setTrainingParams] = useState({
    symbol: '',
    timeframe: '',
    lookback_days: 90,
    window_size: 10,
    total_timesteps: 100000,
    optimize_hyperparameters: false,
    optimization_trials: 10
  });
  
  // Fetch ML status
  const fetchMlStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/ml/status');
      setMlStatus(response.data);
      
      // Update training params with current values
      setTrainingParams(prev => ({
        ...prev,
        symbol: response.data.trading_symbol || prev.symbol,
        timeframe: response.data.timeframe || prev.timeframe,
        lookback_days: response.data.lookback_days || prev.lookback_days,
        window_size: response.data.window_size || prev.window_size,
        total_timesteps: response.data.training_timesteps || prev.total_timesteps,
        optimize_hyperparameters: response.data.hyperparameter_optimization || prev.optimize_hyperparameters,
        optimization_trials: response.data.optimization_trials || prev.optimization_trials
      }));
    } catch (error) {
      console.error('Error fetching ML status:', error);
      setSnackbar({
        open: true,
        message: `Error fetching ML status: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch model info
  const fetchModelInfo = async () => {
    try {
      const response = await axios.get('/api/ml/info');
      if (response.data.success) {
        setModelInfo(response.data.model_info);
      }
    } catch (error) {
      console.error('Error fetching model info:', error);
      setSnackbar({
        open: true,
        message: `Error fetching model info: ${error.message}`,
        severity: 'error'
      });
    }
  };
  
  // Toggle ML weight optimization
  const toggleMlEnabled = async () => {
    try {
      setLoading(true);
      const response = await axios.post('/api/ml/toggle', { 
        enabled: !mlStatus.enabled 
      });
      
      if (response.data.success) {
        setSnackbar({
          open: true,
          message: `ML weight optimization ${response.data.enabled ? 'enabled' : 'disabled'}`,
          severity: 'success'
        });
        fetchMlStatus();
      } else {
        setSnackbar({
          open: true,
          message: `Error: ${response.data.error}`,
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error toggling ML enabled:', error);
      setSnackbar({
        open: true,
        message: `Error toggling ML enabled: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Train model
  const trainModel = async () => {
    setTrainingLoading(true);
    try {
      const response = await axios.post('/api/ml/train', trainingParams);
      if (response.data.success) {
        setSnackbar({
          open: true,
          message: 'Model training started successfully!',
          severity: 'success'
        });
        fetchModelInfo();
      } else {
        setSnackbar({
          open: true,
          message: `Training failed: ${response.data.error}`,
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error training model:', error);
      setSnackbar({
        open: true,
        message: `Error: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setTrainingLoading(false);
    }
  };
  
  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setTrainingParams(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
    }));
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };
  
  // Load data on component mount
  useEffect(() => {
    fetchMlStatus();
    fetchModelInfo();
    
    // Refresh data every 60 seconds
    const interval = setInterval(() => {
      fetchMlStatus();
      fetchModelInfo();
    }, 60000);
    
    return () => clearInterval(interval);
  }, []);
  
  if (loading && Object.keys(mlStatus).length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box>
      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
      
      <Card>
        <CardHeader 
          title="ML Weight Optimization" 
          subheader="Configure and control ML-based strategy weight optimization"
        />
        <Divider />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={mlStatus.enabled || false}
                    onChange={toggleMlEnabled}
                    color="primary"
                    disabled={loading}
                  />
                }
                label="Enable ML Weight Optimization"
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h6">Model Information</Typography>
              <Typography variant="body2">
                Model Path: {mlStatus.model_path || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Last Training: {modelInfo.last_training_time || 'Never'}
              </Typography>
              <Typography variant="body2">
                Training Interval: {mlStatus.training_interval_hours || 0} hours
              </Typography>
              <Typography variant="body2">
                Model Performance: {modelInfo.performance_metrics ? 
                  `${modelInfo.performance_metrics.sharpe_ratio?.toFixed(2)} (Sharpe), ${modelInfo.performance_metrics.sortino_ratio?.toFixed(2)} (Sortino)` : 
                  'N/A'}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Divider />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h6">Training Parameters</Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Symbol"
                name="symbol"
                value={trainingParams.symbol}
                onChange={handleInputChange}
                disabled={trainingLoading}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Timeframe"
                name="timeframe"
                value={trainingParams.timeframe}
                onChange={handleInputChange}
                disabled={trainingLoading}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Lookback Days"
                name="lookback_days"
                type="number"
                value={trainingParams.lookback_days}
                onChange={handleInputChange}
                disabled={trainingLoading}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Window Size"
                name="window_size"
                type="number"
                value={trainingParams.window_size}
                onChange={handleInputChange}
                disabled={trainingLoading}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Training Timesteps"
                name="total_timesteps"
                type="number"
                value={trainingParams.total_timesteps}
                onChange={handleInputChange}
                disabled={trainingLoading}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={trainingParams.optimize_hyperparameters}
                    onChange={handleInputChange}
                    name="optimize_hyperparameters"
                    color="primary"
                    disabled={trainingLoading}
                  />
                }
                label="Optimize Hyperparameters"
              />
            </Grid>
            
            {trainingParams.optimize_hyperparameters && (
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Optimization Trials"
                  name="optimization_trials"
                  type="number"
                  value={trainingParams.optimization_trials}
                  onChange={handleInputChange}
                  disabled={trainingLoading}
                />
              </Grid>
            )}
            
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                onClick={trainModel}
                disabled={trainingLoading}
                startIcon={trainingLoading ? <CircularProgress size={20} /> : null}
              >
                {trainingLoading ? 'Training...' : 'Train Model'}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MLControls;
