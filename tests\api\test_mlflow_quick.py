#!/usr/bin/env python3
"""
Quick MLflow connectivity and functionality test
Tests connection to localhost:5000 and basic operations
"""

import mlflow
import mlflow.tracking
import time
import sys

def test_mlflow_quick():
    """Test MLflow connectivity and basic functionality"""
    
    # Set tracking URI
    mlflow.set_tracking_uri("http://localhost:5000")
    
    try:
        # Test 1: Check server connectivity
        print("🔍 Testing MLflow server connectivity...")
        client = mlflow.tracking.MlflowClient()
        experiments = client.search_experiments()
        print(f"✅ Connected to MLflow server. Found {len(experiments)} experiments.")
        
        # Test 2: Create experiment
        print("\n🧪 Creating test experiment...")
        experiment_name = f"quick_test_{int(time.time())}"
        experiment_id = mlflow.create_experiment(experiment_name)
        print(f"✅ Created experiment: {experiment_name} (ID: {experiment_id})")
        
        # Test 3: Start run and log metrics
        print("\n📊 Starting run and logging metrics...")
        with mlflow.start_run(experiment_id=experiment_id):
            # Log some basic metrics
            mlflow.log_metric("test_accuracy", 0.95)
            mlflow.log_metric("test_loss", 0.05)
            mlflow.log_param("model_type", "test_model")
            mlflow.log_param("quick_test", True)
            
            run_id = mlflow.active_run().info.run_id
            print(f"✅ Logged metrics and parameters (Run ID: {run_id})")
        
        # Test 4: List experiments to verify
        print("\n📋 Listing all experiments...")
        experiments = client.search_experiments()
        for exp in experiments:
            print(f"  - {exp.name} (ID: {exp.experiment_id})")
        
        print(f"\n🎉 All tests passed! MLflow is working correctly.")
        print(f"🌐 MLflow UI available at: http://localhost:5000")
        return True
        
    except Exception as e:
        print(f"❌ MLflow test failed: {str(e)}")
        print("\n💡 Troubleshooting tips:")
        print("  1. Make sure MLflow server is running: mlflow server --host 0.0.0.0 --port 5000")
        print("  2. Check if port 5000 is accessible")
        print("  3. Verify MLflow installation: pip show mlflow")
        return False

if __name__ == "__main__":
    print("🚀 Starting MLflow Quick Test...")
    success = test_mlflow_quick()
    sys.exit(0 if success else 1)