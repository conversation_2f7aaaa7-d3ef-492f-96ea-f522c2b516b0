#!/usr/bin/env node

/**
 * Simple standalone test for Redis MCP Server
 */

import { spawn } from 'child_process';

async function testMCP() {
  console.log('🧪 Testing Redis Trading MCP Server...\n');

  // Start MCP server
  const mcpProcess = spawn('node', ['dist/index.js'], {
    stdio: ['pipe', 'pipe', 'inherit'],
    env: {
      ...process.env,
      REDIS_URL: 'redis://localhost:6379',
      REDIS_DB: '0'
    }
  });

  let serverReady = false;
  let testResults = [];

  // Wait for server to start
  await new Promise(resolve => {
    mcpProcess.stderr.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Redis Trading MCP server running')) {
        serverReady = true;
        console.log('✅ MCP Server started');
        resolve();
      }
    });
    
    setTimeout(() => {
      if (!serverReady) {
        console.log('⚠️  Server startup timeout, proceeding with tests');
        resolve();
      }
    }, 3000);
  });

  // Test 1: List tools
  console.log('\n🔧 Testing list_tools...');
  try {
    const listRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list'
    };

    const response = await sendMCPRequest(mcpProcess, listRequest);
    if (response && response.result && response.result.tools) {
      console.log(`✅ Found ${response.result.tools.length} tools`);
      testResults.push(true);
    } else {
      console.log('❌ Invalid response format');
      testResults.push(false);
    }
  } catch (error) {
    console.log(`❌ List tools failed: ${error.message}`);
    testResults.push(false);
  }

  // Test 2: Cache stats
  console.log('\n📊 Testing cache_stats...');
  try {
    const statsRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'get_cache_stats',
        arguments: {}
      }
    };

    const response = await sendMCPRequest(mcpProcess, statsRequest);
    if (response && response.result) {
      console.log('✅ Cache stats retrieved');
      testResults.push(true);
    } else {
      console.log('❌ Cache stats failed');
      testResults.push(false);
    }
  } catch (error) {
    console.log(`❌ Cache stats failed: ${error.message}`);
    testResults.push(false);
  }

  // Test 3: Strategy weights caching
  console.log('\n💾 Testing strategy weights...');
  try {
    const cacheRequest = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'cache_strategy_weights',
        arguments: {
          weights: {
            'GridStrategy': { weight: 0.4, confidence: 0.8 },
            'TechnicalAnalysisStrategy': { weight: 0.35, confidence: 0.75 },
            'TrendFollowingStrategy': { weight: 0.25, confidence: 0.7 }
          },
          ttl: 60
        }
      }
    };

    const cacheResponse = await sendMCPRequest(mcpProcess, cacheRequest);
    
    // Try to retrieve
    const getRequest = {
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'get_strategy_weights',
        arguments: {}
      }
    };

    const getResponse = await sendMCPRequest(mcpProcess, getRequest);
    
    if (cacheResponse && getResponse) {
      console.log('✅ Strategy weights cache/retrieve works');
      testResults.push(true);
    } else {
      console.log('❌ Strategy weights failed');
      testResults.push(false);
    }
  } catch (error) {
    console.log(`❌ Strategy weights failed: ${error.message}`);
    testResults.push(false);
  }

  // Cleanup
  mcpProcess.kill();

  // Summary
  const passed = testResults.filter(r => r).length;
  const total = testResults.length;
  
  console.log('\n' + '='.repeat(50));
  if (passed === total) {
    console.log(`🎉 All tests passed! (${passed}/${total})`);
    console.log('✅ Redis Trading MCP Server is working correctly');
  } else {
    console.log(`⚠️  Some tests failed (${passed}/${total})`);
  }

  process.exit(passed === total ? 0 : 1);
}

async function sendMCPRequest(process, request) {
  return new Promise((resolve, reject) => {
    let response = '';
    const timeout = setTimeout(() => {
      reject(new Error('Request timeout'));
    }, 5000);

    const onData = (data) => {
      response += data.toString();
      try {
        const lines = response.split('\n').filter(line => line.trim());
        for (const line of lines) {
          try {
            const jsonResponse = JSON.parse(line);
            if (jsonResponse.id === request.id) {
              clearTimeout(timeout);
              process.stdout.off('data', onData);
              resolve(jsonResponse);
              return;
            }
          } catch (e) {
            // Continue to next line
          }
        }
      } catch (e) {
        // Continue collecting data
      }
    };

    process.stdout.on('data', onData);
    process.stdin.write(JSON.stringify(request) + '\n');
  });
}

// Handle interrupts
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  process.exit(1);
});

testMCP().catch(console.error);