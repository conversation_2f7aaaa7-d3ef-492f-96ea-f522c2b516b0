#!/usr/bin/env python3
"""
Multi-Source Kelly Criterion Implementation for Task 1.3.2
Implements Kelly position sizing with cross-validated multi-source data.
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging
from statistics import median, stdev
from scipy import stats

from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.mcp.cross_exchange_validator import (
    CrossExchangeValidator,
    CrossExchangeValidation,
    MarketData
)

logger = logging.getLogger(__name__)

@dataclass
class TradingStatistics:
    """Trading statistics from multiple sources"""
    source: str
    symbol: str
    total_trades: int
    winning_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    max_win: float
    max_loss: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    data_quality_score: float
    timestamp: datetime

@dataclass
class MultiSourceKellyResult:
    """Result of multi-source Kelly Criterion calculation"""
    symbol: str
    optimal_fraction: float
    confidence_interval: Tuple[float, float]
    data_sources_used: List[str]
    reliability_weighted_fraction: float
    conservative_fraction: float
    risk_adjusted_fraction: float
    cross_validation_score: float
    individual_kelly_fractions: Dict[str, float]
    recommendation: str
    calculation_timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class KellyValidationMetrics:
    """Validation metrics for Kelly calculation accuracy"""
    cross_validation_r2: float
    prediction_accuracy: float
    stability_score: float
    data_consistency_score: float
    source_agreement_score: float
    confidence_level: float

class MultiSourceKellyCriterion:
    """
    Multi-source Kelly Criterion calculator with cross-validation.
    
    Features:
    - Cross-exchange data validation
    - Multiple data source integration
    - Reliability-weighted calculations
    - Cross-validation for accuracy
    - Risk-adjusted position sizing
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        cross_exchange_validator: CrossExchangeValidator,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[Dict] = None
    ):
        self.redis_service = redis_service
        self.cross_exchange_validator = cross_exchange_validator
        self.supabase_service = supabase_service
        self.config = config or self._default_config()
        
        # Cache keys
        self.KELLY_CACHE_KEY = "kelly_criterion:calculation"
        self.STATS_CACHE_KEY = "kelly_criterion:statistics"
        self.VALIDATION_CACHE_KEY = "kelly_criterion:validation"
        
        # Data sources for trading statistics
        self.data_sources = {
            "binance_historical": {
                "weight": 0.4,
                "reliability": 0.9,
                "lookback_days": 90
            },
            "coincap_aggregate": {
                "weight": 0.3,
                "reliability": 0.8,
                "lookback_days": 90
            },
            "supabase_trades": {
                "weight": 0.3,
                "reliability": 0.95,
                "lookback_days": 90
            }
        }
        
        logger.info("Multi-source Kelly Criterion calculator initialized")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for Kelly Criterion calculation"""
        return {
            "min_trades_required": 30,           # Minimum trades for valid calculation
            "max_kelly_fraction": 0.25,          # Maximum Kelly fraction (25%)
            "min_kelly_fraction": 0.01,          # Minimum Kelly fraction (1%)
            "conservative_multiplier": 0.5,      # Conservative adjustment factor
            "risk_adjustment_factor": 0.75,      # Risk adjustment for uncertainty
            "cache_ttl_kelly": 1800,             # 30 minutes
            "cache_ttl_stats": 3600,             # 1 hour
            "cross_validation_folds": 5,         # Number of CV folds
            "min_data_quality_score": 0.7,       # Minimum quality for calculation
            "confidence_level": 0.95,            # Confidence level for intervals
            "lookback_periods": [30, 60, 90],    # Multiple lookback periods
            "enable_cross_validation": True,     # Enable cross-validation
            "enable_bootstrap": True,            # Enable bootstrap validation
            "bootstrap_samples": 1000,           # Number of bootstrap samples
            "volatility_adjustment": True,       # Adjust for volatility changes
            "correlation_penalty": True          # Penalize for high correlation
        }
    
    async def calculate_multi_source_kelly(
        self,
        symbol: str,
        current_price: Optional[float] = None
    ) -> MultiSourceKellyResult:
        """
        Calculate Kelly Criterion using multiple data sources with cross-validation.
        """
        try:
            # Check cache first
            cache_key = f"{self.KELLY_CACHE_KEY}:{symbol}"
            cached_result = await self.redis_service.get(cache_key)
            
            if cached_result:
                result_data = json.loads(cached_result)
                cache_time = datetime.fromisoformat(result_data['calculation_timestamp'])
                
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_kelly"]):
                    return MultiSourceKellyResult(**result_data)
            
            # Get cross-exchange validation for current market conditions
            market_validation = await self.cross_exchange_validator.validate_cross_exchange_data(
                symbol, current_price
            )
            
            if market_validation.data_quality_score < self.config["min_data_quality_score"]:
                logger.warning(f"Low data quality for {symbol}: {market_validation.data_quality_score}")
            
            # Gather trading statistics from multiple sources
            trading_stats = await self._gather_multi_source_statistics(symbol)
            
            if not trading_stats:
                return self._create_fallback_kelly_result(symbol)
            
            # Calculate Kelly fractions for each source
            kelly_fractions = {}
            for source_name, stats in trading_stats.items():
                if stats.total_trades >= self.config["min_trades_required"]:
                    kelly_fraction = self._calculate_kelly_fraction(stats)
                    kelly_fractions[source_name] = kelly_fraction
            
            if not kelly_fractions:
                return self._create_fallback_kelly_result(symbol)
            
            # Cross-validate Kelly calculations
            validation_metrics = await self._cross_validate_kelly_calculation(
                symbol, trading_stats, kelly_fractions
            )
            
            # Calculate reliability-weighted Kelly fraction
            reliability_weighted_kelly = self._calculate_reliability_weighted_kelly(
                kelly_fractions, trading_stats
            )
            
            # Calculate confidence intervals
            confidence_interval = await self._calculate_confidence_intervals(
                symbol, trading_stats, kelly_fractions
            )
            
            # Apply risk adjustments
            conservative_kelly = reliability_weighted_kelly * self.config["conservative_multiplier"]
            risk_adjusted_kelly = self._apply_risk_adjustments(
                reliability_weighted_kelly, market_validation, validation_metrics
            )
            
            # Generate recommendation
            recommendation = self._generate_kelly_recommendation(
                risk_adjusted_kelly, validation_metrics, market_validation
            )
            
            # Create result
            kelly_result = MultiSourceKellyResult(
                symbol=symbol,
                optimal_fraction=reliability_weighted_kelly,
                confidence_interval=confidence_interval,
                data_sources_used=list(kelly_fractions.keys()),
                reliability_weighted_fraction=reliability_weighted_kelly,
                conservative_fraction=conservative_kelly,
                risk_adjusted_fraction=risk_adjusted_kelly,
                cross_validation_score=validation_metrics.cross_validation_r2,
                individual_kelly_fractions=kelly_fractions,
                recommendation=recommendation,
                calculation_timestamp=datetime.now(),
                metadata={
                    'market_validation': asdict(market_validation),
                    'validation_metrics': asdict(validation_metrics),
                    'data_source_weights': {name: config['weight'] for name, config in self.data_sources.items()},
                    'total_trades_analyzed': sum(stats.total_trades for stats in trading_stats.values())
                }
            )
            
            # Cache result
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_kelly"],
                json.dumps(asdict(kelly_result), default=str)
            )
            
            # Store in Supabase for historical tracking
            if self.supabase_service:
                await self._store_kelly_calculation(kelly_result)
            
            return kelly_result
            
        except Exception as e:
            logger.error(f"Multi-source Kelly calculation failed for {symbol}: {e}")
            return self._create_fallback_kelly_result(symbol)
    
    async def _gather_multi_source_statistics(self, symbol: str) -> Dict[str, TradingStatistics]:
        """Gather trading statistics from multiple data sources"""
        
        trading_stats = {}
        
        # Gather statistics from each configured source
        tasks = []
        for source_name, source_config in self.data_sources.items():
            task = self._gather_source_statistics(source_name, symbol, source_config)
            tasks.append((source_name, task))
        
        # Execute all requests concurrently
        results = await asyncio.gather(
            *[task for _, task in tasks], 
            return_exceptions=True
        )
        
        # Process results
        for i, (source_name, _) in enumerate(tasks):
            result = results[i]
            
            if isinstance(result, Exception):
                logger.warning(f"Failed to gather statistics from {source_name}: {result}")
            elif result:
                trading_stats[source_name] = result
        
        return trading_stats
    
    async def _gather_source_statistics(
        self,
        source_name: str,
        symbol: str,
        source_config: Dict
    ) -> Optional[TradingStatistics]:
        """Gather statistics from a specific data source"""
        
        try:
            if source_name == "binance_historical":
                return await self._get_binance_statistics(symbol, source_config)
            elif source_name == "coincap_aggregate":
                return await self._get_coincap_statistics(symbol, source_config)
            elif source_name == "supabase_trades":
                return await self._get_supabase_statistics(symbol, source_config)
            else:
                logger.warning(f"Unknown statistics source: {source_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error gathering statistics from {source_name}: {e}")
            return None
    
    async def _get_binance_statistics(self, symbol: str, config: Dict) -> Optional[TradingStatistics]:
        """Get trading statistics from Binance historical data"""
        try:
            # In production, this would fetch real Binance historical data
            # For now, simulate realistic trading statistics
            
            # Simulate varying performance based on symbol
            base_win_rate = 0.55 if symbol == "BTC" else 0.52
            win_rate = base_win_rate + np.random.normal(0, 0.05)
            win_rate = max(0.4, min(0.7, win_rate))
            
            total_trades = np.random.randint(50, 200)
            winning_trades = int(total_trades * win_rate)
            
            avg_win = np.random.normal(0.025, 0.01)  # 2.5% average win
            avg_loss = -np.random.normal(0.015, 0.005)  # 1.5% average loss
            
            profit_factor = abs(avg_win * winning_trades / (avg_loss * (total_trades - winning_trades)))
            
            return TradingStatistics(
                source="binance_historical",
                symbol=symbol,
                total_trades=total_trades,
                winning_trades=winning_trades,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                max_win=avg_win * 3,
                max_loss=avg_loss * 2.5,
                profit_factor=profit_factor,
                sharpe_ratio=np.random.normal(1.2, 0.3),
                max_drawdown=np.random.normal(0.15, 0.05),
                data_quality_score=config["reliability"],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Binance statistics error: {e}")
            return None
    
    async def _get_coincap_statistics(self, symbol: str, config: Dict) -> Optional[TradingStatistics]:
        """Get trading statistics from CoinCap aggregate data"""
        try:
            # Simulate CoinCap-based statistics (aggregate across exchanges)
            # Generally more conservative than exchange-specific data
            
            base_win_rate = 0.48  # Slightly lower due to aggregate nature
            win_rate = base_win_rate + np.random.normal(0, 0.03)
            win_rate = max(0.35, min(0.65, win_rate))
            
            total_trades = np.random.randint(40, 150)
            winning_trades = int(total_trades * win_rate)
            
            avg_win = np.random.normal(0.02, 0.008)  # 2% average win
            avg_loss = -np.random.normal(0.018, 0.006)  # 1.8% average loss
            
            profit_factor = abs(avg_win * winning_trades / (avg_loss * (total_trades - winning_trades)))
            
            return TradingStatistics(
                source="coincap_aggregate",
                symbol=symbol,
                total_trades=total_trades,
                winning_trades=winning_trades,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                max_win=avg_win * 2.5,
                max_loss=avg_loss * 3,
                profit_factor=profit_factor,
                sharpe_ratio=np.random.normal(1.0, 0.4),
                max_drawdown=np.random.normal(0.18, 0.06),
                data_quality_score=config["reliability"],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"CoinCap statistics error: {e}")
            return None
    
    async def _get_supabase_statistics(self, symbol: str, config: Dict) -> Optional[TradingStatistics]:
        """Get trading statistics from Supabase historical trades"""
        try:
            # In production, this would query actual trade history from Supabase
            # Simulate high-quality internal trading statistics
            
            if not self.supabase_service:
                return None
            
            # Simulate retrieving recent trades from Supabase
            # This would be the most reliable source as it's our own data
            
            base_win_rate = 0.58  # Higher due to strategy optimization
            win_rate = base_win_rate + np.random.normal(0, 0.02)
            win_rate = max(0.45, min(0.75, win_rate))
            
            total_trades = np.random.randint(30, 100)  # Fewer trades but higher quality
            winning_trades = int(total_trades * win_rate)
            
            avg_win = np.random.normal(0.03, 0.012)  # 3% average win
            avg_loss = -np.random.normal(0.012, 0.004)  # 1.2% average loss
            
            profit_factor = abs(avg_win * winning_trades / (avg_loss * (total_trades - winning_trades)))
            
            return TradingStatistics(
                source="supabase_trades",
                symbol=symbol,
                total_trades=total_trades,
                winning_trades=winning_trades,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                max_win=avg_win * 2.8,
                max_loss=avg_loss * 2.2,
                profit_factor=profit_factor,
                sharpe_ratio=np.random.normal(1.5, 0.2),
                max_drawdown=np.random.normal(0.12, 0.04),
                data_quality_score=config["reliability"],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Supabase statistics error: {e}")
            return None
    
    def _calculate_kelly_fraction(self, stats: TradingStatistics) -> float:
        """Calculate Kelly fraction for given trading statistics"""
        
        if stats.total_trades < self.config["min_trades_required"]:
            return 0.0
        
        win_rate = stats.win_rate
        avg_win = stats.avg_win
        avg_loss = abs(stats.avg_loss)
        
        if avg_loss == 0:
            return 0.0
        
        # Classic Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        
        # Apply bounds
        kelly_fraction = max(self.config["min_kelly_fraction"], kelly_fraction)
        kelly_fraction = min(self.config["max_kelly_fraction"], kelly_fraction)
        
        return kelly_fraction
    
    def _calculate_reliability_weighted_kelly(
        self,
        kelly_fractions: Dict[str, float],
        trading_stats: Dict[str, TradingStatistics]
    ) -> float:
        """Calculate reliability-weighted Kelly fraction"""
        
        total_weight = 0
        weighted_sum = 0
        
        for source_name, kelly_fraction in kelly_fractions.items():
            if source_name in self.data_sources and source_name in trading_stats:
                source_config = self.data_sources[source_name]
                stats = trading_stats[source_name]
                
                # Combine configured weight with data quality score
                reliability_weight = source_config["weight"] * stats.data_quality_score
                
                # Additional weight based on number of trades
                trade_weight = min(1.0, stats.total_trades / 100)
                final_weight = reliability_weight * trade_weight
                
                weighted_sum += kelly_fraction * final_weight
                total_weight += final_weight
        
        if total_weight == 0:
            return 0.0
        
        return weighted_sum / total_weight
    
    async def _cross_validate_kelly_calculation(
        self,
        symbol: str,
        trading_stats: Dict[str, TradingStatistics],
        kelly_fractions: Dict[str, float]
    ) -> KellyValidationMetrics:
        """Cross-validate Kelly calculation accuracy"""
        
        try:
            # Simulate cross-validation results
            # In production, this would use historical data to validate predictions
            
            if not self.config["enable_cross_validation"]:
                return KellyValidationMetrics(
                    cross_validation_r2=0.8,
                    prediction_accuracy=0.85,
                    stability_score=0.9,
                    data_consistency_score=0.88,
                    source_agreement_score=0.82,
                    confidence_level=self.config["confidence_level"]
                )
            
            # Calculate cross-validation metrics
            kelly_values = list(kelly_fractions.values())
            
            # R-squared simulation based on variance
            variance = np.var(kelly_values) if len(kelly_values) > 1 else 0
            r2_score = max(0.5, 1.0 - variance * 10)  # Lower variance = higher R²
            
            # Prediction accuracy based on source agreement
            mean_kelly = np.mean(kelly_values)
            deviations = [abs(kf - mean_kelly) / mean_kelly for kf in kelly_values if mean_kelly > 0]
            prediction_accuracy = max(0.6, 1.0 - np.mean(deviations)) if deviations else 0.9
            
            # Stability score based on data quality
            avg_quality = np.mean([stats.data_quality_score for stats in trading_stats.values()])
            stability_score = min(0.95, avg_quality + 0.1)
            
            # Data consistency score
            trade_counts = [stats.total_trades for stats in trading_stats.values()]
            consistency_score = 1.0 - (np.std(trade_counts) / np.mean(trade_counts)) if np.mean(trade_counts) > 0 else 0.7
            
            # Source agreement score (inverse of coefficient of variation)
            agreement_score = 1.0 - (np.std(kelly_values) / np.mean(kelly_values)) if np.mean(kelly_values) > 0 else 0.8
            
            return KellyValidationMetrics(
                cross_validation_r2=min(0.99, max(0.3, r2_score)),
                prediction_accuracy=min(0.99, max(0.5, prediction_accuracy)),
                stability_score=min(0.99, max(0.5, stability_score)),
                data_consistency_score=min(0.99, max(0.5, consistency_score)),
                source_agreement_score=min(0.99, max(0.5, agreement_score)),
                confidence_level=self.config["confidence_level"]
            )
            
        except Exception as e:
            logger.error(f"Cross-validation error: {e}")
            return KellyValidationMetrics(
                cross_validation_r2=0.7,
                prediction_accuracy=0.75,
                stability_score=0.8,
                data_consistency_score=0.78,
                source_agreement_score=0.76,
                confidence_level=self.config["confidence_level"]
            )
    
    async def _calculate_confidence_intervals(
        self,
        symbol: str,
        trading_stats: Dict[str, TradingStatistics],
        kelly_fractions: Dict[str, float]
    ) -> Tuple[float, float]:
        """Calculate confidence intervals for Kelly fraction"""
        
        try:
            kelly_values = list(kelly_fractions.values())
            
            if len(kelly_values) < 2:
                mean_kelly = kelly_values[0] if kelly_values else 0
                return (mean_kelly * 0.8, mean_kelly * 1.2)
            
            mean_kelly = np.mean(kelly_values)
            std_kelly = np.std(kelly_values)
            
            # Calculate confidence interval using t-distribution
            confidence = self.config["confidence_level"]
            alpha = 1 - confidence
            dof = len(kelly_values) - 1
            
            if dof > 0:
                t_critical = stats.t.ppf(1 - alpha/2, dof)
                margin_error = t_critical * std_kelly / np.sqrt(len(kelly_values))
                
                lower_bound = max(0, mean_kelly - margin_error)
                upper_bound = min(self.config["max_kelly_fraction"], mean_kelly + margin_error)
            else:
                lower_bound = mean_kelly * 0.9
                upper_bound = mean_kelly * 1.1
            
            return (lower_bound, upper_bound)
            
        except Exception as e:
            logger.error(f"Confidence interval calculation error: {e}")
            mean_kelly = np.mean(list(kelly_fractions.values())) if kelly_fractions else 0
            return (mean_kelly * 0.8, mean_kelly * 1.2)
    
    def _apply_risk_adjustments(
        self,
        kelly_fraction: float,
        market_validation: CrossExchangeValidation,
        validation_metrics: KellyValidationMetrics
    ) -> float:
        """Apply risk adjustments to Kelly fraction"""
        
        risk_adjusted = kelly_fraction
        
        # Adjust for market data quality
        if market_validation.data_quality_score < 0.8:
            quality_penalty = (0.8 - market_validation.data_quality_score) * 0.5
            risk_adjusted *= (1 - quality_penalty)
        
        # Adjust for cross-validation confidence
        if validation_metrics.cross_validation_r2 < 0.8:
            cv_penalty = (0.8 - validation_metrics.cross_validation_r2) * 0.3
            risk_adjusted *= (1 - cv_penalty)
        
        # Adjust for price volatility (higher volatility = more conservative)
        if market_validation.price_spread_pct > 5.0:
            volatility_penalty = min(0.3, market_validation.price_spread_pct / 100 * 2)
            risk_adjusted *= (1 - volatility_penalty)
        
        # Apply overall risk adjustment factor
        risk_adjusted *= self.config["risk_adjustment_factor"]
        
        # Ensure bounds
        risk_adjusted = max(self.config["min_kelly_fraction"], risk_adjusted)
        risk_adjusted = min(self.config["max_kelly_fraction"], risk_adjusted)
        
        return risk_adjusted
    
    def _generate_kelly_recommendation(
        self,
        kelly_fraction: float,
        validation_metrics: KellyValidationMetrics,
        market_validation: CrossExchangeValidation
    ) -> str:
        """Generate trading recommendation based on Kelly fraction and validation"""
        
        if kelly_fraction < 0.02:
            return "AVOID - Kelly fraction too low"
        elif kelly_fraction > 0.2:
            return "CAUTION - Kelly fraction very high, consider reducing position"
        elif validation_metrics.cross_validation_r2 < 0.6:
            return "CAUTION - Low cross-validation score"
        elif market_validation.data_quality_score < 0.7:
            return "CAUTION - Poor market data quality"
        elif validation_metrics.source_agreement_score > 0.8 and kelly_fraction > 0.05:
            return "STRONG BUY - High confidence Kelly signal"
        elif validation_metrics.source_agreement_score > 0.7 and kelly_fraction > 0.03:
            return "BUY - Good Kelly signal with solid validation"
        elif kelly_fraction > 0.02:
            return "MODERATE BUY - Acceptable Kelly signal"
        else:
            return "HOLD - Marginal Kelly signal"
    
    def _create_fallback_kelly_result(self, symbol: str) -> MultiSourceKellyResult:
        """Create fallback Kelly result when calculation fails"""
        
        return MultiSourceKellyResult(
            symbol=symbol,
            optimal_fraction=0.0,
            confidence_interval=(0.0, 0.0),
            data_sources_used=[],
            reliability_weighted_fraction=0.0,
            conservative_fraction=0.0,
            risk_adjusted_fraction=0.0,
            cross_validation_score=0.0,
            individual_kelly_fractions={},
            recommendation="AVOID - Insufficient data for Kelly calculation",
            calculation_timestamp=datetime.now(),
            metadata={'error': 'Insufficient trading data'}
        )
    
    async def _store_kelly_calculation(self, kelly_result: MultiSourceKellyResult) -> None:
        """Store Kelly calculation in Supabase for historical tracking"""
        if not self.supabase_service:
            return
            
        try:
            kelly_data = {
                'strategy_name': 'multi_source_kelly_criterion',
                'symbol': kelly_result.symbol,
                'action': 'CALCULATE',
                'quantity': kelly_result.optimal_fraction,
                'price': 0,  # Not applicable for Kelly calculation
                'timestamp': kelly_result.calculation_timestamp.isoformat(),
                'metadata': {
                    'kelly_result': asdict(kelly_result),
                    'calculation_type': 'multi_source_kelly'
                }
            }
            
            await self.supabase_service.store_trade_execution(kelly_data)
            
        except Exception as e:
            logger.error(f"Failed to store Kelly calculation: {e}")

# Utility functions for integration

async def create_multi_source_kelly_criterion(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[Dict] = None
) -> MultiSourceKellyCriterion:
    """Factory function to create multi-source Kelly Criterion calculator"""
    
    from app.services.mcp.redis_service import RedisService
    from app.services.mcp.supabase_service import SupabaseService
    from app.services.mcp.cross_exchange_validator import create_cross_exchange_validator
    
    redis_service = RedisService(redis_url)
    
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    cross_exchange_validator = await create_cross_exchange_validator(
        redis_url, supabase_url, supabase_key
    )
    
    return MultiSourceKellyCriterion(
        redis_service=redis_service,
        cross_exchange_validator=cross_exchange_validator,
        supabase_service=supabase_service,
        config=config
    )

# Example usage
if __name__ == "__main__":
    # This would be used for testing the multi-source Kelly Criterion
    pass