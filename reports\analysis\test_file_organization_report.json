{"test_file_organization_report": {"timestamp": "2025-06-18", "total_files_to_move": 30, "files_mapping": {"authentication_and_security": {"destination": "tests/validation/", "files": ["test_authentication_security.py"], "rationale": "Security and authentication validation tests"}, "api_integration": {"destination": "tests/api/exchanges/", "files": ["test_binance_integration.py", "test_futures_testnet.py", "test_testnet_connection.py"], "rationale": "External API integration tests for exchanges"}, "auto_trading_system": {"destination": "tests/e2e/", "files": ["test_auto_trading_controller_complete.py", "test_auto_trading_core.py", "test_auto_trading_final.py"], "rationale": "End-to-end auto trading system tests"}, "basic_functionality": {"destination": "tests/unit/components/", "files": ["test_auto_trading_imports.py", "test_auto_trading_simple.py", "test_basic_imports.py"], "rationale": "Basic component and import validation tests"}, "ml_integration": {"destination": "tests/integration/ml/", "files": ["test_complete_ml_pipeline_integration.py", "test_ml_pipeline_integration.py", "test_ml_enhanced_reporting.py", "test_ml_learning_system.py"], "rationale": "ML pipeline integration tests"}, "ml_simple_tests": {"destination": "tests/unit/analytics/", "files": ["test_ml_pipeline_simple.py", "test_ml_dashboard_endpoint.py", "test_mlflow_quick.py"], "rationale": "Simple ML component unit tests"}, "external_services": {"destination": "tests/integration/api/", "files": ["test_external_service_integration.py", "test_external_apis.py"], "rationale": "External service integration tests"}, "paper_trading": {"destination": "tests/features/trading/", "files": ["test_debug_fixed_paper_trading.py", "test_fixed_paper_trading.py", "test_minimal_paper_trading.py", "test_simple_fixed_paper_trading.py"], "rationale": "Paper trading feature tests"}, "system_validation": {"destination": "tests/validation/", "files": ["test_comprehensive_validation.py", "test_system_integrations_fixed.py", "test_system_integrations_validation.py", "test_working_integrations_final.py"], "rationale": "System-wide validation and integration tests"}, "trading_strategies": {"destination": "tests/strategies/", "files": ["test_trading_strategies_ml.py"], "rationale": "Trading strategy specific tests"}, "test_utilities": {"destination": "tests/", "files": ["autonomous_10min_strategy_test.py", "launch_autonomous_test.py", "run_ml_pipeline_test.py"], "rationale": "Test runner utilities - keep in tests root for easy access"}}}}