import React, { useState, useEffect } from 'react';
import { getAccountStatistics } from '../../services/api';
import { IAccountStats } from '../../types/stats';

const AccountStatistics: React.FC = () => {
  const [stats, setStats] = useState<IAccountStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getAccountStatistics();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch account statistics');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []); // Empty dependency array means this runs once on mount

  if (loading) {
    return <div>Loading Account Statistics...</div>;
  }

  if (error) {
    return <div style={{ color: 'red' }}>Error: {error}</div>;
  }

  if (!stats) {
    return <div>No statistics data available.</div>;
  }

  // Basic display - enhance styling later
  return (
    <div className="account-stats-container">
      <h2>Account Statistics</h2>
      <ul>
        <li>Balance: ${stats.balance.toFixed(2)}</li>
        <li>Total PNL: ${stats.total_pnl.toFixed(2)}</li>
        <li>Win Rate: {stats.win_rate.toFixed(2)}%</li>
        <li>Total Trades: {stats.total_trades}</li>
        <li>Winning Trades: {stats.winning_trades}</li>
        <li>Losing Trades: {stats.losing_trades}</li>
        <li>Avg Profit: ${stats.avg_profit.toFixed(2)}</li>
        <li>Avg Loss: ${stats.avg_loss.toFixed(2)}</li>
        <li>Profit Factor: {stats.profit_factor.toFixed(2)}</li>
        <li>Max Drawdown: {stats.max_drawdown.toFixed(2)}%</li>
      </ul>
    </div>
  );
};

export default AccountStatistics; 