"""
Portfolio Manager for Multi-Strategy Ensemble Execution.

This module replaces the Strategy Selector with an ensemble approach that runs
multiple strategies simultaneously with ML-optimized weights and dynamic position sizing.
"""

import pandas as pd
from typing import Dict, Optional, Any, List, Tuple
import logging
import asyncio
from datetime import datetime
from decimal import Decimal
from dataclasses import dataclass

from app.strategies.grid_strategy import GridStrategy
from app.strategies.technical_analysis_strategy import TechnicalAnalysisStrategy
from app.strategies.trend_following_strategy import TrendFollowingStrategy
from app.config.settings import Settings
from app.services.exchange.binance_client import BinanceExchangeClient
from app.services.execution.service import ExecutionService
from app.services.execution.models import OrderSide, OrderStatus, Order
from app.services.execution.trade_executor import execute_trade
from app.services.execution.order_management import OrderManager

# Import existing analysis modules
from app.strategies.market_analysis import MarketAnalyzer
from app.strategies.strategy_scoring import StrategyScorer
from app.strategies.execution_handler import ExecutionHandler
from app.strategies.utils import (
    process_klines_data,
    calculate_next_candle_time
)

# Import ML weight optimizer
from app.ml.models.weight_optimizer import MLWeightOptimizer


@dataclass
class AggregatedSignal:
    """Represents a signal aggregated from multiple strategies."""
    action: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 to 1.0
    position_size: float  # Percentage of portfolio
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    strategy_contributions: Optional[Dict[str, float]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class StrategyMetrics:
    """Performance metrics for individual strategies."""
    strategy_name: str
    weight: float
    last_signal: Optional[str] = None
    returns: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    trades_count: int = 0
    last_updated: Optional[datetime] = None


@dataclass
class PortfolioMetrics:
    """Portfolio-level performance metrics."""
    total_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    volatility: float = 0.0
    active_strategies: int = 0
    correlation_matrix: Optional[Dict[str, Dict[str, float]]] = None
    last_updated: Optional[datetime] = None


class PositionSizeCalculator:
    """Calculates optimal position sizes using Kelly Criterion and risk adjustments."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
    
    def calculate_kelly_size(self, strategy_stats: Dict[str, Any]) -> float:
        """Calculate Kelly optimal position size.
        
        Args:
            strategy_stats: Dictionary containing win_rate, avg_win, avg_loss
            
        Returns:
            Kelly fraction (0.0 to 1.0)
        """
        try:
            win_rate = strategy_stats.get('win_rate', 0.5)
            avg_win = strategy_stats.get('avg_win', 0.02)
            avg_loss = strategy_stats.get('avg_loss', 0.01)
            
            if avg_loss <= 0 or win_rate <= 0:
                return 0.01  # Minimum size
            
            # Kelly formula: f = (bp - q) / b
            # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
            b = avg_win / avg_loss
            p = win_rate
            q = 1 - win_rate
            
            kelly_fraction = (b * p - q) / b
            
            # Apply Kelly safety factor (typically 0.25 to 0.5)
            kelly_fraction *= 0.25
            
            # Ensure reasonable bounds
            kelly_fraction = max(0.01, min(kelly_fraction, 0.20))
            
            return kelly_fraction
            
        except Exception as e:
            self.logger.error(f"Error calculating Kelly size: {e}")
            return 0.01
    
    def adjust_for_volatility(self, base_size: float, current_vol: float, 
                             target_vol: float = 0.02) -> float:
        """Adjust position size based on volatility.
        
        Args:
            base_size: Base position size
            current_vol: Current market volatility
            target_vol: Target volatility level
            
        Returns:
            Volatility-adjusted position size
        """
        if current_vol <= 0:
            return base_size
        
        # Scale position size inversely with volatility
        vol_adjustment = target_vol / current_vol
        adjusted_size = base_size * vol_adjustment
        
        # Ensure reasonable bounds
        return max(0.005, min(adjusted_size, 0.25))
    
    def apply_correlation_discount(self, size: float, correlation: float) -> float:
        """Apply correlation-based position size discount.
        
        Args:
            size: Base position size
            correlation: Correlation between strategies (0.0 to 1.0)
            
        Returns:
            Correlation-adjusted position size
        """
        # Reduce position size for highly correlated strategies
        correlation_discount = 1.0 - (correlation * 0.5)
        return size * correlation_discount
    
    def enforce_risk_limits(self, size: float, max_risk_per_trade: float = 0.02,
                           max_portfolio_risk: float = 0.10) -> float:
        """Enforce risk limits on position size.
        
        Args:
            size: Calculated position size
            max_risk_per_trade: Maximum risk per trade
            max_portfolio_risk: Maximum total portfolio risk
            
        Returns:
            Risk-limited position size
        """
        # Apply individual trade risk limit
        size = min(size, max_risk_per_trade)
        
        # Apply portfolio risk limit (simplified version)
        size = min(size, max_portfolio_risk / 3)  # Assume max 3 strategies active
        
        return size


class PortfolioManager:
    """Portfolio management engine for multi-strategy ensemble execution.
    
    This class replaces the Strategy Selector and runs multiple strategies
    simultaneously with ML-optimized weights and dynamic position sizing.
    """

    def __init__(self,
                 settings: Settings,
                 execution_service: ExecutionService,
                 exchange_client: BinanceExchangeClient,
                 ml_weight_optimizer: Optional[MLWeightOptimizer] = None,
                 params: Optional[Dict[str, Any]] = None):
        """Initialize the Portfolio Manager.

        Args:
            settings: Application settings
            execution_service: Service for executing trades
            exchange_client: Client for interacting with the exchange
            ml_weight_optimizer: Optional ML weight optimizer for strategy weights
            params: Optional parameters to override defaults
        """
        self.settings = settings
        self.execution_service = execution_service
        self.exchange_client = exchange_client
        self.ml_weight_optimizer = ml_weight_optimizer

        # Set up logging
        self.logger = logging.getLogger(__name__)

        # Initialize parameters with defaults
        self.params = {
            'symbol': 'BTCUSDT',
            'timeframe': '15m',
            'risk_per_trade': 1.0,
            'max_position_size': 20.0,
            'portfolio_rebalance_frequency': 4,  # Rebalance every 4 candles
            'min_weight_threshold': 0.1,  # Minimum weight to activate strategy
            'correlation_threshold': 0.8,  # High correlation threshold
        }

        if params:
            self.params.update(params)

        # Extract commonly used parameters
        self.symbol = self.params['symbol']
        self.timeframe = self.params['timeframe']

        # Initialize state variables
        self._is_running = False
        self._stop_requested = False
        self.current_weights = {'grid': 0.33, 'technical_analysis': 0.33, 'trend_following': 0.34}
        self.rebalance_count = 0
        self.last_market_conditions = {}

        # Initialize position size calculator
        self.position_calculator = PositionSizeCalculator(settings)

        # Initialize execution handler
        self.execution_handler = ExecutionHandler(execution_service)

        # Initialize strategy instances
        self.strategies = {
            'grid': GridStrategy(
                symbol=self.symbol,
                timeframe=self.timeframe,
                settings=settings
            ),
            'technical_analysis': TechnicalAnalysisStrategy(
                symbol=self.symbol,
                timeframe=self.timeframe,
                settings=settings
            ),
            'trend_following': TrendFollowingStrategy(
                symbol=self.symbol,
                timeframe=self.timeframe,
                settings=settings
            )
        }

        # Initialize performance tracking
        self.strategy_metrics = {
            name: StrategyMetrics(strategy_name=name, weight=weight)
            for name, weight in self.current_weights.items()
        }
        self.portfolio_metrics = PortfolioMetrics()

        self.logger.info(f"PortfolioManager initialized for {self.symbol}")

    def is_running(self) -> bool:
        """Check if the portfolio management loop is currently active."""
        return self._is_running

    async def stop(self):
        """Signal the portfolio management loop to stop."""
        self.logger.info("Stop requested for PortfolioManager loop.")
        self._stop_requested = True

        # Stop all strategies
        for strategy_name, strategy in self.strategies.items():
            if hasattr(strategy, 'stop'):
                self.logger.info(f"Stopping strategy: {strategy_name}")
                await strategy.stop()

        await asyncio.sleep(1)

    async def start(self):
        """Start the main portfolio management loop."""
        if self._is_running:
            self.logger.warning("Portfolio management loop is already running.")
            return

        self.logger.info(f"Starting PortfolioManager loop for {self.symbol}...")
        self._is_running = True
        self._stop_requested = False

        # Pre-load ML model if optimization enabled
        if self.settings.ml_weight_optimization_enabled and self.ml_weight_optimizer:
            try:
                self.logger.info("Pre-loading ML model for weight optimization")
                loaded = await self.ml_weight_optimizer.load_model()
                if loaded:
                    self.logger.info("ML model loaded successfully")
                else:
                    self.logger.warning("ML model failed to load")
            except Exception as e:
                self.logger.error(f"Error pre-loading ML model: {e}")

        # Fetch initial balance
        try:
            self.logger.info("Fetching initial futures account balance...")
            balance_info = await self.exchange_client.get_account_balance()
            usdt_balance = balance_info.get('USDT')
            if usdt_balance is not None:
                self.logger.info(f"Current USDT Futures Balance: {usdt_balance}")
            else:
                self.logger.warning("Could not find USDT balance in futures account.")
        except Exception as e:
            self.logger.error(f"Failed to fetch initial balance: {e}", exc_info=True)

        # Start all strategies
        for strategy_name, strategy in self.strategies.items():
            try:
                self.logger.info(f"Starting strategy: {strategy_name}")
                await strategy.start()
            except Exception as e:
                self.logger.error(f"Failed to start strategy {strategy_name}: {e}")

        # Main portfolio management loop
        try:
            while not self._stop_requested:
                self.logger.debug(f"PortfolioManager loop iteration for {self.symbol}...")

                try:
                    # Fetch market data
                    klines_list = await self.exchange_client.get_historical_klines(
                        symbol=self.symbol,
                        interval=self.timeframe,
                        limit=self.settings.kline_limit
                    )

                    df = self.process_klines_data(klines_list)
                    if df.empty:
                        self.logger.warning(f"Empty klines data for {self.symbol}")
                        await asyncio.sleep(10)
                        continue

                    current_price = df['close'].iloc[-1]
                    self.logger.debug(f"Current {self.symbol} price: {current_price}")

                    # Analyze market conditions
                    market_conditions = MarketAnalyzer.analyze_market_conditions(df)
                    self.last_market_conditions = market_conditions

                    # Get strategy weights
                    strategy_weights = await self.get_strategy_weights(market_conditions)
                    
                    # Update current weights
                    self.current_weights = strategy_weights
                    
                    # Execute ensemble
                    await self.execute_ensemble(df, strategy_weights)

                    # Update performance metrics
                    self.update_performance_metrics()

                except Exception as e:
                    self.logger.error(f"Error in PortfolioManager loop: {e}", exc_info=True)

                # Wait for next candle
                next_candle_time = calculate_next_candle_time(self.timeframe)
                wait_seconds = (next_candle_time - datetime.now()).total_seconds()
                wait_seconds = max(10, min(wait_seconds, 60))

                self.logger.debug(f"Waiting {wait_seconds:.0f} seconds for next candle...")
                await asyncio.sleep(wait_seconds)

        except asyncio.CancelledError:
            self.logger.info("PortfolioManager loop cancelled.")
        except Exception as e:
            self.logger.error(f"Unexpected error in PortfolioManager loop: {e}", exc_info=True)
        finally:
            # Clean up
            for strategy in self.strategies.values():
                if hasattr(strategy, 'stop'):
                    await strategy.stop()

            self._is_running = False
            self.logger.info("PortfolioManager loop stopped.")

    async def get_strategy_weights(self, market_conditions: Dict[str, float]) -> Dict[str, float]:
        """Get strategy weights using ML optimization or fallback to rule-based scoring.
        
        Args:
            market_conditions: Current market condition metrics
            
        Returns:
            Dictionary of strategy weights (sum = 1.0)
        """
        try:
            # Try ML-optimized weights first
            if self.settings.ml_weight_optimization_enabled and self.ml_weight_optimizer:
                try:
                    self.logger.debug("Getting ML-optimized weights")
                    ml_weights = await self.ml_weight_optimizer.get_optimized_weights(market_conditions)
                    
                    if ml_weights and all(isinstance(v, (int, float)) for v in ml_weights.values()):
                        # Normalize weights to ensure they sum to 1.0
                        total_weight = sum(ml_weights.values())
                        if total_weight > 0:
                            normalized_weights = {
                                name: weight / total_weight
                                for name, weight in ml_weights.items()
                            }
                            self.logger.info(f"Using ML weights: {normalized_weights}")
                            return normalized_weights
                        
                except Exception as e:
                    self.logger.error(f"Error getting ML-optimized weights: {e}")

            # Fallback to rule-based scoring
            self.logger.debug("Using rule-based strategy scoring")
            _, strategy_scores = StrategyScorer.select_best_strategy(
                market_conditions,
                min_score_threshold=0.0  # Allow all strategies
            )
            
            # Convert scores to weights
            total_score = sum(strategy_scores.values())
            if total_score > 0:
                weights = {
                    name: score / total_score
                    for name, score in strategy_scores.items()
                }
            else:
                # Equal weights if no valid scores
                weights = {name: 1.0/3.0 for name in self.strategies.keys()}
            
            self.logger.info(f"Using rule-based weights: {weights}")
            return weights
            
        except Exception as e:
            self.logger.error(f"Error calculating strategy weights: {e}")
            # Return equal weights as ultimate fallback
            return {name: 1.0/3.0 for name in self.strategies.keys()}

    async def execute_ensemble(self, df: pd.DataFrame, weights: Dict[str, float]):
        """Execute all strategies with their assigned weights.
        
        Args:
            df: Market data DataFrame
            weights: Strategy weights dictionary
        """
        try:
            # Update strategy parameters based on market conditions
            for strategy_name, strategy in self.strategies.items():
                if weights.get(strategy_name, 0) >= self.params['min_weight_threshold']:
                    # Update strategy parameters
                    strategy_params = StrategyScorer.get_strategy_parameters(
                        strategy_name,
                        self.last_market_conditions,
                        strategy.params
                    )
                    strategy.update_params(strategy_params)
                    
                    # Execute strategy
                    await strategy.execute(df)
                    
                    # Update strategy metrics
                    self.strategy_metrics[strategy_name].weight = weights[strategy_name]
                    self.strategy_metrics[strategy_name].last_updated = datetime.now()
                else:
                    # Strategy weight too low, don't execute
                    self.logger.debug(f"Skipping {strategy_name} - weight too low: {weights.get(strategy_name, 0):.3f}")

        except Exception as e:
            self.logger.error(f"Error executing ensemble: {e}", exc_info=True)

    def aggregate_signals(self, strategy_signals: Dict[str, Any]) -> AggregatedSignal:
        """Aggregate signals from multiple strategies into a single signal.
        
        Args:
            strategy_signals: Dictionary of signals from each strategy
            
        Returns:
            Aggregated signal combining all strategy inputs
        """
        # This is a placeholder for signal aggregation logic
        # In practice, this would combine multiple strategy signals
        # based on their weights and confidence levels
        
        return AggregatedSignal(
            action='hold',
            confidence=0.5,
            position_size=0.01,
            strategy_contributions=self.current_weights
        )

    def resolve_conflicts(self, overlapping_signals: List[Any]) -> Any:
        """Resolve conflicts when strategies generate overlapping signals.
        
        Args:
            overlapping_signals: List of conflicting signals
            
        Returns:
            Resolved signal
        """
        # Placeholder for conflict resolution logic
        # Could use weight-based voting, confidence scoring, etc.
        if overlapping_signals:
            return overlapping_signals[0]
        return None

    def update_performance_metrics(self):
        """Update performance metrics for strategies and portfolio."""
        try:
            # Update individual strategy metrics
            for strategy_name, metrics in self.strategy_metrics.items():
                # This would be implemented with actual performance calculation
                # based on trade history and returns
                pass
            
            # Update portfolio metrics
            self.portfolio_metrics.active_strategies = sum(
                1 for weight in self.current_weights.values()
                if weight >= self.params['min_weight_threshold']
            )
            self.portfolio_metrics.last_updated = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the portfolio manager.

        Returns:
            Dict[str, Any]: Status information
        """
        return {
            'is_running': self._is_running,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'current_weights': self.current_weights,
            'market_conditions': self.last_market_conditions,
            'active_strategies': [
                name for name, weight in self.current_weights.items()
                if weight >= self.params['min_weight_threshold']
            ],
            'portfolio_metrics': {
                'active_strategies': self.portfolio_metrics.active_strategies,
                'last_updated': self.portfolio_metrics.last_updated
            }
        }

    def process_klines_data(self, klines):
        """Process klines data into a pandas DataFrame."""
        if not klines:
            return pd.DataFrame()

        columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        processed_klines = [kline[:6] for kline in klines]

        df = pd.DataFrame(processed_klines, columns=columns)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in columns[1:]:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        return df