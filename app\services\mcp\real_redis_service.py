#!/usr/bin/env python3
"""
Real Redis Service Implementation
Replaces mock Redis with actual Redis connection using credentials from .env.mcp
"""

import asyncio
import redis.asyncio as redis
import json
import logging
from typing import Optional, Any
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env.mcp')

logger = logging.getLogger(__name__)

class RealRedisService:
    """Real Redis service with actual Redis connection"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379')
        self.redis_client = None
        self.connection_pool = None
        self.operation_metrics = {
            'get_calls': 0,
            'set_calls': 0,
            'operation_times': [],
            'errors': []
        }
        
    async def connect(self):
        """Establish Redis connection"""
        try:
            # Create connection pool for better performance
            self.connection_pool = redis.ConnectionPool.from_url(
                self.redis_url,
                decode_responses=True,
                max_connections=20
            )
            
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis_client.ping()
            logger.info(f"Successfully connected to Redis at {self.redis_url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'connect',
                'error': str(e),
                'timestamp': datetime.now()
            })
            return False
    
    async def disconnect(self):
        """Close Redis connection"""
        try:
            if self.redis_client:
                await self.redis_client.aclose()
            if self.connection_pool:
                await self.connection_pool.aclose()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
    
    async def get(self, key: str) -> Optional[str]:
        """Get value from Redis"""
        start_time = datetime.now()
        
        try:
            if not self.redis_client:
                await self.connect()
            
            self.operation_metrics['get_calls'] += 1
            
            value = await self.redis_client.get(key)
            
            # Track operation time
            operation_time = (datetime.now() - start_time).total_seconds() * 1000
            self.operation_metrics['operation_times'].append(operation_time)
            
            logger.debug(f"Redis GET {key}: {'HIT' if value else 'MISS'} in {operation_time:.2f}ms")
            return value
            
        except Exception as e:
            logger.error(f"Redis GET error for key {key}: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'get',
                'key': key,
                'error': str(e),
                'timestamp': datetime.now()
            })
            return None
    
    async def set(self, key: str, value: str, ex: Optional[int] = None):
        """Set value in Redis with optional expiration"""
        return await self.setex(key, ex or 3600, value)
    
    async def setex(self, key: str, ttl: int, value: str):
        """Set value in Redis with expiration time"""
        start_time = datetime.now()
        
        try:
            if not self.redis_client:
                await self.connect()
            
            self.operation_metrics['set_calls'] += 1
            
            await self.redis_client.setex(key, ttl, value)
            
            # Track operation time
            operation_time = (datetime.now() - start_time).total_seconds() * 1000
            self.operation_metrics['operation_times'].append(operation_time)
            
            logger.debug(f"Redis SET {key} with TTL {ttl}s in {operation_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"Redis SET error for key {key}: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'set',
                'key': key,
                'error': str(e),
                'timestamp': datetime.now()
            })
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis"""
        try:
            if not self.redis_client:
                await self.connect()
            
            result = await self.redis_client.delete(key)
            logger.debug(f"Redis DELETE {key}: {'SUCCESS' if result else 'NOT_FOUND'}")
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis DELETE error for key {key}: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'delete',
                'key': key,
                'error': str(e),
                'timestamp': datetime.now()
            })
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        try:
            if not self.redis_client:
                await self.connect()
            
            result = await self.redis_client.exists(key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis EXISTS error for key {key}: {e}")
            return False
    
    async def keys(self, pattern: str = "*") -> list:
        """Get keys matching pattern"""
        try:
            if not self.redis_client:
                await self.connect()
            
            keys = await self.redis_client.keys(pattern)
            return keys
            
        except Exception as e:
            logger.error(f"Redis KEYS error for pattern {pattern}: {e}")
            return []
    
    async def flushdb(self):
        """Clear all keys in current database (use carefully!)"""
        try:
            if not self.redis_client:
                await self.connect()
            
            await self.redis_client.flushdb()
            logger.info("Redis database flushed")
            
        except Exception as e:
            logger.error(f"Redis FLUSHDB error: {e}")
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        operation_times = self.operation_metrics['operation_times']
        
        if not operation_times:
            return {
                'avg_latency': 0,
                'max_latency': 0,
                'min_latency': 0,
                'total_operations': 0,
                'get_calls': self.operation_metrics['get_calls'],
                'set_calls': self.operation_metrics['set_calls'],
                'error_count': len(self.operation_metrics['errors'])
            }
        
        return {
            'avg_latency': sum(operation_times) / len(operation_times),
            'max_latency': max(operation_times),
            'min_latency': min(operation_times),
            'total_operations': len(operation_times),
            'get_calls': self.operation_metrics['get_calls'],
            'set_calls': self.operation_metrics['set_calls'],
            'error_count': len(self.operation_metrics['errors'])
        }
    
    def get_connection_status(self) -> dict:
        """Get connection status information"""
        return {
            'connected': self.redis_client is not None,
            'redis_url': self.redis_url,
            'pool_created': self.connection_pool is not None,
            'error_count': len(self.operation_metrics['errors']),
            'last_error': self.operation_metrics['errors'][-1] if self.operation_metrics['errors'] else None
        }

# Async context manager support
class RedisConnection:
    """Async context manager for Redis connections"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_service = RealRedisService(redis_url)
    
    async def __aenter__(self):
        await self.redis_service.connect()
        return self.redis_service
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.redis_service.disconnect()

# Factory function for easy integration
async def create_redis_service(redis_url: Optional[str] = None) -> RealRedisService:
    """Factory function to create and connect Redis service"""
    service = RealRedisService(redis_url)
    await service.connect()
    return service

# Example usage and testing
if __name__ == "__main__":
    async def test_redis_connection():
        """Test Redis connection and basic operations"""
        print("Testing Redis connection...")
        
        async with RedisConnection() as redis_service:
            # Test basic operations
            await redis_service.setex("test_key", 60, "test_value")
            value = await redis_service.get("test_key")
            print(f"Test value: {value}")
            
            # Test JSON storage
            test_data = {"message": "Hello Redis!", "timestamp": datetime.now().isoformat()}
            await redis_service.setex("test_json", 60, json.dumps(test_data))
            json_value = await redis_service.get("test_json")
            if json_value:
                parsed_data = json.loads(json_value)
                print(f"JSON test: {parsed_data}")
            
            # Get performance stats
            stats = redis_service.get_performance_stats()
            print(f"Performance stats: {stats}")
            
            # Clean up
            await redis_service.delete("test_key")
            await redis_service.delete("test_json")
            
        print("Redis test completed!")
    
    # Run test
    asyncio.run(test_redis_connection())