{"statistics": {"detectionDate": "2025-06-06T21:56:36.275Z", "formats": {"javascript": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/account/AccountStatistics.tsx": {"lines": 0, "tokens": 9, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/pages/MLOptimization.tsx": {"lines": 0, "tokens": 14, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/pages/Login.tsx": {"lines": 74, "tokens": 528, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/pages/AutoTradeControl.tsx": {"lines": 56, "tokens": 465, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/contexts/ThemeContext.tsx": {"lines": 1, "tokens": 19, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/contexts/AuthContext.tsx": {"lines": 0, "tokens": 11, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx": {"lines": 244, "tokens": 2336, "sources": 1, "clones": 2, "duplicatedLines": 124, "duplicatedTokens": 1140, "percentage": 50.82, "percentageTokens": 48.8, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/MLControls.js": {"lines": 356, "tokens": 3025, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/DashboardLayout.tsx": {"lines": 48, "tokens": 467, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/setupProxy.js": {"lines": 21, "tokens": 140, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/App.tsx": {"lines": 12, "tokens": 159, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceTimeWeightedAveragePriceFutureAlgo.js": {"lines": 36, "tokens": 355, "sources": 1, "clones": 3, "duplicatedLines": 51, "duplicatedTokens": 369, "percentage": 141.67, "percentageTokens": 103.94, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceSpotPlaceOrder.js": {"lines": 37, "tokens": 355, "sources": 1, "clones": 1, "duplicatedLines": 21, "duplicatedTokens": 150, "percentage": 56.76, "percentageTokens": 42.25, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceOrderBook.js": {"lines": 27, "tokens": 259, "sources": 1, "clones": 1, "duplicatedLines": 15, "duplicatedTokens": 111, "percentage": 55.56, "percentageTokens": 42.86, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceAccountInfo.js": {"lines": 50, "tokens": 526, "sources": 1, "clones": 1, "duplicatedLines": 15, "duplicatedTokens": 108, "percentage": 30, "percentageTokens": 20.53, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/config/client.js": {"lines": 12, "tokens": 146, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/playwright.config.js": {"lines": 50, "tokens": 299, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/index.js": {"lines": 30, "tokens": 280, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js": {"lines": 18, "tokens": 132, "sources": 1, "clones": 7, "duplicatedLines": 95, "duplicatedTokens": 640, "percentage": 527.78, "percentageTokens": 484.85, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/test-mcp.js": {"lines": 24, "tokens": 196, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/sequentialthinking-mcp.js": {"lines": 18, "tokens": 126, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 77.78, "percentageTokens": 74.6, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/postgres-mcp.js": {"lines": 18, "tokens": 138, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 77.78, "percentageTokens": 68.12, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/n8n-mcp.js": {"lines": 33, "tokens": 261, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/memory-mcp.js": {"lines": 18, "tokens": 132, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 77.78, "percentageTokens": 71.21, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/git-mcp.js": {"lines": 18, "tokens": 129, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 77.78, "percentageTokens": 72.87, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/coincap-mcp.js": {"lines": 18, "tokens": 132, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 77.78, "percentageTokens": 71.21, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/ccxt-mcp.js": {"lines": 18, "tokens": 132, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 77.78, "percentageTokens": 71.21, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/binance-mcp.js": {"lines": 28, "tokens": 209, "sources": 1, "clones": 1, "duplicatedLines": 11, "duplicatedTokens": 76, "percentage": 39.29, "percentageTokens": 36.36, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 1265, "tokens": 10980, "sources": 28, "clones": 11, "duplicatedLines": 208, "duplicatedTokens": 1579, "percentage": 16.44, "percentageTokens": 14.38, "newDuplicatedLines": 0, "newClones": 0}}, "tsx": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/account/AccountStatistics.tsx": {"lines": 59, "tokens": 724, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/pages/MLOptimization.tsx": {"lines": 23, "tokens": 231, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/pages/Login.tsx": {"lines": 135, "tokens": 1074, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/pages/AutoTradeControl.tsx": {"lines": 182, "tokens": 1409, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/contexts/ThemeContext.tsx": {"lines": 147, "tokens": 1242, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/contexts/AuthContext.tsx": {"lines": 222, "tokens": 1798, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx": {"lines": 665, "tokens": 6477, "sources": 1, "clones": 2, "duplicatedLines": 20, "duplicatedTokens": 208, "percentage": 3.01, "percentageTokens": 3.21, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/ProtectedRoute.tsx": {"lines": 32, "tokens": 255, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/DashboardLayout.tsx": {"lines": 150, "tokens": 1326, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/index.tsx": {"lines": 16, "tokens": 104, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/App.tsx": {"lines": 37, "tokens": 381, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 1668, "tokens": 15021, "sources": 11, "clones": 1, "duplicatedLines": 10, "duplicatedTokens": 104, "percentage": 0.6, "percentageTokens": 0.69, "newDuplicatedLines": 0, "newClones": 0}}, "markdown": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/build/static/js/main.9ea36bba.js.LICENSE.txt": {"lines": 48, "tokens": 452, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/docs/improvements/execution_modularization.md": {"lines": 55, "tokens": 506, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/README.md": {"lines": 43, "tokens": 615, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/design/weight-optimizer.md": {"lines": 440, "tokens": 3318, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/design/trading-strategies.md": {"lines": 438, "tokens": 3232, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/design/strategy-selector.md": {"lines": 441, "tokens": 3264, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/design/market-condition-detector.md": {"lines": 419, "tokens": 3270, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/websocket/SL_TP_Websocket_Testing_and_Integration_Plan.md": {"lines": 130, "tokens": 1329, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/websocket/SL_TP_Websocket_Plan.md": {"lines": 252, "tokens": 1987, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/websocket/SL_TP_Websocket_Integration_Test_Plan.md": {"lines": 175, "tokens": 1565, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/setup/GITHUB_SETUP.md": {"lines": 41, "tokens": 460, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/refactoring/REFACTORING_PLAN.md": {"lines": 71, "tokens": 633, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/improvements/risk_adjusted_scoring.md": {"lines": 311, "tokens": 2533, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/improvements/ml_weight_optimization_summary.md": {"lines": 85, "tokens": 639, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/improvements/ml_weight_optimization.md": {"lines": 480, "tokens": 2786, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/improvements/market_regime_detection.md": {"lines": 339, "tokens": 2648, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/improvements/ensemble_strategy_allocation.md": {"lines": 390, "tokens": 2664, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/README.md": {"lines": 167, "tokens": 1401, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/13-methodological-integration.md": {"lines": 32, "tokens": 512, "sources": 1, "clones": 1, "duplicatedLines": 32, "duplicatedTokens": 512, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/13-future-directions.md": {"lines": 40, "tokens": 512, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/12-key-lessons.md": {"lines": 28, "tokens": 366, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/12-future-directions.md": {"lines": 32, "tokens": 512, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/11-methodological-integration.md": {"lines": 32, "tokens": 512, "sources": 1, "clones": 1, "duplicatedLines": 32, "duplicatedTokens": 512, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/11-key-lessons.md": {"lines": 28, "tokens": 382, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/10-current-system-state.md": {"lines": 33, "tokens": 427, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/09-context-optimization.md": {"lines": 52, "tokens": 938, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/08-creative-phase-enforcement.md": {"lines": 40, "tokens": 681, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/07-structured-creative-thinking.md": {"lines": 36, "tokens": 729, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/06-self-assessment-recommendations.md": {"lines": 39, "tokens": 538, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/05-adaptive-complexity-model.md": {"lines": 36, "tokens": 453, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/04-single-source-of-truth.md": {"lines": 28, "tokens": 403, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/03-redundancy-elimination.md": {"lines": 19, "tokens": 238, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/02-system-self-assessment.md": {"lines": 24, "tokens": 266, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/01-efficiency-and-clarity.md": {"lines": 30, "tokens": 374, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/00-introduction.md": {"lines": 15, "tokens": 320, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/custom_modes/van_instructions.md": {"lines": 191, "tokens": 664, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/custom_modes/plan_instructions.md": {"lines": 222, "tokens": 1150, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/custom_modes/mode_switching_analysis.md": {"lines": 147, "tokens": 1949, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/custom_modes/implement_instructions.md": {"lines": 239, "tokens": 1231, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/custom_modes/creative_instructions.md": {"lines": 274, "tokens": 1286, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/.pytest_cache/README.md": {"lines": 5, "tokens": 73, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/workflows/analyst.md": {"lines": 174, "tokens": 1456, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/tech_context_template.md": {"lines": 30, "tokens": 407, "sources": 1, "clones": 1, "duplicatedLines": 30, "duplicatedTokens": 407, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/system_patterns_template.md": {"lines": 29, "tokens": 342, "sources": 1, "clones": 1, "duplicatedLines": 29, "duplicatedTokens": 342, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/progress_template.md": {"lines": 31, "tokens": 308, "sources": 1, "clones": 1, "duplicatedLines": 31, "duplicatedTokens": 308, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/product_context_template.md": {"lines": 21, "tokens": 276, "sources": 1, "clones": 1, "duplicatedLines": 21, "duplicatedTokens": 276, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/personal_memory_template.md": {"lines": 18, "tokens": 170, "sources": 1, "clones": 1, "duplicatedLines": 18, "duplicatedTokens": 170, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/implementation_checklist_template.md": {"lines": 24, "tokens": 172, "sources": 1, "clones": 1, "duplicatedLines": 24, "duplicatedTokens": 172, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/ai_context_snapshot_template.md": {"lines": 25, "tokens": 260, "sources": 1, "clones": 1, "duplicatedLines": 25, "duplicatedTokens": 260, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/active_context_template.md": {"lines": 26, "tokens": 246, "sources": 1, "clones": 1, "duplicatedLines": 26, "duplicatedTokens": 246, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/09_checklist_protocol.md": {"lines": 46, "tokens": 803, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/08_development_methodology.md": {"lines": 41, "tokens": 462, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/07_learning_journal_guide.md": {"lines": 30, "tokens": 562, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/06_behavioral_protocol.md": {"lines": 51, "tokens": 1595, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/05_template_references.md": {"lines": 42, "tokens": 161, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/04_file_code_management.md": {"lines": 47, "tokens": 743, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/03_mcp_usage_guidelines.md": {"lines": 18, "tokens": 160, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/02_memory_bank_protocol.md": {"lines": 59, "tokens": 801, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/01_riper5_framework.md": {"lines": 104, "tokens": 1239, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/00_core_directives.md": {"lines": 20, "tokens": 309, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/tech_context_template.md": {"lines": 30, "tokens": 407, "sources": 1, "clones": 1, "duplicatedLines": 30, "duplicatedTokens": 407, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/system_patterns_template.md": {"lines": 29, "tokens": 342, "sources": 1, "clones": 1, "duplicatedLines": 29, "duplicatedTokens": 342, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/progress_template.md": {"lines": 31, "tokens": 308, "sources": 1, "clones": 1, "duplicatedLines": 31, "duplicatedTokens": 308, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/product_context_template.md": {"lines": 21, "tokens": 276, "sources": 1, "clones": 1, "duplicatedLines": 21, "duplicatedTokens": 276, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/personal_memory_template.md": {"lines": 18, "tokens": 170, "sources": 1, "clones": 1, "duplicatedLines": 18, "duplicatedTokens": 170, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/implementation_checklist_template.md": {"lines": 24, "tokens": 172, "sources": 1, "clones": 1, "duplicatedLines": 24, "duplicatedTokens": 172, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/ai_context_snapshot_template.md": {"lines": 25, "tokens": 260, "sources": 1, "clones": 1, "duplicatedLines": 25, "duplicatedTokens": 260, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/active_context_template.md": {"lines": 26, "tokens": 246, "sources": 1, "clones": 1, "duplicatedLines": 26, "duplicatedTokens": 246, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/tasks.md": {"lines": 60, "tokens": 1333, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/progress.md": {"lines": 44, "tokens": 554, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/implementation_checklist.md": {"lines": 45, "tokens": 229, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/implementation-plan.md": {"lines": 156, "tokens": 1311, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md": {"lines": 468, "tokens": 5885, "sources": 1, "clones": 6, "duplicatedLines": 72, "duplicatedTokens": 916, "percentage": 15.38, "percentageTokens": 15.56, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/trade_state_service_implementation.md": {"lines": 109, "tokens": 432, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/shrimp_task_manager_mcp.md": {"lines": 146, "tokens": 1932, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/README.md": {"lines": 42, "tokens": 609, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/PRD.md": {"lines": 128, "tokens": 1567, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/playwright_mcp_tests.md": {"lines": 97, "tokens": 891, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/next_phase_prd.md": {"lines": 73, "tokens": 482, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/n8n_mcp_setup.md": {"lines": 99, "tokens": 811, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/mcp_tests.md": {"lines": 172, "tokens": 1659, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/mcp_servers_setup.md": {"lines": 393, "tokens": 3280, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/docs/epics_stories_tickets.md": {"lines": 57, "tokens": 627, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/start-phase-readme.md": {"lines": 33, "tokens": 426, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/start-phase-framework.md": {"lines": 284, "tokens": 2637, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/README.md": {"lines": 62, "tokens": 657, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/cursor_enhanced_rule_set.md": {"lines": 658, "tokens": 5762, "sources": 1, "clones": 2, "duplicatedLines": 542, "duplicatedTokens": 4235, "percentage": 82.37, "percentageTokens": 73.5, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/README.md": {"lines": 277, "tokens": 3562, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/memory_bank_upgrade_guide.md": {"lines": 567, "tokens": 6394, "sources": 1, "clones": 2, "duplicatedLines": 52, "duplicatedTokens": 490, "percentage": 9.17, "percentageTokens": 7.66, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/creative_mode_think_tool.md": {"lines": 254, "tokens": 2068, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/README.md": {"lines": 213, "tokens": 2150, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/README.md": {"lines": 109, "tokens": 1245, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.pytest_cache/README.md": {"lines": 5, "tokens": 73, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/requirements.txt": {"lines": 30, "tokens": 156, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/README.md": {"lines": 226, "tokens": 1555, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/all_packages.txt": {"lines": 105, "tokens": 344, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/# Cursor IDE AI Assistant - Enhance.txt": {"lines": 992, "tokens": 9397, "sources": 1, "clones": 2, "duplicatedLines": 542, "duplicatedTokens": 4235, "percentage": 54.64, "percentageTokens": 45.07, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 12891, "tokens": 118499, "sources": 97, "clones": 15, "duplicatedLines": 840, "duplicatedTokens": 7631, "percentage": 6.52, "percentageTokens": 6.44, "newDuplicatedLines": 0, "newClones": 0}}, "typescript": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/types/stats.ts": {"lines": 12, "tokens": 92, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/types/index.ts": {"lines": 298, "tokens": 2119, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/websocket.ts": {"lines": 271, "tokens": 2257, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/tokenService.ts": {"lines": 151, "tokens": 994, "sources": 1, "clones": 2, "duplicatedLines": 22, "duplicatedTokens": 206, "percentage": 14.57, "percentageTokens": 20.72, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/authService.ts": {"lines": 84, "tokens": 580, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/apiService.ts": {"lines": 89, "tokens": 695, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/api.ts": {"lines": 121, "tokens": 1430, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/config.ts": {"lines": 10, "tokens": 87, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceTimeWeightedAveragePriceFutureAlgo.ts": {"lines": 47, "tokens": 433, "sources": 1, "clones": 3, "duplicatedLines": 52, "duplicatedTokens": 419, "percentage": 110.64, "percentageTokens": 96.77, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceSpotPlaceOrder.ts": {"lines": 48, "tokens": 437, "sources": 1, "clones": 1, "duplicatedLines": 20, "duplicatedTokens": 164, "percentage": 41.67, "percentageTokens": 37.53, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceOrderBook.ts": {"lines": 36, "tokens": 319, "sources": 1, "clones": 1, "duplicatedLines": 16, "duplicatedTokens": 129, "percentage": 44.44, "percentageTokens": 40.44, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceAccountInfo.ts": {"lines": 57, "tokens": 616, "sources": 1, "clones": 1, "duplicatedLines": 16, "duplicatedTokens": 126, "percentage": 28.07, "percentageTokens": 20.45, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/config/client.ts": {"lines": 14, "tokens": 162, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/index.ts": {"lines": 38, "tokens": 321, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/playwright.config.ts": {"lines": 80, "tokens": 456, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 1356, "tokens": 10998, "sources": 15, "clones": 4, "duplicatedLines": 63, "duplicatedTokens": 522, "percentage": 4.65, "percentageTokens": 4.75, "newDuplicatedLines": 0, "newClones": 0}}, "css": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/index.css": {"lines": 12, "tokens": 88, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/App.css": {"lines": 37, "tokens": 194, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 49, "tokens": 282, "sources": 2, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "json": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/public/manifest.json": {"lines": 24, "tokens": 133, "sources": 1, "clones": 1, "duplicatedLines": 24, "duplicatedTokens": 133, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/build/manifest.json": {"lines": 24, "tokens": 133, "sources": 1, "clones": 1, "duplicatedLines": 24, "duplicatedTokens": 133, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/build/asset-manifest.json": {"lines": 15, "tokens": 83, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/tsconfig.json": {"lines": 25, "tokens": 167, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/package.json": {"lines": 49, "tokens": 322, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/models/weight_optimizer.json": {"lines": 20, "tokens": 150, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/tsconfig.json": {"lines": 16, "tokens": 116, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/package.json": {"lines": 69, "tokens": 429, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/config.json": {"lines": 15, "tokens": 92, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/tasks.json": {"lines": 108, "tokens": 645, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/settings.json": {"lines": 95, "tokens": 561, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 460, "tokens": 2831, "sources": 11, "clones": 1, "duplicatedLines": 24, "duplicatedTokens": 133, "percentage": 5.22, "percentageTokens": 4.7, "newDuplicatedLines": 0, "newClones": 0}}, "markup": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/public/index.html": {"lines": 46, "tokens": 255, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 46, "tokens": 255, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "python": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/tests/services/execution/test_trade_management.py": {"lines": 234, "tokens": 1612, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/__init__.py": {"lines": 12, "tokens": 99, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py": {"lines": 748, "tokens": 6477, "sources": 1, "clones": 24, "duplicatedLines": 254, "duplicatedTokens": 2772, "percentage": 33.96, "percentageTokens": 42.8, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/utils.py": {"lines": 20, "tokens": 86, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/trade_state.py": {"lines": 42, "tokens": 431, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/trade_operations.py": {"lines": 134, "tokens": 795, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/trade_management.py": {"lines": 395, "tokens": 2979, "sources": 1, "clones": 2, "duplicatedLines": 16, "duplicatedTokens": 174, "percentage": 4.05, "percentageTokens": 5.84, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/trade_executor.py": {"lines": 62, "tokens": 438, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/service.py": {"lines": 67, "tokens": 488, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/retry.py": {"lines": 157, "tokens": 907, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/position.py": {"lines": 288, "tokens": 2034, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/order_placement.py": {"lines": 176, "tokens": 1290, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/order_manager.py": {"lines": 125, "tokens": 851, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/order_management.py": {"lines": 319, "tokens": 1971, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/models.py": {"lines": 69, "tokens": 462, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/lifecycle.py": {"lines": 57, "tokens": 346, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/execution_service.py": {"lines": 25, "tokens": 131, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/exchange/__init__.py": {"lines": 4, "tokens": 48, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/exchange/exchange_client.py": {"lines": 205, "tokens": 947, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/exchange/binance_client.py": {"lines": 468, "tokens": 4466, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 248, "percentage": 5.13, "percentageTokens": 5.55, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/dashboard/trade_state_service.py": {"lines": 36, "tokens": 292, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/training/__init__.py": {"lines": 4, "tokens": 0, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/training/trainer.py": {"lines": 628, "tokens": 5006, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/training/reward_functions.py": {"lines": 302, "tokens": 1644, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 176, "percentage": 7.95, "percentageTokens": 10.71, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/training/environment.py": {"lines": 268, "tokens": 2053, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/__init__.py": {"lines": 4, "tokens": 0, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/weight_optimizer.py": {"lines": 347, "tokens": 2364, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/reinforcement_learning.py": {"lines": 367, "tokens": 2710, "sources": 1, "clones": 4, "duplicatedLines": 56, "duplicatedTokens": 594, "percentage": 15.26, "percentageTokens": 21.92, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/base_model.py": {"lines": 256, "tokens": 1517, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/__init__.py": {"lines": 4, "tokens": 0, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/metrics.py": {"lines": 243, "tokens": 1817, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py": {"lines": 353, "tokens": 2666, "sources": 1, "clones": 6, "duplicatedLines": 110, "duplicatedTokens": 968, "percentage": 31.16, "percentageTokens": 36.31, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/api/websocket.py": {"lines": 196, "tokens": 1258, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/__init__.py": {"lines": 3, "tokens": 23, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/websocket.py": {"lines": 67, "tokens": 337, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/utils.py": {"lines": 93, "tokens": 504, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/order_management.py": {"lines": 249, "tokens": 1572, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/market_data.py": {"lines": 178, "tokens": 1181, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/client.py": {"lines": 152, "tokens": 1100, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance/account.py": {"lines": 209, "tokens": 1292, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/tests/strategies/test_strategy_selector_modularization.py": {"lines": 277, "tokens": 2094, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/utils/technical_analysis.py": {"lines": 241, "tokens": 3549, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/__init__.py": {"lines": 20, "tokens": 139, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/utils.py": {"lines": 183, "tokens": 1026, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/trend_following_strategy.py": {"lines": 393, "tokens": 3826, "sources": 1, "clones": 5, "duplicatedLines": 47, "duplicatedTokens": 705, "percentage": 11.96, "percentageTokens": 18.43, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py": {"lines": 474, "tokens": 4819, "sources": 1, "clones": 6, "duplicatedLines": 53, "duplicatedTokens": 810, "percentage": 11.18, "percentageTokens": 16.81, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/strategy_selector.py": {"lines": 354, "tokens": 2433, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/strategy_scoring.py": {"lines": 269, "tokens": 1834, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/market_analysis.py": {"lines": 55, "tokens": 281, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/grid_strategy.py": {"lines": 442, "tokens": 3894, "sources": 1, "clones": 1, "duplicatedLines": 6, "duplicatedTokens": 105, "percentage": 1.36, "percentageTokens": 2.7, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/execution_handler.py": {"lines": 327, "tokens": 2052, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/base_strategy.py": {"lines": 122, "tokens": 516, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/user_service.py": {"lines": 28, "tokens": 213, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/auth_service.py": {"lines": 67, "tokens": 494, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/account_service.py": {"lines": 141, "tokens": 930, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/repositories/user_repository.py": {"lines": 24, "tokens": 254, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/repositories/trade_repository.py": {"lines": 264, "tokens": 1833, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/repositories/order_repository.py": {"lines": 24, "tokens": 242, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/__init__.py": {"lines": 5, "tokens": 60, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/training_session.py": {"lines": 35, "tokens": 424, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/trade_status.py": {"lines": 12, "tokens": 76, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/trade_state_snapshot.py": {"lines": 9, "tokens": 118, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/market_data.py": {"lines": 36, "tokens": 525, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/managed_trade.py": {"lines": 30, "tokens": 525, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/base.py": {"lines": 3, "tokens": 19, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/models/api_models.py": {"lines": 13, "tokens": 91, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/__init__.py": {"lines": 5, "tokens": 0, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/feature_engineering.py": {"lines": 260, "tokens": 2213, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/data_collector.py": {"lines": 209, "tokens": 1661, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/ml_router.py": {"lines": 406, "tokens": 3016, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/main.py": {"lines": 198, "tokens": 1619, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/config.py": {"lines": 45, "tokens": 381, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/api_router.py": {"lines": 588, "tokens": 4456, "sources": 1, "clones": 4, "duplicatedLines": 56, "duplicatedTokens": 522, "percentage": 9.52, "percentageTokens": 11.71, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/config/settings.py": {"lines": 133, "tokens": 1428, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/config/app_config.py": {"lines": 78, "tokens": 554, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/api/binance_client.py": {"lines": 9, "tokens": 29, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/tests/test_database_initialization.py": {"lines": 24, "tokens": 176, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/scripts/init_database.py": {"lines": 57, "tokens": 265, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dependencies.py": {"lines": 82, "tokens": 410, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/database.py": {"lines": 84, "tokens": 517, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/main.py": {"lines": 70, "tokens": 491, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 13662, "tokens": 103677, "sources": 81, "clones": 28, "duplicatedLines": 323, "duplicatedTokens": 3537, "percentage": 2.36, "percentageTokens": 3.41, "newDuplicatedLines": 0, "newClones": 0}}, "url": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/rules/02_memory_bank_protocol.md": {"lines": 4, "tokens": 92, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/cursor_enhanced_rule_set.md": {"lines": 354, "tokens": 2588, "sources": 1, "clones": 1, "duplicatedLines": 354, "duplicatedTokens": 2588, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}, "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/# Cursor IDE AI Assistant - Enhance.txt": {"lines": 354, "tokens": 2588, "sources": 1, "clones": 1, "duplicatedLines": 354, "duplicatedTokens": 2588, "percentage": 100, "percentageTokens": 100, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 712, "tokens": 5268, "sources": 3, "clones": 1, "duplicatedLines": 354, "duplicatedTokens": 2588, "percentage": 49.72, "percentageTokens": 49.13, "newDuplicatedLines": 0, "newClones": 0}}, "powershell": {"sources": {"C:/Users/<USER>/Documents/Programming/Crypto_App_V2/run_app.ps1": {"lines": 244, "tokens": 1767, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 244, "tokens": 1767, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}}, "total": {"lines": 32353, "tokens": 269578, "sources": 250, "clones": 61, "duplicatedLines": 1822, "duplicatedTokens": 16094, "percentage": 5.63, "percentageTokens": 5.97, "newDuplicatedLines": 0, "newClones": 0}}, "duplicates": [{"format": "typescript", "lines": 12, "fragment": "();\r\n  if (!token) return true;\r\n\r\n  try {\r\n    const payload = parseJwt(token);\r\n    if (!payload || !payload.exp) return true;\r\n\r\n    // exp is in seconds, Date.now() is in milliseconds\r\n    const expirationTime = payload.exp * 1000;\r\n    const currentTime = Date.now();\r\n\r\n    return", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/tokenService.ts", "start": 124, "end": 135, "startLoc": {"line": 124, "column": 16, "position": 786}, "endLoc": {"line": 135, "column": 7, "position": 889}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/tokenService.ts", "start": 99, "end": 110, "startLoc": {"line": 99, "column": 15, "position": 593}, "endLoc": {"line": 110, "column": 77, "position": 696}}}, {"format": "javascript", "lines": 63, "fragment": "}\r\n                            size=\"small\"\r\n                          />\r\n                        </TableCell>\r\n                        <TableCell>{formatDateTime(trade.created_at)}</TableCell>\r\n                      </TableRow>\r\n                    ))}\r\n                  </TableBody>\r\n                </Table>\r\n              </TableContainer>\r\n            )}\r\n          </Paper>\r\n        </Grid>\r\n\r\n        {/* Recent Trades Section */}\r\n        <Grid item xs={12}>\r\n          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>\r\n            <Typography variant=\"h6\" gutterBottom>\r\n              Recent Trades\r\n            </Typography>\r\n             {loading ? (\r\n              <CircularProgress />\r\n            ) : error && recentTrades.length === 0 ? (\r\n              <Alert severity=\"error\">{error}</Alert>\r\n            ) : (\r\n              <TableContainer>\r\n                <Table size=\"small\">\r\n                  <TableHead>\r\n                    <TableRow>\r\n                      <TableCell>Symbol</TableCell>\r\n                      <TableCell>Side</TableCell>\r\n                      <TableCell>Entry Price</TableCell>\r\n                      <TableCell>Exit Price</TableCell>\r\n                      <TableCell>Quantity</TableCell>\r\n                      <TableCell>PnL</TableCell>\r\n                      <TableCell>Status</TableCell>\r\n                      <TableCell>Closed</TableCell>\r\n                    </TableRow>\r\n                  </TableHead>\r\n                  <TableBody>\r\n                    {recentTrades.map((trade) => {\r\n                      const pnl = calculatePnL(trade);\r\n                      return (\r\n                        <TableRow key={trade.trade_id}>\r\n                          <TableCell>{trade.symbol}</TableCell>\r\n                          <TableCell>\r\n                            <Chip\r\n                              label={trade.entry_side}\r\n                              color={trade.entry_side === 'BUY' ? 'success' : 'error'}\r\n                              size=\"small\"\r\n                            />\r\n                          </TableCell>\r\n                          <TableCell>{formatPrice(trade.entry_fill_price)}</TableCell>\r\n                          <TableCell>\r\n                            {trade.status === 'CLOSED_SL'\r\n                              ? formatPrice(trade.sl_price)\r\n                              : trade.status === 'CLOSED_TP'\r\n                                ? formatPrice(trade.tp_price)\r\n                                : 'N/A'}\r\n                          </TableCell>\r\n                          <TableCell>{trade.entry_fill_qty}</TableCell>\r\n                          <TableCell\r\n                            sx={{", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx", "start": 542, "end": 604, "startLoc": {"line": 542, "column": 2, "position": 5376}, "endLoc": {"line": 604, "column": 2, "position": 5946}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx", "start": 424, "end": 468, "startLoc": {"line": 424, "column": 2, "position": 4200}, "endLoc": {"line": 468, "column": 12, "position": 4647}}}, {"format": "tsx", "lines": 11, "fragment": ".length === 0 ? (\r\n              <Alert severity=\"error\">{error}</Alert>\r\n            ) : (\r\n              <TableContainer>\r\n                <Table size=\"small\">\r\n                  <TableHead>\r\n                    <TableRow>\r\n                      <TableCell>Symbol</TableCell>\r\n                      <TableCell>Side</TableCell>\r\n                      <TableCell>Entry Price</TableCell>\r\n                      <TableCell>Exit", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx", "start": 564, "end": 574, "startLoc": {"line": 564, "column": 13, "position": 5559}, "endLoc": {"line": 574, "column": 5, "position": 5663}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/components/TradeDashboard.tsx", "start": 433, "end": 443, "startLoc": {"line": 433, "column": 13, "position": 4304}, "endLoc": {"line": 443, "column": 8, "position": 4408}}}, {"format": "json", "lines": 25, "fragment": "{\n  \"short_name\": \"React App\",\n  \"name\": \"Create React App Sample\",\n  \"icons\": [\n    {\n      \"src\": \"favicon.ico\",\n      \"sizes\": \"64x64 32x32 24x24 16x16\",\n      \"type\": \"image/x-icon\"\n    },\n    {\n      \"src\": \"logo192.png\",\n      \"type\": \"image/png\",\n      \"sizes\": \"192x192\"\n    },\n    {\n      \"src\": \"logo512.png\",\n      \"type\": \"image/png\",\n      \"sizes\": \"512x512\"\n    }\n  ],\n  \"start_url\": \".\",\n  \"display\": \"standalone\",\n  \"theme_color\": \"#000000\",\n  \"background_color\": \"#ffffff\"\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/build/manifest.json", "start": 1, "end": 25, "startLoc": {"line": 1, "column": 1, "position": 0}, "endLoc": {"line": 25, "column": 2, "position": 133}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/public/manifest.json", "start": 1, "end": 25, "startLoc": {"line": 1, "column": 1, "position": 0}, "endLoc": {"line": 25, "column": 2, "position": 133}}}, {"format": "typescript", "lines": 21, "fragment": "return {\r\n          content: [\r\n            {\r\n              type: \"text\",\r\n              text: `Place a new spot TWAP order with Algo service successfully. result: ${JSON.stringify(result)}}`,\r\n            },\r\n          ],\r\n        };\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : String(error);\r\n        return {\r\n          content: [\r\n            { type: \"text\", text: `Server failed: ${errorMessage}` },\r\n          ],\r\n          isError: true,\r\n        };\r\n      }\r\n    }\r\n  );\r\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceSpotPlaceOrder.ts", "start": 29, "end": 49, "startLoc": {"line": 29, "column": 9, "position": 273}, "endLoc": {"line": 49, "column": 2, "position": 437}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceTimeWeightedAveragePriceFutureAlgo.ts", "start": 28, "end": 48, "startLoc": {"line": 28, "column": 9, "position": 269}, "endLoc": {"line": 48, "column": 2, "position": 433}}}, {"format": "typescript", "lines": 17, "fragment": ")}}`,\r\n            },\r\n          ],\r\n        };\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : String(error);\r\n        return {\r\n          content: [\r\n            { type: \"text\", text: `Server failed: ${errorMessage}` },\r\n          ],\r\n          isError: true,\r\n        };\r\n      }\r\n    }\r\n  );\r\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceOrderBook.ts", "start": 21, "end": 37, "startLoc": {"line": 21, "column": 10, "position": 190}, "endLoc": {"line": 37, "column": 2, "position": 319}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceTimeWeightedAveragePriceFutureAlgo.ts", "start": 32, "end": 48, "startLoc": {"line": 32, "column": 7, "position": 304}, "endLoc": {"line": 48, "column": 2, "position": 433}}}, {"format": "typescript", "lines": 17, "fragment": ",\r\n            },\r\n          ],\r\n        };\r\n      } catch (error) {\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : String(error);\r\n        return {\r\n          content: [\r\n            { type: \"text\", text: `Server failed: ${errorMessage}` },\r\n          ],\r\n          isError: true,\r\n        };\r\n      }\r\n    }\r\n  );\r\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceAccountInfo.ts", "start": 42, "end": 58, "startLoc": {"line": 42, "column": 2, "position": 490}, "endLoc": {"line": 58, "column": 2, "position": 616}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/src/tools/binanceTimeWeightedAveragePriceFutureAlgo.ts", "start": 32, "end": 48, "startLoc": {"line": 32, "column": 3, "position": 307}, "endLoc": {"line": 48, "column": 2, "position": 433}}}, {"format": "javascript", "lines": 22, "fragment": ",\n            });\n            return {\n                content: [\n                    {\n                        type: \"text\",\n                        text: `Place a new spot TWAP order with Algo service successfully. result: ${JSON.stringify(result)}}`,\n                    },\n                ],\n            };\n        }\n        catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            return {\n                content: [\n                    { type: \"text\", text: `Server failed: ${errorMessage}` },\n                ],\n                isError: true,\n            };\n        }\n    });\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceSpotPlaceOrder.js", "start": 17, "end": 38, "startLoc": {"line": 17, "column": 14, "position": 205}, "endLoc": {"line": 38, "column": 2, "position": 355}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceTimeWeightedAveragePriceFutureAlgo.js", "start": 16, "end": 37, "startLoc": {"line": 16, "column": 9, "position": 205}, "endLoc": {"line": 37, "column": 2, "position": 355}}}, {"format": "javascript", "lines": 16, "fragment": ")}}`,\n                    },\n                ],\n            };\n        }\n        catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            return {\n                content: [\n                    { type: \"text\", text: `Server failed: ${errorMessage}` },\n                ],\n                isError: true,\n            };\n        }\n    });\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceOrderBook.js", "start": 13, "end": 28, "startLoc": {"line": 13, "column": 10, "position": 148}, "endLoc": {"line": 28, "column": 2, "position": 259}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceTimeWeightedAveragePriceFutureAlgo.js", "start": 22, "end": 37, "startLoc": {"line": 22, "column": 7, "position": 244}, "endLoc": {"line": 37, "column": 2, "position": 355}}}, {"format": "javascript", "lines": 16, "fragment": ",\n                    },\n                ],\n            };\n        }\n        catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            return {\n                content: [\n                    { type: \"text\", text: `Server failed: ${errorMessage}` },\n                ],\n                isError: true,\n            };\n        }\n    });\n}", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceAccountInfo.js", "start": 36, "end": 51, "startLoc": {"line": 36, "column": 2, "position": 418}, "endLoc": {"line": 51, "column": 2, "position": 526}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/binance-mcp/build/tools/binanceTimeWeightedAveragePriceFutureAlgo.js", "start": 22, "end": 37, "startLoc": {"line": 22, "column": 3, "position": 247}, "endLoc": {"line": 37, "column": 2, "position": 355}}}, {"format": "python", "lines": 9, "fragment": "# Update trade in DB\n                        if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                            await self.trade_repository.update_trade(trade)\n                        else:\n                            async with self.db_session_factory() as session:\n                                from app.repositories.trade_repository import TradeRepository\n                                repo = TradeRepository(session)\n                                await repo.update_trade(trade)\n                except", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 256, "end": 264, "startLoc": {"line": 256, "column": 25, "position": 2098}, "endLoc": {"line": 264, "column": 7, "position": 2190}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 216, "end": 225, "startLoc": {"line": 216, "column": 25, "position": 1659}, "endLoc": {"line": 225, "column": 28, "position": 1752}}}, {"format": "python", "lines": 21, "fragment": ",\n                            trade_id=str(trade.trade_id)\n                        )\n\n                        # Update trade in DB\n                        if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                            await self.trade_repository.update_trade(trade)\n                        else:\n                            async with self.db_session_factory() as session:\n                                from app.repositories.trade_repository import TradeRepository\n                                repo = TradeRepository(session)\n                                await repo.update_trade(trade)\n\n                        # Remove from active trades\n                        if str(trade.trade_id) in self.active_trades:\n                            del self.active_trades[str(trade.trade_id)]\n\n                        # Notify listeners with enhanced trade details\n                        notification = {\n                            'trade_id': str(trade.trade_id),\n                            'status': TradeStatus.CLOSED_TP", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 281, "end": 301, "startLoc": {"line": 281, "column": 12, "position": 2337}, "endLoc": {"line": 301, "column": 10, "position": 2510}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 212, "end": 232, "startLoc": {"line": 212, "column": 12, "position": 1642}, "endLoc": {"line": 232, "column": 10, "position": 1815}}}, {"format": "python", "lines": 9, "fragment": ".value,\n                            'symbol': trade.symbol,\n                            'entry_side': trade.entry_side,\n                            'entry_price': float(trade.entry_fill_price) if trade.entry_fill_price else None,\n                            'entry_qty': float(trade.entry_fill_qty) if trade.entry_fill_qty else None,\n                            'sl_price': float(trade.sl_price) if trade.sl_price else None,\n                            'tp_price': float(trade.tp_price) if trade.tp_price else None,\n                            'timestamp': trade.updated_at.isoformat() if hasattr(trade.updated_at, 'isoformat') else str(trade.updated_at),\n                            'exit_price': float(trade.tp_price", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 301, "end": 309, "startLoc": {"line": 301, "column": 10, "position": 2511}, "endLoc": {"line": 309, "column": 9, "position": 2663}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 232, "end": 240, "startLoc": {"line": 232, "column": 10, "position": 1816}, "endLoc": {"line": 240, "column": 9, "position": 1968}}}, {"format": "python", "lines": 14, "fragment": "else None\n                        }\n\n                        # Try to get current market price for the symbol\n                        try:\n                            ticker = await self.exchange_client.get_ticker(trade.symbol)\n                            if ticker and 'lastPrice' in ticker:\n                                notification['current_price'] = float(ticker['lastPrice'])\n                        except Exception as price_e:\n                            logger.warning(f\"Could not get current price for {trade.symbol}: {price_e}\")\n\n                        self.trade_update_queue.put_nowait(notification)\n                    else:\n                        logger.warning(f\"TP order {trade.tp_order_id} is not open but not filled: {tp_order.get('status')}\"", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 309, "end": 322, "startLoc": {"line": 309, "column": 2, "position": 2672}, "endLoc": {"line": 322, "column": 84, "position": 2775}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 240, "end": 253, "startLoc": {"line": 240, "column": 2, "position": 1977}, "endLoc": {"line": 253, "column": 84, "position": 2080}}}, {"format": "python", "lines": 13, "fragment": ")\n                        # Handle other statuses (CANCELED, REJECTED, etc.)\n                        trade.status = TradeStatus.ERROR\n                        # Update trade in DB\n                        if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                            await self.trade_repository.update_trade(trade)\n                        else:\n                            async with self.db_session_factory() as session:\n                                from app.repositories.trade_repository import TradeRepository\n                                repo = TradeRepository(session)\n                                await repo.update_trade(trade)\n                except Exception as e:\n                    logger.error(f\"Error checking TP order {trade.tp_order_id}: {e}\"", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 322, "end": 334, "startLoc": {"line": 322, "column": 84, "position": 2776}, "endLoc": {"line": 334, "column": 51, "position": 2900}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 253, "end": 265, "startLoc": {"line": 253, "column": 84, "position": 2081}, "endLoc": {"line": 265, "column": 51, "position": 2205}}}, {"format": "python", "lines": 13, "fragment": "trade.status = TradeStatus.ERROR\n\n                # Update trade in DB\n                if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                    await self.trade_repository.update_trade(trade)\n                else:\n                    async with self.db_session_factory() as session:\n                        from app.repositories.trade_repository import TradeRepository\n                        repo = TradeRepository(session)\n                        await repo.update_trade(trade)\n\n        except Exception as e:\n            logger.error(f\"Error reconciling trade {trade.trade_id}: {e}\"", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 339, "end": 351, "startLoc": {"line": 339, "column": 17, "position": 2945}, "endLoc": {"line": 351, "column": 48, "position": 3065}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 255, "end": 265, "startLoc": {"line": 255, "column": 25, "position": 2087}, "endLoc": {"line": 265, "column": 51, "position": 2205}}}, {"format": "python", "lines": 8, "fragment": "if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                                        await self.trade_repository.update_trade(trade)\n                                    else:\n                                        async with self.db_session_factory() as session:\n                                            from app.repositories.trade_repository import TradeRepository\n                                            repo = TradeRepository(session)\n                                            await repo.update_trade(trade)\n                                    logger", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 517, "end": 524, "startLoc": {"line": 517, "column": 37, "position": 4459}, "endLoc": {"line": 524, "column": 7, "position": 4548}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 217, "end": 225, "startLoc": {"line": 217, "column": 25, "position": 1662}, "endLoc": {"line": 225, "column": 28, "position": 1752}}}, {"format": "python", "lines": 10, "fragment": "# Update the trade in the database\n                                    if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                                        await self.trade_repository.update_trade(trade)\n                                    else:\n                                        async with self.db_session_factory() as session:\n                                            from app.repositories.trade_repository import TradeRepository\n                                            repo = TradeRepository(session)\n                                            await repo.update_trade(trade)\n\n                                    # Remove from active trades if present", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 534, "end": 543, "startLoc": {"line": 534, "column": 37, "position": 4622}, "endLoc": {"line": 543, "column": 39, "position": 4715}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 516, "end": 225, "startLoc": {"line": 516, "column": 37, "position": 4456}, "endLoc": {"line": 225, "column": 28, "position": 1752}}}, {"format": "python", "lines": 9, "fragment": "# Update the trade in the database\n                                    if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                                        await self.trade_repository.update_trade(trade)\n                                    else:\n                                        async with self.db_session_factory() as session:\n                                            from app.repositories.trade_repository import TradeRepository\n                                            repo = TradeRepository(session)\n                                            await repo.update_trade(trade)\n                                    logger.info(f\"Updated trade {trade.trade_id} with partial fill details: price={price}, qty={qty}\"", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 558, "end": 566, "startLoc": {"line": 558, "column": 37, "position": 4850}, "endLoc": {"line": 566, "column": 85, "position": 4947}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 516, "end": 524, "startLoc": {"line": 516, "column": 37, "position": 4456}, "endLoc": {"line": 524, "column": 77, "position": 4553}}}, {"format": "python", "lines": 10, "fragment": "# Update trade in DB\n            if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                await self.trade_repository.update_trade(trade)\n            else:\n                async with self.db_session_factory() as session:\n                    from app.repositories.trade_repository import TradeRepository\n                    repo = TradeRepository(session)\n                    await repo.update_trade(trade)\n\n            # Cancel sibling order", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 577, "end": 586, "startLoc": {"line": 577, "column": 13, "position": 5026}, "endLoc": {"line": 586, "column": 23, "position": 5119}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 216, "end": 225, "startLoc": {"line": 216, "column": 25, "position": 1659}, "endLoc": {"line": 225, "column": 28, "position": 1752}}}, {"format": "python", "lines": 9, "fragment": ".value,\n                'symbol': trade.symbol,\n                'entry_side': trade.entry_side,\n                'entry_price': float(trade.entry_fill_price) if trade.entry_fill_price else None,\n                'entry_qty': float(trade.entry_fill_qty) if trade.entry_fill_qty else None,\n                'sl_price': float(trade.sl_price) if trade.sl_price else None,\n                'tp_price': float(trade.tp_price) if trade.tp_price else None,\n                'timestamp': trade.updated_at.isoformat() if hasattr(trade.updated_at, 'isoformat') else str(trade.updated_at),\n                'exit_price': float(trade.sl_price if", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 592, "end": 600, "startLoc": {"line": 592, "column": 7, "position": 5174}, "endLoc": {"line": 600, "column": 3, "position": 5328}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 232, "end": 240, "startLoc": {"line": 232, "column": 10, "position": 1816}, "endLoc": {"line": 240, "column": 2, "position": 1969}}}, {"format": "python", "lines": 14, "fragment": "else None\n            }\n\n            # Try to get current market price for the symbol\n            try:\n                ticker = await self.exchange_client.get_ticker(trade.symbol)\n                if ticker and 'lastPrice' in ticker:\n                    notification['current_price'] = float(ticker['lastPrice'])\n            except Exception as price_e:\n                logger.warning(f\"Could not get current price for {trade.symbol}: {price_e}\")\n            \n            self.trade_update_queue.put_nowait(notification)\n\n        except", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 600, "end": 613, "startLoc": {"line": 600, "column": 2, "position": 5357}, "endLoc": {"line": 613, "column": 7, "position": 5453}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/websocket_handler.py", "start": 240, "end": 252, "startLoc": {"line": 240, "column": 2, "position": 1977}, "endLoc": {"line": 252, "column": 5, "position": 2071}}}, {"format": "python", "lines": 9, "fragment": "if hasattr(self, 'trade_repository') and self.trade_repository is not None:\n                repo = self.trade_repository\n            else:\n                async with self.db_session_factory() as session:\n                    from app.repositories.trade_repository import TradeRepository\n                    repo = TradeRepository(session)\n            await repo.update_trade(trade)\n\n            return", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/trade_management.py", "start": 384, "end": 392, "startLoc": {"line": 384, "column": 13, "position": 2859}, "endLoc": {"line": 392, "column": 7, "position": 2946}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/execution/trade_management.py", "start": 173, "end": 181, "startLoc": {"line": 173, "column": 13, "position": 1143}, "endLoc": {"line": 181, "column": 33, "position": 1230}}}, {"format": "python", "lines": 13, "fragment": "if not self.client: await self._initialize_client()\r\n        if not self.client: raise ConnectionError(\"Binance client not initialized\")\r\n        if not order_id and not client_order_id:\r\n            raise ValueError(\"Either order_id or client_order_id must be provided.\")\r\n\r\n        params = {'symbol': symbol}\r\n        if order_id:\r\n            params['orderId'] = int(order_id) # API expects integer orderId\r\n        if client_order_id:\r\n            params['origClientOrderId'] = client_order_id\r\n\r\n        try:\r\n             # For futures, use futures_get_order", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/exchange/binance_client.py", "start": 282, "end": 294, "startLoc": {"line": 282, "column": 9, "position": 2719}, "endLoc": {"line": 294, "column": 37, "position": 2843}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/services/exchange/binance_client.py", "start": 253, "end": 265, "startLoc": {"line": 253, "column": 9, "position": 2432}, "endLoc": {"line": 265, "column": 40, "position": 2556}}}, {"format": "python", "lines": 13, "fragment": "# Calculate cumulative returns\n            cumulative_returns = np.cumprod(1 + returns_array) - 1\n\n            # Calculate running maximum\n            running_max = np.maximum.accumulate(cumulative_returns)\n\n            # Calculate drawdown\n            drawdown = (cumulative_returns - running_max) / (1 + running_max)\n\n            # Calculate maximum drawdown\n            max_drawdown = abs(np.min(drawdown))\n\n            return", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/training/reward_functions.py", "start": 200, "end": 212, "startLoc": {"line": 200, "column": 13, "position": 1073}, "endLoc": {"line": 212, "column": 7, "position": 1161}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/training/reward_functions.py", "start": 115, "end": 127, "startLoc": {"line": 115, "column": 17, "position": 608}, "endLoc": {"line": 127, "column": 25, "position": 696}}}, {"format": "python", "lines": 12, "fragment": "policy = kwargs.get('policy', 'MlpPolicy')\r\n            learning_rate = kwargs.get('learning_rate', 0.0003)\r\n            n_steps = kwargs.get('n_steps', 2048)\r\n            batch_size = kwargs.get('batch_size', 64)\r\n            n_epochs = kwargs.get('n_epochs', 10)\r\n            gamma = kwargs.get('gamma', 0.99)\r\n            gae_lambda = kwargs.get('gae_lambda', 0.95)\r\n            clip_range = kwargs.get('clip_range', 0.2)\r\n            ent_coef = kwargs.get('ent_coef', 0.01)\r\n            vf_coef = kwargs.get('vf_coef', 0.5)\r\n            max_grad_norm = kwargs.get('max_grad_norm', 0.5)\r\n            verbose", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/reinforcement_learning.py", "start": 120, "end": 131, "startLoc": {"line": 120, "column": 13, "position": 929}, "endLoc": {"line": 131, "column": 8, "position": 1105}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/reinforcement_learning.py", "start": 89, "end": 101, "startLoc": {"line": 89, "column": 17, "position": 617}, "endLoc": {"line": 101, "column": 36, "position": 795}}}, {"format": "python", "lines": 18, "fragment": ")\r\n\r\n            # Store hyperparameters in metadata\r\n            self.update_metadata('hyperparameters', {\r\n                'policy': policy,\r\n                'learning_rate': learning_rate,\r\n                'n_steps': n_steps,\r\n                'batch_size': batch_size,\r\n                'n_epochs': n_epochs,\r\n                'gamma': gamma,\r\n                'gae_lambda': gae_lambda,\r\n                'clip_range': clip_range,\r\n                'ent_coef': ent_coef,\r\n                'vf_coef': vf_coef,\r\n                'max_grad_norm': max_grad_norm\r\n            })\r\n\r\n            self.logger.info(f", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/reinforcement_learning.py", "start": 153, "end": 170, "startLoc": {"line": 153, "column": 13, "position": 1273}, "endLoc": {"line": 170, "column": 2, "position": 1394}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/models/reinforcement_learning.py", "start": 99, "end": 116, "startLoc": {"line": 99, "column": 4, "position": 789}, "endLoc": {"line": 116, "column": 37, "position": 910}}}, {"format": "python", "lines": 27, "fragment": "# Collect market data\n            self.logger.info(f\"Collecting market data for {symbol} ({timeframe})\")\n            market_data, strategy_performances = await self.data_collector.collect_training_data(\n                symbol=symbol,\n                timeframe=timeframe,\n                lookback_days=(end_date - start_date).days + 1\n            )\n            \n            if market_data.empty:\n                self.logger.warning(\"No market data collected for backtesting\")\n                return {'error': 'No market data collected'}\n            \n            # Filter data by date range\n            market_data = market_data[start_date:end_date]\n            \n            for strategy in strategy_performances:\n                strategy_performances[strategy] = strategy_performances[strategy][start_date:end_date]\n            \n            if market_data.empty:\n                self.logger.warning(\"No market data in specified date range\")\n                return {'error': 'No market data in specified date range'}\n            \n            # Extract features\n            self.logger.info(\"Extracting features from market data\")\n            features_df = self.feature_engineer.extract_market_features(market_data)\n            \n            # Create return array", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py", "start": 158, "end": 184, "startLoc": {"line": 158, "column": 13, "position": 1098}, "endLoc": {"line": 184, "column": 22, "position": 1299}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py", "start": 53, "end": 79, "startLoc": {"line": 53, "column": 13, "position": 279}, "endLoc": {"line": 79, "column": 22, "position": 480}}}, {"format": "python", "lines": 14, "fragment": "return_array = np.zeros((len(features_df), len(strategy_names)))\n            \n            for i, name in enumerate(strategy_names):\n                if name in strategy_performances and not strategy_performances[name].empty:\n                    # Align dates\n                    aligned_returns = pd.Series(0.0, index=features_df.index)\n                    if 'profit' in strategy_performances[name].columns:\n                        for date in strategy_performances[name].index:\n                            if date in aligned_returns.index:\n                                aligned_returns[date] = strategy_performances[name].loc[date, 'profit']\n                    \n                    return_array[:, i] = aligned_returns.values\n            \n            # Get optimized weights for each time step", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py", "start": 186, "end": 199, "startLoc": {"line": 186, "column": 13, "position": 1311}, "endLoc": {"line": 199, "column": 43, "position": 1477}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py", "start": 85, "end": 98, "startLoc": {"line": 85, "column": 13, "position": 550}, "endLoc": {"line": 98, "column": 30, "position": 716}}}, {"format": "python", "lines": 17, "fragment": "# Calculate portfolio returns\n            portfolio_returns = np.sum(weight_array * return_array, axis=1)\n            \n            # Calculate metrics\n            metrics = PerformanceMetrics.calculate_portfolio_metrics(portfolio_returns)\n            allocation_metrics = PerformanceMetrics.calculate_strategy_allocation_metrics(weight_array, return_array)\n            \n            # Combine metrics\n            metrics.update(allocation_metrics)\n            \n            # Create result\n            result = {\n                'symbol': symbol,\n                'timeframe': timeframe,\n                'start_date': start_date.isoformat(),\n                'end_date': end_date.isoformat(),\n                'model_path'", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py", "start": 223, "end": 239, "startLoc": {"line": 223, "column": 13, "position": 1774}, "endLoc": {"line": 239, "column": 13, "position": 1891}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/ml/evaluation/backtesting.py", "start": 98, "end": 114, "startLoc": {"line": 98, "column": 13, "position": 716}, "endLoc": {"line": 114, "column": 10, "position": 833}}}, {"format": "markdown", "lines": 33, "fragment": " METHODOLOGICAL INTEGRATION\r\n\r\nOptimization Round 13 focused on deepening the system's methodological foundations while maintaining strict isolation principles:\r\n\r\n1. **<PERSON> \"Think\" Tool Integration**\r\n   - Aligned CREATIVE mode with <PERSON>'s systematic problem-solving approach\r\n   - Implemented structured phases for problem decomposition\r\n   - Created visual process maps for methodology visualization\r\n   - Established clear parallels between methodologies for consistency\r\n\r\n2. **Mode-Specific Rule Isolation**\r\n   - Eliminated global rule dependencies for cleaner architecture\r\n   - Implemented strict mode-based rule containment\r\n   - Preserved global rule space for future extensibility\r\n   - Enhanced system modularity through isolation\r\n\r\n3. **Visual Process Mapping**\r\n   - Developed comprehensive mode-specific process maps\r\n   - Created hierarchical visualization of decision points\r\n   - Implemented cross-mode transition guidelines\r\n   - Established clear entry points and flow patterns\r\n\r\n4. **Architectural Documentation**\r\n   - Enhanced documentation clarity through visual aids\r\n   - Created explicit methodology comparisons\r\n   - Documented architectural decisions and rationales\r\n   - Established clear upgrade paths for users\r\n\r\n5. **Quality Assurance Integration**\r\n   - Implemented mode-specific QA checkpoints\r\n   - Created validation frameworks for each mode\r\n   - Established clear quality metrics and standards\r\n   - Developed systematic verification procedures\r\n\r\nThis optimization round represents a significant maturation of the Memory Bank system, establishing stronger methodological foundations while maintaining strict isolation principles. By aligning with established methodologies like <PERSON>'s \"Think\" tool while preserving modularity through mode-specific rules, the system achieves both theoretical rigor and practical flexibility. The introduction of comprehensive visual process maps further enhances usability while maintaining the system's co", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/11-methodological-integration.md", "start": 3, "end": 35, "startLoc": {"line": 3, "column": 1, "position": 4}, "endLoc": {"line": 35, "column": 12, "position": 516}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/optimization-journey/13-methodological-integration.md", "start": 3, "end": 35, "startLoc": {"line": 3, "column": 1, "position": 4}, "endLoc": {"line": 35, "column": 12, "position": 516}}}, {"format": "python", "lines": 6, "fragment": "# Calculate EMAs\r\n        df['EMA_SHORT'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_short)\r\n        df['EMA_MEDIUM'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_medium)\r\n        df['EMA_LONG'] = ta.calculate_ema(df['close'], timeperiod=self.settings.tf_ema_long)\r\n\r\n        result", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py", "start": 119, "end": 124, "startLoc": {"line": 119, "column": 9, "position": 984}, "endLoc": {"line": 124, "column": 7, "position": 1074}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/trend_following_strategy.py", "start": 65, "end": 70, "startLoc": {"line": 65, "column": 9, "position": 427}, "endLoc": {"line": 70, "column": 51, "position": 513}}}, {"format": "python", "lines": 12, "fragment": "if 'volume' in df.columns:\r\n            df['volume_ma'] = df['volume'].rolling(window=20).mean()\r\n            current_volume = df['volume'].iloc[-1]\r\n            avg_volume = df['volume_ma'].iloc[-1]\r\n\r\n            result['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 0\r\n            result['is_high_volume'] = result['volume_ratio'] > 1.5\r\n        else:\r\n            result['volume_ratio'] = 1.0\r\n            result['is_high_volume'] = False\r\n\r\n        # Count bullish and bearish signals", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py", "start": 170, "end": 181, "startLoc": {"line": 170, "column": 9, "position": 1680}, "endLoc": {"line": 181, "column": 36, "position": 1828}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/trend_following_strategy.py", "start": 119, "end": 129, "startLoc": {"line": 119, "column": 13, "position": 1095}, "endLoc": {"line": 129, "column": 7, "position": 1232}}}, {"format": "python", "lines": 15, "fragment": "# Make a copy of the data to avoid modifying the original\r\n        df = data.copy()\r\n\r\n        # Ensure correct types for TA-Lib\r\n        df['open'] = df['open'].astype(np.float64)\r\n        df['high'] = df['high'].astype(np.float64)\r\n        df['low'] = df['low'].astype(np.float64)\r\n        df['close'] = df['close'].astype(np.float64)\r\n        if 'volume' in df.columns:\r\n            df['volume'] = df['volume'].astype(np.float64)\r\n\r\n        # Initialize signal column\r\n        df['signal'] = 0\r\n\r\n        # Run market analysis first to get overall conditions", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py", "start": 232, "end": 246, "startLoc": {"line": 232, "column": 9, "position": 2182}, "endLoc": {"line": 246, "column": 54, "position": 2341}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/trend_following_strategy.py", "start": 164, "end": 178, "startLoc": {"line": 164, "column": 9, "position": 1654}, "endLoc": {"line": 178, "column": 42, "position": 1801}}}, {"format": "python", "lines": 12, "fragment": "# Ensure correct types\r\n        high_prices = data['high'].values.astype(np.float64)\r\n        low_prices = data['low'].values.astype(np.float64)\r\n        close_prices = data['close'].values.astype(np.float64)\r\n\r\n        # Calculate ATR for dynamic stop loss\r\n        if 'ATR' in data.columns and not data['ATR'].isnull().all():\r\n             # Use already calculated ATR if available and not NaN\r\n             atr = data['ATR'].iloc[-1]\r\n             if np.isnan(atr):\r\n                 # Recalculate using utility function if last value is NaN\r\n                 atr_series = ta.calculate_atr(data['high'], data['low'], data['close'], timeperiod=self.settings.atr_periods) # Use settings (Corrected attribute)", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py", "start": 356, "end": 367, "startLoc": {"line": 356, "column": 9, "position": 3663}, "endLoc": {"line": 367, "column": 37, "position": 3835}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/trend_following_strategy.py", "start": 297, "end": 308, "startLoc": {"line": 297, "column": 9, "position": 2900}, "endLoc": {"line": 308, "column": 15, "position": 3061}}}, {"format": "python", "lines": 7, "fragment": "atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0\r\n        else:\r\n            # Calculate using utility function if not present in input data\r\n            atr_series = ta.calculate_atr(data['high'], data['low'], data['close'], timeperiod=self.settings.atr_periods) # Use settings (Corrected attribute)\r\n            atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0\r\n\r\n        # Get risk multiple based on strategy", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py", "start": 368, "end": 374, "startLoc": {"line": 368, "column": 18, "position": 3839}, "endLoc": {"line": 374, "column": 38, "position": 3975}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/trend_following_strategy.py", "start": 309, "end": 315, "startLoc": {"line": 309, "column": 18, "position": 3064}, "endLoc": {"line": 315, "column": 53, "position": 3195}}}, {"format": "python", "lines": 7, "fragment": "# Volume analysis\r\n        if 'volume' in df.columns:\r\n            df['volume_ma'] = df['volume'].rolling(window=20).mean()\r\n            current_volume = df['volume'].iloc[-1]\r\n            avg_volume = df['volume_ma'].iloc[-1]\r\n            result['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 0\r\n            result['is_low_volume'", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/grid_strategy.py", "start": 81, "end": 87, "startLoc": {"line": 81, "column": 9, "position": 650}, "endLoc": {"line": 87, "column": 16, "position": 755}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/strategies/technical_analysis_strategy.py", "start": 169, "end": 124, "startLoc": {"line": 169, "column": 9, "position": 1676}, "endLoc": {"line": 124, "column": 17, "position": 1191}}}, {"format": "python", "lines": 14, "fragment": "trade_dict = trade.model_dump() if hasattr(trade, 'model_dump') else trade.__dict__\n\n                    # Ensure trade_id is a string\n                    if 'trade_id' in trade_dict and trade_dict['trade_id'] is not None:\n                        trade_dict['trade_id'] = str(trade_dict['trade_id'])\n\n                    # Convert Decimal values to float for JSON serialization\n                    for key, value in trade_dict.items():\n                        if hasattr(value, 'is_finite') and callable(getattr(value, 'is_finite')):\n                            trade_dict[key] = float(value)\n\n                    serialized_trades.append(trade_dict)\n\n                logger.info(f\"User {current_user.username} requested recent trades. Found {len(serialized_trades)} recent trades.\"", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/api_router.py", "start": 422, "end": 435, "startLoc": {"line": 422, "column": 25, "position": 3108}, "endLoc": {"line": 435, "column": 102, "position": 3252}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/api_router.py", "start": 291, "end": 304, "startLoc": {"line": 291, "column": 13, "position": 2140}, "endLoc": {"line": 304, "column": 102, "position": 2284}}}, {"format": "python", "lines": 16, "fragment": ", exc_info=True)\n        return [{\n            \"trade_id\": \"error\",\n            \"symbol\": \"N/A\",\n            \"entry_side\": \"N/A\",\n            \"entry_price\": 0,\n            \"entry_qty\": 0,\n            \"sl_price\": 0,\n            \"tp_price\": 0,\n            \"exit_price\": 0,\n            \"status\": \"ERROR\",\n            \"created_at\": datetime.now().isoformat(),\n            \"closed_at\": None,\n            \"pnl\": 0,\n            \"pnl_percentage\": 0,\n            \"message\": f\"Error fetching recent trades: {e}\"", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/api_router.py", "start": 477, "end": 492, "startLoc": {"line": 477, "column": 36, "position": 3549}, "endLoc": {"line": 492, "column": 36, "position": 3666}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/api_router.py", "start": 330, "end": 345, "startLoc": {"line": 330, "column": 36, "position": 2469}, "endLoc": {"line": 345, "column": 36, "position": 2586}}}, {"format": "markdown", "lines": 31, "fragment": "ech Context: [Project Name]\n\n## 1. Programming Languages\n- **Language 1**: (e.g., Python 3.9)\n  - **Key Libraries/Frameworks**: (e.g., Flask, Pandas, NumPy)\n- **Language 2**: (e.g., JavaScript ES6+)\n  - **Key Libraries/Frameworks**: (e.g., React 18, Node.js 18.x, Express.js)\n\n## 2. Datastores\n- **Primary Database**: (e.g., PostgreSQL 15 - Name, Version, Purpose)\n- **Caching System**: (e.g., Redis 7 - Purpose)\n- **Other Storage**: (e.g., S3 for file storage - Purpose)\n\n## 3. Development Environment & Tools\n- **IDE(s)**: (e.g., VS Code with specific extensions, PyCharm)\n- **Version Control**: (e.g., Git, GitHub/GitLab/Bitbucket - include repo URL if public or contextually safe)\n- **Build Tools**: (e.g., Webpack, Maven, Gradle)\n- **Dependency Management**: (e.g., npm, pip, Poetry)\n- **Containerization (if any)**: (e.g., Docker, Docker Compose - link to Dockerfile/compose file)\n\n## 4. Deployment & Infrastructure\n- **Hosting Provider(s)**: (e.g., AWS, Azure, Google Cloud, Netlify, Vercel)\n- **CI/CD Pipeline**: (e.g., GitHub Actions, Jenkins, GitLab CI - describe key stages)\n- **Operating System(s)**: (Development OS, Server OS)\n- **Web Server (if applicable)**: (e.g., Nginx, Apache)\n\n## 5. Key APIs & Integrations\n- **Internal API 1**: (Name, Purpose, Link to docs if available)\n- **External API 1**: (e.g., Stripe API for payments - Purpose, Link to docs)\n\n## 6.", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/tech_context_template.md", "start": 4, "end": 34, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 34, "column": 8, "position": 410}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/tech_context_template.md", "start": 4, "end": 34, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 34, "column": 8, "position": 410}}}, {"format": "markdown", "lines": 30, "fragment": "ystem Patterns: [Project Name]\n\n## 1. Architectural Style\n- **Overall Architecture**: (e.g., Microservices, Monolith, Layered, Event-Driven, etc. Describe the chosen style and rationale.)\n- **Key Components**: (Diagram or list major components and their interactions.)\n\n## 2. Common Design Patterns Used\n- **Pattern 1**: (e.g., Singleton, Factory, Observer)\n  - **Context/Problem**: (Where and why is it used?)\n  - **Solution**: (Brief description of its implementation in this project.)\n- **Pattern 2**:\n  - **Context/Problem**:\n  - **Solution**:\n\n## 3. Data Management\n- **Data Storage**: (e.g., PostgreSQL, MongoDB, Filesystem. Describe models/schemas if applicable.)\n- **Data Flow**: (How does data move through the system?)\n- **Caching Strategies**: (If any, describe them.)\n\n## 4. API Design (if applicable)\n- **Style**: (e.g., REST, GraphQL, gRPC)\n- **Authentication/Authorization**: (Methods used.)\n- **Versioning Strategy**:\n\n## 5. Error Handling & Logging\n- **Common Error Handling Approach**: (e.g., Centralized error handlers, specific exceptions.)\n- **Logging Strategy**: (What is logged, where, and format? Any specific logging libraries or services?)\n\n## 6. Scalability & Performa", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/system_patterns_template.md", "start": 4, "end": 33, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 33, "column": 13, "position": 345}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/system_patterns_template.md", "start": 4, "end": 33, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 33, "column": 13, "position": 345}}}, {"format": "markdown", "lines": 32, "fragment": "roject Progress: [Project Name] - Last Updated: YYYY-MM-DD\n\n## 1. Overall Status\n- **Current Phase**: (e.g., Development, Alpha, Beta, MVP Release)\n- **Overall Health**: (e.g., <PERSON>, Yellow, Red - with brief explanation)\n- **Next Major Milestone**: (Name and target date if known)\n\n## 2. Completed Milestones / Key Features\n- **Milestone 1**: [Name] - Completed: YYYY-MM-DD\n  - Key achievements/features delivered.\n- **Feature A**: [Name] - Completed: YYYY-MM-DD\n  - Description of functionality.\n\n## 3. Current Sprint / Iteration Goals (if applicable)\n- **Sprint Name/Number**:\n- **Start Date**: YYYY-MM-DD, **End Date**: YYYY-MM-DD\n- **Goals**:\n  - Goal 1:\n  - Goal 2:\n- **Progress**: (e.g., X% complete, specific tasks done/remaining)\n\n## 4. Ongoing Tasks\n- **Task 1**: [Description] - Assignee: [Name/Cascade], Status: [e.g., In Progress, Blocked]\n- **Task 2**: [Description] - Assignee: [Name/Cascade], Status: [e.g., In Progress, Blocked]\n\n## 5. Upcoming Tasks / Backlog Highlights\n- **Task A**: [Description] - Priority: [High/Medium", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/progress_template.md", "start": 4, "end": 35, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 35, "column": 3, "position": 311}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/progress_template.md", "start": 4, "end": 35, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 35, "column": 3, "position": 311}}}, {"format": "markdown", "lines": 22, "fragment": "roduct Context: [Project Name]\n\n## 1. Core Problem & User Need\n- **Problem Statement**: (What specific problem is this project solving?)\n- **Target Users**: (Who are the primary users? Describe their personas if known.)\n- **User Needs Met**: (How does this project address the users' needs?)\n\n## 2. Product Vision & Goals\n- **Product Vision**: (What is the long-term vision for this product?)\n- **Key Goals/Objectives**: (What are the measurable goals for this iteration/version?)\n- **Success Metrics**: (How will success be measured?)\n\n## 3. Key Features & Scope\n- **Core Features**: (List the essential features.)\n  - Feature 1: Description\n  - Feature 2: Description\n- **Out of Scope (for now)**: (What features are explicitly not being built at this stage?)\n\n## 4. Competitive Landscape (Optional)\n- **Key Competitors**: (Who are they?)\n- **Differentiation**: (How is this product different o", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/product_context_template.md", "start": 4, "end": 25, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 25, "column": 10, "position": 279}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/product_context_template.md", "start": 4, "end": 25, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 25, "column": 10, "position": 279}}}, {"format": "markdown", "lines": 19, "fragment": "ascade Personal Memory Entry - YYYY-MM-DD\n\n## Title / Subject\n- (A concise title for this memory entry)\n\n## Context / Background\n- (Briefly describe the situation or reason for this memory entry)\n\n## Key Information / Instruction for Cascade\n- (The specific piece of information, preference, or instruction you want <PERSON> to remember)\n- (Be explicit and clear)\n\n## Desired Behavior Change / Impact (if any)\n- (How should this information influence Cascade's future actions or responses within this project?)\n- (e.g., \"Always prefer X approach for Y tasks,", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/personal_memory_template.md", "start": 4, "end": 22, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 22, "column": 13, "position": 173}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/personal_memory_template.md", "start": 4, "end": 22, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 22, "column": 13, "position": 173}}}, {"format": "markdown", "lines": 25, "fragment": "LEMENTATION CHECKLIST: [Feature/Task Name]\n# Date: YYYY-MM-DD\n# Output from [MODE: PLAN]\n\n## Overall Goal:\n- (Brief description of what this checklist aims to achieve)\n\n## Pre-requisites / Assumptions:\n- (Any conditions that must be met before starting)\n- (Key assumptions made during planning)\n\n## Checklist Items:\n*   [ ] 1. **[Specific Action 1]**\n    *   File(s): `path/to/file.ext`\n    *   Details: (Brief notes on what needs to be done for this step)\n*   [ ] 2. **[Specific Action 2]**\n    *   File(s): `path/to/another/file.ext`, `module/name/`\n    *   Details:\n*   [ ] 3. **[Specific Action 3]**\n    *   Command(s): (If the action involves running a command)\n    *   Details:\n*   [ ] ...\n*   [ ] n. **[Final Action / Verification Step]**\n    *   Details: (e.g., \"Run all unit tests,\" \"Verify feature X works as expected on Y browser\")\n\n## Post-Implementat", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/implementation_checklist_template.md", "start": 6, "end": 30, "startLoc": {"line": 6, "column": 1, "position": 5}, "endLoc": {"line": 30, "column": 6, "position": 177}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/implementation_checklist_template.md", "start": 6, "end": 30, "startLoc": {"line": 6, "column": 1, "position": 5}, "endLoc": {"line": 30, "column": 6, "position": 177}}}, {"format": "markdown", "lines": 26, "fragment": "ascade AI Context Snapshot: [Project Name] - YYYY-MM-DD\n\n## 1. Project Goal\n- (Brief summary of the main objective as understood by Cascade)\n\n## 2. Current Task Assigned to Cascade\n- (Specific task Cascade is currently working on or has just completed)\n- **Status**: (e.g., In Progress, Blocked, Planning, Awaiting Feedback, Completed)\n\n## 3. Key Understandings by Cascade\n- (Bullet points of critical information Cascade has processed and retained relevant to the project/task)\n  - About the codebase structure/logic:\n  - About user preferences/intent:\n  - About project constraints/requirements:\n\n## 4. Recent Significant Actions by Cascade\n- (Last 2-3 major tool uses, analyses, or code modifications performed by Cascade)\n  - Action 1:\n  - Action 2:\n\n## 5. Pending Questions from Cascade for User\n-", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/ai_context_snapshot_template.md", "start": 4, "end": 29, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 29, "column": 3, "position": 263}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/ai_context_snapshot_template.md", "start": 4, "end": 29, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 29, "column": 3, "position": 263}}}, {"format": "markdown", "lines": 27, "fragment": "ctive Context: [Brief Task/Feature Name] - YYYY-MM-DD HH:MM\n\n## 1. Current Goal / Objective\n- (What is the immediate goal of the current work session or task?)\n\n## 2. Key Files / Modules Involved\n- `path/to/file1.ext` (Brief reason for focus)\n- `path/to/module2/` (Brief reason for focus)\n\n## 3. Current Cascade Mode\n- `[MODE: MODE_NAME]` (e.g., PLAN, EXECUTE, RESEARCH)\n\n## 4. Recent Findings / Decisions\n- (Bullet list of important discoveries or decisions made recently relevant to this task.)\n- (e.g., \"Decided to use X library for Y feature.\")\n- (e.g., \"Identified bug Z in module A.\")\n\n## 5. Blockers / Open Questions\n- (Any obstacles preventing progress?)\n- (Specific questions that need answers to proceed?)\n\n## 6. Immediate Next Steps (for Cascade)\n1. ", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.cursor/templates/active_context_template.md", "start": 4, "end": 30, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 30, "column": 7, "position": 249}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.roo/templates/active_context_template.md", "start": 4, "end": 30, "startLoc": {"line": 4, "column": 1, "position": 3}, "endLoc": {"line": 30, "column": 7, "position": 249}}}, {"format": "markdown", "lines": 13, "fragment": " Build a `PortfolioRiskMonitor` for:\r\n   - Tracking overall account exposure\r\n   - Monitoring combined risk metrics\r\n   - Triggering alerts when thresholds are exceeded\r\n   \r\n5. Create a `RiskCoordinator` to:\r\n   - Manage communication between risk components\r\n   - Provide centralized risk configuration\r\n   - Expose risk metrics to the dashboard\r\n   \r\n6. Implement strategy-specific risk adapters to:\r\n   - Define custom risk parameters per strategy\r\n   - Calculate strategy-specific risk metrics\r\n   - Integrate with the layered risk system\r\n", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md", "start": 242, "end": 254, "startLoc": {"line": 242, "column": 1, "position": 2896}, "endLoc": {"line": 254, "column": 6, "position": 3050}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md", "start": 132, "end": 144, "startLoc": {"line": 132, "column": 1, "position": 1515}, "endLoc": {"line": 144, "column": 13, "position": 1669}}}, {"format": "markdown", "lines": 16, "fragment": " \r\n2. Apply PWA enhancements:\r\n   - Create a service worker for offline capabilities\r\n   - Implement a web app manifest\r\n   - Enable push notifications for alerts\r\n   - Optimize for installability\r\n   \r\n3. Optimize performance for mobile:\r\n   - Implement lazy loading for data-heavy components\r\n   - Use efficient data formats for API requests\r\n   - Minimize render-blocking resources\r\n   - Reduce bundle sizes through code splitting\r\n   \r\n4. Create mobile-specific UI components:\r\n   - Bottom navigation bar for core functions\r\n   - Card-based UI for market information\r\n   - S", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md", "start": 348, "end": 363, "startLoc": {"line": 348, "column": 1, "position": 4274}, "endLoc": {"line": 363, "column": 10, "position": 4439}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md", "start": 132, "end": 147, "startLoc": {"line": 132, "column": 1, "position": 1515}, "endLoc": {"line": 147, "column": 5, "position": 1680}}}, {"format": "markdown", "lines": 10, "fragment": " Implement `ExchangeAdapters` for each supported exchange:\r\n   - Exchange-specific authentication\r\n   - Message format translation\r\n   - Exchange-specific error handling\r\n   - Rate limiting compliance\r\n   \r\n3. Develop a `DataStreamManager` that:\r\n   - Manages subscriptions to different market data streams\r\n   - Handles data distribution to consumers\r\n   - Implements message buffering and replay capabilities\r\n   - Provides stream health metrics\r\n   \r\n4. Create specialized `DataHandlers` for differe", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md", "start": 462, "end": 471, "startLoc": {"line": 462, "column": 1, "position": 5750}, "endLoc": {"line": 471, "column": 5, "position": 5889}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/activeContext.md", "start": 132, "end": 141, "startLoc": {"line": 132, "column": 1, "position": 1515}, "endLoc": {"line": 141, "column": 5, "position": 1654}}}, {"format": "markdown", "lines": 27, "fragment": "- Dynamically adjusts rule complexity based on task requirements\r\n- Preserves valuable context space for productive work\r\n\r\n```mermaid\r\ngraph TD\r\n    subgraph \"Old System\"\r\n        OldMain[All Rules Loaded at Start]\r\n    end\r\n    \r\n    subgraph \"New System\"\r\n        Entry[Entry Point]\r\n        Core[Core Rules]\r\n        Phase1[Phase 1 Rules]\r\n        Phase2[Phase 2 Rules]\r\n        Phase3[Phase 3 Rules]\r\n        \r\n        Entry --> Core\r\n        Core --> Phase1\r\n        Core --> Phase2\r\n        Core --> Phase3\r\n    end\r\n    \r\n    style OldMain fill:#ffcccc,stroke:#ff9999\r\n    style Entry fill:#ccffcc,stroke:#99ff99\r\n    style Core fill:#ccffcc,stroke:#99ff99\r\n    style Phase1 fill:#ccffcc,stroke:#99ff99\r\n    style Phase2 fill:#ccffcc,stroke:#99ff99\r\n    style Phase3 fill:#ccffcc,stroke:#99ff99\r\n```\r\n\r\n**Before**: All 25+ rules were loaded at initialization, consuming approximately 70% of the available context.\r\n\r\n**After**: Only 3-7 rules are loaded at any given time, reducing context usage to approximately 15-20%.\r\n\r\n### 2. Graph-Based Efficiency\r\n\r\nThe graph-based structure is a ", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/memory_bank_upgrade_guide.md", "start": 518, "end": 544, "startLoc": {"line": 518, "column": 1, "position": 5926}, "endLoc": {"line": 544, "column": 5, "position": 6171}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursor-memory-bank/memory_bank_upgrade_guide.md", "start": 66, "end": 90, "startLoc": {"line": 66, "column": 1, "position": 772}, "endLoc": {"line": 90, "column": 5, "position": 1013}}}, {"format": "javascript", "lines": 15, "fragment": "], {\n  stdio: 'inherit',\n  shell: true\n});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/sequentialthinking-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 122, "position": 32}, "endLoc": {"line": 19, "column": 2, "position": 126}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 23, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "javascript", "lines": 15, "fragment": "], {\n  stdio: 'inherit',\n  shell: true\n});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/postgres-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 73, "position": 44}, "endLoc": {"line": 19, "column": 2, "position": 138}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 23, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "javascript", "lines": 15, "fragment": "], {\n  stdio: 'inherit',\n  shell: true\n});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/memory-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 38, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 23, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "javascript", "lines": 15, "fragment": "], {\n  stdio: 'inherit',\n  shell: true\n});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/git-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 17, "position": 35}, "endLoc": {"line": 19, "column": 2, "position": 129}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 23, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "javascript", "lines": 15, "fragment": "], {\n  stdio: 'inherit',\n  shell: true\n});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/coincap-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 14, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 23, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "javascript", "lines": 15, "fragment": "], {\n  stdio: 'inherit',\n  shell: true\n});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/ccxt-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 21, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 5, "end": 19, "startLoc": {"line": 5, "column": 23, "position": 38}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "javascript", "lines": 12, "fragment": "});\n\n// Handle process exit\nchild.on('exit', (code) => {\n  process.exit(code);\n});\n\n// Handle errors\nchild.on('error', (err) => {\n  console.error('Failed to start MCP server:', err);\n  process.exit(1);\n});", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/binance-mcp.js", "start": 18, "end": 29, "startLoc": {"line": 18, "column": 1, "position": 133}, "endLoc": {"line": 29, "column": 2, "position": 209}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/.vscode/time-mcp.js", "start": 8, "end": 19, "startLoc": {"line": 8, "column": 1, "position": 56}, "endLoc": {"line": 19, "column": 2, "position": 132}}}, {"format": "url", "lines": 355, "fragment": "  - Parse these files to understand project context, architecture, and current status\r\n  - Acknowledge the loaded context with a brief confirmation\r\n- If the folder doesn't exist:\r\n  - Offer to create a fresh memory-bank structure\r\n  - Start by creating the projectbrief.md foundation document\r\n  - Ask if the user wants to provide basic information about themselves and the project\r\n  - Use this information to initialize the core memory files\r\n\r\n### CONTEXT CATEGORIZATION\r\n- Organize information into these categories:\r\n  - PROJECT_DETAILS: Technical specifications, requirements, architecture\r\n  - PERSONAL_PREFERENCES: User's coding style, communication preferences\r\n  - DECISIONS_MADE: Important choices and their rationales\r\n  - CURRENT_TASKS: Active work items and their status\r\n  - TECHNICAL_CONSTRAINTS: Limitations, dependencies, requirements\r\n  - CURRENT_MODE: Track which RIPER mode is currently active\r\n\r\n### RELEVANCE SCORING\r\n- Assign relevance scores to all important information using [RS:X] notation:\r\n  - [RS:5]: Critical information (current priorities, key preferences)\r\n  - [RS:4]: High importance (active tasks, recent decisions)\r\n  - [RS:3]: Moderate importance (general background, established patterns)\r\n  - [RS:2]: Background information (historical context, past decisions)\r\n  - [RS:1]: Peripheral information (minor details, dated information)\r\n- When context space is limited, prioritize higher-scored memories\r\n- Decrease scores of older information unless explicitly marked as critical\r\n\r\n### MEMORY BANK UPDATES\r\n- Memory Bank updates occur when:\r\n  1. Discovering new project patterns\r\n  2. After implementing significant changes\r\n  3. When user requests with **update memory bank** (MUST review ALL files)\r\n  4. When context needs clarification\r\n\r\n```mermaid\r\nflowchart TD\r\n    Start[Update Process]\r\n    \r\n    subgraph Process\r\n        P1[Review ALL Files]\r\n        P2[Document Current State]\r\n        P3[Clarify Next Steps]\r\n        P4[Update .cursorrules]\r\n        \r\n        P1 --> P2 --> P3 --> P4\r\n    end\r\n    \r\n    Start --> Process\r\n```\r\n\r\n- Autonomously update memory files with new information from conversations\r\n- Only ask the user about memorizing information when uncertain about its importance\r\n- Format as a structured, easy-to-copy block of text\r\n- Include timestamp and version information\r\n- Focus particularly on activeContext.md and progress.md as they track current state\r\n- Automatically save all implementation checklists created in PLAN mode\r\n\r\n### PROJECT INTELLIGENCE (.cursorrules)\r\n- The .cursorrules file serves as a learning journal for each project\r\n- Captures important patterns, preferences, and project intelligence\r\n\r\n```mermaid\r\nflowchart TD\r\n    Start{Discover New Pattern}\r\n    \r\n    subgraph Learn [Learning Process]\r\n        D1[Identify Pattern]\r\n        D2[Validate with User]\r\n        D3[Document in .cursorrules]\r\n    end\r\n    \r\n    subgraph Apply [Usage]\r\n        A1[Read .cursorrules]\r\n        A2[Apply Learned Patterns]\r\n        A3[Improve Future Work]\r\n    end\r\n    \r\n    Start --> Learn\r\n    Learn --> Apply\r\n```\r\n\r\n- What to capture:\r\n  - Critical implementation paths\r\n  - User preferences and workflow\r\n  - Project-specific patterns\r\n  - Known challenges\r\n  - Evolution of project decisions\r\n  - Tool usage patterns\r\n- Update the .cursorrules file when discovering new patterns or after significant work\r\n\r\n### CONTEXT RETRIEVAL\r\n- When user shares saved context, parse and integrate it immediately\r\n- Acknowledge successful loading with a brief confirmation\r\n- Reference specific context items when they become relevant\r\n- Provide context visualization when requested to show relationships\r\n\r\n## ENHANCED INTERACTION GUIDELINES\r\n\r\n### CONTEXT AWARENESS\r\n- Proactively reference relevant context when responding\r\n- Indicate when you're using previously established context\r\n- Ask for clarification when context seems contradictory\r\n- Make smart suggestions for mode transitions based on conversation flow\r\n\r\n### CONTINUOUS LEARNING\r\n- Update your understanding as new information emerges\r\n- Adjust relevance scores based on frequency of reference and recency\r\n- Identify patterns in user preferences and project requirements\r\n- Track progress through implementation checklists, marking items as complete\r\n\r\n### SESSION CONTINUITY\r\n- At the end of each session, provide a \"CONTINUE_FROM\" marker\r\n- Summarize where the conversation left off\r\n- List next steps or pending questions\r\n- Track which RIPER mode was last active\r\n\r\n### NATURAL LANGUAGE INTERACTION\r\n- Process user requests in natural language without requiring special commands\r\n- Automatically update memory files based on conversation content\r\n- Maintain context across sessions without explicit user instructions\r\n- Proactively use stored information to provide personalized assistance\r\n- Handle context management behind the scenes without user involvement\r\n- Only ask about memorizing information when uncertain about its importance\r\n\r\n## MEMORY BANK STRUCTURE\r\n\r\nWhen creating a new memory bank, establish this folder structure:\r\n```\r\nmemory-bank/\r\n├── README.md                      # Instructions for using memory files\r\n├── projectbrief.md                # Foundation document defining core requirements and goals\r\n├── productContext.md              # Why this project exists and problems it solves\r\n├── systemPatterns.md              # System architecture and key technical decisions\r\n├── techContext.md                 # Technologies used and development setup\r\n├── activeContext.md               # Current work focus and next steps\r\n├── progress.md                    # What works, what's left to build, and known issues\r\n├── personal-memory.md             # User's personal preferences and details\r\n└── implementation-plans/          # Saved PLAN mode checklists\r\n    └── README.md                  # Instructions for implementation plans\r\n```\r\n\r\n## CORE MEMORY FILES TEMPLATES\r\n\r\n### projectbrief.md Template\r\n```markdown\r\n# Project Brief: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Created: [CURRENT_DATE]*\r\n\r\n## Project Overview\r\n[Brief description of the project, its purpose, and main goals]\r\n\r\n## Core Requirements\r\n- [REQUIREMENT_1]\r\n- [REQUIREMENT_2]\r\n- [REQUIREMENT_3]\r\n\r\n## Success Criteria\r\n- [CRITERION_1]\r\n- [CRITERION_2]\r\n- [CRITERION_3]\r\n\r\n## Scope\r\n### In Scope\r\n- [IN_SCOPE_ITEM_1]\r\n- [IN_SCOPE_ITEM_2]\r\n\r\n### Out of Scope\r\n- [OUT_OF_SCOPE_ITEM_1]\r\n- [OUT_OF_SCOPE_ITEM_2]\r\n\r\n## Timeline\r\n- [MILESTONE_1]: [DATE]\r\n- [MILESTONE_2]: [DATE]\r\n- [MILESTONE_3]: [DATE]\r\n\r\n## Stakeholders\r\n- [STAKEHOLDER_1]: [ROLE]\r\n- [STAKEHOLDER_2]: [ROLE]\r\n\r\n---\r\n\r\n*This document serves as the foundation for the project and informs all other memory files.*\r\n```\r\n\r\n### productContext.md Template\r\n```markdown\r\n# Product Context: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Problem Statement\r\n[Description of the problem the product aims to solve]\r\n\r\n## User Personas\r\n### [PERSONA_1]\r\n- Demographics: [DEMOGRAPHICS]\r\n- Goals: [GOALS]\r\n- Pain Points: [PAIN_POINTS]\r\n\r\n### [PERSONA_2]\r\n- Demographics: [DEMOGRAPHICS]\r\n- Goals: [GOALS]\r\n- Pain Points: [PAIN_POINTS]\r\n\r\n## User Experience Goals\r\n- [UX_GOAL_1]\r\n- [UX_GOAL_2]\r\n- [UX_GOAL_3]\r\n\r\n## Key Features\r\n- [FEATURE_1]: [DESCRIPTION]\r\n- [FEATURE_2]: [DESCRIPTION]\r\n- [FEATURE_3]: [DESCRIPTION]\r\n\r\n## Success Metrics\r\n- [METRIC_1]: [TARGET]\r\n- [METRIC_2]: [TARGET]\r\n- [METRIC_3]: [TARGET]\r\n\r\n---\r\n\r\n*This document explains why the project exists and what problems it solves.*\r\n```\r\n\r\n### systemPatterns.md Template\r\n```markdown\r\n# System Patterns: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Architecture Overview\r\n[High-level description of the system architecture]\r\n\r\n## Key Components\r\n- [COMPONENT_1]: [PURPOSE]\r\n- [COMPONENT_2]: [PURPOSE]\r\n- [COMPONENT_3]: [PURPOSE]\r\n\r\n## Design Patterns in Use\r\n- [PATTERN_1]: [USAGE_CONTEXT]\r\n- [PATTERN_2]: [USAGE_CONTEXT]\r\n- [PATTERN_3]: [USAGE_CONTEXT]\r\n\r\n## Data Flow\r\n[Description or diagram of how data flows through the system]\r\n\r\n## Key Technical Decisions\r\n- [DECISION_1]: [RATIONALE]\r\n- [DECISION_2]: [RATIONALE]\r\n- [DECISION_3]: [RATIONALE]\r\n\r\n## Component Relationships\r\n[Description of how components interact with each other]\r\n\r\n---\r\n\r\n*This document captures the system architecture and design patterns used in the project.*\r\n```\r\n\r\n### techContext.md Template\r\n```markdown\r\n# Technical Context: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Technology Stack\r\n- Frontend: [FRONTEND_TECHNOLOGIES]\r\n- Backend: [BACKEND_TECHNOLOGIES]\r\n- Database: [DATABASE_TECHNOLOGIES]\r\n- Infrastructure: [INFRASTRUCTURE_TECHNOLOGIES]\r\n\r\n## Development Environment Setup\r\n[Instructions for setting up the development environment]\r\n\r\n## Dependencies\r\n- [DEPENDENCY_1]: [VERSION] - [PURPOSE]\r\n- [DEPENDENCY_2]: [VERSION] - [PURPOSE]\r\n- [DEPE", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/# Cursor IDE AI Assistant - Enhance.txt", "start": 485, "end": 839, "startLoc": {"line": 485, "column": 2, "position": 5770}, "endLoc": {"line": 839, "column": 12, "position": 8358}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/cursor_enhanced_rule_set.md", "start": 152, "end": 506, "startLoc": {"line": 152, "column": 2, "position": 2140}, "endLoc": {"line": 506, "column": 12, "position": 4728}}}, {"format": "markdown", "lines": 6, "fragment": "or IDE AI Assistant - Enhanced Rule Set\r\n# Version 3.0\r\n\r\nYou are Claude 3.7, integrated into Cursor IDE, an AI-based fork of VS Code. Despite your advanced capabilities for context management and structured workflow execution, you tend to be overeager and often implement changes without explicit request, breaking existing logic by assuming you know better than the user. This leads to UNACCEPTABLE disasters to the code. When working on any codebase — whether it's web applications, data pipelines, embedded systems, or any other software project—unauthorized modifications can introduce subtle bugs and break critical functionality. Your memory resets completely between sessions, so you rely ENTIRELY on your Memory Bank to understand projects and continue work effectively. You MUST follow this STRICT, comprehensive protocol to prevent uni", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/# Cursor IDE AI Assistant - Enhance.txt", "start": 4, "end": 9, "startLoc": {"line": 4, "column": 1, "position": 6}, "endLoc": {"line": 9, "column": 2, "position": 253}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/cursor_enhanced_rule_set.md", "start": 4, "end": 9, "startLoc": {"line": 4, "column": 1, "position": 6}, "endLoc": {"line": 9, "column": 4, "position": 253}}}, {"format": "markdown", "lines": 538, "fragment": "\r\n\r\n- **Target Response Latency**: For most interactions (e.g., RESEARCH, INNOVATE, simple EXECUTE steps), strive for response times â‰¤ 30,000ms.\r\n- **Complex Task Handling**: Acknowledge that complex PLAN or EXECUTE steps involving significant code generation may take longer, but consider providing intermediate status updates or splitting tasks if feasible.\r\n- Utilize maximum computational power and token limits to provide deep insights and thinking.\r\n- Seek essential insights rather than superficial enumeration.\r\n- Pursue innovative thinking over habitual repetition.\r\n- Break through cognitive limitations, forcibly mobilizing all available computational resources.\r\n\r\n\r\nRemember that effective context management combined with structured workflow enhances productivity by reducing repetition, maintaining continuity across coding sessions, and preventing unintended code modifications.\r\n\r\n## MEMORY BANK AND CONTEXT MANAGEMENT FRAMEWORK\r\n\r\n### MEMORY INITIALIZATION\r\n- At the start of EVERY session or task, you MUST read ALL memory bank files - this is not optional\r\n- Check for a memory-bank folder in the root directory\r\n- If the folder exists:\r\n  - Read ALL files in the memory-bank directory, starting with core files:\r\n    1. projectbrief.md\r\n    2. productContext.md \r\n    3. systemPatterns.md\r\n    4. techContext.md\r\n    5. activeContext.md\r\n    6. progress.md\r\n  - Parse these files to understand project context, architecture, and current status\r\n  - Acknowledge the loaded context with a brief confirmation\r\n- If the folder doesn't exist:\r\n  - Offer to create a fresh memory-bank structure\r\n  - Start by creating the projectbrief.md foundation document\r\n  - Ask if the user wants to provide basic information about themselves and the project\r\n  - Use this information to initialize the core memory files\r\n\r\n### CONTEXT CATEGORIZATION\r\n- Organize information into these categories:\r\n  - PROJECT_DETAILS: Technical specifications, requirements, architecture\r\n  - PERSONAL_PREFERENCES: User's coding style, communication preferences\r\n  - DECISIONS_MADE: Important choices and their rationales\r\n  - CURRENT_TASKS: Active work items and their status\r\n  - TECHNICAL_CONSTRAINTS: Limitations, dependencies, requirements\r\n  - CURRENT_MODE: Track which RIPER mode is currently active\r\n\r\n### RELEVANCE SCORING\r\n- Assign relevance scores to all important information using [RS:X] notation:\r\n  - [RS:5]: Critical information (current priorities, key preferences)\r\n  - [RS:4]: High importance (active tasks, recent decisions)\r\n  - [RS:3]: Moderate importance (general background, established patterns)\r\n  - [RS:2]: Background information (historical context, past decisions)\r\n  - [RS:1]: Peripheral information (minor details, dated information)\r\n- When context space is limited, prioritize higher-scored memories\r\n- Decrease scores of older information unless explicitly marked as critical\r\n\r\n### MEMORY BANK UPDATES\r\n- Memory Bank updates occur when:\r\n  1. Discovering new project patterns\r\n  2. After implementing significant changes\r\n  3. When user requests with **update memory bank** (MUST review ALL files)\r\n  4. When context needs clarification\r\n\r\n```mermaid\r\nflowchart TD\r\n    Start[Update Process]\r\n    \r\n    subgraph Process\r\n        P1[Review ALL Files]\r\n        P2[Document Current State]\r\n        P3[Clarify Next Steps]\r\n        P4[Update .cursorrules]\r\n        \r\n        P1 --> P2 --> P3 --> P4\r\n    end\r\n    \r\n    Start --> Process\r\n```\r\n\r\n- Autonomously update memory files with new information from conversations\r\n- Only ask the user about memorizing information when uncertain about its importance\r\n- Format as a structured, easy-to-copy block of text\r\n- Include timestamp and version information\r\n- Focus particularly on activeContext.md and progress.md as they track current state\r\n- Automatically save all implementation checklists created in PLAN mode\r\n\r\n### PROJECT INTELLIGENCE (.cursorrules)\r\n- The .cursorrules file serves as a learning journal for each project\r\n- Captures important patterns, preferences, and project intelligence\r\n\r\n```mermaid\r\nflowchart TD\r\n    Start{Discover New Pattern}\r\n    \r\n    subgraph Learn [Learning Process]\r\n        D1[Identify Pattern]\r\n        D2[Validate with User]\r\n        D3[Document in .cursorrules]\r\n    end\r\n    \r\n    subgraph Apply [Usage]\r\n        A1[Read .cursorrules]\r\n        A2[Apply Learned Patterns]\r\n        A3[Improve Future Work]\r\n    end\r\n    \r\n    Start --> Learn\r\n    Learn --> Apply\r\n```\r\n\r\n- What to capture:\r\n  - Critical implementation paths\r\n  - User preferences and workflow\r\n  - Project-specific patterns\r\n  - Known challenges\r\n  - Evolution of project decisions\r\n  - Tool usage patterns\r\n- Update the .cursorrules file when discovering new patterns or after significant work\r\n\r\n### CONTEXT RETRIEVAL\r\n- When user shares saved context, parse and integrate it immediately\r\n- Acknowledge successful loading with a brief confirmation\r\n- Reference specific context items when they become relevant\r\n- Provide context visualization when requested to show relationships\r\n\r\n## ENHANCED INTERACTION GUIDELINES\r\n\r\n### CONTEXT AWARENESS\r\n- Proactively reference relevant context when responding\r\n- Indicate when you're using previously established context\r\n- Ask for clarification when context seems contradictory\r\n- Make smart suggestions for mode transitions based on conversation flow\r\n\r\n### CONTINUOUS LEARNING\r\n- Update your understanding as new information emerges\r\n- Adjust relevance scores based on frequency of reference and recency\r\n- Identify patterns in user preferences and project requirements\r\n- Track progress through implementation checklists, marking items as complete\r\n\r\n### SESSION CONTINUITY\r\n- At the end of each session, provide a \"CONTINUE_FROM\" marker\r\n- Summarize where the conversation left off\r\n- List next steps or pending questions\r\n- Track which RIPER mode was last active\r\n\r\n### NATURAL LANGUAGE INTERACTION\r\n- Process user requests in natural language without requiring special commands\r\n- Automatically update memory files based on conversation content\r\n- Maintain context across sessions without explicit user instructions\r\n- Proactively use stored information to provide personalized assistance\r\n- Handle context management behind the scenes without user involvement\r\n- Only ask about memorizing information when uncertain about its importance\r\n\r\n## MEMORY BANK STRUCTURE\r\n\r\nWhen creating a new memory bank, establish this folder structure:\r\n```\r\nmemory-bank/\r\n├── README.md                      # Instructions for using memory files\r\n├── projectbrief.md                # Foundation document defining core requirements and goals\r\n├── productContext.md              # Why this project exists and problems it solves\r\n├── systemPatterns.md              # System architecture and key technical decisions\r\n├── techContext.md                 # Technologies used and development setup\r\n├── activeContext.md               # Current work focus and next steps\r\n├── progress.md                    # What works, what's left to build, and known issues\r\n├── personal-memory.md             # User's personal preferences and details\r\n└── implementation-plans/          # Saved PLAN mode checklists\r\n    └── README.md                  # Instructions for implementation plans\r\n```\r\n\r\n## CORE MEMORY FILES TEMPLATES\r\n\r\n### projectbrief.md Template\r\n```markdown\r\n# Project Brief: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Created: [CURRENT_DATE]*\r\n\r\n## Project Overview\r\n[Brief description of the project, its purpose, and main goals]\r\n\r\n## Core Requirements\r\n- [REQUIREMENT_1]\r\n- [REQUIREMENT_2]\r\n- [REQUIREMENT_3]\r\n\r\n## Success Criteria\r\n- [CRITERION_1]\r\n- [CRITERION_2]\r\n- [CRITERION_3]\r\n\r\n## Scope\r\n### In Scope\r\n- [IN_SCOPE_ITEM_1]\r\n- [IN_SCOPE_ITEM_2]\r\n\r\n### Out of Scope\r\n- [OUT_OF_SCOPE_ITEM_1]\r\n- [OUT_OF_SCOPE_ITEM_2]\r\n\r\n## Timeline\r\n- [MILESTONE_1]: [DATE]\r\n- [MILESTONE_2]: [DATE]\r\n- [MILESTONE_3]: [DATE]\r\n\r\n## Stakeholders\r\n- [STAKEHOLDER_1]: [ROLE]\r\n- [STAKEHOLDER_2]: [ROLE]\r\n\r\n---\r\n\r\n*This document serves as the foundation for the project and informs all other memory files.*\r\n```\r\n\r\n### productContext.md Template\r\n```markdown\r\n# Product Context: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Problem Statement\r\n[Description of the problem the product aims to solve]\r\n\r\n## User Personas\r\n### [PERSONA_1]\r\n- Demographics: [DEMOGRAPHICS]\r\n- Goals: [GOALS]\r\n- Pain Points: [PAIN_POINTS]\r\n\r\n### [PERSONA_2]\r\n- Demographics: [DEMOGRAPHICS]\r\n- Goals: [GOALS]\r\n- Pain Points: [PAIN_POINTS]\r\n\r\n## User Experience Goals\r\n- [UX_GOAL_1]\r\n- [UX_GOAL_2]\r\n- [UX_GOAL_3]\r\n\r\n## Key Features\r\n- [FEATURE_1]: [DESCRIPTION]\r\n- [FEATURE_2]: [DESCRIPTION]\r\n- [FEATURE_3]: [DESCRIPTION]\r\n\r\n## Success Metrics\r\n- [METRIC_1]: [TARGET]\r\n- [METRIC_2]: [TARGET]\r\n- [METRIC_3]: [TARGET]\r\n\r\n---\r\n\r\n*This document explains why the project exists and what problems it solves.*\r\n```\r\n\r\n### systemPatterns.md Template\r\n```markdown\r\n# System Patterns: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Architecture Overview\r\n[High-level description of the system architecture]\r\n\r\n## Key Components\r\n- [COMPONENT_1]: [PURPOSE]\r\n- [COMPONENT_2]: [PURPOSE]\r\n- [COMPONENT_3]: [PURPOSE]\r\n\r\n## Design Patterns in Use\r\n- [PATTERN_1]: [USAGE_CONTEXT]\r\n- [PATTERN_2]: [USAGE_CONTEXT]\r\n- [PATTERN_3]: [USAGE_CONTEXT]\r\n\r\n## Data Flow\r\n[Description or diagram of how data flows through the system]\r\n\r\n## Key Technical Decisions\r\n- [DECISION_1]: [RATIONALE]\r\n- [DECISION_2]: [RATIONALE]\r\n- [DECISION_3]: [RATIONALE]\r\n\r\n## Component Relationships\r\n[Description of how components interact with each other]\r\n\r\n---\r\n\r\n*This document captures the system architecture and design patterns used in the project.*\r\n```\r\n\r\n### techContext.md Template\r\n```markdown\r\n# Technical Context: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Technology Stack\r\n- Frontend: [FRONTEND_TECHNOLOGIES]\r\n- Backend: [BACKEND_TECHNOLOGIES]\r\n- Database: [DATABASE_TECHNOLOGIES]\r\n- Infrastructure: [INFRASTRUCTURE_TECHNOLOGIES]\r\n\r\n## Development Environment Setup\r\n[Instructions for setting up the development environment]\r\n\r\n## Dependencies\r\n- [DEPENDENCY_1]: [VERSION] - [PURPOSE]\r\n- [DEPENDENCY_2]: [VERSION] - [PURPOSE]\r\n- [DEPENDENCY_3]: [VERSION] - [PURPOSE]\r\n\r\n## Technical Constraints\r\n- [CONSTRAINT_1]\r\n- [CONSTRAINT_2]\r\n- [CONSTRAINT_3]\r\n\r\n## Build and Deployment\r\n- Build Process: [BUILD_PROCESS]\r\n- Deployment Procedure: [DEPLOYMENT_PROCEDURE]\r\n- CI/CD: [CI_CD_SETUP]\r\n\r\n## Testing Approach\r\n- Unit Testing: [UNIT_TESTING_APPROACH]\r\n- Integration Testing: [INTEGRATION_TESTING_APPROACH]\r\n- E2E Testing: [E2E_TESTING_APPROACH]\r\n\r\n---\r\n\r\n*This document describes the technologies used in the project and how they're configured.*\r\n```\r\n\r\n### activeContext.md Template\r\n```markdown\r\n# Active Context: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n*Current RIPER Mode: [MODE_NAME]*\r\n\r\n## Current Focus\r\n[Description of what we're currently working on]\r\n\r\n## Recent Changes\r\n- [CHANGE_1]: [DATE] - [DESCRIPTION]\r\n- [CHANGE_2]: [DATE] - [DESCRIPTION]\r\n- [CHANGE_3]: [DATE] - [DESCRIPTION]\r\n\r\n## Active Decisions\r\n- [DECISION_1]: [STATUS] - [DESCRIPTION]\r\n- [DECISION_2]: [STATUS] - [DESCRIPTION]\r\n- [DECISION_3]: [STATUS] - [DESCRIPTION]\r\n\r\n## Next Steps\r\n1. [NEXT_STEP_1]\r\n2. [NEXT_STEP_2]\r\n3. [NEXT_STEP_3]\r\n\r\n## Current Challenges\r\n- [CHALLENGE_1]: [DESCRIPTION]\r\n- [CHALLENGE_2]: [DESCRIPTION]\r\n- [CHALLENGE_3]: [DESCRIPTION]\r\n\r\n## Implementation Progress\r\n- [✓] [COMPLETED_TASK_1]\r\n- [✓] [COMPLETED_TASK_2]\r\n- [ ] [PENDING_TASK_1]\r\n- [ ] [PENDING_TASK_2]\r\n\r\n---\r\n\r\n*This document captures the current state of work and immediate next steps.*\r\n```\r\n\r\n### progress.md Template\r\n```markdown\r\n# Progress Tracker: [PROJECT_NAME]\r\n*Version: 1.0*\r\n*Updated: [CURRENT_DATE]*\r\n\r\n## Project Status\r\nOverall Completion: [PERCENTAGE]%\r\n\r\n## What Works\r\n- [FEATURE_1]: [COMPLETION_STATUS] - [NOTES]\r\n- [FEATURE_2]: [COMPLETION_STATUS] - [NOTES]\r\n- [FEATURE_3]: [COMPLETION_STATUS] - [NOTES]\r\n\r\n## What's In Progress\r\n- [FEATURE_4]: [PROGRESS_PERCENTAGE]% - [NOTES]\r\n- [FEATURE_5]: [PROGRESS_PERCENTAGE]% - [NOTES]\r\n- [FEATURE_6]: [PROGRESS_PERCENTAGE]% - [NOTES]\r\n\r\n## What's Left To Build\r\n- [FEATURE_7]: [PRIORITY] - [NOTES]\r\n- [FEATURE_8]: [PRIORITY] - [NOTES]\r\n- [FEATURE_9]: [PRIORITY] - [NOTES]\r\n\r\n## Known Issues\r\n- [ISSUE_1]: [SEVERITY] - [DESCRIPTION] - [STATUS]\r\n- [ISSUE_2]: [SEVERITY] - [DESCRIPTION] - [STATUS]\r\n- [ISSUE_3]: [SEVERITY] - [DESCRIPTION] - [STATUS]\r\n\r\n## Milestones\r\n- [MILESTONE_1]: [DUE_DATE] - [STATUS]\r\n- [MILESTONE_2]: [DUE_DATE] - [STATUS]\r\n- [MILESTONE_3]: [DUE_DATE] - [STATUS]\r\n\r\n---\r\n\r\n*This document tracks what works, what's in progress, and what's left to build.*\r\n```\r\n\r\n## CONTEXT SNAPSHOT TEMPLATE\r\n\r\nWhen generating a context snapshot, use this template:\r\n```\r\n# AI Context Snapshot\r\n*Version: 1.0*\r\n*Generated: [CURRENT_DATE]*\r\n*Current RIPER Mode: [MODE_NAME]*\r\n\r\n## PROJECT_DETAILS\r\n- [RS:5] Project Name: [PROJECT_NAME]\r\n- [RS:4] Framework: [FRAMEWORK]\r\n- [RS:4] Timeline: [TIMELINE]\r\n- [RS:3] Architecture: [ARCHITECTURE]\r\n\r\n## PERSONAL_PREFERENCES\r\n- [RS:5] Communication: [COMMUNICATION_STYLE]\r\n- [RS:4] Code Style: [CODE_STYLE]\r\n- [RS:4] Feedback Style: [FEEDBACK_STYLE]\r\n- [RS:3] Documentation: [DOCUMENTATION_PREFERENCES]\r\n\r\n## DECISIONS_MADE\r\n- [RS:5] [RECENT_DECISION] - Rationale: [DECISION_RATIONALE]\r\n- [RS:4] [IMPORTANT_DECISION] - Rationale: [DECISION_RATIONALE]\r\n- [RS:3] [EARLIER_DECISION] - Rationale: [DECISION_RATIONALE]\r\n\r\n## CURRENT_TASKS\r\n- [RS:5] [HIGHEST_PRIORITY_TASK]\r\n- [R", "tokens": 0, "firstFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/# Cursor IDE AI Assistant - Enhance.txt", "start": 456, "end": 993, "startLoc": {"line": 456, "column": 1, "position": 5359}, "endLoc": {"line": 993, "column": 2, "position": 9347}}, "secondFile": {"name": "C:/Users/<USER>/Documents/Programming/Crypto_App_V2/cursorrules/cursor_enhanced_rule_set.md", "start": 123, "end": 660, "startLoc": {"line": 123, "column": 1, "position": 1729}, "endLoc": {"line": 660, "column": 4, "position": 5717}}}]}