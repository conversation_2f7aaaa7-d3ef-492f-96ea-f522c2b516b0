# Machine Learning for Strategy Weight Optimization

## Overview

This improvement implements a machine learning system to dynamically optimize the weights used in the strategy scoring functions. Currently, the weights in `StrategyScorer` are fixed and determined by expert knowledge. By using reinforcement learning, we can dynamically adjust these weights based on historical performance to maximize risk-adjusted returns.

## Technical Specification

### Architecture

The system will consist of the following components:

1. **Data Collection Module**: Collects and stores historical market data and strategy performance metrics
2. **Feature Engineering Module**: Transforms raw market data into features for the ML model
3. **Reinforcement Learning Agent**: Learns optimal weights for strategy scoring
4. **Weight Application Module**: Applies learned weights to the strategy scoring system
5. **Performance Evaluation Module**: Evaluates the performance of the ML-optimized weights

### Technology Stack

- **Python Libraries**:
  - `scikit-learn`: For data preprocessing and feature engineering
  - `pandas` and `numpy`: For data manipulation
  - `stable-baselines3`: For reinforcement learning implementation
  - `optuna`: For hyperparameter optimization
  - `joblib`: For model serialization

- **Database**:
  - Use existing PostgreSQL database for storing historical data and model states
  - Add new tables for ML model metadata and performance metrics

### Data Flow

```
Market Data → Feature Engineering → RL Agent → Optimized Weights → Strategy Scorer → Trading Decisions
     ↑                                                                    |
     └────────────────── Performance Feedback Loop ───────────────────────┘
```

### Implementation Details

1. **Reinforcement Learning Environment**:
   - **State**: Market conditions (volatility, trend, range-bound, volume) and current strategy performance
   - **Actions**: Adjustments to strategy scoring weights
   - **Reward**: Risk-adjusted returns (Sharpe ratio or similar)

2. **Model Architecture**:
   - Use Proximal Policy Optimization (PPO) algorithm
   - Neural network with 2-3 hidden layers
   - Input layer size: Number of market features + current weights
   - Output layer size: Number of weights to optimize

3. **Training Process**:
   - Train on historical data using sliding window approach
   - Periodically retrain on new data
   - Use early stopping to prevent overfitting

4. **Integration with Existing Code**:
   - Modify `StrategyScorer` to accept dynamic weights
   - Create a new `MLWeightOptimizer` class to manage the ML model
   - Add configuration options for ML features

### Database Schema

```sql
CREATE TABLE ml_model_metadata (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    description TEXT,
    hyperparameters JSONB,
    performance_metrics JSONB
);

CREATE TABLE strategy_weights (
    id SERIAL PRIMARY KEY,
    model_id INTEGER REFERENCES ml_model_metadata(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    strategy_name VARCHAR(50) NOT NULL,
    weight_name VARCHAR(50) NOT NULL,
    weight_value FLOAT NOT NULL
);

CREATE TABLE training_sessions (
    id SERIAL PRIMARY KEY,
    model_id INTEGER REFERENCES ml_model_metadata(id),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    training_data_range JSONB,
    validation_data_range JSONB,
    training_metrics JSONB,
    validation_metrics JSONB
);
```

### File Structure

```
app/
├── ml/
│   ├── __init__.py
│   ├── data_collector.py
│   ├── feature_engineering.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base_model.py
│   │   ├── reinforcement_learning.py
│   │   └── weight_optimizer.py
│   ├── training/
│   │   ├── __init__.py
│   │   ├── environment.py
│   │   ├── reward_functions.py
│   │   └── trainer.py
│   └── evaluation/
│       ├── __init__.py
│       ├── metrics.py
│       └── backtesting.py
├── strategies/
│   ├── strategy_scoring.py (modified)
│   └── ...
└── ...
```

### Configuration

The following settings have been added to `app/config/settings.py`:

```python
# ML Weight Optimization Settings
ml_weight_optimization_enabled: bool = Field(False, env='ML_WEIGHT_OPTIMIZATION_ENABLED')
ml_model_path: str = Field("models/weight_optimizer", env='ML_MODEL_PATH')
ml_training_interval_hours: int = Field(24, env='ML_TRAINING_INTERVAL_HOURS')
ml_lookback_days: int = Field(90, env='ML_LOOKBACK_DAYS')
ml_window_size: int = Field(10, env='ML_WINDOW_SIZE')
ml_training_timesteps: int = Field(100000, env='ML_TRAINING_TIMESTEPS')
ml_hyperparameter_optimization: bool = Field(False, env='ML_HYPERPARAMETER_OPTIMIZATION')
ml_optimization_trials: int = Field(10, env='ML_OPTIMIZATION_TRIALS')
ml_reward_function: str = Field("sharpe", env='ML_REWARD_FUNCTION')
ml_default_weights: Dict[str, Dict[str, float]] = Field(
    {
        'grid': {
            'range_bound': 0.5,
            'volatility': 0.3,
            'trend': 0.2
        },
        'technical_analysis': {
            'volatility': 0.4,
            'volume': 0.3,
            'adaptability': 0.3
        },
        'trend_following': {
            'trend_strength': 0.5,
            'volatility': 0.2,
            'range_bound_penalty': 0.3
        }
    },
    env='ML_DEFAULT_WEIGHTS'
)
```

### API Endpoints

The following endpoints have been added to the API:

```python
@router.get("/ml/status", response_model=Dict[str, Any])
async def get_ml_status():
    """Get the status of ML weight optimization."""
    # Returns the current ML settings

@router.get("/ml/info", response_model=ModelInfoResponse)
async def get_model_info():
    """Get information about the ML model."""
    # Returns information about the ML model

@router.post("/ml/weights", response_model=WeightsResponse)
async def get_optimized_weights(request: WeightsRequest):
    """Get ML-optimized weights for strategy scoring."""
    # Returns optimized weights based on market conditions

@router.post("/ml/train", response_model=TrainingResponse)
async def train_ml_model(request: TrainingRequest):
    """Train the ML model for weight optimization."""
    # Trains the ML model

@router.post("/ml/backtest", response_model=BacktestResponse)
async def backtest_ml_weights(request: BacktestRequest):
    """Backtest with ML-optimized weights."""
    # Backtests with ML-optimized weights
```

### API Models

The following models have been added for the API endpoints:

```python
class TrainingRequest(BaseModel):
    """Request model for training the ML model."""

    symbol: str = settings.trading_symbol
    timeframe: str = settings.timeframe
    lookback_days: int = settings.ml_lookback_days
    window_size: int = settings.ml_window_size
    total_timesteps: int = settings.ml_training_timesteps
    optimize_hyperparameters: bool = settings.ml_hyperparameter_optimization
    optimization_trials: int = settings.ml_optimization_trials


class TrainingResponse(BaseModel):
    """Response model for training the ML model."""

    success: bool
    message: str
    training_id: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class WeightsRequest(BaseModel):
    """Request model for getting ML-optimized weights."""

    market_conditions: Dict[str, float]


class WeightsResponse(BaseModel):
    """Response model for getting ML-optimized weights."""

    success: bool
    weights: Optional[Dict[str, Dict[str, float]]] = None
    strategy_weights: Optional[Dict[str, float]] = None
    timestamp: str
    model_info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BacktestRequest(BaseModel):
    """Request model for backtesting with ML weights."""

    symbol: str = settings.trading_symbol
    timeframe: str = settings.timeframe
    start_date: str
    end_date: str
    fixed_weights: Optional[Dict[str, float]] = None


class BacktestResponse(BaseModel):
    """Response model for backtesting with ML weights."""

    success: bool
    comparison: Optional[Dict[str, Any]] = None
    fixed_metrics: Optional[Dict[str, float]] = None
    ml_metrics: Optional[Dict[str, float]] = None
    error: Optional[str] = None


class ModelInfoResponse(BaseModel):
    """Response model for getting ML model information."""

    success: bool
    model_info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
```

## Potential Challenges and Mitigations

1. **Overfitting**:
   - **Challenge**: The model might overfit to historical data
   - **Mitigation**: Use cross-validation, regularization, and early stopping

2. **Data Scarcity**:
   - **Challenge**: Limited historical data for rare market conditions
   - **Mitigation**: Use synthetic data generation and data augmentation techniques

3. **Computational Resources**:
   - **Challenge**: RL training can be computationally intensive
   - **Mitigation**: Implement efficient training schedules and use cloud computing resources

4. **Model Drift**:
   - **Challenge**: Market conditions change over time, making models less effective
   - **Mitigation**: Implement continuous learning and model monitoring

5. **Integration Complexity**:
   - **Challenge**: Integrating ML with the existing trading system
   - **Mitigation**: Use a phased approach with thorough testing at each stage

## Testing Strategy

1. **Unit Tests**:
   - Test individual components of the ML system
   - Verify data preprocessing and feature engineering
   - Test model serialization and loading

2. **Integration Tests**:
   - Test the interaction between ML components and the trading system
   - Verify that weight updates are correctly applied

3. **Backtesting**:
   - Compare performance of ML-optimized weights vs. fixed weights
   - Test on different market conditions and time periods

4. **A/B Testing**:
   - Run both systems in parallel in a test environment
   - Compare performance metrics over time

## Deployment Strategy

1. **Phase 1: Development and Testing**
   - Implement ML components
   - Train and evaluate on historical data
   - Run extensive backtests

2. **Phase 2: Shadow Mode**
   - Deploy in production but only log recommendations
   - Compare with actual trading decisions
   - Analyze performance differences

3. **Phase 3: Partial Integration**
   - Apply ML-optimized weights with limited impact
   - Gradually increase influence based on performance

4. **Phase 4: Full Deployment**
   - Fully integrate ML-optimized weights
   - Implement continuous monitoring and retraining

## Task Checklist

- [x] **Setup and Infrastructure**
  - [x] Create ML module structure
  - [x] Set up database tables for ML metadata
  - [x] Configure environment variables and settings

- [x] **Data Collection and Processing**
  - [x] Implement data collection module
  - [x] Develop feature engineering pipeline
  - [x] Create data validation and cleaning utilities

- [x] **Model Development**
  - [x] Implement reinforcement learning environment
  - [x] Develop reward function based on risk-adjusted returns
  - [x] Create model architecture and training pipeline
  - [x] Implement hyperparameter optimization

- [x] **Integration with Trading System**
  - [x] Modify `StrategyScorer` to use dynamic weights
  - [x] Create weight application module
  - [x] Implement model versioning and rollback capabilities

- [x] **Testing and Validation**
  - [x] Develop unit tests for ML components
  - [x] Create integration tests for the full system
  - [x] Implement backtesting framework for ML models
  - [x] Conduct performance comparison with baseline

- [x] **Deployment and Monitoring**
  - [x] Set up model monitoring and alerting
  - [x] Create automated retraining pipeline
  - [x] Implement gradual rollout strategy
  - [x] Develop performance dashboards

- [x] **Documentation and Knowledge Transfer**
  - [x] Create technical documentation
  - [x] Develop user guide for ML features
  - [x] Document model performance and limitations

## Performance Metrics

The success of this improvement will be measured by:

1. **Sharpe Ratio**: Increase in risk-adjusted returns
2. **Win Rate**: Percentage of profitable trades
3. **Maximum Drawdown**: Reduction in maximum drawdown
4. **Strategy Selection Accuracy**: Improved selection of optimal strategies for market conditions
5. **Adaptability**: Faster adaptation to changing market conditions

## Usage Examples

### Enable ML Weight Optimization

To enable ML weight optimization, set the following environment variable:

```
ML_WEIGHT_OPTIMIZATION_ENABLED=True
```

### Train the ML Model

```python
import requests
import json

# Train the ML model
response = requests.post(
    "http://localhost:8000/api/ml/train",
    json={
        "symbol": "BTCUSDT",
        "timeframe": "1h",
        "lookback_days": 90,
        "window_size": 10,
        "total_timesteps": 100000,
        "optimize_hyperparameters": False,
        "optimization_trials": 10
    }
)

print(json.dumps(response.json(), indent=2))
```

### Get Optimized Weights

```python
import requests
import json

# Get optimized weights
response = requests.post(
    "http://localhost:8000/api/ml/weights",
    json={
        "market_conditions": {
            "volatility": 0.02,
            "trend": 0.3,
            "range_bound": 0.4,
            "volume": 0.8
        }
    }
)

print(json.dumps(response.json(), indent=2))
```

### Backtest ML Weights

```python
import requests
import json
from datetime import datetime, timedelta

# Get dates for backtesting
end_date = datetime.now()
start_date = end_date - timedelta(days=30)

# Backtest ML weights
response = requests.post(
    "http://localhost:8000/api/ml/backtest",
    json={
        "symbol": "BTCUSDT",
        "timeframe": "1h",
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "fixed_weights": {
            "grid": 0.3,
            "technical_analysis": 0.4,
            "trend_following": 0.3
        }
    }
)

print(json.dumps(response.json(), indent=2))
```

### Get Model Info

```python
import requests
import json

# Get model info
response = requests.get("http://localhost:8000/api/ml/info")

print(json.dumps(response.json(), indent=2))
```

### Get ML Status

```python
import requests
import json

# Get ML status
response = requests.get("http://localhost:8000/api/ml/status")

print(json.dumps(response.json(), indent=2))
```
