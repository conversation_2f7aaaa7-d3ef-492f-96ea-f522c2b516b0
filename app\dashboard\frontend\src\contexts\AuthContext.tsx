import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { jwtDecode } from 'jwt-decode';
import { AuthState, User, LoginCredentials } from '../types';
import authService from '../services/authService';
import tokenService from '../services/tokenService';

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: true,
  error: null,
};

// Action types
type AuthAction =
  | { type: 'LOGIN_REQUEST' }
  | { type: 'LOGIN_SUCCESS'; payload: { token: string; user: User } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' };

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_REQUEST':
      return {
        ...state,
        loading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
interface AuthContextProps {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for token on mount
  useEffect(() => {
    const loadUser = async () => {
      const token = tokenService.getAccessToken();

      if (!token) {
        dispatch({ type: 'LOGOUT' });
        return;
      }

      try {
        // Check if token is expired
        if (tokenService.isTokenExpired()) {
          // Try to refresh the token
          const refreshed = await authService.refreshToken();

          if (!refreshed) {
            dispatch({ type: 'LOGOUT' });
            tokenService.clearTokens();
            return;
          }

          // Get the new token
          const newToken = tokenService.getAccessToken();
          if (!newToken) {
            dispatch({ type: 'LOGOUT' });
            return;
          }

          // Decode the new token
          const decoded: any = tokenService.parseJwt(newToken);

          // Set user and token
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: {
              token: newToken,
              user: {
                username: decoded.sub,
                is_active: true,
              },
            },
          });
        } else {
          // Decode token to get user info
          const decoded: any = tokenService.parseJwt(token);

          // Set user and token
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: {
              token,
              user: {
                username: decoded.sub,
                is_active: true,
              },
            },
          });
        }
      } catch (error) {
        dispatch({ type: 'LOGOUT' });
        tokenService.clearTokens();
      }
    };

    loadUser();
  }, []);

  // Login
  const login = async (credentials: LoginCredentials) => {
    dispatch({ type: 'LOGIN_REQUEST' });

    try {
      // Use the authService to login
      const success = await authService.login(credentials.username, credentials.password);

      if (!success) {
        throw new Error('Login failed. Please check your credentials and try again.');
      }

      // Get the token from tokenService
      const token = tokenService.getAccessToken();

      if (!token) {
        throw new Error('Login failed. No token received.');
      }

      // Decode token to get user info
      const decoded: any = tokenService.parseJwt(token);

      // Dispatch success
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          token,
          user: {
            username: decoded.sub,
            is_active: true,
          },
        },
      });
    } catch (error: any) {
      // Extract the error message - use the message property if it's an Error object
      const errorMessage = error instanceof Error
        ? error.message
        : 'Login failed. Please check your credentials and try again.';

      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage,
      });
    }
  };

  // Logout
  const logout = () => {
    // Use the authService to logout
    authService.logout();
    dispatch({ type: 'LOGOUT' });
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  return (
    <AuthContext.Provider value={{ state, login, logout, clearError }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};