#!/usr/bin/env python3
"""
Quick Ensemble Trading Demonstration
Execute 10-15 trades to demonstrate ensemble strategy performance.
"""

import asyncio
import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any
import numpy as np
from dotenv import load_dotenv
from binance.client import Client
from binance.enums import *

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickEnsembleDemo:
    """Quick demonstration of ensemble trading system"""
    
    def __init__(self, client: Client, initial_balance: float):
        self.client = client
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.trades = []
        self.performance_metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'execution_times': [],
            'strategy_signals': {'grid': 0, 'technical': 0, 'trend': 0},
            'risk_metrics': {
                'max_position_size': 0,
                'avg_position_size': 0,
                'total_exposure': 0
            }
        }
        
        # Conservative risk parameters for demo
        self.risk_per_trade = 0.01  # 1% risk per trade
        self.min_trade_size = 0.001
        self.demo_position_size = 0.002  # Small demo size

    async def get_current_price(self):
        """Get current BTC price"""
        try:
            ticker = self.client.futures_symbol_ticker(symbol='BTCUSDT')
            return float(ticker['price'])
        except Exception as e:
            logger.error(f"Error getting price: {e}")
            return None

    async def generate_demo_signal(self, trade_number: int):
        """Generate demonstration signals with different strategies"""
        current_price = await self.get_current_price()
        if not current_price:
            return None, 0, ""
        
        # Simulate different strategy signals for demonstration
        signals = [
            ('BUY', 0.75, 'grid'),      # Grid strategy buy signal
            ('SELL', 0.80, 'technical'), # Technical analysis sell signal
            ('BUY', 0.65, 'trend'),     # Trend following buy signal
            ('SELL', 0.70, 'grid'),     # Grid strategy sell signal
            ('BUY', 0.85, 'technical'), # Strong technical buy signal
        ]
        
        # Cycle through signals for demonstration
        signal_index = trade_number % len(signals)
        signal, confidence, strategy = signals[signal_index]
        
        # Update strategy counters
        self.performance_metrics['strategy_signals'][strategy] += 1
        
        logger.info(f"🎯 Signal #{trade_number + 1}: {signal} from {strategy} strategy (confidence: {confidence:.2f})")
        
        return signal, confidence, strategy

    async def execute_demo_trade(self, signal: str, confidence: float, strategy: str):
        """Execute demonstration trade"""
        try:
            start_time = time.time()
            current_price = await self.get_current_price()
            
            if not current_price:
                return None
            
            side = SIDE_BUY if signal == 'BUY' else SIDE_SELL
            quantity = self.demo_position_size
            
            logger.info(f"📊 Executing {signal}: {quantity} BTC at ${current_price:,.2f} (Strategy: {strategy})")
            
            # Execute trade
            order = self.client.futures_create_order(
                symbol='BTCUSDT',
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=quantity
            )
            
            execution_time = (time.time() - start_time) * 1000
            self.performance_metrics['execution_times'].append(execution_time)
            
            fill_price = float(order.get('avgPrice', current_price))
            
            # Store trade
            trade_info = {
                'trade_number': len(self.trades) + 1,
                'timestamp': datetime.now().isoformat(),
                'order_id': order['orderId'],
                'strategy': strategy,
                'signal': signal,
                'quantity': quantity,
                'price': fill_price,
                'confidence': confidence,
                'execution_time_ms': execution_time,
                'notional_value': quantity * fill_price
            }
            
            self.trades.append(trade_info)
            self.performance_metrics['total_trades'] += 1
            
            # Update risk metrics
            notional = quantity * fill_price
            self.performance_metrics['risk_metrics']['total_exposure'] += notional
            self.performance_metrics['risk_metrics']['max_position_size'] = max(
                self.performance_metrics['risk_metrics']['max_position_size'], notional
            )
            
            logger.info(f"✅ Trade #{len(self.trades)} executed: {order['orderId']} in {execution_time:.1f}ms")
            logger.info(f"   Fill Price: ${fill_price:,.2f}, Notional: ${notional:.2f}")
            
            return trade_info
            
        except Exception as e:
            logger.error(f"Trade execution failed: {e}")
            return None

    async def close_demo_position(self, original_trade: Dict):
        """Close demonstration position"""
        try:
            # Wait a bit to simulate holding period
            await asyncio.sleep(2)
            
            current_price = await self.get_current_price()
            if not current_price:
                return
            
            # Determine close side
            close_side = SIDE_SELL if original_trade['signal'] == 'BUY' else SIDE_BUY
            quantity = original_trade['quantity']
            
            logger.info(f"🔄 Closing position from trade #{original_trade['trade_number']}")
            
            # Execute close order
            close_order = self.client.futures_create_order(
                symbol='BTCUSDT',
                side=close_side,
                type=ORDER_TYPE_MARKET,
                quantity=quantity
            )
            
            close_price = float(close_order.get('avgPrice', current_price))
            
            # Calculate PnL
            if original_trade['signal'] == 'BUY':
                pnl = (close_price - original_trade['price']) * quantity
            else:
                pnl = (original_trade['price'] - close_price) * quantity
            
            # Update trade record
            original_trade['close_timestamp'] = datetime.now().isoformat()
            original_trade['close_price'] = close_price
            original_trade['realized_pnl'] = pnl
            original_trade['holding_period_seconds'] = 2
            original_trade['status'] = 'CLOSED'
            
            # Update performance
            self.performance_metrics['total_pnl'] += pnl
            self.current_balance += pnl
            
            if pnl > 0:
                self.performance_metrics['successful_trades'] += 1
            
            logger.info(f"✅ Position closed: PnL ${pnl:.2f}, New balance: ${self.current_balance:.2f}")
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")

    async def run_demo_campaign(self, num_trades: int = 10):
        """Run demonstration trading campaign"""
        logger.info("=" * 80)
        logger.info("🚀 ENSEMBLE TRADING DEMONSTRATION")
        logger.info("=" * 80)
        logger.info(f"Initial Balance: ${self.initial_balance:,.2f} USDT")
        logger.info(f"Demo Trades: {num_trades}")
        logger.info(f"Position Size: {self.demo_position_size} BTC per trade")
        
        current_price = await self.get_current_price()
        logger.info(f"Current BTC Price: ${current_price:,.2f}")
        
        try:
            # Execute demonstration trades
            for i in range(num_trades):
                logger.info(f"\n🔄 Executing Demo Trade {i + 1}/{num_trades}")
                
                # Generate signal
                signal, confidence, strategy = await self.generate_demo_signal(i)
                
                if signal:
                    # Execute trade
                    trade_info = await self.execute_demo_trade(signal, confidence, strategy)
                    
                    if trade_info:
                        # Close position after brief hold
                        await self.close_demo_position(trade_info)
                
                # Brief pause between trades
                await asyncio.sleep(1)
            
            # Calculate final statistics
            await self.calculate_final_stats()
            
            return self.performance_metrics, self.trades
            
        except Exception as e:
            logger.error(f"Demo campaign failed: {e}")
            return self.performance_metrics, self.trades

    async def calculate_final_stats(self):
        """Calculate comprehensive final statistics"""
        try:
            metrics = self.performance_metrics
            
            # Win rate calculation
            closed_trades = [t for t in self.trades if t.get('status') == 'CLOSED']
            profitable_trades = [t for t in closed_trades if t.get('realized_pnl', 0) > 0]
            
            if closed_trades:
                metrics['win_rate'] = len(profitable_trades) / len(closed_trades)
                metrics['avg_pnl_per_trade'] = metrics['total_pnl'] / len(closed_trades)
                
                # Calculate returns
                total_return = self.current_balance - self.initial_balance
                return_percentage = (total_return / self.initial_balance) * 100
                metrics['total_return_usd'] = total_return
                metrics['return_percentage'] = return_percentage
                
                # PnL statistics
                pnls = [t.get('realized_pnl', 0) for t in closed_trades]
                metrics['best_trade'] = max(pnls) if pnls else 0
                metrics['worst_trade'] = min(pnls) if pnls else 0
                metrics['avg_win'] = np.mean([p for p in pnls if p > 0]) if profitable_trades else 0
                metrics['avg_loss'] = np.mean([p for p in pnls if p < 0]) if any(p < 0 for p in pnls) else 0
            
            # Execution performance
            if metrics['execution_times']:
                metrics['avg_execution_time_ms'] = np.mean(metrics['execution_times'])
                metrics['fastest_execution_ms'] = min(metrics['execution_times'])
                metrics['slowest_execution_ms'] = max(metrics['execution_times'])
            
            # Risk metrics
            if self.trades:
                notional_values = [t.get('notional_value', 0) for t in self.trades]
                metrics['risk_metrics']['avg_position_size'] = np.mean(notional_values)
                metrics['risk_metrics']['total_volume_traded'] = sum(notional_values)
            
            # Strategy performance breakdown
            for strategy in ['grid', 'technical', 'trend']:
                strategy_trades = [t for t in closed_trades if t.get('strategy') == strategy]
                if strategy_trades:
                    strategy_pnl = sum(t.get('realized_pnl', 0) for t in strategy_trades)
                    metrics[f'{strategy}_strategy_pnl'] = strategy_pnl
                    metrics[f'{strategy}_strategy_trades'] = len(strategy_trades)
            
        except Exception as e:
            logger.error(f"Error calculating final stats: {e}")

    def print_comprehensive_report(self):
        """Print detailed performance report"""
        metrics = self.performance_metrics
        
        print("\n" + "=" * 100)
        print("📊 COMPREHENSIVE ENSEMBLE TRADING PERFORMANCE REPORT")
        print("=" * 100)
        
        # Campaign Summary
        print(f"\n🎯 CAMPAIGN SUMMARY:")
        print(f"{'Initial Balance:':<25} ${self.initial_balance:>12,.2f} USDT")
        print(f"{'Final Balance:':<25} ${self.current_balance:>12,.2f} USDT")
        print(f"{'Total Return:':<25} ${metrics.get('total_return_usd', 0):>12,.2f} USDT")
        print(f"{'Return Percentage:':<25} {metrics.get('return_percentage', 0):>12.2f}%")
        print(f"{'Total Trades:':<25} {metrics['total_trades']:>12}")
        print(f"{'Successful Trades:':<25} {metrics['successful_trades']:>12}")
        
        # Performance Metrics
        print(f"\n📈 PERFORMANCE METRICS:")
        print(f"{'Win Rate:':<25} {metrics.get('win_rate', 0)*100:>12.1f}%")
        print(f"{'Avg PnL per Trade:':<25} ${metrics.get('avg_pnl_per_trade', 0):>12.2f}")
        print(f"{'Best Trade:':<25} ${metrics.get('best_trade', 0):>12.2f}")
        print(f"{'Worst Trade:':<25} ${metrics.get('worst_trade', 0):>12.2f}")
        print(f"{'Average Win:':<25} ${metrics.get('avg_win', 0):>12.2f}")
        print(f"{'Average Loss:':<25} ${metrics.get('avg_loss', 0):>12.2f}")
        
        # Execution Performance
        print(f"\n⚡ EXECUTION PERFORMANCE:")
        print(f"{'Avg Execution Time:':<25} {metrics.get('avg_execution_time_ms', 0):>12.1f}ms")
        print(f"{'Fastest Execution:':<25} {metrics.get('fastest_execution_ms', 0):>12.1f}ms")
        print(f"{'Slowest Execution:':<25} {metrics.get('slowest_execution_ms', 0):>12.1f}ms")
        
        # Risk Management
        print(f"\n🛡️ RISK MANAGEMENT:")
        print(f"{'Max Position Size:':<25} ${metrics['risk_metrics']['max_position_size']:>12.2f}")
        print(f"{'Avg Position Size:':<25} ${metrics['risk_metrics']['avg_position_size']:>12.2f}")
        print(f"{'Total Volume Traded:':<25} ${metrics['risk_metrics']['total_volume_traded']:>12.2f}")
        print(f"{'Risk per Trade:':<25} {self.risk_per_trade*100:>12.1f}%")
        
        # Strategy Performance
        print(f"\n🎲 STRATEGY PERFORMANCE:")
        for strategy in ['grid', 'technical', 'trend']:
            signals = metrics['strategy_signals'].get(strategy, 0)
            trades = metrics.get(f'{strategy}_strategy_trades', 0)
            pnl = metrics.get(f'{strategy}_strategy_pnl', 0)
            print(f"{strategy.title() + ' Strategy:':<25} {signals} signals, {trades} trades, ${pnl:.2f} PnL")
        
        # Trade Details
        print(f"\n📝 TRADE DETAILS:")
        print(f"{'Trade#':<8} {'Strategy':<12} {'Side':<6} {'Price':<12} {'PnL':<10} {'Exec Time':<10}")
        print("-" * 70)
        
        for trade in self.trades:
            if trade.get('status') == 'CLOSED':
                trade_num = trade.get('trade_number', 0)
                strategy = trade.get('strategy', '').title()
                signal = trade.get('signal', '')
                price = trade.get('price', 0)
                pnl = trade.get('realized_pnl', 0)
                exec_time = trade.get('execution_time_ms', 0)
                
                print(f"{trade_num:<8} {strategy:<12} {signal:<6} ${price:<11.2f} ${pnl:<9.2f} {exec_time:<9.1f}ms")
        
        print("=" * 100)

async def main():
    """Main demonstration function"""
    try:
        # Load environment
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        # Initialize client
        client = Client(api_key, api_secret, testnet=True)
        
        # Get account balance
        account = client.futures_account()
        initial_balance = float(account['availableBalance'])
        
        # Run demonstration
        demo = QuickEnsembleDemo(client, initial_balance)
        metrics, trades = await demo.run_demo_campaign(num_trades=12)
        
        # Print comprehensive report
        demo.print_comprehensive_report()
        
        # Save results
        results = {
            'demo_summary': {
                'timestamp': datetime.now().isoformat(),
                'initial_balance': initial_balance,
                'final_balance': demo.current_balance,
                'num_trades': len(trades)
            },
            'performance_metrics': metrics,
            'trades': trades
        }
        
        with open('ensemble_demo_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: ensemble_demo_results.json")
        
        return True
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)