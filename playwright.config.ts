import { PlaywrightTestConfig, devices } from '@playwright/test';

/**
 * Configuration for Playwright E2E testing
 * @see https://playwright.dev/docs/test-configuration
 */
const config: PlaywrightTestConfig = {
  // Directory where tests are located
  testDir: './e2e',
  
  // Maximum time one test can run for
  timeout: 60000,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry failed tests on CI to reduce flakiness
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests because our app runs on single instance
  workers: 1,
  
  // Reporter to use
  reporter: [
    ['html', { open: 'never' }],
    ['list']
  ],
  
  // Shared settings for all projects (browsers)
  use: {
    // Base URL to use in navigation
    baseURL: 'http://localhost:3000',
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Record videos for failed tests
    video: 'on-first-retry',
    
    // Record screenshots on failure
    screenshot: 'only-on-failure',
    
    // Browser window size
    viewport: { width: 1280, height: 720 },
    
    // Maximum time to wait for expected condition
    actionTimeout: 15000,
  },
  
  // Configure projects for different browsers
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    // Add mobile browsers if needed
    // {
    //   name: 'mobile-chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
  ],
  
  // Local dev server handling (optional)
  // Uncomment if you want Playwright to automatically start your local server
  // webServer: {
  //   command: 'npm run dev',
  //   port: 3000,
  //   timeout: 120000,
  //   reuseExistingServer: !process.env.CI,
  // },
};

export default config; 