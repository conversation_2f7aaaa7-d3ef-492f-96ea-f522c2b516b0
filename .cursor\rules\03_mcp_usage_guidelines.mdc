---
description: 
globs: 
alwaysApply: true
---

# General MCP Usage Guidelines

- Before initiating any task, always assess the full list of currently installed MCPs. Proactively and thoroughly utilize any relevant MCPs to streamline development, automate processes, and ensure comprehensive project context. This includes, but is not limited to:
    - Memory & Context MCPs (e.g., `mcp0_*`) for persistent knowledge storage and retrieval.
    - Documentation MCPs (e.g., `mcp2_*`) to fetch up-to-date library docs.
    - Version-Control MCPs (e.g., `mcp3_*`) for Git operations (status, diff, commit).
    - Filesystem MCPs (e.g., `mcp-filesystem` exposing `mcp4_*` tools) for secure file/directory tasks.
    - Frontend-Testing MCPs (e.g., `mcp-frontend-testing` exposing `mcp5_*` tools) for Jest/Cypress automation.
    - Browser-Automation MCPs (e.g., Playwright MCP exposing `mcp6_*` tools) for programmatic web interaction.
    - Database MCPs (e.g., Postgres MCP exposing `mcp7_*` tools) for read-only SQL queries.
    - Supabase MCPs (e.g., `mcp8_*`) for project/branch management, migrations, edge functions, and raw SQL operations.
        - **Crucial**: For any Supabase-related checks, information retrieval, or operations, Supabase MCP tools (`mcp8_*`) MUST be the first resort before asking the USER to perform actions manually or check the Supabase dashboard.
    - Time & Scheduling MCPs (e.g., `mcp9_*`) for timezone conversions and current time.
    - Data-specific MCPs such as CoinCap (`mcp1_*`) for realtime crypto prices.
    - Any newly added or project-specific MCPs (ensure they are documented here and in the memory-bank).
- Prioritize using an MCP if it can fulfill a task requirement or provide necessary information, rather than performing actions manually or requesting information that can be tool-assisted.
