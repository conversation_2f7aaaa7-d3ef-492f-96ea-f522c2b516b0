from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import List, Dict, Optional
import os
from dotenv import load_dotenv
import logging

# Load environment variables from .env file
load_dotenv()

# Configure logging early
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    # Binance API Credentials
    binance_api_key: Optional[str] = None
    binance_api_secret: Optional[str] = None
    use_testnet: bool = Field(True, json_schema_extra={"env": 'USE_TESTNET'})

    # JWT Authentication
    secret_key: str = Field(..., json_schema_extra={"env": 'SECRET_KEY'})
    algorithm: str = Field("HS256", json_schema_extra={"env": 'ALGORITHM'})
    access_token_expire_minutes: int = Field(30, json_schema_extra={"env": 'ACCESS_TOKEN_EXPIRE_MINUTES'})
    refresh_token_expire_days: int = Field(7, json_schema_extra={"env": 'REFRESH_TOKEN_EXPIRE_DAYS'})

    # Trading Parameters
    trading_symbol: str = Field("BTCUSDT", json_schema_extra={"env": 'TRADING_SYMBOL'})
    timeframe: str = Field("15m", json_schema_extra={"env": 'TIMEFRAME'}) # Defaulting to 15m as per previous decision
    kline_limit: int = Field(100, json_schema_extra={"env": 'KLINE_LIMIT'}) # Defaulting to 100 as per previous decision
    portfolio_value: float = Field(1000.0, json_schema_extra={"env": 'PORTFOLIO_VALUE'}) # Example default

    # Strategy Parameters
    min_score_threshold: float = Field(0.6, json_schema_extra={"env": 'MIN_SCORE_THRESHOLD'}) # Min score to select a strategy

    # --- Grid Strategy Parameters ---
    grid_levels: int = Field(10, json_schema_extra={"env": 'GRID_LEVELS'})
    grid_range: float = Field(0.05, json_schema_extra={"env": 'GRID_RANGE'}) # 5% range
    min_level_distance: float = Field(0.005, json_schema_extra={"env": 'MIN_LEVEL_DISTANCE'}) # 0.5% min distance
    support_resistance_periods: int = Field(20, json_schema_extra={"env": 'SUPPORT_RESISTANCE_PERIODS'})
    atr_periods: int = Field(14, json_schema_extra={"env": 'ATR_PERIODS'})
    atr_multiplier: float = Field(2.5, json_schema_extra={"env": 'ATR_MULTIPLIER'})

    # --- Technical Analysis Strategy Parameters ---
    ta_rsi_period: int = Field(14, json_schema_extra={"env": 'TA_RSI_PERIOD'})
    ta_signal_threshold: int = Field(3, json_schema_extra={"env": 'TA_SIGNAL_THRESHOLD'}) # Min signals for TA

    ta_rsi_oversold: int = Field(30, json_schema_extra={"env": 'TA_RSI_OVERSOLD'})
    ta_rsi_overbought: int = Field(70, json_schema_extra={"env": 'TA_RSI_OVERBOUGHT'})
    ta_macd_fast: int = Field(12, json_schema_extra={"env": 'TA_MACD_FAST'})
    ta_macd_slow: int = Field(26, json_schema_extra={"env": 'TA_MACD_SLOW'})
    ta_macd_signal: int = Field(9, json_schema_extra={"env": 'TA_MACD_SIGNAL'})
    tf_adx_period: int = Field(14, json_schema_extra={"env": 'TF_ADX_PERIOD'})
    tf_adx_threshold: int = Field(25, json_schema_extra={"env": 'TF_ADX_THRESHOLD'})

    ta_bollinger_period: int = Field(20, json_schema_extra={"env": 'TA_BOLLINGER_PERIOD'})
    ta_bollinger_stddev: int = Field(2, json_schema_extra={"env": 'TA_BOLLINGER_STDDEV'})

    # --- Trend Following Strategy Parameters ---
    tf_ema_short: int = Field(9, json_schema_extra={"env": 'TF_EMA_SHORT'})
    tf_ema_medium: int = Field(21, json_schema_extra={"env": 'TF_EMA_MEDIUM'})
    tf_ema_long: int = Field(50, json_schema_extra={"env": 'TF_EMA_LONG'})
    tf_rsi_period: int = Field(14, json_schema_extra={"env": 'TF_RSI_PERIOD'})
    tf_macd_fast: int = Field(12, json_schema_extra={"env": 'TF_MACD_FAST'})
    tf_macd_slow: int = Field(26, json_schema_extra={"env": 'TF_MACD_SLOW'})
    tf_macd_signal: int = Field(9, json_schema_extra={"env": 'TF_MACD_SIGNAL'})
    tf_breakout_periods: int = Field(20, json_schema_extra={"env": 'TF_BREAKOUT_PERIODS'})

    # Server Settings (from existing app/dashboard/config.py)
    api_prefix: str = "/api"
    debug: bool = Field(False, json_schema_extra={"env": 'DEBUG'})

    # ML Weight Optimization Settings
    ml_weight_optimization_enabled: bool = Field(False, json_schema_extra={"env": 'ML_WEIGHT_OPTIMIZATION_ENABLED'})
    ml_model_path: str = Field("models/weight_optimizer", json_schema_extra={"env": 'ML_MODEL_PATH'})
    ml_training_interval_hours: int = Field(24, json_schema_extra={"env": 'ML_TRAINING_INTERVAL_HOURS'})
    ml_lookback_days: int = Field(90, json_schema_extra={"env": 'ML_LOOKBACK_DAYS'})
    ml_window_size: int = Field(10, json_schema_extra={"env": 'ML_WINDOW_SIZE'})
    ml_training_timesteps: int = Field(100000, json_schema_extra={"env": 'ML_TRAINING_TIMESTEPS'})
    ml_hyperparameter_optimization: bool = Field(False, json_schema_extra={"env": 'ML_HYPERPARAMETER_OPTIMIZATION'})
    ml_optimization_trials: int = Field(10, json_schema_extra={"env": 'ML_OPTIMIZATION_TRIALS'})
    ml_reward_function: str = Field("sharpe", json_schema_extra={"env": 'ML_REWARD_FUNCTION'})

    # --- Account Settings --- # Added Section
    model_config = SettingsConfigDict(
        env_file = '.env',
        env_file_encoding = 'utf-8',
        extra = 'ignore',  # Ignore extra fields from .env
        case_sensitive = False  # Makes BaseSettings case-insensitive with environment variables
    )
    
    @field_validator('secret_key')
    def validate_secret_key(cls, v):
        """Validate JWT secret key security requirements."""
        if len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters long for security')
        return v
    
    @field_validator('trading_symbol')
    def validate_trading_symbol(cls, v):
        """Validate trading symbol format."""
        if not v or len(v) < 6:
            raise ValueError('TRADING_SYMBOL must be a valid trading pair (e.g., BTCUSDT)')
        return v.upper()
    
    @field_validator('timeframe')
    def validate_timeframe(cls, v):
        """Validate timeframe against supported intervals."""
        valid_timeframes = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d']
        if v not in valid_timeframes:
            raise ValueError(f'TIMEFRAME must be one of: {", ".join(valid_timeframes)}')
        return v

# Create a single instance to be imported
settings = Settings()

# You can still define helper functions or methods here if needed
def get_settings() -> Settings:
    """Dependency function to get settings."""
    # This function simply returns the globally configured instance.
    # Ensures that all parts of the application use the same settings object.
    return settings

# Example of how settings might be used elsewhere:
# from app.config.settings import settings
# print(f"Trading Symbol: {settings.trading_symbol}")
# print(f"Database URL: {settings.database_url}")

