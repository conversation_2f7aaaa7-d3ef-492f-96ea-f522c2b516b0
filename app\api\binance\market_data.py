"""Market data methods for Binance API client."""
from typing import Dict, List, Optional, Any, Union
import logging
from collections import OrderedDict

logger = logging.getLogger(__name__)

class MarketDataMixin:
    """Market data methods for Binance API client."""

    def _make_request(self, *args, **kwargs):
        """This method should be implemented by the concrete client class.

        In tests, this method will be mocked.
        """
        raise NotImplementedError("_make_request must be implemented by the concrete client class")

    def get_klines(self, symbol: str, interval: str, limit: int = 500) -> List[List[Any]]:
        """Get kline/candlestick data.

        Args:
            symbol (str): Trading pair symbol
            interval (str): Kline interval
            limit (int, optional): Number of records. Defaults to 500.

        Returns:
            List[List[Any]]: Kline data
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['interval'] = interval
        params['limit'] = limit

        return self._make_request('GET', '/fapi/v1/klines', params=params)

    def get_ticker(self, symbol: Optional[str] = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Get ticker price.

        Args:
            symbol (Optional[str], optional): Symbol to get ticker for. Defaults to None for all symbols.

        Returns:
            Union[Dict[str, Any], List[Dict[str, Any]]]: Ticker data
        """
        params = {}
        if symbol:
            params['symbol'] = symbol

        return self._make_request('GET', '/fapi/v1/ticker/price', params=params)

    def get_24hr_ticker(self, symbol: Optional[str] = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Get 24hr ticker price change statistics.

        Args:
            symbol (Optional[str], optional): Symbol to get ticker for. Defaults to None for all symbols.

        Returns:
            Union[Dict[str, Any], List[Dict[str, Any]]]: 24hr ticker data
        """
        params = {}
        if symbol:
            params['symbol'] = symbol

        return self._make_request('GET', '/fapi/v1/ticker/24hr', params=params)

    def get_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get orderbook.

        Args:
            symbol (str): Symbol to get orderbook for
            limit (int, optional): Limit of results. Defaults to 100.

        Returns:
            Dict[str, Any]: Orderbook data
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['limit'] = limit

        return self._make_request('GET', '/fapi/v1/depth', params=params)

    def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information.

        Returns:
            Dict[str, Any]: Exchange information
        """
        return self._make_request('GET', '/fapi/v1/exchangeInfo')

    def _init_exchange_info(self) -> None:
        """Initialize exchange info cache."""
        try:
            exchange_info = self.get_exchange_info()
            self.exchange_info_cache = exchange_info
            logger.info("Exchange info initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize exchange info: {e}")

    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get symbol information.

        Args:
            symbol (str): Symbol to get information for

        Returns:
            Optional[Dict[str, Any]]: Symbol information or None if not found
        """
        if not self.exchange_info_cache or 'symbols' not in self.exchange_info_cache:
            self._init_exchange_info()

        if 'symbols' in self.exchange_info_cache:
            for symbol_info in self.exchange_info_cache['symbols']:
                if symbol_info['symbol'] == symbol:
                    return symbol_info

        return None

    def get_recent_trades(self, symbol: str, limit: int = 500) -> List[Dict[str, Any]]:
        """Get recent trades.

        Args:
            symbol (str): Symbol to get trades for
            limit (int, optional): Limit of results. Defaults to 500.

        Returns:
            List[Dict[str, Any]]: Recent trades
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['limit'] = limit

        return self._make_request('GET', '/fapi/v1/trades', params=params)

    def get_historical_trades(self, symbol: str, limit: int = 500, from_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get historical trades.

        Args:
            symbol (str): Symbol to get trades for
            limit (int, optional): Limit of results. Defaults to 500.
            from_id (Optional[int], optional): Trade ID to fetch from. Defaults to None.

        Returns:
            List[Dict[str, Any]]: Historical trades
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['limit'] = limit
        if from_id:
            params['fromId'] = from_id

        return self._make_request('GET', '/fapi/v1/historicalTrades', params=params)

    def get_aggregate_trades(self, symbol: str, limit: int = 500,
                            from_id: Optional[int] = None,
                            start_time: Optional[int] = None,
                            end_time: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get aggregate trades.

        Args:
            symbol (str): Symbol to get trades for
            limit (int, optional): Limit of results. Defaults to 500.
            from_id (Optional[int], optional): ID to get aggregate trades from. Defaults to None.
            start_time (Optional[int], optional): Start time in ms. Defaults to None.
            end_time (Optional[int], optional): End time in ms. Defaults to None.

        Returns:
            List[Dict[str, Any]]: Aggregate trades
        """
        params = OrderedDict()
        params['symbol'] = symbol
        params['limit'] = limit
        if from_id:
            params['fromId'] = from_id
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time

        return self._make_request('GET', '/fapi/v1/aggTrades', params=params)
