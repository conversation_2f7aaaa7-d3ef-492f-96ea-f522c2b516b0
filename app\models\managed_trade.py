"""SQLAlchemy model for managed trades."""
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Numeric, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID

from app.models.base import Base
from app.models.trade_status import TradeStatus

class ManagedTradeDB(Base):
    """SQLAlchemy model for persisting ManagedTrade state."""
    __tablename__ = "managed_trades"

    # Use UUID for PostgreSQL
    trade_id = Column(UUID(as_uuid=True), primary_key=True, index=True, default=uuid.uuid4)
    symbol = Column(String, index=True, nullable=False)
    entry_order_id = Column(String, nullable=True)
    entry_fill_price = Column(Numeric(precision=20, scale=10), nullable=True) # Adjust precision/scale as needed
    entry_fill_qty = Column(Numeric(precision=20, scale=10), nullable=True)
    entry_side = Column(String, nullable=False) # 'BUY' or 'SELL'
    sl_order_id = Column(String, nullable=True)
    tp_order_id = Column(String, nullable=True)
    sl_price = Column(Numeric(precision=20, scale=10), nullable=True)
    tp_price = Column(Numeric(precision=20, scale=10), nullable=True)
    status = Column(SQLEnum(TradeStatus, name="trade_status_enum"), nullable=False, default=TradeStatus.PENDING_ENTRY)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    exit_price = Column(Numeric(precision=20, scale=10), nullable=True)
    pnl = Column(Numeric(precision=20, scale=10), nullable=True)
    pnl_percentage = Column(Numeric(precision=10, scale=2), nullable=True)
    closed_at = Column(DateTime(timezone=True), nullable=True)
