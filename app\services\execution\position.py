"""Position model for trading operations."""
from enum import Enum
from typing import Optional, Union, Dict, Any
from datetime import datetime
import uuid


class PositionSide(Enum):
    """Enum for position sides."""
    LONG = "LONG"
    SHORT = "SHORT"


class PositionStatus(Enum):
    """Enum for position statuses."""
    OPEN = "OPEN"
    PARTIALLY_CLOSED = "PARTIALLY_CLOSED"
    CLOSED = "CLOSED"


class Position:
    """Position model for trading operations.
    
    This class represents a trading position.
    """
    
    def __init__(self,
                symbol: str,
                side: Union[PositionSide, str],
                entry_price: float,
                quantity: float,
                stop_loss_price: Optional[float] = None,
                take_profit_price: Optional[float] = None,
                leverage: float = 1.0,
                strategy: Optional[str] = None,
                position_id: Optional[str] = None,
                timestamp: Optional[datetime] = None):
        """Initialize a position.
        
        Args:
            symbol: The trading pair symbol.
            side: The position side (LONG or SHORT).
            entry_price: The entry price.
            quantity: The position quantity.
            stop_loss_price: The stop loss price.
            take_profit_price: The take profit price.
            leverage: The leverage used.
            strategy: The strategy that created this position.
            position_id: The position ID (generated if not provided).
            timestamp: The position timestamp (defaults to now).
        """
        self.symbol = symbol
        
        # Handle enum or string for side
        if isinstance(side, PositionSide):
            self.side = side
        else:
            self.side = PositionSide(side.upper())
            
        self.entry_price = entry_price
        self.quantity = quantity
        self.stop_loss_price = stop_loss_price
        self.take_profit_price = take_profit_price
        self.leverage = leverage
        self.strategy = strategy
        self.position_id = position_id or f"pos_{uuid.uuid4().hex[:12]}"
        self.timestamp = timestamp or datetime.now()
        
        # Position tracking
        self.status = PositionStatus.OPEN
        self.remaining_quantity = quantity
        self.realized_pnl = 0.0
        self.unrealized_pnl = 0.0
        self.exit_price = None
        self.exit_timestamp = None
        self.highest_price = entry_price if self.side == PositionSide.LONG else None
        self.lowest_price = entry_price if self.side == PositionSide.SHORT else None
        self.last_price = entry_price
        
        # Position value
        self.position_value = entry_price * quantity
        
    def update_unrealized_pnl(self, current_price: float) -> float:
        """Update the unrealized PnL for the position.
        
        Args:
            current_price: The current market price.
            
        Returns:
            The updated unrealized PnL.
        """
        self.last_price = current_price
        
        # Update highest/lowest price tracking
        if self.side == PositionSide.LONG:
            self.highest_price = max(self.highest_price or current_price, current_price)
        else:
            self.lowest_price = min(self.lowest_price or current_price, current_price)
        
        if self.remaining_quantity == 0:
            self.unrealized_pnl = 0.0
            return 0.0
            
        if self.side == PositionSide.LONG:
            # For long positions, profit when price increases
            price_change = current_price - self.entry_price
        else:
            # For short positions, profit when price decreases
            price_change = self.entry_price - current_price
            
        # Calculate unrealized PnL
        self.unrealized_pnl = price_change * self.remaining_quantity
        return self.unrealized_pnl
        
    def close_position(self, exit_price: float, quantity: Optional[float] = None) -> float:
        """Close the position fully or partially.
        
        Args:
            exit_price: The exit price.
            quantity: The quantity to close (defaults to all remaining).
            
        Returns:
            The realized PnL from this close operation.
            
        Raises:
            ValueError: If quantity exceeds remaining quantity.
        """
        close_quantity = quantity if quantity is not None else self.remaining_quantity
        
        if close_quantity > self.remaining_quantity:
            raise ValueError(f"Cannot close {close_quantity} units, only {self.remaining_quantity} remaining")
            
        # Calculate PnL for this close
        if self.side == PositionSide.LONG:
            # For long positions, profit when exit price > entry price
            price_change = exit_price - self.entry_price
        else:
            # For short positions, profit when exit price < entry price
            price_change = self.entry_price - exit_price
            
        # Calculate realized PnL for this close
        pnl_this_close = price_change * close_quantity
        self.realized_pnl += pnl_this_close
        
        # Update remaining quantity
        self.remaining_quantity -= close_quantity
        
        # Update position status
        if self.remaining_quantity <= 0:
            self.status = PositionStatus.CLOSED
            self.exit_price = exit_price
            self.exit_timestamp = datetime.now()
        else:
            self.status = PositionStatus.PARTIALLY_CLOSED
            
        return pnl_this_close
        
    def update_stop_loss(self, new_stop_loss: float) -> None:
        """Update the stop loss price.
        
        Args:
            new_stop_loss: The new stop loss price.
            
        Raises:
            ValueError: If the new stop loss is invalid.
        """
        # Validate stop loss based on position side
        if self.side == PositionSide.LONG and new_stop_loss > self.entry_price:
            raise ValueError("Stop loss for long position must be below entry price")
        elif self.side == PositionSide.SHORT and new_stop_loss < self.entry_price:
            raise ValueError("Stop loss for short position must be above entry price")
            
        self.stop_loss_price = new_stop_loss
        
    def update_take_profit(self, new_take_profit: float) -> None:
        """Update the take profit price.
        
        Args:
            new_take_profit: The new take profit price.
            
        Raises:
            ValueError: If the new take profit is invalid.
        """
        # Validate take profit based on position side
        if self.side == PositionSide.LONG and new_take_profit < self.entry_price:
            raise ValueError("Take profit for long position must be above entry price")
        elif self.side == PositionSide.SHORT and new_take_profit > self.entry_price:
            raise ValueError("Take profit for short position must be below entry price")
            
        self.take_profit_price = new_take_profit
        
    def check_stop_loss_triggered(self, current_price: float) -> bool:
        """Check if the stop loss is triggered.
        
        Args:
            current_price: The current market price.
            
        Returns:
            True if stop loss is triggered, False otherwise.
        """
        if self.stop_loss_price is None:
            return False
            
        if self.side == PositionSide.LONG:
            # For long positions, stop loss triggers when price falls below stop loss
            return current_price <= self.stop_loss_price
        else:
            # For short positions, stop loss triggers when price rises above stop loss
            return current_price >= self.stop_loss_price
            
    def check_take_profit_triggered(self, current_price: float) -> bool:
        """Check if the take profit is triggered.
        
        Args:
            current_price: The current market price.
            
        Returns:
            True if take profit is triggered, False otherwise.
        """
        if self.take_profit_price is None:
            return False
            
        if self.side == PositionSide.LONG:
            # For long positions, take profit triggers when price rises above take profit
            return current_price >= self.take_profit_price
        else:
            # For short positions, take profit triggers when price falls below take profit
            return current_price <= self.take_profit_price
            
    def get_current_risk_reward_ratio(self, current_price: float) -> Optional[Dict[str, float]]:
        """Calculate the current risk-reward ratio.
        
        Args:
            current_price: The current market price.
            
        Returns:
            Dictionary with risk-reward metrics, or None if stop loss or take profit is not set.
        """
        if self.stop_loss_price is None or self.take_profit_price is None:
            return None
            
        if self.side == PositionSide.LONG:
            risk = self.entry_price - self.stop_loss_price
            current_reward = current_price - self.entry_price
            max_reward = self.take_profit_price - self.entry_price
        else:
            risk = self.stop_loss_price - self.entry_price
            current_reward = self.entry_price - current_price
            max_reward = self.entry_price - self.take_profit_price
            
        if risk <= 0:
            return None
            
        current_rr = current_reward / risk
        max_rr = max_reward / risk
        
        return {
            'current_rr': current_rr,
            'max_rr': max_rr,
            'percent_to_target': 0 if max_reward <= 0 else min(1.0, current_reward / max_reward)
        }
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the position to a dictionary.
        
        Returns:
            Dictionary representation of the position.
        """
        return {
            'position_id': self.position_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'entry_price': self.entry_price,
            'quantity': self.quantity,
            'remaining_quantity': self.remaining_quantity,
            'stop_loss_price': self.stop_loss_price,
            'take_profit_price': self.take_profit_price,
            'leverage': self.leverage,
            'strategy': self.strategy,
            'status': self.status.value,
            'timestamp': self.timestamp.isoformat(),
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'pnl': self.realized_pnl + self.unrealized_pnl,
            'exit_price': self.exit_price,
            'exit_timestamp': self.exit_timestamp.isoformat() if self.exit_timestamp else None,
            'position_value': self.position_value,
            'last_price': self.last_price
        } 