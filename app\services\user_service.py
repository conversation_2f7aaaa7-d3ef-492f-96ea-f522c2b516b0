from app.services.auth_service import fake_users_db, get_user, User, UserInDB # Import User models from auth_service
from typing import Optional

def get_user_by_username(username: str) -> Optional[User]:
    """
    Get a user by username, returns a User object (without password) or None if not found
    """
    user = get_user(fake_users_db, username)
    if user:
        # Convert UserInDB to User to exclude password
        return User(
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            # roles=user.roles # Roles might not be needed if User model doesn't have it
        )
    return None

def get_all_users() -> list[User]:
    """
    Get all users, returns a list of User objects
    """
    users = []
    for username in fake_users_db:
        user = get_user_by_username(username)
        if user:
            users.append(user)
    return users