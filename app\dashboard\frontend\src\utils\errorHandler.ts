import { AxiosError } from 'axios';

export interface ErrorInfo {
  message: string;
  statusCode?: number;
  severity: 'error' | 'warning' | 'info';
  retryable: boolean;
}

export class ApiError extends Error {
  public statusCode?: number;
  public severity: 'error' | 'warning' | 'info';
  public retryable: boolean;

  constructor(
    message: string,
    statusCode?: number,
    severity: 'error' | 'warning' | 'info' = 'error',
    retryable = false
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.severity = severity;
    this.retryable = retryable;
  }
}

export const handleApiError = (error: unknown): ApiError => {
  if (error instanceof ApiError) {
    return error;
  }

  if (error instanceof AxiosError) {
    const statusCode = error.response?.status;
    const errorData = error.response?.data;
    
    // Extract error message
    let message = 'An unexpected error occurred';
    if (typeof errorData === 'string') {
      message = errorData;
    } else if (errorData?.detail) {
      message = errorData.detail;
    } else if (errorData?.message) {
      message = errorData.message;
    } else if (error.message) {
      message = error.message;
    }

    // Determine severity and retryability based on status code
    let severity: 'error' | 'warning' | 'info' = 'error';
    let retryable = false;

    if (statusCode) {
      switch (true) {
        case statusCode >= 500:
          severity = 'error';
          retryable = true;
          break;
        case statusCode === 429:
          severity = 'warning';
          retryable = true;
          message = 'Too many requests. Please try again later.';
          break;
        case statusCode === 401:
          severity = 'warning';
          retryable = false;
          message = 'Authentication required. Please log in again.';
          break;
        case statusCode === 403:
          severity = 'error';
          retryable = false;
          message = 'Access denied. You do not have permission to perform this action.';
          break;
        case statusCode === 404:
          severity = 'warning';
          retryable = false;
          message = 'The requested resource was not found.';
          break;
        case statusCode >= 400:
          severity = 'warning';
          retryable = false;
          break;
        default:
          severity = 'error';
          retryable = true;
      }
    } else if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
      severity = 'error';
      retryable = true;
      message = 'Network error. Please check your connection and try again.';
    }

    return new ApiError(message, statusCode, severity, retryable);
  }

  if (error instanceof Error) {
    return new ApiError(error.message, undefined, 'error', false);
  }

  return new ApiError('An unknown error occurred', undefined, 'error', false);
};

export const getErrorMessage = (error: unknown): string => {
  const apiError = handleApiError(error);
  return apiError.message;
};

export const isRetryableError = (error: unknown): boolean => {
  const apiError = handleApiError(error);
  return apiError.retryable;
};

export const getErrorSeverity = (error: unknown): 'error' | 'warning' | 'info' => {
  const apiError = handleApiError(error);
  return apiError.severity;
};

// Retry utility for API calls
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  maxRetries = 3,
  delayMs = 1000
): Promise<T> => {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      if (!isRetryableError(error) || attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff
      const delay = delayMs * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

// Hook for handling errors in components
export const useErrorHandler = () => {
  const handleError = (error: unknown, context = '') => {
    const apiError = handleApiError(error);
    
    // Log error for debugging
    console.error(`Error in ${context}:`, {
      message: apiError.message,
      statusCode: apiError.statusCode,
      severity: apiError.severity,
      retryable: apiError.retryable,
      originalError: error
    });

    return apiError;
  };

  return { handleError };
};