#!/usr/bin/env python3
"""
Comprehensive Test Suite for Task 3.1.3: W&B Cost Optimization Tracking
Tests all aspects of the cost optimization tracking system including W&B integration,
cost metrics accuracy, and performance validation.
"""

import asyncio
import pytest
import json
import numpy as np
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import AsyncMock, MagicMock

# Import the modules to test
from app.services.mcp.wandb_cost_tracker import (
    WandBCostTracker,
    CostOptimizationMetrics,
    CostTrendAnalysis,
    CostEffectivenessReport,
    create_wandb_cost_tracker
)
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.cost_calculator import CostCalculator, TotalTradingCost, OrderType
from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator

class TestWandBCostTracking:
    """Comprehensive test suite for W&B cost optimization tracking"""
    
    @pytest.fixture
    async def mock_redis_service(self):
        """Create mock Redis service for testing"""
        mock_redis = AsyncMock()
        mock_redis.get.return_value = None  # Default no cache
        mock_redis.setex.return_value = True
        return mock_redis
    
    @pytest.fixture
    async def mock_supabase_service(self):
        """Create mock Supabase service for testing"""
        mock_supabase = AsyncMock()
        mock_supabase.store_trade_execution.return_value = {"success": True}
        return mock_supabase
    
    @pytest.fixture
    async def mock_cost_calculator(self):
        """Create mock cost calculator for testing"""
        mock_calculator = AsyncMock()
        
        # Mock cost calculation result
        mock_result = TotalTradingCost(
            symbol="BTC",
            trade_size_usd=10000.0,
            order_type="market",
            exchange_fees_usd=10.0,
            funding_costs_usd=2.0,
            withdrawal_fees_usd=1.0,
            slippage_cost_usd=5.0,
            market_impact_cost_usd=3.0,
            bid_ask_spread_cost_usd=2.0,
            total_cost_usd=23.0,
            total_cost_bps=23.0,
            cost_percentage=0.23,
            calculation_timestamp=datetime.now(),
            cost_breakdown={
                "exchange_fees": 10.0,
                "slippage_cost": 5.0,
                "market_impact_cost": 3.0
            },
            optimization_suggestions=["Use limit orders", "Consider smaller size"],
            confidence=0.85
        )
        
        mock_calculator.calculate_total_trading_cost.return_value = mock_result
        mock_calculator.exchange_fees = {
            "binance": MagicMock(),
            "coinbase": MagicMock(),
            "kraken": MagicMock()
        }
        mock_calculator.config = {"default_exchange": "binance"}
        
        return mock_calculator
    
    @pytest.fixture
    async def mock_slippage_estimator(self):
        """Create mock slippage estimator for testing"""
        mock_estimator = AsyncMock()
        
        # Mock slippage analysis
        from dataclasses import dataclass
        
        @dataclass
        class MockSlippageAnalysis:
            consensus_slippage_bps: float = 5.0
            min_slippage_bps: float = 3.0
            max_slippage_bps: float = 8.0
        
        mock_estimator.estimate_multi_exchange_slippage.return_value = MockSlippageAnalysis()
        return mock_estimator
    
    @pytest.fixture
    async def wandb_cost_tracker(self, mock_redis_service, mock_cost_calculator, 
                                mock_slippage_estimator, mock_supabase_service):
        """Create WandB cost tracker for testing"""
        return WandBCostTracker(
            redis_service=mock_redis_service,
            cost_calculator=mock_cost_calculator,
            slippage_estimator=mock_slippage_estimator,
            supabase_service=mock_supabase_service,
            wandb_config={
                "project_name": "test_cost_optimization",
                "entity": "test_team",
                "tags": ["test"]
            }
        )

class TestCostOptimizationTracking:
    """Test cost optimization experiment tracking functionality"""
    
    async def test_track_cost_optimization_experiment_basic(self, wandb_cost_tracker):
        """Test basic cost optimization experiment tracking"""
        
        # Test parameters
        strategy_name = "test_strategy"
        symbol = "BTC"
        trade_size_usd = 10000.0
        
        # Execute tracking
        start_time = time.perf_counter()
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name=strategy_name,
            symbol=symbol,
            trade_size_usd=trade_size_usd,
            order_type=OrderType.MARKET
        )
        end_time = time.perf_counter()
        execution_time = (end_time - start_time) * 1000
        
        # Verify result structure
        assert isinstance(result, CostOptimizationMetrics)
        assert result.strategy_name == strategy_name
        assert result.symbol == symbol
        assert result.trade_size_usd == trade_size_usd
        
        # Verify cost metrics
        assert result.total_cost_usd > 0
        assert result.total_cost_bps > 0
        assert result.exchange_fees_usd >= 0
        assert result.slippage_cost_usd >= 0
        
        # Verify performance requirement (<200ms)
        assert execution_time < 200, f"Cost tracking took {execution_time:.2f}ms, should be <200ms"
        assert result.calculation_time_ms < 200
        
        print(f"✅ Cost optimization tracking completed in {execution_time:.2f}ms")
        print(f"   Total cost: {result.total_cost_bps:.2f} bps")
        print(f"   Confidence: {result.confidence_score:.2f}")
    
    async def test_track_cost_optimization_with_exchange_comparison(self, wandb_cost_tracker):
        """Test cost optimization tracking with exchange comparison"""
        
        # Mock exchange cost comparison
        exchange_costs = {
            "binance": 20.0,
            "coinbase": 25.0,
            "kraken": 22.0
        }
        
        wandb_cost_tracker._analyze_exchange_costs = AsyncMock(return_value=exchange_costs)
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="exchange_optimization",
            symbol="ETH",
            trade_size_usd=25000.0
        )
        
        # Verify exchange optimization
        assert result.optimal_exchange in exchange_costs.keys()
        assert result.cost_savings_vs_default_bps >= 0
        assert len(result.exchange_cost_ranking) > 0
        
        print(f"✅ Exchange comparison completed")
        print(f"   Optimal exchange: {result.optimal_exchange}")
        print(f"   Cost savings: {result.cost_savings_vs_default_bps:.2f} bps")
    
    async def test_track_cost_optimization_with_slippage_analysis(self, wandb_cost_tracker):
        """Test cost optimization tracking with slippage analysis"""
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="slippage_optimization",
            symbol="BTC",
            trade_size_usd=50000.0
        )
        
        # Verify slippage metrics
        assert result.slippage_estimation_accuracy >= 0
        assert result.slippage_estimation_accuracy <= 1.0
        assert result.slippage_improvement_bps >= 0
        assert result.model_slippage_vs_actual > 0
        
        print(f"✅ Slippage analysis completed")
        print(f"   Slippage accuracy: {result.slippage_estimation_accuracy:.2f}")
        print(f"   Slippage improvement: {result.slippage_improvement_bps:.2f} bps")
    
    async def test_cost_optimization_caching(self, wandb_cost_tracker):
        """Test cost optimization results caching"""
        
        strategy_name = "cache_test"
        symbol = "BTC"
        
        # First call - should calculate
        start_time = time.perf_counter()
        result1 = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name=strategy_name,
            symbol=symbol,
            trade_size_usd=10000.0
        )
        first_call_time = (time.perf_counter() - start_time) * 1000
        
        # Verify Redis cache was called
        wandb_cost_tracker.redis_service.setex.assert_called()
        
        # Verify caching functionality
        cache_calls = wandb_cost_tracker.redis_service.setex.call_count
        assert cache_calls > 0, "Caching should be used"
        
        print(f"✅ Caching functionality verified")
        print(f"   First call time: {first_call_time:.2f}ms")
        print(f"   Cache operations: {cache_calls}")

class TestCostTrendAnalysis:
    """Test cost trend monitoring and analysis functionality"""
    
    async def test_analyze_cost_trends_basic(self, wandb_cost_tracker):
        """Test basic cost trend analysis"""
        
        # Mock historical cost data
        mock_history = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "total_cost_bps": 20.0 - i * 0.5,  # Improving trend
                "exchange_fees_usd": 10.0,
                "slippage_cost_usd": 5.0,
                "market_impact_cost_usd": 2.0,
                "optimal_exchange": "binance",
                "cost_improvement_bps": i * 0.3
            }
            for i in range(20)  # 20 data points
        ]
        
        wandb_cost_tracker._get_cost_history = AsyncMock(return_value=mock_history)
        
        # Execute trend analysis
        start_time = time.perf_counter()
        result = await wandb_cost_tracker.analyze_cost_trends(
            symbol="BTC",
            analysis_hours=72
        )
        execution_time = (time.perf_counter() - start_time) * 1000
        
        # Verify result structure
        assert isinstance(result, CostTrendAnalysis)
        assert result.symbol == "BTC"
        assert result.analysis_period_hours == 72
        
        # Verify trend metrics
        assert result.avg_cost_bps > 0
        assert result.cost_volatility >= 0
        assert result.cost_trend_direction in ["improving", "worsening", "stable"]
        
        # Verify component analysis
        assert "avg" in result.fees_trend
        assert "trend" in result.slippage_trend
        assert "volatility" in result.market_impact_trend
        
        # Performance validation
        assert execution_time < 2000, f"Trend analysis took {execution_time:.2f}ms, should be <2000ms"
        
        print(f"✅ Cost trend analysis completed in {execution_time:.2f}ms")
        print(f"   Average cost: {result.avg_cost_bps:.2f} bps")
        print(f"   Trend direction: {result.cost_trend_direction}")
        print(f"   Improvement rate: {result.cost_improvement_rate:.2f} bps/day")
    
    async def test_analyze_cost_trends_with_insufficient_data(self, wandb_cost_tracker):
        """Test trend analysis with insufficient data"""
        
        # Mock insufficient historical data
        mock_history = [
            {
                "timestamp": datetime.now().isoformat(),
                "total_cost_bps": 20.0,
                "exchange_fees_usd": 10.0,
                "slippage_cost_usd": 5.0,
                "market_impact_cost_usd": 2.0,
                "optimal_exchange": "binance",
                "cost_improvement_bps": 0
            }
        ]  # Only 1 data point (minimum is 10)
        
        wandb_cost_tracker._get_cost_history = AsyncMock(return_value=mock_history)
        
        result = await wandb_cost_tracker.analyze_cost_trends("BTC")
        
        # Should return fallback result
        assert isinstance(result, CostTrendAnalysis)
        assert result.cost_trend_direction == "stable"
        
        print(f"✅ Insufficient data handling verified")
    
    async def test_cost_trend_optimization_effectiveness(self, wandb_cost_tracker):
        """Test optimization effectiveness metrics in trend analysis"""
        
        # Mock history with optimization data
        mock_history = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "total_cost_bps": 20.0,
                "exchange_fees_usd": 10.0,
                "slippage_cost_usd": 5.0,
                "market_impact_cost_usd": 2.0,
                "optimal_exchange": "binance",
                "cost_improvement_bps": 2.0 if i % 3 == 0 else 0  # 33% optimization rate
            }
            for i in range(15)
        ]
        
        wandb_cost_tracker._get_cost_history = AsyncMock(return_value=mock_history)
        
        result = await wandb_cost_tracker.analyze_cost_trends("BTC")
        
        # Verify optimization effectiveness metrics
        assert result.optimization_adoption_rate >= 0
        assert result.optimization_adoption_rate <= 1.0
        assert result.avg_savings_per_optimization >= 0
        assert result.cost_prediction_mae > 0
        
        print(f"✅ Optimization effectiveness metrics verified")
        print(f"   Adoption rate: {result.optimization_adoption_rate:.2f}")
        print(f"   Avg savings: {result.avg_savings_per_optimization:.2f} bps")

class TestCostEffectivenessMeasurement:
    """Test cost optimization effectiveness measurement functionality"""
    
    async def test_measure_cost_optimization_effectiveness_basic(self, wandb_cost_tracker):
        """Test basic cost effectiveness measurement"""
        
        # Execute effectiveness measurement
        start_time = time.perf_counter()
        result = await wandb_cost_tracker.measure_cost_optimization_effectiveness(
            optimization_strategy="exchange_selection",
            measurement_days=7
        )
        execution_time = (time.perf_counter() - start_time) * 1000
        
        # Verify result structure
        assert isinstance(result, CostEffectivenessReport)
        assert result.optimization_strategy == "exchange_selection"
        assert result.measurement_period_days == 7
        
        # Verify effectiveness metrics
        assert result.baseline_cost_bps >= 0
        assert result.optimized_cost_bps >= 0
        assert result.total_improvement_bps >= 0
        assert result.improvement_percentage >= 0
        
        # Verify ROI metrics
        assert result.total_trades_analyzed >= 0
        assert result.total_volume_usd >= 0
        assert result.total_cost_savings_usd >= 0
        assert result.optimization_roi >= 0
        
        # Performance validation
        assert execution_time < 500, f"Effectiveness measurement took {execution_time:.2f}ms, should be <500ms"
        
        print(f"✅ Cost effectiveness measurement completed in {execution_time:.2f}ms")
        print(f"   Baseline cost: {result.baseline_cost_bps:.2f} bps")
        print(f"   Optimized cost: {result.optimized_cost_bps:.2f} bps")
        print(f"   Improvement: {result.total_improvement_bps:.2f} bps ({result.improvement_percentage:.1f}%)")
    
    async def test_cost_effectiveness_optimization_breakdown(self, wandb_cost_tracker):
        """Test optimization breakdown by type"""
        
        result = await wandb_cost_tracker.measure_cost_optimization_effectiveness(
            optimization_strategy="comprehensive",
            measurement_days=14
        )
        
        # Verify optimization breakdown
        assert result.exchange_optimization_savings >= 0
        assert result.timing_optimization_savings >= 0
        assert result.size_optimization_savings >= 0
        assert result.order_type_optimization_savings >= 0
        
        # Verify success metrics
        assert result.optimization_success_rate >= 0
        assert result.optimization_success_rate <= 1.0
        assert result.avg_implementation_time_ms > 0
        assert result.recommendation_accuracy >= 0
        assert result.recommendation_accuracy <= 1.0
        
        print(f"✅ Optimization breakdown verified")
        print(f"   Exchange savings: {result.exchange_optimization_savings:.2f} bps")
        print(f"   Timing savings: {result.timing_optimization_savings:.2f} bps")
        print(f"   Success rate: {result.optimization_success_rate:.2f}")

class TestWandBIntegration:
    """Test W&B MCP integration functionality"""
    
    async def test_wandb_cost_metrics_logging(self, wandb_cost_tracker):
        """Test W&B cost metrics logging"""
        
        # Track cost optimization
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="wandb_test",
            symbol="BTC",
            trade_size_usd=15000.0
        )
        
        # Verify W&B logging was called
        wandb_cost_tracker.redis_service.setex.assert_called()
        
        # Check that W&B queue key was used
        call_args = wandb_cost_tracker.redis_service.setex.call_args_list
        wandb_calls = [call for call in call_args if "wandb_logs:cost_optimization" in str(call)]
        assert len(wandb_calls) > 0, "W&B logging should be called"
        
        print(f"✅ W&B cost metrics logging verified")
        print(f"   Logged experiment ID: {result.experiment_id}")
    
    async def test_wandb_trend_analysis_logging(self, wandb_cost_tracker):
        """Test W&B trend analysis logging"""
        
        # Mock historical data for trend analysis
        mock_history = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "total_cost_bps": 15.0,
                "exchange_fees_usd": 8.0,
                "slippage_cost_usd": 4.0,
                "market_impact_cost_usd": 1.0,
                "optimal_exchange": "binance",
                "cost_improvement_bps": 1.0
            }
            for i in range(12)
        ]
        wandb_cost_tracker._get_cost_history = AsyncMock(return_value=mock_history)
        
        # Execute trend analysis
        result = await wandb_cost_tracker.analyze_cost_trends("BTC")
        
        # Verify W&B trend logging
        call_args = wandb_cost_tracker.redis_service.setex.call_args_list
        trend_calls = [call for call in call_args if "wandb_logs:cost_trends" in str(call)]
        assert len(trend_calls) > 0, "W&B trend logging should be called"
        
        print(f"✅ W&B trend analysis logging verified")
    
    async def test_wandb_effectiveness_report_logging(self, wandb_cost_tracker):
        """Test W&B effectiveness report logging"""
        
        # Execute effectiveness measurement
        result = await wandb_cost_tracker.measure_cost_optimization_effectiveness(
            "test_strategy", 7
        )
        
        # Verify W&B effectiveness logging
        call_args = wandb_cost_tracker.redis_service.setex.call_args_list
        effectiveness_calls = [call for call in call_args if "wandb_logs:cost_effectiveness" in str(call)]
        assert len(effectiveness_calls) > 0, "W&B effectiveness logging should be called"
        
        print(f"✅ W&B effectiveness report logging verified")

class TestPerformanceValidation:
    """Test performance requirements and optimization"""
    
    async def test_cost_tracking_performance_target(self, wandb_cost_tracker):
        """Test that cost tracking meets <200ms performance target"""
        
        # Test multiple operations to get average performance
        execution_times = []
        
        for i in range(5):
            start_time = time.perf_counter()
            await wandb_cost_tracker.track_cost_optimization_experiment(
                strategy_name=f"perf_test_{i}",
                symbol="BTC",
                trade_size_usd=10000.0
            )
            execution_time = (time.perf_counter() - start_time) * 1000
            execution_times.append(execution_time)
        
        avg_execution_time = np.mean(execution_times)
        max_execution_time = max(execution_times)
        
        # Performance assertions
        assert avg_execution_time < 200, f"Average execution time {avg_execution_time:.2f}ms exceeds 200ms target"
        assert max_execution_time < 300, f"Max execution time {max_execution_time:.2f}ms exceeds 300ms limit"
        
        print(f"✅ Performance target met")
        print(f"   Average execution time: {avg_execution_time:.2f}ms")
        print(f"   Max execution time: {max_execution_time:.2f}ms")
        print(f"   All executions: {[f'{t:.1f}ms' for t in execution_times]}")
    
    async def test_trend_analysis_performance_target(self, wandb_cost_tracker):
        """Test that trend analysis meets <2000ms performance target"""
        
        # Mock large historical dataset
        large_history = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "total_cost_bps": 15.0 + np.random.normal(0, 2),
                "exchange_fees_usd": 8.0,
                "slippage_cost_usd": 4.0,
                "market_impact_cost_usd": 1.0,
                "optimal_exchange": "binance",
                "cost_improvement_bps": np.random.exponential(0.5)
            }
            for i in range(100)  # Large dataset
        ]
        wandb_cost_tracker._get_cost_history = AsyncMock(return_value=large_history)
        
        # Test trend analysis performance
        start_time = time.perf_counter()
        result = await wandb_cost_tracker.analyze_cost_trends("BTC", 168)
        execution_time = (time.perf_counter() - start_time) * 1000
        
        # Performance assertion
        assert execution_time < 2000, f"Trend analysis took {execution_time:.2f}ms, should be <2000ms"
        
        print(f"✅ Trend analysis performance target met: {execution_time:.2f}ms")
    
    async def test_effectiveness_measurement_performance_target(self, wandb_cost_tracker):
        """Test that effectiveness measurement meets <500ms performance target"""
        
        start_time = time.perf_counter()
        result = await wandb_cost_tracker.measure_cost_optimization_effectiveness(
            "performance_test", 7
        )
        execution_time = (time.perf_counter() - start_time) * 1000
        
        # Performance assertion
        assert execution_time < 500, f"Effectiveness measurement took {execution_time:.2f}ms, should be <500ms"
        
        print(f"✅ Effectiveness measurement performance target met: {execution_time:.2f}ms")

class TestCostMetricsAccuracy:
    """Test cost metrics accuracy and validation"""
    
    async def test_cost_improvement_calculation_accuracy(self, wandb_cost_tracker):
        """Test accuracy of cost improvement calculations"""
        
        # Test with known baseline
        wandb_cost_tracker._get_baseline_cost = AsyncMock(return_value=25.0)  # 25 bps baseline
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="accuracy_test",
            symbol="BTC",
            trade_size_usd=10000.0
        )
        
        # Expected improvement: 25.0 - 23.0 = 2.0 bps (from mock cost calculator)
        expected_improvement = 2.0
        actual_improvement = result.cost_improvement_bps
        
        # Allow small tolerance for floating point operations
        assert abs(actual_improvement - expected_improvement) < 0.1, \
            f"Cost improvement calculation inaccurate: expected {expected_improvement}, got {actual_improvement}"
        
        print(f"✅ Cost improvement calculation accurate: {actual_improvement:.2f} bps")
    
    async def test_cost_component_breakdown_accuracy(self, wandb_cost_tracker):
        """Test accuracy of cost component breakdown"""
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="breakdown_test",
            symbol="BTC",
            trade_size_usd=10000.0
        )
        
        # Verify component breakdown adds up to total
        component_sum = (
            result.exchange_fees_usd +
            result.slippage_cost_usd +
            result.market_impact_cost_usd +
            result.funding_costs_usd +
            result.withdrawal_fees_usd
        )
        
        # Should match total cost (allowing for small rounding differences)
        assert abs(component_sum - result.total_cost_usd) < 0.01, \
            f"Cost breakdown inaccurate: components sum to {component_sum}, total is {result.total_cost_usd}"
        
        print(f"✅ Cost component breakdown accurate")
        print(f"   Components sum: ${component_sum:.2f}")
        print(f"   Total cost: ${result.total_cost_usd:.2f}")
    
    async def test_cost_prediction_accuracy_validation(self, wandb_cost_tracker):
        """Test cost prediction accuracy validation"""
        
        # Mock historical data for accuracy calculation
        mock_recent_costs = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "total_cost_bps": 22.0 + i * 0.1  # Recent average around 23 bps
            }
            for i in range(10)
        ]
        wandb_cost_tracker._get_recent_historical_costs = AsyncMock(return_value=mock_recent_costs)
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="prediction_test",
            symbol="BTC",
            trade_size_usd=10000.0
        )
        
        # Prediction accuracy should be reasonable (>0.5 for good predictions)
        assert result.cost_prediction_accuracy >= 0.0
        assert result.cost_prediction_accuracy <= 1.0
        assert result.cost_prediction_accuracy > 0.5, \
            f"Cost prediction accuracy too low: {result.cost_prediction_accuracy:.2f}"
        
        print(f"✅ Cost prediction accuracy validated: {result.cost_prediction_accuracy:.2f}")

class TestIntegrationWithExistingInfrastructure:
    """Test integration with existing cost calculation infrastructure"""
    
    async def test_integration_with_cost_calculator(self, wandb_cost_tracker):
        """Test integration with CostCalculator"""
        
        # Verify cost calculator integration
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="integration_test",
            symbol="BTC",
            trade_size_usd=20000.0,
            order_type=OrderType.LIMIT,
            exchange="binance",
            leverage=2.0
        )
        
        # Verify cost calculator was called with correct parameters
        wandb_cost_tracker.cost_calculator.calculate_total_trading_cost.assert_called()
        call_args = wandb_cost_tracker.cost_calculator.calculate_total_trading_cost.call_args
        
        assert call_args[1]['symbol'] == "BTC"
        assert call_args[1]['trade_size_usd'] == 20000.0
        assert call_args[1]['order_type'] == OrderType.LIMIT
        assert call_args[1]['exchange'] == "binance"
        assert call_args[1]['leverage'] == 2.0
        
        print(f"✅ CostCalculator integration verified")
    
    async def test_integration_with_slippage_estimator(self, wandb_cost_tracker):
        """Test integration with EnhancedSlippageEstimator"""
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="slippage_integration_test",
            symbol="ETH",
            trade_size_usd=30000.0
        )
        
        # Verify slippage estimator was called
        wandb_cost_tracker.slippage_estimator.estimate_multi_exchange_slippage.assert_called()
        
        # Verify slippage metrics are populated
        assert result.slippage_estimation_accuracy > 0
        assert result.slippage_improvement_bps >= 0
        
        print(f"✅ SlippageEstimator integration verified")
    
    async def test_integration_with_supabase_analytics(self, wandb_cost_tracker):
        """Test integration with Supabase analytics storage"""
        
        result = await wandb_cost_tracker.track_cost_optimization_experiment(
            strategy_name="supabase_integration_test",
            symbol="BTC",
            trade_size_usd=15000.0
        )
        
        # Verify Supabase storage was called
        wandb_cost_tracker.supabase_service.store_trade_execution.assert_called()
        
        # Verify correct data structure was passed
        call_args = wandb_cost_tracker.supabase_service.store_trade_execution.call_args[0][0]
        assert call_args['strategy_name'] == "supabase_integration_test"
        assert call_args['symbol'] == "BTC"
        assert call_args['action'] == 'COST_OPTIMIZATION_TRACKING'
        assert 'cost_metrics' in call_args['metadata']
        
        print(f"✅ Supabase analytics integration verified")

async def run_comprehensive_test_suite():
    """Run the complete test suite for W&B cost optimization tracking"""
    
    print("🚀 Starting W&B Cost Optimization Tracking Test Suite")
    print("=" * 70)
    
    # Initialize test environment
    mock_redis = AsyncMock()
    mock_redis.get.return_value = None
    mock_redis.setex.return_value = True
    
    mock_supabase = AsyncMock()
    mock_supabase.store_trade_execution.return_value = {"success": True}
    
    mock_cost_calculator = AsyncMock()
    mock_result = TotalTradingCost(
        symbol="BTC",
        trade_size_usd=10000.0,
        order_type="market",
        exchange_fees_usd=10.0,
        funding_costs_usd=2.0,
        withdrawal_fees_usd=1.0,
        slippage_cost_usd=5.0,
        market_impact_cost_usd=3.0,
        bid_ask_spread_cost_usd=2.0,
        total_cost_usd=23.0,
        total_cost_bps=23.0,
        cost_percentage=0.23,
        calculation_timestamp=datetime.now(),
        cost_breakdown={"exchange_fees": 10.0, "slippage_cost": 5.0},
        optimization_suggestions=["Use limit orders"],
        confidence=0.85
    )
    mock_cost_calculator.calculate_total_trading_cost.return_value = mock_result
    mock_cost_calculator.exchange_fees = {"binance": MagicMock(), "coinbase": MagicMock()}
    mock_cost_calculator.config = {"default_exchange": "binance"}
    
    mock_slippage_estimator = AsyncMock()
    from dataclasses import dataclass
    
    @dataclass
    class MockSlippageAnalysis:
        consensus_slippage_bps: float = 5.0
        min_slippage_bps: float = 3.0
        max_slippage_bps: float = 8.0
    
    mock_slippage_estimator.estimate_multi_exchange_slippage.return_value = MockSlippageAnalysis()
    
    # Create test tracker
    tracker = WandBCostTracker(
        redis_service=mock_redis,
        cost_calculator=mock_cost_calculator,
        slippage_estimator=mock_slippage_estimator,
        supabase_service=mock_supabase
    )
    
    # Test suite results
    test_results = {
        "passed": 0,
        "failed": 0,
        "total_time": 0
    }
    
    start_time = time.perf_counter()
    
    try:
        print("\n📊 Test 1: Basic Cost Optimization Tracking")
        result = await tracker.track_cost_optimization_experiment(
            strategy_name="test_strategy",
            symbol="BTC",
            trade_size_usd=10000.0
        )
        assert isinstance(result, CostOptimizationMetrics)
        assert result.calculation_time_ms < 200
        print(f"   ✅ PASSED - Execution time: {result.calculation_time_ms:.2f}ms")
        test_results["passed"] += 1
        
    except Exception as e:
        print(f"   ❌ FAILED - {e}")
        test_results["failed"] += 1
    
    try:
        print("\n📈 Test 2: Cost Trend Analysis")
        # Mock historical data
        mock_history = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "total_cost_bps": 20.0 - i * 0.5,
                "exchange_fees_usd": 10.0,
                "slippage_cost_usd": 5.0,
                "market_impact_cost_usd": 2.0,
                "optimal_exchange": "binance",
                "cost_improvement_bps": i * 0.3
            }
            for i in range(20)
        ]
        tracker._get_cost_history = AsyncMock(return_value=mock_history)
        
        trend_start = time.perf_counter()
        trend_result = await tracker.analyze_cost_trends("BTC")
        trend_time = (time.perf_counter() - trend_start) * 1000
        
        assert isinstance(trend_result, CostTrendAnalysis)
        assert trend_time < 2000
        print(f"   ✅ PASSED - Analysis time: {trend_time:.2f}ms")
        test_results["passed"] += 1
        
    except Exception as e:
        print(f"   ❌ FAILED - {e}")
        test_results["failed"] += 1
    
    try:
        print("\n📋 Test 3: Cost Effectiveness Measurement")
        effectiveness_start = time.perf_counter()
        effectiveness_result = await tracker.measure_cost_optimization_effectiveness(
            "test_strategy", 7
        )
        effectiveness_time = (time.perf_counter() - effectiveness_start) * 1000
        
        assert isinstance(effectiveness_result, CostEffectivenessReport)
        assert effectiveness_time < 500
        print(f"   ✅ PASSED - Measurement time: {effectiveness_time:.2f}ms")
        test_results["passed"] += 1
        
    except Exception as e:
        print(f"   ❌ FAILED - {e}")
        test_results["failed"] += 1
    
    try:
        print("\n⚡ Test 4: Performance Validation")
        perf_times = []
        for i in range(3):
            perf_start = time.perf_counter()
            await tracker.track_cost_optimization_experiment(
                strategy_name=f"perf_test_{i}",
                symbol="BTC",
                trade_size_usd=25000.0
            )
            perf_time = (time.perf_counter() - perf_start) * 1000
            perf_times.append(perf_time)
        
        avg_perf_time = np.mean(perf_times)
        assert avg_perf_time < 200
        print(f"   ✅ PASSED - Average performance: {avg_perf_time:.2f}ms")
        test_results["passed"] += 1
        
    except Exception as e:
        print(f"   ❌ FAILED - {e}")
        test_results["failed"] += 1
    
    try:
        print("\n🔗 Test 5: W&B Integration Validation")
        wb_result = await tracker.track_cost_optimization_experiment(
            strategy_name="wandb_test",
            symbol="ETH",
            trade_size_usd=15000.0
        )
        
        # Verify W&B logging was called
        assert mock_redis.setex.called
        wb_calls = [call for call in mock_redis.setex.call_args_list 
                   if "wandb_logs:cost_optimization" in str(call)]
        assert len(wb_calls) > 0
        print(f"   ✅ PASSED - W&B logging verified")
        test_results["passed"] += 1
        
    except Exception as e:
        print(f"   ❌ FAILED - {e}")
        test_results["failed"] += 1
    
    total_time = (time.perf_counter() - start_time) * 1000
    test_results["total_time"] = total_time
    
    # Print summary
    print("\n" + "=" * 70)
    print("🎯 W&B COST OPTIMIZATION TRACKING TEST SUMMARY")
    print("=" * 70)
    print(f"✅ Tests Passed: {test_results['passed']}")
    print(f"❌ Tests Failed: {test_results['failed']}")
    print(f"⏱️  Total Time: {total_time:.2f}ms")
    print(f"🎯 Success Rate: {test_results['passed']/(test_results['passed']+test_results['failed'])*100:.1f}%")
    
    if test_results["failed"] == 0:
        print("\n🎉 ALL TESTS PASSED - W&B Cost Optimization Tracking is ready for production!")
    else:
        print(f"\n⚠️  {test_results['failed']} tests failed - review implementation before production deployment")
    
    return test_results

if __name__ == "__main__":
    # Run the comprehensive test suite
    results = asyncio.run(run_comprehensive_test_suite())