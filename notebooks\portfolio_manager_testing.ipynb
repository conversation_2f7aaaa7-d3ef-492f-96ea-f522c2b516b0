{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Portfolio Manager Testing Notebook\n", "\n", "Quick testing environment for portfolio manager functionality and ensemble strategy execution."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import asyncio\n", "from datetime import datetime\n", "\n", "# Add project root\n", "project_root = '/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2'\n", "sys.path.append(project_root)\n", "\n", "from app.ml.models.weight_optimizer import WeightOptimizer\n", "\n", "print(\"🚀 Portfolio Manager Testing Environment\")\n", "print(f\"Time: {datetime.now()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick weight test\n", "optimizer = WeightOptimizer(enable_experiment_tracking=False)\n", "\n", "test_conditions = {\n", "    'volatility': 0.025,\n", "    'volume': 1800000,\n", "    'rsi': 65,\n", "    'macd': 0.2,\n", "    'price_change': 0.015,\n", "    'volatility_ma': 0.022,\n", "    'volume_ma': 1750000,\n", "    'rsi_ma': 63\n", "}\n", "\n", "weights = await optimizer.predict_weights(test_conditions, log_prediction=False)\n", "print(f\"Predicted weights: {weights}\")\n", "print(f\"Grid: {weights[0]:.1%}, TA: {weights[1]:.1%}, Trend: {weights[2]:.1%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different scenarios quickly\n", "scenarios = [\n", "    {\"name\": \"<PERSON>\", \"volatility\": 0.02, \"rsi\": 70, \"volume\": 2000000},\n", "    {\"name\": \"Bear\", \"volatility\": 0.04, \"rsi\": 30, \"volume\": 1500000},\n", "    {\"name\": \"Sideways\", \"volatility\": 0.015, \"rsi\": 50, \"volume\": 1200000}\n", "]\n", "\n", "for scenario in scenarios:\n", "    conditions = {\n", "        'volatility': scenario['volatility'],\n", "        'volume': scenario['volume'],\n", "        'rsi': scenario['rsi'],\n", "        'macd': 0,\n", "        'price_change': 0.001,\n", "        'volatility_ma': scenario['volatility'] * 0.9,\n", "        'volume_ma': scenario['volume'] * 0.95,\n", "        'rsi_ma': scenario['rsi'] * 0.98\n", "    }\n", "    \n", "    weights = await optimizer.predict_weights(conditions, log_prediction=False)\n", "    print(f\"{scenario['name']:8}: Grid={weights[0]:.2f}, TA={weights[1]:.2f}, Trend={weights[2]:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Performance check\n", "import time\n", "\n", "start_time = time.time()\n", "for i in range(10):\n", "    weights = await optimizer.predict_weights(test_conditions, log_prediction=False)\n", "end_time = time.time()\n", "\n", "avg_time = (end_time - start_time) / 10 * 1000\n", "print(f\"Average prediction time: {avg_time:.1f}ms\")\n", "print(f\"Predictions per second: {1000/avg_time:.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get model info\n", "model_info = optimizer.get_model_info()\n", "print(\"Model Information:\")\n", "for key, value in model_info.items():\n", "    print(f\"  {key}: {value}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}