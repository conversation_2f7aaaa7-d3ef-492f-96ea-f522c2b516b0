from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
import uvicorn
import logging

from app.config.settings import settings
from app.dashboard.api_router import router as api_router
from app.services.exchange.binance_client import BinanceExchangeClient
from app.ml.models.weight_optimizer import ML<PERSON>eightOptimizer
from app.strategies.portfolio_manager import PortfolioManager
from app.dependencies import get_execution_service

# Configure logging
logging.basicConfig(
    level=settings.log_level.upper(),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# This dictionary will hold the application's state, like service instances
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handles application startup and shutdown events.
    Initializes all necessary services and makes them available in the app_state.
    """
    logger.info("Application startup: Initializing trading engine components...")
    
    # Initialize components and store them in the application state
    exchange_client = BinanceExchangeClient(settings=settings)
    execution_service = get_execution_service()
    ml_weight_optimizer = MLWeightOptimizer(
        exchange_client=exchange_client,
        model_path=settings.ml_model_path
    )
    portfolio_manager = PortfolioManager(
        settings=settings,
        execution_service=execution_service,
        exchange_client=exchange_client,
        ml_weight_optimizer=ml_weight_optimizer
    )
    
    app_state['exchange_client'] = exchange_client
    app_state['execution_service'] = execution_service
    app_state['ml_weight_optimizer'] = ml_weight_optimizer
    app_state['portfolio_manager'] = portfolio_manager
    
    logger.info("Trading engine components initialized successfully.")
    
    yield
    
    logger.info("Application shutdown: Cleaning up resources...")
    app_state.clear()
    logger.info("Shutdown complete.")

app = FastAPI(
    title="Crypto Auto Trader",
    description="A multi-strategy cryptocurrency auto-trading application.",
    version="1.0.0",
    lifespan=lifespan
)

# Include the API router
app.include_router(api_router)

@app.get("/", tags=["root"])
async def read_root():
    """A simple root endpoint to confirm the API is running."""
    return {"message": "Welcome to the Crypto Auto Trader API"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level=settings.log_level.lower())