"""Lifecycle management methods for the execution service."""
import logging
import asyncio

logger = logging.getLogger(__name__)

class LifecycleMixin:
    """Mixin for execution service lifecycle management."""

    async def start_monitoring(self):
        """Start the websocket monitoring and keep-alive tasks."""
        if self.websocket_handler:
            await self.websocket_handler.start_monitoring()
            # Store references to tasks for backward compatibility
            self.websocket_task = self.websocket_handler.websocket_task
            self.keepalive_task = self.websocket_handler.keepalive_task
        else:
            logger.warning("Cannot start monitoring: websocket_handler is not initialized.")

    async def stop_monitoring(self):
        """Stop the websocket monitoring and keep-alive tasks."""
        if self.websocket_handler:
            await self.websocket_handler.stop_monitoring()
        self.websocket_task = None
        self.keepalive_task = None

    async def _sync_state_on_startup(self):
        """Synchronize state from database and exchange on startup.

        This is a pass-through to the websocket handler's method.
        """
        if self.websocket_handler:
            await self.websocket_handler._sync_state_on_startup()
        else:
            logger.warning("Cannot sync state: websocket_handler is not initialized.")

    async def _keep_listen_key_alive(self):
        """Keep the listen key alive by sending periodic requests.

        This is a pass-through to the websocket handler's method.
        """
        if self.websocket_handler:
            await self.websocket_handler._keep_listen_key_alive()
        else:
            logger.warning("Cannot keep listen key alive: websocket_handler is not initialized.")

    async def _handle_user_stream_message(self, msg: dict):
        """Process incoming messages from the user data stream.

        This is a pass-through to the websocket handler's method.

        Args:
            msg: The websocket message to process.
        """
        if self.websocket_handler:
            await self.websocket_handler._handle_user_stream_message(msg)
        else:
            logger.warning("Cannot handle user stream message: websocket_handler is not initialized.")
