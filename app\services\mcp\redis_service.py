"""
Redis Service for Real-time Caching in Strategy Ensemble System
Provides sub-second response times for strategy signals and weights.
"""

import redis.asyncio as redis
import json
import pickle
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import logging
import asyncio
from dataclasses import asdict

logger = logging.getLogger(__name__)


class RedisService:
    """High-performance Redis service for ensemble strategy caching."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", db: int = 0):
        """
        Initialize Redis service.
        
        Args:
            redis_url: Redis connection URL
            db: Redis database number
        """
        self.redis_url = redis_url
        self.db = db
        self.redis = None
        self.connection_pool = None
        
        # Cache key patterns
        self.WEIGHTS_KEY = "ensemble:weights"
        self.SIGNALS_KEY = "ensemble:signals"
        self.METRICS_KEY = "ensemble:metrics"
        self.CORRELATION_KEY = "ensemble:correlation"
        self.VOLATILITY_KEY = "market:volatility"
        self.POSITION_KEY = "position:calculation"
        
        # Default TTL values (in seconds)
        self.DEFAULT_TTL = {
            'weights': 300,      # 5 minutes
            'signals': 30,       # 30 seconds
            'metrics': 60,       # 1 minute
            'correlation': 1800, # 30 minutes
            'volatility': 900,   # 15 minutes
            'position': 60       # 1 minute
        }
    
    async def connect(self) -> bool:
        """
        Establish connection to Redis.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create connection pool for better performance
            self.connection_pool = redis.ConnectionPool.from_url(
                self.redis_url,
                db=self.db,
                max_connections=20,
                retry_on_timeout=True,
                decode_responses=False  # We'll handle encoding/decoding
            )
            
            self.redis = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis.ping()
            logger.info(f"Connected to Redis at {self.redis_url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            return False
    
    async def ping(self) -> bool:
        """Ping Redis to test connectivity."""
        try:
            if self.redis:
                result = await self.redis.ping()
                return result
            return False
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Close Redis connections."""
        try:
            if self.redis:
                await self.redis.close()
            if self.connection_pool:
                await self.connection_pool.disconnect()
            logger.info("Disconnected from Redis")
        except Exception as e:
            logger.error(f"Error disconnecting from Redis: {e}")
    
    # Strategy Weights Caching
    
    async def cache_strategy_weights(
        self, 
        weights: Dict[str, float], 
        confidence: float = 1.0,
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache strategy weights with metadata.
        
        Args:
            weights: Dictionary of strategy weights
            confidence: Confidence score for the weights
            ttl: Time to live in seconds (optional)
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_data = {
                'weights': weights,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat(),
                'cache_version': '1.0'
            }
            
            ttl_value = ttl or self.DEFAULT_TTL['weights']
            
            await self.redis.setex(
                self.WEIGHTS_KEY,
                ttl_value,
                json.dumps(cache_data)
            )
            
            logger.debug(f"Cached strategy weights: {weights}")
            
        except Exception as e:
            logger.error(f"Failed to cache strategy weights: {e}")
    
    async def get_cached_weights(self) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached strategy weights.
        
        Returns:
            Dictionary with weights and metadata, or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cached = await self.redis.get(self.WEIGHTS_KEY)
            if cached:
                data = json.loads(cached)
                logger.debug(f"Retrieved cached weights: {data['weights']}")
                return data
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached weights: {e}")
            return None
    
    # Strategy Signals Caching
    
    async def cache_strategy_signals(
        self,
        strategy_name: str,
        signal_data: Dict[str, Any],
        market_hash: str,
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache individual strategy signals.
        
        Args:
            strategy_name: Name of the strategy
            signal_data: Signal data dictionary
            market_hash: Hash of market conditions for cache key
            ttl: Time to live in seconds
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.SIGNALS_KEY}:{strategy_name}:{market_hash}"
            ttl_value = ttl or self.DEFAULT_TTL['signals']
            
            cache_data = {
                'signal': signal_data,
                'timestamp': datetime.now().isoformat(),
                'strategy': strategy_name
            }
            
            await self.redis.setex(
                cache_key,
                ttl_value,
                json.dumps(cache_data)
            )
            
            logger.debug(f"Cached signal for {strategy_name}")
            
        except Exception as e:
            logger.error(f"Failed to cache signal for {strategy_name}: {e}")
    
    async def get_cached_signal(
        self,
        strategy_name: str,
        market_hash: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached strategy signal.
        
        Args:
            strategy_name: Name of the strategy
            market_hash: Hash of market conditions
            
        Returns:
            Signal data or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.SIGNALS_KEY}:{strategy_name}:{market_hash}"
            cached = await self.redis.get(cache_key)
            
            if cached:
                data = json.loads(cached)
                logger.debug(f"Retrieved cached signal for {strategy_name}")
                return data['signal']
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached signal for {strategy_name}: {e}")
            return None
    
    async def cache_aggregated_signal(
        self,
        aggregated_signal: Dict[str, Any],
        signal_hash: str,
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache aggregated signal from multiple strategies.
        
        Args:
            aggregated_signal: Aggregated signal data
            signal_hash: Hash of input signals for cache key
            ttl: Time to live in seconds
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.SIGNALS_KEY}:aggregated:{signal_hash}"
            ttl_value = ttl or self.DEFAULT_TTL['signals']
            
            cache_data = {
                'aggregated_signal': aggregated_signal,
                'timestamp': datetime.now().isoformat()
            }
            
            await self.redis.setex(
                cache_key,
                ttl_value,
                json.dumps(cache_data, default=str)
            )
            
            logger.debug("Cached aggregated signal")
            
        except Exception as e:
            logger.error(f"Failed to cache aggregated signal: {e}")
    
    async def get_cached_aggregated_signal(self, signal_hash: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached aggregated signal.
        
        Args:
            signal_hash: Hash of input signals
            
        Returns:
            Aggregated signal data or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.SIGNALS_KEY}:aggregated:{signal_hash}"
            cached = await self.redis.get(cache_key)
            
            if cached:
                data = json.loads(cached)
                logger.debug("Retrieved cached aggregated signal")
                return data['aggregated_signal']
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached aggregated signal: {e}")
            return None
    
    # Market Volatility Caching
    
    async def cache_market_volatility(
        self,
        symbol: str,
        volatility: float,
        calculation_metadata: Optional[Dict] = None,
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache market volatility calculations.
        
        Args:
            symbol: Trading symbol
            volatility: Calculated volatility value
            calculation_metadata: Additional metadata about calculation
            ttl: Time to live in seconds
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.VOLATILITY_KEY}:{symbol}"
            ttl_value = ttl or self.DEFAULT_TTL['volatility']
            
            cache_data = {
                'volatility': volatility,
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'metadata': calculation_metadata or {}
            }
            
            await self.redis.setex(
                cache_key,
                ttl_value,
                json.dumps(cache_data)
            )
            
            logger.debug(f"Cached volatility for {symbol}: {volatility:.6f}")
            
        except Exception as e:
            logger.error(f"Failed to cache volatility for {symbol}: {e}")
    
    async def get_cached_volatility(self, symbol: str) -> Optional[float]:
        """
        Get cached volatility for symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Volatility value or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.VOLATILITY_KEY}:{symbol}"
            cached = await self.redis.get(cache_key)
            
            if cached:
                data = json.loads(cached)
                logger.debug(f"Retrieved cached volatility for {symbol}: {data['volatility']:.6f}")
                return data['volatility']
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached volatility for {symbol}: {e}")
            return None
    
    # Position Size Calculation Caching
    
    async def cache_position_calculation(
        self,
        strategy_name: str,
        calculation_data: Dict[str, Any],
        market_conditions_hash: str,
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache position size calculations.
        
        Args:
            strategy_name: Name of the strategy
            calculation_data: Position calculation results
            market_conditions_hash: Hash of market conditions
            ttl: Time to live in seconds
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.POSITION_KEY}:{strategy_name}:{market_conditions_hash}"
            ttl_value = ttl or self.DEFAULT_TTL['position']
            
            cache_data = {
                'calculation': calculation_data,
                'strategy': strategy_name,
                'timestamp': datetime.now().isoformat()
            }
            
            await self.redis.setex(
                cache_key,
                ttl_value,
                pickle.dumps(cache_data)
            )
            
            logger.debug(f"Cached position calculation for {strategy_name}")
            
        except Exception as e:
            logger.error(f"Failed to cache position calculation for {strategy_name}: {e}")
    
    async def get_cached_position_calculation(
        self,
        strategy_name: str,
        market_conditions_hash: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached position calculation.
        
        Args:
            strategy_name: Name of the strategy
            market_conditions_hash: Hash of market conditions
            
        Returns:
            Position calculation data or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_key = f"{self.POSITION_KEY}:{strategy_name}:{market_conditions_hash}"
            cached = await self.redis.get(cache_key)
            
            if cached:
                data = pickle.loads(cached)
                logger.debug(f"Retrieved cached position calculation for {strategy_name}")
                return data['calculation']
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached position calculation for {strategy_name}: {e}")
            return None
    
    # Correlation Matrix Caching
    
    async def cache_correlation_matrix(
        self,
        matrix: Dict[str, Dict[str, float]],
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache strategy correlation matrix.
        
        Args:
            matrix: Correlation matrix dictionary
            ttl: Time to live in seconds
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_data = {
                'matrix': matrix,
                'timestamp': datetime.now().isoformat(),
                'strategies': list(matrix.keys())
            }
            
            ttl_value = ttl or self.DEFAULT_TTL['correlation']
            
            await self.redis.setex(
                self.CORRELATION_KEY,
                ttl_value,
                json.dumps(cache_data)
            )
            
            logger.debug("Cached correlation matrix")
            
        except Exception as e:
            logger.error(f"Failed to cache correlation matrix: {e}")
    
    async def get_cached_correlation_matrix(self) -> Optional[Dict[str, Dict[str, float]]]:
        """
        Get cached correlation matrix.
        
        Returns:
            Correlation matrix or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cached = await self.redis.get(self.CORRELATION_KEY)
            if cached:
                data = json.loads(cached)
                logger.debug("Retrieved cached correlation matrix")
                return data['matrix']
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached correlation matrix: {e}")
            return None
    
    # Portfolio Metrics Caching
    
    async def cache_portfolio_metrics(
        self,
        metrics: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> None:
        """
        Cache portfolio performance metrics.
        
        Args:
            metrics: Portfolio metrics dictionary
            ttl: Time to live in seconds
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_data = {
                'metrics': metrics,
                'timestamp': datetime.now().isoformat()
            }
            
            ttl_value = ttl or self.DEFAULT_TTL['metrics']
            
            await self.redis.setex(
                self.METRICS_KEY,
                ttl_value,
                json.dumps(cache_data, default=str)
            )
            
            logger.debug("Cached portfolio metrics")
            
        except Exception as e:
            logger.error(f"Failed to cache portfolio metrics: {e}")
    
    async def get_cached_portfolio_metrics(self) -> Optional[Dict[str, Any]]:
        """
        Get cached portfolio metrics.
        
        Returns:
            Portfolio metrics or None if not found
        """
        try:
            if not self.redis:
                await self.connect()
            
            cached = await self.redis.get(self.METRICS_KEY)
            if cached:
                data = json.loads(cached)
                logger.debug("Retrieved cached portfolio metrics")
                return data['metrics']
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached portfolio metrics: {e}")
            return None
    
    # Batch Operations
    
    async def pipeline_cache_update(self, updates: List[Dict[str, Any]]) -> bool:
        """
        Batch update multiple cache entries using Redis pipeline.
        
        Args:
            updates: List of update dictionaries with 'key', 'value', 'ttl'
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.redis:
                await self.connect()
            
            pipe = self.redis.pipeline()
            
            for update in updates:
                pipe.setex(
                    update['key'],
                    update['ttl'],
                    update['value']
                )
            
            await pipe.execute()
            logger.debug(f"Batch updated {len(updates)} cache entries")
            return True
            
        except Exception as e:
            logger.error(f"Failed to execute batch cache update: {e}")
            return False
    
    async def batch_get_signals(self, strategy_names: List[str], market_hash: str) -> Dict[str, Any]:
        """
        Batch retrieve strategy signals using Redis pipeline.
        
        Args:
            strategy_names: List of strategy names
            market_hash: Hash of market conditions
            
        Returns:
            Dictionary of strategy signals
        """
        try:
            if not self.redis:
                await self.connect()
            
            cache_keys = [f"{self.SIGNALS_KEY}:{name}:{market_hash}" for name in strategy_names]
            
            pipe = self.redis.pipeline()
            for key in cache_keys:
                pipe.get(key)
            
            results = await pipe.execute()
            
            signals = {}
            for i, (strategy_name, result) in enumerate(zip(strategy_names, results)):
                if result:
                    data = json.loads(result)
                    signals[strategy_name] = data['signal']
            
            logger.debug(f"Batch retrieved {len(signals)} cached signals")
            return signals
            
        except Exception as e:
            logger.error(f"Failed to batch get signals: {e}")
            return {}
    
    # Utility Methods
    
    async def clear_cache(self, pattern: str = None) -> bool:
        """
        Clear cache entries matching pattern.
        
        Args:
            pattern: Pattern to match (e.g., 'ensemble:*')
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.redis:
                await self.connect()
            
            if pattern:
                keys = await self.redis.keys(pattern)
                if keys:
                    await self.redis.delete(*keys)
                    logger.info(f"Cleared {len(keys)} cache entries matching pattern: {pattern}")
            else:
                await self.redis.flushdb()
                logger.info("Cleared entire cache database")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get Redis cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            if not self.redis:
                await self.connect()
            
            info = await self.redis.info()
            
            stats = {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'instantaneous_ops_per_sec': info.get('instantaneous_ops_per_sec', 0)
            }
            
            # Calculate hit rate
            hits = stats['keyspace_hits']
            misses = stats['keyspace_misses']
            total = hits + misses
            hit_rate = (hits / total * 100) if total > 0 else 0
            stats['cache_hit_rate'] = round(hit_rate, 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {}
    
    def generate_hash(self, data: Any) -> str:
        """
        Generate hash for cache keys.
        
        Args:
            data: Data to hash
            
        Returns:
            Hash string
        """
        import hashlib
        
        if isinstance(data, dict):
            # Sort dictionary for consistent hashing
            sorted_items = sorted(data.items())
            data_str = json.dumps(sorted_items)
        else:
            data_str = str(data)
        
        return hashlib.md5(data_str.encode()).hexdigest()[:16]