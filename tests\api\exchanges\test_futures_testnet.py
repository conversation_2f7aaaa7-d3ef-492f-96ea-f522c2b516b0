#!/usr/bin/env python3
"""
Test Binance Futures Testnet Connection
Using the correct Futures API client for testnet access.
"""

import os
import asyncio
from dotenv import load_dotenv
from binance.client import Client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_futures_testnet():
    """Test Binance Futures testnet API connection"""
    print("=" * 60)
    print("TESTING BINANCE FUTURES TESTNET CONNECTION")
    print("=" * 60)
    
    try:
        # Load environment variables
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        if not api_key or not api_secret:
            print("❌ API credentials not found in environment")
            return False
        
        print(f"🔑 API Key: {api_key[:8]}...")
        print(f"🔧 Using Futures Testnet: True")
        
        # Initialize Futures client for testnet
        client = Client(api_key, api_secret, testnet=True)
        
        # Test 1: Futures server time
        print("\n🕐 Testing server connectivity...")
        server_time = client.get_server_time()
        print(f"✅ Server time: {server_time['serverTime']}")
        
        # Test 2: Futures account information  
        print("\n💰 Getting Futures account info...")
        futures_account = client.futures_account()
        
        print("✅ Futures Account Access: SUCCESS")
        print(f"   Can Trade: {futures_account.get('canTrade', False)}")
        print(f"   Can Deposit: {futures_account.get('canDeposit', False)}")
        print(f"   Can Withdraw: {futures_account.get('canWithdraw', False)}")
        print(f"   Total Wallet Balance: {futures_account.get('totalWalletBalance', '0')} USDT")
        print(f"   Total Unrealized PnL: {futures_account.get('totalUnrealizedProfit', '0')} USDT")
        print(f"   Total Margin Balance: {futures_account.get('totalMarginBalance', '0')} USDT")
        print(f"   Available Balance: {futures_account.get('availableBalance', '0')} USDT")
        
        # Test 3: Asset balances
        print("\n💼 Account Asset Balances:")
        assets = futures_account.get('assets', [])
        active_assets = [a for a in assets if float(a['walletBalance']) > 0]
        
        if active_assets:
            for asset in active_assets:
                wallet_balance = float(asset['walletBalance'])
                unrealized_profit = float(asset['unrealizedProfit'])
                available_balance = float(asset['availableBalance'])
                print(f"   {asset['asset']}: {wallet_balance:.8f} (unrealized PnL: {unrealized_profit:.8f}, available: {available_balance:.8f})")
        else:
            print("   No active asset balances found")
        
        # Test 4: Current positions
        print("\n📊 Current Futures Positions:")
        positions = client.futures_position_information()
        active_positions = [p for p in positions if float(p['positionAmt']) != 0]
        
        if active_positions:
            for pos in active_positions:
                print(f"   {pos['symbol']}: {pos['positionAmt']} (side: {pos['positionSide']}, unrealized PnL: {pos['unrealizedProfit']})")
        else:
            print("   No active positions")
        
        # Test 5: Get symbol info for BTCUSDT
        print("\n📈 Market Data Test:")
        symbol_ticker = client.futures_symbol_ticker(symbol='BTCUSDT')
        btc_price = float(symbol_ticker['price'])
        print(f"   BTC/USDT Futures Price: ${btc_price:,.2f}")
        
        # Test 6: Get trading rules
        exchange_info = client.futures_exchange_info()
        btc_symbol = next((s for s in exchange_info['symbols'] if s['symbol'] == 'BTCUSDT'), None)
        if btc_symbol:
            print(f"   Trading Status: {btc_symbol['status']}")
            print(f"   Base Asset: {btc_symbol['baseAsset']}")
            print(f"   Quote Asset: {btc_symbol['quoteAsset']}")
            
            # Get lot size and price filters
            for filter_info in btc_symbol['filters']:
                if filter_info['filterType'] == 'LOT_SIZE':
                    print(f"   Min Quantity: {filter_info['minQty']}")
                    print(f"   Max Quantity: {filter_info['maxQty']}")
                    print(f"   Step Size: {filter_info['stepSize']}")
                elif filter_info['filterType'] == 'PRICE_FILTER':
                    print(f"   Min Price: {filter_info['minPrice']}")
                    print(f"   Max Price: {filter_info['maxPrice']}")
                    print(f"   Tick Size: {filter_info['tickSize']}")
                elif filter_info['filterType'] == 'MIN_NOTIONAL':
                    print(f"   Min Notional: {filter_info['notional']}")
        
        print("\n" + "=" * 60)
        print("✅ FUTURES TESTNET CONNECTION FULLY VALIDATED")
        print("Ready for futures trading operations!")
        print("=" * 60)
        
        return True, futures_account
        
    except Exception as e:
        print(f"\n❌ Futures testnet connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """Main test function"""
    success, account_info = asyncio.run(test_futures_testnet())
    
    if success:
        print(f"\n🎯 Account ready for trading with {account_info.get('availableBalance', '0')} USDT available")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)