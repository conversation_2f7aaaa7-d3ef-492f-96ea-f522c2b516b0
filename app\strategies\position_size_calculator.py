#!/usr/bin/env python3
"""
Enhanced Position Size Calculator for Dynamic Position Optimization
Implements Task 2.1.2: Cross-validated Kelly sizing with volatility integration.

Features:
- Integration with MultiSourceKellyCriterion for cross-validated Kelly fractions
- VolatilityCalculator integration for risk-adjusted position sizing
- Redis caching for sub-second position calculations
- Safety limits and validation mechanisms
- Real-time position size optimization
- Multiple position sizing models (Kelly, volatility-scaled, risk-parity)
"""

import asyncio
import json
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging
from enum import Enum

from app.services.volatility_calculator import VolatilityCalculator, VolatilityResult
from app.services.mcp.multi_source_kelly_criterion import (
    MultiSourceKellyCriterion,
    MultiSourceKellyResult
)
from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.mcp.cross_exchange_validator import MarketData

logger = logging.getLogger(__name__)

class PositionSizingModel(Enum):
    """Position sizing model types"""
    KELLY = "kelly"
    VOLATILITY_SCALED = "volatility_scaled"
    RISK_PARITY = "risk_parity"
    HYBRID = "hybrid"
    CONSERVATIVE = "conservative"

@dataclass
class PositionSizeConfig:
    """Configuration for position size calculator"""
    # Model selection
    primary_model: PositionSizingModel = PositionSizingModel.HYBRID
    fallback_model: PositionSizingModel = PositionSizingModel.CONSERVATIVE
    
    # Safety limits
    max_position_size: float = 0.25        # Maximum 25% of portfolio
    min_position_size: float = 0.001       # Minimum 0.1% of portfolio
    max_kelly_fraction: float = 0.15       # Maximum Kelly fraction (safety)
    kelly_safety_multiplier: float = 0.5   # Conservative Kelly adjustment
    
    # Risk parameters
    max_portfolio_volatility: float = 0.15  # Maximum portfolio volatility
    target_sharpe_ratio: float = 1.5        # Target Sharpe ratio
    risk_free_rate: float = 0.02            # Risk-free rate for calculations
    
    # Volatility adjustments
    vol_target: float = 0.15               # Target volatility for vol-scaled positions
    vol_lookback_days: int = 30            # Volatility lookback period
    vol_adjustment_strength: float = 1.0    # Volatility adjustment factor
    
    # Correlation constraints
    max_correlation_exposure: float = 0.6   # Maximum correlated exposure
    correlation_decay_factor: float = 0.9   # Correlation adjustment factor
    
    # Cache settings
    cache_ttl_positions: int = 60          # 1 minute cache for positions
    cache_ttl_calculations: int = 300      # 5 minutes for calculations
    
    # Performance targets
    max_calculation_time_ms: float = 100   # Sub-100ms target
    
    # Validation thresholds
    min_confidence_threshold: float = 0.6  # Minimum confidence for sizing
    min_data_quality_score: float = 0.7    # Minimum data quality

@dataclass
class PositionSizeResult:
    """Result of position size calculation"""
    symbol: str
    strategy_name: str
    recommended_position_size: float
    position_size_percentage: float
    sizing_model_used: str
    kelly_fraction: float
    volatility_adjusted_size: float
    risk_adjusted_size: float
    confidence: float
    safety_checks_passed: bool
    
    # Component calculations
    base_kelly_size: float
    volatility_scaling: float
    correlation_adjustment: float
    risk_limit_adjustment: float
    
    # Metadata
    calculation_timestamp: datetime
    data_quality_score: float
    volatility_regime: str
    market_conditions: Dict[str, Any]
    
    # Performance tracking
    cache_hit: bool = False
    calculation_time_ms: float = 0.0
    
    # Safety and validation
    safety_warnings: List[str] = None
    validation_errors: List[str] = None

class PositionSizeCalculator:
    """
    Enhanced position size calculator with Kelly criterion and volatility integration.
    
    Features:
    - Cross-validated Kelly sizing with multiple data sources
    - Real-time volatility-adjusted position sizing
    - Comprehensive safety limits and validation
    - Redis caching for sub-second performance
    - Multiple position sizing models with fallback
    """
    
    def __init__(
        self,
        volatility_calculator: VolatilityCalculator,
        kelly_criterion: MultiSourceKellyCriterion,
        redis_service: RedisService,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[PositionSizeConfig] = None
    ):
        self.volatility_calculator = volatility_calculator
        self.kelly_criterion = kelly_criterion
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.config = config or PositionSizeConfig()
        
        # Cache keys
        self.POSITION_SIZE_KEY = "position_size:calculation"
        self.PORTFOLIO_RISK_KEY = "position_size:portfolio_risk"
        self.CORRELATION_KEY = "position_size:correlation"
        
        # Performance tracking
        self.calculation_times = []
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Portfolio state tracking
        self.current_positions: Dict[str, float] = {}
        self.portfolio_risk_metrics: Dict[str, Any] = {}
        
        logger.info("PositionSizeCalculator initialized with Kelly and volatility integration")
    
    async def calculate_position_size(
        self,
        symbol: str,
        strategy_name: str,
        market_data: MarketData,
        portfolio_value: float,
        current_price: Optional[float] = None
    ) -> PositionSizeResult:
        """
        Calculate optimal position size using integrated Kelly and volatility models.
        Target: <100ms execution time.
        """
        start_time = time.perf_counter()
        
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(symbol, strategy_name, market_data)
            
            # Try cache first
            cached_result = await self._get_cached_position_size(cache_key)
            if cached_result:
                self.cache_hits += 1
                calculation_time = (time.perf_counter() - start_time) * 1000
                
                cached_result.cache_hit = True
                cached_result.calculation_time_ms = calculation_time
                
                logger.debug(f"Position size cache hit for {symbol}/{strategy_name}: {calculation_time:.2f}ms")
                return cached_result
            
            # Cache miss - perform calculation
            self.cache_misses += 1
            
            # Parallel data gathering for performance
            kelly_task = self.kelly_criterion.calculate_multi_source_kelly(
                symbol, current_price or market_data.price
            )
            volatility_task = self.volatility_calculator.calculate_volatility(
                symbol, market_data, 60  # 1-hour timeframe
            )
            portfolio_risk_task = self._get_portfolio_risk_metrics(symbol)
            
            # Wait for all calculations
            kelly_result, volatility_result, portfolio_risk = await asyncio.gather(
                kelly_task, volatility_task, portfolio_risk_task,
                return_exceptions=True
            )
            
            # Handle calculation errors
            if isinstance(kelly_result, Exception):
                logger.warning(f"Kelly calculation failed: {kelly_result}")
                kelly_result = self._create_fallback_kelly_result(symbol)
            
            if isinstance(volatility_result, Exception):
                logger.warning(f"Volatility calculation failed: {volatility_result}")
                volatility_result = self._create_fallback_volatility_result(symbol)
            
            if isinstance(portfolio_risk, Exception):
                logger.warning(f"Portfolio risk calculation failed: {portfolio_risk}")
                portfolio_risk = {}
            
            # Calculate position size using selected model
            position_result = await self._calculate_position_with_model(
                symbol,
                strategy_name,
                kelly_result,
                volatility_result,
                portfolio_risk,
                portfolio_value,
                market_data
            )
            
            # Apply safety checks and validation
            validated_result = await self._apply_safety_checks(position_result, portfolio_risk)
            
            # Cache the result
            await self._cache_position_size_result(cache_key, validated_result)
            
            # Update performance metrics
            calculation_time = (time.perf_counter() - start_time) * 1000
            validated_result.calculation_time_ms = calculation_time
            self.calculation_times.append(calculation_time)
            
            # Store analytics
            if self.supabase_service:
                asyncio.create_task(self._store_position_size_analytics(validated_result))
            
            # Performance validation
            if calculation_time > self.config.max_calculation_time_ms:
                logger.warning(f"Position size calculation exceeded target: {calculation_time:.2f}ms")
            else:
                logger.debug(f"Position size calculated for {symbol}/{strategy_name}: {calculation_time:.2f}ms")
            
            return validated_result
            
        except Exception as e:
            calculation_time = (time.perf_counter() - start_time) * 1000
            logger.error(f"Position size calculation failed for {symbol}/{strategy_name}: {e}")
            return await self._create_fallback_position_result(
                symbol, strategy_name, start_time
            )
    
    async def _calculate_position_with_model(
        self,
        symbol: str,
        strategy_name: str,
        kelly_result: MultiSourceKellyResult,
        volatility_result: VolatilityResult,
        portfolio_risk: Dict[str, Any],
        portfolio_value: float,
        market_data: MarketData
    ) -> PositionSizeResult:
        """Calculate position size using the configured model"""
        
        # Base Kelly position size
        base_kelly_fraction = kelly_result.risk_adjusted_fraction * self.config.kelly_safety_multiplier
        base_kelly_size = min(base_kelly_fraction, self.config.max_kelly_fraction)
        
        # Volatility scaling
        volatility_scaling = self._calculate_volatility_scaling(volatility_result)
        volatility_adjusted_size = base_kelly_size * volatility_scaling
        
        # Correlation adjustment
        correlation_adjustment = await self._calculate_correlation_adjustment(
            symbol, strategy_name, portfolio_risk
        )
        
        # Risk limit adjustment
        risk_adjustment = self._calculate_risk_limit_adjustment(
            volatility_result, portfolio_risk
        )
        
        # Apply model-specific logic
        if self.config.primary_model == PositionSizingModel.KELLY:
            final_size = base_kelly_size * correlation_adjustment * risk_adjustment
            
        elif self.config.primary_model == PositionSizingModel.VOLATILITY_SCALED:
            final_size = volatility_adjusted_size * correlation_adjustment * risk_adjustment
            
        elif self.config.primary_model == PositionSizingModel.RISK_PARITY:
            final_size = self._calculate_risk_parity_size(
                volatility_result, portfolio_risk
            ) * correlation_adjustment
            
        elif self.config.primary_model == PositionSizingModel.HYBRID:
            # Weighted combination of Kelly and volatility-scaled
            kelly_weight = kelly_result.cross_validation_score
            vol_weight = volatility_result.confidence
            total_weight = kelly_weight + vol_weight
            
            if total_weight > 0:
                hybrid_size = (
                    (base_kelly_size * kelly_weight + 
                     volatility_adjusted_size * vol_weight) / total_weight
                )
            else:
                hybrid_size = (base_kelly_size + volatility_adjusted_size) / 2
                
            final_size = hybrid_size * correlation_adjustment * risk_adjustment
            
        else:  # Conservative
            final_size = min(
                base_kelly_size * 0.5,
                volatility_adjusted_size * 0.5
            ) * correlation_adjustment * risk_adjustment
        
        # Apply absolute limits
        final_size = max(self.config.min_position_size, 
                        min(self.config.max_position_size, final_size))
        
        # Calculate position size in currency terms
        position_value = final_size * portfolio_value
        position_percentage = final_size * 100
        
        return PositionSizeResult(
            symbol=symbol,
            strategy_name=strategy_name,
            recommended_position_size=final_size,
            position_size_percentage=position_percentage,
            sizing_model_used=self.config.primary_model.value,
            kelly_fraction=kelly_result.risk_adjusted_fraction,
            volatility_adjusted_size=volatility_adjusted_size,
            risk_adjusted_size=final_size,
            confidence=min(kelly_result.cross_validation_score, volatility_result.confidence),
            safety_checks_passed=True,  # Will be validated later
            
            # Component calculations
            base_kelly_size=base_kelly_size,
            volatility_scaling=volatility_scaling,
            correlation_adjustment=correlation_adjustment,
            risk_limit_adjustment=risk_adjustment,
            
            # Metadata
            calculation_timestamp=datetime.now(),
            data_quality_score=min(
                kelly_result.metadata.get('market_validation', {}).get('data_quality_score', 1.0),
                volatility_result.confidence
            ),
            volatility_regime=volatility_result.volatility_regime,
            market_conditions={
                'volatility': volatility_result.current_volatility,
                'volatility_percentile': volatility_result.volatility_percentile,
                'kelly_confidence': kelly_result.cross_validation_score,
                'price': market_data.price,
                'volume': market_data.volume
            },
            
            safety_warnings=[],
            validation_errors=[]
        )
    
    def _calculate_volatility_scaling(self, volatility_result: VolatilityResult) -> float:
        """Calculate volatility-based scaling factor"""
        
        current_vol = volatility_result.current_volatility
        target_vol = self.config.vol_target
        
        if current_vol <= 0:
            return 1.0
        
        # Inverse volatility scaling with adjustment strength
        vol_ratio = target_vol / current_vol
        scaling_factor = vol_ratio ** self.config.vol_adjustment_strength
        
        # Apply regime-based adjustments
        if volatility_result.volatility_regime == "extreme":
            scaling_factor *= 0.5  # Halve position in extreme volatility
        elif volatility_result.volatility_regime == "high":
            scaling_factor *= 0.75  # Reduce position in high volatility
        elif volatility_result.volatility_regime == "low":
            scaling_factor *= 1.25  # Increase position in low volatility
        
        # Ensure reasonable bounds
        return max(0.1, min(2.0, scaling_factor))
    
    async def _calculate_correlation_adjustment(
        self,
        symbol: str,
        strategy_name: str,
        portfolio_risk: Dict[str, Any]
    ) -> float:
        """Calculate position adjustment based on correlation with existing positions"""
        
        try:
            # Get strategy correlations from portfolio risk metrics
            correlations = portfolio_risk.get('strategy_correlations', {})
            
            if not correlations or strategy_name not in correlations:
                return 1.0  # No correlation adjustment if no data
            
            strategy_correlations = correlations[strategy_name]
            current_exposures = portfolio_risk.get('current_exposures', {})
            
            # Calculate weighted correlation exposure
            total_correlation_exposure = 0
            total_weight = 0
            
            for other_strategy, correlation in strategy_correlations.items():
                if other_strategy != strategy_name:
                    exposure = current_exposures.get(other_strategy, 0)
                    if exposure > 0:
                        correlation_exposure = abs(correlation) * exposure
                        total_correlation_exposure += correlation_exposure
                        total_weight += exposure
            
            if total_weight > 0:
                avg_correlation_exposure = total_correlation_exposure / total_weight
                
                # Reduce position size if correlation exposure is high
                if avg_correlation_exposure > self.config.max_correlation_exposure:
                    reduction_factor = (
                        1 - (avg_correlation_exposure - self.config.max_correlation_exposure) *
                        self.config.correlation_decay_factor
                    )
                    return max(0.1, reduction_factor)
            
            return 1.0
            
        except Exception as e:
            logger.warning(f"Correlation adjustment calculation failed: {e}")
            return 1.0
    
    def _calculate_risk_limit_adjustment(
        self,
        volatility_result: VolatilityResult,
        portfolio_risk: Dict[str, Any]
    ) -> float:
        """Calculate risk-based position adjustment"""
        
        try:
            current_portfolio_vol = portfolio_risk.get('portfolio_volatility', 0.1)
            max_vol = self.config.max_portfolio_volatility
            
            # If portfolio volatility is approaching limits, reduce new positions
            if current_portfolio_vol > max_vol * 0.8:  # 80% of maximum
                vol_reduction = 1 - ((current_portfolio_vol - max_vol * 0.8) / (max_vol * 0.2))
                return max(0.1, vol_reduction)
            
            return 1.0
            
        except Exception as e:
            logger.warning(f"Risk limit adjustment calculation failed: {e}")
            return 1.0
    
    def _calculate_risk_parity_size(
        self,
        volatility_result: VolatilityResult,
        portfolio_risk: Dict[str, Any]
    ) -> float:
        """Calculate risk parity position size"""
        
        try:
            target_vol_contribution = self.config.vol_target / 10  # 10% of target vol per position
            asset_volatility = volatility_result.current_volatility
            
            if asset_volatility <= 0:
                return self.config.min_position_size
            
            # Risk parity size = target risk contribution / asset volatility
            risk_parity_size = target_vol_contribution / asset_volatility
            
            return max(self.config.min_position_size, 
                      min(self.config.max_position_size, risk_parity_size))
            
        except Exception as e:
            logger.warning(f"Risk parity calculation failed: {e}")
            return self.config.min_position_size
    
    async def _apply_safety_checks(
        self,
        result: PositionSizeResult,
        portfolio_risk: Dict[str, Any]
    ) -> PositionSizeResult:
        """Apply comprehensive safety checks and validation"""
        
        warnings = []
        errors = []
        safety_passed = True
        
        # Check minimum confidence threshold
        if result.confidence < self.config.min_confidence_threshold:
            warnings.append(f"Low confidence: {result.confidence:.3f} < {self.config.min_confidence_threshold}")
            # Reduce position size for low confidence
            result.recommended_position_size *= 0.5
        
        # Check data quality
        if result.data_quality_score < self.config.min_data_quality_score:
            warnings.append(f"Low data quality: {result.data_quality_score:.3f}")
            result.recommended_position_size *= 0.7
        
        # Check extreme volatility
        if result.volatility_regime == "extreme":
            warnings.append("Extreme volatility regime detected")
            result.recommended_position_size = min(result.recommended_position_size, 
                                                 self.config.max_position_size * 0.5)
        
        # Check portfolio concentration
        current_total_exposure = sum(portfolio_risk.get('current_exposures', {}).values())
        if current_total_exposure + result.recommended_position_size > 0.95:  # 95% max exposure
            warnings.append(f"High portfolio concentration: {current_total_exposure + result.recommended_position_size:.1%}")
            available_capacity = max(0.05, 0.95 - current_total_exposure)
            result.recommended_position_size = min(result.recommended_position_size, available_capacity)
        
        # Final bounds check
        if result.recommended_position_size < self.config.min_position_size:
            errors.append(f"Position size below minimum: {result.recommended_position_size:.4f}")
            result.recommended_position_size = self.config.min_position_size
            safety_passed = False
        
        if result.recommended_position_size > self.config.max_position_size:
            warnings.append(f"Position size capped at maximum: {self.config.max_position_size}")
            result.recommended_position_size = self.config.max_position_size
        
        # Update result with safety check results
        result.safety_checks_passed = safety_passed and len(errors) == 0
        result.safety_warnings = warnings
        result.validation_errors = errors
        result.position_size_percentage = result.recommended_position_size * 100
        
        return result
    
    async def _get_portfolio_risk_metrics(self, symbol: str) -> Dict[str, Any]:
        """Get current portfolio risk metrics"""
        
        cache_key = f"{self.PORTFOLIO_RISK_KEY}:current"
        cached_metrics = await self.redis_service.get(cache_key)
        
        if cached_metrics:
            return json.loads(cached_metrics)
        
        # Default portfolio risk metrics (would be calculated from current positions)
        default_metrics = {
            'portfolio_volatility': 0.12,
            'current_exposures': {},
            'strategy_correlations': {},
            'max_drawdown': 0.05,
            'sharpe_ratio': 1.2,
            'total_exposure': 0.0
        }
        
        # Cache default metrics
        await self.redis_service.setex(
            cache_key,
            300,  # 5 minutes
            json.dumps(default_metrics)
        )
        
        return default_metrics
    
    def _create_fallback_kelly_result(self, symbol: str) -> MultiSourceKellyResult:
        """Create fallback Kelly result when calculation fails"""
        from app.services.mcp.multi_source_kelly_criterion import MultiSourceKellyResult
        
        return MultiSourceKellyResult(
            symbol=symbol,
            optimal_fraction=0.05,  # Conservative 5%
            confidence_interval=(0.02, 0.08),
            data_sources_used=[],
            reliability_weighted_fraction=0.05,
            conservative_fraction=0.025,
            risk_adjusted_fraction=0.03,
            cross_validation_score=0.5,
            individual_kelly_fractions={},
            recommendation="CONSERVATIVE - Using fallback Kelly fraction",
            calculation_timestamp=datetime.now(),
            metadata={'error': 'Kelly calculation failed, using fallback'}
        )
    
    def _create_fallback_volatility_result(self, symbol: str) -> VolatilityResult:
        """Create fallback volatility result when calculation fails"""
        from app.services.volatility_calculator import VolatilityResult
        
        return VolatilityResult(
            symbol=symbol,
            current_volatility=0.15,  # Default 15% volatility
            volatility_percentile=50.0,
            volatility_regime="normal",
            ewma_volatility=0.15,
            rolling_volatility=0.15,
            intraday_volatility=0.15,
            scaling_factor=1.0,
            confidence=0.5,
            calculation_timestamp=datetime.now(),
            timeframe_minutes=60,
            data_points_used=0,
            cache_hit=False,
            calculation_time_ms=0.0
        )
    
    async def _create_fallback_position_result(
        self,
        symbol: str,
        strategy_name: str,
        start_time: float
    ) -> PositionSizeResult:
        """Create fallback position result when calculation fails"""
        
        calculation_time = (time.perf_counter() - start_time) * 1000
        
        return PositionSizeResult(
            symbol=symbol,
            strategy_name=strategy_name,
            recommended_position_size=self.config.min_position_size,
            position_size_percentage=self.config.min_position_size * 100,
            sizing_model_used="fallback",
            kelly_fraction=0.0,
            volatility_adjusted_size=self.config.min_position_size,
            risk_adjusted_size=self.config.min_position_size,
            confidence=0.1,
            safety_checks_passed=False,
            
            base_kelly_size=0.0,
            volatility_scaling=1.0,
            correlation_adjustment=1.0,
            risk_limit_adjustment=1.0,
            
            calculation_timestamp=datetime.now(),
            data_quality_score=0.0,
            volatility_regime="unknown",
            market_conditions={},
            
            cache_hit=False,
            calculation_time_ms=calculation_time,
            safety_warnings=["Fallback position size used due to calculation failure"],
            validation_errors=["Primary calculation failed"]
        )
    
    def _generate_cache_key(
        self,
        symbol: str,
        strategy_name: str,
        market_data: MarketData
    ) -> str:
        """Generate cache key for position size calculation"""
        # Round timestamp to cache interval for efficiency
        time_bucket = int(market_data.timestamp.timestamp() // self.config.cache_ttl_positions)
        price_bucket = int(market_data.price / market_data.price * 100)  # 1% price buckets
        
        return f"{self.POSITION_SIZE_KEY}:{symbol}:{strategy_name}:{time_bucket}:{price_bucket}"
    
    async def _get_cached_position_size(self, cache_key: str) -> Optional[PositionSizeResult]:
        """Get cached position size result"""
        try:
            cached_data = await self.redis_service.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                # Reconstruct the result object
                return PositionSizeResult(
                    safety_warnings=data.get('safety_warnings', []),
                    validation_errors=data.get('validation_errors', []),
                    **{k: v for k, v in data.items() if k not in ['safety_warnings', 'validation_errors']}
                )
            return None
        except Exception as e:
            logger.warning(f"Failed to get cached position size: {e}")
            return None
    
    async def _cache_position_size_result(
        self,
        cache_key: str,
        result: PositionSizeResult
    ) -> None:
        """Cache position size calculation result"""
        try:
            await self.redis_service.setex(
                cache_key,
                self.config.cache_ttl_positions,
                json.dumps(asdict(result), default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to cache position size result: {e}")
    
    async def _store_position_size_analytics(self, result: PositionSizeResult) -> None:
        """Store position size analytics in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            analytics_data = {
                'timestamp': result.calculation_timestamp.isoformat(),
                'symbol': result.symbol,
                'strategy_name': result.strategy_name,
                'position_size': result.recommended_position_size,
                'position_size_percentage': result.position_size_percentage,
                'sizing_model': result.sizing_model_used,
                'kelly_fraction': result.kelly_fraction,
                'volatility_scaling': result.volatility_scaling,
                'confidence': result.confidence,
                'data_quality_score': result.data_quality_score,
                'volatility_regime': result.volatility_regime,
                'safety_checks_passed': result.safety_checks_passed,
                'calculation_time_ms': result.calculation_time_ms,
                'cache_hit': result.cache_hit,
                'warnings_count': len(result.safety_warnings or []),
                'errors_count': len(result.validation_errors or [])
            }
            
            # Store in position_size_analytics table (would need to be created)
            # await self.supabase_service.store_position_size_analytics(analytics_data)
            
        except Exception as e:
            logger.error(f"Failed to store position size analytics: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring"""
        
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        avg_time = np.mean(self.calculation_times) if self.calculation_times else 0
        p95_time = np.percentile(self.calculation_times, 95) if self.calculation_times else 0
        
        return {
            'total_calculations': total_requests,
            'cache_hit_rate': round(hit_rate, 2),
            'avg_calculation_time_ms': round(avg_time, 2),
            'p95_calculation_time_ms': round(p95_time, 2),
            'target_met_percentage': round(
                sum(1 for t in self.calculation_times if t < self.config.max_calculation_time_ms) / 
                max(1, len(self.calculation_times)) * 100, 2
            ),
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'primary_model': self.config.primary_model.value,
            'tracked_symbols': len(self.current_positions)
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on position size calculator"""
        try:
            # Test components
            vol_health = await self.volatility_calculator.health_check()
            
            # Test calculation with dummy data
            from app.services.mcp.cross_exchange_validator import MarketData
            
            dummy_market_data = MarketData(
                symbol="TEST",
                price=100.0,
                volume=1000000,
                timestamp=datetime.now()
            )
            
            start_time = time.perf_counter()
            result = await self.calculate_position_size(
                "TEST", "TestStrategy", dummy_market_data, 10000.0
            )
            test_time = (time.perf_counter() - start_time) * 1000
            
            performance_stats = self.get_performance_stats()
            
            return {
                'status': 'healthy' if vol_health.get('status') == 'healthy' and test_time < 200 else 'degraded',
                'volatility_calculator_healthy': vol_health.get('status') == 'healthy',
                'test_calculation_time_ms': round(test_time, 2),
                'test_result_valid': result.recommended_position_size > 0,
                'performance_stats': performance_stats,
                'config': asdict(self.config)
            }
            
        except Exception as e:
            logger.error(f"Position size calculator health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }

# Factory function for easy initialization
async def create_position_size_calculator(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[PositionSizeConfig] = None
) -> PositionSizeCalculator:
    """Factory function to create position size calculator with all dependencies"""
    
    from app.services.volatility_calculator import create_volatility_calculator
    from app.services.mcp.multi_source_kelly_criterion import create_multi_source_kelly_criterion
    from app.services.mcp.redis_service import RedisService
    
    # Initialize services
    redis_service = RedisService(redis_url)
    await redis_service.connect()
    
    volatility_calculator = await create_volatility_calculator(
        redis_url, supabase_url, supabase_key
    )
    
    kelly_criterion = await create_multi_source_kelly_criterion(
        redis_url, supabase_url, supabase_key
    )
    
    supabase_service = None
    if supabase_url and supabase_key:
        from app.services.mcp.supabase_service import SupabaseService
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return PositionSizeCalculator(
        volatility_calculator=volatility_calculator,
        kelly_criterion=kelly_criterion,
        redis_service=redis_service,
        supabase_service=supabase_service,
        config=config
    )

# Example usage and testing
if __name__ == "__main__":
    async def test_position_size_calculator():
        """Test the position size calculator functionality"""
        
        # Initialize calculator
        calc = await create_position_size_calculator("redis://localhost:6379")
        
        # Test with sample data
        from app.services.mcp.cross_exchange_validator import MarketData
        
        market_data = MarketData(
            symbol="BTC",
            price=50000.0,
            volume=1000000,
            timestamp=datetime.now()
        )
        
        # Calculate position size
        result = await calc.calculate_position_size(
            "BTC", "TechnicalAnalysisStrategy", market_data, 100000.0
        )
        
        print(f"Position Size Result:")
        print(f"  Recommended Size: {result.recommended_position_size:.4f} ({result.position_size_percentage:.2f}%)")
        print(f"  Kelly Fraction: {result.kelly_fraction:.4f}")
        print(f"  Volatility Scaling: {result.volatility_scaling:.2f}")
        print(f"  Confidence: {result.confidence:.3f}")
        print(f"  Model Used: {result.sizing_model_used}")
        print(f"  Safety Passed: {result.safety_checks_passed}")
        print(f"  Calculation Time: {result.calculation_time_ms:.2f}ms")
        print(f"  Warnings: {len(result.safety_warnings or [])}")
        
        # Get performance stats
        stats = calc.get_performance_stats()
        print(f"\nPerformance Stats: {stats}")
        
        # Health check
        health = await calc.health_check()
        print(f"\nHealth Check: {health}")
    
    # Run test
    asyncio.run(test_position_size_calculator())