#!/usr/bin/env python3
"""
ML Pipeline Test Setup Validation
Created: June 17, 2025

Simple validation script to ensure the ML pipeline test is properly configured
and all dependencies are available before running the full test.
"""

import sys
import os
import json
import asyncio
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_file_exists(filename, description):
    """Check if a file exists and print status"""
    if os.path.exists(filename):
        size_kb = os.path.getsize(filename) / 1024
        print(f"✅ {description}: {filename} ({size_kb:.1f}KB)")
        return True
    else:
        print(f"❌ {description}: {filename} (missing)")
        return False

def check_dependencies():
    """Check required Python dependencies"""
    print("🔍 Checking Python dependencies...")
    
    required_packages = [
        ('asyncio', 'Async operations'),
        ('json', 'JSON handling'),
        ('time', 'Time utilities'),
        ('datetime', 'Date/time handling'),
        ('logging', 'Logging'),
        ('numpy', 'Numerical operations'),
        ('pandas', 'Data handling')
    ]
    
    all_available = True
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ❌ {package} - {description} (missing)")
            all_available = False
    
    return all_available

def check_optional_dependencies():
    """Check optional dependencies"""
    print("\n🔍 Checking optional dependencies...")
    
    optional_packages = [
        ('pytest', 'Testing framework'),
        ('psutil', 'System monitoring'),
        ('requests', 'HTTP requests'),
        ('fastapi', 'API framework'),
        ('websockets', 'WebSocket support')
    ]
    
    for package, description in optional_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ⚠️  {package} - {description} (optional, will use mocks)")

def validate_test_config():
    """Validate test configuration file"""
    print("\n📋 Validating test configuration...")
    
    config_file = "ml_pipeline_test_config.json"
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        required_sections = [
            'test_configuration',
            'test_parameters', 
            'performance_thresholds',
            'ml_services',
            'testing_modes'
        ]
        
        all_sections_present = True
        for section in required_sections:
            if section in config:
                print(f"  ✅ {section}")
            else:
                print(f"  ❌ {section} (missing)")
                all_sections_present = False
        
        return all_sections_present
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in configuration file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading configuration file: {e}")
        return False

async def test_basic_functionality():
    """Test basic ML pipeline functionality"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test basic imports
        from test_complete_ml_pipeline_integration import MLPipelineTestManager, TEST_CONFIG
        print("  ✅ Test module imports successfully")
        
        # Test manager instantiation
        test_manager = MLPipelineTestManager()
        print("  ✅ MLPipelineTestManager instantiated")
        
        # Test configuration loading
        print(f"  ✅ Test configuration loaded ({len(TEST_CONFIG)} parameters)")
        
        # Test mock services
        from test_complete_ml_pipeline_integration import MockRedisService, MockExchangeClient
        mock_redis = MockRedisService()
        mock_exchange = MockExchangeClient()
        
        await mock_redis.connect()
        await mock_redis.setex("test_key", 60, "test_value")
        result = await mock_redis.get("test_key")
        
        if result == "test_value":
            print("  ✅ Mock services working correctly")
        else:
            print("  ❌ Mock services not working correctly")
            
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Functionality test error: {e}")
        return False

def check_system_resources():
    """Check available system resources"""
    print("\n💻 Checking system resources...")
    
    try:
        import psutil
        
        # Memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        memory_available_gb = memory.available / (1024**3)
        
        print(f"  💾 Total Memory: {memory_gb:.1f}GB")
        print(f"  💾 Available Memory: {memory_available_gb:.1f}GB")
        
        if memory_available_gb < 1.0:
            print("  ⚠️  Low available memory (< 1GB)")
        else:
            print("  ✅ Sufficient memory available")
        
        # CPU
        cpu_count = psutil.cpu_count()
        cpu_usage = psutil.cpu_percent(interval=1)
        
        print(f"  🖥️  CPU Cores: {cpu_count}")
        print(f"  🖥️  CPU Usage: {cpu_usage:.1f}%")
        
        if cpu_usage > 80:
            print("  ⚠️  High CPU usage (> 80%)")
        else:
            print("  ✅ CPU usage acceptable")
        
        # Disk
        disk = psutil.disk_usage('/')
        disk_free_gb = disk.free / (1024**3)
        
        print(f"  💿 Disk Free: {disk_free_gb:.1f}GB")
        
        if disk_free_gb < 1.0:
            print("  ⚠️  Low disk space (< 1GB)")
        else:
            print("  ✅ Sufficient disk space")
            
    except ImportError:
        print("  ⚠️  psutil not available, skipping resource check")
    except Exception as e:
        print(f"  ❌ Error checking system resources: {e}")

def print_validation_summary(results):
    """Print validation summary"""
    print("\n" + "="*60)
    print("📊 VALIDATION SUMMARY")
    print("="*60)
    
    all_passed = all(results.values())
    status = "✅ READY" if all_passed else "⚠️  WARNINGS"
    
    print(f"Overall Status: {status}")
    print()
    
    for check, passed in results.items():
        icon = "✅" if passed else "❌"
        print(f"{icon} {check}")
    
    print("\n" + "="*60)
    
    if all_passed:
        print("🚀 All validation checks passed! Ready to run ML pipeline test.")
        print("   Run: python run_ml_pipeline_test.py")
    else:
        print("⚠️  Some validation checks failed. Test may still run with mocks.")
        print("   Review the issues above and install missing dependencies if needed.")
    
    return all_passed

async def main():
    """Main validation function"""
    print("🔍 ML PIPELINE TEST SETUP VALIDATION")
    print("="*50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    validation_results = {}
    
    # Check test files
    print("📁 Checking test files...")
    files_ok = all([
        check_file_exists("test_complete_ml_pipeline_integration.py", "Main test file"),
        check_file_exists("run_ml_pipeline_test.py", "Test runner"),
        check_file_exists("ml_pipeline_test_config.json", "Test configuration"),
        check_file_exists("ML_PIPELINE_TEST_GUIDE.md", "Test guide")
    ])
    validation_results["Test Files"] = files_ok
    
    # Check dependencies
    deps_ok = check_dependencies()
    validation_results["Required Dependencies"] = deps_ok
    
    # Check optional dependencies
    check_optional_dependencies()
    
    # Validate configuration
    config_ok = validate_test_config()
    validation_results["Test Configuration"] = config_ok
    
    # Test basic functionality
    if deps_ok:
        func_ok = await test_basic_functionality()
        validation_results["Basic Functionality"] = func_ok
    else:
        validation_results["Basic Functionality"] = False
        print("⏭️  Skipping functionality test due to missing dependencies")
    
    # Check system resources
    check_system_resources()
    validation_results["System Resources"] = True  # Always pass, just informational
    
    # Print summary
    all_passed = print_validation_summary(validation_results)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  Validation interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Validation failed: {e}")
        sys.exit(1)