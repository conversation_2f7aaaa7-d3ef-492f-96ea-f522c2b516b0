#!/bin/bash

# Docker Configuration Test Script for Dynamic Position Optimization
# Task 2.2.1: Validate Docker containerization setup

set -e

echo "🧪 Testing Docker Configuration for Dynamic Position Optimization"
echo "================================================================="

# Test 1: Validate docker-compose syntax
echo "1️⃣  Testing docker-compose.yml syntax..."
if docker-compose config > /dev/null 2>&1; then
    echo "   ✅ docker-compose.yml syntax is valid"
else
    echo "   ❌ docker-compose.yml syntax error"
    docker-compose config
    exit 1
fi

# Test 2: Validate Dockerfile syntax
echo ""
echo "2️⃣  Testing Dockerfile syntax..."
if docker build --dry-run . > /dev/null 2>&1; then
    echo "   ✅ Dockerfile syntax is valid"
else
    echo "   ⚠️  Note: --dry-run not supported in this Docker version, building base image for validation..."
    docker build --target base --tag test-dockerfile-validation . > /dev/null
    echo "   ✅ Dockerfile builds successfully"
    docker rmi test-dockerfile-validation > /dev/null 2>&1 || true
fi

# Test 3: Check required environment variables
echo ""
echo "3️⃣  Checking environment configuration..."
ENV_FILE=".env.mcp"
if [ -f "$ENV_FILE" ]; then
    echo "   ✅ Environment file exists: $ENV_FILE"
    
    # Check for critical variables
    REQUIRED_VARS=("REDIS_URL" "SUPABASE_URL" "BINANCE_API_KEY" "TELEGRAM_BOT_TOKEN")
    for var in "${REQUIRED_VARS[@]}"; do
        if grep -q "^$var=" "$ENV_FILE" 2>/dev/null; then
            echo "   ✅ $var is configured"
        else
            echo "   ⚠️  $var is missing from environment"
        fi
    done
else
    echo "   ⚠️  Environment file not found: $ENV_FILE"
    echo "   📝 Create from .env.example"
fi

# Test 4: Validate nginx configuration
echo ""
echo "4️⃣  Testing nginx configuration..."
if [ -f "nginx.conf" ]; then
    # Test nginx config syntax using Docker
    if docker run --rm -v "$(pwd)/nginx.conf:/etc/nginx/nginx.conf:ro" nginx:alpine nginx -t > /dev/null 2>&1; then
        echo "   ✅ nginx.conf syntax is valid"
    else
        echo "   ❌ nginx.conf syntax error"
        docker run --rm -v "$(pwd)/nginx.conf:/etc/nginx/nginx.conf:ro" nginx:alpine nginx -t
        exit 1
    fi
else
    echo "   ❌ nginx.conf not found"
    exit 1
fi

# Test 5: Check Redis configuration optimizations
echo ""
echo "5️⃣  Validating Redis configuration..."
REDIS_CONFIG_FOUND=false
if grep -q "maxmemory 256mb" docker-compose.yml; then
    echo "   ✅ Redis memory limit configured"
    REDIS_CONFIG_FOUND=true
fi
if grep -q "maxmemory-policy allkeys-lru" docker-compose.yml; then
    echo "   ✅ Redis eviction policy configured"
    REDIS_CONFIG_FOUND=true
fi
if [ "$REDIS_CONFIG_FOUND" = false ]; then
    echo "   ⚠️  Redis optimization settings not found"
fi

# Test 6: Validate service resource limits
echo ""
echo "6️⃣  Checking resource configurations..."
if grep -q "deploy:" docker-compose.yml; then
    echo "   ✅ Docker Compose deploy configurations found"
    if grep -q "limits:" docker-compose.yml; then
        echo "   ✅ Resource limits configured"
    fi
    if grep -q "reservations:" docker-compose.yml; then
        echo "   ✅ Resource reservations configured"
    fi
else
    echo "   ⚠️  No resource limits configured"
fi

# Test 7: Check for performance optimizations
echo ""
echo "7️⃣  Validating performance optimizations..."
PERFORMANCE_CHECKS=0

if grep -q "PYTHONOPTIMIZE=1" Dockerfile; then
    echo "   ✅ Python optimization enabled"
    ((PERFORMANCE_CHECKS++))
fi

if grep -q "keepalive" docker-compose.yml; then
    echo "   ✅ Connection keepalive configured"
    ((PERFORMANCE_CHECKS++))
fi

if grep -q "gzip" nginx.conf; then
    echo "   ✅ Nginx compression enabled"
    ((PERFORMANCE_CHECKS++))
fi

if grep -q "position-optimizer" docker-compose.yml; then
    echo "   ✅ Specialized position optimizer service configured"
    ((PERFORMANCE_CHECKS++))
fi

echo "   📊 Performance optimizations: $PERFORMANCE_CHECKS/4"

# Test 8: Health check configurations
echo ""
echo "8️⃣  Validating health checks..."
HEALTHCHECK_COUNT=$(grep -c "healthcheck:" docker-compose.yml || echo "0")
echo "   📊 Services with health checks: $HEALTHCHECK_COUNT"

if [ "$HEALTHCHECK_COUNT" -gt 0 ]; then
    echo "   ✅ Health checks configured"
else
    echo "   ⚠️  No health checks found"
fi

# Test 9: Security configurations
echo ""
echo "9️⃣  Checking security configurations..."
SECURITY_CHECKS=0

if grep -q "ensemble_user" Dockerfile; then
    echo "   ✅ Non-root user configured"
    ((SECURITY_CHECKS++))
fi

if grep -q "X-Frame-Options" nginx.conf; then
    echo "   ✅ Security headers configured"
    ((SECURITY_CHECKS++))
fi

if grep -q "limit_req" nginx.conf; then
    echo "   ✅ Rate limiting configured"
    ((SECURITY_CHECKS++))
fi

echo "   📊 Security configurations: $SECURITY_CHECKS/3"

# Summary
echo ""
echo "📋 Configuration Test Summary"
echo "============================="
echo "✅ Docker and nginx syntax validation passed"
echo "📊 Performance optimizations: $PERFORMANCE_CHECKS/4"
echo "🔒 Security configurations: $SECURITY_CHECKS/3"
echo ""

if [ "$PERFORMANCE_CHECKS" -ge 3 ] && [ "$SECURITY_CHECKS" -ge 2 ]; then
    echo "🎉 Configuration is production-ready for Dynamic Position Optimization!"
    echo ""
    echo "📝 Next steps:"
    echo "   1. Run: ./scripts/build-production.sh"
    echo "   2. Deploy: docker-compose --profile prod --profile optimizer up -d"
    echo "   3. Test: curl http://localhost/health/position-optimizer"
    exit 0
else
    echo "⚠️  Configuration needs improvement before production deployment"
    echo ""
    echo "📝 Recommendations:"
    if [ "$PERFORMANCE_CHECKS" -lt 3 ]; then
        echo "   - Review performance optimization settings"
    fi
    if [ "$SECURITY_CHECKS" -lt 2 ]; then
        echo "   - Review security configuration settings"
    fi
    exit 1
fi