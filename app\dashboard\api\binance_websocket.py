"""
WebSocket API for Binance real-time updates
Provides real-time streaming of:
- Account balance updates
- Position changes and PnL
- Order status updates
- Price updates for watched symbols
- Balance changes

Created: June 16, 2025
"""

import logging
import asyncio
import json
from typing import Dict, List, Any, Set, Optional
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from starlette.websockets import WebSocketState

from app.services.exchange.binance_client import BinanceExchangeClient
from app.config.settings import Settings
from app.security import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter()

# Store active WebSocket connections for different streams
binance_connections: Set[WebSocket] = set()
watched_symbols: Set[str] = set()

class BinanceWebSocketManager:
    """Manages Binance WebSocket connections and data streaming."""
    
    def __init__(self):
        self.binance_client: Optional[BinanceExchangeClient] = None
        self.account_stream_active = False
        self.price_streams_active = False
        self.update_interval = 5  # seconds
        self.last_account_data: Optional[Dict] = None
        self.last_positions_data: List[Dict] = []
        self.last_orders_data: List[Dict] = []
        
    async def initialize_client(self):
        """Initialize Binance client if not already done."""
        if self.binance_client is None:
            try:
                settings = Settings()
                self.binance_client = BinanceExchangeClient(settings)
                await self.binance_client._initialize_client()
                logger.info("Binance WebSocket client initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Binance client: {e}")
                raise
    
    async def start_account_stream(self):
        """Start streaming account updates."""
        if self.account_stream_active:
            return
            
        self.account_stream_active = True
        asyncio.create_task(self._account_update_loop())
        logger.info("Started Binance account update stream")
    
    async def start_price_stream(self, symbols: List[str]):
        """Start streaming price updates for specified symbols."""
        watched_symbols.update(symbols)
        
        if not self.price_streams_active:
            self.price_streams_active = True
            asyncio.create_task(self._price_update_loop())
            logger.info(f"Started Binance price update stream for {len(symbols)} symbols")
    
    async def stop_streams(self):
        """Stop all active streams."""
        self.account_stream_active = False
        self.price_streams_active = False
        logger.info("Stopped all Binance streams")
    
    async def _account_update_loop(self):
        """Main loop for account data updates."""
        while self.account_stream_active and binance_connections:
            try:
                await self.initialize_client()
                
                # Get account information
                account_info = await self._get_account_update()
                if account_info and account_info != self.last_account_data:
                    await self._broadcast_account_update(account_info)
                    self.last_account_data = account_info
                
                # Get positions
                positions = await self._get_positions_update()
                if positions != self.last_positions_data:
                    await self._broadcast_positions_update(positions)
                    self.last_positions_data = positions
                
                # Get open orders
                orders = await self._get_orders_update()
                if orders != self.last_orders_data:
                    await self._broadcast_orders_update(orders)
                    self.last_orders_data = orders
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in account update loop: {e}")
                await asyncio.sleep(self.update_interval * 2)  # Back off on error
    
    async def _price_update_loop(self):
        """Main loop for price updates."""
        while self.price_streams_active and binance_connections:
            try:
                await self.initialize_client()
                
                # Get price updates for watched symbols
                for symbol in list(watched_symbols):
                    try:
                        price_update = await self._get_price_update(symbol)
                        if price_update:
                            await self._broadcast_price_update(price_update)
                    except Exception as e:
                        logger.warning(f"Error getting price for {symbol}: {e}")
                
                await asyncio.sleep(1)  # Price updates more frequently
                
            except Exception as e:
                logger.error(f"Error in price update loop: {e}")
                await asyncio.sleep(5)  # Back off on error
    
    async def _get_account_update(self) -> Optional[Dict]:
        """Get account balance and equity information."""
        try:
            account = await self.binance_client.client.futures_account()
            
            return {
                "balance": float(account.get('totalWalletBalance', 0)),
                "equity": float(account.get('totalMarginBalance', 0)),
                "margin_used": float(account.get('totalPositionInitialMargin', 0)),
                "margin_available": float(account.get('availableBalance', 0)),
                "pnl_unrealized": float(account.get('totalUnrealizedProfit', 0)),
                "pnl_realized_today": 0.0,  # Would need historical data
                "margin_ratio": (
                    float(account.get('totalPositionInitialMargin', 0)) /
                    max(float(account.get('totalMarginBalance', 1)), 1)
                ) * 100,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting account update: {e}")
            return None
    
    async def _get_positions_update(self) -> List[Dict]:
        """Get current positions information."""
        try:
            positions = await self.binance_client.get_open_positions()
            
            position_updates = []
            for pos in positions:
                if float(pos.get('positionAmt', 0)) != 0:
                    symbol = pos.get('symbol', '')
                    position_amt = float(pos.get('positionAmt', 0))
                    
                    # Get current price
                    try:
                        ticker = await self.binance_client.get_ticker(symbol)
                        current_price = float(ticker.get('price', 0))
                    except Exception:
                        current_price = float(pos.get('entryPrice', 0))
                    
                    position_updates.append({
                        "symbol": symbol,
                        "size": abs(position_amt),
                        "entry_price": float(pos.get('entryPrice', 0)),
                        "current_price": current_price,
                        "pnl": float(pos.get('unRealizedProfit', 0)),
                        "pnl_percentage": float(pos.get('percentage', 0)),
                        "side": "LONG" if position_amt > 0 else "SHORT",
                        "liquidation_price": float(pos.get('liquidationPrice', 0)),
                        "timestamp": datetime.now().isoformat()
                    })
            
            return position_updates
        except Exception as e:
            logger.error(f"Error getting positions update: {e}")
            return []
    
    async def _get_orders_update(self) -> List[Dict]:
        """Get open orders information."""
        try:
            orders = await self.binance_client.get_open_orders()
            
            order_updates = []
            for order in orders:
                order_updates.append({
                    "order_id": str(order.get('orderId', '')),
                    "symbol": order.get('symbol', ''),
                    "side": order.get('side', ''),
                    "type": order.get('type', ''),
                    "amount": float(order.get('origQty', 0)),
                    "filled": float(order.get('executedQty', 0)),
                    "remaining": float(order.get('origQty', 0)) - float(order.get('executedQty', 0)),
                    "price": float(order.get('price', 0)) if order.get('price') else None,
                    "status": order.get('status', ''),
                    "timestamp": datetime.now().isoformat()
                })
            
            return order_updates
        except Exception as e:
            logger.error(f"Error getting orders update: {e}")
            return []
    
    async def _get_price_update(self, symbol: str) -> Optional[Dict]:
        """Get price update for a specific symbol."""
        try:
            ticker = await self.binance_client.get_ticker(symbol)
            
            return {
                "symbol": symbol,
                "price": float(ticker.get('price', 0)),
                "change": float(ticker.get('priceChange', 0)),
                "change_percent": float(ticker.get('priceChangePercent', 0)),
                "volume": float(ticker.get('volume', 0)),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting price update for {symbol}: {e}")
            return None
    
    async def _broadcast_account_update(self, account_data: Dict):
        """Broadcast account update to all connected clients."""
        message = {
            "type": "binance_account_update",
            "data": account_data
        }
        await self._broadcast_to_all(message)
    
    async def _broadcast_positions_update(self, positions_data: List[Dict]):
        """Broadcast positions update to all connected clients."""
        for position in positions_data:
            message = {
                "type": "binance_position_update",
                "data": position
            }
            await self._broadcast_to_all(message)
    
    async def _broadcast_orders_update(self, orders_data: List[Dict]):
        """Broadcast orders update to all connected clients."""
        for order in orders_data:
            message = {
                "type": "binance_order_update",
                "data": order
            }
            await self._broadcast_to_all(message)
    
    async def _broadcast_price_update(self, price_data: Dict):
        """Broadcast price update to all connected clients."""
        message = {
            "type": "binance_price_update",
            "data": price_data
        }
        await self._broadcast_to_all(message)
    
    async def _broadcast_to_all(self, message: Dict):
        """Broadcast message to all connected WebSocket clients."""
        if not binance_connections:
            return
        
        disconnected = set()
        for connection in binance_connections:
            try:
                await connection.send_json(message)
            except Exception as e:
                logger.error(f"Error broadcasting to client: {e}")
                disconnected.add(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            if connection in binance_connections:
                binance_connections.remove(connection)
                logger.info("Removed disconnected client")

# Global WebSocket manager instance
ws_manager = BinanceWebSocketManager()

@router.websocket("/ws/binance")
async def websocket_binance(websocket: WebSocket):
    """WebSocket endpoint for Binance real-time updates."""
    await websocket.accept()
    binance_connections.add(websocket)
    
    logger.info(f"New Binance WebSocket connection. Total connections: {len(binance_connections)}")
    
    try:
        # Send initial connection status
        await websocket.send_json({
            "type": "system_status",
            "data": {
                "status": "connected",
                "message": "Connected to Binance real-time stream",
                "timestamp": datetime.now().isoformat()
            }
        })
        
        # Start data streams if this is the first connection
        if len(binance_connections) == 1:
            await ws_manager.start_account_stream()
            
            # Start price stream for common symbols
            await ws_manager.start_price_stream([
                "BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT"
            ])
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for incoming message with timeout
                message = await asyncio.wait_for(websocket.receive_json(), timeout=30.0)
                
                # Handle client messages
                if message.get("type") == "subscribe_symbol":
                    symbol = message.get("symbol")
                    if symbol:
                        watched_symbols.add(symbol)
                        logger.info(f"Added symbol {symbol} to watch list")
                        
                        # Send confirmation
                        await websocket.send_json({
                            "type": "subscription_confirmed",
                            "data": {
                                "symbol": symbol,
                                "message": f"Subscribed to {symbol} price updates"
                            }
                        })
                
                elif message.get("type") == "unsubscribe_symbol":
                    symbol = message.get("symbol")
                    if symbol in watched_symbols:
                        watched_symbols.remove(symbol)
                        logger.info(f"Removed symbol {symbol} from watch list")
                        
                        # Send confirmation
                        await websocket.send_json({
                            "type": "unsubscription_confirmed",
                            "data": {
                                "symbol": symbol,
                                "message": f"Unsubscribed from {symbol} price updates"
                            }
                        })
                
                elif message.get("type") == "ping":
                    # Respond to ping
                    await websocket.send_json({
                        "type": "pong",
                        "data": {
                            "timestamp": datetime.now().isoformat()
                        }
                    })
                
            except asyncio.TimeoutError:
                # Send keepalive ping
                try:
                    await websocket.send_json({
                        "type": "ping",
                        "data": {
                            "timestamp": datetime.now().isoformat()
                        }
                    })
                except Exception as e:
                    logger.error(f"Error sending keepalive ping: {e}")
                    break
            
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                try:
                    await websocket.send_json({
                        "type": "error",
                        "data": {
                            "code": "message_error",
                            "message": "Error processing message"
                        }
                    })
                except Exception:
                    break
    
    except WebSocketDisconnect:
        logger.info("Binance WebSocket client disconnected")
    except Exception as e:
        logger.error(f"Binance WebSocket error: {e}")
    finally:
        # Clean up connection
        if websocket in binance_connections:
            binance_connections.remove(websocket)
            logger.info(f"Removed connection. Remaining: {len(binance_connections)}")
        
        # Stop streams if no more connections
        if len(binance_connections) == 0:
            await ws_manager.stop_streams()
            logger.info("Stopped all streams - no active connections")

@router.get("/binance/websocket/status")
async def get_websocket_status():
    """Get status of Binance WebSocket connections."""
    return {
        "active_connections": len(binance_connections),
        "watched_symbols": list(watched_symbols),
        "account_stream_active": ws_manager.account_stream_active,
        "price_streams_active": ws_manager.price_streams_active,
        "timestamp": datetime.now().isoformat()
    }

async def broadcast_custom_update(update_type: str, data: Dict):
    """Allow external services to broadcast custom updates."""
    message = {
        "type": update_type,
        "data": data
    }
    await ws_manager._broadcast_to_all(message)

# Export the manager for external use
__all__ = ["router", "ws_manager", "broadcast_custom_update"]