"""
ZenML Pipeline for Ensemble Weight Optimization with Cost-Aware Rewards and W&B Tracking
Enhanced ML pipeline with cost-aware reward functions, trading cost integration, and automated model retraining.
Task 3.1.1: Implements cost-aware reward functions in ZenML pipeline.
"""

from zenml import pipeline, step
from zenml.config import DockerSettings
from typing import Dict, Tuple, Any
import numpy as np
import pandas as pd
from datetime import datetime
import pickle
import logging
import os
import asyncio

logger = logging.getLogger(__name__)

# W&B configuration
WANDB_PROJECT = "crypto-ensemble-optimization"
WANDB_ENTITY = os.getenv("WANDB_ENTITY", "crypto-trading-ai")

# W&B tracking utility functions
def init_wandb_tracking(experiment_name: str, tags: list = None):
    """Initialize W&B tracking if available."""
    try:
        import wandb
        if not wandb.run:
            wandb.init(
                project=WANDB_PROJECT,
                name=f"{experiment_name}-{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                tags=tags or [],
                reinit=True
            )
        return True
    except ImportError:
        logger.warning(f"W&B not available. Running without tracking.")
        return False
    except Exception as e:
        logger.warning(f"W&B tracking initialization failed: {e}. Continuing without tracking.")
        return False

def log_to_wandb(metrics_dict: dict):
    """Log metrics to W&B if available."""
    try:
        import wandb
        if wandb.run:
            wandb.log(metrics_dict)
    except:
        pass

@step
def load_historical_data() -> pd.DataFrame:
    """Load historical market data and strategy performance with cost-aware enhancements."""
    try:
        # Enhanced synthetic data with cost-aware metrics for Task 3.1.1
        np.random.seed(42)
        n_samples = 1000
        
        # Base market data
        timestamps = pd.date_range(start='2023-01-01', periods=n_samples, freq='1H')
        volatility = np.random.lognormal(mean=-3, sigma=0.5, size=n_samples)
        volume = np.random.lognormal(mean=15, sigma=1, size=n_samples)
        rsi = np.random.normal(50, 15, n_samples).clip(0, 100)
        macd = np.random.normal(0, 0.1, n_samples)
        price_change = np.random.normal(0, 0.02, n_samples)
        
        # Strategy returns (synthetic)
        grid_return = np.random.normal(0.001, 0.015, n_samples)
        ta_return = np.random.normal(0.0008, 0.012, n_samples)
        trend_return = np.random.normal(0.0012, 0.018, n_samples)
        
        # Cost-aware enhancements for Task 3.1.1
        # Trading costs (basis points) - varies with volatility and volume
        base_trading_costs = 5 + (volatility * 1000) + np.random.normal(0, 2, n_samples)
        trading_costs_bps = np.clip(base_trading_costs, 1, 50)  # 1-50 basis points
        
        # Trade sizes (USD) - varies with market conditions
        base_trade_size = 10000 + (volume / np.mean(volume)) * 5000
        trade_sizes = base_trade_size + np.random.normal(0, 2000, n_samples)
        trade_sizes = np.clip(trade_sizes, 1000, 100000)
        
        # Slippage estimates (basis points) - higher in volatile markets
        slippage_bps = 2 + (volatility * 500) + np.random.exponential(1, n_samples)
        slippage_bps = np.clip(slippage_bps, 0.5, 25)
        
        # Market impact (basis points) - increases with trade size relative to volume
        market_impact_bps = (trade_sizes / volume) * 100 + np.random.gamma(2, 0.5, n_samples)
        market_impact_bps = np.clip(market_impact_bps, 0.1, 15)
        
        # Funding costs (for leveraged positions)
        funding_rates = np.random.normal(0.01, 0.005, n_samples)  # 1% base rate
        funding_costs_bps = funding_rates * 100 / 3  # Per 8-hour period, converted to bps
        
        # Total cost impact on returns
        total_costs_bps = trading_costs_bps + slippage_bps + market_impact_bps + funding_costs_bps
        total_costs_pct = total_costs_bps / 10000  # Convert bps to percentage
        
        # Net returns after costs
        grid_net_return = grid_return - total_costs_pct * np.random.uniform(0.8, 1.2, n_samples)
        ta_net_return = ta_return - total_costs_pct * np.random.uniform(0.7, 1.1, n_samples)
        trend_net_return = trend_return - total_costs_pct * np.random.uniform(0.9, 1.3, n_samples)
        
        # Cost efficiency metrics
        cost_efficiency_grid = grid_return / np.maximum(total_costs_pct, 0.0001)
        cost_efficiency_ta = ta_return / np.maximum(total_costs_pct, 0.0001) 
        cost_efficiency_trend = trend_return / np.maximum(total_costs_pct, 0.0001)
        
        data = pd.DataFrame({
            'timestamp': timestamps,
            'volatility': volatility,
            'volume': volume,
            'rsi': rsi,
            'macd': macd,
            'price_change': price_change,
            
            # Strategy returns (gross)
            'grid_return': grid_return,
            'ta_return': ta_return,
            'trend_return': trend_return,
            
            # Cost components (Task 3.1.1)
            'trading_costs_bps': trading_costs_bps,
            'slippage_bps': slippage_bps,
            'market_impact_bps': market_impact_bps,
            'funding_costs_bps': funding_costs_bps,
            'total_costs_bps': total_costs_bps,
            'total_costs_pct': total_costs_pct,
            'trade_sizes_usd': trade_sizes,
            
            # Net returns after costs
            'grid_net_return': grid_net_return,
            'ta_net_return': ta_net_return,
            'trend_net_return': trend_net_return,
            
            # Cost efficiency metrics
            'cost_efficiency_grid': cost_efficiency_grid,
            'cost_efficiency_ta': cost_efficiency_ta,
            'cost_efficiency_trend': cost_efficiency_trend,
            
            # Additional cost-aware features
            'cost_to_return_ratio': total_costs_pct / np.maximum(np.abs(grid_return), 0.0001),
            'net_profit_margin': (grid_return - total_costs_pct) / np.maximum(grid_return, 0.0001),
            'cost_volatility': np.roll(pd.Series(total_costs_bps).rolling(24).std().fillna(0), 0)
        })
        
        logger.info(f"Loaded {len(data)} historical data points with cost-aware enhancements")
        logger.info(f"Average trading costs: {np.mean(total_costs_bps):.2f} bps")
        logger.info(f"Cost impact on returns: {np.mean(total_costs_pct) * 100:.3f}%")
        
        return data
        
    except Exception as e:
        logger.error(f"Failed to load historical data: {e}")
        raise

@step
def prepare_training_features(data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
    """Prepare cost-aware features and targets for ML training (Task 3.1.1)."""
    try:
        # Enhanced feature set with cost-aware components
        base_features = ['volatility', 'volume', 'rsi', 'macd', 'price_change']
        
        # Add rolling statistics
        data['volatility_ma'] = data['volatility'].rolling(24).mean()
        data['volume_ma'] = data['volume'].rolling(24).mean()
        data['rsi_ma'] = data['rsi'].rolling(24).mean()
        
        # Cost-aware features (Task 3.1.1)
        data['trading_costs_ma'] = data['trading_costs_bps'].rolling(24).mean()
        data['slippage_ma'] = data['slippage_bps'].rolling(24).mean()
        data['cost_efficiency_ma'] = (
            (data['cost_efficiency_grid'] + data['cost_efficiency_ta'] + data['cost_efficiency_trend']) / 3
        ).rolling(24).mean()
        data['net_return_volatility'] = (
            (data['grid_net_return'] + data['ta_net_return'] + data['trend_net_return']) / 3
        ).rolling(24).std()
        
        # Market microstructure features
        data['volume_cost_ratio'] = data['volume'] / np.maximum(data['total_costs_bps'], 1)
        data['volatility_cost_interaction'] = data['volatility'] * data['total_costs_bps']
        data['trade_size_impact'] = data['trade_sizes_usd'] / data['volume']
        
        # Extended feature set with cost awareness
        cost_aware_features = base_features + [
            'volatility_ma', 'volume_ma', 'rsi_ma',
            # Cost-specific features
            'trading_costs_bps', 'slippage_bps', 'market_impact_bps', 'funding_costs_bps',
            'total_costs_bps', 'cost_to_return_ratio', 'net_profit_margin',
            'trading_costs_ma', 'slippage_ma', 'cost_efficiency_ma', 'net_return_volatility',
            'volume_cost_ratio', 'volatility_cost_interaction', 'trade_size_impact'
        ]
        
        extended_features = data[cost_aware_features].fillna(0).values
        
        # Cost-aware target calculation (Task 3.1.1)
        # Use net returns (after costs) instead of gross returns for optimization
        net_returns = data[['grid_net_return', 'ta_net_return', 'trend_net_return']].values
        gross_returns = data[['grid_return', 'ta_return', 'trend_return']].values
        cost_efficiencies = data[['cost_efficiency_grid', 'cost_efficiency_ta', 'cost_efficiency_trend']].values
        
        # Calculate cost-aware optimal weights
        window = 24
        optimal_weights = []
        
        for i in range(len(net_returns)):
            if i < window:
                # Equal weights for initial period
                optimal_weights.append([1/3, 1/3, 1/3])
            else:
                # Multi-objective optimization considering both returns and costs
                window_net_returns = net_returns[i-window:i]
                window_gross_returns = gross_returns[i-window:i]
                window_costs = data.iloc[i-window:i]['total_costs_pct'].values.reshape(-1, 1)
                window_cost_efficiency = cost_efficiencies[i-window:i]
                
                # Cost-adjusted Sharpe ratios
                net_sharpe_ratios = np.mean(window_net_returns, axis=0) / (np.std(window_net_returns, axis=0) + 1e-8)
                
                # Cost efficiency scores
                efficiency_scores = np.mean(window_cost_efficiency, axis=0)
                
                # Risk-adjusted cost scores (lower is better, so invert)
                avg_costs = np.mean(window_costs)
                cost_penalty = np.maximum(0, avg_costs - 0.01) * 10  # Penalty above 1% cost
                
                # Combined scoring with cost awareness
                # 60% weight on net returns, 30% on cost efficiency, 10% on cost penalty
                combined_scores = (
                    net_sharpe_ratios * 0.6 + 
                    efficiency_scores * 0.3 - 
                    cost_penalty * 0.1
                )
                
                # Handle edge cases
                if np.all(np.isnan(combined_scores)) or np.all(combined_scores <= 0):
                    # Fallback to equal weights
                    weights = np.array([1/3, 1/3, 1/3])
                else:
                    # Softmax normalization with temperature scaling
                    temp_scaling = 5  # Lower temperature for more conservative allocation
                    exp_scores = np.exp(combined_scores * temp_scaling)
                    weights = exp_scores / (np.sum(exp_scores) + 1e-8)
                    
                    # Ensure minimum allocation (avoid zero weights)
                    min_weight = 0.05
                    weights = np.maximum(weights, min_weight)
                    weights = weights / np.sum(weights)
                
                optimal_weights.append(weights)
        
        targets = np.array(optimal_weights)
        
        logger.info(f"Prepared cost-aware features: {extended_features.shape}, targets: {targets.shape}")
        logger.info(f"Feature count: {len(cost_aware_features)} (including {len(cost_aware_features) - len(base_features)} cost-aware features)")
        logger.info(f"Cost-aware optimization applied to {len(targets)} target weight vectors")
        
        return extended_features, targets
        
    except Exception as e:
        logger.error(f"Failed to prepare cost-aware training features: {e}")
        raise

@step
def train_ensemble_model(
    features: np.ndarray, 
    targets: np.ndarray
) -> Dict[str, Any]:
    """Train ensemble weight optimization model with comprehensive W&B tracking."""
    try:
        # Initialize W&B tracking
        wandb_available = init_wandb_tracking(
            "ensemble-weight-training",
            ["ensemble", "trading", "ml", "strategy-optimization"]
        )
        
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import mean_squared_error, r2_score
        
        # Initialize W&B run with detailed config
        wandb_config = {
            "model_type": "RandomForestRegressor",
            "n_estimators": 100,
            "max_depth": 10,
            "test_size": 0.2,
            "random_state": 42,
            "feature_count": features.shape[1],
            "sample_count": features.shape[0],
            "target_strategies": ["GridStrategy", "TechnicalAnalysisStrategy", "TrendFollowingStrategy"]
        }
        
        # Log configuration to W&B
        if wandb_available:
            try:
                import wandb
                wandb.config.update(wandb_config)
            except:
                pass
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, targets, test_size=0.2, random_state=42
        )
        
        # Log data split information
        log_to_wandb({
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "train_ratio": len(X_train) / len(features),
            "test_ratio": len(X_test) / len(features)
        })
        
        # Train Random Forest model
        model = RandomForestRegressor(
            n_estimators=wandb_config["n_estimators"],
            max_depth=wandb_config["max_depth"],
            random_state=wandb_config["random_state"],
            n_jobs=-1
        )
        
        # Track training time
        import time
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Evaluate model
        train_pred = model.predict(X_train)
        test_pred = model.predict(X_test)
        
        # Ensure predictions sum to 1 (normalize)
        train_pred = train_pred / train_pred.sum(axis=1, keepdims=True)
        test_pred = test_pred / test_pred.sum(axis=1, keepdims=True)
        
        # Calculate comprehensive metrics
        train_mse = mean_squared_error(y_train, train_pred)
        test_mse = mean_squared_error(y_test, test_pred)
        train_r2 = r2_score(y_train, train_pred)
        test_r2 = r2_score(y_test, test_pred)
        
        # Calculate per-strategy metrics
        strategy_names = ["GridStrategy", "TechnicalAnalysisStrategy", "TrendFollowingStrategy"]
        strategy_metrics = {}
        for i, strategy in enumerate(strategy_names):
            strategy_train_mse = mean_squared_error(y_train[:, i], train_pred[:, i])
            strategy_test_mse = mean_squared_error(y_test[:, i], test_pred[:, i])
            strategy_train_r2 = r2_score(y_train[:, i], train_pred[:, i])
            strategy_test_r2 = r2_score(y_test[:, i], test_pred[:, i])
            
            strategy_metrics[f"{strategy}_train_mse"] = strategy_train_mse
            strategy_metrics[f"{strategy}_test_mse"] = strategy_test_mse
            strategy_metrics[f"{strategy}_train_r2"] = strategy_train_r2
            strategy_metrics[f"{strategy}_test_r2"] = strategy_test_r2
        
        # Cost-aware feature importance analysis (Task 3.1.1)
        feature_names = [
            'volatility', 'volume', 'rsi', 'macd', 'price_change', 
            'volatility_ma', 'volume_ma', 'rsi_ma',
            # Cost-aware features
            'trading_costs_bps', 'slippage_bps', 'market_impact_bps', 'funding_costs_bps',
            'total_costs_bps', 'cost_to_return_ratio', 'net_profit_margin',
            'trading_costs_ma', 'slippage_ma', 'cost_efficiency_ma', 'net_return_volatility',
            'volume_cost_ratio', 'volatility_cost_interaction', 'trade_size_impact'
        ]
        
        # Ensure feature names match actual features
        actual_feature_count = model.n_features_in_
        if len(feature_names) != actual_feature_count:
            logger.warning(f"Feature name count mismatch: {len(feature_names)} vs {actual_feature_count}")
            # Truncate or pad feature names as needed
            if len(feature_names) > actual_feature_count:
                feature_names = feature_names[:actual_feature_count]
            else:
                # Add generic names for missing features
                for i in range(len(feature_names), actual_feature_count):
                    feature_names.append(f"feature_{i}")
        
        feature_importance = dict(zip(feature_names, model.feature_importances_))
        
        # Analyze cost-aware feature importance
        cost_feature_importance = {
            k: v for k, v in feature_importance.items() 
            if any(cost_term in k for cost_term in ['cost', 'slippage', 'impact', 'funding', 'efficiency'])
        }
        cost_importance_sum = sum(cost_feature_importance.values())
        
        logger.info(f"Cost-aware features contribute {cost_importance_sum:.3f} total importance")
        logger.info(f"Top cost features: {sorted(cost_feature_importance.items(), key=lambda x: x[1], reverse=True)[:3]}")
        
        # Comprehensive metrics dictionary with cost-aware enhancements
        metrics = {
            'train_mse': train_mse,
            'test_mse': test_mse,
            'train_r2': train_r2,
            'test_r2': test_r2,
            'training_time_seconds': training_time,
            'feature_count': features.shape[1],
            'sample_count': len(features),
            **strategy_metrics,
            **{f"feature_importance_{k}": v for k, v in feature_importance.items()},
            
            # Cost-aware metrics (Task 3.1.1)
            'cost_feature_importance_sum': cost_importance_sum,
            'cost_feature_count': len(cost_feature_importance),
            'cost_awareness_ratio': cost_importance_sum / max(sum(feature_importance.values()), 0.001),
            'model_type': 'cost_aware_ensemble'
        }
        
        # Log all metrics to W&B
        log_to_wandb(metrics)
        
        # Log model summary without visualizations for now
        model_summary = {
            "model_type": "RandomForestRegressor",
            "n_estimators": model.n_estimators,
            "max_depth": model.max_depth,
            "n_features": model.n_features_in_,
            "n_outputs": model.n_outputs_
        }
        log_to_wandb({"model_summary": model_summary})
        
        logger.info(f"Model trained successfully. Test R2: {test_r2:.4f}, Test MSE: {test_mse:.6f}")
        logger.info(f"Training time: {training_time:.2f} seconds")
        
        return {
            'model': model,
            'metrics': metrics,
            'feature_names': feature_names,
            'strategy_names': strategy_names
        }
        
    except Exception as e:
        logger.error(f"Model training failed: {e}")
        log_to_wandb({"training_error": str(e)})
        raise

@step
def evaluate_model(model_data: Dict[str, Any]) -> Dict[str, float]:
    """Evaluate model performance with comprehensive W&B tracking."""
    try:
        # Initialize W&B tracking for evaluation
        wandb_available = init_wandb_tracking(
            "ensemble-weight-evaluation",
            ["evaluation", "model-performance", "ensemble"]
        )
        
        model = model_data['model']
        metrics = model_data['metrics']
        feature_names = model_data['feature_names']
        strategy_names = model_data['strategy_names']
        
        # Additional evaluation metrics
        feature_importance = model.feature_importances_
        importance_dict = dict(zip(feature_names, feature_importance))
        
        # Calculate model confidence based on performance
        # Use weighted average of strategy R2 scores
        strategy_r2_scores = [
            metrics.get(f"{strategy}_test_r2", 0) 
            for strategy in strategy_names
        ]
        avg_strategy_r2 = np.mean(strategy_r2_scores)
        confidence = min(1.0, max(0.1, avg_strategy_r2))
        
        # Advanced evaluation metrics
        evaluation_metrics = {
            **metrics,
            'model_confidence': confidence,
            'avg_strategy_r2': avg_strategy_r2,
            'strategy_r2_std': np.std(strategy_r2_scores),
            'feature_importance_volatility': importance_dict.get('volatility', 0),
            'feature_importance_volume': importance_dict.get('volume', 0),
            'feature_importance_rsi': importance_dict.get('rsi', 0),
            'feature_importance_macd': importance_dict.get('macd', 0),
            'total_features_used': len(feature_names),
            'model_complexity_score': model.max_depth * model.n_estimators / 1000
        }
        
        # Log evaluation metrics to W&B
        log_to_wandb({
            "evaluation/model_confidence": confidence,
            "evaluation/avg_strategy_r2": avg_strategy_r2,
            "evaluation/strategy_r2_std": np.std(strategy_r2_scores),
            "evaluation/overall_test_r2": metrics['test_r2'],
            "evaluation/overall_test_mse": metrics['test_mse'],
            "evaluation/model_complexity": evaluation_metrics['model_complexity_score']
        })
        
        # Model readiness assessment
        readiness_score = (
            confidence * 0.4 +  # Model confidence
            min(1.0, avg_strategy_r2 / 0.8) * 0.3 +  # Strategy performance
            min(1.0, (1 - metrics['test_mse']) / 0.1) * 0.3  # Overall accuracy
        )
        
        log_to_wandb({
            "evaluation/model_readiness_score": readiness_score,
            "evaluation/ready_for_production": readiness_score > 0.7
        })
        
        evaluation_metrics['model_readiness_score'] = readiness_score
        evaluation_metrics['ready_for_production'] = readiness_score > 0.7
        
        logger.info(f"Model evaluation completed. Confidence: {confidence:.3f}")
        logger.info(f"Average strategy R²: {avg_strategy_r2:.3f}")
        logger.info(f"Model readiness score: {readiness_score:.3f}")
        
        return evaluation_metrics
        
    except Exception as e:
        logger.error(f"Model evaluation failed: {e}")
        log_to_wandb({"evaluation_error": str(e)})
        raise

@step
def deploy_model(model_data: Dict[str, Any], metrics: Dict[str, float]) -> str:
    """Deploy model with comprehensive W&B artifact tracking."""
    try:
        # Initialize W&B tracking for deployment
        wandb_available = init_wandb_tracking(
            "ensemble-model-deployment",
            ["deployment", "model-registry", "production"]
        )
        
        import os
        try:
            import joblib
        except ImportError:
            joblib = None
        
        model = model_data['model']
        feature_names = model_data['feature_names']
        strategy_names = model_data['strategy_names']
        
        # Prepare deployment metadata
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_version = f'v1.0.{timestamp}'
        
        # Save model to models directory
        models_dir = '/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/models'
        os.makedirs(models_dir, exist_ok=True)
        
        model_filename = f'ensemble_weight_optimizer_{timestamp}.pkl'
        model_path = os.path.join(models_dir, model_filename)
        
        # Enhanced model package with comprehensive metadata
        model_package = {
            'model': model,
            'metrics': metrics,
            'feature_names': feature_names,
            'strategy_names': strategy_names,
            'timestamp': datetime.now().isoformat(),
            'version': model_version,
            'deployment_config': {
                'model_type': 'RandomForestRegressor',
                'n_estimators': model.n_estimators,
                'max_depth': model.max_depth,
                'feature_count': len(feature_names),
                'strategy_count': len(strategy_names),
                'ready_for_production': metrics.get('ready_for_production', False),
                'model_confidence': metrics.get('model_confidence', 0)
            },
            'performance_thresholds': {
                'min_confidence': 0.6,
                'min_r2_score': 0.5,
                'max_mse': 0.1
            }
        }
        
        # Save model package
        with open(model_path, 'wb') as f:
            pickle.dump(model_package, f)
        
        # Also save with joblib for better scikit-learn compatibility (if available)
        if joblib:
            joblib_path = model_path.replace('.pkl', '_joblib.pkl')
            joblib.dump(model_package, joblib_path)
        else:
            joblib_path = None
        
        # Create symlink to latest model
        latest_path = os.path.join(models_dir, 'ensemble_weight_optimizer_latest.pkl')
        if os.path.exists(latest_path):
            os.remove(latest_path)
        os.symlink(model_filename, latest_path)
        
        if joblib and joblib_path:
            latest_joblib_path = os.path.join(models_dir, 'ensemble_weight_optimizer_latest_joblib.pkl')
            if os.path.exists(latest_joblib_path):
                os.remove(latest_joblib_path)
            os.symlink(os.path.basename(joblib_path), latest_joblib_path)
        
        # Log deployment information to W&B
        deployment_info = {
            "deployment/model_version": model_version,
            "deployment/model_path": model_path,
            "deployment/deployment_timestamp": datetime.now().isoformat(),
            "deployment/model_size_mb": os.path.getsize(model_path) / (1024 * 1024),
            "deployment/ready_for_production": metrics.get('ready_for_production', False),
            "deployment/model_confidence": metrics.get('model_confidence', 0),
            "deployment/test_r2_score": metrics.get('test_r2', 0),
            "deployment/test_mse": metrics.get('test_mse', 0)
        }
        log_to_wandb(deployment_info)
        
        # Create model config file
        config_path = os.path.join(models_dir, f'model_config_{timestamp}.json')
        import json
        with open(config_path, 'w') as f:
            json.dump({
                "version": model_version,
                "feature_names": feature_names,
                "strategy_names": strategy_names,
                "deployment_config": model_package['deployment_config'],
                "performance_metrics": metrics,
                "usage_instructions": {
                    "input_format": "numpy array with features: " + str(feature_names),
                    "output_format": "numpy array with strategy weights: " + str(strategy_names),
                    "normalization": "Outputs are normalized to sum to 1.0",
                    "confidence_threshold": "Use predictions only if model confidence > 0.6"
                }
            }, f, indent=2)
        
        # Log deployment success
        log_to_wandb({
            "deployment/status": "success",
            "deployment/final_model_version": model_version,
            "deployment/config_path": config_path
        })
        
        logger.info(f"Model deployed successfully to {model_path}")
        logger.info(f"Model version: {model_version}")
        logger.info(f"Model config saved to: {config_path}")
        logger.info(f"Ready for production: {metrics.get('ready_for_production', False)}")
        
        return model_version
        
    except Exception as e:
        logger.error(f"Model deployment failed: {e}")
        log_to_wandb({
            "deployment/status": "failed",
            "deployment/error": str(e)
        })
        raise

@step
def evaluate_cost_performance(model_data: Dict[str, Any], metrics: Dict[str, float]) -> Dict[str, Any]:
    """
    Evaluate cost-aware model performance and determine retraining necessity.
    Task 3.1.1: Automated cost-aware model retraining decision.
    """
    try:
        # Initialize W&B tracking for cost performance evaluation
        wandb_available = init_wandb_tracking(
            "cost-performance-evaluation",
            ["cost-optimization", "performance-monitoring", "retraining"]
        )
        
        model = model_data['model']
        feature_names = model_data['feature_names']
        strategy_names = model_data['strategy_names']
        
        # Cost-aware performance thresholds
        cost_performance_thresholds = {
            'min_cost_awareness_ratio': 0.15,      # At least 15% importance from cost features
            'min_cost_adjusted_r2': 0.6,           # Minimum cost-adjusted R² score
            'max_cost_prediction_error': 0.05,     # Maximum error in cost prediction
            'min_net_return_accuracy': 0.7,        # Minimum accuracy in net return prediction
            'cost_efficiency_threshold': 5.0       # Minimum cost efficiency ratio
        }
        
        # Analyze cost-aware model performance
        cost_awareness_ratio = metrics.get('cost_awareness_ratio', 0.0)
        cost_feature_importance = metrics.get('cost_feature_importance_sum', 0.0)
        overall_r2 = metrics.get('test_r2', 0.0)
        
        # Calculate cost-specific performance metrics
        cost_prediction_accuracy = 1.0 - metrics.get('test_mse', 1.0)  # Simplified metric
        
        # Determine model health regarding cost optimization
        cost_health_scores = {
            'cost_awareness': min(1.0, cost_awareness_ratio / cost_performance_thresholds['min_cost_awareness_ratio']),
            'cost_feature_usage': min(1.0, cost_feature_importance / 0.3),  # Target 30% cost feature importance
            'overall_accuracy': min(1.0, overall_r2 / cost_performance_thresholds['min_cost_adjusted_r2']),
            'cost_prediction': cost_prediction_accuracy
        }
        
        # Overall cost performance score
        cost_performance_score = np.mean(list(cost_health_scores.values()))
        
        # Determine if retraining is needed
        retraining_triggers = {
            'low_cost_awareness': cost_awareness_ratio < cost_performance_thresholds['min_cost_awareness_ratio'],
            'poor_accuracy': overall_r2 < cost_performance_thresholds['min_cost_adjusted_r2'],
            'high_prediction_error': metrics.get('test_mse', 0) > cost_performance_thresholds['max_cost_prediction_error'],
            'low_overall_performance': cost_performance_score < 0.7
        }
        
        needs_retraining = any(retraining_triggers.values())
        
        # Cost optimization recommendations
        optimization_recommendations = []
        
        if cost_awareness_ratio < 0.1:
            optimization_recommendations.append("Increase focus on cost features in training data")
        
        if overall_r2 < 0.6:
            optimization_recommendations.append("Improve model architecture or increase training data")
        
        if cost_feature_importance < 0.2:
            optimization_recommendations.append("Enhance cost feature engineering")
        
        if needs_retraining:
            optimization_recommendations.append("Schedule automated model retraining")
        
        # Prepare evaluation result
        cost_evaluation_result = {
            'cost_performance_score': cost_performance_score,
            'cost_health_scores': cost_health_scores,
            'needs_retraining': needs_retraining,
            'retraining_triggers': retraining_triggers,
            'optimization_recommendations': optimization_recommendations,
            'evaluation_timestamp': datetime.now().isoformat(),
            
            # Performance benchmarks
            'cost_awareness_benchmark': cost_awareness_ratio >= cost_performance_thresholds['min_cost_awareness_ratio'],
            'accuracy_benchmark': overall_r2 >= cost_performance_thresholds['min_cost_adjusted_r2'],
            'efficiency_benchmark': cost_performance_score >= 0.8,
            
            # Next actions
            'recommended_retraining_priority': 'high' if cost_performance_score < 0.6 else 'medium' if cost_performance_score < 0.8 else 'low',
            'estimated_performance_improvement': max(0, 0.9 - cost_performance_score) if needs_retraining else 0.0
        }
        
        # Log cost performance metrics to W&B
        log_to_wandb({
            "cost_performance/overall_score": cost_performance_score,
            "cost_performance/cost_awareness_ratio": cost_awareness_ratio,
            "cost_performance/needs_retraining": needs_retraining,
            "cost_performance/retraining_priority": cost_evaluation_result['recommended_retraining_priority'],
            **{f"cost_health/{k}": v for k, v in cost_health_scores.items()},
            **{f"retraining_trigger/{k}": v for k, v in retraining_triggers.items()}
        })
        
        logger.info(f"Cost performance evaluation completed")
        logger.info(f"  Overall score: {cost_performance_score:.3f}")
        logger.info(f"  Cost awareness ratio: {cost_awareness_ratio:.3f}")
        logger.info(f"  Needs retraining: {needs_retraining}")
        logger.info(f"  Recommendations: {len(optimization_recommendations)}")
        
        return cost_evaluation_result
        
    except Exception as e:
        logger.error(f"Cost performance evaluation failed: {e}")
        log_to_wandb({"cost_evaluation_error": str(e)})
        
        # Return safe defaults
        return {
            'cost_performance_score': 0.5,
            'needs_retraining': True,
            'retraining_triggers': {'evaluation_error': True},
            'optimization_recommendations': ['Manual evaluation required due to error'],
            'recommended_retraining_priority': 'high',
            'error': str(e)
        }

@step 
def trigger_automated_retraining(
    cost_evaluation: Dict[str, Any],
    model_data: Dict[str, Any]
) -> str:
    """
    Trigger automated cost-aware model retraining based on performance evaluation.
    Task 3.1.1: Deploy automated cost-aware model retraining.
    """
    try:
        # Initialize W&B tracking for retraining
        wandb_available = init_wandb_tracking(
            "automated-retraining",
            ["automation", "retraining", "cost-optimization"]
        )
        
        needs_retraining = cost_evaluation.get('needs_retraining', False)
        retraining_priority = cost_evaluation.get('recommended_retraining_priority', 'low')
        
        if not needs_retraining:
            logger.info("Model performance is satisfactory, no retraining needed")
            log_to_wandb({
                "retraining/triggered": False,
                "retraining/reason": "performance_satisfactory"
            })
            return "no_retraining_needed"
        
        # Check retraining conditions
        current_time = datetime.now()
        
        # Implement retraining throttling (don't retrain too frequently)
        # In production, this would check last retraining time from database
        min_retraining_interval_hours = {
            'low': 168,    # 1 week
            'medium': 72,  # 3 days  
            'high': 24     # 1 day
        }
        
        interval_hours = min_retraining_interval_hours.get(retraining_priority, 72)
        
        # For demo purposes, always allow retraining
        # In production: check if interval_hours have passed since last retraining
        can_retrain = True
        
        if not can_retrain:
            logger.info(f"Retraining throttled: must wait {interval_hours} hours between retrainings")
            log_to_wandb({
                "retraining/triggered": False,
                "retraining/reason": "throttled",
                "retraining/next_eligible": (current_time + timedelta(hours=interval_hours)).isoformat()
            })
            return "retraining_throttled"
        
        # Trigger retraining
        logger.info(f"Triggering automated retraining (priority: {retraining_priority})")
        
        # Log retraining trigger
        log_to_wandb({
            "retraining/triggered": True,
            "retraining/priority": retraining_priority,
            "retraining/triggers": cost_evaluation.get('retraining_triggers', {}),
            "retraining/timestamp": current_time.isoformat(),
            "retraining/cost_performance_score": cost_evaluation.get('cost_performance_score', 0.0)
        })
        
        # In a production system, this would:
        # 1. Schedule a new pipeline run with fresh data
        # 2. Update model registry with retraining status
        # 3. Send notifications to monitoring systems
        # 4. Create model backup before retraining
        
        # For now, return status indicating retraining would be triggered
        retraining_status = {
            "status": "triggered",
            "priority": retraining_priority,
            "scheduled_time": current_time.isoformat(),
            "estimated_duration_minutes": 30,
            "triggers": list(cost_evaluation.get('retraining_triggers', {}).keys()),
            "expected_improvements": cost_evaluation.get('optimization_recommendations', [])
        }
        
        logger.info(f"Automated retraining triggered successfully")
        logger.info(f"  Priority: {retraining_priority}")
        logger.info(f"  Triggers: {len(retraining_status['triggers'])}")
        logger.info(f"  Expected improvements: {len(retraining_status['expected_improvements'])}")
        
        return f"retraining_triggered_{retraining_priority}"
        
    except Exception as e:
        logger.error(f"Automated retraining trigger failed: {e}")
        log_to_wandb({
            "retraining/triggered": False,
            "retraining/error": str(e)
        })
        return f"retraining_error: {str(e)}"

# Docker configuration for pipeline with W&B and visualization support
docker_settings = DockerSettings(
    requirements=[
        "scikit-learn==1.3.0",
        "pandas==2.0.3",
        "numpy==1.24.3",
        "joblib==1.3.2"
    ],
    environment={
        "WANDB_PROJECT": WANDB_PROJECT,
        "WANDB_ENTITY": WANDB_ENTITY
    }
)

@pipeline(
    settings={"docker": docker_settings},
    name="cost_aware_ensemble_optimization_pipeline"
)
def ensemble_training_pipeline(
    experiment_name: str = "cost-aware-ensemble-training",
    model_type: str = "RandomForestRegressor",
    enable_cost_optimization: bool = True
) -> str:
    """
    Cost-aware ensemble training pipeline with automated retraining.
    Task 3.1.1: Complete cost-aware reward functions in ZenML.
    
    Args:
        experiment_name: Name for the W&B experiment
        model_type: Type of model to train (currently supports RandomForestRegressor)
        enable_cost_optimization: Whether to enable cost-aware features and evaluation
    
    Returns:
        Model version string for the deployed model
    """
    # Load and prepare cost-aware data
    historical_data = load_historical_data()
    features, targets = prepare_training_features(historical_data)
    
    # Train cost-aware model with W&B tracking
    model_data = train_ensemble_model(features, targets)
    
    # Evaluate model with comprehensive metrics
    metrics = evaluate_model(model_data)
    
    # Cost-aware performance evaluation (Task 3.1.1)
    if enable_cost_optimization:
        cost_evaluation = evaluate_cost_performance(model_data, metrics)
        
        # Automated retraining decision (Task 3.1.1)
        retraining_status = trigger_automated_retraining(cost_evaluation, model_data)
    
    # Deploy model with artifact tracking
    model_version = deploy_model(model_data, metrics)
    
    return model_version

# Additional utility function for manual pipeline execution
def run_ensemble_pipeline_with_config(config: Dict[str, Any] = None) -> str:
    """
    Run the ensemble training pipeline with custom configuration.
    
    Args:
        config: Configuration dictionary with pipeline parameters
    
    Returns:
        Model version string
    """
    # Set default config with cost-aware enhancements
    default_config = {
        "experiment_name": f"cost-aware-ensemble-{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "model_type": "RandomForestRegressor",
        "project_name": WANDB_PROJECT,
        "tags": ["ensemble", "automated", "production", "cost-aware", "task-3-1-1"],
        "enable_cost_optimization": True
    }
    
    if config:
        default_config.update(config)
    
    # Initialize W&B tracking for pipeline coordination
    wandb_available = init_wandb_tracking(
        default_config["experiment_name"],
        default_config["tags"]
    )
    
    logger.info(f"Starting ensemble training pipeline: {default_config['experiment_name']}")
    if wandb_available:
        logger.info("W&B tracking enabled")
    
    # Run the cost-aware pipeline
    model_version = ensemble_training_pipeline(
        experiment_name=default_config["experiment_name"],
        model_type=default_config["model_type"],
        enable_cost_optimization=default_config["enable_cost_optimization"]
    )
    
    # Log pipeline completion
    log_to_wandb({
        "pipeline/status": "completed",
        "pipeline/model_version": model_version,
        "pipeline/completion_time": datetime.now().isoformat()
    })
    
    logger.info(f"Pipeline completed successfully. Model version: {model_version}")
    return model_version

if __name__ == "__main__":
    # Run the pipeline with or without W&B tracking
    try:
        # Try to run with W&B tracking if available
        try:
            import wandb
            has_wandb = True
        except ImportError:
            has_wandb = False
        
        if has_wandb and os.getenv("WANDB_API_KEY"):
            logger.info("Running cost-aware pipeline with W&B tracking")
            model_version = run_ensemble_pipeline_with_config({
                "experiment_name": f"cost-aware-ensemble-{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "tags": ["development", "testing", "automated", "cost-aware", "task-3-1-1"],
                "enable_cost_optimization": True
            })
        else:
            logger.info("Running cost-aware pipeline without W&B tracking")
            model_version = ensemble_training_pipeline(
                enable_cost_optimization=True
            )
            
        print(f"✅ Cost-aware ensemble pipeline completed successfully!")
        print(f"📊 Model version: {model_version}")
        print(f"💰 Cost-aware optimization: ENABLED")
        print(f"🔗 Model artifacts saved to: /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/models/")
        print(f"📈 Features: Enhanced with trading costs, slippage, and market impact")
        print(f"🤖 Automation: Cost-aware model retraining enabled")
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        print(f"❌ Pipeline failed: {e}")
        exit(1)