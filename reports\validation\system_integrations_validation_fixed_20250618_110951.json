{"timestamp": "2025-06-18T11:09:20.117052", "mcp_servers": {"supabase_mcp_available": {"success": true, "details": {"test_method": "direct_mcp_call"}, "error": null, "timestamp": "2025-06-18T11:09:20.127731"}}, "external_apis": {"binance_server_time": {"success": false, "details": {}, "error": "'BinanceExchangeClient' object has no attribute 'get_server_time'", "timestamp": "2025-06-18T11:09:24.096686"}, "binance_account_info": {"success": false, "details": {}, "error": "'BinanceExchangeClient' object has no attribute 'get_account_info'", "timestamp": "2025-06-18T11:09:24.097527"}, "binance_market_data": {"success": true, "details": {"symbol": "BTCUSDT", "price": "104559.70"}, "error": null, "timestamp": "2025-06-18T11:09:28.221698"}, "wandb_api_key": {"success": true, "details": {"key_length": 40}, "error": null, "timestamp": "2025-06-18T11:09:40.542237"}, "wandb_connection": {"success": true, "details": {"project": "crypto-app-validation", "mode": "offline"}, "error": null, "timestamp": "2025-06-18T11:09:48.020970"}}, "database_connections": {"supabase_basic_connection": {"success": false, "details": {}, "error": "'SupabaseService' object has no attribute 'execute_query'", "timestamp": "2025-06-18T11:09:51.131841"}, "supabase_tables_check": {"success": false, "details": {}, "error": "'SupabaseService' object has no attribute 'execute_query'", "timestamp": "2025-06-18T11:09:51.132359"}, "redis_basic_operations": {"success": false, "details": {}, "error": "'RedisService' object has no attribute 'set'", "timestamp": "2025-06-18T11:09:51.546010"}, "redis_list_keys": {"success": false, "details": {}, "error": "'RedisService' object has no attribute 'list_keys'", "timestamp": "2025-06-18T11:09:51.546493"}}, "websocket_connections": {}, "configuration_checks": {"env_var_binance_api_key": {"success": true, "details": {"length": 64, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:09:20.118833"}, "env_var_binance_api_secret": {"success": true, "details": {"length": 64, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:09:20.120177"}, "env_var_supabase_url": {"success": true, "details": {"length": 40, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:09:20.120657"}, "env_var_supabase_key": {"success": true, "details": {"length": 208, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:09:20.121164"}, "env_var_wandb_api_key": {"success": true, "details": {"length": 40, "has_value": true}, "error": null, "timestamp": "2025-06-18T11:09:20.121618"}, "file_access_.env": {"success": true, "details": {}, "error": null, "timestamp": "2025-06-18T11:09:20.123408"}, "file_access_app_config_settings.py": {"success": true, "details": {}, "error": null, "timestamp": "2025-06-18T11:09:20.125796"}, "file_access_requirements.txt": {"success": true, "details": {}, "error": null, "timestamp": "2025-06-18T11:09:20.127324"}}, "summary": {"total_tests": 18, "passed": 12, "failed": 6, "errors": ["external_apis - binance_server_time: 'BinanceExchangeClient' object has no attribute 'get_server_time'", "external_apis - binance_account_info: 'BinanceExchangeClient' object has no attribute 'get_account_info'", "database_connections - supabase_basic_connection: 'SupabaseService' object has no attribute 'execute_query'", "database_connections - supabase_tables_check: 'SupabaseService' object has no attribute 'execute_query'", "database_connections - redis_basic_operations: 'RedisService' object has no attribute 'set'", "database_connections - redis_list_keys: 'RedisService' object has no attribute 'list_keys'"]}}