#!/usr/bin/env python3
"""
Cost-Aware Reward Functions for Task 3.1.1
Enhanced reward calculation system that integrates trading costs, fees, slippage, and market impact.

Features:
- Cost-adjusted Sharpe ratio calculation
- Net return optimization (returns - costs)
- Risk-adjusted cost metrics
- Dynamic cost thresholds based on market conditions
- Multi-objective reward optimization
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import asyncio

from app.ml.training.reward_functions import RewardFunctions
from app.services.cost_calculator import CostCalculator, TotalTradingCost, OrderType

logger = logging.getLogger(__name__)

@dataclass
class CostAwareMetrics:
    """Cost-aware performance metrics"""
    gross_return: float
    trading_costs: float
    net_return: float
    cost_adjusted_sharpe: float
    cost_efficiency_ratio: float
    cost_to_profit_ratio: float
    net_profit_margin: float

@dataclass
class RewardComponents:
    """Breakdown of reward function components"""
    base_reward: float
    cost_penalty: float
    risk_adjustment: float
    efficiency_bonus: float
    final_reward: float
    
    # Metadata
    cost_breakdown: Dict[str, float]
    performance_metrics: CostAwareMetrics

class CostAwareRewardFunctions:
    """
    Enhanced reward functions that incorporate comprehensive trading costs.
    
    Features:
    - Integration with CostCalculator for real-time cost estimates
    - Multiple cost-aware reward formulations
    - Dynamic cost thresholds based on market conditions
    - Risk-adjusted cost penalties
    - Performance tracking and optimization
    """
    
    def __init__(
        self,
        cost_calculator: CostCalculator,
        config: Optional[Dict] = None
    ):
        self.cost_calculator = cost_calculator
        self.config = config or self._default_config()
        
        # Traditional reward functions for fallback
        self.base_rewards = RewardFunctions()
        
        # Performance tracking
        self.reward_history = []
        self.cost_history = []
        
        logger.info("CostAwareRewardFunctions initialized with cost integration")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for cost-aware rewards"""
        return {
            # Cost penalty weights
            "cost_penalty_weight": 1.0,           # Base cost penalty multiplier
            "slippage_penalty_weight": 1.5,       # Higher penalty for slippage
            "market_impact_penalty_weight": 2.0,  # Highest penalty for market impact
            "fee_penalty_weight": 0.5,            # Lower penalty for fixed fees
            
            # Reward function parameters
            "base_reward_weight": 0.7,            # Weight for base performance metrics
            "cost_weight": 0.3,                   # Weight for cost components
            "risk_free_rate": 0.02,               # Risk-free rate for Sharpe calculation
            "target_cost_ratio": 0.01,            # Target cost-to-return ratio (1%)
            
            # Dynamic thresholds
            "high_cost_threshold_bps": 50,        # 50 basis points high cost threshold
            "excessive_cost_threshold_bps": 100,  # 100 basis points excessive cost threshold
            "efficiency_bonus_threshold": 0.005,  # 0.5% efficiency bonus threshold
            
            # Risk adjustments
            "volatility_cost_multiplier": 1.2,    # Cost multiplier during high volatility
            "low_liquidity_cost_multiplier": 1.5, # Cost multiplier during low liquidity
            
            # Performance optimization
            "min_net_return_threshold": 0.0,      # Minimum net return for positive reward
            "cost_efficiency_target": 10.0,       # Target cost efficiency ratio
            
            # Reward scaling
            "max_reward_magnitude": 10.0,         # Maximum absolute reward value
            "reward_smoothing_factor": 0.1        # Exponential smoothing for reward stability
        }
    
    async def cost_adjusted_sharpe_ratio(
        self,
        returns: List[float],
        trade_sizes: List[float],
        symbols: List[str],
        order_types: Optional[List[OrderType]] = None,
        risk_free_rate: Optional[float] = None
    ) -> Tuple[float, RewardComponents]:
        """
        Calculate cost-adjusted Sharpe ratio incorporating trading costs.
        
        Args:
            returns: List of gross returns
            trade_sizes: List of trade sizes in USD
            symbols: List of trading symbols
            order_types: List of order types (defaults to MARKET)
            risk_free_rate: Risk-free rate (uses config default if None)
            
        Returns:
            Tuple of (cost_adjusted_sharpe, reward_components)
        """
        try:
            if len(returns) < 2:
                return 0.0, self._create_fallback_components("insufficient_data")
            
            risk_free_rate = risk_free_rate or self.config["risk_free_rate"]
            
            # Ensure all lists have same length
            min_length = min(len(returns), len(trade_sizes), len(symbols))
            returns = returns[:min_length]
            trade_sizes = trade_sizes[:min_length]
            symbols = symbols[:min_length]
            
            if order_types is None:
                order_types = [OrderType.MARKET] * min_length
            else:
                order_types = order_types[:min_length]
            
            # Calculate trading costs for each trade
            trading_costs = []
            for i in range(min_length):
                cost_result = await self.cost_calculator.calculate_total_trading_cost(
                    symbol=symbols[i],
                    trade_size_usd=abs(trade_sizes[i]),  # Use absolute value
                    order_type=order_types[i]
                )
                
                # Convert cost to percentage of trade value
                cost_percentage = cost_result.cost_percentage / 100  # Convert percentage to decimal
                trading_costs.append(cost_percentage)
            
            # Calculate net returns (gross returns - trading costs)
            net_returns = np.array(returns) - np.array(trading_costs)
            
            # Calculate cost-adjusted Sharpe ratio
            excess_net_returns = net_returns - risk_free_rate
            mean_excess_return = np.mean(excess_net_returns)
            std_excess_return = np.std(excess_net_returns)
            
            if std_excess_return == 0:
                cost_adjusted_sharpe = 0.0
            else:
                cost_adjusted_sharpe = mean_excess_return / std_excess_return
                # Annualize (assuming daily returns)
                cost_adjusted_sharpe *= np.sqrt(252)
            
            # Calculate reward components
            base_sharpe = self.base_rewards.sharpe_ratio(returns, risk_free_rate)
            cost_penalty = np.mean(trading_costs) * self.config["cost_penalty_weight"]
            
            # Risk adjustment based on cost variance
            cost_variance_penalty = np.std(trading_costs) * 0.5
            
            # Efficiency bonus for low-cost high-return combinations
            efficiency_ratio = np.mean(np.abs(returns)) / max(np.mean(trading_costs), 0.001)
            efficiency_bonus = max(0, (efficiency_ratio - self.config["cost_efficiency_target"]) * 0.1)
            
            # Final reward calculation
            final_reward = (
                base_sharpe * self.config["base_reward_weight"] -
                cost_penalty * self.config["cost_weight"] -
                cost_variance_penalty +
                efficiency_bonus
            )
            
            # Apply reward scaling
            final_reward = np.clip(final_reward, -self.config["max_reward_magnitude"], self.config["max_reward_magnitude"])
            
            # Create metrics
            cost_aware_metrics = CostAwareMetrics(
                gross_return=float(np.mean(returns)),
                trading_costs=float(np.mean(trading_costs)),
                net_return=float(np.mean(net_returns)),
                cost_adjusted_sharpe=float(cost_adjusted_sharpe),
                cost_efficiency_ratio=float(efficiency_ratio),
                cost_to_profit_ratio=float(np.mean(trading_costs) / max(np.mean(np.abs(returns)), 0.001)),
                net_profit_margin=float(np.mean(net_returns) / max(np.mean(np.abs(returns)), 0.001))
            )
            
            reward_components = RewardComponents(
                base_reward=float(base_sharpe),
                cost_penalty=float(cost_penalty),
                risk_adjustment=float(cost_variance_penalty),
                efficiency_bonus=float(efficiency_bonus),
                final_reward=float(final_reward),
                cost_breakdown={
                    "mean_cost_pct": float(np.mean(trading_costs)),
                    "cost_volatility": float(np.std(trading_costs)),
                    "max_cost_pct": float(np.max(trading_costs)),
                    "min_cost_pct": float(np.min(trading_costs))
                },
                performance_metrics=cost_aware_metrics
            )
            
            # Track performance
            self.reward_history.append(final_reward)
            self.cost_history.append(np.mean(trading_costs))
            
            return final_reward, reward_components
            
        except Exception as e:
            logger.error(f"Cost-adjusted Sharpe calculation failed: {e}")
            return 0.0, self._create_fallback_components("calculation_error")
    
    async def net_profit_optimization_reward(
        self,
        returns: List[float],
        trade_sizes: List[float],
        symbols: List[str],
        holding_periods: List[int],  # In hours
        order_types: Optional[List[OrderType]] = None
    ) -> Tuple[float, RewardComponents]:
        """
        Reward function focused on maximizing net profit after all costs.
        
        Args:
            returns: List of gross returns
            trade_sizes: List of trade sizes in USD
            symbols: List of trading symbols  
            holding_periods: List of holding periods in hours
            order_types: List of order types
            
        Returns:
            Tuple of (net_profit_reward, reward_components)
        """
        try:
            if len(returns) < 1:
                return 0.0, self._create_fallback_components("insufficient_data")
            
            min_length = min(len(returns), len(trade_sizes), len(symbols), len(holding_periods))
            returns = returns[:min_length]
            trade_sizes = trade_sizes[:min_length]
            symbols = symbols[:min_length]
            holding_periods = holding_periods[:min_length]
            
            if order_types is None:
                order_types = [OrderType.MARKET] * min_length
            else:
                order_types = order_types[:min_length]
            
            total_gross_profit = 0.0
            total_trading_costs = 0.0
            cost_breakdowns = []
            
            # Calculate costs for each trade
            for i in range(min_length):
                # Calculate trading cost
                cost_result = await self.cost_calculator.calculate_total_trading_cost(
                    symbol=symbols[i],
                    trade_size_usd=abs(trade_sizes[i]),
                    order_type=order_types[i]
                )
                
                # Add funding costs for holding periods > 8 hours
                additional_funding = 0.0
                if holding_periods[i] > 8:
                    funding_periods = (holding_periods[i] - 1) // 8  # Additional 8-hour periods
                    additional_funding = cost_result.funding_costs_usd * funding_periods
                
                total_cost_usd = cost_result.total_cost_usd + additional_funding
                
                # Calculate profit/loss in USD
                trade_value = abs(trade_sizes[i])
                gross_profit_usd = returns[i] * trade_value
                
                total_gross_profit += gross_profit_usd
                total_trading_costs += total_cost_usd
                
                cost_breakdowns.append({
                    "trade_size": trade_value,
                    "gross_profit": gross_profit_usd,
                    "trading_cost": total_cost_usd,
                    "cost_percentage": (total_cost_usd / trade_value) * 100,
                    "net_profit": gross_profit_usd - total_cost_usd
                })
            
            # Calculate net profit
            net_profit = total_gross_profit - total_trading_costs
            
            # Calculate profit efficiency
            gross_profit_margin = total_gross_profit / sum(abs(size) for size in trade_sizes) if trade_sizes else 0
            cost_ratio = total_trading_costs / sum(abs(size) for size in trade_sizes) if trade_sizes else 0
            net_profit_margin = net_profit / sum(abs(size) for size in trade_sizes) if trade_sizes else 0
            
            # Base reward from net profit margin
            base_reward = net_profit_margin * 100  # Convert to percentage
            
            # Penalty for high costs
            if cost_ratio > self.config["target_cost_ratio"]:
                cost_penalty = (cost_ratio - self.config["target_cost_ratio"]) * 50  # Penalty multiplier
            else:
                cost_penalty = 0.0
            
            # Efficiency bonus for good cost management
            efficiency_bonus = 0.0
            if cost_ratio < self.config["efficiency_bonus_threshold"] and net_profit > 0:
                efficiency_bonus = (self.config["efficiency_bonus_threshold"] - cost_ratio) * 20
            
            # Risk adjustment based on consistency
            profit_consistency = 1.0 - (np.std([breakdown["net_profit"] for breakdown in cost_breakdowns]) / 
                                       max(abs(net_profit), 1.0))
            risk_adjustment = profit_consistency * 0.5
            
            # Final reward
            final_reward = base_reward - cost_penalty + efficiency_bonus + risk_adjustment
            
            # Apply constraints
            if net_profit < self.config["min_net_return_threshold"]:
                final_reward = min(final_reward, 0.0)  # Cap reward at 0 for negative net profit
            
            final_reward = np.clip(final_reward, -self.config["max_reward_magnitude"], self.config["max_reward_magnitude"])
            
            # Create metrics
            cost_aware_metrics = CostAwareMetrics(
                gross_return=float(gross_profit_margin),
                trading_costs=float(cost_ratio),
                net_return=float(net_profit_margin),
                cost_adjusted_sharpe=0.0,  # Not applicable for this reward
                cost_efficiency_ratio=float(gross_profit_margin / max(cost_ratio, 0.001)),
                cost_to_profit_ratio=float(total_trading_costs / max(total_gross_profit, 0.001)),
                net_profit_margin=float(net_profit_margin)
            )
            
            reward_components = RewardComponents(
                base_reward=float(base_reward),
                cost_penalty=float(cost_penalty),
                risk_adjustment=float(risk_adjustment),
                efficiency_bonus=float(efficiency_bonus),
                final_reward=float(final_reward),
                cost_breakdown={
                    "total_gross_profit": total_gross_profit,
                    "total_trading_costs": total_trading_costs,
                    "net_profit": net_profit,
                    "cost_ratio": cost_ratio,
                    "trade_count": min_length
                },
                performance_metrics=cost_aware_metrics
            )
            
            return final_reward, reward_components
            
        except Exception as e:
            logger.error(f"Net profit optimization reward calculation failed: {e}")
            return 0.0, self._create_fallback_components("calculation_error")
    
    async def risk_adjusted_cost_efficiency_reward(
        self,
        returns: List[float],
        volatilities: List[float],
        trade_sizes: List[float],
        symbols: List[str],
        market_conditions: List[Dict[str, Any]],
        order_types: Optional[List[OrderType]] = None
    ) -> Tuple[float, RewardComponents]:
        """
        Risk-adjusted reward that considers cost efficiency under different market conditions.
        
        Args:
            returns: List of returns
            volatilities: List of volatility measurements
            trade_sizes: List of trade sizes
            symbols: List of symbols
            market_conditions: List of market condition dictionaries
            order_types: List of order types
            
        Returns:
            Tuple of (risk_adjusted_reward, reward_components)
        """
        try:
            if len(returns) < 2:
                return 0.0, self._create_fallback_components("insufficient_data")
            
            min_length = min(len(returns), len(volatilities), len(trade_sizes), len(symbols), len(market_conditions))
            returns = returns[:min_length]
            volatilities = volatilities[:min_length]
            trade_sizes = trade_sizes[:min_length]
            symbols = symbols[:min_length]
            market_conditions = market_conditions[:min_length]
            
            if order_types is None:
                order_types = [OrderType.MARKET] * min_length
            else:
                order_types = order_types[:min_length]
            
            risk_adjusted_returns = []
            cost_efficiency_scores = []
            
            for i in range(min_length):
                # Calculate trading costs with market condition adjustments
                cost_result = await self.cost_calculator.calculate_total_trading_cost(
                    symbol=symbols[i],
                    trade_size_usd=abs(trade_sizes[i]),
                    order_type=order_types[i]
                )
                
                # Market condition adjustments
                market_vol = market_conditions[i].get('volatility', volatilities[i])
                liquidity_score = market_conditions[i].get('liquidity_score', 1.0)
                
                # Adjust costs for market conditions
                adjusted_cost_pct = cost_result.cost_percentage / 100
                
                if market_vol > np.percentile(volatilities, 75):  # High volatility
                    adjusted_cost_pct *= self.config["volatility_cost_multiplier"]
                
                if liquidity_score < 0.5:  # Low liquidity
                    adjusted_cost_pct *= self.config["low_liquidity_cost_multiplier"]
                
                # Risk-adjusted return (return per unit of volatility)
                risk_adjusted_return = returns[i] / max(volatilities[i], 0.01)
                
                # Cost efficiency (risk-adjusted return per unit of cost)
                cost_efficiency = risk_adjusted_return / max(adjusted_cost_pct, 0.001)
                
                risk_adjusted_returns.append(risk_adjusted_return)
                cost_efficiency_scores.append(cost_efficiency)
            
            # Calculate aggregate metrics
            mean_risk_adj_return = np.mean(risk_adjusted_returns)
            mean_cost_efficiency = np.mean(cost_efficiency_scores)
            
            # Consistency bonuses
            return_consistency = 1.0 - (np.std(risk_adjusted_returns) / max(abs(mean_risk_adj_return), 0.1))
            cost_consistency = 1.0 - (np.std(cost_efficiency_scores) / max(mean_cost_efficiency, 0.1))
            
            # Base reward from risk-adjusted performance
            base_reward = mean_risk_adj_return * 10  # Scale up
            
            # Cost efficiency component
            efficiency_component = np.log1p(max(mean_cost_efficiency, 0)) * 2  # Log scaling for efficiency
            
            # Consistency bonuses
            consistency_bonus = (return_consistency + cost_consistency) * 0.5
            
            # Volatility penalty for excessive risk
            volatility_penalty = max(0, (np.mean(volatilities) - 0.3) * 5)  # Penalty above 30% volatility
            
            # Final reward
            final_reward = base_reward + efficiency_component + consistency_bonus - volatility_penalty
            
            final_reward = np.clip(final_reward, -self.config["max_reward_magnitude"], self.config["max_reward_magnitude"])
            
            # Create metrics
            cost_aware_metrics = CostAwareMetrics(
                gross_return=float(np.mean(returns)),
                trading_costs=0.0,  # Embedded in efficiency calculation
                net_return=float(mean_risk_adj_return),
                cost_adjusted_sharpe=0.0,  # Not directly applicable
                cost_efficiency_ratio=float(mean_cost_efficiency),
                cost_to_profit_ratio=0.0,  # Not directly applicable
                net_profit_margin=float(mean_risk_adj_return)
            )
            
            reward_components = RewardComponents(
                base_reward=float(base_reward),
                cost_penalty=float(volatility_penalty),
                risk_adjustment=float(consistency_bonus),
                efficiency_bonus=float(efficiency_component),
                final_reward=float(final_reward),
                cost_breakdown={
                    "mean_risk_adj_return": mean_risk_adj_return,
                    "mean_cost_efficiency": mean_cost_efficiency,
                    "return_consistency": return_consistency,
                    "cost_consistency": cost_consistency,
                    "mean_volatility": float(np.mean(volatilities))
                },
                performance_metrics=cost_aware_metrics
            )
            
            return final_reward, reward_components
            
        except Exception as e:
            logger.error(f"Risk-adjusted cost efficiency reward calculation failed: {e}")
            return 0.0, self._create_fallback_components("calculation_error")
    
    async def multi_objective_cost_reward(
        self,
        returns: List[float],
        trade_sizes: List[float],
        symbols: List[str],
        drawdowns: List[float],
        win_rates: List[float],
        order_types: Optional[List[OrderType]] = None,
        objective_weights: Optional[Dict[str, float]] = None
    ) -> Tuple[float, RewardComponents]:
        """
        Multi-objective reward function balancing returns, costs, risk, and consistency.
        
        Args:
            returns: List of returns
            trade_sizes: List of trade sizes
            symbols: List of symbols
            drawdowns: List of drawdown measurements
            win_rates: List of win rate measurements
            order_types: List of order types
            objective_weights: Custom weights for different objectives
            
        Returns:
            Tuple of (multi_objective_reward, reward_components)
        """
        try:
            if len(returns) < 1:
                return 0.0, self._create_fallback_components("insufficient_data")
            
            # Default objective weights
            default_weights = {
                "return_objective": 0.4,
                "cost_objective": 0.3,
                "risk_objective": 0.2,
                "consistency_objective": 0.1
            }
            
            weights = objective_weights or default_weights
            
            min_length = min(len(returns), len(trade_sizes), len(symbols), len(drawdowns), len(win_rates))
            returns = returns[:min_length]
            trade_sizes = trade_sizes[:min_length]
            symbols = symbols[:min_length]
            drawdowns = drawdowns[:min_length]
            win_rates = win_rates[:min_length]
            
            if order_types is None:
                order_types = [OrderType.MARKET] * min_length
            else:
                order_types = order_types[:min_length]
            
            # Calculate trading costs
            total_costs = []
            for i in range(min_length):
                cost_result = await self.cost_calculator.calculate_total_trading_cost(
                    symbol=symbols[i],
                    trade_size_usd=abs(trade_sizes[i]),
                    order_type=order_types[i]
                )
                total_costs.append(cost_result.cost_percentage / 100)
            
            # Objective 1: Return maximization
            mean_return = np.mean(returns)
            return_score = mean_return * 20  # Scale up returns
            
            # Objective 2: Cost minimization (invert costs for maximization)
            mean_cost = np.mean(total_costs)
            cost_score = max(0, (self.config["target_cost_ratio"] - mean_cost) * 100)
            
            # Objective 3: Risk minimization
            mean_drawdown = np.mean(drawdowns)
            risk_score = max(0, (0.1 - mean_drawdown) * 50)  # Reward low drawdowns
            
            # Objective 4: Consistency maximization
            mean_win_rate = np.mean(win_rates)
            return_stability = 1.0 - (np.std(returns) / max(abs(mean_return), 0.01))
            consistency_score = (mean_win_rate + return_stability) * 5
            
            # Weighted combination
            multi_objective_score = (
                return_score * weights["return_objective"] +
                cost_score * weights["cost_objective"] +
                risk_score * weights["risk_objective"] +
                consistency_score * weights["consistency_objective"]
            )
            
            # Bonus for balanced performance (no single objective dominates)
            objective_scores = [return_score, cost_score, risk_score, consistency_score]
            balance_score = 1.0 - (np.std(objective_scores) / max(np.mean(objective_scores), 0.1))
            balance_bonus = balance_score * 2
            
            final_reward = multi_objective_score + balance_bonus
            
            final_reward = np.clip(final_reward, -self.config["max_reward_magnitude"], self.config["max_reward_magnitude"])
            
            # Create metrics
            cost_aware_metrics = CostAwareMetrics(
                gross_return=float(mean_return),
                trading_costs=float(mean_cost),
                net_return=float(mean_return - mean_cost),
                cost_adjusted_sharpe=0.0,  # Would need more data to calculate
                cost_efficiency_ratio=float(mean_return / max(mean_cost, 0.001)),
                cost_to_profit_ratio=float(mean_cost / max(mean_return, 0.001)),
                net_profit_margin=float((mean_return - mean_cost) / max(mean_return, 0.001))
            )
            
            reward_components = RewardComponents(
                base_reward=float(multi_objective_score),
                cost_penalty=0.0,  # Embedded in cost objective
                risk_adjustment=float(risk_score),
                efficiency_bonus=float(balance_bonus),
                final_reward=float(final_reward),
                cost_breakdown={
                    "return_score": return_score,
                    "cost_score": cost_score,
                    "risk_score": risk_score,
                    "consistency_score": consistency_score,
                    "balance_score": balance_score,
                    "objective_weights": weights
                },
                performance_metrics=cost_aware_metrics
            )
            
            return final_reward, reward_components
            
        except Exception as e:
            logger.error(f"Multi-objective cost reward calculation failed: {e}")
            return 0.0, self._create_fallback_components("calculation_error")
    
    def _create_fallback_components(self, error_type: str) -> RewardComponents:
        """Create fallback reward components when calculation fails"""
        
        return RewardComponents(
            base_reward=0.0,
            cost_penalty=0.0,
            risk_adjustment=0.0,
            efficiency_bonus=0.0,
            final_reward=0.0,
            cost_breakdown={"error": error_type},
            performance_metrics=CostAwareMetrics(
                gross_return=0.0,
                trading_costs=0.0,
                net_return=0.0,
                cost_adjusted_sharpe=0.0,
                cost_efficiency_ratio=0.0,
                cost_to_profit_ratio=0.0,
                net_profit_margin=0.0
            )
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of reward calculations"""
        
        if not self.reward_history:
            return {"status": "no_data", "message": "No reward calculations performed yet"}
        
        return {
            "total_calculations": len(self.reward_history),
            "mean_reward": float(np.mean(self.reward_history)),
            "reward_volatility": float(np.std(self.reward_history)),
            "best_reward": float(np.max(self.reward_history)),
            "worst_reward": float(np.min(self.reward_history)),
            "mean_cost": float(np.mean(self.cost_history)) if self.cost_history else 0.0,
            "cost_trend": "increasing" if len(self.cost_history) > 1 and self.cost_history[-1] > self.cost_history[0] else "stable",
            "reward_trend": "improving" if len(self.reward_history) > 1 and self.reward_history[-1] > self.reward_history[0] else "stable"
        }
    
    async def get_cost_aware_reward_function(self, function_name: str) -> callable:
        """
        Get a cost-aware reward function by name.
        
        Args:
            function_name: Name of the reward function
            
        Returns:
            Async callable reward function
        """
        reward_functions = {
            'cost_adjusted_sharpe': self.cost_adjusted_sharpe_ratio,
            'net_profit_optimization': self.net_profit_optimization_reward,
            'risk_adjusted_cost_efficiency': self.risk_adjusted_cost_efficiency_reward,
            'multi_objective_cost': self.multi_objective_cost_reward
        }
        
        if function_name in reward_functions:
            return reward_functions[function_name]
        else:
            logger.warning(f"Unknown cost-aware reward function: {function_name}")
            # Return a wrapper for the base sharpe ratio
            async def fallback_sharpe(*args, **kwargs):
                returns = args[0] if args else []
                base_reward = self.base_rewards.sharpe_ratio(returns)
                components = self._create_fallback_components("fallback_function")
                components.base_reward = base_reward
                components.final_reward = base_reward
                return base_reward, components
            
            return fallback_sharpe

# Factory function for easy initialization
async def create_cost_aware_reward_functions(
    redis_url: str,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    config: Optional[Dict] = None
) -> CostAwareRewardFunctions:
    """Factory function to create cost-aware reward functions with dependencies"""
    
    from app.services.cost_calculator import create_cost_calculator
    
    cost_calculator = await create_cost_calculator(
        redis_url, supabase_url, supabase_key
    )
    
    return CostAwareRewardFunctions(
        cost_calculator=cost_calculator,
        config=config
    )

if __name__ == "__main__":
    async def test_cost_aware_rewards():
        """Test the cost-aware reward functions"""
        
        # Initialize cost-aware rewards
        rewards = await create_cost_aware_reward_functions("redis://localhost:6379")
        
        # Test data
        returns = [0.02, 0.015, -0.01, 0.025, 0.01]
        trade_sizes = [10000, 15000, 8000, 12000, 9000]
        symbols = ["BTC", "ETH", "BTC", "ETH", "BTC"]
        
        # Test cost-adjusted Sharpe ratio
        sharpe_reward, components = await rewards.cost_adjusted_sharpe_ratio(
            returns, trade_sizes, symbols
        )
        
        print(f"Cost-Adjusted Sharpe Ratio Test:")
        print(f"  Reward: {sharpe_reward:.4f}")
        print(f"  Base Reward: {components.base_reward:.4f}")
        print(f"  Cost Penalty: {components.cost_penalty:.4f}")
        print(f"  Efficiency Bonus: {components.efficiency_bonus:.4f}")
        print(f"  Net Return: {components.performance_metrics.net_return:.4f}")
        print(f"  Cost Efficiency: {components.performance_metrics.cost_efficiency_ratio:.2f}")
        
        # Test net profit optimization
        holding_periods = [24, 12, 8, 16, 6]  # Hours
        profit_reward, profit_components = await rewards.net_profit_optimization_reward(
            returns, trade_sizes, symbols, holding_periods
        )
        
        print(f"\nNet Profit Optimization Test:")
        print(f"  Reward: {profit_reward:.4f}")
        print(f"  Net Profit Margin: {profit_components.performance_metrics.net_profit_margin:.4f}")
        print(f"  Cost-to-Profit Ratio: {profit_components.performance_metrics.cost_to_profit_ratio:.4f}")
        
        # Performance summary
        summary = rewards.get_performance_summary()
        print(f"\nPerformance Summary: {summary}")
    
    asyncio.run(test_cost_aware_rewards())