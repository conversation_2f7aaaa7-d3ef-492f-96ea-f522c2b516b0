{"test_configuration": {"description": "Configuration for comprehensive ML Pipeline integration test", "created": "2025-06-17T00:03:00.566Z", "version": "1.0.0"}, "test_parameters": {"test_duration_seconds": 300, "prediction_interval_seconds": 5, "performance_check_interval": 30, "websocket_test_duration": 60, "training_timeout_seconds": 180}, "performance_thresholds": {"expected_prediction_latency_ms": 500, "min_model_accuracy": 0.7, "max_memory_increase_mb": 100, "min_concurrent_success_rate": 0.95, "min_throughput_predictions_per_second": 10}, "test_data": {"test_symbols": ["BTCUSDT", "ETHUSDT"], "test_amounts": {"BTCUSDT": 0.001, "ETHUSDT": 0.01}, "market_condition_ranges": {"volatility": {"min": 0.01, "max": 0.05}, "volume": {"min": 500000, "max": 2000000}, "rsi": {"min": 20, "max": 80}, "macd": {"min": -0.02, "max": 0.02}, "price_change": {"min": -0.05, "max": 0.05}}}, "ml_services": {"wandb": {"project_name": "ml-pipeline-test", "enable_logging": true, "experiment_tracking": true}, "mlflow": {"tracking_uri": "http://localhost:5000", "experiment_name": "ml-pipeline-integration-test", "model_registry_enabled": true}, "redis": {"url": "redis://localhost:6379", "cache_ttl_seconds": 300, "enable_caching": true}}, "testing_modes": {"component_isolation": true, "integration_testing": true, "dashboard_testing": true, "end_to_end_workflow": true, "error_handling": true, "performance_benchmarks": true, "websocket_testing": true, "session_reporting": true}, "mock_services": {"use_mocks_when_unavailable": true, "mock_exchange_client": true, "mock_database_when_offline": true, "simulate_network_failures": false}, "reporting": {"generate_detailed_report": true, "save_test_artifacts": true, "include_performance_graphs": false, "export_to_wandb": false}, "validation_criteria": {"ml_model_loading": "Model must load successfully or use fallback", "prediction_accuracy": "Predictions must return valid weights that sum to 1.0", "api_endpoints": "All ML API endpoints must respond within 5 seconds", "websocket_updates": "WebSocket updates must be delivered in real-time", "error_recovery": "System must gracefully handle all error scenarios", "performance_sla": "Must meet all performance threshold requirements"}}