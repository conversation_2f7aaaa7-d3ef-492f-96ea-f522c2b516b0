#!/usr/bin/env python3
"""
Test Testnet API Connection
Validate that we can connect to Binance testnet and get account info.
"""

import os
import asyncio
from dotenv import load_dotenv
from binance.client import Client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_testnet_connection():
    """Test Binance testnet API connection"""
    print("=" * 60)
    print("TESTING BINANCE TESTNET CONNECTION")
    print("=" * 60)
    
    try:
        # Load environment variables
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        use_testnet = os.getenv('USE_TESTNET', 'True').lower() == 'true'
        
        if not api_key or not api_secret:
            print("❌ API credentials not found in environment")
            return False
        
        print(f"🔧 Using testnet: {use_testnet}")
        print(f"🔑 API Key: {api_key[:8]}...")
        
        # Initialize client with additional configuration
        print(f"🔧 Initializing Binance client...")
        client = Client(
            api_key, 
            api_secret, 
            testnet=use_testnet,
            requests_params={'timeout': 30}
        )
        
        # Check server time sync
        server_time = client.get_server_time()
        print(f"🕐 Server time sync: {server_time['serverTime']}")
        
        # Test ping first
        ping_result = client.ping()
        print(f"✅ Ping test: SUCCESS")
        
        # Test exchange info
        exchange_info = client.get_exchange_info()
        print(f"📊 Exchange info: {len(exchange_info['symbols'])} symbols available")
        
        # Test 1: Account information
        print("\n🔍 Testing account access...")
        account = client.get_account()
        
        print("✅ Account Access: SUCCESS")
        print(f"   Account Type: {account.get('accountType', 'N/A')}")
        print(f"   Can Trade: {account.get('canTrade', False)}")
        print(f"   Can Withdraw: {account.get('canWithdraw', False)}")
        print(f"   Can Deposit: {account.get('canDeposit', False)}")
        
        # Test 2: Account balances
        print("\n💰 Account Balances:")
        balances = [b for b in account['balances'] if float(b['free']) > 0 or float(b['locked']) > 0]
        print(f"   Active Balances: {len(balances)}")
        
        for balance in balances[:5]:  # Show first 5 non-zero balances
            free = float(balance['free'])
            locked = float(balance['locked'])
            total = free + locked
            print(f"   {balance['asset']}: {total:.8f} (free: {free:.8f}, locked: {locked:.8f})")
        
        # Test 3: Market data access
        print("\n📊 Testing market data access...")
        ticker = client.get_symbol_ticker(symbol='BTCUSDT')
        btc_price = float(ticker['price'])
        print(f"✅ Market Data Access: SUCCESS")
        print(f"   BTC/USDT Price: ${btc_price:,.2f}")
        
        # Test 4: Order book
        depth = client.get_order_book(symbol='BTCUSDT', limit=5)
        print(f"   Order Book Depth: {len(depth['bids'])} bids, {len(depth['asks'])} asks")
        
        # Test 5: Kline data
        klines = client.get_klines(symbol='BTCUSDT', interval='1m', limit=5)
        print(f"   Kline Data: {len(klines)} candles retrieved")
        
        print("\n" + "=" * 60)
        print("✅ TESTNET CONNECTION FULLY VALIDATED")
        print("Ready for live trading operations!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Testnet connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = asyncio.run(test_testnet_connection())
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)