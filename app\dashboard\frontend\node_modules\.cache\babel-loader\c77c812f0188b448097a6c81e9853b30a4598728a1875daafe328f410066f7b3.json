{"ast": null, "code": "/**\n * WebSocket service for real-time trade updates\n */\n\nimport { WS_URL } from '../config';\n\n// Define event types\nexport let WebSocketEventType = /*#__PURE__*/function (WebSocketEventType) {\n  WebSocketEventType[\"TRADE_UPDATE\"] = \"trade_update\";\n  WebSocketEventType[\"SYSTEM_STATUS\"] = \"system_status\";\n  WebSocketEventType[\"ERROR\"] = \"error\";\n  // Auto Trading Events\n  WebSocketEventType[\"AUTO_TRADING_SESSION_STARTED\"] = \"auto_trading_session_started\";\n  WebSocketEventType[\"AUTO_TRADING_SESSION_STOPPED\"] = \"auto_trading_session_stopped\";\n  WebSocketEventType[\"AUTO_TRADING_SESSION_PAUSED\"] = \"auto_trading_session_paused\";\n  WebSocketEventType[\"AUTO_TRADING_SESSION_RESUMED\"] = \"auto_trading_session_resumed\";\n  WebSocketEventType[\"AUTO_TRADING_PERFORMANCE_UPDATE\"] = \"auto_trading_performance_update\";\n  WebSocketEventType[\"AUTO_TRADING_ALERT\"] = \"auto_trading_alert\";\n  WebSocketEventType[\"AUTO_TRADING_EMERGENCY_STOP\"] = \"auto_trading_emergency_stop\";\n  WebSocketEventType[\"AUTO_TRADING_CONNECTION_ESTABLISHED\"] = \"auto_trading_connection_established\";\n  WebSocketEventType[\"AUTO_TRADING_INITIAL_STATUS\"] = \"auto_trading_initial_status\";\n  // Session Reports Events\n  WebSocketEventType[\"LIVE_SESSION_METRICS\"] = \"live_session_metrics\";\n  WebSocketEventType[\"SESSION_RISK_ALERT\"] = \"session_risk_alert\";\n  WebSocketEventType[\"SESSION_REPORT_UPDATE\"] = \"session_report_update\";\n  return WebSocketEventType;\n}({});\n\n// Event type interfaces\n\n// Define event data interfaces\n\n// Define event handler types\n\n// WebSocket service class\nexport class WebSocketService {\n  constructor() {\n    this.socket = null;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000;\n    // Start with 1 second delay\n    this.reconnectTimer = null;\n    this.isConnecting = false;\n    this.isConnected = false;\n    this.eventHandlers = {};\n  }\n  /**\n   * Connect to the WebSocket server\n   */\n  connect() {\n    if (this.socket || this.isConnecting) {\n      return;\n    }\n    this.isConnecting = true;\n    try {\n      // Use the WebSocket URL from config\n      const wsUrl = `${WS_URL}/trades`;\n      console.log(`Connecting to WebSocket at ${wsUrl}`);\n      this.socket = new WebSocket(wsUrl);\n      this.socket.onopen = this.handleOpen.bind(this);\n      this.socket.onmessage = this.handleMessage.bind(this);\n      this.socket.onclose = this.handleClose.bind(this);\n      this.socket.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      this.isConnecting = false;\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Disconnect from the WebSocket server\n   */\n  disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n    this.isConnected = false;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n\n  /**\n   * Add an event handler\n   */\n  on(eventType, handler) {\n    if (!this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType] = [];\n    }\n    this.eventHandlers[eventType].push(handler);\n  }\n\n  /**\n   * Remove an event handler\n   */\n  off(eventType, handler) {\n    if (!this.eventHandlers[eventType]) {\n      return;\n    }\n    const index = this.eventHandlers[eventType].indexOf(handler);\n    if (index !== -1) {\n      this.eventHandlers[eventType].splice(index, 1);\n    }\n  }\n\n  /**\n   * Handle WebSocket open event\n   */\n  handleOpen() {\n    console.log('WebSocket connection established');\n    this.isConnected = true;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n\n    // Notify listeners of system status\n    const statusEvent = {\n      status: 'online',\n      message: 'Connected to server'\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n  }\n\n  /**\n   * Handle WebSocket message event\n   */\n  handleMessage(event) {\n    try {\n      const data = JSON.parse(event.data);\n      if (data.type === 'trade_update') {\n        this.notifyHandlers(WebSocketEventType.TRADE_UPDATE, data.data);\n      } else if (data.type === 'system_status') {\n        this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, data.data);\n      } else if (data.type === 'error') {\n        this.notifyHandlers(WebSocketEventType.ERROR, data.data);\n      } else {\n        console.warn('Unknown WebSocket message type:', data.type);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error, event.data);\n    }\n  }\n\n  /**\n   * Handle WebSocket close event\n   */\n  handleClose(event) {\n    console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);\n    this.socket = null;\n    this.isConnected = false;\n    this.isConnecting = false;\n\n    // Notify listeners of system status\n    const statusEvent = {\n      status: 'offline',\n      message: `Disconnected from server: ${event.reason || 'Connection closed'}`\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n    // Attempt to reconnect if the close was unexpected\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Handle WebSocket error event\n   */\n  handleError(event) {\n    console.error('WebSocket error:', event);\n\n    // Notify listeners of error\n    const errorEvent = {\n      code: 'connection_error',\n      message: 'WebSocket connection error'\n    };\n    this.notifyHandlers(WebSocketEventType.ERROR, errorEvent);\n  }\n\n  /**\n   * Schedule a reconnection attempt\n   */\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log('Maximum reconnection attempts reached');\n\n      // Notify listeners of system status\n      const statusEvent = {\n        status: 'offline',\n        message: 'Failed to reconnect to server after multiple attempts'\n      };\n      this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n      return;\n    }\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n    }\n    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);\n    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);\n    this.reconnectTimer = window.setTimeout(() => {\n      this.reconnectAttempts++;\n      this.connect();\n    }, delay);\n  }\n\n  /**\n   * Notify all handlers of an event\n   */\n  notifyHandlers(eventType, data) {\n    if (!this.eventHandlers[eventType]) {\n      return;\n    }\n    for (const handler of this.eventHandlers[eventType]) {\n      try {\n        handler(data);\n      } catch (error) {\n        console.error(`Error in ${eventType} handler:`, error);\n      }\n    }\n  }\n}\n\n// Create a singleton instance\nexport const websocketService = new WebSocketService();\n\n// Export the singleton instance as default\nexport default websocketService;", "map": {"version": 3, "names": ["WS_URL", "WebSocketEventType", "WebSocketService", "constructor", "socket", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "reconnectTimer", "isConnecting", "isConnected", "eventHandlers", "connect", "wsUrl", "console", "log", "WebSocket", "onopen", "handleOpen", "bind", "onmessage", "handleMessage", "onclose", "handleClose", "onerror", "handleError", "error", "scheduleReconnect", "disconnect", "close", "window", "clearTimeout", "on", "eventType", "handler", "push", "off", "index", "indexOf", "splice", "statusEvent", "status", "message", "notifyHandlers", "SYSTEM_STATUS", "event", "data", "JSON", "parse", "type", "TRADE_UPDATE", "ERROR", "warn", "code", "reason", "errorEvent", "delay", "Math", "pow", "setTimeout", "websocketService"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/websocket.ts"], "sourcesContent": ["/**\n * WebSocket service for real-time trade updates\n */\n\nimport { WS_URL } from '../config';\n\n// Define event types\nexport enum WebSocketEventType {\n  TRADE_UPDATE = 'trade_update',\n  SYSTEM_STATUS = 'system_status',\n  ERROR = 'error',\n  // Auto Trading Events\n  AUTO_TRADING_SESSION_STARTED = 'auto_trading_session_started',\n  AUTO_TRADING_SESSION_STOPPED = 'auto_trading_session_stopped',\n  AUTO_TRADING_SESSION_PAUSED = 'auto_trading_session_paused',\n  AUTO_TRADING_SESSION_RESUMED = 'auto_trading_session_resumed',\n  AUTO_TRADING_PERFORMANCE_UPDATE = 'auto_trading_performance_update',\n  AUTO_TRADING_ALERT = 'auto_trading_alert',\n  AUTO_TRADING_EMERGENCY_STOP = 'auto_trading_emergency_stop',\n  AUTO_TRADING_CONNECTION_ESTABLISHED = 'auto_trading_connection_established',\n  AUTO_TRADING_INITIAL_STATUS = 'auto_trading_initial_status',\n  // Session Reports Events\n  LIVE_SESSION_METRICS = 'live_session_metrics',\n  SESSION_RISK_ALERT = 'session_risk_alert',\n  SESSION_REPORT_UPDATE = 'session_report_update',\n}\n\n// Event type interfaces\nexport interface LiveSessionMetricsEvent {\n  session_id: string;\n  live_metrics: {\n    current_pnl: number;\n    unrealized_pnl: number;\n    win_rate: number;\n    active_trades: number;\n    sharpe_ratio: number;\n  };\n  active_alerts?: Array<{\n    level: string;\n    message: string;\n    timestamp: string;\n  }>;\n}\n\nexport interface SessionRiskAlertEvent {\n  alert: {\n    level: string;\n    message: string;\n  };\n  timestamp: string;\n}\n\nexport interface SessionReportUpdateEvent {\n  session_id: string;\n  status: string;\n}\n\n// Define event data interfaces\nexport interface TradeUpdateEvent {\n  trade_id: string;\n  status: string;\n  symbol: string;\n  entry_side: string;\n  entry_price: number | null;\n  entry_qty: number | null;\n  sl_price: number | null;\n  tp_price: number | null;\n  timestamp: string;\n  exit_price?: number | null;\n  current_price?: number;\n}\n\nexport interface SystemStatusEvent {\n  status: 'online' | 'offline' | 'degraded';\n  message: string;\n}\n\nexport interface ErrorEvent {\n  code: string;\n  message: string;\n}\n\n// Define event handler types\nexport type TradeUpdateHandler = (data: TradeUpdateEvent) => void;\nexport type SystemStatusHandler = (data: SystemStatusEvent) => void;\nexport type ErrorHandler = (data: ErrorEvent) => void;\nexport type LiveSessionMetricsHandler = (data: LiveSessionMetricsEvent) => void;\nexport type SessionRiskAlertHandler = (data: SessionRiskAlertEvent) => void;\nexport type SessionReportUpdateHandler = (data: SessionReportUpdateEvent) => void;\n\n// WebSocket service class\nexport class WebSocketService {\n  private socket: WebSocket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000; // Start with 1 second delay\n  private reconnectTimer: number | null = null;\n  private isConnecting = false;\n  private isConnected = false;\n  private eventHandlers: Record<string, Function[]> = {};\n\n  /**\n   * Connect to the WebSocket server\n   */\n  public connect(): void {\n    if (this.socket || this.isConnecting) {\n      return;\n    }\n\n    this.isConnecting = true;\n\n    try {\n      // Use the WebSocket URL from config\n      const wsUrl = `${WS_URL}/trades`;\n\n      console.log(`Connecting to WebSocket at ${wsUrl}`);\n      this.socket = new WebSocket(wsUrl);\n\n      this.socket.onopen = this.handleOpen.bind(this);\n      this.socket.onmessage = this.handleMessage.bind(this);\n      this.socket.onclose = this.handleClose.bind(this);\n      this.socket.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      this.isConnecting = false;\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Disconnect from the WebSocket server\n   */\n  public disconnect(): void {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    this.isConnected = false;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n\n  /**\n   * Add an event handler\n   */\n  public on(eventType: string, handler: Function): void {\n    if (!this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType] = [];\n    }\n    this.eventHandlers[eventType].push(handler);\n  }\n\n  /**\n   * Remove an event handler\n   */\n  public off(eventType: string, handler: Function): void {\n    if (!this.eventHandlers[eventType]) {\n      return;\n    }\n    const index = this.eventHandlers[eventType].indexOf(handler);\n    if (index !== -1) {\n      this.eventHandlers[eventType].splice(index, 1);\n    }\n  }\n\n  /**\n   * Handle WebSocket open event\n   */\n  private handleOpen(): void {\n    console.log('WebSocket connection established');\n    this.isConnected = true;\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n\n    // Notify listeners of system status\n    const statusEvent: SystemStatusEvent = {\n      status: 'online',\n      message: 'Connected to server',\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n  }\n\n  /**\n   * Handle WebSocket message event\n   */\n  private handleMessage(event: MessageEvent): void {\n    try {\n      const data = JSON.parse(event.data);\n\n      if (data.type === 'trade_update') {\n        this.notifyHandlers(WebSocketEventType.TRADE_UPDATE, data.data);\n      } else if (data.type === 'system_status') {\n        this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, data.data);\n      } else if (data.type === 'error') {\n        this.notifyHandlers(WebSocketEventType.ERROR, data.data);\n      } else {\n        console.warn('Unknown WebSocket message type:', data.type);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error, event.data);\n    }\n  }\n\n  /**\n   * Handle WebSocket close event\n   */\n  private handleClose(event: CloseEvent): void {\n    console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);\n    this.socket = null;\n    this.isConnected = false;\n    this.isConnecting = false;\n\n    // Notify listeners of system status\n    const statusEvent: SystemStatusEvent = {\n      status: 'offline',\n      message: `Disconnected from server: ${event.reason || 'Connection closed'}`,\n    };\n    this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n    // Attempt to reconnect if the close was unexpected\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n\n  /**\n   * Handle WebSocket error event\n   */\n  private handleError(event: Event): void {\n    console.error('WebSocket error:', event);\n\n    // Notify listeners of error\n    const errorEvent: ErrorEvent = {\n      code: 'connection_error',\n      message: 'WebSocket connection error',\n    };\n    this.notifyHandlers(WebSocketEventType.ERROR, errorEvent);\n  }\n\n  /**\n   * Schedule a reconnection attempt\n   */\n  private scheduleReconnect(): void {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log('Maximum reconnection attempts reached');\n\n      // Notify listeners of system status\n      const statusEvent: SystemStatusEvent = {\n        status: 'offline',\n        message: 'Failed to reconnect to server after multiple attempts',\n      };\n      this.notifyHandlers(WebSocketEventType.SYSTEM_STATUS, statusEvent);\n\n      return;\n    }\n\n    if (this.reconnectTimer !== null) {\n      window.clearTimeout(this.reconnectTimer);\n    }\n\n    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);\n    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);\n\n    this.reconnectTimer = window.setTimeout(() => {\n      this.reconnectAttempts++;\n      this.connect();\n    }, delay);\n  }\n\n  /**\n   * Notify all handlers of an event\n   */\n  private notifyHandlers(eventType: string, data: any): void {\n    if (!this.eventHandlers[eventType]) {\n      return;\n    }\n    for (const handler of this.eventHandlers[eventType]) {\n      try {\n        handler(data);\n      } catch (error) {\n        console.error(`Error in ${eventType} handler:`, error);\n      }\n    }\n  }\n}\n\n// Create a singleton instance\nexport const websocketService = new WebSocketService();\n\n// Export the singleton instance as default\nexport default websocketService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,MAAM,QAAQ,WAAW;;AAElC;AACA,WAAYC,kBAAkB,0BAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAI5B;EAJUA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAc5B;EAdUA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAA,OAAlBA,kBAAkB;AAAA;;AAoB9B;;AA8BA;;AAyBA;;AAQA;AACA,OAAO,MAAMC,gBAAgB,CAAC;EAAAC,YAAA;IAAA,KACpBC,MAAM,GAAqB,IAAI;IAAA,KAC/BC,iBAAiB,GAAG,CAAC;IAAA,KACrBC,oBAAoB,GAAG,CAAC;IAAA,KACxBC,cAAc,GAAG,IAAI;IAAE;IAAA,KACvBC,cAAc,GAAkB,IAAI;IAAA,KACpCC,YAAY,GAAG,KAAK;IAAA,KACpBC,WAAW,GAAG,KAAK;IAAA,KACnBC,aAAa,GAA+B,CAAC,CAAC;EAAA;EAEtD;AACF;AACA;EACSC,OAAOA,CAAA,EAAS;IACrB,IAAI,IAAI,CAACR,MAAM,IAAI,IAAI,CAACK,YAAY,EAAE;MACpC;IACF;IAEA,IAAI,CAACA,YAAY,GAAG,IAAI;IAExB,IAAI;MACF;MACA,MAAMI,KAAK,GAAG,GAAGb,MAAM,SAAS;MAEhCc,OAAO,CAACC,GAAG,CAAC,8BAA8BF,KAAK,EAAE,CAAC;MAClD,IAAI,CAACT,MAAM,GAAG,IAAIY,SAAS,CAACH,KAAK,CAAC;MAElC,IAAI,CAACT,MAAM,CAACa,MAAM,GAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI,CAACf,MAAM,CAACgB,SAAS,GAAG,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC;MACrD,IAAI,CAACf,MAAM,CAACkB,OAAO,GAAG,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC;MACjD,IAAI,CAACf,MAAM,CAACoB,OAAO,GAAG,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,IAAI,CAAC;IACnD,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACjB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACkB,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACSC,UAAUA,CAAA,EAAS;IACxB,IAAI,IAAI,CAACxB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACyB,KAAK,CAAC,CAAC;MACnB,IAAI,CAACzB,MAAM,GAAG,IAAI;IACpB;IAEA,IAAI,IAAI,CAACI,cAAc,KAAK,IAAI,EAAE;MAChCsB,MAAM,CAACC,YAAY,CAAC,IAAI,CAACvB,cAAc,CAAC;MACxC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC5B;IAEA,IAAI,CAACE,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACJ,iBAAiB,GAAG,CAAC;EAC5B;;EAEA;AACF;AACA;EACS2B,EAAEA,CAACC,SAAiB,EAAEC,OAAiB,EAAQ;IACpD,IAAI,CAAC,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,EAAE;MAClC,IAAI,CAACtB,aAAa,CAACsB,SAAS,CAAC,GAAG,EAAE;IACpC;IACA,IAAI,CAACtB,aAAa,CAACsB,SAAS,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC;EAC7C;;EAEA;AACF;AACA;EACSE,GAAGA,CAACH,SAAiB,EAAEC,OAAiB,EAAQ;IACrD,IAAI,CAAC,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,EAAE;MAClC;IACF;IACA,MAAMI,KAAK,GAAG,IAAI,CAAC1B,aAAa,CAACsB,SAAS,CAAC,CAACK,OAAO,CAACJ,OAAO,CAAC;IAC5D,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC1B,aAAa,CAACsB,SAAS,CAAC,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACUnB,UAAUA,CAAA,EAAS;IACzBJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACJ,iBAAiB,GAAG,CAAC;;IAE1B;IACA,MAAMmC,WAA8B,GAAG;MACrCC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,IAAI,CAACC,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,EAAEJ,WAAW,CAAC;EACpE;;EAEA;AACF;AACA;EACUnB,aAAaA,CAACwB,KAAmB,EAAQ;IAC/C,IAAI;MACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MAEnC,IAAIA,IAAI,CAACG,IAAI,KAAK,cAAc,EAAE;QAChC,IAAI,CAACN,cAAc,CAAC1C,kBAAkB,CAACiD,YAAY,EAAEJ,IAAI,CAACA,IAAI,CAAC;MACjE,CAAC,MAAM,IAAIA,IAAI,CAACG,IAAI,KAAK,eAAe,EAAE;QACxC,IAAI,CAACN,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,EAAEE,IAAI,CAACA,IAAI,CAAC;MAClE,CAAC,MAAM,IAAIA,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;QAChC,IAAI,CAACN,cAAc,CAAC1C,kBAAkB,CAACkD,KAAK,EAAEL,IAAI,CAACA,IAAI,CAAC;MAC1D,CAAC,MAAM;QACLhC,OAAO,CAACsC,IAAI,CAAC,iCAAiC,EAAEN,IAAI,CAACG,IAAI,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,EAAEmB,KAAK,CAACC,IAAI,CAAC;IACtE;EACF;;EAEA;AACF;AACA;EACUvB,WAAWA,CAACsB,KAAiB,EAAQ;IAC3C/B,OAAO,CAACC,GAAG,CAAC,gCAAgC8B,KAAK,CAACQ,IAAI,IAAIR,KAAK,CAACS,MAAM,EAAE,CAAC;IACzE,IAAI,CAAClD,MAAM,GAAG,IAAI;IAClB,IAAI,CAACM,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,YAAY,GAAG,KAAK;;IAEzB;IACA,MAAM+B,WAA8B,GAAG;MACrCC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,6BAA6BG,KAAK,CAACS,MAAM,IAAI,mBAAmB;IAC3E,CAAC;IACD,IAAI,CAACX,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,EAAEJ,WAAW,CAAC;;IAElE;IACA,IAAIK,KAAK,CAACQ,IAAI,KAAK,IAAI,EAAE;MACvB,IAAI,CAAC1B,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACUF,WAAWA,CAACoB,KAAY,EAAQ;IACtC/B,OAAO,CAACY,KAAK,CAAC,kBAAkB,EAAEmB,KAAK,CAAC;;IAExC;IACA,MAAMU,UAAsB,GAAG;MAC7BF,IAAI,EAAE,kBAAkB;MACxBX,OAAO,EAAE;IACX,CAAC;IACD,IAAI,CAACC,cAAc,CAAC1C,kBAAkB,CAACkD,KAAK,EAAEI,UAAU,CAAC;EAC3D;;EAEA;AACF;AACA;EACU5B,iBAAiBA,CAAA,EAAS;IAChC,IAAI,IAAI,CAACtB,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACvDQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;MAEpD;MACA,MAAMyB,WAA8B,GAAG;QACrCC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE;MACX,CAAC;MACD,IAAI,CAACC,cAAc,CAAC1C,kBAAkB,CAAC2C,aAAa,EAAEJ,WAAW,CAAC;MAElE;IACF;IAEA,IAAI,IAAI,CAAChC,cAAc,KAAK,IAAI,EAAE;MAChCsB,MAAM,CAACC,YAAY,CAAC,IAAI,CAACvB,cAAc,CAAC;IAC1C;IAEA,MAAMgD,KAAK,GAAG,IAAI,CAACjD,cAAc,GAAGkD,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACrD,iBAAiB,CAAC;IACzES,OAAO,CAACC,GAAG,CAAC,2BAA2ByC,KAAK,eAAe,IAAI,CAACnD,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAEtH,IAAI,CAACE,cAAc,GAAGsB,MAAM,CAAC6B,UAAU,CAAC,MAAM;MAC5C,IAAI,CAACtD,iBAAiB,EAAE;MACxB,IAAI,CAACO,OAAO,CAAC,CAAC;IAChB,CAAC,EAAE4C,KAAK,CAAC;EACX;;EAEA;AACF;AACA;EACUb,cAAcA,CAACV,SAAiB,EAAEa,IAAS,EAAQ;IACzD,IAAI,CAAC,IAAI,CAACnC,aAAa,CAACsB,SAAS,CAAC,EAAE;MAClC;IACF;IACA,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,EAAE;MACnD,IAAI;QACFC,OAAO,CAACY,IAAI,CAAC;MACf,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,YAAYO,SAAS,WAAW,EAAEP,KAAK,CAAC;MACxD;IACF;EACF;AACF;;AAEA;AACA,OAAO,MAAMkC,gBAAgB,GAAG,IAAI1D,gBAAgB,CAAC,CAAC;;AAEtD;AACA,eAAe0D,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}