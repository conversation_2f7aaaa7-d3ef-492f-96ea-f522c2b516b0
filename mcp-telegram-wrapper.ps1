# MCP Telegram PowerShell Wrapper
# Better Unicode support than cmd.exe

# Set environment variables
$env:API_ID = "28134400"
$env:API_HASH = "1ba5405c7b0d1ea7d2ee3556c517b34c"
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUNBUFFERED = "1"

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

# Execute mcp-telegram
& "C:\Users\<USER>\AppData\Roaming\uv\tools\mcp-telegram\Scripts\mcp-telegram.exe" $args