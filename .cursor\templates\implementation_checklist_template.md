# IMPLEMENTATION CHECKLIST: [Feature/Task Name]
# Date: YYYY-MM-DD
# Output from [MODE: PLAN]

## Overall Goal:
- (Brief description of what this checklist aims to achieve)

## Pre-requisites / Assumptions:
- (Any conditions that must be met before starting)
- (Key assumptions made during planning)

## Checklist Items:
*   [ ] 1. **[Specific Action 1]**
    *   File(s): `path/to/file.ext`
    *   Details: (Brief notes on what needs to be done for this step)
*   [ ] 2. **[Specific Action 2]**
    *   File(s): `path/to/another/file.ext`, `module/name/`
    *   Details:
*   [ ] 3. **[Specific Action 3]**
    *   Command(s): (If the action involves running a command)
    *   Details:
*   [ ] ...
*   [ ] n. **[Final Action / Verification Step]**
    *   Details: (e.g., "Run all unit tests," "Verify feature X works as expected on Y browser")

## Post-Implementation Steps:
- (e.g., Update documentation, notify stakeholders, deploy to staging)

## Notes / Considerations:
- (Any other important points, potential issues, or alternative approaches considered during PLAN mode)
