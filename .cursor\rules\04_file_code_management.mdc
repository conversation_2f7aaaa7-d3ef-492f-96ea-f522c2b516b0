---
description: 
globs: 
alwaysApply: true
---
# <PERSON> AI Assistant - File & Code Management Protocol
# Version 2.1

## I. File Creation (`write_to_file` / `mcp4_write`)
1.  **No Overwriting**: NEVER use `write_to_file` to modify or overwrite existing files. Confirm the file does not exist before calling this tool. For modifications, use `edit_file`.
2.  **Directory Creation**: The `write_to_file` tool will create parent directories if they do not exist.
3.  **Empty Files**: Use `EmptyFile: true` if an empty file needs to be created.

## II. Code Editing (`edit_file`)
1.  **Target File First**: Always specify the `TargetFile` argument first.
2.  **Single Edit Call**: CRITICAL: Combine ALL changes to a single file into a SINGLE `edit_file` tool call.
3.  **Precise Edits**: Specify ONLY the precise lines of code that you wish to edit.
4.  **Placeholder for Unchanged Code**: NEVER specify or write out unchanged code. Represent all unchanged code using the special placeholder: `{{ ... }}`.
5.  **Instruction Clarity**: Provide a clear `Instruction` describing the changes being made.

## III. Code Quality & Maintainability
1.  **Dependency Management**:
    *   If creating a codebase from scratch, create an appropriate dependency management file (e.g., `requirements.txt`, `package.json`) with specific package versions.
    *   Include a helpful `README.md` for new projects.
2.  **Web App UI/UX**: If building a web app from scratch, aim for a beautiful and modern UI, imbued with best UX practices.
3.  **No Binary/Long Hashes**: NEVER generate extremely long non-textual code (like binary data or excessively long hashes) directly into files.
4.  **Modularity**: Adhere to a 500-line limit per file. If logic exceeds this, modularize it into smaller, focused functions, classes, or files to improve readability, maintainability, and testability.

## IV. General File Operations (using `mcp4_` tools)
1.  **Caution with Deletion**: Be extremely cautious with `mcp4_delete`. Confirm with the user if there's any doubt, especially with `recursive: true`.
2.  **Clear Intent**: Clearly state the source and target for `mcp4_move` and `mcp4_rename` operations.

# (This document guides file and code management practices for Cascade.)
