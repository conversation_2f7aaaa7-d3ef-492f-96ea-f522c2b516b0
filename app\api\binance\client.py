"""Main Binance API client class."""
from typing import Dict, List, Optional, Union, Any
import requests
from urllib.parse import urlencode
from collections import OrderedDict
import logging
import os

from app.api.binance.utils import generate_signature, prepare_params, sync_time
from app.api.binance.market_data import MarketDataMixin
from app.api.binance.account import AccountMixin
from app.api.binance.order_management import OrderManagementMixin
from app.api.binance.websocket import WebsocketMixin

logger = logging.getLogger(__name__)

class BinanceClient(MarketDataMixin, AccountMixin, OrderManagementMixin, WebsocketMixin):
    """Client for interacting with Binance Futures API."""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        """Initialize Binance client with API key and secret."""
        self.api_key = api_key
        self.api_secret = api_secret
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': api_key,
            'Content-Type': 'application/json'
        })
        
        # Default recvWindow set to 60000 to ensure requests are accepted
        self.recv_window = 60000
        
        # Set API base URL based on testnet flag
        if testnet:
            self.base_url = 'https://testnet.binancefuture.com'
        else:
            self.base_url = 'https://fapi.binance.com'
            
        # Initialize time offset to 0
        self.time_offset = 0
        
        # Initialize time synchronization
        self.time_offset = sync_time(self.base_url)

        # Cache for exchange info
        self.exchange_info_cache = {}
        self._init_exchange_info()
    
    def set_recv_window(self, recv_window: int) -> None:
        """Set the recvWindow parameter value.
        
        Args:
            recv_window (int): recvWindow value in milliseconds
        """
        self.recv_window = recv_window
    
    def _make_request(self, method: str, endpoint: str, signed: bool = False, **kwargs) -> Union[Dict[str, Any], List[Any]]:
        """Make a request to the Binance API.
        
        Args:
            method (str): HTTP method (GET, POST, DELETE)
            endpoint (str): API endpoint
            signed (bool): Whether the request needs a signature
            **kwargs: Additional request parameters
            
        Returns:
            Union[Dict[str, Any], List[Any]]: API response
            
        Raises:
            Exception: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        
        # Prepare params and data
        params = kwargs.get('params', OrderedDict())
        
        # Check if we need to sign the request
        if signed:
            # Prepare parameters, adding timestamp and recvWindow
            params = prepare_params(params, self.time_offset, self.recv_window)
            
            # Generate signature
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            signature = generate_signature(self.api_secret, query_string)
            params['signature'] = signature
        
        # Log the request
        logger.debug(f"Making {method} request to {url} with params {params}")
        
        # Make the request
        response = None
        try:
            if method == "GET":
                response = self.session.get(url, params=params)
            elif method == "POST":
                response = self.session.post(url, params=params)
            elif method == "DELETE":
                response = self.session.delete(url, params=params)
            elif method == "PUT":
                response = self.session.put(url, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response.raise_for_status()
            
            if not response.text:
                logger.warning(f"Empty response received from {url}")
                return {}
                
            data = response.json()
            return data
            
        except requests.exceptions.RequestException as e:
            if hasattr(e, 'response') and e.response is not None:
                status_code = e.response.status_code
                
                # Log detailed URL including params for debugging
                full_url = f"{url}"
                if params:
                    query_string = urlencode(params)
                    full_url = f"{url}?{query_string}"
                
                error_msg = f"Request failed: {e}"
                
                # Always print the raw response text for debugging
                print(f"BINANCE API ERROR - Raw response: {e.response.text}")
                
                # Try to extract JSON error message if available
                try:
                    error_json = e.response.json()
                    if isinstance(error_json, dict):
                        error_code = error_json.get('code', 'Unknown')
                        error_message = error_json.get('msg', 'No message')
                        # Print complete error details
                        print(f"BINANCE API ERROR - Code: {error_code} - Message: {error_message} - Full response: {error_json}")
                        error_msg = f"Binance API Error: {error_code} - {error_message}"
                    else:
                        error_msg = f"Request failed: {e} - Response: {e.response.text}"
                except:
                    error_msg = f"Request failed: {e} - Response: {e.response.text}"
                
                # Handle specific error codes with actionable messages
                if status_code == 401:
                    logger.error("Authentication failed. Check your API keys.")
                elif status_code == 418 or status_code == 429:
                    logger.error("Rate limit exceeded. Consider implementing backoff strategy.")
                elif status_code == 400:
                    logger.error("Bad request. Check your parameters and API version.")
            else:
                error_msg = f"Request failed: {e}"
            
            logger.error(error_msg)
            raise Exception(error_msg)
