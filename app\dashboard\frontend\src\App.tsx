import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import Login from './pages/Login';
import AutoTradeControl from './pages/AutoTradeControl'; // Renamed import
import MLOptimization from './pages/MLOptimization'; // ML Optimization page
import ProtectedRoute from './components/ProtectedRoute';

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <CssBaseline />
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />

            {/* Protected routes */}
            <Route element={<ProtectedRoute />}>
              {/* Render AutoTradeControl directly */}
              <Route path="/trading" element={<AutoTradeControl />} />
              {/* ML Optimization page */}
              <Route path="/ml" element={<MLOptimization />} />
            </Route>

            {/* Redirect to trading by default */}
            <Route path="/" element={<Navigate to="/trading" replace />} />
            <Route path="*" element={<Navigate to="/trading" replace />} />
          </Routes>
        </BrowserRouter>
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
