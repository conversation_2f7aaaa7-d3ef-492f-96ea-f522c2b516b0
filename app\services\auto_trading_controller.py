"""
Auto Trading Controller - Central orchestration service for the complete ensemble trading system.
This is the main control system that manages the entire end-to-end trading pipeline from the PRD.
"""

import asyncio
import logging
import json
import uuid
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict

@dataclass
class MarketData:
    """Market data structure for trading operations."""
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    high_24h: float
    low_24h: float
    change_24h: float

# Import services with error handling for optional dependencies
try:
    from app.strategies.ensemble_portfolio_manager import EnsemblePortfolioManager
except ImportError:
    try:
        from app.strategies.simple_ensemble_manager import SimpleEnsembleManager as EnsemblePortfolioManager
    except ImportError:
        EnsemblePortfolioManager = None

try:
    from app.services.execution.execution_service import ExecutionService
except ImportError:
    ExecutionService = None

try:
    from app.services.mcp.redis_service import RedisService
except ImportError:
    RedisService = None

try:
    from app.services.mcp.supabase_service import SupabaseService
except ImportError:
    SupabaseService = None

try:
    from app.services.mcp.wandb_service import WandBService
except ImportError:
    WandBService = None

try:
    from app.services.mcp.cross_exchange_validator import CrossExchangeValidator
except ImportError:
    CrossExchangeValidator = None

try:
    from app.services.volatility_calculator import VolatilityCalculator
except ImportError:
    VolatilityCalculator = None

try:
    from app.services.position_size_calculator import PositionSizeCalculator
except ImportError:
    PositionSizeCalculator = None

try:
    from app.services.correlation_calculator import CorrelationCalculator
except ImportError:
    CorrelationCalculator = None

try:
    from app.services.cost_calculator import CostCalculator
except ImportError:
    CostCalculator = None

try:
    from app.utils.risk_calculator import RiskCalculator
except ImportError:
    RiskCalculator = None

try:
    from app.utils.performance_calculator import PerformanceCalculator
except ImportError:
    PerformanceCalculator = None

try:
    from app.utils.drawdown_calculator import DrawdownCalculator
except ImportError:
    DrawdownCalculator = None

try:
    from app.monitoring.risk_monitor import RiskMonitor
except ImportError:
    RiskMonitor = None

try:
    from app.monitoring.telegram_performance_monitor import TelegramPerformanceMonitor
except ImportError:
    TelegramPerformanceMonitor = None

try:
    from app.dashboard.api.websocket import (
        broadcast_ml_training_update,
        broadcast_ml_prediction_update, 
        broadcast_ml_model_deployed,
        broadcast_ml_performance_update,
        broadcast_ml_retraining_triggered
    )
except ImportError:
    # Create no-op functions if WebSocket module is not available
    async def broadcast_ml_training_update(data): pass
    async def broadcast_ml_prediction_update(data): pass
    async def broadcast_ml_model_deployed(data): pass
    async def broadcast_ml_performance_update(data): pass
    async def broadcast_ml_retraining_triggered(data): pass

logger = logging.getLogger(__name__)

class TradingSessionStatus(Enum):
    """Trading session status enumeration"""
    RUNNING = "running"
    STOPPED = "stopped"
    PAUSED = "paused"
    ERROR = "error"
    INITIALIZING = "initializing"
    STOPPING = "stopping"

@dataclass
class TradingParameters:
    """Trading session configuration parameters"""
    # Risk Management
    max_position_size: float = 0.1  # 10% max per position
    portfolio_exposure_limit: float = 0.8  # 80% max total exposure
    max_drawdown_limit: float = 0.15  # 15% max drawdown
    stop_loss_pct: float = 0.02  # 2% stop loss
    take_profit_pct: float = 0.05  # 5% take profit
    
    # Strategy Settings
    grid_strategy_enabled: bool = True
    ta_strategy_enabled: bool = True
    trend_strategy_enabled: bool = True
    min_confidence_threshold: float = 0.6
    
    # Execution Settings
    order_type: str = "MARKET"  # MARKET, LIMIT
    slippage_tolerance: float = 0.001  # 0.1% slippage tolerance
    execution_speed: str = "FAST"  # FAST, NORMAL, CAREFUL
    
    # ML Settings
    model_refresh_frequency: int = 300  # 5 minutes
    weight_confidence_threshold: float = 0.7
    enable_dynamic_rebalancing: bool = True
    
    # Monitoring Settings
    alert_thresholds: Dict[str, float] = None
    reporting_frequency: int = 60  # 1 minute
    telegram_alerts_enabled: bool = True
    
    # Symbols and Markets
    symbols: List[str] = None
    base_currency: str = "USDT"
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "drawdown": 0.05,  # 5% drawdown alert
                "position_size": 0.08,  # 8% position size alert
                "correlation": 0.8,  # 80% correlation alert
                "volatility": 0.03  # 3% volatility alert
            }
        if self.symbols is None:
            self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]

@dataclass
class SessionPerformance:
    """Real-time session performance metrics with ML enhancements"""
    # Financial Metrics
    total_return: float = 0.0
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    
    # Risk Metrics
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    volatility: float = 0.0
    var_95: float = 0.0  # Value at Risk 95%
    
    # Trading Metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    
    # Execution Metrics
    avg_execution_time_ms: float = 0.0
    slippage_cost: float = 0.0
    commission_cost: float = 0.0
    
    # Strategy Attribution
    strategy_performance: Dict[str, float] = None
    strategy_weights: Dict[str, float] = None
    
    # ML Performance Metrics (Enhanced)
    ml_model_accuracy: float = 0.0
    ml_model_confidence: float = 0.0
    ml_model_version: str = "unknown"
    ml_drift_score: float = 0.0
    ml_prediction_accuracy: float = 0.0
    ml_vs_traditional_performance: Dict[str, float] = None
    
    # Feature Importance Evolution
    feature_importance_current: Dict[str, float] = None
    feature_importance_drift: Dict[str, float] = None
    top_features: List[str] = None
    
    # Training Costs and ROI
    ml_training_cost: float = 0.0
    ml_inference_cost: float = 0.0
    ml_total_cost: float = 0.0
    ml_roi: float = 0.0  # ML ROI vs traditional methods
    cost_per_prediction: float = 0.0
    
    # Model Decision Tracking
    ml_decisions_count: int = 0
    ml_decisions_correct: int = 0
    ml_decisions_profitable: int = 0
    ml_confidence_buckets: Dict[str, int] = None  # High/Medium/Low confidence counts
    
    # Real-time ML Metrics
    current_model_state: str = "active"  # active, training, updating, degraded
    model_last_update: datetime = None
    prediction_latency_ms: float = 0.0
    model_memory_usage_mb: float = 0.0
    
    # W&B Integration Metrics
    wandb_run_id: str = None
    wandb_experiment_name: str = None
    wandb_project_name: str = None
    ml_experiment_tags: List[str] = None
    
    # Timestamps
    start_time: datetime = None
    last_update: datetime = None
    
    def __post_init__(self):
        if self.strategy_performance is None:
            self.strategy_performance = {}
        if self.strategy_weights is None:
            self.strategy_weights = {}
        if self.ml_vs_traditional_performance is None:
            self.ml_vs_traditional_performance = {"ml": 0.0, "traditional": 0.0, "combined": 0.0}
        if self.feature_importance_current is None:
            self.feature_importance_current = {}
        if self.feature_importance_drift is None:
            self.feature_importance_drift = {}
        if self.top_features is None:
            self.top_features = []
        if self.ml_confidence_buckets is None:
            self.ml_confidence_buckets = {"high": 0, "medium": 0, "low": 0}
        if self.ml_experiment_tags is None:
            self.ml_experiment_tags = []
        if self.start_time is None:
            self.start_time = datetime.now()
        if self.last_update is None:
            self.last_update = datetime.now()
        if self.model_last_update is None:
            self.model_last_update = datetime.now()

@dataclass
class Alert:
    """Trading session alert"""
    id: str
    timestamp: datetime
    level: str  # INFO, WARNING, ERROR, CRITICAL
    type: str  # RISK, PERFORMANCE, EXECUTION, SYSTEM
    message: str
    details: Dict[str, Any] = None
    acknowledged: bool = False
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}

@dataclass
class Trade:
    """Trading session trade record"""
    id: str
    timestamp: datetime
    symbol: str
    action: str  # BUY, SELL
    quantity: float
    price: float
    value: float
    strategy: str
    confidence: float
    execution_time_ms: float
    slippage: float
    commission: float
    pnl: float = 0.0
    status: str = "PENDING"  # PENDING, FILLED, CANCELLED, FAILED

@dataclass
class TradingSession:
    """Complete trading session data structure"""
    id: str
    status: TradingSessionStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    parameters: TradingParameters = None
    performance: SessionPerformance = None
    trades: List[Trade] = None
    alerts: List[Alert] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = TradingParameters()
        if self.performance is None:
            self.performance = SessionPerformance()
        if self.trades is None:
            self.trades = []
        if self.alerts is None:
            self.alerts = []

class AutoTradingController:
    """
    Central Auto Trading Controller that orchestrates the complete end-to-end strategy ensemble pipeline.
    This is the main system that implements the PRD workflow:
    
    Market Data → Portfolio Manager → [Grid, TA, Trend Strategies] → Signal Aggregation 
    → ML Weight Optimization → Position Sizing → Risk Management → Execution → Analytics
    """
    
    def __init__(
        self,
        ensemble_manager: EnsemblePortfolioManager,
        execution_service: ExecutionService,
        redis_service: RedisService,
        supabase_service: SupabaseService,
        wandb_service: Optional[WandBService] = None,
        risk_monitor: Optional[RiskMonitor] = None,
        telegram_monitor: Optional[TelegramPerformanceMonitor] = None,
        config: Optional[Dict] = None
    ):
        """
        Initialize the Auto Trading Controller.
        
        Args:
            ensemble_manager: Strategy ensemble portfolio manager
            execution_service: Trade execution service
            redis_service: Redis caching service
            supabase_service: Supabase analytics service
            wandb_service: W&B ML tracking service (optional)
            risk_monitor: Risk monitoring service (optional)
            telegram_monitor: Telegram alerts service (optional)
            config: Controller configuration
        """
        self.ensemble_manager = ensemble_manager
        self.execution_service = execution_service
        self.redis_service = redis_service
        self.supabase_service = supabase_service
        self.wandb_service = wandb_service
        self.risk_monitor = risk_monitor
        self.telegram_monitor = telegram_monitor
        
        # Configuration
        self.config = config or self._default_config()
        
        # State Management
        self.current_session: Optional[TradingSession] = None
        self.session_history: Dict[str, TradingSession] = {}
        self.is_running = False
        self.is_stopping = False
        
        # ML Integration
        self.weight_optimizer = None
        self.ml_monitoring_enabled = True
        self.last_ml_prediction_time = None
        self.ml_prediction_cache = {}
        self.ml_performance_metrics = {
            "prediction_accuracy": 0.0,
            "prediction_latency_ms": 0.0,
            "model_confidence": 0.0,
            "predictions_count": 0
        }
        
        # Real-time ML Learning System
        self.ml_feedback_buffer = []  # Buffer for collecting training feedback
        self.ml_training_data = []  # Real-time training dataset
        self.online_learning_enabled = True
        self.learning_rate_adaptive = True
        self.prediction_accuracy_history = []  # Track prediction accuracy over time
        self.model_update_frequency = 10  # Update every N trading cycles
        self.trade_cycle_count = 0
        self.last_model_update_time = None
        self.min_feedback_samples = 5  # Minimum samples before model update
        self.accuracy_threshold = 0.6  # Threshold for adjusting learning rate
        
        # Services (with None checks for optional dependencies)
        self.cross_validator = CrossExchangeValidator() if CrossExchangeValidator else None
        self.volatility_calc = VolatilityCalculator() if VolatilityCalculator else None
        self.position_calc = PositionSizeCalculator() if PositionSizeCalculator else None
        self.correlation_calc = CorrelationCalculator() if CorrelationCalculator else None
        self.cost_calc = CostCalculator() if CostCalculator else None
        
        # Enhanced Risk Management Calculators
        self.risk_calc = None
        self.performance_calc = None
        self.drawdown_calc = None
        
        # Initialize enhanced calculators with Redis if available
        if RiskCalculator and redis_service:
            self.risk_calc = RiskCalculator(redis_service.redis)
            logger.info("Risk Calculator initialized")
        
        if PerformanceCalculator and redis_service:
            self.performance_calc = PerformanceCalculator(redis_service.redis)
            logger.info("Performance Calculator initialized")
            
        if DrawdownCalculator and redis_service:
            self.drawdown_calc = DrawdownCalculator(redis_service.redis)
            logger.info("Drawdown Calculator initialized")
        
        # Trading loop control
        self._trading_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self._continuous_learning_task: Optional[asyncio.Task] = None
        
        # Cache keys
        self.SESSION_CACHE_KEY = "auto_trading:session"
        self.STATUS_CACHE_KEY = "auto_trading:status"
        self.METRICS_CACHE_KEY = "auto_trading:metrics"
        
        logger.info("Auto Trading Controller initialized successfully")
    
    async def initialize_ml_services(self):
        """Initialize ML services for weight optimization"""
        try:
            from app.ml.models.weight_optimizer import WeightOptimizer
            
            self.weight_optimizer = WeightOptimizer(
                enable_experiment_tracking=True,
                wandb_api_key=None,  # Uses environment variable
                mlflow_tracking_uri="http://localhost:5000"
            )
            
            logger.info("ML Weight Optimizer initialized successfully")
            
            # Test initial prediction to verify model loading
            test_market_conditions = {
                'volatility': 0.02,
                'volume': 1000000,
                'rsi': 50,
                'macd': 0,
                'price_change': 0,
                'volatility_ma': 0.02,
                'volume_ma': 1000000,
                'rsi_ma': 50
            }
            
            test_weights = await self.weight_optimizer.predict_weights(test_market_conditions, log_prediction=False)
            logger.info(f"ML model test prediction successful: {test_weights}")
            
        except Exception as e:
            logger.warning(f"Failed to initialize ML services: {e}")
            self.weight_optimizer = None
            self.ml_monitoring_enabled = False
    
    def _default_config(self) -> Dict:
        """Default configuration for the controller."""
        return {
            "trading_loop_interval": 5.0,  # 5 seconds between cycles
            "monitoring_interval": 10.0,   # 10 seconds monitoring
            "max_session_duration": 28800,  # 8 hours max session
            "emergency_stop_enabled": True,
            "auto_restart_on_error": False,
            "max_restart_attempts": 3,
            "health_check_interval": 30.0,  # 30 seconds
            "performance_logging_interval": 60.0,  # 1 minute
            "cache_ttl": 300,  # 5 minutes
            "enable_paper_trading": True,  # Safety default
            "enable_real_trading": False   # Must be explicitly enabled
        }
    
    async def get_ml_trading_signals(self, market_data: MarketData) -> Dict[str, float]:
        """Get ML-optimized strategy weights for current market conditions"""
        if not self.weight_optimizer or not self.ml_monitoring_enabled:
            # Return equal weights as fallback
            return {"grid_weight": 0.33, "ta_weight": 0.33, "trend_weight": 0.34}
        
        try:
            start_time = datetime.now()
            
            # Prepare market conditions for ML prediction
            market_conditions = {
                'volatility': getattr(market_data, 'volatility', 0.02),
                'volume': market_data.volume,
                'rsi': getattr(market_data, 'rsi', 50),
                'macd': getattr(market_data, 'macd', 0),
                'price_change': getattr(market_data, 'change_24h', 0) / 100,  # Convert percentage
                'volatility_ma': getattr(market_data, 'volatility_ma', 0.02),
                'volume_ma': getattr(market_data, 'volume_ma', market_data.volume),
                'rsi_ma': getattr(market_data, 'rsi_ma', 50)
            }
            
            # Get ML prediction
            weights = await self.weight_optimizer.predict_weights(market_conditions, log_prediction=True)
            
            # Calculate prediction latency
            prediction_latency = (datetime.now() - start_time).total_seconds() * 1000
            
            # Update ML monitoring metrics
            self.ml_performance_metrics["prediction_latency_ms"] = prediction_latency
            self.ml_performance_metrics["predictions_count"] += 1
            self.last_ml_prediction_time = datetime.now()
            
            # Convert numpy array to dictionary
            result = {
                "grid_weight": float(weights[0]),
                "ta_weight": float(weights[1]),
                "trend_weight": float(weights[2])
            }
            
            # Cache the prediction
            cache_key = f"ml_prediction:{market_data.symbol}:{int(start_time.timestamp())}"
            self.ml_prediction_cache[cache_key] = {
                "weights": result,
                "market_conditions": market_conditions,
                "timestamp": start_time.isoformat(),
                "latency_ms": prediction_latency
            }
            
            # Keep cache size manageable (last 100 predictions)
            if len(self.ml_prediction_cache) > 100:
                oldest_key = min(self.ml_prediction_cache.keys())
                del self.ml_prediction_cache[oldest_key]
            
            # Broadcast ML prediction update via WebSocket
            try:
                await broadcast_ml_prediction_update({
                    "symbol": market_data.symbol,
                    "weights": result,
                    "market_conditions": market_conditions,
                    "latency_ms": prediction_latency,
                    "prediction_count": self.ml_performance_metrics["predictions_count"],
                    "timestamp": start_time.isoformat()
                })
            except Exception as broadcast_error:
                logger.warning(f"Failed to broadcast ML prediction update: {broadcast_error}")
            
            logger.debug(f"ML weights prediction: {result} (latency: {prediction_latency:.2f}ms)")
            return result
            
        except Exception as e:
            logger.error(f"ML prediction failed: {e}")
            # Return equal weights as fallback
            return {"grid_weight": 0.33, "ta_weight": 0.33, "trend_weight": 0.34}
    
    async def monitor_ml_model_health(self) -> Dict[str, Any]:
        """Monitor ML model health and performance"""
        if not self.weight_optimizer:
            return {"status": "disabled", "message": "ML services not initialized"}
        
        try:
            # Get model information
            model_info = self.weight_optimizer.get_model_info()
            
            # Calculate performance metrics
            current_time = datetime.now()
            time_since_last_prediction = None
            if self.last_ml_prediction_time:
                time_since_last_prediction = (current_time - self.last_ml_prediction_time).total_seconds()
            
            health_status = {
                "status": "healthy" if model_info.get("status") == "loaded" else "degraded",
                "model_version": model_info.get("version", "unknown"),
                "model_source": model_info.get("source", "unknown"),
                "last_prediction_seconds_ago": time_since_last_prediction,
                "prediction_count": self.ml_performance_metrics["predictions_count"],
                "avg_latency_ms": self.ml_performance_metrics["prediction_latency_ms"],
                "model_confidence": self.ml_performance_metrics["model_confidence"],
                "cache_size": len(self.ml_prediction_cache),
                "monitoring_enabled": self.ml_monitoring_enabled
            }
            
            # Add model-specific metrics if available
            if model_info.get("metrics"):
                health_status["model_metrics"] = model_info["metrics"]
            
            return health_status
            
        except Exception as e:
            logger.error(f"ML health monitoring failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "monitoring_enabled": self.ml_monitoring_enabled
            }
    
    async def trigger_ml_model_retrain(self, force: bool = False) -> Dict[str, Any]:
        """Trigger ML model retraining based on performance metrics"""
        if not self.weight_optimizer:
            return {"success": False, "message": "ML services not initialized"}
        
        try:
            # Check if retraining is needed
            should_retrain = force
            
            if not force and self.current_session:
                # Auto-trigger retraining if performance is poor
                win_rate = self.current_session.performance.win_rate
                if win_rate < 0.4 and self.ml_performance_metrics["predictions_count"] > 50:
                    should_retrain = True
                    logger.info(f"Auto-triggering ML retraining due to low win rate: {win_rate:.2%}")
            
            if should_retrain:
                logger.info("Starting ML model retraining...")
                
                # Broadcast retraining triggered event
                try:
                    await broadcast_ml_retraining_triggered({
                        "message": "ML model retraining started",
                        "forced": force,
                        "trigger_reason": "low_win_rate" if not force else "manual",
                        "session_id": self.current_session.id if self.current_session else None,
                        "predictions_count": self.ml_performance_metrics["predictions_count"],
                        "timestamp": datetime.now().isoformat()
                    })
                except Exception as broadcast_error:
                    logger.warning(f"Failed to broadcast retraining trigger: {broadcast_error}")
                
                retrain_success = await self.weight_optimizer.retrain_model()
                
                if retrain_success:
                    # Reset performance metrics after retraining
                    self.ml_performance_metrics["predictions_count"] = 0
                    self.ml_prediction_cache.clear()
                    
                    # Broadcast successful retraining completion
                    try:
                        await broadcast_ml_model_deployed({
                            "message": "Model retraining completed successfully",
                            "forced": force,
                            "model_version": "retrained",
                            "session_id": self.current_session.id if self.current_session else None,
                            "timestamp": datetime.now().isoformat()
                        })
                    except Exception as broadcast_error:
                        logger.warning(f"Failed to broadcast retraining completion: {broadcast_error}")
                    
                    return {
                        "success": True,
                        "message": "Model retraining completed successfully",
                        "forced": force
                    }
                else:
                    # Broadcast failed retraining
                    try:
                        await broadcast_ml_training_update({
                            "message": "Model retraining failed",
                            "status": "failed",
                            "forced": force,
                            "session_id": self.current_session.id if self.current_session else None,
                            "timestamp": datetime.now().isoformat()
                        })
                    except Exception as broadcast_error:
                        logger.warning(f"Failed to broadcast retraining failure: {broadcast_error}")
                    
                    return {
                        "success": False,
                        "message": "Model retraining failed",
                        "forced": force
                    }
            else:
                return {
                    "success": False,
                    "message": "Retraining not needed based on current performance",
                    "forced": force
                }
                
        except Exception as e:
            logger.error(f"ML model retraining failed: {e}")
            return {
                "success": False,
                "message": f"Retraining error: {str(e)}",
                "forced": force
            }
    
    async def _execute_ensemble_with_ml_weights(
        self, 
        validated_data: Dict[str, MarketData], 
        ml_trading_signals: Dict[str, Dict[str, float]]
    ) -> Tuple[List[Any], Dict[str, Any]]:
        """Execute ensemble strategy with ML-optimized weights."""
        try:
            # Check if the ensemble manager supports ML weight injection
            if hasattr(self.ensemble_manager, 'execute_ensemble_with_ml_weights'):
                # Use the enhanced ensemble manager method with ML weights
                executed_trades, performance_metrics = await self.ensemble_manager.execute_ensemble_with_ml_weights(
                    validated_data, ml_trading_signals
                )
            else:
                # Fallback: Use standard ensemble execution and manually apply weights
                executed_trades, performance_metrics = await self.ensemble_manager.execute_ensemble_with_caching(
                    validated_data
                )
                
                # Post-process trades with ML weights (simplified approach)
                for trade in executed_trades:
                    symbol_weights = ml_trading_signals.get(trade.symbol, {})
                    if symbol_weights:
                        # Adjust trade quantity based on strategy weights
                        strategy_weight = symbol_weights.get(f"{trade.strategy}_weight", 1.0)
                        trade.quantity *= strategy_weight
                        logger.debug(f"Adjusted {trade.strategy} trade quantity by {strategy_weight:.3f}")
            
            return executed_trades, performance_metrics
            
        except Exception as e:
            logger.error(f"Failed to execute ensemble with ML weights: {e}")
            # Fallback to standard ensemble execution
            return await self.ensemble_manager.execute_ensemble_with_caching(validated_data)
    
    async def _monitor_ml_model_performance(self):
        """Monitor ML model performance and trigger retraining if needed."""
        try:
            if not self.weight_optimizer or not self.ml_monitoring_enabled:
                return
            
            # Get model health status
            model_health = await self.monitor_ml_model_health()
            
            # Check if model needs retraining based on performance
            should_retrain = False
            
            # Trigger retraining if:
            # 1. Model predictions are consistently failing
            # 2. Prediction accuracy has degraded
            # 3. Win rate is consistently low
            
            if model_health.get("status") == "degraded":
                logger.warning("ML model health is degraded")
                should_retrain = True
            
            # Check prediction performance
            if self.ml_performance_metrics["predictions_count"] > 100:
                # Check if prediction latency is too high
                if self.ml_performance_metrics["prediction_latency_ms"] > 1000:  # 1 second threshold
                    logger.warning(f"ML prediction latency too high: {self.ml_performance_metrics['prediction_latency_ms']:.1f}ms")
                    await self._add_alert(
                        level="WARNING",
                        type="ML",
                        message=f"ML prediction latency high: {self.ml_performance_metrics['prediction_latency_ms']:.1f}ms"
                    )
            
            # Check trading session performance for auto-retraining
            if self.current_session and self.current_session.performance.total_trades > 50:
                win_rate = self.current_session.performance.win_rate
                
                # Auto-trigger retraining if win rate is consistently poor
                if win_rate < 0.3 and not should_retrain:
                    logger.info(f"Auto-triggering ML retraining due to poor win rate: {win_rate:.2%}")
                    should_retrain = True
            
            # Trigger retraining if needed
            if should_retrain:
                retrain_result = await self.trigger_ml_model_retrain(force=False)
                
                if retrain_result["success"]:
                    await self._add_alert(
                        level="INFO",
                        type="ML",
                        message="ML model retraining completed successfully"
                    )
                else:
                    await self._add_alert(
                        level="ERROR",
                        type="ML",
                        message=f"ML model retraining failed: {retrain_result['message']}"
                    )
            
            # Update ML performance in session
            if self.current_session:
                self.current_session.performance.strategy_weights.update({
                    "ml_prediction_count": self.ml_performance_metrics["predictions_count"],
                    "ml_prediction_latency": self.ml_performance_metrics["prediction_latency_ms"],
                    "ml_model_confidence": self.ml_performance_metrics["model_confidence"]
                })
            
            # Broadcast ML performance update via WebSocket
            try:
                await broadcast_ml_performance_update({
                    "model_health": model_health,
                    "performance_metrics": self.ml_performance_metrics,
                    "session_id": self.current_session.id if self.current_session else None,
                    "win_rate": self.current_session.performance.win_rate if self.current_session else 0,
                    "total_trades": self.current_session.performance.total_trades if self.current_session else 0,
                    "should_retrain": should_retrain,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as broadcast_error:
                logger.warning(f"Failed to broadcast ML performance update: {broadcast_error}")
            
        except Exception as e:
            logger.error(f"ML performance monitoring failed: {e}")
            await self._add_alert(
                level="ERROR",
                type="ML",
                message=f"ML monitoring error: {str(e)}"
            )
    
    async def start_trading_session(
        self, 
        parameters: Optional[TradingParameters] = None,
        session_name: Optional[str] = None
    ) -> str:
        """
        Start a new auto trading session.
        
        Args:
            parameters: Trading session parameters
            session_name: Optional session name for identification
            
        Returns:
            Session ID
            
        Raises:
            RuntimeError: If session is already running or system error
        """
        if self.is_running:
            raise RuntimeError("Auto trading session is already running")
        
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Create new session
            self.current_session = TradingSession(
                id=session_id,
                status=TradingSessionStatus.INITIALIZING,
                start_time=datetime.now(),
                parameters=parameters or TradingParameters()
            )
            
            # Add session name to details if provided
            if session_name:
                self.current_session.parameters.session_name = session_name
            
            # Store session in history
            self.session_history[session_id] = self.current_session
            
            # Cache session data
            await self._cache_session_data()
            
            # Pre-flight checks
            await self._perform_pre_flight_checks()
            
            # Initialize monitoring systems
            await self._initialize_monitoring()
            
            # Start trading loop
            self.current_session.status = TradingSessionStatus.RUNNING
            self.is_running = True
            
            # Start async tasks
            self._trading_task = asyncio.create_task(self._trading_loop())
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            self._continuous_learning_task = asyncio.create_task(self._continuous_learning_loop())
            
            # Log session start
            await self._add_alert(
                level="INFO",
                type="SYSTEM",
                message=f"Auto trading session started: {session_id}"
            )
            
            # Send Telegram notification
            if self.telegram_monitor:
                await self.telegram_monitor.send_alert(
                    f"🚀 Auto Trading Started\nSession: {session_id[:8]}\n"
                    f"Symbols: {', '.join(self.current_session.parameters.symbols)}\n"
                    f"Max Position: {self.current_session.parameters.max_position_size:.1%}"
                )
            
            # Log to W&B
            if self.wandb_service:
                await self.wandb_service.log_event("trading_session_started", {
                    "session_id": session_id,
                    "parameters": asdict(self.current_session.parameters)
                })
            
            logger.info(f"Auto trading session started successfully: {session_id}")
            return session_id
            
        except Exception as e:
            # Cleanup on error
            if self.current_session:
                self.current_session.status = TradingSessionStatus.ERROR
                await self._add_alert(
                    level="ERROR",
                    type="SYSTEM", 
                    message=f"Failed to start trading session: {str(e)}"
                )
            
            self.is_running = False
            logger.error(f"Failed to start auto trading session: {e}")
            raise RuntimeError(f"Failed to start trading session: {str(e)}")
    
    async def stop_trading_session(self, emergency: bool = False) -> Dict[str, Any]:
        """
        Stop the current auto trading session.
        
        Args:
            emergency: If True, perform emergency stop (immediate)
            
        Returns:
            Session summary report
        """
        if not self.is_running or not self.current_session:
            raise RuntimeError("No active trading session to stop")
        
        try:
            self.is_stopping = True
            self.current_session.status = TradingSessionStatus.STOPPING
            
            # Log stop request
            stop_type = "EMERGENCY" if emergency else "NORMAL"
            await self._add_alert(
                level="INFO",
                type="SYSTEM",
                message=f"{stop_type} stop requested for session {self.current_session.id}"
            )
            
            if emergency:
                # Emergency stop - immediate halt
                await self._emergency_stop()
            else:
                # Graceful stop - close positions and cleanup
                await self._graceful_stop()
            
            # Cancel trading tasks
            if self._trading_task and not self._trading_task.done():
                self._trading_task.cancel()
                try:
                    await self._trading_task
                except asyncio.CancelledError:
                    pass
            
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            if self._continuous_learning_task and not self._continuous_learning_task.done():
                self._continuous_learning_task.cancel()
                try:
                    await self._continuous_learning_task
                except asyncio.CancelledError:
                    pass
            
            # Finalize session
            self.current_session.status = TradingSessionStatus.STOPPED
            self.current_session.end_time = datetime.now()
            self.is_running = False
            self.is_stopping = False
            
            # Generate session report
            session_report = await self._generate_session_report()
            
            # Cache final session data
            await self._cache_session_data()
            
            # Send completion notification
            if self.telegram_monitor:
                await self.telegram_monitor.send_alert(
                    f"🛑 Auto Trading Stopped\n"
                    f"Session: {self.current_session.id[:8]}\n"
                    f"Duration: {self._format_duration(self.current_session.end_time - self.current_session.start_time)}\n"
                    f"Total PnL: {self.current_session.performance.total_pnl:.2f}\n"
                    f"Total Trades: {self.current_session.performance.total_trades}"
                )
            
            # Log to W&B
            if self.wandb_service:
                await self.wandb_service.log_event("trading_session_stopped", {
                    "session_id": self.current_session.id,
                    "session_report": session_report,
                    "emergency_stop": emergency
                })
            
            logger.info(f"Auto trading session stopped: {self.current_session.id}")
            return session_report
            
        except Exception as e:
            logger.error(f"Error stopping trading session: {e}")
            self.is_running = False
            self.is_stopping = False
            raise RuntimeError(f"Error stopping session: {str(e)}")
    
    async def get_session_status(self) -> Dict[str, Any]:
        """
        Get current trading session status and metrics.
        
        Returns:
            Comprehensive session status
        """
        if not self.current_session:
            return {
                "session_active": False,
                "status": "NO_SESSION",
                "message": "No active trading session"
            }
        
        # Update performance metrics
        await self._update_performance_metrics()
        
        # Get real-time market data
        market_data = await self._get_current_market_data()
        
        # Get strategy signals
        strategy_signals = await self._get_current_strategy_signals()
        
        return {
            "session_active": self.is_running,
            "session_id": self.current_session.id,
            "status": self.current_session.status.value,
            "start_time": self.current_session.start_time.isoformat(),
            "duration_seconds": (datetime.now() - self.current_session.start_time).total_seconds(),
            "parameters": asdict(self.current_session.parameters),
            "performance": asdict(self.current_session.performance),
            "recent_trades": [asdict(trade) for trade in self.current_session.trades[-10:]],
            "recent_alerts": [asdict(alert) for alert in self.current_session.alerts[-10:]],
            "market_data": market_data,
            "strategy_signals": strategy_signals,
            "system_health": await self._get_system_health()
        }
    
    async def get_session_details(self, session_id: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific trading session.
        
        Args:
            session_id: Session ID to retrieve
            
        Returns:
            Complete session details
        """
        if session_id not in self.session_history:
            raise ValueError(f"Session not found: {session_id}")
        
        session = self.session_history[session_id]
        
        return {
            "session": asdict(session),
            "summary": await self._generate_session_summary(session),
            "analytics": await self._generate_session_analytics(session)
        }
    
    async def list_sessions(
        self, 
        limit: int = 50, 
        offset: int = 0,
        status_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        List trading sessions with pagination.
        
        Args:
            limit: Maximum number of sessions to return
            offset: Number of sessions to skip
            status_filter: Filter by session status
            
        Returns:
            Paginated session list
        """
        # Get sessions from history
        sessions = list(self.session_history.values())
        
        # Apply status filter
        if status_filter:
            sessions = [s for s in sessions if s.status.value == status_filter]
        
        # Sort by start time (newest first)
        sessions.sort(key=lambda x: x.start_time, reverse=True)
        
        # Apply pagination
        total_count = len(sessions)
        paginated_sessions = sessions[offset:offset + limit]
        
        # Create session summaries
        session_summaries = []
        for session in paginated_sessions:
            summary = {
                "id": session.id,
                "status": session.status.value,
                "start_time": session.start_time.isoformat(),
                "end_time": session.end_time.isoformat() if session.end_time else None,
                "duration": self._format_duration(
                    (session.end_time or datetime.now()) - session.start_time
                ),
                "total_pnl": session.performance.total_pnl,
                "total_trades": session.performance.total_trades,
                "win_rate": session.performance.win_rate,
                "symbols": session.parameters.symbols
            }
            session_summaries.append(summary)
        
        return {
            "sessions": session_summaries,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            }
        }
    
    async def pause_session(self) -> bool:
        """Pause the current trading session."""
        if not self.is_running or not self.current_session:
            return False
        
        self.current_session.status = TradingSessionStatus.PAUSED
        await self._add_alert(
            level="INFO",
            type="SYSTEM",
            message="Trading session paused"
        )
        
        return True
    
    async def resume_session(self) -> bool:
        """Resume a paused trading session."""
        if not self.current_session or self.current_session.status != TradingSessionStatus.PAUSED:
            return False
        
        self.current_session.status = TradingSessionStatus.RUNNING
        await self._add_alert(
            level="INFO",
            type="SYSTEM",
            message="Trading session resumed"
        )
        
        return True
    
    async def emergency_stop(self) -> bool:
        """Trigger emergency stop of all trading operations."""
        try:
            await self.stop_trading_session(emergency=True)
            return True
        except Exception as e:
            logger.error(f"Emergency stop failed: {e}")
            return False
    
    # Private methods for internal operations
    
    async def _trading_loop(self):
        """Main trading loop that executes the complete ensemble pipeline."""
        try:
            while self.is_running and not self.is_stopping:
                try:
                    if self.current_session.status == TradingSessionStatus.PAUSED:
                        await asyncio.sleep(self.config["trading_loop_interval"])
                        continue
                    
                    # Step 1: Get current market data from multiple sources
                    market_data = await self._get_current_market_data()
                    
                    # Step 2: Cross-validate market data
                    validated_data = await self._validate_market_data(market_data)
                    
                    # Step 3: Get ML-optimized strategy weights for current market conditions
                    ml_trading_signals = {}
                    for symbol, symbol_market_data in validated_data.items():
                        try:
                            ml_weights = await self.get_ml_trading_signals(symbol_market_data)
                            ml_trading_signals[symbol] = ml_weights
                            logger.debug(f"ML weights for {symbol}: {ml_weights}")
                        except Exception as e:
                            logger.warning(f"Failed to get ML weights for {symbol}: {e}")
                            # Fallback to equal weights
                            ml_trading_signals[symbol] = {"grid_weight": 0.33, "ta_weight": 0.33, "trend_weight": 0.34}
                    
                    # Step 4: Execute ensemble strategy with ML-optimized weights and caching
                    executed_trades, performance_metrics = await self._execute_ensemble_with_ml_weights(
                        validated_data, ml_trading_signals
                    )
                    
                    # Step 5: Apply position sizing and risk management
                    sized_trades = await self._apply_position_sizing(executed_trades, validated_data)
                    
                    # Step 6: Execute trades through execution service
                    final_trades = await self._execute_trades(sized_trades)
                    
                    # Step 7: Update session performance and store analytics
                    await self._update_session_performance(final_trades, validated_data)
                    
                    # Step 8: Monitor ML model health and trigger retraining if needed
                    await self._monitor_ml_model_performance()
                    
                    # Step 9: Collect ML feedback from trading results
                    await self._collect_ml_feedback(validated_data, ml_trading_signals, final_trades)
                    
                    # Step 10: Monitor risk and send alerts if needed
                    await self._monitor_risk_and_alerts()
                    
                    # Step 11: Log performance to external services including ML metrics
                    await self._log_trading_cycle_performance(ml_trading_signals)
                    
                    # Step 12: Increment trade cycle count for learning system
                    self.trade_cycle_count += 1
                    
                    # Wait for next cycle
                    await asyncio.sleep(self.config["trading_loop_interval"])
                    
                except Exception as e:
                    logger.error(f"Error in trading loop: {e}")
                    await self._handle_trading_error(e)
                    
                    if not self.config.get("auto_restart_on_error", False):
                        break
                    
                    await asyncio.sleep(5.0)  # Brief pause before retry
                    
        except asyncio.CancelledError:
            logger.info("Trading loop cancelled")
        except Exception as e:
            logger.error(f"Fatal error in trading loop: {e}")
            await self._handle_fatal_error(e)
    
    async def _monitoring_loop(self):
        """Monitoring loop for system health and performance."""
        try:
            while self.is_running and not self.is_stopping:
                try:
                    # Health checks
                    await self._perform_health_checks()
                    
                    # Performance monitoring
                    await self._monitor_performance()
                    
                    # System resource monitoring
                    await self._monitor_system_resources()
                    
                    # Cache maintenance
                    await self._maintain_cache()
                    
                    await asyncio.sleep(self.config["monitoring_interval"])
                    
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    await asyncio.sleep(10.0)  # Brief pause on error
                    
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled")
    
    async def _get_current_market_data(self) -> Dict[str, MarketData]:
        """Get current market data for all configured symbols using MCP services."""
        market_data = {}
        
        try:
            # Cache key for market data
            cache_key = f"market_data:{':'.join(self.current_session.parameters.symbols)}"
            
            # Try to get from cache first (5-second TTL for real-time data)
            cached_data = await self.redis_service.get(cache_key)
            if cached_data:
                try:
                    cached_market_data = json.loads(cached_data)
                    for symbol, data_dict in cached_market_data.items():
                        market_data[symbol] = MarketData(**data_dict)
                    logger.debug(f"Retrieved market data from cache for {len(market_data)} symbols")
                    return market_data
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"Failed to parse cached market data: {e}")
            
            # Get fresh data from exchange API
            for symbol in self.current_session.parameters.symbols:
                try:
                    # Use the Binance client or CoinCap API through MCP
                    # This integrates with the existing market data services
                    if hasattr(self.ensemble_manager, 'get_market_data'):
                        data = await self.ensemble_manager.get_market_data(symbol)
                    else:
                        # Fallback to creating MarketData with current time
                        data = MarketData(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            price=0.0,  # Will be updated by actual data
                            volume=0,
                            high_24h=0.0,
                            low_24h=0.0,
                            change_24h=0.0
                        )
                    
                    market_data[symbol] = data
                    
                except Exception as e:
                    logger.error(f"Failed to get market data for {symbol}: {e}")
                    
                    # Add alert for market data failure
                    await self._add_alert(
                        level="WARNING",
                        type="SYSTEM",
                        message=f"Market data unavailable for {symbol}: {str(e)}"
                    )
            
            # Cache the fresh data
            if market_data:
                cache_data = {symbol: asdict(data) for symbol, data in market_data.items()}
                await self.redis_service.setex(
                    cache_key, 
                    5,  # 5-second TTL for real-time data
                    json.dumps(cache_data, default=str)
                )
                logger.debug(f"Cached market data for {len(market_data)} symbols")
            
        except Exception as e:
            logger.error(f"Critical error getting market data: {e}")
            await self._add_alert(
                level="ERROR",
                type="SYSTEM",
                message=f"Market data service failure: {str(e)}"
            )
        
        return market_data
    
    async def _validate_market_data(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Cross-validate market data using multiple sources."""
        validated_data = {}
        
        for symbol, data in market_data.items():
            try:
                # Use cross-exchange validator
                is_valid = await self.cross_validator.validate_price_data(
                    symbol, data.price, data.timestamp
                )
                
                if is_valid:
                    validated_data[symbol] = data
                else:
                    logger.warning(f"Market data validation failed for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error validating market data for {symbol}: {e}")
        
        return validated_data
    
    async def _apply_position_sizing(
        self, 
        trades: List[Any], 
        market_data: Dict[str, MarketData]
    ) -> List[Any]:
        """
        Enhanced position sizing with comprehensive risk management.
        Integrates VaR, drawdown, correlation, and Kelly criterion calculations.
        """
        sized_trades = []
        
        # Get current portfolio positions for risk calculations
        current_portfolio = await self._get_current_portfolio()
        
        # Perform comprehensive risk assessment before sizing
        portfolio_risk_clear = await self._validate_portfolio_risk(current_portfolio, market_data)
        if not portfolio_risk_clear:
            logger.warning("Portfolio risk validation failed - reducing position sizes")
        
        for trade in trades:
            try:
                symbol_data = market_data.get(trade.symbol)
                if not symbol_data:
                    continue
                
                # Enhanced position sizing with multiple risk factors
                sized_trade = await self._calculate_enhanced_position_size(
                    trade, symbol_data, current_portfolio, portfolio_risk_clear
                )
                
                if sized_trade and sized_trade.quantity > 0:
                    sized_trades.append(sized_trade)
                    
            except Exception as e:
                logger.error(f"Error in enhanced position sizing for {trade.symbol}: {e}")
                # Fallback to conservative sizing
                trade.quantity = min(trade.quantity, 0.01)  # 1% max fallback
                sized_trades.append(trade)
        
        return sized_trades
    
    async def _calculate_enhanced_position_size(
        self,
        trade: Any,
        symbol_data: MarketData,
        current_portfolio: Dict[str, float],
        portfolio_risk_clear: bool
    ) -> Optional[Any]:
        """
        Calculate enhanced position size using multiple risk factors.
        """
        try:
            # 1. Get signal confidence from trade
            signal_confidence = getattr(trade, 'confidence', 0.7)
            
            # 2. Calculate risk limits
            from app.utils.risk_calculator import RiskLimits
            risk_limits = RiskLimits(
                max_position_size=self.current_session.parameters.max_position_size,
                max_portfolio_risk=self.current_session.parameters.portfolio_exposure_limit,
                max_drawdown_limit=self.current_session.parameters.max_drawdown_limit,
                max_correlation_exposure=0.6,
                max_leverage=2.0,
                min_position_size=0.001  # 0.1% minimum
            )
            
            # 3. Enhanced position sizing with new calculators
            if self.risk_calc and hasattr(self.risk_calc, 'calculate_optimal_position_size'):
                # Use advanced position size calculator from utils
                from app.utils.position_size_calculator import PositionSizeCalculator
                enhanced_position_calc = PositionSizeCalculator(
                    self.redis_service.redis,
                    self.volatility_calc if hasattr(self, 'volatility_calc') else None
                )
                
                position_result = await enhanced_position_calc.calculate_optimal_position_size(
                    strategy_name=getattr(trade, 'strategy', 'ensemble'),
                    symbol=symbol_data.symbol,
                    signal_confidence=signal_confidence,
                    current_portfolio=current_portfolio,
                    risk_limits=risk_limits,
                    market_data={
                        'price': symbol_data.price,
                        'volume': symbol_data.volume,
                        'volatility': symbol_data.change_24h
                    }
                )
                
                trade.quantity = position_result.final_size
                
                # Add risk metadata to trade
                trade.risk_metrics = {
                    'kelly_fraction': position_result.base_kelly_size,
                    'volatility_adjusted': position_result.volatility_adjusted_size,
                    'correlation_adjusted': position_result.correlation_adjusted_size,
                    'confidence_score': position_result.confidence_score,
                    'adjustments_applied': position_result.adjustments_applied
                }
                
            else:
                # Fallback to basic Kelly criterion
                win_rate = self.current_session.performance.win_rate or 0.55
                avg_win = self.current_session.performance.avg_win or 0.02
                avg_loss = abs(self.current_session.performance.avg_loss or 0.015)
                
                # Kelly formula: f = (bp - q) / b
                if avg_loss > 0:
                    b = avg_win / avg_loss
                    kelly_fraction = (b * win_rate - (1 - win_rate)) / b
                    kelly_fraction = max(0, min(kelly_fraction, risk_limits.max_position_size))
                else:
                    kelly_fraction = risk_limits.max_position_size / 2
                
                # Apply confidence scaling
                trade.quantity = kelly_fraction * signal_confidence
                
                # Apply risk adjustment if portfolio risk not clear
                if not portfolio_risk_clear:
                    trade.quantity *= 0.5  # 50% reduction for risk
            
            # 4. Real-time drawdown check
            if self.drawdown_calc:
                portfolio_value = sum(current_portfolio.values()) * 100000  # Estimate
                drawdown_monitor = await self.drawdown_calc.monitor_real_time_drawdown(
                    new_return=0.0,  # Will be updated post-trade
                    portfolio_value=portfolio_value
                )
                
                # Reduce position size if in significant drawdown
                if drawdown_monitor['current_drawdown'] < -0.05:  # 5% drawdown
                    trade.quantity *= 0.7  # 30% reduction
                    logger.warning(f"Reducing position size due to drawdown: {drawdown_monitor['current_drawdown']:.2%}")
                
                if drawdown_monitor['current_drawdown'] < -0.10:  # 10% drawdown
                    trade.quantity *= 0.5  # Additional 50% reduction
                    logger.error(f"Significant drawdown detected: {drawdown_monitor['current_drawdown']:.2%}")
            
            # 5. Final position size validation
            trade.quantity = max(risk_limits.min_position_size, 
                               min(trade.quantity, risk_limits.max_position_size))
            
            return trade
            
        except Exception as e:
            logger.error(f"Error in enhanced position size calculation: {e}")
            # Conservative fallback
            trade.quantity = 0.01  # 1% fallback
            return trade
    
    async def _validate_portfolio_risk(
        self,
        current_portfolio: Dict[str, float],
        market_data: Dict[str, MarketData]
    ) -> bool:
        """
        Validate overall portfolio risk before position sizing.
        """
        try:
            if not self.risk_calc or not current_portfolio:
                return True  # Default to safe if no risk calc available
            
            # Calculate current portfolio risk metrics
            risk_metrics = await self.risk_calc.calculate_portfolio_risk(current_portfolio)
            
            # Check portfolio VaR limits
            if risk_metrics.portfolio_var_95 > 0.05:  # 5% daily VaR limit
                logger.warning(f"Portfolio VaR exceeds limits: {risk_metrics.portfolio_var_95:.2%}")
                return False
            
            # Check concentration risk
            if risk_metrics.concentration_risk > 0.7:  # 70% concentration limit
                logger.warning(f"Portfolio concentration too high: {risk_metrics.concentration_risk:.2%}")
                return False
            
            # Check current drawdown
            if abs(risk_metrics.current_drawdown) > 0.10:  # 10% drawdown limit
                logger.error(f"Portfolio drawdown exceeds limits: {risk_metrics.current_drawdown:.2%}")
                return False
            
            # Update session performance with risk metrics
            self.current_session.performance.var_95 = risk_metrics.portfolio_var_95
            self.current_session.performance.current_drawdown = risk_metrics.current_drawdown
            self.current_session.performance.max_drawdown = risk_metrics.max_drawdown
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating portfolio risk: {e}")
            return False  # Err on the side of caution
    
    async def _get_current_portfolio(self) -> Dict[str, float]:
        """
        Get current portfolio positions as weights.
        """
        try:
            # This would typically fetch from the execution service or portfolio manager
            # For now, return session's strategy weights as proxy
            if self.current_session and self.current_session.performance.strategy_weights:
                return self.current_session.performance.strategy_weights
            
            # Default equal weights for enabled strategies
            strategies = []
            if self.current_session.parameters.grid_strategy_enabled:
                strategies.append("GridStrategy")
            if self.current_session.parameters.ta_strategy_enabled:
                strategies.append("TechnicalAnalysisStrategy")
            if self.current_session.parameters.trend_strategy_enabled:
                strategies.append("TrendFollowingStrategy")
            
            if strategies:
                equal_weight = 1.0 / len(strategies)
                return {strategy: equal_weight for strategy in strategies}
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting current portfolio: {e}")
            return {}
    
    async def _execute_trades(self, trades: List[Any]) -> List[Trade]:
        """Execute trades through the execution service."""
        executed_trades = []
        
        for trade in trades:
            try:
                start_time = datetime.now()
                
                # Execute trade
                result = await self.execution_service.execute_trade(
                    symbol=trade.symbol,
                    action=trade.action,
                    quantity=trade.quantity,
                    price=trade.price
                )
                
                if result:
                    execution_time = (datetime.now() - start_time).total_seconds() * 1000
                    
                    # Create trade record
                    trade_record = Trade(
                        id=str(uuid.uuid4()),
                        timestamp=datetime.now(),
                        symbol=trade.symbol,
                        action=trade.action,
                        quantity=trade.quantity,
                        price=trade.price,
                        value=trade.quantity * trade.price,
                        strategy="ensemble",
                        confidence=getattr(trade, 'confidence', 0.0),
                        execution_time_ms=execution_time,
                        slippage=0.0,  # Calculate from result
                        commission=0.0,  # Calculate from result
                        status="FILLED"
                    )
                    
                    executed_trades.append(trade_record)
                    self.current_session.trades.append(trade_record)
                
            except Exception as e:
                logger.error(f"Error executing trade: {e}")
                await self._add_alert(
                    level="ERROR",
                    type="EXECUTION",
                    message=f"Trade execution failed: {str(e)}"
                )
        
        return executed_trades
    
    async def _update_session_performance(
        self, 
        trades: List[Trade], 
        market_data: Dict[str, MarketData]
    ):
        """Update session performance metrics."""
        try:
            performance = self.current_session.performance
            
            # Update trade counts
            new_trades = len(trades)
            performance.total_trades += new_trades
            
            # Calculate PnL for new trades
            total_pnl = sum(trade.pnl for trade in trades)
            performance.total_pnl += total_pnl
            
            # Update win/loss statistics
            for trade in trades:
                if trade.pnl > 0:
                    performance.winning_trades += 1
                elif trade.pnl < 0:
                    performance.losing_trades += 1
            
            # Recalculate derived metrics
            total_completed = performance.winning_trades + performance.losing_trades
            if total_completed > 0:
                performance.win_rate = performance.winning_trades / total_completed
            
            # Update timestamps
            performance.last_update = datetime.now()
            
            # Store analytics in Supabase
            await self._store_performance_analytics()
            
        except Exception as e:
            logger.error(f"Error updating session performance: {e}")
    
    async def _monitor_risk_and_alerts(self):
        """
        Enhanced risk monitoring with comprehensive risk calculators.
        Monitors VaR, drawdown, correlation, concentration, and performance metrics.
        """
        try:
            # Get current portfolio for risk calculations
            current_portfolio = await self._get_current_portfolio()
            
            # 1. Enhanced Drawdown Monitoring
            await self._monitor_enhanced_drawdown()
            
            # 2. Portfolio Risk Monitoring (VaR, concentration, etc.)
            await self._monitor_portfolio_risk(current_portfolio)
            
            # 3. Correlation Risk Monitoring
            await self._monitor_correlation_risk(current_portfolio)
            
            # 4. Performance Risk Monitoring
            await self._monitor_performance_risk()
            
            # 5. ML Model Risk Monitoring
            await self._monitor_ml_risk()
            
            # 6. System Resource Monitoring
            await self._monitor_system_risk()
            
        except Exception as e:
            logger.error(f"Error in enhanced risk monitoring: {e}")
    
    async def _monitor_enhanced_drawdown(self):
        """Monitor drawdown using enhanced drawdown calculator."""
        try:
            if not self.drawdown_calc:
                # Fallback to basic drawdown check
                if self.current_session.performance.current_drawdown > self.current_session.parameters.max_drawdown_limit:
                    await self._add_alert(
                        level="CRITICAL",
                        type="RISK",
                        message=f"Maximum drawdown exceeded: {self.current_session.performance.current_drawdown:.2%}"
                    )
                return
            
            # Get session returns for drawdown analysis
            session_returns = await self._get_session_returns()
            if len(session_returns) < 10:
                return  # Need sufficient data
            
            # Calculate comprehensive drawdown metrics
            drawdown_metrics = await self.drawdown_calc.calculate_drawdown_metrics(session_returns)
            
            # Update session performance with enhanced drawdown metrics
            self.current_session.performance.current_drawdown = drawdown_metrics.current_drawdown
            self.current_session.performance.max_drawdown = drawdown_metrics.max_drawdown
            
            # Generate alerts based on drawdown thresholds
            if abs(drawdown_metrics.current_drawdown) > 0.05:  # 5% warning
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message=f"Significant drawdown detected: {drawdown_metrics.current_drawdown:.2%}",
                    details={
                        "current_drawdown": drawdown_metrics.current_drawdown,
                        "max_drawdown": drawdown_metrics.max_drawdown,
                        "drawdown_duration": drawdown_metrics.current_drawdown_duration,
                        "ulcer_index": drawdown_metrics.ulcer_index
                    }
                )
            
            if abs(drawdown_metrics.current_drawdown) > self.current_session.parameters.max_drawdown_limit:
                await self._add_alert(
                    level="CRITICAL",
                    type="RISK",
                    message=f"Maximum drawdown limit exceeded: {drawdown_metrics.current_drawdown:.2%}",
                    details={
                        "limit": self.current_session.parameters.max_drawdown_limit,
                        "actual": drawdown_metrics.current_drawdown,
                        "duration": drawdown_metrics.current_drawdown_duration,
                        "recovery_factor": drawdown_metrics.recovery_factor
                    }
                )
            
            # Check for extended underwater periods
            if drawdown_metrics.time_underwater > 0.7:  # 70% of time underwater
                await self._add_alert(
                    level="WARNING",
                    type="PERFORMANCE",
                    message=f"Extended underwater period: {drawdown_metrics.time_underwater:.1%} of time in drawdown"
                )
                
        except Exception as e:
            logger.error(f"Error in enhanced drawdown monitoring: {e}")
    
    async def _monitor_portfolio_risk(self, current_portfolio: Dict[str, float]):
        """Monitor portfolio risk using enhanced risk calculator."""
        try:
            if not self.risk_calc or not current_portfolio:
                return
            
            # Calculate comprehensive portfolio risk metrics
            risk_metrics = await self.risk_calc.calculate_portfolio_risk(current_portfolio)
            
            # Update session performance with risk metrics
            self.current_session.performance.var_95 = risk_metrics.portfolio_var_95
            self.current_session.performance.volatility = risk_metrics.portfolio_volatility
            
            # Check VaR limits
            if risk_metrics.portfolio_var_95 > 0.03:  # 3% daily VaR warning
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message=f"Portfolio VaR elevated: {risk_metrics.portfolio_var_95:.2%} daily VaR",
                    details={
                        "var_95": risk_metrics.portfolio_var_95,
                        "var_99": risk_metrics.portfolio_var_99,
                        "expected_shortfall_95": risk_metrics.expected_shortfall_95
                    }
                )
            
            if risk_metrics.portfolio_var_95 > 0.05:  # 5% daily VaR critical
                await self._add_alert(
                    level="CRITICAL",
                    type="RISK",
                    message=f"Portfolio VaR exceeds limits: {risk_metrics.portfolio_var_95:.2%} daily VaR",
                    details={
                        "var_95": risk_metrics.portfolio_var_95,
                        "var_99": risk_metrics.portfolio_var_99,
                        "sharpe_ratio": risk_metrics.risk_adjusted_return,
                        "portfolio_beta": risk_metrics.portfolio_beta
                    }
                )
            
            # Check concentration risk
            if risk_metrics.concentration_risk > 0.6:  # 60% concentration warning
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message=f"High portfolio concentration: {risk_metrics.concentration_risk:.1%}",
                    details={
                        "concentration_risk": risk_metrics.concentration_risk,
                        "leverage_ratio": risk_metrics.leverage_ratio
                    }
                )
            
            if risk_metrics.concentration_risk > 0.8:  # 80% concentration critical
                await self._add_alert(
                    level="CRITICAL",
                    type="RISK",
                    message=f"Excessive portfolio concentration: {risk_metrics.concentration_risk:.1%}",
                    details={
                        "concentration_risk": risk_metrics.concentration_risk,
                        "diversification_needed": True
                    }
                )
            
            # Check leverage ratio
            if risk_metrics.leverage_ratio > 1.5:  # 1.5x leverage warning
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message=f"High leverage detected: {risk_metrics.leverage_ratio:.2f}x"
                )
                
        except Exception as e:
            logger.error(f"Error in portfolio risk monitoring: {e}")
    
    async def _monitor_correlation_risk(self, current_portfolio: Dict[str, float]):
        """Monitor correlation risk between strategies."""
        try:
            if not self.correlation_calc or len(current_portfolio) < 2:
                return
            
            # Calculate current correlation matrix
            correlation_matrix = await self.correlation_calc.calculate_correlation_matrix(
                list(current_portfolio.keys())
            )
            
            # Check for high correlations
            high_correlations = []
            for strategy1 in current_portfolio:
                for strategy2 in current_portfolio:
                    if strategy1 != strategy2:
                        correlation = correlation_matrix.get(strategy1, {}).get(strategy2, 0)
                        if abs(correlation) > 0.8:  # 80% correlation threshold
                            high_correlations.append((strategy1, strategy2, correlation))
            
            if high_correlations:
                correlation_details = {
                    "high_correlations": [
                        {
                            "strategy1": s1,
                            "strategy2": s2,
                            "correlation": corr
                        }
                        for s1, s2, corr in high_correlations
                    ]
                }
                
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message=f"High strategy correlations detected: {len(high_correlations)} pairs > 80%",
                    details=correlation_details
                )
                
        except Exception as e:
            logger.error(f"Error in correlation risk monitoring: {e}")
    
    async def _monitor_performance_risk(self):
        """Monitor performance-based risk indicators."""
        try:
            if not self.performance_calc:
                return
            
            # Get session returns for performance analysis
            session_returns = await self._get_session_returns()
            if len(session_returns) < 20:
                return  # Need sufficient data
            
            # Calculate performance metrics
            performance_metrics = await self.performance_calc.calculate_performance_metrics(session_returns)
            
            # Update session performance
            self.current_session.performance.sharpe_ratio = performance_metrics.sharpe_ratio
            
            # Check Sharpe ratio degradation
            if performance_metrics.sharpe_ratio < 0.5 and len(session_returns) > 50:
                await self._add_alert(
                    level="WARNING",
                    type="PERFORMANCE",
                    message=f"Low Sharpe ratio: {performance_metrics.sharpe_ratio:.2f}",
                    details={
                        "sharpe_ratio": performance_metrics.sharpe_ratio,
                        "sortino_ratio": performance_metrics.sortino_ratio,
                        "information_ratio": performance_metrics.information_ratio
                    }
                )
            
            # Check win rate degradation
            if performance_metrics.win_rate < 0.4 and len(session_returns) > 30:
                await self._add_alert(
                    level="WARNING",
                    type="PERFORMANCE",
                    message=f"Low win rate: {performance_metrics.win_rate:.1%}",
                    details={
                        "win_rate": performance_metrics.win_rate,
                        "profit_factor": performance_metrics.profit_factor,
                        "avg_win": performance_metrics.avg_win,
                        "avg_loss": performance_metrics.avg_loss
                    }
                )
                
        except Exception as e:
            logger.error(f"Error in performance risk monitoring: {e}")
    
    async def _monitor_ml_risk(self):
        """Monitor ML model risk indicators."""
        try:
            if not self.weight_optimizer:
                return
            
            # Check model health
            model_info = self.weight_optimizer.get_model_info()
            if model_info.get("status") != "loaded":
                await self._add_alert(
                    level="CRITICAL",
                    type="SYSTEM",
                    message="ML model not loaded - falling back to traditional strategies"
                )
            
            # Check prediction confidence
            if self.ml_performance_metrics["model_confidence"] < 0.6:
                await self._add_alert(
                    level="WARNING",
                    type="SYSTEM",
                    message=f"Low ML model confidence: {self.ml_performance_metrics['model_confidence']:.2f}"
                )
            
            # Check prediction latency
            if self.ml_performance_metrics["prediction_latency_ms"] > 1000:
                await self._add_alert(
                    level="WARNING",
                    type="SYSTEM",
                    message=f"High ML prediction latency: {self.ml_performance_metrics['prediction_latency_ms']:.1f}ms"
                )
                
        except Exception as e:
            logger.error(f"Error in ML risk monitoring: {e}")
    
    async def _monitor_system_risk(self):
        """Monitor system-level risk indicators."""
        try:
            # Check session duration
            if self.current_session:
                session_duration = datetime.now() - self.current_session.start_time
                if session_duration.total_seconds() > 24 * 3600:  # 24 hours
                    await self._add_alert(
                        level="INFO",
                        type="SYSTEM",
                        message=f"Long trading session: {session_duration.total_seconds()/3600:.1f} hours"
                    )
            
            # Check trade execution rate
            if self.current_session and self.current_session.performance.total_trades > 100:
                session_hours = (datetime.now() - self.current_session.start_time).total_seconds() / 3600
                trade_rate = self.current_session.performance.total_trades / session_hours
                
                if trade_rate > 20:  # More than 20 trades per hour
                    await self._add_alert(
                        level="WARNING",
                        type="EXECUTION",
                        message=f"High trade frequency: {trade_rate:.1f} trades/hour"
                    )
                    
        except Exception as e:
            logger.error(f"Error in system risk monitoring: {e}")
    
    async def _get_session_returns(self) -> List[float]:
        """Get session returns for risk calculations."""
        try:
            if not self.current_session or not self.current_session.trades:
                return []
            
            # Calculate returns from trade PnL
            returns = []
            cumulative_balance = 100000  # Starting balance
            
            for trade in self.current_session.trades:
                if trade.status == "FILLED" and trade.pnl != 0:
                    return_pct = trade.pnl / cumulative_balance
                    returns.append(return_pct)
                    cumulative_balance += trade.pnl
            
            return returns
            
        except Exception as e:
            logger.error(f"Error getting session returns: {e}")
            return []
    
    async def _add_alert(self, level: str, type: str, message: str, details: Dict = None):
        """Add alert to current session."""
        if not self.current_session:
            return
        
        alert = Alert(
            id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            level=level,
            type=type,
            message=message,
            details=details or {}
        )
        
        self.current_session.alerts.append(alert)
        
        # Send to Telegram if critical
        if level == "CRITICAL" and self.telegram_monitor:
            await self.telegram_monitor.send_alert(f"🚨 {message}")
        
        logger.info(f"Alert added: {level} - {message}")
    
    async def _cache_session_data(self):
        """Cache current session data to Redis."""
        if not self.current_session:
            return
        
        try:
            session_data = asdict(self.current_session)
            await self.redis_service.setex(
                f"{self.SESSION_CACHE_KEY}:{self.current_session.id}",
                self.config["cache_ttl"],
                json.dumps(session_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Error caching session data: {e}")
    
    async def _generate_session_report(self) -> Dict[str, Any]:
        """Generate comprehensive session report."""
        if not self.current_session:
            return {}
        
        session = self.current_session
        duration = (session.end_time or datetime.now()) - session.start_time
        
        return {
            "session_id": session.id,
            "duration": self._format_duration(duration),
            "performance_summary": {
                "total_return": session.performance.total_return,
                "total_pnl": session.performance.total_pnl,
                "sharpe_ratio": session.performance.sharpe_ratio,
                "max_drawdown": session.performance.max_drawdown,
                "win_rate": session.performance.win_rate,
                "profit_factor": session.performance.profit_factor
            },
            "trading_summary": {
                "total_trades": session.performance.total_trades,
                "winning_trades": session.performance.winning_trades,
                "losing_trades": session.performance.losing_trades,
                "avg_execution_time_ms": session.performance.avg_execution_time_ms,
                "total_commission": sum(trade.commission for trade in session.trades),
                "total_slippage": sum(trade.slippage for trade in session.trades)
            },
            "strategy_attribution": session.performance.strategy_performance,
            "risk_analysis": {
                "max_drawdown": session.performance.max_drawdown,
                "volatility": session.performance.volatility,
                "var_95": session.performance.var_95
            },
            "alerts_summary": {
                "total_alerts": len(session.alerts),
                "critical_alerts": len([a for a in session.alerts if a.level == "CRITICAL"]),
                "error_alerts": len([a for a in session.alerts if a.level == "ERROR"])
            }
        }
    
    def _format_duration(self, duration: timedelta) -> str:
        """Format duration as human-readable string."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    # Additional helper methods implementing the complete PRD workflow
    
    async def _perform_pre_flight_checks(self):
        """Perform comprehensive pre-flight checks before starting session."""
        try:
            logger.info("Performing pre-flight checks...")
            
            # 1. Check Redis connection
            try:
                await self.redis_service.ping()
                logger.info("✓ Redis connection healthy")
            except Exception as e:
                raise RuntimeError(f"Redis connection failed: {e}")
            
            # 2. Check Supabase connection
            if self.supabase_service:
                try:
                    await self.supabase_service.test_connection()
                    logger.info("✓ Supabase connection healthy")
                except Exception as e:
                    logger.warning(f"Supabase connection issue: {e}")
            
            # 3. Validate trading parameters
            params = self.current_session.parameters
            if params.max_position_size <= 0 or params.max_position_size > 1:
                raise ValueError("Invalid max position size")
            if len(params.symbols) == 0:
                raise ValueError("No trading symbols configured")
            
            # 4. Check strategy availability
            strategy_count = sum([
                params.grid_strategy_enabled,
                params.ta_strategy_enabled,
                params.trend_strategy_enabled
            ])
            if strategy_count == 0:
                raise ValueError("No strategies enabled")
            
            # 5. Verify ML model availability
            if hasattr(self.ensemble_manager, 'weight_optimizer'):
                logger.info("✓ ML weight optimizer available")
            else:
                logger.warning("ML weight optimizer not available - using equal weights")
            
            # 6. Test market data access
            test_data = await self._get_current_market_data()
            if not test_data:
                raise RuntimeError("Unable to access market data")
            logger.info(f"✓ Market data access verified for {len(test_data)} symbols")
            
            # 7. Check execution service
            if self.execution_service:
                logger.info("✓ Execution service available")
            else:
                logger.warning("No execution service - running in simulation mode")
            
            # 8. Enhanced Risk Calculator Validations
            await self._validate_risk_calculators()
            
            # 9. Validate Market Conditions
            await self._validate_market_conditions(test_data)
            
            # 10. Account Balance Validation
            await self._validate_account_balance()
            
            logger.info("All enhanced pre-flight checks passed!")
            
        except Exception as e:
            logger.error(f"Pre-flight check failed: {e}")
            raise RuntimeError(f"Pre-flight check failed: {e}")
    
    async def _validate_risk_calculators(self):
        """Validate enhanced risk calculator services."""
        try:
            # 1. Risk Calculator Validation
            if self.risk_calc:
                # Test risk calculation with dummy portfolio
                test_portfolio = {"TEST": 0.1}
                test_risk = await self.risk_calc.calculate_portfolio_risk(test_portfolio)
                if test_risk:
                    logger.info("✓ Enhanced Risk Calculator operational")
                else:
                    logger.warning("⚠ Risk Calculator test failed")
            else:
                logger.warning("⚠ Enhanced Risk Calculator not available")
            
            # 2. Performance Calculator Validation
            if self.performance_calc:
                # Test performance calculation with dummy returns
                test_returns = [0.01, -0.005, 0.02, -0.01, 0.015]
                test_performance = await self.performance_calc.calculate_performance_metrics(test_returns)
                if test_performance:
                    logger.info("✓ Enhanced Performance Calculator operational")
                else:
                    logger.warning("⚠ Performance Calculator test failed")
            else:
                logger.warning("⚠ Enhanced Performance Calculator not available")
            
            # 3. Drawdown Calculator Validation
            if self.drawdown_calc:
                # Test drawdown calculation
                test_returns = [0.01, -0.005, 0.02, -0.01, 0.015]
                test_drawdown = await self.drawdown_calc.calculate_drawdown_metrics(test_returns)
                if test_drawdown:
                    logger.info("✓ Enhanced Drawdown Calculator operational")
                else:
                    logger.warning("⚠ Drawdown Calculator test failed")
            else:
                logger.warning("⚠ Enhanced Drawdown Calculator not available")
            
            # 4. Position Size Calculator Validation
            if hasattr(self, 'position_calc') and self.position_calc:
                logger.info("✓ Position Size Calculator available")
            else:
                logger.warning("⚠ Position Size Calculator not available")
            
            # 5. Correlation Calculator Validation
            if hasattr(self, 'correlation_calc') and self.correlation_calc:
                logger.info("✓ Correlation Calculator available")
            else:
                logger.warning("⚠ Correlation Calculator not available")
                
        except Exception as e:
            logger.error(f"Risk calculator validation failed: {e}")
            # Don't raise exception - these are enhancements, not critical
    
    async def _validate_market_conditions(self, market_data: Dict[str, MarketData]):
        """Validate current market conditions for safe trading."""
        try:
            extreme_volatility_count = 0
            low_volume_count = 0
            
            for symbol, data in market_data.items():
                # Check for extreme volatility (>10% daily change)
                if abs(data.change_24h) > 0.10:
                    extreme_volatility_count += 1
                    logger.warning(f"Extreme volatility detected in {symbol}: {data.change_24h:.2%}")
                
                # Check for low volume (basic heuristic)
                if data.volume < 1000:  # This would be adjusted based on symbol
                    low_volume_count += 1
                    logger.warning(f"Low volume detected in {symbol}: {data.volume}")
            
            # Market condition alerts
            total_symbols = len(market_data)
            if extreme_volatility_count > total_symbols * 0.5:  # More than 50% volatile
                logger.error(f"Market-wide volatility detected: {extreme_volatility_count}/{total_symbols} symbols >10% change")
                await self._add_alert(
                    level="WARNING",
                    type="MARKET",
                    message=f"High market volatility: {extreme_volatility_count}/{total_symbols} symbols >10% change"
                )
            
            if low_volume_count > total_symbols * 0.3:  # More than 30% low volume
                logger.warning(f"Low market liquidity: {low_volume_count}/{total_symbols} symbols with low volume")
                await self._add_alert(
                    level="WARNING",
                    type="MARKET",
                    message=f"Low market liquidity detected: {low_volume_count}/{total_symbols} symbols"
                )
            
            logger.info("✓ Market conditions validated")
            
        except Exception as e:
            logger.error(f"Market conditions validation failed: {e}")
            # Don't raise - this is informational
    
    async def _validate_account_balance(self):
        """Validate account balance and trading capacity."""
        try:
            # This would typically check actual account balance via execution service
            # For now, we'll do basic parameter validation
            
            params = self.current_session.parameters
            
            # Validate position sizing makes sense
            if params.max_position_size * len(params.symbols) > 1.0:
                logger.warning("Position sizing may exceed portfolio capacity")
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message="Position sizing configuration may exceed portfolio capacity"
                )
            
            # Validate exposure limits
            if params.portfolio_exposure_limit > 0.95:  # More than 95% exposure
                logger.warning("Very high portfolio exposure limit configured")
                await self._add_alert(
                    level="WARNING",
                    type="RISK",
                    message=f"High portfolio exposure: {params.portfolio_exposure_limit:.1%}"
                )
            
            # If execution service available, check actual balance
            if self.execution_service and hasattr(self.execution_service, 'get_account_balance'):
                try:
                    # This would be implemented based on the actual execution service
                    logger.info("✓ Account balance validation completed")
                except Exception as e:
                    logger.warning(f"Could not validate account balance: {e}")
            else:
                logger.info("✓ Parameter-based balance validation completed")
                
        except Exception as e:
            logger.error(f"Account balance validation failed: {e}")
            # Don't raise - this is a safety check
    
    async def _initialize_monitoring(self):
        """Initialize comprehensive monitoring systems."""
        try:
            # Initialize performance tracking
            self.current_session.performance.start_time = datetime.now()
            
            # Set up alerts for monitoring thresholds
            await self._add_alert(
                level="INFO",
                type="SYSTEM",
                message="Monitoring systems initialized"
            )
            
            # Initialize W&B tracking if available
            if self.wandb_service:
                await self.wandb_service.start_run(
                    name=f"auto_trading_{self.current_session.id[:8]}",
                    config=asdict(self.current_session.parameters)
                )
            
            # Store initial session data
            await self._store_performance_analytics()
            
        except Exception as e:
            logger.error(f"Failed to initialize monitoring: {e}")
    
    async def _emergency_stop(self):
        """Perform emergency stop procedures with immediate position closure."""
        logger.critical("EMERGENCY STOP INITIATED")
        
        try:
            # 1. Immediately halt trading loop
            self.is_stopping = True
            
            # 2. Close all open positions immediately
            if self.execution_service:
                # This would implement immediate position closure
                await self._close_all_positions(emergency=True)
            
            # 3. Cancel all pending orders
            if self.execution_service:
                await self._cancel_all_orders()
            
            # 4. Clear strategy signals cache
            cache_pattern = f"signals:*"
            await self.redis_service.delete_pattern(cache_pattern)
            
            # 5. Send critical alert
            await self._add_alert(
                level="CRITICAL",
                type="SYSTEM",
                message="EMERGENCY STOP: All trading halted and positions closed"
            )
            
            # 6. Notify via Telegram immediately
            if self.telegram_monitor:
                await self.telegram_monitor.send_alert(
                    "🚨 EMERGENCY STOP ACTIVATED 🚨\n"
                    f"Session: {self.current_session.id[:8]}\n"
                    "All positions closed immediately!"
                )
            
        except Exception as e:
            logger.critical(f"Error during emergency stop: {e}")
    
    async def _graceful_stop(self):
        """Perform graceful stop procedures with controlled position closure."""
        logger.info("Initiating graceful stop...")
        
        try:
            # 1. Stop accepting new signals
            self.is_stopping = True
            
            # 2. Allow current trades to complete (with timeout)
            timeout_seconds = 30
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < timeout_seconds:
                # Check if there are pending trades
                # This would integrate with your execution service
                if not hasattr(self.execution_service, 'has_pending_trades') or \
                   not await self.execution_service.has_pending_trades():
                    break
                await asyncio.sleep(1)
            
            # 3. Close remaining positions gracefully
            if self.execution_service:
                await self._close_all_positions(emergency=False)
            
            # 4. Finalize analytics
            await self._store_performance_analytics()
            
            # 5. Clear cache data
            session_keys = f"auto_trading:session:{self.current_session.id}:*"
            await self.redis_service.delete_pattern(session_keys)
            
            logger.info("Graceful stop completed")
            
        except Exception as e:
            logger.error(f"Error during graceful stop: {e}")
            # Fall back to emergency stop if graceful fails
            await self._emergency_stop()
    
    async def _get_current_strategy_signals(self) -> Dict:
        """Get current strategy signals with caching."""
        try:
            signals = {}
            
            # Get cached signals from Redis
            for strategy_name in ['GridStrategy', 'TechnicalAnalysisStrategy', 'TrendFollowingStrategy']:
                cache_key = f"signals:{strategy_name}:latest"
                cached_signal = await self.redis_service.get(cache_key)
                
                if cached_signal:
                    try:
                        signal_data = json.loads(cached_signal)
                        signals[strategy_name] = signal_data
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse cached signal for {strategy_name}")
            
            return signals
            
        except Exception as e:
            logger.error(f"Error getting strategy signals: {e}")
            return {}
    
    async def _get_system_health(self) -> Dict:
        """Get comprehensive system health status."""
        try:
            health = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "components": {}
            }
            
            # Check Redis
            try:
                await self.redis_service.ping()
                health["components"]["redis"] = "healthy"
            except Exception:
                health["components"]["redis"] = "unhealthy"
                health["status"] = "degraded"
            
            # Check Supabase
            if self.supabase_service:
                try:
                    await self.supabase_service.test_connection()
                    health["components"]["supabase"] = "healthy"
                except Exception:
                    health["components"]["supabase"] = "unhealthy"
                    health["status"] = "degraded"
            
            # Check strategies
            enabled_strategies = []
            if self.current_session:
                params = self.current_session.parameters
                if params.grid_strategy_enabled:
                    enabled_strategies.append("grid")
                if params.ta_strategy_enabled:
                    enabled_strategies.append("technical_analysis")
                if params.trend_strategy_enabled:
                    enabled_strategies.append("trend_following")
            
            health["components"]["strategies"] = {
                "enabled": enabled_strategies,
                "count": len(enabled_strategies)
            }
            
            # Check execution service
            if self.execution_service:
                health["components"]["execution"] = "available"
            else:
                health["components"]["execution"] = "unavailable"
            
            return health
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _generate_session_summary(self, session: TradingSession) -> Dict:
        """Generate comprehensive session summary."""
        try:
            duration = (session.end_time or datetime.now()) - session.start_time
            
            return {
                "session_overview": {
                    "id": session.id,
                    "duration_seconds": duration.total_seconds(),
                    "duration_formatted": self._format_duration(duration),
                    "symbols_traded": session.parameters.symbols,
                    "strategies_used": [
                        name for name, enabled in [
                            ("Grid", session.parameters.grid_strategy_enabled),
                            ("Technical Analysis", session.parameters.ta_strategy_enabled),
                            ("Trend Following", session.parameters.trend_strategy_enabled)
                        ] if enabled
                    ]
                },
                "performance": {
                    "total_pnl": session.performance.total_pnl,
                    "total_return": session.performance.total_return,
                    "sharpe_ratio": session.performance.sharpe_ratio,
                    "max_drawdown": session.performance.max_drawdown,
                    "volatility": session.performance.volatility,
                    "win_rate": session.performance.win_rate,
                    "profit_factor": session.performance.profit_factor
                },
                "trading_activity": {
                    "total_trades": session.performance.total_trades,
                    "winning_trades": session.performance.winning_trades,
                    "losing_trades": session.performance.losing_trades,
                    "avg_execution_time_ms": session.performance.avg_execution_time_ms
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating session summary: {e}")
            return {"error": str(e)}
    
    async def _generate_session_analytics(self, session: TradingSession) -> Dict:
        """Generate detailed session analytics."""
        try:
            analytics = {
                "risk_analysis": {
                    "max_drawdown": session.performance.max_drawdown,
                    "current_drawdown": session.performance.current_drawdown,
                    "volatility": session.performance.volatility,
                    "var_95": session.performance.var_95
                },
                "strategy_attribution": session.performance.strategy_performance,
                "execution_analysis": {
                    "total_commission": sum(trade.commission for trade in session.trades),
                    "total_slippage": sum(trade.slippage for trade in session.trades),
                    "avg_execution_time": session.performance.avg_execution_time_ms
                },
                "alerts_summary": {
                    "total": len(session.alerts),
                    "by_level": defaultdict(int),
                    "by_type": defaultdict(int)
                }
            }
            
            # Aggregate alerts
            for alert in session.alerts:
                analytics["alerts_summary"]["by_level"][alert.level] += 1
                analytics["alerts_summary"]["by_type"][alert.type] += 1
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error generating session analytics: {e}")
            return {"error": str(e)}
    
    async def _handle_trading_error(self, error: Exception):
        """Enhanced trading error handling with recovery logic."""
        try:
            error_msg = str(error)
            logger.error(f"Trading loop error: {error_msg}")
            
            # Add alert
            await self._add_alert(
                level="ERROR",
                type="SYSTEM",
                message=f"Trading loop error: {error_msg}",
                details={"error_type": type(error).__name__}
            )
            
            # Implement recovery logic based on error type
            if "connection" in error_msg.lower():
                # Connection error - pause briefly and retry
                await asyncio.sleep(5)
            elif "api" in error_msg.lower():
                # API error - longer pause
                await asyncio.sleep(10)
            elif "market_data" in error_msg.lower():
                # Market data error - clear cache and retry
                await self.redis_service.delete_pattern("market_data:*")
                await asyncio.sleep(3)
            
            # Send critical errors to Telegram
            if "critical" in error_msg.lower() or "fatal" in error_msg.lower():
                if self.telegram_monitor:
                    await self.telegram_monitor.send_alert(
                        f"🚨 Trading Error: {error_msg[:100]}..."
                    )
            
        except Exception as e:
            logger.critical(f"Error in error handling: {e}")
    
    async def _handle_fatal_error(self, error: Exception):
        """Handle fatal error with emergency procedures."""
        try:
            logger.critical(f"FATAL ERROR: {error}")
            
            # Set session to error state
            if self.current_session:
                self.current_session.status = TradingSessionStatus.ERROR
                
                await self._add_alert(
                    level="CRITICAL",
                    type="SYSTEM",
                    message=f"FATAL ERROR: {str(error)}",
                    details={"error_type": type(error).__name__, "fatal": True}
                )
            
            # Attempt emergency stop
            try:
                await self._emergency_stop()
            except Exception as stop_error:
                logger.critical(f"Emergency stop also failed: {stop_error}")
            
            # Send critical notification
            if self.telegram_monitor:
                await self.telegram_monitor.send_alert(
                    f"💀 FATAL ERROR - SYSTEM HALTED\n"
                    f"Error: {str(error)[:100]}...\n"
                    f"Session: {self.current_session.id[:8] if self.current_session else 'N/A'}"
                )
            
            # Mark system as stopped
            self.is_running = False
            
        except Exception as e:
            logger.critical(f"Failed to handle fatal error: {e}")
    
    async def _perform_health_checks(self):
        """Perform periodic system health checks."""
        try:
            # Check Redis health
            try:
                await self.redis_service.ping()
            except Exception as e:
                await self._add_alert(
                    level="WARNING",
                    type="SYSTEM",
                    message=f"Redis health check failed: {e}"
                )
            
            # Check memory usage and system resources
            if self.current_session:
                # Check if session has been running too long
                max_duration = self.config.get("max_session_duration", 28800)  # 8 hours
                duration = (datetime.now() - self.current_session.start_time).total_seconds()
                
                if duration > max_duration:
                    await self._add_alert(
                        level="WARNING",
                        type="SYSTEM",
                        message=f"Session running for {duration/3600:.1f} hours - consider stopping"
                    )
            
            # Check strategy performance
            await self._monitor_strategy_health()
            
        except Exception as e:
            logger.error(f"Health check error: {e}")
    
    async def _monitor_performance(self):
        """Monitor and log performance metrics."""
        try:
            if not self.current_session:
                return
            
            # Update performance metrics
            await self._update_performance_metrics()
            
            # Log to W&B if available
            if self.wandb_service:
                metrics = {
                    "total_pnl": self.current_session.performance.total_pnl,
                    "total_trades": self.current_session.performance.total_trades,
                    "win_rate": self.current_session.performance.win_rate,
                    "current_drawdown": self.current_session.performance.current_drawdown
                }
                await self.wandb_service.log_metrics(metrics)
            
            # Store in Supabase
            await self._store_performance_analytics()
            
        except Exception as e:
            logger.error(f"Performance monitoring error: {e}")
    
    async def _monitor_system_resources(self):
        """Monitor system resources and performance."""
        try:
            # Monitor cache size and performance
            cache_info = await self.redis_service.info()
            
            # Check if cache is getting too large
            if 'used_memory' in cache_info:
                memory_mb = cache_info['used_memory'] / 1024 / 1024
                if memory_mb > 100:  # 100 MB threshold
                    await self._add_alert(
                        level="WARNING",
                        type="SYSTEM",
                        message=f"Redis memory usage high: {memory_mb:.1f} MB"
                    )
            
        except Exception as e:
            logger.error(f"System resource monitoring error: {e}")
    
    async def _maintain_cache(self):
        """Maintain cache health and cleanup old data."""
        try:
            # Clean up old session data
            current_time = datetime.now()
            cleanup_before = current_time - timedelta(hours=24)
            
            # This would implement cache cleanup logic
            # For now, just log the maintenance
            logger.debug("Performing cache maintenance")
            
        except Exception as e:
            logger.error(f"Cache maintenance error: {e}")
    
    async def _log_trading_cycle_performance(self, ml_trading_signals: Optional[Dict[str, Dict[str, float]]] = None):
        """Log detailed trading cycle performance metrics including ML metrics."""
        try:
            if not self.current_session:
                return
            
            cycle_metrics = {
                "timestamp": datetime.now().isoformat(),
                "session_id": self.current_session.id,
                "total_pnl": self.current_session.performance.total_pnl,
                "total_trades": self.current_session.performance.total_trades,
                "current_drawdown": self.current_session.performance.current_drawdown,
                "strategy_weights": self.current_session.performance.strategy_weights
            }
            
            # Add ML metrics if ml_trading_signals is provided
            if ml_trading_signals is not None:
                # Calculate ML prediction counts per symbol
                ml_prediction_count = len(ml_trading_signals) if ml_trading_signals else 0
                
                # Get ML prediction latency from ML performance metrics
                ml_prediction_latency = self.ml_performance_metrics.get("prediction_latency_ms", 0.0)
                
                # Get ML model confidence
                ml_model_confidence = self.ml_performance_metrics.get("model_confidence", 0.0)
                
                # Extract strategy weights from ML predictions
                ml_strategy_weights = {}
                if ml_trading_signals:
                    for symbol, weights in ml_trading_signals.items():
                        for weight_key, weight_value in weights.items():
                            # Aggregate weights across symbols for overall strategy allocation
                            strategy_name = weight_key.replace('_weight', '')
                            if strategy_name not in ml_strategy_weights:
                                ml_strategy_weights[strategy_name] = 0.0
                            ml_strategy_weights[strategy_name] += weight_value
                    
                    # Average the weights across symbols
                    symbol_count = len(ml_trading_signals)
                    if symbol_count > 0:
                        for strategy in ml_strategy_weights:
                            ml_strategy_weights[strategy] /= symbol_count
                
                # Add ML metrics to cycle metrics
                cycle_metrics.update({
                    "ml_prediction_count": ml_prediction_count,
                    "ml_prediction_latency_ms": ml_prediction_latency,
                    "ml_model_confidence": ml_model_confidence,
                    "ml_strategy_weights": ml_strategy_weights,
                    "ml_total_predictions": self.ml_performance_metrics.get("predictions_count", 0),
                    "ml_model_enabled": self.ml_monitoring_enabled,
                    "ml_last_prediction_time": self.last_ml_prediction_time.isoformat() if self.last_ml_prediction_time else None
                })
                
                logger.debug(f"ML metrics logged: {ml_prediction_count} predictions, "
                           f"{ml_prediction_latency:.2f}ms latency, "
                           f"{ml_model_confidence:.3f} confidence")
            
            # Log to W&B
            if self.wandb_service:
                await self.wandb_service.log_metrics(cycle_metrics)
            
            # Cache recent performance
            cache_key = f"performance:recent:{self.current_session.id}"
            await self.redis_service.setex(
                cache_key,
                300,  # 5 minutes
                json.dumps(cycle_metrics, default=str)
            )
            
        except Exception as e:
            logger.error(f"Trading cycle logging error: {e}")
    
    async def _store_performance_analytics(self):
        """Store comprehensive performance analytics to Supabase."""
        try:
            if not self.supabase_service or not self.current_session:
                return
            
            analytics_data = {
                "session_id": self.current_session.id,
                "timestamp": datetime.now().isoformat(),
                "performance_metrics": asdict(self.current_session.performance),
                "session_parameters": asdict(self.current_session.parameters),
                "system_health": await self._get_system_health(),
                "strategy_signals": await self._get_current_strategy_signals()
            }
            
            # Store in Supabase analytics table
            await self.supabase_service.insert_analytics_data(
                table="trading_sessions_analytics",
                data=analytics_data
            )
            
        except Exception as e:
            logger.error(f"Analytics storage error: {e}")
    
    async def _monitor_strategy_health(self):
        """Monitor individual strategy performance and health."""
        try:
            if not self.current_session:
                return
            
            strategy_performance = self.current_session.performance.strategy_performance
            
            for strategy, performance in strategy_performance.items():
                # Check if strategy is significantly underperforming
                if performance < -1000:  # $1000 loss threshold
                    await self._add_alert(
                        level="WARNING",
                        type="PERFORMANCE",
                        message=f"Strategy {strategy} underperforming: {performance:.2f}"
                    )
            
        except Exception as e:
            logger.error(f"Strategy health monitoring error: {e}")
    
    async def _close_all_positions(self, emergency: bool = False):
        """Close all open positions."""
        try:
            if not self.execution_service:
                logger.warning("No execution service available for position closure")
                return
            
            # This would implement actual position closure
            # The implementation depends on your execution service
            logger.info(f"Closing all positions (emergency={emergency})")
            
        except Exception as e:
            logger.error(f"Error closing positions: {e}")
    
    async def _cancel_all_orders(self):
        """Cancel all pending orders."""
        try:
            if not self.execution_service:
                logger.warning("No execution service available for order cancellation")
                return
            
            # This would implement actual order cancellation
            # The implementation depends on your execution service
            logger.info("Cancelling all pending orders")
            
        except Exception as e:
            logger.error(f"Error cancelling orders: {e}")
    
    async def _update_performance_metrics(self):
        """Update session performance metrics."""
        try:
            if not self.current_session:
                return
            
            performance = self.current_session.performance
            performance.last_update = datetime.now()
            
            # Calculate derived metrics
            if performance.winning_trades + performance.losing_trades > 0:
                performance.win_rate = performance.winning_trades / (performance.winning_trades + performance.losing_trades)
            
            # Update timestamps
            performance.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    # ============================================================================
    # REAL-TIME ML PREDICTION AND LEARNING SYSTEM
    # ============================================================================
    
    async def _continuous_learning_loop(self):
        """
        Continuous learning loop that runs in parallel with trading.
        Collects performance data and improves the ML model in real-time.
        """
        try:
            logger.info("Starting continuous ML learning loop")
            
            while self.is_running and not self.is_stopping:
                try:
                    if self.current_session.status == TradingSessionStatus.PAUSED:
                        await asyncio.sleep(30)  # Check every 30 seconds when paused
                        continue
                    
                    # Analyze prediction accuracy if we have enough data
                    if len(self.prediction_accuracy_history) >= 5:
                        await self._analyze_prediction_accuracy()
                    
                    # Check if model update is needed based on cycle count or accuracy
                    should_update = await self._should_update_model()
                    
                    if should_update and len(self.ml_feedback_buffer) >= self.min_feedback_samples:
                        logger.info("Triggering online model update")
                        success = await self._update_model_online()
                        
                        if success:
                            await self._add_alert(
                                level="INFO",
                                type="ML",
                                message=f"Online model update completed with {len(self.ml_feedback_buffer)} samples"
                            )
                            
                            # Clear feedback buffer after successful update
                            self.ml_feedback_buffer.clear()
                            self.last_model_update_time = datetime.now()
                        else:
                            await self._add_alert(
                                level="WARNING",
                                type="ML",
                                message="Online model update failed"
                            )
                    
                    # Adjust learning rate if adaptive learning is enabled
                    if self.learning_rate_adaptive:
                        await self._adjust_learning_rate()
                    
                    # Clean up old training data (keep last 1000 samples)
                    if len(self.ml_training_data) > 1000:
                        self.ml_training_data = self.ml_training_data[-1000:]
                    
                    # Clean up old feedback buffer (keep last 100 samples)
                    if len(self.ml_feedback_buffer) > 100:
                        self.ml_feedback_buffer = self.ml_feedback_buffer[-100:]
                    
                    # Wait before next learning cycle (every 2 minutes)
                    await asyncio.sleep(120)
                    
                except Exception as e:
                    logger.error(f"Error in continuous learning loop: {e}")
                    await asyncio.sleep(30)  # Brief pause on error
                    
        except asyncio.CancelledError:
            logger.info("Continuous learning loop cancelled")
        except Exception as e:
            logger.error(f"Fatal error in continuous learning loop: {e}")
    
    async def _collect_ml_feedback(
        self, 
        market_data: Dict[str, MarketData],
        ml_predictions: Dict[str, Dict[str, float]],
        executed_trades: List[Trade]
    ):
        """
        Collect trading results and create training data from successful/unsuccessful predictions.
        
        Args:
            market_data: Current market data used for predictions
            ml_predictions: ML predictions made for each symbol
            executed_trades: Actual trades that were executed
        """
        try:
            if not self.online_learning_enabled or not self.weight_optimizer:
                return
            
            current_time = datetime.now()
            
            # For each symbol that had ML predictions
            for symbol, symbol_market_data in market_data.items():
                if symbol not in ml_predictions:
                    continue
                
                predicted_weights = ml_predictions[symbol]
                
                # Find trades executed for this symbol
                symbol_trades = [trade for trade in executed_trades if trade.symbol == symbol]
                
                # Calculate actual performance for this prediction
                total_pnl = sum(trade.pnl for trade in symbol_trades)
                total_value = sum(trade.value for trade in symbol_trades)
                
                # Create market conditions vector
                market_conditions = {
                    'volatility': getattr(symbol_market_data, 'volatility', 0.02),
                    'volume': symbol_market_data.volume,
                    'rsi': getattr(symbol_market_data, 'rsi', 50),
                    'macd': getattr(symbol_market_data, 'macd', 0),
                    'price_change': getattr(symbol_market_data, 'change_24h', 0) / 100,
                    'volatility_ma': getattr(symbol_market_data, 'volatility_ma', 0.02),
                    'volume_ma': getattr(symbol_market_data, 'volume_ma', symbol_market_data.volume),
                    'rsi_ma': getattr(symbol_market_data, 'rsi_ma', 50)
                }
                
                # Calculate prediction accuracy score (0-1)
                if total_value > 0:
                    actual_return = total_pnl / total_value
                    # Score based on whether prediction led to profitable trades
                    accuracy_score = 1.0 if actual_return > 0 else 0.0
                    
                    # Add more nuanced scoring based on magnitude
                    if actual_return > 0.01:  # > 1% return
                        accuracy_score = 1.0
                    elif actual_return > 0:  # Positive but small
                        accuracy_score = 0.7
                    elif actual_return > -0.005:  # Small loss
                        accuracy_score = 0.3
                    else:  # Significant loss
                        accuracy_score = 0.0
                else:
                    accuracy_score = 0.5  # Neutral if no trades
                
                # Create feedback entry
                feedback_entry = {
                    'timestamp': current_time.isoformat(),
                    'symbol': symbol,
                    'market_conditions': market_conditions,
                    'predicted_weights': predicted_weights,
                    'actual_pnl': total_pnl,
                    'actual_return': actual_return if total_value > 0 else 0.0,
                    'accuracy_score': accuracy_score,
                    'trade_count': len(symbol_trades),
                    'total_value': total_value
                }
                
                # Add to feedback buffer
                self.ml_feedback_buffer.append(feedback_entry)
                
                # Add to training data for long-term learning
                training_entry = {
                    'features': list(market_conditions.values()),
                    'target_weights': [
                        predicted_weights.get('grid_weight', 0.33),
                        predicted_weights.get('ta_weight', 0.33),
                        predicted_weights.get('trend_weight', 0.34)
                    ],
                    'performance_score': accuracy_score,
                    'timestamp': current_time.isoformat()
                }
                
                self.ml_training_data.append(training_entry)
                
                # Update prediction accuracy history
                self.prediction_accuracy_history.append(accuracy_score)
                if len(self.prediction_accuracy_history) > 100:  # Keep last 100 predictions
                    self.prediction_accuracy_history = self.prediction_accuracy_history[-100:]
                
                logger.debug(f"ML feedback collected for {symbol}: accuracy={accuracy_score:.3f}, return={actual_return:.4f}")
                
        except Exception as e:
            logger.error(f"Error collecting ML feedback: {e}")
    
    async def _update_model_online(self) -> bool:
        """
        Perform incremental model updates without full retraining.
        Updates the model based on recent feedback data.
        
        Returns:
            True if update was successful, False otherwise
        """
        try:
            if not self.weight_optimizer or not self.online_learning_enabled:
                logger.warning("Online learning not available")
                return False
            
            if len(self.ml_feedback_buffer) < self.min_feedback_samples:
                logger.debug(f"Insufficient feedback samples: {len(self.ml_feedback_buffer)} < {self.min_feedback_samples}")
                return False
            
            logger.info(f"Starting online model update with {len(self.ml_feedback_buffer)} feedback samples")
            
            # Prepare training data from feedback buffer
            X_features = []
            y_weights = []
            sample_weights = []
            
            for feedback in self.ml_feedback_buffer:
                # Extract features
                features = list(feedback['market_conditions'].values())
                X_features.append(features)
                
                # Target weights (adjusted based on performance)
                target_weights = [
                    feedback['predicted_weights'].get('grid_weight', 0.33),
                    feedback['predicted_weights'].get('ta_weight', 0.33),
                    feedback['predicted_weights'].get('trend_weight', 0.34)
                ]
                
                # Adjust target weights based on actual performance
                accuracy = feedback['accuracy_score']
                if accuracy > 0.7:  # Good prediction - reinforce
                    adjusted_weights = target_weights
                elif accuracy < 0.3:  # Bad prediction - adjust away
                    # Distribute weight more evenly
                    adjusted_weights = [0.33, 0.33, 0.34]
                else:  # Neutral - slight adjustment toward equal weights
                    adjusted_weights = [
                        0.7 * target_weights[0] + 0.3 * 0.33,
                        0.7 * target_weights[1] + 0.3 * 0.33,
                        0.7 * target_weights[2] + 0.3 * 0.34
                    ]
                
                y_weights.append(adjusted_weights)
                
                # Sample weight based on accuracy and recency
                days_old = (datetime.now() - datetime.fromisoformat(feedback['timestamp'])).days
                recency_weight = max(0.1, 1.0 - days_old * 0.1)  # Decay over time
                sample_weight = feedback['accuracy_score'] * recency_weight
                sample_weights.append(sample_weight)
            
            # Convert to numpy arrays
            X = np.array(X_features, dtype=np.float32)
            y = np.array(y_weights, dtype=np.float32)
            weights = np.array(sample_weights, dtype=np.float32)
            
            # Perform online learning update
            if hasattr(self.weight_optimizer, 'partial_fit'):
                # Use partial_fit for incremental learning (if available)
                self.weight_optimizer.partial_fit(X, y, sample_weight=weights)
            elif hasattr(self.weight_optimizer, 'fit'):
                # Fallback to regular fit with recent data
                # Combine with some historical training data if available
                if len(self.ml_training_data) > 20:
                    # Add some historical data for stability
                    recent_historical = self.ml_training_data[-20:]
                    for entry in recent_historical:
                        X_features.append(entry['features'])
                        y_weights.append(entry['target_weights'])
                        sample_weights.append(entry['performance_score'] * 0.5)  # Lower weight for historical
                    
                    X = np.array(X_features, dtype=np.float32)
                    y = np.array(y_weights, dtype=np.float32)
                    weights = np.array(sample_weights, dtype=np.float32)
                
                # Retrain with new data
                self.weight_optimizer.model.fit(X, y, sample_weight=weights)
            else:
                logger.warning("Model does not support online learning")
                return False
            
            # Update model performance metrics
            self.ml_performance_metrics["model_confidence"] = np.mean(sample_weights)
            
            # Log to W&B if available
            if self.wandb_service:
                await self.wandb_service.log_metrics({
                    "online_learning/update_samples": len(self.ml_feedback_buffer),
                    "online_learning/avg_accuracy": np.mean([f['accuracy_score'] for f in self.ml_feedback_buffer]),
                    "online_learning/model_confidence": self.ml_performance_metrics["model_confidence"],
                    "online_learning/update_timestamp": datetime.now().timestamp()
                })
            
            logger.info(f"Online model update completed successfully with {len(self.ml_feedback_buffer)} samples")
            return True
            
        except Exception as e:
            logger.error(f"Online model update failed: {e}")
            return False
    
    async def _analyze_prediction_accuracy(self):
        """
        Analyze how well ML predictions correlate with actual trading results.
        Track prediction accuracy over time and identify patterns.
        """
        try:
            if len(self.prediction_accuracy_history) < 5:
                return
            
            # Calculate recent accuracy metrics
            recent_accuracy = np.mean(self.prediction_accuracy_history[-10:])  # Last 10 predictions
            overall_accuracy = np.mean(self.prediction_accuracy_history)
            accuracy_trend = np.mean(self.prediction_accuracy_history[-5:]) - np.mean(self.prediction_accuracy_history[-10:-5])
            
            # Update ML performance metrics
            self.ml_performance_metrics["prediction_accuracy"] = recent_accuracy
            
            # Analyze prediction patterns
            accuracy_volatility = np.std(self.prediction_accuracy_history[-20:]) if len(self.prediction_accuracy_history) >= 20 else 0
            
            # Log accuracy analysis
            logger.info(f"ML Prediction Analysis - Recent: {recent_accuracy:.3f}, "
                       f"Overall: {overall_accuracy:.3f}, Trend: {accuracy_trend:+.3f}, "
                       f"Volatility: {accuracy_volatility:.3f}")
            
            # Generate alerts based on accuracy
            if recent_accuracy < 0.3:
                await self._add_alert(
                    level="WARNING",
                    type="ML",
                    message=f"ML prediction accuracy low: {recent_accuracy:.2%}",
                    details={
                        "recent_accuracy": recent_accuracy,
                        "overall_accuracy": overall_accuracy,
                        "trend": accuracy_trend
                    }
                )
            elif recent_accuracy > 0.8:
                await self._add_alert(
                    level="INFO",
                    type="ML",
                    message=f"ML prediction accuracy high: {recent_accuracy:.2%}",
                    details={
                        "recent_accuracy": recent_accuracy,
                        "overall_accuracy": overall_accuracy,
                        "trend": accuracy_trend
                    }
                )
            
            # Log to W&B if available
            if self.wandb_service:
                await self.wandb_service.log_metrics({
                    "prediction_analysis/recent_accuracy": recent_accuracy,
                    "prediction_analysis/overall_accuracy": overall_accuracy,
                    "prediction_analysis/accuracy_trend": accuracy_trend,
                    "prediction_analysis/accuracy_volatility": accuracy_volatility,
                    "prediction_analysis/sample_count": len(self.prediction_accuracy_history)
                })
            
            # Store accuracy data in Redis for dashboard access
            accuracy_data = {
                "recent_accuracy": recent_accuracy,
                "overall_accuracy": overall_accuracy,
                "accuracy_trend": accuracy_trend,
                "accuracy_volatility": accuracy_volatility,
                "history_length": len(self.prediction_accuracy_history),
                "timestamp": datetime.now().isoformat()
            }
            
            await self.redis_service.setex(
                "ml_accuracy_analysis",
                300,  # 5 minutes
                json.dumps(accuracy_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Error analyzing prediction accuracy: {e}")
    
    async def _should_update_model(self) -> bool:
        """
        Determine if the model should be updated based on various criteria.
        
        Returns:
            True if model should be updated, False otherwise
        """
        try:
            # Check cycle-based updates
            if self.trade_cycle_count % self.model_update_frequency == 0 and self.trade_cycle_count > 0:
                logger.debug(f"Model update triggered by cycle count: {self.trade_cycle_count}")
                return True
            
            # Check time-based updates (every hour)
            if self.last_model_update_time:
                time_since_update = (datetime.now() - self.last_model_update_time).total_seconds()
                if time_since_update > 3600:  # 1 hour
                    logger.debug("Model update triggered by time threshold")
                    return True
            else:
                # First update
                logger.debug("Model update triggered - first update")
                return True
            
            # Check accuracy-based updates
            if len(self.prediction_accuracy_history) >= 10:
                recent_accuracy = np.mean(self.prediction_accuracy_history[-10:])
                if recent_accuracy < self.accuracy_threshold:
                    logger.debug(f"Model update triggered by low accuracy: {recent_accuracy:.3f}")
                    return True
            
            # Check feedback buffer size
            if len(self.ml_feedback_buffer) >= 20:  # Large buffer suggests need for update
                logger.debug(f"Model update triggered by feedback buffer size: {len(self.ml_feedback_buffer)}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking model update criteria: {e}")
            return False
    
    async def _adjust_learning_rate(self):
        """
        Dynamically adjust model update frequency based on prediction accuracy.
        Implements adaptive learning that responds to model performance.
        """
        try:
            if not self.learning_rate_adaptive or len(self.prediction_accuracy_history) < 10:
                return
            
            # Calculate recent accuracy
            recent_accuracy = np.mean(self.prediction_accuracy_history[-10:])
            
            # Adjust update frequency based on accuracy
            if recent_accuracy > 0.8:
                # High accuracy - reduce update frequency to maintain stability
                self.model_update_frequency = min(20, self.model_update_frequency + 2)
                self.min_feedback_samples = min(10, self.min_feedback_samples + 1)
            elif recent_accuracy < 0.4:
                # Low accuracy - increase update frequency for faster adaptation
                self.model_update_frequency = max(5, self.model_update_frequency - 2)
                self.min_feedback_samples = max(3, self.min_feedback_samples - 1)
            elif recent_accuracy < 0.6:
                # Medium-low accuracy - moderate increase in update frequency
                self.model_update_frequency = max(8, self.model_update_frequency - 1)
            
            # Adjust accuracy threshold based on recent performance
            accuracy_volatility = np.std(self.prediction_accuracy_history[-20:]) if len(self.prediction_accuracy_history) >= 20 else 0
            if accuracy_volatility > 0.3:
                # High volatility - be more conservative
                self.accuracy_threshold = min(0.7, self.accuracy_threshold + 0.05)
            else:
                # Low volatility - can be more aggressive
                self.accuracy_threshold = max(0.5, self.accuracy_threshold - 0.02)
            
            logger.debug(f"Adaptive learning adjustment - Update frequency: {self.model_update_frequency}, "
                        f"Min samples: {self.min_feedback_samples}, Accuracy threshold: {self.accuracy_threshold:.3f}")
            
            # Log adaptive learning metrics
            if self.wandb_service:
                await self.wandb_service.log_metrics({
                    "adaptive_learning/update_frequency": self.model_update_frequency,
                    "adaptive_learning/min_feedback_samples": self.min_feedback_samples,
                    "adaptive_learning/accuracy_threshold": self.accuracy_threshold,
                    "adaptive_learning/recent_accuracy": recent_accuracy,
                    "adaptive_learning/accuracy_volatility": accuracy_volatility
                })
            
        except Exception as e:
            logger.error(f"Error adjusting learning rate: {e}")
    
    async def get_ml_learning_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status of the real-time ML learning system.
        
        Returns:
            Dictionary containing learning system status and metrics
        """
        try:
            current_time = datetime.now()
            
            # Calculate accuracy metrics
            recent_accuracy = np.mean(self.prediction_accuracy_history[-10:]) if len(self.prediction_accuracy_history) >= 10 else 0.0
            overall_accuracy = np.mean(self.prediction_accuracy_history) if self.prediction_accuracy_history else 0.0
            
            # Calculate learning metrics
            time_since_last_update = None
            if self.last_model_update_time:
                time_since_last_update = (current_time - self.last_model_update_time).total_seconds()
            
            status = {
                "learning_system": {
                    "enabled": self.online_learning_enabled,
                    "adaptive_learning": self.learning_rate_adaptive,
                    "trade_cycle_count": self.trade_cycle_count,
                    "last_update_time": self.last_model_update_time.isoformat() if self.last_model_update_time else None,
                    "time_since_last_update_seconds": time_since_last_update
                },
                "accuracy_metrics": {
                    "recent_accuracy": recent_accuracy,
                    "overall_accuracy": overall_accuracy,
                    "accuracy_samples": len(self.prediction_accuracy_history),
                    "accuracy_threshold": self.accuracy_threshold
                },
                "learning_parameters": {
                    "model_update_frequency": self.model_update_frequency,
                    "min_feedback_samples": self.min_feedback_samples,
                    "current_feedback_samples": len(self.ml_feedback_buffer),
                    "training_data_samples": len(self.ml_training_data)
                },
                "buffer_status": {
                    "feedback_buffer_size": len(self.ml_feedback_buffer),
                    "training_data_size": len(self.ml_training_data),
                    "prediction_history_size": len(self.prediction_accuracy_history)
                },
                "next_update_conditions": {
                    "cycles_until_update": self.model_update_frequency - (self.trade_cycle_count % self.model_update_frequency),
                    "feedback_samples_needed": max(0, self.min_feedback_samples - len(self.ml_feedback_buffer)),
                    "should_update": await self._should_update_model()
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting ML learning status: {e}")
            return {
                "error": str(e),
                "learning_system": {"enabled": False}
            }
    
    async def configure_ml_learning(
        self,
        online_learning_enabled: Optional[bool] = None,
        learning_rate_adaptive: Optional[bool] = None,
        model_update_frequency: Optional[int] = None,
        min_feedback_samples: Optional[int] = None,
        accuracy_threshold: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Configure the real-time ML learning system parameters.
        
        Args:
            online_learning_enabled: Enable/disable online learning
            learning_rate_adaptive: Enable/disable adaptive learning rate
            model_update_frequency: Frequency of model updates (in trading cycles)
            min_feedback_samples: Minimum feedback samples before model update
            accuracy_threshold: Accuracy threshold for triggering updates
            
        Returns:
            Dictionary with configuration status
        """
        try:
            changes = {}
            
            if online_learning_enabled is not None:
                old_value = self.online_learning_enabled
                self.online_learning_enabled = online_learning_enabled
                changes['online_learning_enabled'] = {'old': old_value, 'new': online_learning_enabled}
            
            if learning_rate_adaptive is not None:
                old_value = self.learning_rate_adaptive
                self.learning_rate_adaptive = learning_rate_adaptive
                changes['learning_rate_adaptive'] = {'old': old_value, 'new': learning_rate_adaptive}
            
            if model_update_frequency is not None:
                old_value = self.model_update_frequency
                self.model_update_frequency = max(1, model_update_frequency)
                changes['model_update_frequency'] = {'old': old_value, 'new': self.model_update_frequency}
            
            if min_feedback_samples is not None:
                old_value = self.min_feedback_samples
                self.min_feedback_samples = max(1, min_feedback_samples)
                changes['min_feedback_samples'] = {'old': old_value, 'new': self.min_feedback_samples}
            
            if accuracy_threshold is not None:
                old_value = self.accuracy_threshold
                self.accuracy_threshold = max(0.0, min(1.0, accuracy_threshold))
                changes['accuracy_threshold'] = {'old': old_value, 'new': self.accuracy_threshold}
            
            # Log configuration changes
            if changes:
                await self._add_alert(
                    level="INFO",
                    type="ML",
                    message=f"ML learning configuration updated",
                    details={"changes": changes}
                )
                
                logger.info(f"ML learning configuration updated: {changes}")
            
            return {
                "success": True,
                "changes": changes,
                "current_config": {
                    "online_learning_enabled": self.online_learning_enabled,
                    "learning_rate_adaptive": self.learning_rate_adaptive,
                    "model_update_frequency": self.model_update_frequency,
                    "min_feedback_samples": self.min_feedback_samples,
                    "accuracy_threshold": self.accuracy_threshold
                }
            }
            
        except Exception as e:
            logger.error(f"Error configuring ML learning: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_ml_performance_metrics(
        self,
        model_accuracy: float = None,
        model_confidence: float = None,
        model_version: str = None,
        drift_score: float = None,
        feature_importance: Dict[str, float] = None,
        training_cost: float = None,
        inference_cost: float = None,
        wandb_run_id: str = None,
        wandb_experiment_name: str = None
    ) -> Dict[str, Any]:
        """
        Update ML performance metrics for the current session
        
        Args:
            model_accuracy: Current model accuracy score
            model_confidence: Current model confidence score
            model_version: Model version identifier
            drift_score: Model drift score
            feature_importance: Current feature importance values
            training_cost: Cost of model training
            inference_cost: Cost of model inference
            wandb_run_id: Weights & Biases run ID
            wandb_experiment_name: W&B experiment name
            
        Returns:
            Dictionary with update status and current ML metrics
        """
        try:
            if not self.active_session:
                return {"success": False, "error": "No active session"}
            
            # Update ML performance metrics
            performance = self.active_session.performance
            
            if model_accuracy is not None:
                performance.ml_model_accuracy = max(0.0, min(1.0, model_accuracy))
            
            if model_confidence is not None:
                performance.ml_model_confidence = max(0.0, min(1.0, model_confidence))
            
            if model_version is not None:
                performance.ml_model_version = str(model_version)
            
            if drift_score is not None:
                performance.ml_drift_score = max(0.0, drift_score)
            
            if feature_importance is not None:
                performance.feature_importance_current = dict(feature_importance)
                # Calculate feature drift if we have previous importance
                if performance.feature_importance_drift:
                    drift = {}
                    for feature, importance in feature_importance.items():
                        old_importance = performance.feature_importance_drift.get(feature, importance)
                        drift[feature] = abs(importance - old_importance)
                    performance.feature_importance_drift = drift
                
                # Update top features
                sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                performance.top_features = [f[0] for f in sorted_features[:10]]
            
            if training_cost is not None:
                performance.ml_training_cost = max(0.0, training_cost)
                performance.ml_total_cost = performance.ml_training_cost + performance.ml_inference_cost
            
            if inference_cost is not None:
                performance.ml_inference_cost = max(0.0, inference_cost)
                performance.ml_total_cost = performance.ml_training_cost + performance.ml_inference_cost
            
            # Update cost per prediction
            if performance.ml_decisions_count > 0:
                performance.cost_per_prediction = performance.ml_total_cost / performance.ml_decisions_count
            
            # Update W&B integration
            if wandb_run_id is not None:
                performance.wandb_run_id = str(wandb_run_id)
            
            if wandb_experiment_name is not None:
                performance.wandb_experiment_name = str(wandb_experiment_name)
            
            # Update timestamps
            performance.model_last_update = datetime.now()
            performance.last_update = datetime.now()
            
            # Calculate ML ROI
            ml_return = performance.ml_vs_traditional_performance.get("ml", 0.0)
            if performance.ml_total_cost > 0:
                performance.ml_roi = (ml_return - performance.ml_total_cost) / performance.ml_total_cost
            
            logger.info(f"ML performance metrics updated for session {self.active_session.id}")
            
            return {
                "success": True,
                "updated_metrics": {
                    "model_accuracy": performance.ml_model_accuracy,
                    "model_confidence": performance.ml_model_confidence,
                    "model_version": performance.ml_model_version,
                    "drift_score": performance.ml_drift_score,
                    "total_cost": performance.ml_total_cost,
                    "ml_roi": performance.ml_roi,
                    "last_update": performance.model_last_update.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error updating ML performance metrics: {e}")
            return {"success": False, "error": str(e)}
    
    async def track_ml_decision(
        self,
        prediction: str,
        confidence: float,
        actual_outcome: str = None,
        profitable: bool = None,
        strategy_name: str = "ml_strategy"
    ) -> Dict[str, Any]:
        """
        Track an ML model decision and its outcome
        
        Args:
            prediction: The model's prediction
            confidence: Confidence score for the prediction
            actual_outcome: Actual outcome (for accuracy tracking)
            profitable: Whether the decision was profitable
            strategy_name: Name of the strategy making the decision
            
        Returns:
            Dictionary with tracking status
        """
        try:
            if not self.active_session:
                return {"success": False, "error": "No active session"}
            
            performance = self.active_session.performance
            
            # Increment decision count
            performance.ml_decisions_count += 1
            
            # Track confidence distribution
            if confidence >= 0.8:
                performance.ml_confidence_buckets["high"] += 1
            elif confidence >= 0.5:
                performance.ml_confidence_buckets["medium"] += 1
            else:
                performance.ml_confidence_buckets["low"] += 1
            
            # Track accuracy if outcome is known
            if actual_outcome is not None:
                if prediction == actual_outcome:
                    performance.ml_decisions_correct += 1
                
                # Update prediction accuracy
                performance.ml_prediction_accuracy = performance.ml_decisions_correct / performance.ml_decisions_count
            
            # Track profitability
            if profitable is not None and profitable:
                performance.ml_decisions_profitable += 1
            
            # Update model state
            if performance.ml_prediction_accuracy < 0.6:
                performance.current_model_state = "degraded"
            elif performance.ml_drift_score > 0.3:
                performance.current_model_state = "updating"
            else:
                performance.current_model_state = "active"
            
            performance.last_update = datetime.now()
            
            logger.debug(f"ML decision tracked: prediction={prediction}, confidence={confidence}, profitable={profitable}")
            
            return {
                "success": True,
                "decision_id": f"{self.active_session.id}_{performance.ml_decisions_count}",
                "current_stats": {
                    "total_decisions": performance.ml_decisions_count,
                    "accuracy": performance.ml_prediction_accuracy,
                    "profitable_rate": performance.ml_decisions_profitable / performance.ml_decisions_count,
                    "model_state": performance.current_model_state
                }
            }
            
        except Exception as e:
            logger.error(f"Error tracking ML decision: {e}")
            return {"success": False, "error": str(e)}
    
    async def update_ml_vs_traditional_performance(
        self,
        ml_return: float = None,
        traditional_return: float = None,
        combined_return: float = None
    ) -> Dict[str, Any]:
        """
        Update ML vs traditional performance comparison
        
        Args:
            ml_return: Return from ML strategies
            traditional_return: Return from traditional strategies
            combined_return: Return from combined approach
            
        Returns:
            Dictionary with update status
        """
        try:
            if not self.active_session:
                return {"success": False, "error": "No active session"}
            
            performance = self.active_session.performance
            
            if ml_return is not None:
                performance.ml_vs_traditional_performance["ml"] = ml_return
            
            if traditional_return is not None:
                performance.ml_vs_traditional_performance["traditional"] = traditional_return
            
            if combined_return is not None:
                performance.ml_vs_traditional_performance["combined"] = combined_return
            
            # Recalculate ML ROI
            ml_perf = performance.ml_vs_traditional_performance["ml"]
            if performance.ml_total_cost > 0:
                performance.ml_roi = (ml_perf - performance.ml_total_cost) / performance.ml_total_cost
            
            performance.last_update = datetime.now()
            
            return {
                "success": True,
                "performance_comparison": performance.ml_vs_traditional_performance,
                "ml_roi": performance.ml_roi
            }
            
        except Exception as e:
            logger.error(f"Error updating ML vs traditional performance: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_ml_session_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive ML analytics for the current session
        
        Returns:
            Dictionary with ML analytics data
        """
        try:
            if not self.active_session:
                return {"success": False, "error": "No active session"}
            
            from app.utils.ml_analytics import MLAnalyticsEngine
            
            # Convert session to dictionary format
            session_dict = {
                "id": self.active_session.id,
                "start_time": self.active_session.start_time.isoformat(),
                "end_time": self.active_session.end_time.isoformat() if self.active_session.end_time else None,
                "status": self.active_session.status,
                "performance": self.active_session.performance.__dict__,
                "trades": [trade.__dict__ for trade in self.active_session.trades],
                "parameters": self.active_session.parameters.__dict__
            }
            
            # Initialize ML analytics engine
            ml_engine = MLAnalyticsEngine()
            
            # Perform comprehensive ML analysis
            ml_performance = await ml_engine.analyze_ml_performance(session_dict)
            strategy_correlation = await ml_engine.analyze_strategy_correlation(session_dict)
            market_impact = await ml_engine.analyze_market_impact(session_dict)
            cost_benefit = await ml_engine.analyze_cost_benefit(session_dict)
            
            return {
                "success": True,
                "session_id": self.active_session.id,
                "ml_performance_analysis": ml_performance,
                "strategy_correlation_analysis": strategy_correlation,
                "market_impact_analysis": market_impact,
                "cost_benefit_analysis": cost_benefit,
                "summary": {
                    "model_accuracy": self.active_session.performance.ml_model_accuracy,
                    "model_confidence": self.active_session.performance.ml_model_confidence,
                    "total_decisions": self.active_session.performance.ml_decisions_count,
                    "profitable_decisions": self.active_session.performance.ml_decisions_profitable,
                    "ml_roi": self.active_session.performance.ml_roi,
                    "total_cost": self.active_session.performance.ml_total_cost,
                    "model_state": self.active_session.performance.current_model_state
                },
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting ML session analytics: {e}")
            return {"success": False, "error": str(e)}