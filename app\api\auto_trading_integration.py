"""
Auto Trading Integration Module - Integrates the Auto Trading Controller with the FastAPI application.
This module provides the dependency injection and startup/shutdown lifecycle management for the
complete auto trading system.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI, Depends, HTTPException
from app.services.auto_trading_controller import AutoTradingController
from app.services.auto_trading_factory import (
    AutoTradingFactory, 
    get_auto_trading_controller,
    set_auto_trading_factory,
    initialize_auto_trading_system
)
from app.api.routes.auto_trading_routes import router as auto_trading_router, set_trading_controller
from app.config.settings import Settings

logger = logging.getLogger(__name__)

# Global controller instance
_controller_instance: Optional[AutoTradingController] = None

@asynccontextmanager
async def auto_trading_lifespan(app: FastAPI):
    """
    Lifespan context manager for Auto Trading Controller.
    Handles startup and shutdown of the auto trading system.
    """
    global _controller_instance
    
    try:
        # Startup
        logger.info("Starting Auto Trading Controller system...")
        
        # Get settings
        settings = Settings()
        
        # Initialize the auto trading system
        _controller_instance = await initialize_auto_trading_system(
            environment=getattr(settings, 'environment', 'development'),
            redis_url=getattr(settings, 'redis_url', 'redis://localhost:6379'),
            supabase_url=getattr(settings, 'supabase_url', None),
            supabase_key=getattr(settings, 'supabase_key', None),
            wandb_api_key=getattr(settings, 'wandb_api_key', None),
            telegram_bot_token=getattr(settings, 'telegram_bot_token', None),
            telegram_chat_id=getattr(settings, 'telegram_chat_id', None),
            enable_real_trading=getattr(settings, 'enable_real_trading', False)
        )
        
        # Set the controller in the routes module
        set_trading_controller(_controller_instance)
        
        logger.info("Auto Trading Controller system started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start Auto Trading Controller: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down Auto Trading Controller system...")
        
        try:
            if _controller_instance and _controller_instance.is_running:
                await _controller_instance.stop_trading_session(emergency=False)
                logger.info("Active trading session stopped")
        except Exception as e:
            logger.error(f"Error stopping trading session: {e}")
        
        logger.info("Auto Trading Controller system shutdown complete")

def get_auto_trading_controller_dependency() -> AutoTradingController:
    """
    FastAPI dependency to get the Auto Trading Controller instance.
    
    Returns:
        AutoTradingController instance
        
    Raises:
        HTTPException: If controller is not available
    """
    global _controller_instance
    
    if _controller_instance is None:
        raise HTTPException(
            status_code=503,
            detail="Auto Trading Controller not initialized"
        )
    
    return _controller_instance

def integrate_auto_trading_routes(app: FastAPI):
    """
    Integrate auto trading routes into the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    # Include the auto trading router
    app.include_router(auto_trading_router)
    
    logger.info("Auto Trading routes integrated")

def setup_auto_trading_system(app: FastAPI, settings: Optional[Settings] = None):
    """
    Complete setup of the Auto Trading system in a FastAPI application.
    
    This function:
    1. Sets up the lifespan manager for startup/shutdown
    2. Integrates the API routes
    3. Configures dependency injection
    
    Args:
        app: FastAPI application instance
        settings: Application settings (optional)
    """
    # Set up lifespan manager
    if not hasattr(app, 'router'):
        # For FastAPI apps without existing lifespan
        app = FastAPI(lifespan=auto_trading_lifespan)
    else:
        # For existing FastAPI apps, we need to compose lifespans
        logger.warning("Adding lifespan to existing FastAPI app - ensure proper composition")
    
    # Integrate routes
    integrate_auto_trading_routes(app)
    
    # Add health check endpoint
    @app.get("/api/trading/system/health")
    async def system_health_check():
        """System health check endpoint"""
        try:
            controller = get_auto_trading_controller_dependency()
            
            health_status = {
                "status": "healthy",
                "auto_trading_available": True,
                "controller_initialized": controller is not None,
                "active_session": controller.is_running if controller else False
            }
            
            return health_status
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "auto_trading_available": False,
                "error": str(e)
            }
    
    logger.info("Auto Trading system setup complete")

# Utility functions for manual initialization (useful for testing)

async def create_development_controller() -> AutoTradingController:
    """
    Create a development Auto Trading Controller for testing.
    
    Returns:
        AutoTradingController instance configured for development
    """
    factory = AutoTradingFactory.create_development_instance()
    return await factory.create_auto_trading_controller()

async def create_production_controller(
    redis_url: str,
    supabase_url: str,
    supabase_key: str,
    **kwargs
) -> AutoTradingController:
    """
    Create a production Auto Trading Controller.
    
    Args:
        redis_url: Redis connection URL
        supabase_url: Supabase project URL
        supabase_key: Supabase API key
        **kwargs: Additional configuration parameters
        
    Returns:
        AutoTradingController instance configured for production
    """
    factory = AutoTradingFactory.create_production_instance(
        redis_url=redis_url,
        supabase_url=supabase_url,
        supabase_key=supabase_key,
        **kwargs
    )
    return await factory.create_auto_trading_controller()

# Testing utilities

class AutoTradingTestClient:
    """Test client for Auto Trading Controller operations"""
    
    def __init__(self, controller: AutoTradingController):
        self.controller = controller
    
    async def start_test_session(self, **params):
        """Start a test trading session"""
        from app.services.auto_trading_controller import TradingParameters
        
        parameters = TradingParameters(**params)
        return await self.controller.start_trading_session(
            parameters=parameters,
            session_name="Test Session"
        )
    
    async def stop_test_session(self):
        """Stop the test trading session"""
        return await self.controller.stop_trading_session(emergency=False)
    
    async def get_test_status(self):
        """Get test session status"""
        return await self.controller.get_session_status()

async def create_test_client() -> AutoTradingTestClient:
    """
    Create a test client for Auto Trading Controller.
    
    Returns:
        AutoTradingTestClient instance
    """
    controller = await create_development_controller()
    return AutoTradingTestClient(controller)

# Example usage and initialization patterns

async def example_manual_initialization():
    """
    Example of manual Auto Trading Controller initialization.
    This shows how to set up the system outside of FastAPI.
    """
    try:
        # Create factory
        factory = AutoTradingFactory.create_development_instance()
        
        # Create controller
        controller = await factory.create_auto_trading_controller()
        
        # Start a test session
        from app.services.auto_trading_controller import TradingParameters
        
        parameters = TradingParameters(
            symbols=["BTCUSDT"],
            max_position_size=0.05,  # 5% max position
            telegram_alerts_enabled=False  # Disable for testing
        )
        
        session_id = await controller.start_trading_session(
            parameters=parameters,
            session_name="Manual Test Session"
        )
        
        print(f"Started trading session: {session_id}")
        
        # Run for a short time
        await asyncio.sleep(30)
        
        # Get status
        status = await controller.get_session_status()
        print(f"Session status: {status}")
        
        # Stop session
        report = await controller.stop_trading_session()
        print(f"Session report: {report}")
        
    except Exception as e:
        logger.error(f"Manual initialization failed: {e}")
        raise

def example_fastapi_integration():
    """
    Example of integrating Auto Trading Controller with FastAPI.
    """
    from fastapi import FastAPI
    
    # Create FastAPI app
    app = FastAPI(
        title="Crypto Trading Bot",
        description="Auto Trading Controller API",
        version="1.0.0",
        lifespan=auto_trading_lifespan
    )
    
    # Setup auto trading system
    setup_auto_trading_system(app)
    
    # Add custom endpoints
    @app.get("/")
    async def root():
        return {"message": "Crypto Auto Trading Controller API"}
    
    @app.get("/trading/quick-status")
    async def quick_status(
        controller: AutoTradingController = Depends(get_auto_trading_controller_dependency)
    ):
        """Quick trading status endpoint"""
        return {
            "active": controller.is_running,
            "session_id": controller.current_session.id if controller.current_session else None
        }
    
    return app

if __name__ == "__main__":
    # Example of running the manual initialization
    asyncio.run(example_manual_initialization())