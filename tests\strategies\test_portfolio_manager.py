"""
Tests for Portfolio Manager functionality.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch

from app.strategies.portfolio_manager import (
    PortfolioManager,
    PositionSizeCalculator,
    AggregatedSignal,
    StrategyMetrics,
    PortfolioMetrics
)
from app.config.settings import Settings


class TestPositionSizeCalculator:
    """Tests for position size calculation logic."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.settings = Mock(spec=Settings)
        self.calculator = PositionSizeCalculator(self.settings)
    
    def test_kelly_criterion_calculation(self):
        """Test Kelly Criterion position sizing."""
        strategy_stats = {
            'win_rate': 0.6,
            'avg_win': 0.03,
            'avg_loss': 0.02
        }
        
        kelly_size = self.calculator.calculate_kelly_size(strategy_stats)
        
        # Kelly formula: f = (bp - q) / b where b = avg_win/avg_loss
        # b = 0.03/0.02 = 1.5, p = 0.6, q = 0.4
        # f = (1.5 * 0.6 - 0.4) / 1.5 = (0.9 - 0.4) / 1.5 = 0.333
        # With 0.25 safety factor: 0.333 * 0.25 = 0.083
        expected_kelly = ((0.03/0.02) * 0.6 - 0.4) / (0.03/0.02) * 0.25
        
        assert abs(kelly_size - expected_kelly) < 0.001
        assert 0.01 <= kelly_size <= 0.20  # Reasonable bounds
    
    def test_kelly_criterion_edge_cases(self):
        """Test Kelly Criterion with edge cases."""
        # Zero win rate
        strategy_stats = {'win_rate': 0.0, 'avg_win': 0.03, 'avg_loss': 0.02}
        kelly_size = self.calculator.calculate_kelly_size(strategy_stats)
        assert kelly_size == 0.01  # Minimum size
        
        # Zero avg_loss
        strategy_stats = {'win_rate': 0.6, 'avg_win': 0.03, 'avg_loss': 0.0}
        kelly_size = self.calculator.calculate_kelly_size(strategy_stats)
        assert kelly_size == 0.01  # Minimum size
    
    def test_volatility_adjustment(self):
        """Test volatility-based position adjustment."""
        base_size = 0.10
        
        # High volatility should reduce position size
        high_vol_size = self.calculator.adjust_for_volatility(base_size, 0.04, 0.02)
        assert high_vol_size < base_size
        
        # Low volatility should increase position size
        low_vol_size = self.calculator.adjust_for_volatility(base_size, 0.01, 0.02)
        assert low_vol_size > base_size
        
        # Size should stay within bounds
        assert 0.005 <= high_vol_size <= 0.25
        assert 0.005 <= low_vol_size <= 0.25
    
    def test_correlation_discount(self):
        """Test correlation-based position discount."""
        base_size = 0.10
        
        # High correlation should reduce position size
        high_corr_size = self.calculator.apply_correlation_discount(base_size, 0.8)
        assert high_corr_size < base_size
        
        # Low correlation should have minimal impact
        low_corr_size = self.calculator.apply_correlation_discount(base_size, 0.2)
        assert low_corr_size >= high_corr_size
    
    def test_risk_limits(self):
        """Test risk limit enforcement."""
        # Test individual trade risk limit
        large_size = 0.50
        limited_size = self.calculator.enforce_risk_limits(large_size, 0.02, 0.10)
        assert limited_size <= 0.02  # Should be capped by max_risk_per_trade
        
        # Test portfolio risk limit
        medium_size = 0.05
        portfolio_limited = self.calculator.enforce_risk_limits(medium_size, 0.10, 0.06)
        assert portfolio_limited <= 0.06 / 3  # Should be capped by portfolio limit


class TestPortfolioManager:
    """Tests for Portfolio Manager functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.settings = Mock(spec=Settings)
        self.settings.ml_weight_optimization_enabled = False
        self.settings.kline_limit = 100
        
        self.execution_service = AsyncMock()
        self.exchange_client = AsyncMock()
        self.ml_optimizer = AsyncMock()
        
        self.portfolio_manager = PortfolioManager(
            settings=self.settings,
            execution_service=self.execution_service,
            exchange_client=self.exchange_client,
            ml_weight_optimizer=self.ml_optimizer
        )
    
    def test_initialization(self):
        """Test Portfolio Manager initialization."""
        assert self.portfolio_manager.symbol == 'BTCUSDT'
        assert self.portfolio_manager.timeframe == '15m'
        assert len(self.portfolio_manager.strategies) == 3
        assert 'grid' in self.portfolio_manager.strategies
        assert 'technical_analysis' in self.portfolio_manager.strategies
        assert 'trend_following' in self.portfolio_manager.strategies
        
        # Check initial weights
        weights = self.portfolio_manager.current_weights
        assert abs(sum(weights.values()) - 1.0) < 0.01  # Should sum to ~1.0
    
    def test_is_running_status(self):
        """Test running status tracking."""
        assert not self.portfolio_manager.is_running()
        
        self.portfolio_manager._is_running = True
        assert self.portfolio_manager.is_running()
    
    @pytest.mark.asyncio
    async def test_get_strategy_weights_ml_disabled(self):
        """Test strategy weight calculation without ML optimization."""
        market_conditions = {
            'volatility': 0.02,
            'trend': 0.3,
            'range_bound': 0.4,
            'volume': 0.8
        }
        
        with patch('app.strategies.portfolio_manager.StrategyScorer') as mock_scorer:
            mock_scorer.select_best_strategy.return_value = (
                'grid',
                {'grid': 0.8, 'technical_analysis': 0.6, 'trend_following': 0.4}
            )
            
            weights = await self.portfolio_manager.get_strategy_weights(market_conditions)
            
            # Weights should be normalized
            assert abs(sum(weights.values()) - 1.0) < 0.01
            assert all(w >= 0 for w in weights.values())
            mock_scorer.select_best_strategy.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_strategy_weights_ml_enabled(self):
        """Test strategy weight calculation with ML optimization."""
        self.portfolio_manager.settings.ml_weight_optimization_enabled = True
        
        market_conditions = {
            'volatility': 0.02,
            'trend': 0.3,
            'range_bound': 0.4,
            'volume': 0.8
        }
        
        # Mock ML optimizer to return valid weights
        ml_weights = {'grid': 0.5, 'technical_analysis': 0.3, 'trend_following': 0.2}
        self.ml_optimizer.get_optimized_weights.return_value = ml_weights
        
        weights = await self.portfolio_manager.get_strategy_weights(market_conditions)
        
        # Should use ML weights
        assert weights == ml_weights
        self.ml_optimizer.get_optimized_weights.assert_called_once_with(market_conditions)
    
    @pytest.mark.asyncio
    async def test_get_strategy_weights_ml_fallback(self):
        """Test fallback to rule-based weights when ML fails."""
        self.portfolio_manager.settings.ml_weight_optimization_enabled = True
        
        market_conditions = {
            'volatility': 0.02,
            'trend': 0.3,
            'range_bound': 0.4,
            'volume': 0.8
        }
        
        # Mock ML optimizer to fail
        self.ml_optimizer.get_optimized_weights.side_effect = Exception("ML failed")
        
        with patch('app.strategies.portfolio_manager.StrategyScorer') as mock_scorer:
            mock_scorer.select_best_strategy.return_value = (
                'grid',
                {'grid': 0.6, 'technical_analysis': 0.5, 'trend_following': 0.4}
            )
            
            weights = await self.portfolio_manager.get_strategy_weights(market_conditions)
            
            # Should fallback to rule-based scoring
            assert abs(sum(weights.values()) - 1.0) < 0.01
            mock_scorer.select_best_strategy.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_ensemble(self):
        """Test ensemble execution logic."""
        # Create sample DataFrame
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103],
            'low': [99, 100, 101],
            'close': [100.5, 101.5, 102.5],
            'volume': [1000, 1100, 1200]
        })
        
        weights = {'grid': 0.5, 'technical_analysis': 0.3, 'trend_following': 0.2}
        
        # Mock strategy execute methods
        for strategy in self.portfolio_manager.strategies.values():
            strategy.execute = AsyncMock()
            strategy.update_params = Mock()
            strategy.params = {}
        
        with patch('app.strategies.portfolio_manager.StrategyScorer') as mock_scorer:
            mock_scorer.get_strategy_parameters.return_value = {}
            
            await self.portfolio_manager.execute_ensemble(df, weights)
            
            # All strategies with sufficient weight should execute
            for strategy_name, strategy in self.portfolio_manager.strategies.items():
                if weights[strategy_name] >= 0.1:  # min_weight_threshold
                    strategy.execute.assert_called_once_with(df)
                    strategy.update_params.assert_called_once()
    
    def test_aggregate_signals(self):
        """Test signal aggregation functionality."""
        strategy_signals = {
            'grid': {'action': 'buy', 'confidence': 0.8},
            'technical_analysis': {'action': 'hold', 'confidence': 0.6},
            'trend_following': {'action': 'sell', 'confidence': 0.7}
        }
        
        aggregated = self.portfolio_manager.aggregate_signals(strategy_signals)
        
        assert isinstance(aggregated, AggregatedSignal)
        assert aggregated.action in ['buy', 'sell', 'hold']
        assert 0.0 <= aggregated.confidence <= 1.0
        assert aggregated.position_size > 0
    
    def test_get_status(self):
        """Test status reporting."""
        self.portfolio_manager._is_running = True
        self.portfolio_manager.current_weights = {'grid': 0.4, 'technical_analysis': 0.3, 'trend_following': 0.3}
        self.portfolio_manager.last_market_conditions = {'volatility': 0.02}
        
        status = self.portfolio_manager.get_status()
        
        assert status['is_running'] is True
        assert status['symbol'] == 'BTCUSDT'
        assert status['timeframe'] == '15m'
        assert 'current_weights' in status
        assert 'market_conditions' in status
        assert 'active_strategies' in status
        assert 'portfolio_metrics' in status
    
    def test_process_klines_data(self):
        """Test klines data processing."""
        # Empty klines
        empty_df = self.portfolio_manager.process_klines_data([])
        assert empty_df.empty
        
        # Valid klines data
        klines = [
            [1234567890000, '100.0', '101.0', '99.0', '100.5', '1000.0', 1234567890999],
            [1234567891000, '100.5', '102.0', '100.0', '101.5', '1100.0', 1234567891999]
        ]
        
        df = self.portfolio_manager.process_klines_data(klines)
        
        assert len(df) == 2
        assert list(df.columns) == ['open', 'high', 'low', 'close', 'volume']
        assert df['close'].iloc[0] == 100.5
        assert df['close'].iloc[1] == 101.5
        assert isinstance(df.index, pd.DatetimeIndex)


class TestDataClasses:
    """Tests for data classes used in portfolio management."""
    
    def test_aggregated_signal(self):
        """Test AggregatedSignal data class."""
        signal = AggregatedSignal(
            action='buy',
            confidence=0.8,
            position_size=0.05,
            stop_loss=99.0,
            take_profit=105.0,
            strategy_contributions={'grid': 0.6, 'ta': 0.4},
            metadata={'market_regime': 'trending'}
        )
        
        assert signal.action == 'buy'
        assert signal.confidence == 0.8
        assert signal.position_size == 0.05
        assert signal.stop_loss == 99.0
        assert signal.take_profit == 105.0
        assert 'grid' in signal.strategy_contributions
        assert 'market_regime' in signal.metadata
    
    def test_strategy_metrics(self):
        """Test StrategyMetrics data class."""
        metrics = StrategyMetrics(
            strategy_name='grid',
            weight=0.4,
            last_signal='buy',
            returns=0.15,
            sharpe_ratio=1.5,
            max_drawdown=0.08,
            win_rate=0.65,
            trades_count=25
        )
        
        assert metrics.strategy_name == 'grid'
        assert metrics.weight == 0.4
        assert metrics.returns == 0.15
        assert metrics.sharpe_ratio == 1.5
    
    def test_portfolio_metrics(self):
        """Test PortfolioMetrics data class."""
        metrics = PortfolioMetrics(
            total_return=0.25,
            sharpe_ratio=1.8,
            max_drawdown=0.12,
            volatility=0.15,
            active_strategies=3
        )
        
        assert metrics.total_return == 0.25
        assert metrics.sharpe_ratio == 1.8
        assert metrics.active_strategies == 3