/**
 * Global Setup for Dynamic Position Optimization E2E Tests
 * Task 2.2.2: Initialize test environment
 */

async function globalSetup(config) {
  console.log('🚀 Starting Dynamic Position Optimization E2E Test Environment...');
  
  // Wait for services to be ready
  const { chromium } = require('@playwright/test');
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Wait for main application health check
  let retries = 30;
  while (retries > 0) {
    try {
      const response = await page.request.get(
        `${config.projects[0].use.baseURL}/health`,
        { timeout: 5000 }
      );
      if (response.ok()) {
        console.log('✅ Main application health check passed');
        break;
      }
    } catch (error) {
      console.log(`⏳ Waiting for main application... (${retries} retries left)`);
    }
    retries--;
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Wait for position optimizer health check
  retries = 20;
  while (retries > 0) {
    try {
      const response = await page.request.get(
        `${config.projects[0].use.baseURL.replace('8000', '8001')}/health`,
        { timeout: 5000 }
      );
      if (response.ok()) {
        console.log('✅ Position optimizer health check passed');
        break;
      }
    } catch (error) {
      console.log(`⏳ Waiting for position optimizer... (${retries} retries left)`);
    }
    retries--;
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Test Redis connectivity
  try {
    const response = await page.request.post(
      `${config.projects[0].use.baseURL}/api/v1/test/redis`,
      { 
        data: { key: 'test', value: 'setup' },
        timeout: 5000 
      }
    );
    if (response.ok()) {
      console.log('✅ Redis connectivity verified');
    }
  } catch (error) {
    console.log('⚠️  Redis connectivity test failed:', error.message);
  }
  
  await browser.close();
  console.log('🎯 E2E test environment ready!');
}

module.exports = globalSetup;