#!/usr/bin/env python3
"""
Real Supabase Service Implementation
Replaces mock Supabase with actual Supabase connection using credentials from .env
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import os
from dotenv import load_dotenv
import aiohttp
import time

# Load environment variables
load_dotenv('.env')

logger = logging.getLogger(__name__)

class RealSupabaseService:
    """Real Supabase service with actual API connection"""
    
    def __init__(self, supabase_url: Optional[str] = None, supabase_key: Optional[str] = None):
        self.supabase_url = supabase_url or os.getenv('SUPABASE_URL')
        self.supabase_key = supabase_key or os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Supabase URL and KEY must be provided via environment variables or parameters")
        
        self.api_url = f"{self.supabase_url}/rest/v1"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
        
        self.session = None
        self.operation_metrics = {
            'store_calls': 0,
            'query_calls': 0,
            'operation_times': [],
            'errors': []
        }
        
        logger.info(f"Initialized Supabase service with URL: {self.supabase_url}")
    
    async def connect(self):
        """Create HTTP session for Supabase API calls"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Test connection with a simple query
            await self.test_connection()
            logger.info("Successfully connected to Supabase")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Supabase: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'connect',
                'error': str(e),
                'timestamp': datetime.now()
            })
            return False
    
    async def disconnect(self):
        """Close HTTP session"""
        try:
            if self.session:
                await self.session.close()
            logger.info("Supabase connection closed")
        except Exception as e:
            logger.error(f"Error closing Supabase connection: {e}")
    
    async def test_connection(self):
        """Test Supabase connection"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            # Try to query a simple endpoint to test connection
            async with self.session.get(
                f"{self.api_url}/trade_executions?select=id&limit=1",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    return True
                elif response.status == 401:
                    raise Exception("Unauthorized - check Supabase credentials")
                elif response.status == 404:
                    # Table might not exist yet, but connection is working
                    logger.info("trade_executions table not found, but connection successful")
                    return True
                else:
                    raise Exception(f"HTTP {response.status}: {await response.text()}")
        except Exception as e:
            raise Exception(f"Connection test failed: {e}")
    
    async def store_trade_execution(self, trade_data: Dict[str, Any]):
        """Store trade execution data in Supabase"""
        start_time = time.time()
        
        try:
            if not self.session:
                await self.connect()
            
            self.operation_metrics['store_calls'] += 1
            
            # Ensure required fields are present
            execution_record = {
                'strategy_name': trade_data.get('strategy_name', 'unknown'),
                'symbol': trade_data.get('symbol', 'UNKNOWN'),
                'action': trade_data.get('action', 'UNKNOWN'),
                'quantity': trade_data.get('quantity', 0),
                'price': trade_data.get('price', 0),
                'timestamp': trade_data.get('timestamp', datetime.now().isoformat()),
                'metadata': trade_data.get('metadata', {}),
                'created_at': datetime.now().isoformat()
            }
            
            async with self.session.post(
                f"{self.api_url}/trade_executions",
                headers=self.headers,
                json=execution_record
            ) as response:
                operation_time = (time.time() - start_time) * 1000
                self.operation_metrics['operation_times'].append(operation_time)
                
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.debug(f"Stored trade execution in {operation_time:.2f}ms")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"Failed to store trade execution: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'store_trade_execution',
                'error': str(e),
                'timestamp': datetime.now()
            })
            raise
    
    async def query_trade_executions(
        self,
        symbol: Optional[str] = None,
        strategy_name: Optional[str] = None,
        limit: int = 100,
        order_by: str = 'created_at.desc'
    ) -> List[Dict[str, Any]]:
        """Query trade executions from Supabase"""
        start_time = time.time()
        
        try:
            if not self.session:
                await self.connect()
            
            self.operation_metrics['query_calls'] += 1
            
            # Build query parameters
            params = {
                'select': '*',
                'limit': str(limit),
                'order': order_by
            }
            
            if symbol:
                params['symbol'] = f'eq.{symbol}'
            if strategy_name:
                params['strategy_name'] = f'eq.{strategy_name}'
            
            # Convert params to query string
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            
            async with self.session.get(
                f"{self.api_url}/trade_executions?{query_string}",
                headers=self.headers
            ) as response:
                operation_time = (time.time() - start_time) * 1000
                self.operation_metrics['operation_times'].append(operation_time)
                
                if response.status == 200:
                    result = await response.json()
                    logger.debug(f"Queried {len(result)} records in {operation_time:.2f}ms")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"Failed to query trade executions: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'query_trade_executions',
                'error': str(e),
                'timestamp': datetime.now()
            })
            return []
    
    async def store_portfolio_metrics(self, metrics_data: Dict[str, Any]):
        """Store portfolio metrics data"""
        start_time = time.time()
        
        try:
            if not self.session:
                await self.connect()
            
            # Prepare portfolio metrics record
            metrics_record = {
                'portfolio_value': metrics_data.get('portfolio_value', 0),
                'total_return': metrics_data.get('total_return', 0),
                'sharpe_ratio': metrics_data.get('sharpe_ratio', 0),
                'max_drawdown': metrics_data.get('max_drawdown', 0),
                'win_rate': metrics_data.get('win_rate', 0),
                'symbol': metrics_data.get('symbol', 'PORTFOLIO'),
                'timestamp': metrics_data.get('timestamp', datetime.now().isoformat()),
                'metadata': metrics_data.get('metadata', {}),
                'created_at': datetime.now().isoformat()
            }
            
            async with self.session.post(
                f"{self.api_url}/portfolio_metrics",
                headers=self.headers,
                json=metrics_record
            ) as response:
                operation_time = (time.time() - start_time) * 1000
                self.operation_metrics['operation_times'].append(operation_time)
                
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.debug(f"Stored portfolio metrics in {operation_time:.2f}ms")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"Failed to store portfolio metrics: {e}")
            self.operation_metrics['errors'].append({
                'operation': 'store_portfolio_metrics',
                'error': str(e),
                'timestamp': datetime.now()
            })
            raise
    
    async def get_analytics_summary(self) -> Dict[str, Any]:
        """Get analytics summary from stored data"""
        try:
            if not self.session:
                await self.connect()
            
            # Get trade execution counts
            async with self.session.get(
                f"{self.api_url}/trade_executions?select=strategy_name,symbol",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    records = await response.json()
                    
                    strategies = [r.get('strategy_name', 'unknown') for r in records]
                    symbols = [r.get('symbol', 'unknown') for r in records]
                    
                    return {
                        'total_records': len(records),
                        'unique_strategies': len(set(strategies)),
                        'unique_symbols': len(set(symbols)),
                        'latest_timestamp': records[0].get('timestamp', '') if records else '',
                        'connection_status': 'connected'
                    }
                else:
                    return {
                        'total_records': 0,
                        'connection_status': 'error',
                        'error': await response.text()
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get analytics summary: {e}")
            return {
                'total_records': 0,
                'connection_status': 'error',
                'error': str(e)
            }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        operation_times = self.operation_metrics['operation_times']
        
        if not operation_times:
            return {
                'avg_latency': 0,
                'max_latency': 0,
                'min_latency': 0,
                'total_operations': 0,
                'store_calls': self.operation_metrics['store_calls'],
                'query_calls': self.operation_metrics['query_calls'],
                'error_count': len(self.operation_metrics['errors'])
            }
        
        return {
            'avg_latency': sum(operation_times) / len(operation_times),
            'max_latency': max(operation_times),
            'min_latency': min(operation_times),
            'total_operations': len(operation_times),
            'store_calls': self.operation_metrics['store_calls'],
            'query_calls': self.operation_metrics['query_calls'],
            'error_count': len(self.operation_metrics['errors'])
        }
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get connection status"""
        return {
            'connected': self.session is not None,
            'supabase_url': self.supabase_url,
            'error_count': len(self.operation_metrics['errors']),
            'last_error': self.operation_metrics['errors'][-1] if self.operation_metrics['errors'] else None
        }

# Async context manager
class SupabaseConnection:
    """Async context manager for Supabase connections"""
    
    def __init__(self, supabase_url: Optional[str] = None, supabase_key: Optional[str] = None):
        self.supabase_service = RealSupabaseService(supabase_url, supabase_key)
    
    async def __aenter__(self):
        await self.supabase_service.connect()
        return self.supabase_service
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.supabase_service.disconnect()

# Factory function
async def create_supabase_service(
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None
) -> RealSupabaseService:
    """Factory function to create and connect Supabase service"""
    service = RealSupabaseService(supabase_url, supabase_key)
    await service.connect()
    return service

# Testing
if __name__ == "__main__":
    async def test_supabase_connection():
        """Test Supabase connection and operations"""
        print("Testing Supabase connection...")
        
        try:
            async with SupabaseConnection() as supabase_service:
                # Test storing a trade execution
                test_trade = {
                    'strategy_name': 'test_strategy',
                    'symbol': 'BTCUSDT',
                    'action': 'BUY',
                    'quantity': 0.001,
                    'price': 50000.0,
                    'timestamp': datetime.now().isoformat(),
                    'metadata': {
                        'test': True,
                        'test_id': 'supabase_connection_test'
                    }
                }
                
                print("Storing test trade execution...")
                result = await supabase_service.store_trade_execution(test_trade)
                print(f"Stored trade: {result}")
                
                # Test querying
                print("Querying trade executions...")
                trades = await supabase_service.query_trade_executions(limit=5)
                print(f"Found {len(trades)} trades")
                
                # Get analytics summary
                print("Getting analytics summary...")
                summary = await supabase_service.get_analytics_summary()
                print(f"Analytics summary: {summary}")
                
                # Get performance stats
                stats = supabase_service.get_performance_stats()
                print(f"Performance stats: {stats}")
                
        except Exception as e:
            print(f"Supabase test failed: {e}")
            import traceback
            traceback.print_exc()
        
        print("Supabase test completed!")
    
    # Run test
    asyncio.run(test_supabase_connection())