# Strategy Ensemble System Environment Configuration

# Application
ENVIRONMENT=development  # development, staging, production
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/ensemble_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Supabase Configuration (Real-time Analytics)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key

# Binance API Configuration
BINANCE_API_KEY=your-binance-api-key
BINANCE_API_SECRET=your-binance-api-secret
BINANCE_TESTNET=true  # Set to false for production

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

# Security
SECRET_KEY=your-super-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# ML Configuration
WANDB_API_KEY=your-wandb-api-key
WANDB_PROJECT=strategy-ensemble
MLFLOW_TRACKING_URI=http://localhost:5000

# ZenML Configuration
ZENML_CONFIG_DIR=/app/.zenml
ZENML_ANALYTICS_OPT_IN=false

# Trading Configuration
DEFAULT_QUOTE_ASSET=USDT
MAX_POSITION_SIZE=0.1  # Maximum position size as fraction of portfolio
RISK_PER_TRADE=0.02    # Risk per trade as fraction of portfolio
MIN_TRADE_AMOUNT=10    # Minimum trade amount in quote asset

# Performance Thresholds
MAX_DRAWDOWN_THRESHOLD=0.15    # 15% maximum drawdown
MIN_SHARPE_RATIO=1.0          # Minimum acceptable Sharpe ratio
REBALANCE_FREQUENCY=3600      # Rebalance frequency in seconds (1 hour)

# MCP Server Configuration
MCP_REDIS_URL=redis://localhost:6379
MCP_SUPABASE_URL=${SUPABASE_URL}
MCP_ZENML_HOST=localhost
MCP_ZENML_PORT=8080

# Docker Configuration
COMPOSE_PROJECT_NAME=ensemble
DOCKER_BUILDKIT=1

# Monitoring & Alerting
SENTRY_DSN=your-sentry-dsn
SLACK_WEBHOOK_URL=your-slack-webhook-url

# Rate Limiting
API_RATE_LIMIT=100  # Requests per minute
AUTH_RATE_LIMIT=20  # Auth requests per minute

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Feature Flags
ENABLE_PAPER_TRADING=true
ENABLE_REAL_TRADING=false
ENABLE_ML_OPTIMIZATION=true
ENABLE_ENSEMBLE_MODE=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=6
BACKUP_RETENTION_DAYS=30