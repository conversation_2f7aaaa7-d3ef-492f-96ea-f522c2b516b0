#!/usr/bin/env python3
"""
Minimal Paper Trading Test
Test paper trading without problematic imports
"""

import asyncio
import json
import logging
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal
import uuid

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PaperTradingBalance:
    """Paper trading account balance"""
    symbol: str
    available: float
    locked: float
    total: float
    timestamp: datetime

@dataclass
class PaperTradingOrder:
    """Paper trading order simulation"""
    order_id: str
    symbol: str
    side: str
    type: str
    quantity: float
    price: float
    filled_quantity: float
    filled_price: float
    status: str
    timestamp: datetime
    filled_timestamp: Optional[datetime]
    simulated_slippage: float
    simulated_fees: float
    cost_optimization_used: bool

class MinimalPaperTradingManager:
    """Minimal paper trading manager without external dependencies"""
    
    def __init__(self, initial_balance_usd: float):
        self.initial_balance = initial_balance_usd
        self.balances = {'USD': PaperTradingBalance('USD', initial_balance_usd, 0, initial_balance_usd, datetime.now())}
        self.positions = {}
        self.orders = {}
        self.order_history = []
        self.trade_count = 0
        
        logger.info(f"Minimal Paper Trading Manager initialized with ${initial_balance_usd:,.2f}")
    
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        total_value = self.balances.get('USD', PaperTradingBalance('USD', 0, 0, 0, datetime.now())).total
        
        return {
            "account": {
                "initial_balance": self.initial_balance,
                "current_value": total_value,
                "total_pnl": total_value - self.initial_balance,
                "pnl_percentage": ((total_value - self.initial_balance) / self.initial_balance) * 100
            },
            "balances": {
                symbol: {
                    "available": balance.available,
                    "locked": balance.locked,
                    "total": balance.total
                }
                for symbol, balance in self.balances.items()
            },
            "positions": {},
            "trading_stats": {
                "total_trades": self.trade_count,
                "win_rate": 0.0,
                "total_fees_paid": 0.0,
                "cost_savings": 0.0
            }
        }
    
    async def execute_paper_trade(self, symbol: str, side: str, quantity: float, price: float) -> Optional[PaperTradingOrder]:
        """Execute a simple paper trade"""
        
        # Validate basic parameters
        if quantity <= 0:
            logger.error("Invalid quantity")
            return None
        
        # Calculate simple fees (0.1%)
        order_value = quantity * price
        fees = order_value * 0.001
        
        # Create order
        order = PaperTradingOrder(
            order_id=str(uuid.uuid4()),
            symbol=symbol,
            side=side,
            type="MARKET",
            quantity=quantity,
            price=price,
            filled_quantity=quantity,
            filled_price=price,
            status="FILLED",
            timestamp=datetime.now(),
            filled_timestamp=datetime.now(),
            simulated_slippage=0.0,
            simulated_fees=fees,
            cost_optimization_used=False
        )
        
        # Update balances
        if side == "BUY":
            usd_balance = self.balances.get('USD', PaperTradingBalance('USD', 0, 0, 0, datetime.now()))
            if usd_balance.available >= (order_value + fees):
                usd_balance.available -= (order_value + fees)
                usd_balance.total = usd_balance.available + usd_balance.locked
                self.balances['USD'] = usd_balance
                
                # Add to positions (simplified)
                base_symbol = symbol.replace('USDT', '').replace('USD', '')
                if base_symbol not in self.balances:
                    self.balances[base_symbol] = PaperTradingBalance(base_symbol, 0, 0, 0, datetime.now())
                
                self.balances[base_symbol].available += quantity
                self.balances[base_symbol].total = self.balances[base_symbol].available
                
                self.trade_count += 1
                self.order_history.append(order)
                
                logger.info(f"Paper trade executed: {side} {quantity} {symbol} @ {price}")
                return order
            else:
                logger.error("Insufficient balance")
                return None
        
        return order

async def test_minimal_paper_trading():
    """Test minimal paper trading functionality"""
    logger.info("Testing minimal paper trading...")
    
    try:
        # Create manager
        manager = MinimalPaperTradingManager(100000.0)
        
        # Test portfolio summary
        summary = await manager.get_portfolio_summary()
        assert summary["account"]["initial_balance"] == 100000.0
        assert summary["account"]["current_value"] == 100000.0
        logger.info("✅ Portfolio summary test passed")
        
        # Test trade execution
        order = await manager.execute_paper_trade("BTCUSDT", "BUY", 1.0, 50000.0)
        assert order is not None
        assert order.status == "FILLED"
        logger.info("✅ Trade execution test passed")
        
        # Test updated portfolio
        updated_summary = await manager.get_portfolio_summary()
        assert updated_summary["trading_stats"]["total_trades"] == 1
        assert updated_summary["balances"]["USD"]["available"] < 100000.0
        logger.info("✅ Portfolio update test passed")
        
        return True
        
    except Exception as e:
        logger.error(f"Minimal paper trading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("=" * 60)
    print("MINIMAL PAPER TRADING TEST")
    print("=" * 60)
    
    success = await test_minimal_paper_trading()
    
    if success:
        print("\n✅ MINIMAL PAPER TRADING TEST SUCCESSFUL")
        print("The core paper trading logic works without external dependencies!")
    else:
        print("\n❌ MINIMAL PAPER TRADING TEST FAILED")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)