# Shrimp Task Manager MCP

## Overview

The Shrimp Task Manager MCP is an intelligent task management system based on the Model Context Protocol (MCP). It provides a structured workflow for AI agents to plan, analyze, and execute complex programming tasks.

## Features

- **Task Planning and Analysis**: Deep understanding and analysis of complex task requirements
- **Intelligent Task Decomposition**: Automatically break down large tasks into manageable smaller tasks
- **Dependency Management**: Precisely handle dependencies between tasks, ensuring correct execution order
- **Execution Status Tracking**: Real-time monitoring of task execution progress and status
- **Task Completeness Verification**: Ensure task results meet expected requirements
- **Task Complexity Assessment**: Automatically evaluate task complexity and provide optimal handling suggestions
- **Automatic Task Summary Updates**: Automatically generate summaries upon task completion, optimizing memory performance
- **Task Memory Function**: Automatically backup task history, providing long-term memory and reference capabilities

## Installation

The Shrimp Task Manager MCP has been installed and configured in the Crypto_App_V2 project. The installation includes:

1. **Code Repository**: Located at `mcp-shrimp-task-manager/`
2. **Compiled JavaScript Files**: Located at `mcp-shrimp-task-manager/dist/index.js`
3. **Data Directory**: Located at `mcp-shrimp-task-manager/data/`

## Configuration

The Shrimp Task Manager MCP has been configured in the following files:

1. **.clauderc**: Configuration for Claude AI integration
2. **.vscode/settings.json**: Configuration for VS Code
3. **start_mcp_servers.ps1**: Script to start MCP servers
4. **stop_mcp_servers.ps1**: Script to stop MCP servers

## Usage

### Starting the Shrimp Task Manager MCP Server

You can start the Shrimp Task Manager MCP server using the following command:

```powershell
.\start_mcp_servers.ps1 -Servers shrimptaskmanager
```

Or start all MCP servers including the Shrimp Task Manager:

```powershell
.\start_mcp_servers.ps1 -Servers all
```

### Stopping the Shrimp Task Manager MCP Server

You can stop the Shrimp Task Manager MCP server using the following command:

```powershell
.\stop_mcp_servers.ps1 -Servers shrimptaskmanager
```

Or stop all MCP servers including the Shrimp Task Manager:

```powershell
.\stop_mcp_servers.ps1 -Servers all
```

### Checking the Status of the Shrimp Task Manager MCP Server

You can check the status of the Shrimp Task Manager MCP server using the following command:

```powershell
.\check_mcp_servers.ps1
```

## Task Management Workflow

The Shrimp Task Manager MCP provides a complete task management lifecycle:

1. **Start Planning** `plan_task`: Analyze task problems, determine requirement scope
2. **In-depth Analysis** `analyze_task`: Check existing codebase to avoid duplicate work
3. **Solution Reflection** `reflect_task`: Critically review analysis results, ensure comprehensive solutions
4. **Task Decomposition** `split_tasks`: Break down complex tasks into smaller ones, establish clear dependencies
5. **Task List** `list_tasks`: View all tasks and their execution status
6. **Execute Task** `execute_task`: Execute specific tasks while assessing complexity
7. **Result Verification** `verify_task`: Comprehensively check task completion
8. **Task Completion** `complete_task`: Mark tasks as complete and generate reports, automatically update summaries
9. **Task Management** `delete_task`: Manage incomplete tasks (completed tasks remain in the system)
10. **Query Tasks** `query_task`: Search for related tasks in past memories using keywords
11. **Display Task** `get_task_detail`: Display complete task guidance

## Available Tools

The Shrimp Task Manager MCP provides the following tools:

| Category                | Tool Name         | Description                            |
| ----------------------- | ----------------- | -------------------------------------- |
| **Task Planning**       | `plan_task`       | Start planning tasks                   |
| **Task Analysis**       | `analyze_task`    | In-depth analysis of task requirements |
| **Solution Assessment** | `reflect_task`    | Reflect and improve solution concepts  |
| **Task Management**     | `split_tasks`     | Break tasks into subtasks              |
|                         | `list_tasks`      | Display all tasks and status           |
|                         | `query_task`      | Search and list tasks                  |
|                         | `get_task_detail` | Display complete task details          |
|                         | `delete_task`     | Delete incomplete tasks                |
| **Task Execution**      | `execute_task`    | Execute specific tasks                 |
|                         | `verify_task`     | Verify task completion                 |
|                         | `complete_task`   | Mark tasks as completed                |

## System Prompt Guidance

### TaskPlanner Mode

```
You are a professional task planning expert who must interact with users, analyze their requirements, and collect project-related information, ultimately using mcp_shrimp_task_manager_plan_task to establish tasks. When tasks are established, you must summarize and inform users to use the Task Execution Model for task execution. You must focus on task planning and are prohibited from using mcp_shrimp_task_manager_execute_task to execute tasks. Serious warning: you are a task planning expert, you cannot directly modify code, you can only plan tasks, and you cannot directly modify code, you can only plan tasks.
```

### TaskExecutor Mode

```
You are a professional task execution expert. When users specify task execution, use mcp_shrimp_task_manager_execute_task for task execution. Without specified tasks, use mcp_shrimp_task_manager_list_tasks to find unexecuted tasks and execute them. After completion, you must summarize and inform users. You can only execute one task at a time, and unless explicitly instructed by users, you are prohibited from proceeding to the next task after a single task is completed. If users request "continuous mode," execute all tasks in sequence.
```

## Task Memory Function

The Shrimp Task Manager MCP has long-term memory capabilities, automatically saving task execution history and providing reference experiences when planning new tasks.

### Key Features

- The system automatically backs up tasks to the memory directory
- Backup files are named in chronological order, in the format tasks_backup_YYYY-MM-DDThh-mm-ss.json
- Task planning Agents automatically receive guidance on how to use the memory function

### Advantages and Benefits

- **Avoid Duplicate Work**: Reference past tasks, no need to solve similar problems from scratch
- **Learn from Successful Experiences**: Utilize proven effective solutions, improve development efficiency
- **Learning and Improvement**: Identify past mistakes or inefficient solutions, continuously optimize workflows
- **Knowledge Accumulation**: Form a continuously expanding knowledge base as system usage increases

## Troubleshooting

If you encounter issues with the Shrimp Task Manager MCP:

1. **Check if the server is running**: Use the `check_mcp_servers.ps1` script to verify that the Shrimp Task Manager MCP server is running.
2. **Check the log file**: Check the `mcp_servers.log` file for any error messages.
3. **Restart the server**: Stop and restart the Shrimp Task Manager MCP server using the `stop_mcp_servers.ps1` and `start_mcp_servers.ps1` scripts.
4. **Check the data directory**: Ensure that the `mcp-shrimp-task-manager/data/` directory exists and contains the `tasks.json` file.
5. **Check the environment variables**: Ensure that the `DATA_DIR` environment variable is set correctly in the configuration files.

## References

- [Shrimp Task Manager MCP GitHub Repository](https://github.com/cjo4m06/mcp-shrimp-task-manager)
- [Smithery Package](https://smithery.ai/server/@cjo4m06/mcp-shrimp-task-manager)
