{"ast": null, "code": "var _jsxFileName = \"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\nimport { CssBaseline } from '@mui/material';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport Login from './pages/Login';\nimport AutoTradeControl from './pages/AutoTradeControl'; // Renamed import\nimport MLOptimization from './pages/MLOptimization'; // ML Optimization page\nimport ProtectedRoute from './components/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 29\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/trading\",\n              element: /*#__PURE__*/_jsxDEV(AutoTradeControl, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/ml\",\n              element: /*#__PURE__*/_jsxDEV(MLOptimization, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/trading\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/trading\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "CssBaseline", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "<PERSON><PERSON>", "AutoTradeControl", "MLOptimization", "ProtectedRoute", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { CssBaseline } from '@mui/material';\r\nimport { AuthProvider } from './contexts/AuthContext';\r\nimport { ThemeProvider } from './contexts/ThemeContext';\r\nimport Login from './pages/Login';\r\nimport AutoTradeControl from './pages/AutoTradeControl'; // Renamed import\r\nimport MLOptimization from './pages/MLOptimization'; // ML Optimization page\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\n\r\nfunction App() {\r\n  return (\r\n    <AuthProvider>\r\n      <ThemeProvider>\r\n        <CssBaseline />\r\n        <BrowserRouter>\r\n          <Routes>\r\n            <Route path=\"/login\" element={<Login />} />\r\n\r\n            {/* Protected routes */}\r\n            <Route element={<ProtectedRoute />}>\r\n              {/* Render AutoTradeControl directly */}\r\n              <Route path=\"/trading\" element={<AutoTradeControl />} />\r\n              {/* ML Optimization page */}\r\n              <Route path=\"/ml\" element={<MLOptimization />} />\r\n            </Route>\r\n\r\n            {/* Redirect to trading by default */}\r\n            <Route path=\"/\" element={<Navigate to=\"/trading\" replace />} />\r\n            <Route path=\"*\" element={<Navigate to=\"/trading\" replace />} />\r\n          </Routes>\r\n        </BrowserRouter>\r\n      </ThemeProvider>\r\n    </AuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACzE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,gBAAgB,MAAM,0BAA0B,CAAC,CAAC;AACzD,OAAOC,cAAc,MAAM,wBAAwB,CAAC,CAAC;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,YAAY;IAAAS,QAAA,eACXF,OAAA,CAACN,aAAa;MAAAQ,QAAA,gBACZF,OAAA,CAACR,WAAW;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfN,OAAA,CAACZ,aAAa;QAAAc,QAAA,eACZF,OAAA,CAACX,MAAM;UAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;YAACiB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAER,OAAA,CAACL,KAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG3CN,OAAA,CAACV,KAAK;YAACkB,OAAO,eAAER,OAAA,CAACF,cAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,gBAEjCF,OAAA,CAACV,KAAK;cAACiB,IAAI,EAAC,UAAU;cAACC,OAAO,eAAER,OAAA,CAACJ,gBAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAExDN,OAAA,CAACV,KAAK;cAACiB,IAAI,EAAC,KAAK;cAACC,OAAO,eAAER,OAAA,CAACH,cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAGRN,OAAA,CAACV,KAAK;YAACiB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAER,OAAA,CAACT,QAAQ;cAACkB,EAAE,EAAC,UAAU;cAACC,OAAO;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DN,OAAA,CAACV,KAAK;YAACiB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAER,OAAA,CAACT,QAAQ;cAACkB,EAAE,EAAC,UAAU;cAACC,OAAO;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEnB;AAACK,EAAA,GAzBQV,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}