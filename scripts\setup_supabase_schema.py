#!/usr/bin/env python3
"""
Supabase Database Schema Setup Script
Creates all necessary tables for the Strategy Ensemble System
"""

import os
import sys
import asyncio
from datetime import datetime

# Add the app directory to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'app'))

from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Database schema SQL from our service
SCHEMA_SQL = """
-- Portfolio performance tracking
CREATE TABLE IF NOT EXISTS portfolio_metrics (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_pnl DECIMAL(15, 6) NOT NULL,
    sharpe_ratio DECIMAL(8, 4),
    max_drawdown DECIMAL(8, 4),
    win_rate DECIMAL(8, 4),
    strategy_contributions JSONB,
    correlation_matrix JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy performance tracking
CREATE TABLE IF NOT EXISTS strategy_performance (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    pnl DECIMAL(15, 6) NOT NULL,
    return_pct DECIMAL(8, 6),
    trades_count INTEGER DEFAULT 0,
    win_rate DECIMAL(8, 4),
    confidence_score DECIMAL(8, 4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trade execution tracking
CREATE TABLE IF NOT EXISTS trades (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    action VARCHAR(10) NOT NULL, -- BUY, SELL
    quantity DECIMAL(20, 8) NOT NULL,
    price DECIMAL(15, 6) NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    pnl DECIMAL(15, 6),
    return_pct DECIMAL(8, 6),
    fees DECIMAL(15, 6),
    confidence DECIMAL(8, 4),
    weight_used DECIMAL(8, 4),
    position_size DECIMAL(8, 4),
    market_conditions JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time alerts log
CREATE TABLE IF NOT EXISTS alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    metrics_snapshot JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy weights history
CREATE TABLE IF NOT EXISTS strategy_weights (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    grid_weight DECIMAL(8, 4),
    technical_analysis_weight DECIMAL(8, 4),
    trend_following_weight DECIMAL(8, 4),
    model_confidence DECIMAL(8, 4),
    market_regime VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_timestamp ON portfolio_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_timestamp ON strategy_performance(strategy_name, timestamp);
CREATE INDEX IF NOT EXISTS idx_trades_strategy_timestamp ON trades(strategy_name, executed_at);
CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, executed_at);
CREATE INDEX IF NOT EXISTS idx_alerts_type_timestamp ON alerts(alert_type, triggered_at);
CREATE INDEX IF NOT EXISTS idx_strategy_weights_timestamp ON strategy_weights(timestamp);
"""

def test_supabase_connection():
    """Test connection to Supabase and return client"""
    print("🔗 Testing Supabase connection...")
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Missing Supabase credentials in .env file")
        print("Required variables: SUPABASE_URL, SUPABASE_KEY")
        return None
    
    try:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Test connection with a simple query
        result = supabase.table('_supabase_tables').select("*").limit(1).execute()
        
        print(f"✅ Successfully connected to Supabase!")
        print(f"📍 Project URL: {SUPABASE_URL}")
        print(f"🔑 Using anon key: {SUPABASE_KEY[:50]}...")
        
        return supabase
        
    except Exception as e:
        print(f"❌ Failed to connect to Supabase: {e}")
        return None

def create_database_schema(supabase: Client):
    """Create database schema using Supabase SQL execution"""
    print("\n🗄️ Creating database schema...")
    
    try:
        # Split SQL into individual statements
        statements = [stmt.strip() for stmt in SCHEMA_SQL.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement:
                print(f"📝 Executing statement {i+1}/{len(statements)}...")
                try:
                    # Use RPC to execute SQL
                    result = supabase.rpc('sql', {'query': statement}).execute()
                    print(f"✅ Statement {i+1} executed successfully")
                except Exception as stmt_error:
                    print(f"⚠️ Statement {i+1} failed (might already exist): {stmt_error}")
        
        print("✅ Database schema creation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create database schema: {e}")
        print("💡 You may need to run this SQL manually in the Supabase SQL editor")
        return False

def test_table_operations(supabase: Client):
    """Test basic table operations"""
    print("\n🧪 Testing table operations...")
    
    try:
        # Test portfolio metrics insert
        test_metrics = {
            'total_pnl': 1000.50,
            'sharpe_ratio': 1.25,
            'max_drawdown': -0.05,
            'win_rate': 0.75,
            'strategy_contributions': {
                'GridStrategy': 350.25,
                'TechnicalAnalysisStrategy': 325.75,
                'TrendFollowingStrategy': 324.50
            },
            'correlation_matrix': {
                'GridStrategy': {'GridStrategy': 1.0, 'TechnicalAnalysisStrategy': 0.3}
            }
        }
        
        result = supabase.table('portfolio_metrics').insert(test_metrics).execute()
        if result.data:
            print("✅ Portfolio metrics insert successful")
            test_id = result.data[0]['id']
        
        # Test trade execution insert
        test_trade = {
            'strategy_name': 'GridStrategy',
            'symbol': 'BTCUSDT',
            'action': 'BUY',
            'quantity': 0.1,
            'price': 43000.00,
            'pnl': 50.25,
            'return_pct': 0.00117,
            'fees': 4.30,
            'confidence': 0.85,
            'weight_used': 0.33,
            'position_size': 0.25,
            'market_conditions': {
                'volatility': 0.025,
                'volume': 1500000,
                'rsi': 65.5
            }
        }
        
        result = supabase.table('trades').insert(test_trade).execute()
        if result.data:
            print("✅ Trade execution insert successful")
        
        # Test data retrieval
        result = supabase.table('portfolio_metrics').select('*').limit(5).execute()
        print(f"✅ Data retrieval successful: {len(result.data)} records found")
        
        # Test strategy weights insert
        test_weights = {
            'grid_weight': 0.35,
            'technical_analysis_weight': 0.30,
            'trend_following_weight': 0.35,
            'model_confidence': 0.92,
            'market_regime': 'trending'
        }
        
        result = supabase.table('strategy_weights').insert(test_weights).execute()
        if result.data:
            print("✅ Strategy weights insert successful")
        
        print("✅ All table operations working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Table operations test failed: {e}")
        return False

def display_setup_summary():
    """Display setup summary and next steps"""
    print("\n" + "="*60)
    print("🎉 SUPABASE SETUP COMPLETE!")
    print("="*60)
    print("\n📋 What was created:")
    print("   • portfolio_metrics table (real-time portfolio tracking)")
    print("   • strategy_performance table (individual strategy metrics)")
    print("   • trades table (trade execution tracking)")
    print("   • alerts table (system alerts and notifications)")
    print("   • strategy_weights table (ML model weight history)")
    print("   • Performance indexes for fast queries")
    
    print("\n🔄 Next steps:")
    print("   1. Your Supabase service will now use real data instead of mock")
    print("   2. Run tests to verify integration: python -m pytest tests/services/test_supabase_service.py")
    print("   3. Start the ensemble system to see live data")
    
    print("\n🔧 Configuration:")
    print(f"   • Project URL: {SUPABASE_URL}")
    print(f"   • Using anon key for secure access")
    print(f"   • Real-time subscriptions ready")
    
    print("\n📊 Monitor your data:")
    print("   • Supabase Dashboard → Table Editor")
    print("   • Real-time data will appear as the system runs")
    print("   • Set up Row Level Security (RLS) policies if needed")

def main():
    """Main setup function"""
    print("🚀 SUPABASE SETUP FOR STRATEGY ENSEMBLE SYSTEM")
    print("=" * 60)
    
    # Test connection
    supabase = test_supabase_connection()
    if not supabase:
        print("\n❌ Setup failed: Cannot connect to Supabase")
        print("\n🔧 Troubleshooting:")
        print("   1. Check your .env file has correct SUPABASE_URL and SUPABASE_KEY")
        print("   2. Verify the URL format: https://your-project.supabase.co")
        print("   3. Ensure you're using the 'anon/public' key, not service_role")
        print("   4. Check your Supabase project is active")
        return False
    
    # Note about schema creation
    print("\n⚠️  IMPORTANT: Schema Creation")
    print("Supabase requires SQL execution via the dashboard for security.")
    print("We'll test the connection and show you the SQL to run manually.")
    
    # Test table operations with existing tables (if any)
    print("\n🧪 Testing existing tables...")
    try:
        # Try to query existing tables
        result = supabase.table('portfolio_metrics').select('*').limit(1).execute()
        print("✅ portfolio_metrics table exists and accessible")
        
        # If tables exist, test operations
        if test_table_operations(supabase):
            display_setup_summary()
            return True
            
    except Exception as e:
        print(f"📋 Tables don't exist yet: {e}")
        print("\n🔧 MANUAL SETUP REQUIRED:")
        print("1. Go to your Supabase Dashboard")
        print("2. Navigate to SQL Editor")
        print("3. Create a new query")
        print("4. Copy and paste the following SQL:")
        print("\n" + "="*50)
        print("-- STRATEGY ENSEMBLE SYSTEM SCHEMA")
        print(SCHEMA_SQL)
        print("="*50)
        print("\n5. Click 'Run' to execute the SQL")
        print("6. Run this script again to test the setup")
        
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)