# Redis Trading MCP Installation Script for Windows
# Run with: powershell -ExecutionPolicy Bypass -File install.ps1

Write-Host "🚀 Installing Redis Trading MCP Server..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
    
    # Extract major version number
    $majorVersion = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
    if ($majorVersion -lt 18) {
        Write-Host "❌ Node.js version 18+ required. Current version: $nodeVersion" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    Write-Host "   Download from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "✅ npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm is not installed. Please install npm first." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Blue
try {
    npm install
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "🔨 Building TypeScript..." -ForegroundColor Blue
try {
    npm run build
    Write-Host "✅ Build completed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

# Test Redis connection
Write-Host "🔍 Testing Redis connection..." -ForegroundColor Blue
try {
    # Try to connect to Redis using node
    $testScript = @"
const redis = require('redis');
const client = redis.createClient({ url: 'redis://localhost:6379' });
client.connect().then(() => {
    console.log('Redis connection successful');
    process.exit(0);
}).catch((err) => {
    console.log('Redis connection failed:', err.message);
    process.exit(1);
});
"@
    
    $testScript | node
    Write-Host "✅ Redis is running and accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Redis connection failed. Please ensure Redis is running:" -ForegroundColor Yellow
    Write-Host "   Option 1: Docker - docker run -d -p 6379:6379 redis:7-alpine" -ForegroundColor Yellow
    Write-Host "   Option 2: WSL2 - wsl redis-server" -ForegroundColor Yellow
    Write-Host "   Option 3: Windows Redis - Download from GitHub releases" -ForegroundColor Yellow
}

# Check for global installation flag
if ($args -contains "--global") {
    Write-Host "🔗 Creating global npm link..." -ForegroundColor Blue
    try {
        npm link
        Write-Host "✅ MCP server available globally as 'mcp-redis-trading'" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Global link failed (may require admin privileges)" -ForegroundColor Yellow
    }
}

Write-Host "✅ Installation complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Ensure Redis is running (see options above)" -ForegroundColor White
Write-Host "2. Test the MCP server: npm run dev" -ForegroundColor White
Write-Host "3. Add to Claude configuration (see config-example.json)" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Configuration:" -ForegroundColor Cyan
Write-Host "   Set REDIS_URL environment variable (default: redis://localhost:6379)" -ForegroundColor White
Write-Host "   Set REDIS_DB environment variable (default: 0)" -ForegroundColor White