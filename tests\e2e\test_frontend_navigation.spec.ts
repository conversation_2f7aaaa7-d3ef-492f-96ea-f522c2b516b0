import { test, expect } from '@playwright/test';

test.describe('Frontend Navigation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*\/trading/);
  });

  test('should navigate to ML Optimization page', async ({ page }) => {
    // Navigate to ML page via URL (sidebar may not be visible initially)
    await page.goto('http://localhost:3000/ml');
    
    // Verify we're on ML page
    await expect(page).toHaveURL(/.*\/ml/);
    await expect(page.locator('h1')).toContainText('ML Weight Optimization');
    
    // Verify navigation sidebar is visible
    await expect(page.getByRole('link', { name: 'Trading Control' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Session Reports' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'ML Optimization' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Strategy Ensemble' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Binance Account' })).toBeVisible();
    
    // Verify ML page specific elements
    await expect(page.getByText('Enable ML Weight Optimization')).toBeVisible();
    await expect(page.getByText('Model Information')).toBeVisible();
    await expect(page.getByText('Training Parameters')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Train Model' })).toBeVisible();
    
    // Test form inputs
    await expect(page.getByRole('textbox', { name: 'Symbol' })).toBeVisible();
    await expect(page.getByRole('textbox', { name: 'Timeframe' })).toBeVisible();
    await expect(page.getByRole('spinbutton', { name: 'Lookback Days' })).toHaveValue('90');
    await expect(page.getByRole('spinbutton', { name: 'Window Size' })).toHaveValue('10');
    await expect(page.getByRole('spinbutton', { name: 'Training Timesteps' })).toHaveValue('100000');
  });

  test('should navigate to Session Reports page', async ({ page }) => {
    await page.goto('http://localhost:3000/reports');
    
    // Verify we're on Session Reports page
    await expect(page).toHaveURL(/.*\/reports/);
    await expect(page.locator('h4')).toContainText('Session Reports & Analytics');
    
    // Verify tab navigation
    await expect(page.getByRole('tab', { name: 'Live Monitoring' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Session History' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Analytics' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Comparison' })).toBeVisible();
    
    // Verify Live Monitoring tab is selected by default
    await expect(page.getByRole('tab', { name: 'Live Monitoring' })).toHaveAttribute('aria-selected', 'true');
    
    // Test tab switching
    await page.getByRole('tab', { name: 'Analytics' }).click();
    await expect(page.getByRole('tab', { name: 'Analytics' })).toHaveAttribute('aria-selected', 'true');
    await expect(page.getByText('Performance Analytics')).toBeVisible();
  });

  test('should navigate to Binance Account page', async ({ page }) => {
    await page.goto('http://localhost:3000/binance');
    
    // Verify we're on Binance page
    await expect(page).toHaveURL(/.*\/binance/);
    await expect(page.locator('h1')).toContainText('Binance Futures Account');
    
    // Verify breadcrumb navigation
    await expect(page.getByRole('link', { name: 'Dashboard' })).toBeVisible();
    await expect(page.getByText('Binance Account')).toBeVisible();
    
    // Verify page elements
    await expect(page.getByText('Real-time account management')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Account Information' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Settings' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Help' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Refresh Status' })).toBeVisible();
    
    // Should show service unavailable error (expected in test environment)
    await expect(page.getByText('Service Unavailable')).toBeVisible();
  });

  test('should navigate between pages using sidebar links', async ({ page }) => {
    // Start from ML page where sidebar is visible
    await page.goto('http://localhost:3000/ml');
    
    // Navigate to Trading Control
    await page.getByRole('link', { name: 'Trading Control' }).click();
    await expect(page).toHaveURL(/.*\/trading/);
    await expect(page.locator('h1')).toContainText('Automated Trading Control');
    
    // Navigate to Session Reports
    await page.goto('http://localhost:3000/ml'); // Go back to ML to use sidebar
    await page.getByRole('link', { name: 'Session Reports' }).click();
    await expect(page).toHaveURL(/.*\/reports/);
    await expect(page.locator('h4')).toContainText('Session Reports & Analytics');
    
    // Navigate to Binance Account
    await page.goto('http://localhost:3000/ml'); // Go back to ML to use sidebar
    await page.getByRole('link', { name: 'Binance Account' }).click();
    await expect(page).toHaveURL(/.*\/binance/);
    await expect(page.locator('h1')).toContainText('Binance Futures Account');
  });

  test('should handle logout functionality', async ({ page }) => {
    await page.goto('http://localhost:3000/ml');
    
    // Test logout from header button (if visible)
    const logoutButton = page.getByRole('button', { name: 'Logout' });
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      await expect(page).toHaveURL(/.*\/login/);
      await expect(page.locator('h2')).toContainText('Login');
    }
  });
});