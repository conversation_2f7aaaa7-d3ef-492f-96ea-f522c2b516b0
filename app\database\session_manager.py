from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession
import logging

logger = logging.getLogger(__name__)

@asynccontextmanager
async def get_managed_session(db_session_factory):
    """Provide a transactional scope around a series of operations."""
    session: AsyncSession = db_session_factory()
    try:
        yield session
        await session.commit()
    except Exception as e:
        logger.error(f"Session rollback due to error: {e}", exc_info=True)
        await session.rollback()
        raise
    finally:
        await session.close() 