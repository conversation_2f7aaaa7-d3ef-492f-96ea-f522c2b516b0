{"ast": null, "code": "// Keep only needed types\nimport apiClient from './apiService';\n\n// Use the apiClient from apiService.ts which already has token handling\n\n// Authentication API (kept because Login.tsx and ProtectedRoute are kept)\nexport const authAPI = {\n  login: async credentials => {\n    const params = new URLSearchParams();\n    params.append('username', credentials.username);\n    params.append('password', credentials.password);\n    try {\n      const response = await apiClient.post('/token', params, {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2;\n      // Simplified error handling for brevity\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message || 'Login failed';\n      throw new Error(typeof message === 'string' ? message : JSON.stringify(message));\n    }\n  },\n  refreshToken: async refreshToken => {\n    try {\n      const response = await apiClient.post('/refresh-token', {\n        refresh_token: refreshToken\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4;\n      // Simplified error handling for brevity\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message || 'Token refresh failed';\n      throw new Error(typeof message === 'string' ? message : JSON.stringify(message));\n    }\n  }\n};\n\n// Trading API - Minimal version for AutoTradeControl\nexport const tradingAPI = {\n  // Assumes GET /trading/status returns { enabled: boolean }\n  getAutoTradingStatus: async () => {\n    const response = await apiClient.get('/api/trading/status');\n    return response.data;\n  },\n  // Assumes POST /trading/enable takes { symbol: string } (optional?) and returns success/fail\n  enableAutoTrading: async symbol => {\n    // Backend API might not need the symbol, adjust if necessary\n    const response = await apiClient.post('/api/trading/enable', {\n      symbol\n    });\n    return response.data;\n  },\n  // Assumes POST /trading/disable returns success/fail\n  disableAutoTrading: async () => {\n    const response = await apiClient.post('/api/trading/disable');\n    return response.data;\n  }\n};\n\n// Function to fetch account statistics\nexport const getAccountStatistics = async () => {\n  try {\n    // Assuming apiClient handles auth headers via interceptors\n    const response = await apiClient.get('/api/account/statistics');\n    return response.data; // Return the data part of the response\n  } catch (error) {\n    console.error('Error fetching account statistics:', error);\n    // Re-throw the error or handle it as needed for the UI\n    throw error;\n  }\n};\n\n// Trading API - Additional endpoints\nexport const getActiveTrades = async () => {\n  const response = await apiClient.get('/api/trading/active-trades');\n  return response.data;\n};\nexport const getRecentTrades = async () => {\n  const response = await apiClient.get('/api/trading/recent-trades');\n  return response.data;\n};\n\n// Market API\nexport const getMarketTicker = async symbol => {\n  const response = await apiClient.get(`/api/market/ticker?symbol=${encodeURIComponent(symbol)}`);\n  return response.data;\n};\n\n// ML API\nexport const mlAPI = {\n  trainModel: async payload => {\n    const response = await apiClient.post('/api/ml/train', payload);\n    return response.data;\n  },\n  getOptimizedWeights: async payload => {\n    const response = await apiClient.post('/api/ml/weights', payload);\n    return response.data;\n  },\n  backtestMLWeights: async payload => {\n    const response = await apiClient.post('/api/ml/backtest', payload);\n    return response.data;\n  },\n  getModelInfo: async () => {\n    const response = await apiClient.get('/api/ml/info');\n    return response.data;\n  },\n  getMLStatus: async () => {\n    const response = await apiClient.get('/api/ml/status');\n    return response.data;\n  },\n  toggleMLOptimization: async enabled => {\n    const response = await apiClient.post('/api/ml/toggle', {\n      enabled\n    });\n    return response.data;\n  }\n};\n\n// Session Reports API\nexport const sessionReportsAPI = {\n  getSessionsSummary: async (days, status) => {\n    const params = new URLSearchParams();\n    params.append('days', days.toString());\n    if (status) {\n      params.append('status', status);\n    }\n    const response = await apiClient.get(`/api/sessions/summary?${params.toString()}`);\n    return response.data;\n  },\n  getLiveSessionReport: async () => {\n    const response = await apiClient.get('/api/sessions/live');\n    return response.data;\n  },\n  getSessionReport: async sessionId => {\n    const response = await apiClient.get(`/api/sessions/${sessionId}/report`);\n    return response.data;\n  },\n  getSessionAnalytics: async sessionId => {\n    const response = await apiClient.get(`/api/sessions/${sessionId}/analytics`);\n    return response.data;\n  }\n};\n\n// Ensemble API\nexport const ensembleAPI = {\n  getEnsembleData: async () => {\n    const response = await apiClient.get('/api/ensemble/data');\n    return response.data;\n  },\n  getEnsembleStatus: async () => {\n    const response = await apiClient.get('/api/ensemble/status');\n    return response.data;\n  },\n  updateEnsembleConfig: async config => {\n    const response = await apiClient.post('/api/ensemble/config', config);\n    return response.data;\n  }\n};\nexport { apiClient }; // Export the api instance", "map": {"version": 3, "names": ["apiClient", "authAPI", "login", "credentials", "params", "URLSearchParams", "append", "username", "password", "response", "post", "headers", "data", "error", "_error$response", "_error$response$data", "_error$response2", "message", "detail", "Error", "JSON", "stringify", "refreshToken", "refresh_token", "_error$response3", "_error$response3$data", "_error$response4", "tradingAPI", "getAutoTradingStatus", "get", "enableAutoTrading", "symbol", "disableAutoTrading", "getAccountStatistics", "console", "getActiveTrades", "getRecentTrades", "getMarketTicker", "encodeURIComponent", "mlAPI", "trainModel", "payload", "getOptimizedWeights", "backtestMLWeights", "getModelInfo", "getMLStatus", "toggleMLOptimization", "enabled", "sessionReportsAPI", "getSessionsSummary", "days", "status", "toString", "getLiveSessionReport", "getSessionReport", "sessionId", "getSessionAnalytics", "ensembleAPI", "getEnsembleData", "getEnsembleStatus", "updateEnsembleConfig", "config"], "sources": ["/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/api.ts"], "sourcesContent": ["import { LoginCredentials, AuthResponse, MarketTicker, ManagedTrade, MLTrainingResponse, MLWeightsResponse, MLBacktestResponse, MLModelInfoResponse, MLStatusResponse, MLToggleResponse } from '../types'; // Keep only needed types\nimport apiClient from './apiService';\n\n// Use the apiClient from apiService.ts which already has token handling\n\n// Authentication API (kept because Login.tsx and ProtectedRoute are kept)\nexport const authAPI = {\n  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    const params = new URLSearchParams();\n    params.append('username', credentials.username);\n    params.append('password', credentials.password);\n\n    try {\n      const response = await apiClient.post<AuthResponse>('/token', params, {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        }\n      });\n      return response.data;\n    } catch (error: any) {\n      // Simplified error handling for brevity\n      const message = error.response?.data?.detail || error.response?.data || error.message || 'Login failed';\n      throw new Error(typeof message === 'string' ? message : JSON.stringify(message));\n    }\n  },\n\n  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {\n    try {\n      const response = await apiClient.post<AuthResponse>('/refresh-token', { refresh_token: refreshToken });\n      return response.data;\n    } catch (error: any) {\n      // Simplified error handling for brevity\n      const message = error.response?.data?.detail || error.response?.data || error.message || 'Token refresh failed';\n      throw new Error(typeof message === 'string' ? message : JSON.stringify(message));\n    }\n  },\n};\n\n// Trading API - Minimal version for AutoTradeControl\nexport const tradingAPI = {\n  // Assumes GET /trading/status returns { enabled: boolean }\n  getAutoTradingStatus: async (): Promise<{ enabled: boolean }> => {\n    const response = await apiClient.get<{ enabled: boolean }>('/api/trading/status');\n    return response.data;\n  },\n\n  // Assumes POST /trading/enable takes { symbol: string } (optional?) and returns success/fail\n  enableAutoTrading: async (symbol: string): Promise<any> => {\n    // Backend API might not need the symbol, adjust if necessary\n    const response = await apiClient.post('/api/trading/enable', { symbol });\n    return response.data;\n  },\n\n  // Assumes POST /trading/disable returns success/fail\n  disableAutoTrading: async (): Promise<any> => {\n    const response = await apiClient.post('/api/trading/disable');\n    return response.data;\n  },\n};\n\n// Function to fetch account statistics\nexport const getAccountStatistics = async () => {\n  try {\n    // Assuming apiClient handles auth headers via interceptors\n    const response = await apiClient.get('/api/account/statistics');\n    return response.data; // Return the data part of the response\n  } catch (error) {\n    console.error('Error fetching account statistics:', error);\n    // Re-throw the error or handle it as needed for the UI\n    throw error;\n  }\n};\n\n// Trading API - Additional endpoints\nexport const getActiveTrades = async (): Promise<ManagedTrade[]> => {\n  const response = await apiClient.get<ManagedTrade[]>('/api/trading/active-trades');\n  return response.data;\n};\n\nexport const getRecentTrades = async (): Promise<ManagedTrade[]> => {\n  const response = await apiClient.get<ManagedTrade[]>('/api/trading/recent-trades');\n  return response.data;\n};\n\n// Market API\nexport const getMarketTicker = async (symbol: string): Promise<MarketTicker> => {\n  const response = await apiClient.get<MarketTicker>(`/api/market/ticker?symbol=${encodeURIComponent(symbol)}`);\n  return response.data;\n};\n\n// ML API\nexport const mlAPI = {\n  trainModel: async (payload: any): Promise<MLTrainingResponse> => {\n    const response = await apiClient.post<MLTrainingResponse>('/api/ml/train', payload);\n    return response.data;\n  },\n  getOptimizedWeights: async (payload: any): Promise<MLWeightsResponse> => {\n    const response = await apiClient.post<MLWeightsResponse>('/api/ml/weights', payload);\n    return response.data;\n  },\n  backtestMLWeights: async (payload: any): Promise<MLBacktestResponse> => {\n    const response = await apiClient.post<MLBacktestResponse>('/api/ml/backtest', payload);\n    return response.data;\n  },\n  getModelInfo: async (): Promise<MLModelInfoResponse> => {\n    const response = await apiClient.get<MLModelInfoResponse>('/api/ml/info');\n    return response.data;\n  },\n  getMLStatus: async (): Promise<MLStatusResponse> => {\n    const response = await apiClient.get<MLStatusResponse>('/api/ml/status');\n    return response.data;\n  },\n  toggleMLOptimization: async (enabled: boolean): Promise<MLToggleResponse> => {\n    const response = await apiClient.post<MLToggleResponse>('/api/ml/toggle', { enabled });\n    return response.data;\n  },\n};\n\n// Session Reports API\nexport const sessionReportsAPI = {\n  getSessionsSummary: async (days: number, status?: string): Promise<any> => {\n    const params = new URLSearchParams();\n    params.append('days', days.toString());\n    if (status) {\n      params.append('status', status);\n    }\n    const response = await apiClient.get(`/api/sessions/summary?${params.toString()}`);\n    return response.data;\n  },\n\n  getLiveSessionReport: async (): Promise<any> => {\n    const response = await apiClient.get('/api/sessions/live');\n    return response.data;\n  },\n\n  getSessionReport: async (sessionId: string): Promise<any> => {\n    const response = await apiClient.get(`/api/sessions/${sessionId}/report`);\n    return response.data;\n  },\n\n  getSessionAnalytics: async (sessionId: string): Promise<any> => {\n    const response = await apiClient.get(`/api/sessions/${sessionId}/analytics`);\n    return response.data;\n  },\n};\n\n// Ensemble API\nexport const ensembleAPI = {\n  getEnsembleData: async (): Promise<any> => {\n    const response = await apiClient.get('/api/ensemble/data');\n    return response.data;\n  },\n\n  getEnsembleStatus: async (): Promise<any> => {\n    const response = await apiClient.get('/api/ensemble/status');\n    return response.data;\n  },\n\n  updateEnsembleConfig: async (config: any): Promise<any> => {\n    const response = await apiClient.post('/api/ensemble/config', config);\n    return response.data;\n  },\n};\n\nexport { apiClient }; // Export the api instance"], "mappings": "AAA2M;AAC3M,OAAOA,SAAS,MAAM,cAAc;;AAEpC;;AAEA;AACA,OAAO,MAAMC,OAAO,GAAG;EACrBC,KAAK,EAAE,MAAOC,WAA6B,IAA4B;IACrE,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpCD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW,CAACI,QAAQ,CAAC;IAC/CH,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW,CAACK,QAAQ,CAAC;IAE/C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAe,QAAQ,EAAEN,MAAM,EAAE;QACpEO,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOF,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA;MACnB;MACA,MAAMC,OAAO,GAAG,EAAAH,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,OAAAF,gBAAA,GAAIH,KAAK,CAACJ,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBJ,IAAI,KAAIC,KAAK,CAACI,OAAO,IAAI,cAAc;MACvG,MAAM,IAAIE,KAAK,CAAC,OAAOF,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGG,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,CAAC;IAClF;EACF,CAAC;EAEDK,YAAY,EAAE,MAAOA,YAAoB,IAA4B;IACnE,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAe,gBAAgB,EAAE;QAAEa,aAAa,EAAED;MAAa,CAAC,CAAC;MACtG,OAAOb,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAW,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;MACnB;MACA,MAAMT,OAAO,GAAG,EAAAO,gBAAA,GAAAX,KAAK,CAACJ,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,OAAAQ,gBAAA,GAAIb,KAAK,CAACJ,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBd,IAAI,KAAIC,KAAK,CAACI,OAAO,IAAI,sBAAsB;MAC/G,MAAM,IAAIE,KAAK,CAAC,OAAOF,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGG,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,CAAC;IAClF;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,UAAU,GAAG;EACxB;EACAC,oBAAoB,EAAE,MAAAA,CAAA,KAA2C;IAC/D,MAAMnB,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAuB,qBAAqB,CAAC;IACjF,OAAOpB,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAkB,iBAAiB,EAAE,MAAOC,MAAc,IAAmB;IACzD;IACA,MAAMtB,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAC,qBAAqB,EAAE;MAAEqB;IAAO,CAAC,CAAC;IACxE,OAAOtB,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAoB,kBAAkB,EAAE,MAAAA,CAAA,KAA0B;IAC5C,MAAMvB,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAC,sBAAsB,CAAC;IAC7D,OAAOD,QAAQ,CAACG,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF;IACA,MAAMxB,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,yBAAyB,CAAC;IAC/D,OAAOpB,QAAQ,CAACG,IAAI,CAAC,CAAC;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdqB,OAAO,CAACrB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D;IACA,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAqC;EAClE,MAAM1B,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAiB,4BAA4B,CAAC;EAClF,OAAOpB,QAAQ,CAACG,IAAI;AACtB,CAAC;AAED,OAAO,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAqC;EAClE,MAAM3B,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAiB,4BAA4B,CAAC;EAClF,OAAOpB,QAAQ,CAACG,IAAI;AACtB,CAAC;;AAED;AACA,OAAO,MAAMyB,eAAe,GAAG,MAAON,MAAc,IAA4B;EAC9E,MAAMtB,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAe,6BAA6BS,kBAAkB,CAACP,MAAM,CAAC,EAAE,CAAC;EAC7G,OAAOtB,QAAQ,CAACG,IAAI;AACtB,CAAC;;AAED;AACA,OAAO,MAAM2B,KAAK,GAAG;EACnBC,UAAU,EAAE,MAAOC,OAAY,IAAkC;IAC/D,MAAMhC,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAqB,eAAe,EAAE+B,OAAO,CAAC;IACnF,OAAOhC,QAAQ,CAACG,IAAI;EACtB,CAAC;EACD8B,mBAAmB,EAAE,MAAOD,OAAY,IAAiC;IACvE,MAAMhC,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAoB,iBAAiB,EAAE+B,OAAO,CAAC;IACpF,OAAOhC,QAAQ,CAACG,IAAI;EACtB,CAAC;EACD+B,iBAAiB,EAAE,MAAOF,OAAY,IAAkC;IACtE,MAAMhC,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAqB,kBAAkB,EAAE+B,OAAO,CAAC;IACtF,OAAOhC,QAAQ,CAACG,IAAI;EACtB,CAAC;EACDgC,YAAY,EAAE,MAAAA,CAAA,KAA0C;IACtD,MAAMnC,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAsB,cAAc,CAAC;IACzE,OAAOpB,QAAQ,CAACG,IAAI;EACtB,CAAC;EACDiC,WAAW,EAAE,MAAAA,CAAA,KAAuC;IAClD,MAAMpC,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAmB,gBAAgB,CAAC;IACxE,OAAOpB,QAAQ,CAACG,IAAI;EACtB,CAAC;EACDkC,oBAAoB,EAAE,MAAOC,OAAgB,IAAgC;IAC3E,MAAMtC,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAmB,gBAAgB,EAAE;MAAEqC;IAAQ,CAAC,CAAC;IACtF,OAAOtC,QAAQ,CAACG,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMoC,iBAAiB,GAAG;EAC/BC,kBAAkB,EAAE,MAAAA,CAAOC,IAAY,EAAEC,MAAe,KAAmB;IACzE,MAAM/C,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpCD,MAAM,CAACE,MAAM,CAAC,MAAM,EAAE4C,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;IACtC,IAAID,MAAM,EAAE;MACV/C,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAE6C,MAAM,CAAC;IACjC;IACA,MAAM1C,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,yBAAyBzB,MAAM,CAACgD,QAAQ,CAAC,CAAC,EAAE,CAAC;IAClF,OAAO3C,QAAQ,CAACG,IAAI;EACtB,CAAC;EAEDyC,oBAAoB,EAAE,MAAAA,CAAA,KAA0B;IAC9C,MAAM5C,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,oBAAoB,CAAC;IAC1D,OAAOpB,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED0C,gBAAgB,EAAE,MAAOC,SAAiB,IAAmB;IAC3D,MAAM9C,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,iBAAiB0B,SAAS,SAAS,CAAC;IACzE,OAAO9C,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED4C,mBAAmB,EAAE,MAAOD,SAAiB,IAAmB;IAC9D,MAAM9C,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,iBAAiB0B,SAAS,YAAY,CAAC;IAC5E,OAAO9C,QAAQ,CAACG,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAM6C,WAAW,GAAG;EACzBC,eAAe,EAAE,MAAAA,CAAA,KAA0B;IACzC,MAAMjD,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,oBAAoB,CAAC;IAC1D,OAAOpB,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED+C,iBAAiB,EAAE,MAAAA,CAAA,KAA0B;IAC3C,MAAMlD,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,GAAG,CAAC,sBAAsB,CAAC;IAC5D,OAAOpB,QAAQ,CAACG,IAAI;EACtB,CAAC;EAEDgD,oBAAoB,EAAE,MAAOC,MAAW,IAAmB;IACzD,MAAMpD,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAC,sBAAsB,EAAEmD,MAAM,CAAC;IACrE,OAAOpD,QAAQ,CAACG,IAAI;EACtB;AACF,CAAC;AAED,SAASZ,SAAS,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}