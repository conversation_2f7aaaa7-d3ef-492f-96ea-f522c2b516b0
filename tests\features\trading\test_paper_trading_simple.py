#!/usr/bin/env python3
"""
Simplified Paper Trading Test for Task 3.2.1
Tests core functionality without complex dependencies.
"""

import asyncio
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import numpy as np
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockRedisService:
    """Mock Redis service for testing"""
    
    def __init__(self, url: str):
        self.url = url
        self.data = {}
        
    async def connect(self):
        pass
        
    async def get(self, key: str):
        return self.data.get(key)
        
    async def setex(self, key: str, ttl: int, value: str):
        self.data[key] = value
        
    async def delete(self, key: str):
        self.data.pop(key, None)
        
    async def ping(self):
        return True

class MockCostCalculator:
    """Mock cost calculator for testing"""
    
    async def calculate_total_trading_cost(self, **kwargs):
        from dataclasses import dataclass
        
        @dataclass
        class MockCostResult:
            total_cost_usd: float = 5.0
            total_cost_bps: float = 10.0
            exchange_fees_usd: float = 3.0
            slippage_cost_usd: float = 2.0
            market_impact_cost_usd: float = 0.0
            funding_costs_usd: float = 0.0
            withdrawal_fees_usd: float = 0.0
            confidence: float = 0.8
            optimization_suggestions: List = None
            
            def __post_init__(self):
                if self.optimization_suggestions is None:
                    self.optimization_suggestions = []
        
        return MockCostResult()

class MockSlippageEstimator:
    """Mock slippage estimator for testing"""
    
    def __init__(self, **kwargs):
        pass
        
    async def estimate_multi_exchange_slippage(self, **kwargs):
        from dataclasses import dataclass
        
        @dataclass
        class MockSlippageResult:
            consensus_slippage_bps: float = 5.0
            min_slippage_bps: float = 3.0
            max_slippage_bps: float = 8.0
            
        return MockSlippageResult()

class MockWandBCostTracker:
    """Mock W&B cost tracker for testing"""
    
    async def track_cost_optimization_experiment(self, **kwargs):
        return {"experiment_id": "test", "cost_savings": 1.5}

async def test_paper_trading_basic():
    """Test basic paper trading functionality"""
    
    logger.info("Starting basic paper trading test...")
    
    try:
        # Import the paper trading module with mocked dependencies
        from app.strategies.paper_trading_portfolio_manager import PaperTradingPortfolioManager
        from app.models.market_data import MarketData
        
        # Create mock services
        redis_service = MockRedisService("redis://localhost:6379")
        cost_calculator = MockCostCalculator()
        slippage_estimator = MockSlippageEstimator()
        wandb_cost_tracker = MockWandBCostTracker()
        
        # Create paper trading manager
        manager = PaperTradingPortfolioManager(
            initial_balance_usd=100000.0,
            redis_service=redis_service,
            cost_calculator=cost_calculator,
            slippage_estimator=slippage_estimator,
            wandb_cost_tracker=wandb_cost_tracker,
            config={
                "max_position_size_pct": 20.0,
                "slippage_simulation": True,
                "fee_simulation": True,
                "enable_cost_optimization": True,
                "execution_latency_ms": 0,  # No delay for testing
                "market_data_latency_ms": 0
            }
        )
        
        # Test 1: Portfolio initialization
        logger.info("Test 1: Portfolio initialization")
        summary = await manager.get_portfolio_summary()
        
        assert summary["account"]["initial_balance"] == 100000.0
        assert summary["account"]["current_value"] == 100000.0
        assert summary["balances"]["USD"]["available"] == 100000.0
        logger.info("✅ Portfolio initialization passed")
        
        # Test 2: Buy order execution
        logger.info("Test 2: Buy order execution")
        market_data = MarketData(
            symbol="BTCUSDT",
            price=50000.0,
            volume=1000000.0,
            timestamp=datetime.now(),
            bid=49999.0,
            ask=50001.0,
            high_24h=51000.0,
            low_24h=49000.0,
            volatility=0.02
        )
        
        start_time = time.perf_counter()
        
        buy_order = await manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="BUY",
            quantity=1.0,
            order_type="MARKET",
            market_data=market_data
        )
        
        execution_time = (time.perf_counter() - start_time) * 1000
        
        assert buy_order is not None
        assert buy_order.status == "FILLED"
        assert buy_order.side == "BUY"
        assert buy_order.filled_quantity > 0
        
        logger.info(f"✅ Buy order execution passed ({execution_time:.1f}ms)")
        
        # Test 3: Portfolio state after buy
        logger.info("Test 3: Portfolio state verification")
        summary_after_buy = await manager.get_portfolio_summary()
        
        assert "BTC" in summary_after_buy["positions"]
        assert summary_after_buy["balances"]["USD"]["available"] < 100000.0
        assert summary_after_buy["trading_stats"]["total_trades"] == 1
        
        logger.info("✅ Portfolio state verification passed")
        
        # Test 4: Sell order execution
        logger.info("Test 4: Sell order execution")
        
        sell_order = await manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="SELL",
            quantity=0.5,
            order_type="MARKET",
            market_data=market_data
        )
        
        assert sell_order is not None
        assert sell_order.status == "FILLED"
        assert sell_order.side == "SELL"
        
        logger.info("✅ Sell order execution passed")
        
        # Test 5: Performance metrics
        logger.info("Test 5: Performance metrics")
        
        final_summary = await manager.get_portfolio_summary()
        assert final_summary["trading_stats"]["total_trades"] == 2
        assert final_summary["trading_stats"]["total_fees_paid"] > 0
        
        logger.info("✅ Performance metrics passed")
        
        # Test 6: Portfolio reset
        logger.info("Test 6: Portfolio reset")
        
        await manager.reset_portfolio(new_initial_balance=150000.0)
        reset_summary = await manager.get_portfolio_summary()
        
        assert reset_summary["account"]["initial_balance"] == 150000.0
        assert reset_summary["trading_stats"]["total_trades"] == 0
        assert len(reset_summary["positions"]) == 0
        
        logger.info("✅ Portfolio reset passed")
        
        # Performance benchmark
        logger.info("Test 7: Performance benchmark")
        
        execution_times = []
        for i in range(5):
            start = time.perf_counter()
            
            order = await manager.execute_paper_trade(
                symbol="BTCUSDT",
                side="BUY",
                quantity=0.1,
                order_type="MARKET",
                market_data=market_data
            )
            
            exec_time = (time.perf_counter() - start) * 1000
            execution_times.append(exec_time)
            
            if order:
                logger.info(f"Trade {i+1}: {exec_time:.1f}ms")
        
        avg_time = np.mean(execution_times)
        logger.info(f"Average execution time: {avg_time:.1f}ms")
        
        # Performance target: <100ms average
        assert avg_time < 100, f"Performance target not met: {avg_time:.1f}ms"
        
        logger.info("✅ Performance benchmark passed")
        
        logger.info("\n🎉 ALL BASIC PAPER TRADING TESTS PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_integration():
    """Test API integration"""
    
    logger.info("Testing API integration...")
    
    try:
        # Test basic import of API routes
        from app.api.routes.paper_trading import router
        
        logger.info("✅ API routes imported successfully")
        
        # Test route definitions
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/api/paper-trading/",
            "/api/paper-trading/health",
            "/api/paper-trading/portfolio/summary",
            "/api/paper-trading/trade"
        ]
        
        for expected_route in expected_routes:
            if any(expected_route in route for route in routes):
                logger.info(f"✅ Route found: {expected_route}")
            else:
                logger.warning(f"⚠️ Route missing: {expected_route}")
        
        logger.info("✅ API integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ API integration test failed: {e}")
        return False

async def test_docker_configuration():
    """Test Docker configuration"""
    
    logger.info("Testing Docker configuration...")
    
    try:
        # Check if docker-compose.yml contains paper trading service
        with open('docker-compose.yml', 'r') as f:
            docker_compose = f.read()
        
        assert 'paper-trading:' in docker_compose
        assert 'ensemble_paper_trading' in docker_compose
        assert 'PAPER_TRADING_MODE=true' in docker_compose
        
        logger.info("✅ Docker Compose configuration verified")
        
        # Check if Dockerfile contains paper trading stage
        with open('Dockerfile', 'r') as f:
            dockerfile = f.read()
        
        assert 'FROM production as paper-trading' in dockerfile
        assert 'PAPER_TRADING_MODE=true' in dockerfile
        
        logger.info("✅ Dockerfile configuration verified")
        
        logger.info("✅ Docker configuration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Docker configuration test failed: {e}")
        return False

async def main():
    """Main test runner"""
    
    logger.info("Paper Trading Environment Test Suite (Task 3.2.1)")
    logger.info("=" * 60)
    
    tests = [
        ("Basic Paper Trading Functionality", test_paper_trading_basic),
        ("API Integration", test_api_integration),
        ("Docker Configuration", test_docker_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning: {test_name}")
        logger.info("-" * 40)
        
        try:
            if await test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Tests Passed: {passed}/{total}")
    logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        logger.info("\n🚀 Paper Trading Environment Ready for Deployment!")
        logger.info("Next steps:")
        logger.info("1. Run: chmod +x scripts/deploy-paper-trading.sh")
        logger.info("2. Run: ./scripts/deploy-paper-trading.sh")
        logger.info("3. Access: http://localhost:8002/api/paper-trading/")
        return True
    else:
        logger.warning(f"\n⚠️ {total-passed} tests failed - review before deployment")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)