import pandas as pd
import numpy as np
import logging
from typing import List, Tuple, Dict

logger = logging.getLogger(__name__)


def calculate_atr_volatility(df: pd.DataFrame, window: int = 14) -> float:
    """Calculate market volatility using Average True Range (ATR)."""
    if df.empty or len(df) < window:
        return 0.0
    try:
        df_copy = df.copy()
        df_copy['high_low'] = df_copy['high'] - df_copy['low']
        df_copy['high_close'] = np.abs(df_copy['high'] - df_copy['close'].shift(1))
        df_copy['low_close'] = np.abs(df_copy['low'] - df_copy['close'].shift(1))
        df_copy['tr'] = df_copy[['high_low', 'high_close', 'low_close']].max(axis=1)
        atr = df_copy['tr'].rolling(window=window).mean().iloc[-1]
        current_price = df_copy['close'].iloc[-1]
        if current_price == 0:
            return 0.0
        normalized_atr = (atr / current_price) * 100
        return normalized_atr
    except Exception as e:
        logger.error(f"Error calculating ATR volatility: {e}")
        return 0.0


def calculate_trend_strength(df: pd.DataFrame, short_window: int = 20, long_window: int = 50) -> float:
    """Detect market trend strength using moving averages."""
    if df.empty or len(df) < long_window:
        return 0.0
    try:
        df_copy = df.copy()
        df_copy['short_ma'] = df_copy['close'].rolling(window=short_window).mean()
        df_copy['long_ma'] = df_copy['close'].rolling(window=long_window).mean()
        last_row = df_copy.iloc[-1]
        if pd.isna(last_row['short_ma']) or pd.isna(last_row['long_ma']) or last_row['long_ma'] == 0:
            return 0.0
        ma_diff = (last_row['short_ma'] - last_row['long_ma']) / last_row['long_ma']
        trend_strength = np.clip(ma_diff * 10, -1.0, 1.0)
        return trend_strength
    except Exception as e:
        logger.error(f"Error detecting trend strength: {e}")
        return 0.0


def detect_range_bound(df: pd.DataFrame, window: int = 20, threshold: float = 0.03) -> float:
    """Detect if market is range-bound."""
    if df.empty or len(df) < window:
        return 0.0
    try:
        recent_df = df.tail(window).copy()
        price_high = recent_df['high'].max()
        price_low = recent_df['low'].min()
        current_price = recent_df['close'].iloc[-1]
        if current_price == 0:
            return 0.0
        price_range = (price_high - price_low) / current_price
        x = np.arange(len(recent_df))
        y = recent_df['close'].values
        slope, _ = np.polyfit(x, y, 1)
        normalized_slope = abs(slope * len(recent_df) / current_price)
        range_score = 1.0 - min(price_range / threshold, 1.0)
        slope_score = 1.0 - min(normalized_slope / (threshold / 2), 1.0)
        range_bound_score = (range_score * 0.7) + (slope_score * 0.3)
        return range_bound_score
    except Exception as e:
        logger.error(f"Error detecting range-bound market: {e}")
        return 0.0


def analyze_volume(df: pd.DataFrame, window: int = 20) -> float:
    """Analyze trading volume to detect unusual activity."""
    if df.empty or len(df) < window or 'volume' not in df.columns:
        return 0.0
    try:
        avg_volume = df['volume'].rolling(window=window).mean()
        current_volume = df['volume'].iloc[-1]
        avg_vol_val = avg_volume.iloc[-1]
        if pd.isna(avg_vol_val) or avg_vol_val == 0:
            return 0.0
        relative_volume = current_volume / avg_vol_val
        volume_score = min(relative_volume / 3.0, 1.0)
        return volume_score
    except Exception as e:
        logger.error(f"Error analyzing volume: {e}")
        return 0.0


def detect_support_resistance(df: pd.DataFrame, num_levels: int = 3) -> Tuple[List[float], List[float]]:
    """Detect support and resistance levels from a DataFrame."""
    if df.empty or len(df) < 10:
        return [], []
    try:
        highs = df['high'].values
        lows = df['low'].values
        swing_highs = []
        swing_lows = []
        for i in range(2, len(df) - 2):
            if highs[i] > highs[i-1] and highs[i] > highs[i-2] and highs[i] > highs[i+1] and highs[i] > highs[i+2]:
                swing_highs.append(highs[i])
            if lows[i] < lows[i-1] and lows[i] < lows[i-2] and lows[i] < lows[i+1] and lows[i] < lows[i+2]:
                swing_lows.append(lows[i])
        support_clusters = _cluster_price_levels(swing_lows)
        resistance_clusters = _cluster_price_levels(swing_highs)
        support_clusters.sort(key=lambda x: len(x), reverse=True)
        resistance_clusters.sort(key=lambda x: len(x), reverse=True)
        support_levels = [sum(c) / len(c) for c in support_clusters[:num_levels] if c]
        resistance_levels = [sum(c) / len(c) for c in resistance_clusters[:num_levels] if c]
        support_levels.sort()
        resistance_levels.sort()
        return support_levels, resistance_levels
    except Exception as e:
        logger.error(f"Error detecting support/resistance levels: {e}", exc_info=True)
        return [], []


def _cluster_price_levels(prices: List[float], threshold_pct: float = 0.005) -> List[List[float]]:
    """Helper to cluster similar price levels."""
    if not prices:
        return []
    sorted_prices = sorted(prices)
    if not sorted_prices:
        return []
    avg_price = sum(sorted_prices) / len(sorted_prices)
    if avg_price == 0:
        return []
    threshold = avg_price * threshold_pct
    clusters = []
    current_cluster = [sorted_prices[0]]
    for i in range(1, len(sorted_prices)):
        if sorted_prices[i] - sorted_prices[i-1] <= threshold:
            current_cluster.append(sorted_prices[i])
        else:
            clusters.append(current_cluster)
            current_cluster = [sorted_prices[i]]
    if current_cluster:
        clusters.append(current_cluster)
    return clusters


def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, timeperiod: int = 14) -> pd.Series:
    """Calculate ADX using TA-Lib."""
    try:
        from talib import ADX
        # Ensure correct data types
        high_f = high.astype(float)
        low_f = low.astype(float)
        close_f = close.astype(float)
        adx_series = ADX(high_f, low_f, close_f, timeperiod=timeperiod)
        return adx_series.replace([np.inf, -np.inf], np.nan).fillna(25)  # Replace inf and fill NaNs
    except ImportError:
        logger.error("TA-Lib is not installed. Cannot calculate ADX.")
        return pd.Series([25] * len(close), index=close.index) # Return default value
    except Exception as e:
        logger.error(f"Error calculating ADX: {e}", exc_info=True)
        return pd.Series([25] * len(close), index=close.index)


def calculate_rsi(close: pd.Series, timeperiod: int = 14) -> pd.Series:
    """Calculate RSI using TA-Lib."""
    try:
        from talib import RSI
        close_f = close.astype(float)
        rsi_series = RSI(close_f, timeperiod=timeperiod)
        return rsi_series.replace([np.inf, -np.inf], np.nan).fillna(50)  # Replace inf and fill NaNs
    except ImportError:
        logger.error("TA-Lib is not installed. Cannot calculate RSI.")
        return pd.Series([50] * len(close), index=close.index)
    except Exception as e:
        logger.error(f"Error calculating RSI: {e}", exc_info=True)
        return pd.Series([50] * len(close), index=close.index)


def calculate_macd(close: pd.Series, fastperiod: int = 12, slowperiod: int = 26, signalperiod: int = 9) -> Dict[str, pd.Series]:
    """Calculate MACD using TA-Lib."""
    try:
        from talib import MACD
        close_f = close.astype(float)
        macd, macdsignal, macdhist = MACD(close_f, fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
        return {
            "macd": macd.replace([np.inf, -np.inf], np.nan).fillna(0),
            "macd_signal": macdsignal.replace([np.inf, -np.inf], np.nan).fillna(0),
            "macd_hist": macdhist.replace([np.inf, -np.inf], np.nan).fillna(0)
        }
    except ImportError:
        logger.error("TA-Lib is not installed. Cannot calculate MACD.")
        nan_series = pd.Series([0] * len(close), index=close.index)
        return {"macd": nan_series, "macd_signal": nan_series, "macd_hist": nan_series}
    except Exception as e:
        logger.error(f"Error calculating MACD: {e}", exc_info=True)
        nan_series = pd.Series([0] * len(close), index=close.index)
        return {"macd": nan_series, "macd_signal": nan_series, "macd_hist": nan_series}


def calculate_bollinger_bands(close: pd.Series, timeperiod: int = 20, nbdevup: int = 2, nbdevdn: int = 2) -> Dict[str, pd.Series]:
    """Calculate Bollinger Bands using TA-Lib."""
    try:
        from talib import BBANDS
        close_f = close.astype(float)
        upper, middle, lower = BBANDS(close_f, timeperiod=timeperiod, nbdevup=nbdevup, nbdevdn=nbdevdn)
        
        # If TA-Lib returns all NaNs (e.g., for insufficient data), return zeros like the other fallbacks.
        if upper.isnull().all() or middle.isnull().all() or lower.isnull().all():
            nan_series = pd.Series([0] * len(close), index=close.index)
            return {"upper": nan_series, "middle": nan_series, "lower": nan_series}

        return {
            "upper": upper.replace([np.inf, -np.inf], np.nan).bfill().ffill(),
            "middle": middle.replace([np.inf, -np.inf], np.nan).bfill().ffill(),
            "lower": lower.replace([np.inf, -np.inf], np.nan).bfill().ffill()
        }
    except ImportError:
        logger.error("TA-Lib is not installed. Cannot calculate Bollinger Bands.")
        nan_series = pd.Series([0] * len(close), index=close.index)
        return {"upper": nan_series, "middle": nan_series, "lower": nan_series}
    except Exception as e:
        logger.error(f"Error calculating Bollinger Bands: {e}", exc_info=True)
        nan_series = pd.Series([0] * len(close), index=close.index)
        return {"upper": nan_series, "middle": nan_series, "lower": nan_series}


def calculate_ema(close: pd.Series, timeperiod: int = 20) -> pd.Series:
    """Calculate EMA using TA-Lib."""
    try:
        from talib import EMA
        close_f = close.astype(float)
        ema_series = EMA(close_f, timeperiod=timeperiod)
        
        # If TA-Lib returns all NaNs, fallback to a copy of the original series
        if ema_series.isnull().all():
            return close.copy()

        return ema_series.replace([np.inf, -np.inf], np.nan).bfill().ffill()
    except ImportError:
        logger.error("TA-Lib is not installed. Cannot calculate EMA.")
        return close.copy() # Return a copy of close as fallback
    except Exception as e:
        logger.error(f"Error calculating EMA: {e}", exc_info=True)
        return close.copy()
