# Playwright MCP Server Tests

This document provides information about the Playwright MCP server tests that have been created for the Crypto_App_V2 project.

## Overview

The Playwright MCP (Model Context Protocol) server provides browser automation capabilities using Playwright. These tests demonstrate how to use the Playwright MCP server to automate browser interactions, take screenshots, extract data from web pages, and more.

## Test Scripts

### 1. Basic Test Script (`test_playwright_mcp.js`)

This script demonstrates basic Playwright functionality:

- Launching a browser
- Creating a new page
- Navigating to a website
- Taking a screenshot
- Getting the page title
- Closing the browser

To run this test:

```bash
node test_playwright_mcp.js
```

### 2. Advanced Test Script (`test_playwright_mcp_advanced.js`)

This script demonstrates more advanced Playwright functionality:

- Launching a browser with specific options
- Creating a new context with custom viewport and user agent
- Navigating to multiple pages
- Taking screenshots
- Extracting data from web pages
- Working with multiple tabs
- Switching between tabs
- Closing the browser

To run this test:

```bash
node test_playwright_mcp_advanced.js
```

### 3. Claude Integration Test Script (`test_playwright_mcp_claude.js`)

This script demonstrates how to use the Playwright MCP server with <PERSON>:

- Navigating to cryptocurrency price pages
- Taking screenshots for <PERSON> to analyze
- Extracting price data for <PERSON> to analyze
- Saving data to JSON files for <PERSON> to analyze
- Creating a comparison report for Claude

To run this test:

```bash
node test_playwright_mcp_claude.js
```

## Generated Files

The test scripts generate the following files:

- `binance_screenshot.png` - Screenshot of the Binance homepage
- `binance_markets_screenshot.png` - Screenshot of the Binance markets page
- `bitcoin_page_screenshot.png` - Screenshot of the Bitcoin price page
- `ethereum_page_screenshot.png` - Screenshot of the Ethereum price page
- `bitcoin_price_for_claude.png` - Screenshot of the Bitcoin price page for Claude
- `ethereum_price_for_claude.png` - Screenshot of the Ethereum price page for Claude
- `bitcoin_data_for_claude.json` - Bitcoin price data for Claude
- `ethereum_data_for_claude.json` - Ethereum price data for Claude
- `crypto_comparison_for_claude.json` - Comparison report for Claude

## Using with Claude

To use the generated files with Claude:

1. Upload the screenshots and JSON files to Claude
2. Ask Claude to analyze the data and provide insights
3. Claude can use the Playwright MCP server to automate browser interactions and extract data from web pages

## Troubleshooting

If you encounter issues with the test scripts:

1. Make sure the Playwright MCP server is running
2. Check if the Playwright package is installed (`npm install playwright`)
3. Check if the website structure has changed (selectors might need to be updated)
4. Try running the scripts with the `headless: false` option to see what's happening in the browser

## Documentation

For more information about the Playwright MCP server, visit:

- [Playwright MCP Server Documentation](https://executeautomation.github.io/mcp-playwright/)
- [API Reference](https://executeautomation.github.io/mcp-playwright/docs/playwright-web/Supported-Tools)
- [Playwright Documentation](https://playwright.dev/docs/intro)
