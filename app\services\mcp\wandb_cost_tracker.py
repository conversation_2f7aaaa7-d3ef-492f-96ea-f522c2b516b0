#!/usr/bin/env python3
"""
W&B Cost Optimization Tracking Service for Task 3.1.3
Implements comprehensive cost optimization tracking with detailed metrics monitoring.

Features:
- Cost optimization experiment tracking in W&B
- Transaction cost trend monitoring and analysis
- Cost effectiveness measurement and improvement tracking
- Real-time cost breakdown component analysis
- Integration with existing cost calculation infrastructure
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
import logging
from statistics import mean, stdev
import uuid
import time

from app.services.mcp.redis_service import RedisService
from app.services.mcp.supabase_service import SupabaseService
from app.services.cost_calculator import CostCalculator, TotalTradingCost, OrderType
from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator

logger = logging.getLogger(__name__)

@dataclass
class CostOptimizationMetrics:
    """Cost optimization experiment metrics"""
    experiment_id: str
    strategy_name: str
    symbol: str
    trade_size_usd: float
    
    # Cost components (USD)
    total_cost_usd: float
    exchange_fees_usd: float
    slippage_cost_usd: float
    market_impact_cost_usd: float
    funding_costs_usd: float
    withdrawal_fees_usd: float
    
    # Cost efficiency metrics (basis points)
    total_cost_bps: float
    cost_improvement_bps: float  # vs baseline
    cost_prediction_accuracy: float
    
    # Exchange comparison
    optimal_exchange: str
    cost_savings_vs_default_bps: float
    exchange_cost_ranking: Dict[str, float]
    
    # Slippage optimization
    slippage_estimation_accuracy: float
    slippage_improvement_bps: float
    model_slippage_vs_actual: float
    
    # Performance metrics
    calculation_time_ms: float
    optimization_suggestions_count: int
    confidence_score: float
    
    timestamp: datetime

@dataclass
class CostTrendAnalysis:
    """Cost trend monitoring and analysis"""
    symbol: str
    analysis_period_hours: int
    
    # Trend metrics
    avg_cost_bps: float
    cost_volatility: float
    cost_trend_direction: str  # "improving", "worsening", "stable"
    cost_improvement_rate: float  # bps per day
    
    # Component analysis
    fees_trend: Dict[str, float]
    slippage_trend: Dict[str, float]
    market_impact_trend: Dict[str, float]
    
    # Optimization effectiveness
    optimization_adoption_rate: float
    avg_savings_per_optimization: float
    cost_prediction_mae: float
    
    # Exchange performance
    best_exchange_frequency: Dict[str, int]
    exchange_cost_stability: Dict[str, float]
    arbitrage_opportunities_detected: int
    
    timestamp: datetime

@dataclass
class CostEffectivenessReport:
    """Cost optimization effectiveness measurement"""
    optimization_strategy: str
    measurement_period_days: int
    
    # Before vs After analysis
    baseline_cost_bps: float
    optimized_cost_bps: float
    total_improvement_bps: float
    improvement_percentage: float
    
    # ROI calculation
    total_trades_analyzed: int
    total_volume_usd: float
    total_cost_savings_usd: float
    optimization_roi: float
    
    # Breakdown by optimization type
    exchange_optimization_savings: float
    timing_optimization_savings: float
    size_optimization_savings: float
    order_type_optimization_savings: float
    
    # Success metrics
    optimization_success_rate: float
    avg_implementation_time_ms: float
    recommendation_accuracy: float
    
    timestamp: datetime

class WandBCostTracker:
    """
    Comprehensive W&B integration for cost optimization tracking.
    
    Features:
    - Cost optimization experiment logging
    - Real-time cost trend monitoring
    - Cost effectiveness measurement
    - Integration with existing cost infrastructure
    """
    
    def __init__(
        self,
        redis_service: RedisService,
        cost_calculator: CostCalculator,
        slippage_estimator: Optional[EnhancedSlippageEstimator] = None,
        supabase_service: Optional[SupabaseService] = None,
        wandb_config: Optional[Dict] = None,
        config: Optional[Dict] = None
    ):
        self.redis_service = redis_service
        self.cost_calculator = cost_calculator
        self.slippage_estimator = slippage_estimator
        self.supabase_service = supabase_service
        self.wandb_config = wandb_config or self._default_wandb_config()
        self.config = config or self._default_config()
        
        # Cache keys
        self.COST_METRICS_KEY = "wandb_cost_tracker:metrics"
        self.TREND_ANALYSIS_KEY = "wandb_cost_tracker:trends"
        self.EFFECTIVENESS_KEY = "wandb_cost_tracker:effectiveness"
        self.BASELINE_CACHE_KEY = "wandb_cost_tracker:baseline"
        
        # Tracking data
        self.experiment_id = str(uuid.uuid4())
        self.cost_history = {}
        self.optimization_cache = {}
        
        logger.info("W&B Cost Optimization Tracker initialized")
    
    def _default_wandb_config(self) -> Dict[str, Any]:
        """Default W&B configuration for cost optimization tracking"""
        return {
            "project_name": "crypto_cost_optimization",
            "entity": "crypto_trading_team",
            "experiment_name": f"cost_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "tags": ["cost_optimization", "transaction_costs", "real_time"],
            "log_frequency": 30,  # Log every 30 seconds
            "enable_real_time": True,
            "enable_trend_analysis": True,
            "enable_effectiveness_tracking": True,
            "dashboard_update_interval": 60  # 1 minute
        }
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for cost optimization tracking"""
        return {
            "cache_ttl_metrics": 300,         # 5 minutes for metrics
            "cache_ttl_trends": 600,          # 10 minutes for trend analysis
            "cache_ttl_effectiveness": 1800,  # 30 minutes for effectiveness
            "baseline_measurement_period": 168,  # 1 week in hours
            "trend_analysis_window": 72,      # 3 days in hours
            "min_trades_for_trend": 10,       # Minimum trades for trend analysis
            "cost_improvement_threshold": 0.5,  # 0.5 bps minimum improvement
            "performance_target_ms": 200,     # Target <200ms for logging
            "enable_real_time_tracking": True,
            "enable_cost_alerts": True,
            "alert_thresholds": {
                "high_cost_bps": 50,          # Alert if cost >50 bps
                "cost_spike_multiplier": 2.0,  # Alert if cost >2x baseline
                "low_accuracy": 0.7           # Alert if prediction accuracy <70%
            }
        }
    
    async def track_cost_optimization_experiment(
        self,
        strategy_name: str,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType = OrderType.MARKET,
        exchange: str = None,
        leverage: float = 1.0
    ) -> CostOptimizationMetrics:
        """
        Track comprehensive cost optimization experiment with W&B integration.
        """
        start_time = time.perf_counter()
        
        try:
            # Calculate comprehensive trading costs
            cost_result = await self.cost_calculator.calculate_total_trading_cost(
                symbol=symbol,
                trade_size_usd=trade_size_usd,
                order_type=order_type,
                exchange=exchange,
                leverage=leverage
            )
            
            # Get baseline costs for comparison
            baseline_cost = await self._get_baseline_cost(symbol, trade_size_usd, order_type)
            
            # Calculate cost improvements
            cost_improvement_bps = baseline_cost - cost_result.total_cost_bps
            
            # Get exchange cost comparison
            exchange_rankings = await self._analyze_exchange_costs(
                symbol, trade_size_usd, order_type
            )
            
            optimal_exchange = min(exchange_rankings.items(), key=lambda x: x[1])[0]
            cost_savings_vs_default = exchange_rankings.get(
                self.cost_calculator.config["default_exchange"], 0
            ) - exchange_rankings.get(optimal_exchange, 0)
            
            # Analyze slippage optimization if estimator available
            slippage_metrics = await self._analyze_slippage_optimization(
                symbol, trade_size_usd, cost_result
            )
            
            # Calculate cost prediction accuracy
            prediction_accuracy = await self._calculate_cost_prediction_accuracy(
                symbol, cost_result
            )
            
            calculation_time = (time.perf_counter() - start_time) * 1000
            
            # Create cost optimization metrics
            metrics = CostOptimizationMetrics(
                experiment_id=self.experiment_id,
                strategy_name=strategy_name,
                symbol=symbol,
                trade_size_usd=trade_size_usd,
                
                # Cost components
                total_cost_usd=cost_result.total_cost_usd,
                exchange_fees_usd=cost_result.exchange_fees_usd,
                slippage_cost_usd=cost_result.slippage_cost_usd,
                market_impact_cost_usd=cost_result.market_impact_cost_usd,
                funding_costs_usd=cost_result.funding_costs_usd,
                withdrawal_fees_usd=cost_result.withdrawal_fees_usd,
                
                # Cost efficiency metrics
                total_cost_bps=cost_result.total_cost_bps,
                cost_improvement_bps=cost_improvement_bps,
                cost_prediction_accuracy=prediction_accuracy,
                
                # Exchange comparison
                optimal_exchange=optimal_exchange,
                cost_savings_vs_default_bps=cost_savings_vs_default,
                exchange_cost_ranking=exchange_rankings,
                
                # Slippage optimization
                slippage_estimation_accuracy=slippage_metrics["accuracy"],
                slippage_improvement_bps=slippage_metrics["improvement_bps"],
                model_slippage_vs_actual=slippage_metrics["model_vs_actual"],
                
                # Performance metrics
                calculation_time_ms=calculation_time,
                optimization_suggestions_count=len(cost_result.optimization_suggestions),
                confidence_score=cost_result.confidence,
                
                timestamp=datetime.now()
            )
            
            # Log to W&B using MCP
            await self._log_cost_metrics_to_wandb(metrics)
            
            # Cache metrics for fast retrieval
            cache_key = f"{self.COST_METRICS_KEY}:{symbol}:{strategy_name}"
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_metrics"],
                json.dumps(asdict(metrics), default=str)
            )
            
            # Store historical data for trend analysis
            await self._store_cost_history(metrics)
            
            # Store in Supabase for analytics
            if self.supabase_service:
                await self._store_cost_optimization_analytics(metrics)
            
            logger.info(f"Cost optimization tracking completed in {calculation_time:.2f}ms")
            return metrics
            
        except Exception as e:
            logger.error(f"Cost optimization tracking failed: {e}")
            return self._create_fallback_cost_metrics(strategy_name, symbol, trade_size_usd)
    
    async def analyze_cost_trends(
        self,
        symbol: str,
        analysis_hours: int = None
    ) -> CostTrendAnalysis:
        """
        Analyze cost trends and optimization effectiveness over time.
        """
        try:
            analysis_hours = analysis_hours or self.config["trend_analysis_window"]
            
            # Check cache first
            cache_key = f"{self.TREND_ANALYSIS_KEY}:{symbol}:{analysis_hours}"
            cached_analysis = await self.redis_service.get(cache_key)
            
            if cached_analysis:
                cache_data = json.loads(cached_analysis)
                cache_time = datetime.fromisoformat(cache_data['timestamp'])
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_trends"]):
                    return CostTrendAnalysis(**cache_data)
            
            # Get historical cost data
            cost_history = await self._get_cost_history(symbol, analysis_hours)
            
            if len(cost_history) < self.config["min_trades_for_trend"]:
                logger.warning(f"Insufficient data for trend analysis: {len(cost_history)} trades")
                return self._create_fallback_trend_analysis(symbol, analysis_hours)
            
            # Calculate trend metrics
            costs_bps = [entry["total_cost_bps"] for entry in cost_history]
            timestamps = [datetime.fromisoformat(entry["timestamp"]) for entry in cost_history]
            
            avg_cost_bps = np.mean(costs_bps)
            cost_volatility = np.std(costs_bps)
            
            # Trend direction analysis
            if len(costs_bps) > 1:
                trend_slope = np.polyfit(range(len(costs_bps)), costs_bps, 1)[0]
                if trend_slope < -self.config["cost_improvement_threshold"]:
                    trend_direction = "improving"
                    cost_improvement_rate = abs(trend_slope) * 24  # per day
                elif trend_slope > self.config["cost_improvement_threshold"]:
                    trend_direction = "worsening"
                    cost_improvement_rate = -trend_slope * 24
                else:
                    trend_direction = "stable"
                    cost_improvement_rate = 0
            else:
                trend_direction = "stable"
                cost_improvement_rate = 0
            
            # Component trend analysis
            fees_trend = await self._analyze_component_trend(cost_history, "exchange_fees_usd")
            slippage_trend = await self._analyze_component_trend(cost_history, "slippage_cost_usd")
            market_impact_trend = await self._analyze_component_trend(cost_history, "market_impact_cost_usd")
            
            # Optimization effectiveness
            optimization_metrics = await self._analyze_optimization_effectiveness(cost_history)
            
            # Exchange performance analysis
            exchange_analysis = await self._analyze_exchange_performance(cost_history)
            
            trend_analysis = CostTrendAnalysis(
                symbol=symbol,
                analysis_period_hours=analysis_hours,
                
                # Trend metrics
                avg_cost_bps=avg_cost_bps,
                cost_volatility=cost_volatility,
                cost_trend_direction=trend_direction,
                cost_improvement_rate=cost_improvement_rate,
                
                # Component analysis
                fees_trend=fees_trend,
                slippage_trend=slippage_trend,
                market_impact_trend=market_impact_trend,
                
                # Optimization effectiveness
                optimization_adoption_rate=optimization_metrics["adoption_rate"],
                avg_savings_per_optimization=optimization_metrics["avg_savings"],
                cost_prediction_mae=optimization_metrics["prediction_mae"],
                
                # Exchange performance
                best_exchange_frequency=exchange_analysis["frequency"],
                exchange_cost_stability=exchange_analysis["stability"],
                arbitrage_opportunities_detected=exchange_analysis["arbitrage_count"],
                
                timestamp=datetime.now()
            )
            
            # Log to W&B
            await self._log_trend_analysis_to_wandb(trend_analysis)
            
            # Cache results
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_trends"],
                json.dumps(asdict(trend_analysis), default=str)
            )
            
            return trend_analysis
            
        except Exception as e:
            logger.error(f"Cost trend analysis failed: {e}")
            return self._create_fallback_trend_analysis(symbol, analysis_hours or 72)
    
    async def measure_cost_optimization_effectiveness(
        self,
        optimization_strategy: str,
        measurement_days: int = 7
    ) -> CostEffectivenessReport:
        """
        Measure the effectiveness of cost optimization strategies.
        """
        try:
            # Check cache
            cache_key = f"{self.EFFECTIVENESS_KEY}:{optimization_strategy}:{measurement_days}"
            cached_report = await self.redis_service.get(cache_key)
            
            if cached_report:
                cache_data = json.loads(cached_report)
                cache_time = datetime.fromisoformat(cache_data['timestamp'])
                if datetime.now() - cache_time < timedelta(seconds=self.config["cache_ttl_effectiveness"]):
                    return CostEffectivenessReport(**cache_data)
            
            # Get baseline and optimized cost data
            end_time = datetime.now()
            start_time = end_time - timedelta(days=measurement_days)
            
            baseline_data = await self._get_baseline_performance_data(start_time, end_time)
            optimized_data = await self._get_optimized_performance_data(
                optimization_strategy, start_time, end_time
            )
            
            # Calculate before vs after metrics
            baseline_cost_bps = baseline_data["avg_cost_bps"]
            optimized_cost_bps = optimized_data["avg_cost_bps"]
            total_improvement_bps = baseline_cost_bps - optimized_cost_bps
            improvement_percentage = (total_improvement_bps / baseline_cost_bps) * 100 if baseline_cost_bps > 0 else 0
            
            # ROI calculation
            total_trades = optimized_data["trade_count"]
            total_volume = optimized_data["total_volume_usd"]
            total_cost_savings_usd = (total_improvement_bps / 10000) * total_volume
            optimization_roi = total_cost_savings_usd / max(total_volume * 0.0001, 1)  # vs 1bp cost
            
            # Breakdown by optimization type
            optimization_breakdown = await self._analyze_optimization_breakdown(
                optimized_data["optimizations"]
            )
            
            # Success metrics
            success_rate = optimized_data["success_rate"]
            avg_implementation_time = optimized_data["avg_implementation_time_ms"]
            recommendation_accuracy = optimized_data["recommendation_accuracy"]
            
            effectiveness_report = CostEffectivenessReport(
                optimization_strategy=optimization_strategy,
                measurement_period_days=measurement_days,
                
                # Before vs After analysis
                baseline_cost_bps=baseline_cost_bps,
                optimized_cost_bps=optimized_cost_bps,
                total_improvement_bps=total_improvement_bps,
                improvement_percentage=improvement_percentage,
                
                # ROI calculation
                total_trades_analyzed=total_trades,
                total_volume_usd=total_volume,
                total_cost_savings_usd=total_cost_savings_usd,
                optimization_roi=optimization_roi,
                
                # Breakdown by optimization type
                exchange_optimization_savings=optimization_breakdown["exchange_savings"],
                timing_optimization_savings=optimization_breakdown["timing_savings"],
                size_optimization_savings=optimization_breakdown["size_savings"],
                order_type_optimization_savings=optimization_breakdown["order_type_savings"],
                
                # Success metrics
                optimization_success_rate=success_rate,
                avg_implementation_time_ms=avg_implementation_time,
                recommendation_accuracy=recommendation_accuracy,
                
                timestamp=datetime.now()
            )
            
            # Log to W&B
            await self._log_effectiveness_report_to_wandb(effectiveness_report)
            
            # Cache results
            await self.redis_service.setex(
                cache_key,
                self.config["cache_ttl_effectiveness"],
                json.dumps(asdict(effectiveness_report), default=str)
            )
            
            return effectiveness_report
            
        except Exception as e:
            logger.error(f"Cost effectiveness measurement failed: {e}")
            return self._create_fallback_effectiveness_report(optimization_strategy, measurement_days)
    
    # Helper methods for data analysis
    
    async def _get_baseline_cost(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType
    ) -> float:
        """Get baseline cost for comparison"""
        
        cache_key = f"{self.BASELINE_CACHE_KEY}:{symbol}:{int(trade_size_usd/1000)*1000}:{order_type.value}"
        cached_baseline = await self.redis_service.get(cache_key)
        
        if cached_baseline:
            return float(cached_baseline)
        
        # Calculate baseline using default exchange and no optimizations
        baseline_result = await self.cost_calculator.calculate_total_trading_cost(
            symbol=symbol,
            trade_size_usd=trade_size_usd,
            order_type=order_type,
            exchange=self.cost_calculator.config["default_exchange"]
        )
        
        # Cache baseline for comparison
        await self.redis_service.setex(
            cache_key,
            3600,  # 1 hour cache
            str(baseline_result.total_cost_bps)
        )
        
        return baseline_result.total_cost_bps
    
    async def _analyze_exchange_costs(
        self,
        symbol: str,
        trade_size_usd: float,
        order_type: OrderType
    ) -> Dict[str, float]:
        """Analyze costs across different exchanges"""
        
        exchange_costs = {}
        
        for exchange in self.cost_calculator.exchange_fees.keys():
            try:
                cost_result = await self.cost_calculator.calculate_total_trading_cost(
                    symbol=symbol,
                    trade_size_usd=trade_size_usd,
                    order_type=order_type,
                    exchange=exchange
                )
                exchange_costs[exchange] = cost_result.total_cost_bps
            except Exception as e:
                logger.warning(f"Failed to calculate cost for {exchange}: {e}")
                exchange_costs[exchange] = 999.0  # High penalty for failed calculation
        
        return exchange_costs
    
    async def _analyze_slippage_optimization(
        self,
        symbol: str,
        trade_size_usd: float,
        cost_result: TotalTradingCost
    ) -> Dict[str, Any]:
        """Analyze slippage optimization effectiveness"""
        
        try:
            if not self.slippage_estimator:
                return {
                    "accuracy": 0.7,  # Default moderate accuracy
                    "improvement_bps": 0.0,
                    "model_vs_actual": 1.0
                }
            
            # Get enhanced slippage analysis
            slippage_analysis = await self.slippage_estimator.estimate_multi_exchange_slippage(
                symbol=symbol,
                trade_size_usd=trade_size_usd,
                is_buy_order=True
            )
            
            # Calculate slippage metrics
            estimated_slippage_bps = slippage_analysis.consensus_slippage_bps
            actual_slippage_bps = (cost_result.slippage_cost_usd / trade_size_usd) * 10000
            
            accuracy = 1.0 - abs(estimated_slippage_bps - actual_slippage_bps) / max(actual_slippage_bps, 1)
            improvement_bps = slippage_analysis.max_slippage_bps - slippage_analysis.min_slippage_bps
            model_vs_actual = estimated_slippage_bps / max(actual_slippage_bps, 0.1)
            
            return {
                "accuracy": max(0.0, min(1.0, accuracy)),
                "improvement_bps": improvement_bps,
                "model_vs_actual": model_vs_actual
            }
            
        except Exception as e:
            logger.error(f"Slippage optimization analysis failed: {e}")
            return {
                "accuracy": 0.5,
                "improvement_bps": 0.0,
                "model_vs_actual": 1.0
            }
    
    async def _calculate_cost_prediction_accuracy(
        self,
        symbol: str,
        cost_result: TotalTradingCost
    ) -> float:
        """Calculate cost prediction accuracy against historical data"""
        
        try:
            # Get recent historical cost data for comparison
            historical_costs = await self._get_recent_historical_costs(symbol, hours=24)
            
            if not historical_costs:
                return 0.8  # Default good accuracy
            
            # Calculate prediction accuracy vs recent history
            recent_avg_cost = np.mean([entry["total_cost_bps"] for entry in historical_costs])
            predicted_cost = cost_result.total_cost_bps
            
            accuracy = 1.0 - abs(predicted_cost - recent_avg_cost) / max(recent_avg_cost, 1)
            return max(0.0, min(1.0, accuracy))
            
        except Exception as e:
            logger.error(f"Cost prediction accuracy calculation failed: {e}")
            return 0.7
    
    async def _get_recent_historical_costs(self, symbol: str, hours: int) -> List[Dict]:
        """Get recent historical cost data"""
        
        try:
            # In production, this would query actual historical data
            # For now, simulate with cache data
            history_key = f"cost_history:{symbol}"
            cached_history = await self.redis_service.get(history_key)
            
            if cached_history:
                all_history = json.loads(cached_history)
                cutoff_time = datetime.now() - timedelta(hours=hours)
                
                recent_history = [
                    entry for entry in all_history
                    if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
                ]
                return recent_history
            
            return []
            
        except Exception as e:
            logger.warning(f"Failed to get recent historical costs: {e}")
            return []
    
    # W&B logging methods
    
    async def _log_cost_metrics_to_wandb(self, metrics: CostOptimizationMetrics) -> None:
        """Log cost optimization metrics to W&B using MCP"""
        try:
            wandb_data = {
                'experiment_id': metrics.experiment_id,
                'type': 'cost_optimization_metrics',
                'strategy_name': metrics.strategy_name,
                'symbol': metrics.symbol,
                
                # Cost metrics
                'total_cost_usd': metrics.total_cost_usd,
                'total_cost_bps': metrics.total_cost_bps,
                'cost_improvement_bps': metrics.cost_improvement_bps,
                'cost_prediction_accuracy': metrics.cost_prediction_accuracy,
                
                # Cost breakdown
                'exchange_fees_usd': metrics.exchange_fees_usd,
                'slippage_cost_usd': metrics.slippage_cost_usd,
                'market_impact_cost_usd': metrics.market_impact_cost_usd,
                'funding_costs_usd': metrics.funding_costs_usd,
                
                # Optimization metrics
                'optimal_exchange': metrics.optimal_exchange,
                'cost_savings_vs_default_bps': metrics.cost_savings_vs_default_bps,
                'slippage_estimation_accuracy': metrics.slippage_estimation_accuracy,
                'slippage_improvement_bps': metrics.slippage_improvement_bps,
                
                # Performance
                'calculation_time_ms': metrics.calculation_time_ms,
                'confidence_score': metrics.confidence_score,
                'optimization_suggestions_count': metrics.optimization_suggestions_count,
                
                'step': int(datetime.now().timestamp()),
                'tags': self.wandb_config['tags'] + [metrics.strategy_name, metrics.symbol]
            }
            
            # Store in Redis as W&B log queue
            wandb_queue_key = f"wandb_logs:cost_optimization:{metrics.symbol}"
            await self.redis_service.setex(
                wandb_queue_key,
                3600,  # 1 hour
                json.dumps(wandb_data, default=str)
            )
            
            logger.info(f"Logged cost optimization metrics for {metrics.symbol} to W&B queue")
            
        except Exception as e:
            logger.error(f"Failed to log cost metrics to W&B: {e}")
    
    async def _log_trend_analysis_to_wandb(self, analysis: CostTrendAnalysis) -> None:
        """Log cost trend analysis to W&B"""
        try:
            wandb_data = {
                'experiment_id': self.experiment_id,
                'type': 'cost_trend_analysis',
                'symbol': analysis.symbol,
                'analysis_period_hours': analysis.analysis_period_hours,
                
                # Trend metrics
                'avg_cost_bps': analysis.avg_cost_bps,
                'cost_volatility': analysis.cost_volatility,
                'cost_trend_direction': analysis.cost_trend_direction,
                'cost_improvement_rate': analysis.cost_improvement_rate,
                
                # Optimization effectiveness
                'optimization_adoption_rate': analysis.optimization_adoption_rate,
                'avg_savings_per_optimization': analysis.avg_savings_per_optimization,
                'cost_prediction_mae': analysis.cost_prediction_mae,
                
                'step': int(datetime.now().timestamp())
            }
            
            wandb_queue_key = f"wandb_logs:cost_trends:{analysis.symbol}"
            await self.redis_service.setex(
                wandb_queue_key,
                3600,
                json.dumps(wandb_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to log trend analysis to W&B: {e}")
    
    async def _log_effectiveness_report_to_wandb(self, report: CostEffectivenessReport) -> None:
        """Log cost effectiveness report to W&B"""
        try:
            wandb_data = {
                'experiment_id': self.experiment_id,
                'type': 'cost_effectiveness_report',
                'optimization_strategy': report.optimization_strategy,
                'measurement_period_days': report.measurement_period_days,
                
                # Effectiveness metrics
                'baseline_cost_bps': report.baseline_cost_bps,
                'optimized_cost_bps': report.optimized_cost_bps,
                'total_improvement_bps': report.total_improvement_bps,
                'improvement_percentage': report.improvement_percentage,
                
                # ROI metrics
                'total_trades_analyzed': report.total_trades_analyzed,
                'total_cost_savings_usd': report.total_cost_savings_usd,
                'optimization_roi': report.optimization_roi,
                
                # Success metrics
                'optimization_success_rate': report.optimization_success_rate,
                'recommendation_accuracy': report.recommendation_accuracy,
                
                'step': int(datetime.now().timestamp())
            }
            
            wandb_queue_key = f"wandb_logs:cost_effectiveness:{report.optimization_strategy}"
            await self.redis_service.setex(
                wandb_queue_key,
                3600,
                json.dumps(wandb_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to log effectiveness report to W&B: {e}")
    
    # Data storage and retrieval methods
    
    async def _store_cost_history(self, metrics: CostOptimizationMetrics) -> None:
        """Store cost metrics in historical data"""
        try:
            history_key = f"cost_history:{metrics.symbol}"
            
            # Get existing history
            existing_history = await self.redis_service.get(history_key)
            if existing_history:
                history_list = json.loads(existing_history)
            else:
                history_list = []
            
            # Add new entry
            history_entry = {
                "timestamp": metrics.timestamp.isoformat(),
                "total_cost_bps": metrics.total_cost_bps,
                "exchange_fees_usd": metrics.exchange_fees_usd,
                "slippage_cost_usd": metrics.slippage_cost_usd,
                "market_impact_cost_usd": metrics.market_impact_cost_usd,
                "optimal_exchange": metrics.optimal_exchange,
                "cost_improvement_bps": metrics.cost_improvement_bps
            }
            
            history_list.append(history_entry)
            
            # Keep only recent history (last 1000 entries)
            if len(history_list) > 1000:
                history_list = history_list[-1000:]
            
            # Store updated history
            await self.redis_service.setex(
                history_key,
                86400,  # 24 hours
                json.dumps(history_list, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to store cost history: {e}")
    
    async def _store_cost_optimization_analytics(self, metrics: CostOptimizationMetrics) -> None:
        """Store cost optimization analytics in Supabase"""
        if not self.supabase_service:
            return
            
        try:
            analytics_data = {
                'timestamp': metrics.timestamp.isoformat(),
                'strategy_name': metrics.strategy_name,
                'symbol': metrics.symbol,
                'action': 'COST_OPTIMIZATION_TRACKING',
                'quantity': metrics.trade_size_usd,
                'price': metrics.total_cost_bps,
                'metadata': {
                    'experiment_id': metrics.experiment_id,
                    'cost_metrics': asdict(metrics),
                    'tracking_type': 'wandb_cost_optimization'
                }
            }
            
            await self.supabase_service.store_trade_execution(analytics_data)
            
        except Exception as e:
            logger.error(f"Failed to store cost optimization analytics: {e}")
    
    # Data analysis helper methods (simplified implementations)
    
    async def _get_cost_history(self, symbol: str, hours: int) -> List[Dict]:
        """Get cost history for trend analysis"""
        try:
            history_key = f"cost_history:{symbol}"
            cached_history = await self.redis_service.get(history_key)
            
            if cached_history:
                all_history = json.loads(cached_history)
                cutoff_time = datetime.now() - timedelta(hours=hours)
                
                filtered_history = [
                    entry for entry in all_history
                    if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
                ]
                return filtered_history
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to get cost history: {e}")
            return []
    
    async def _analyze_component_trend(self, cost_history: List[Dict], component: str) -> Dict[str, float]:
        """Analyze trend for a specific cost component"""
        try:
            values = [entry.get(component, 0) for entry in cost_history]
            
            if len(values) < 2:
                return {"avg": 0, "trend": 0, "volatility": 0}
            
            avg_value = np.mean(values)
            trend_slope = np.polyfit(range(len(values)), values, 1)[0]
            volatility = np.std(values)
            
            return {
                "avg": avg_value,
                "trend": trend_slope,
                "volatility": volatility
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze component trend: {e}")
            return {"avg": 0, "trend": 0, "volatility": 0}
    
    async def _analyze_optimization_effectiveness(self, cost_history: List[Dict]) -> Dict[str, float]:
        """Analyze optimization effectiveness metrics"""
        try:
            total_entries = len(cost_history)
            optimized_entries = len([e for e in cost_history if e.get("cost_improvement_bps", 0) > 0])
            
            adoption_rate = optimized_entries / total_entries if total_entries > 0 else 0
            
            improvements = [e.get("cost_improvement_bps", 0) for e in cost_history if e.get("cost_improvement_bps", 0) > 0]
            avg_savings = np.mean(improvements) if improvements else 0
            
            # Simplified prediction MAE
            prediction_mae = 2.5  # Assume 2.5 bps average error
            
            return {
                "adoption_rate": adoption_rate,
                "avg_savings": avg_savings,
                "prediction_mae": prediction_mae
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze optimization effectiveness: {e}")
            return {"adoption_rate": 0, "avg_savings": 0, "prediction_mae": 10}
    
    async def _analyze_exchange_performance(self, cost_history: List[Dict]) -> Dict[str, Any]:
        """Analyze exchange performance metrics"""
        try:
            exchanges = [entry.get("optimal_exchange", "binance") for entry in cost_history]
            
            # Exchange frequency
            from collections import Counter
            exchange_counts = Counter(exchanges)
            
            # Exchange stability (simplified)
            exchange_stability = {
                exchange: 0.9 - (count / len(cost_history)) * 0.3
                for exchange, count in exchange_counts.items()
            }
            
            return {
                "frequency": dict(exchange_counts),
                "stability": exchange_stability,
                "arbitrage_count": len(cost_history) // 20  # Assume 5% arbitrage rate
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze exchange performance: {e}")
            return {"frequency": {}, "stability": {}, "arbitrage_count": 0}
    
    # Placeholder methods for comprehensive functionality
    
    async def _get_baseline_performance_data(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Get baseline performance data for effectiveness measurement"""
        # Simulate baseline data
        return {
            "avg_cost_bps": 15.0,
            "trade_count": 100,
            "total_volume_usd": 1000000
        }
    
    async def _get_optimized_performance_data(
        self, optimization_strategy: str, start_time: datetime, end_time: datetime
    ) -> Dict[str, Any]:
        """Get optimized performance data"""
        # Simulate optimized data
        return {
            "avg_cost_bps": 12.0,  # 3 bps improvement
            "trade_count": 120,
            "total_volume_usd": 1200000,
            "success_rate": 0.85,
            "avg_implementation_time_ms": 150,
            "recommendation_accuracy": 0.82,
            "optimizations": []
        }
    
    async def _analyze_optimization_breakdown(self, optimizations: List[Dict]) -> Dict[str, float]:
        """Analyze optimization breakdown by type"""
        return {
            "exchange_savings": 1.5,  # bps
            "timing_savings": 0.8,
            "size_savings": 0.5,
            "order_type_savings": 0.2
        }
    
    # Fallback methods
    
    def _create_fallback_cost_metrics(
        self, strategy_name: str, symbol: str, trade_size_usd: float
    ) -> CostOptimizationMetrics:
        """Create fallback cost metrics when calculation fails"""
        return CostOptimizationMetrics(
            experiment_id=self.experiment_id,
            strategy_name=strategy_name,
            symbol=symbol,
            trade_size_usd=trade_size_usd,
            total_cost_usd=trade_size_usd * 0.0015,  # 15 bps fallback
            exchange_fees_usd=trade_size_usd * 0.001,
            slippage_cost_usd=trade_size_usd * 0.0005,
            market_impact_cost_usd=0,
            funding_costs_usd=0,
            withdrawal_fees_usd=0,
            total_cost_bps=15.0,
            cost_improvement_bps=0,
            cost_prediction_accuracy=0.5,
            optimal_exchange="binance",
            cost_savings_vs_default_bps=0,
            exchange_cost_ranking={"binance": 15.0},
            slippage_estimation_accuracy=0.5,
            slippage_improvement_bps=0,
            model_slippage_vs_actual=1.0,
            calculation_time_ms=50.0,
            optimization_suggestions_count=0,
            confidence_score=0.3,
            timestamp=datetime.now()
        )
    
    def _create_fallback_trend_analysis(self, symbol: str, hours: int) -> CostTrendAnalysis:
        """Create fallback trend analysis"""
        return CostTrendAnalysis(
            symbol=symbol,
            analysis_period_hours=hours,
            avg_cost_bps=15.0,
            cost_volatility=3.0,
            cost_trend_direction="stable",
            cost_improvement_rate=0.0,
            fees_trend={"avg": 10.0, "trend": 0, "volatility": 1.0},
            slippage_trend={"avg": 5.0, "trend": 0, "volatility": 2.0},
            market_impact_trend={"avg": 0.0, "trend": 0, "volatility": 0.0},
            optimization_adoption_rate=0.0,
            avg_savings_per_optimization=0.0,
            cost_prediction_mae=5.0,
            best_exchange_frequency={"binance": 10},
            exchange_cost_stability={"binance": 0.8},
            arbitrage_opportunities_detected=0,
            timestamp=datetime.now()
        )
    
    def _create_fallback_effectiveness_report(
        self, optimization_strategy: str, days: int
    ) -> CostEffectivenessReport:
        """Create fallback effectiveness report"""
        return CostEffectivenessReport(
            optimization_strategy=optimization_strategy,
            measurement_period_days=days,
            baseline_cost_bps=15.0,
            optimized_cost_bps=15.0,
            total_improvement_bps=0.0,
            improvement_percentage=0.0,
            total_trades_analyzed=0,
            total_volume_usd=0.0,
            total_cost_savings_usd=0.0,
            optimization_roi=0.0,
            exchange_optimization_savings=0.0,
            timing_optimization_savings=0.0,
            size_optimization_savings=0.0,
            order_type_optimization_savings=0.0,
            optimization_success_rate=0.0,
            avg_implementation_time_ms=200.0,
            recommendation_accuracy=0.5,
            timestamp=datetime.now()
        )

# Factory function for easy initialization
async def create_wandb_cost_tracker(
    redis_url: str,
    cost_calculator: CostCalculator,
    slippage_estimator: Optional[EnhancedSlippageEstimator] = None,
    supabase_url: Optional[str] = None,
    supabase_key: Optional[str] = None,
    wandb_config: Optional[Dict] = None,
    config: Optional[Dict] = None
) -> WandBCostTracker:
    """Factory function to create W&B cost optimization tracker"""
    
    from app.services.mcp.redis_service import RedisService
    from app.services.mcp.supabase_service import SupabaseService
    
    redis_service = RedisService(redis_url)
    
    supabase_service = None
    if supabase_url and supabase_key:
        supabase_service = SupabaseService(supabase_url, supabase_key)
    
    return WandBCostTracker(
        redis_service=redis_service,
        cost_calculator=cost_calculator,
        slippage_estimator=slippage_estimator,
        supabase_service=supabase_service,
        wandb_config=wandb_config,
        config=config
    )

# Example usage and testing
if __name__ == "__main__":
    async def test_wandb_cost_tracker():
        """Test the W&B cost optimization tracker"""
        
        # This would be used for testing the cost optimization tracker
        print("W&B Cost Optimization Tracker test implementation")
        
        # In production, would integrate with real cost calculator and services
        
    asyncio.run(test_wandb_cost_tracker())