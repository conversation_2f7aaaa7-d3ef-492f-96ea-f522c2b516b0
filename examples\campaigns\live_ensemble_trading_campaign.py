#!/usr/bin/env python3
"""
Live Ensemble Trading Campaign
Execute comprehensive ensemble strategy trading on Binance Futures testnet.
"""

import asyncio
import os
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from binance.client import Client
from binance.enums import *
import traceback

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleTradingStrategy:
    """
    Comprehensive ensemble trading strategy implementation
    """
    
    def __init__(self, client: Client, initial_balance: float):
        self.client = client
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.trades = []
        self.positions = {}
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'peak_balance': initial_balance,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'execution_times': [],
            'strategy_performance': {
                'grid': {'trades': 0, 'pnl': 0.0, 'signals': 0},
                'technical': {'trades': 0, 'pnl': 0.0, 'signals': 0},
                'trend': {'trades': 0, 'pnl': 0.0, 'signals': 0}
            }
        }
        
        # Risk management parameters
        self.risk_per_trade = 0.02  # 2% risk per trade
        self.max_portfolio_risk = 0.10  # 10% max portfolio exposure
        self.stop_loss_pct = 0.025  # 2.5% stop loss
        self.take_profit_pct = 0.05  # 5% take profit (2:1 R:R)
        self.min_trade_size = 0.001  # Minimum BTC trade size
        self.min_notional = 100  # Minimum USDT notional
        
        # Strategy weights (will be optimized during trading)
        self.strategy_weights = {
            'grid': 0.33,
            'technical': 0.33,
            'trend': 0.34
        }
        
        # Market data storage
        self.market_data = {
            'price_history': [],
            'volume_history': [],
            'indicators': {}
        }

    async def get_market_data(self, symbol='BTCUSDT', interval='1m', limit=100):
        """Get current market data and calculate indicators"""
        try:
            # Get kline data
            klines = self.client.futures_klines(symbol=symbol, interval=interval, limit=limit)
            
            prices = []
            volumes = []
            for kline in klines:
                close_price = float(kline[4])
                volume = float(kline[5])
                prices.append(close_price)
                volumes.append(volume)
            
            self.market_data['price_history'] = prices
            self.market_data['volume_history'] = volumes
            
            # Calculate technical indicators
            await self.calculate_indicators(prices)
            
            return prices[-1]  # Return current price
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return None

    async def calculate_indicators(self, prices):
        """Calculate technical indicators for strategy signals"""
        try:
            prices_array = np.array(prices)
            
            # Simple Moving Averages
            sma_20 = np.mean(prices_array[-20:]) if len(prices_array) >= 20 else prices_array[-1]
            sma_50 = np.mean(prices_array[-50:]) if len(prices_array) >= 50 else prices_array[-1]
            
            # RSI calculation
            rsi = self.calculate_rsi(prices_array)
            
            # MACD calculation
            macd, macd_signal = self.calculate_macd(prices_array)
            
            # Bollinger Bands
            bb_upper, bb_lower, bb_middle = self.calculate_bollinger_bands(prices_array)
            
            # ATR for volatility
            atr = self.calculate_atr(prices_array)
            
            self.market_data['indicators'] = {
                'sma_20': sma_20,
                'sma_50': sma_50,
                'rsi': rsi,
                'macd': macd,
                'macd_signal': macd_signal,
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'bb_middle': bb_middle,
                'atr': atr,
                'current_price': prices[-1]
            }
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI"""
        try:
            if len(prices) < period + 1:
                return 50  # Neutral RSI
            
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
            
        except Exception:
            return 50

    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """Calculate MACD"""
        try:
            if len(prices) < slow:
                return 0, 0
            
            prices_array = np.array(prices)
            exp1 = pd.Series(prices_array).ewm(span=fast).mean()
            exp2 = pd.Series(prices_array).ewm(span=slow).mean()
            
            macd_line = exp1.iloc[-1] - exp2.iloc[-1]
            macd_signal = pd.Series([macd_line]).ewm(span=signal).mean().iloc[-1]
            
            return macd_line, macd_signal
            
        except Exception:
            return 0, 0

    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """Calculate Bollinger Bands"""
        try:
            if len(prices) < period:
                current_price = prices[-1]
                return current_price * 1.02, current_price * 0.98, current_price
            
            prices_array = np.array(prices[-period:])
            middle = np.mean(prices_array)
            std = np.std(prices_array)
            
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            
            return upper, lower, middle
            
        except Exception:
            current_price = prices[-1]
            return current_price * 1.02, current_price * 0.98, current_price

    def calculate_atr(self, prices, period=14):
        """Calculate Average True Range"""
        try:
            if len(prices) < period + 1:
                return prices[-1] * 0.02  # 2% default volatility
            
            # Simplified ATR using price ranges
            ranges = []
            for i in range(1, len(prices)):
                high_low = abs(prices[i] - prices[i-1])
                ranges.append(high_low)
            
            atr = np.mean(ranges[-period:])
            return atr
            
        except Exception:
            return prices[-1] * 0.02

    async def generate_ensemble_signal(self):
        """Generate trading signal from ensemble of strategies"""
        try:
            current_price = await self.get_market_data()
            if not current_price:
                return None, 0
            
            indicators = self.market_data['indicators']
            
            # Grid Strategy Signal
            grid_signal, grid_confidence = self.grid_strategy_signal(indicators)
            
            # Technical Analysis Signal
            technical_signal, technical_confidence = self.technical_analysis_signal(indicators)
            
            # Trend Following Signal
            trend_signal, trend_confidence = self.trend_following_signal(indicators)
            
            # Update strategy signal counts
            self.performance_metrics['strategy_performance']['grid']['signals'] += 1
            self.performance_metrics['strategy_performance']['technical']['signals'] += 1
            self.performance_metrics['strategy_performance']['trend']['signals'] += 1
            
            # Weighted ensemble decision
            total_signal = (
                grid_signal * self.strategy_weights['grid'] * grid_confidence +
                technical_signal * self.strategy_weights['technical'] * technical_confidence +
                trend_signal * self.strategy_weights['trend'] * trend_confidence
            )
            
            total_confidence = (
                grid_confidence * self.strategy_weights['grid'] +
                technical_confidence * self.strategy_weights['technical'] +
                trend_confidence * self.strategy_weights['trend']
            )
            
            # Determine final signal
            if total_signal > 0.6 and total_confidence > 0.6:
                return 'BUY', total_confidence
            elif total_signal < -0.6 and total_confidence > 0.6:
                return 'SELL', total_confidence
            else:
                return 'HOLD', total_confidence
                
        except Exception as e:
            logger.error(f"Error generating ensemble signal: {e}")
            return None, 0

    def grid_strategy_signal(self, indicators):
        """Grid strategy logic"""
        try:
            current_price = indicators['current_price']
            bb_upper = indicators['bb_upper']
            bb_lower = indicators['bb_lower']
            bb_middle = indicators['bb_middle']
            
            # Grid logic: buy near support, sell near resistance
            price_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            
            if price_position < 0.3:  # Near lower band
                return 1.0, 0.8  # Strong buy signal
            elif price_position > 0.7:  # Near upper band
                return -1.0, 0.8  # Strong sell signal
            elif 0.4 < price_position < 0.6:  # Near middle
                return 0.0, 0.5  # Neutral
            else:
                return 0.0, 0.3  # Weak signal
                
        except Exception:
            return 0.0, 0.0

    def technical_analysis_signal(self, indicators):
        """Technical analysis strategy logic"""
        try:
            rsi = indicators['rsi']
            macd = indicators['macd']
            macd_signal = indicators['macd_signal']
            sma_20 = indicators['sma_20']
            sma_50 = indicators['sma_50']
            current_price = indicators['current_price']
            
            signals = []
            
            # RSI signals
            if rsi < 30:
                signals.append(1.0)  # Oversold - buy
            elif rsi > 70:
                signals.append(-1.0)  # Overbought - sell
            else:
                signals.append(0.0)
            
            # MACD signals
            if macd > macd_signal:
                signals.append(0.5)  # Bullish
            else:
                signals.append(-0.5)  # Bearish
            
            # Moving average signals
            if current_price > sma_20 > sma_50:
                signals.append(0.7)  # Strong uptrend
            elif current_price < sma_20 < sma_50:
                signals.append(-0.7)  # Strong downtrend
            else:
                signals.append(0.0)
            
            avg_signal = np.mean(signals)
            confidence = min(0.9, abs(avg_signal) + 0.1)
            
            return avg_signal, confidence
            
        except Exception:
            return 0.0, 0.0

    def trend_following_signal(self, indicators):
        """Trend following strategy logic"""
        try:
            sma_20 = indicators['sma_20']
            sma_50 = indicators['sma_50']
            current_price = indicators['current_price']
            atr = indicators['atr']
            
            # Trend strength
            trend_strength = abs(sma_20 - sma_50) / atr if atr > 0 else 0
            
            # Trend direction
            if current_price > sma_20 > sma_50:
                trend_signal = min(1.0, trend_strength / 2)
                confidence = min(0.9, trend_strength / 3 + 0.4)
            elif current_price < sma_20 < sma_50:
                trend_signal = -min(1.0, trend_strength / 2)
                confidence = min(0.9, trend_strength / 3 + 0.4)
            else:
                trend_signal = 0.0
                confidence = 0.3
            
            return trend_signal, confidence
            
        except Exception:
            return 0.0, 0.0

    def calculate_position_size(self, current_price: float, confidence: float):
        """Calculate position size based on risk management"""
        try:
            # Account for current balance
            available_balance = self.current_balance
            
            # Risk amount per trade
            risk_amount = available_balance * self.risk_per_trade * confidence
            
            # Calculate position size based on stop loss
            stop_distance = current_price * self.stop_loss_pct
            position_value = risk_amount / (stop_distance / current_price)
            
            # Position size in BTC
            position_size = position_value / current_price
            
            # Apply constraints
            position_size = max(self.min_trade_size, position_size)
            
            # Check notional value
            notional_value = position_size * current_price
            if notional_value < self.min_notional:
                position_size = self.min_notional / current_price
            
            # Check maximum portfolio exposure
            max_position_value = available_balance * self.max_portfolio_risk
            if position_size * current_price > max_position_value:
                position_size = max_position_value / current_price
            
            return round(position_size, 3)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return self.min_trade_size

    async def execute_trade(self, signal: str, confidence: float, current_price: float):
        """Execute trade on Binance Futures"""
        try:
            if signal == 'HOLD':
                return None
            
            start_time = time.time()
            
            # Calculate position size
            position_size = self.calculate_position_size(current_price, confidence)
            
            # Determine order side
            side = SIDE_BUY if signal == 'BUY' else SIDE_SELL
            
            logger.info(f"Executing {signal} order: {position_size} BTC at ${current_price:,.2f}")
            
            # Place market order
            order = self.client.futures_create_order(
                symbol='BTCUSDT',
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=position_size
            )
            
            execution_time = (time.time() - start_time) * 1000
            self.performance_metrics['execution_times'].append(execution_time)
            
            # Calculate stop loss and take profit levels
            if signal == 'BUY':
                stop_loss = current_price * (1 - self.stop_loss_pct)
                take_profit = current_price * (1 + self.take_profit_pct)
            else:
                stop_loss = current_price * (1 + self.stop_loss_pct)
                take_profit = current_price * (1 - self.take_profit_pct)
            
            # Store trade information
            trade_info = {
                'timestamp': datetime.now().isoformat(),
                'order_id': order['orderId'],
                'symbol': 'BTCUSDT',
                'side': signal,
                'quantity': position_size,
                'price': float(order.get('avgPrice', current_price)),
                'confidence': confidence,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'execution_time_ms': execution_time,
                'status': 'FILLED'
            }
            
            self.trades.append(trade_info)
            self.performance_metrics['total_trades'] += 1
            
            logger.info(f"✅ Trade executed: {order['orderId']} in {execution_time:.1f}ms")
            logger.info(f"   Stop Loss: ${stop_loss:,.2f}, Take Profit: ${take_profit:,.2f}")
            
            return trade_info
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None

    async def monitor_positions(self):
        """Monitor and manage open positions"""
        try:
            positions = self.client.futures_position_information(symbol='BTCUSDT')
            
            for position in positions:
                position_amt = float(position['positionAmt'])
                if position_amt != 0:
                    # Update position tracking
                    unrealized_pnl = float(position['unrealizedProfit'])
                    entry_price = float(position['entryPrice'])
                    mark_price = float(position['markPrice'])
                    
                    logger.info(f"📊 Position: {position_amt} BTC, Entry: ${entry_price:,.2f}, Mark: ${mark_price:,.2f}, PnL: ${unrealized_pnl:,.2f}")
                    
                    # Check for stop loss or take profit conditions
                    await self.check_exit_conditions(position)
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")

    async def check_exit_conditions(self, position):
        """Check if position should be closed based on stop loss or take profit"""
        try:
            position_amt = float(position['positionAmt'])
            entry_price = float(position['entryPrice'])
            mark_price = float(position['markPrice'])
            unrealized_pnl = float(position['unrealizedProfit'])
            
            # Find corresponding trade
            matching_trade = None
            for trade in reversed(self.trades):
                if trade.get('status') == 'FILLED' and abs(float(trade['quantity'])) == abs(position_amt):
                    matching_trade = trade
                    break
            
            if not matching_trade:
                return
            
            should_close = False
            close_reason = ""
            
            # Check stop loss
            if position_amt > 0:  # Long position
                if mark_price <= matching_trade['stop_loss']:
                    should_close = True
                    close_reason = "STOP_LOSS"
                elif mark_price >= matching_trade['take_profit']:
                    should_close = True
                    close_reason = "TAKE_PROFIT"
            else:  # Short position
                if mark_price >= matching_trade['stop_loss']:
                    should_close = True
                    close_reason = "STOP_LOSS"
                elif mark_price <= matching_trade['take_profit']:
                    should_close = True
                    close_reason = "TAKE_PROFIT"
            
            if should_close:
                await self.close_position(position, matching_trade, close_reason, unrealized_pnl)
            
        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")

    async def close_position(self, position, original_trade, reason, pnl):
        """Close a position"""
        try:
            position_amt = float(position['positionAmt'])
            
            # Determine close side
            close_side = SIDE_SELL if position_amt > 0 else SIDE_BUY
            close_quantity = abs(position_amt)
            
            logger.info(f"🔄 Closing position: {close_quantity} BTC, Reason: {reason}, PnL: ${pnl:.2f}")
            
            # Place close order
            close_order = self.client.futures_create_order(
                symbol='BTCUSDT',
                side=close_side,
                type=ORDER_TYPE_MARKET,
                quantity=close_quantity
            )
            
            # Update trade record
            original_trade['close_timestamp'] = datetime.now().isoformat()
            original_trade['close_price'] = float(close_order.get('avgPrice', 0))
            original_trade['close_reason'] = reason
            original_trade['realized_pnl'] = pnl
            original_trade['status'] = 'CLOSED'
            
            # Update performance metrics
            self.current_balance += pnl
            self.performance_metrics['total_pnl'] += pnl
            
            if pnl > 0:
                self.performance_metrics['winning_trades'] += 1
                self.performance_metrics['avg_win'] = (
                    (self.performance_metrics['avg_win'] * (self.performance_metrics['winning_trades'] - 1) + pnl) /
                    self.performance_metrics['winning_trades']
                )
            else:
                self.performance_metrics['losing_trades'] += 1
                self.performance_metrics['avg_loss'] = (
                    (self.performance_metrics['avg_loss'] * (self.performance_metrics['losing_trades'] - 1) + abs(pnl)) /
                    self.performance_metrics['losing_trades']
                )
            
            # Update peak balance and drawdown
            if self.current_balance > self.performance_metrics['peak_balance']:
                self.performance_metrics['peak_balance'] = self.current_balance
            
            drawdown = (self.performance_metrics['peak_balance'] - self.current_balance) / self.performance_metrics['peak_balance']
            self.performance_metrics['max_drawdown'] = max(self.performance_metrics['max_drawdown'], drawdown)
            
            # Calculate win rate
            total_closed = self.performance_metrics['winning_trades'] + self.performance_metrics['losing_trades']
            if total_closed > 0:
                self.performance_metrics['win_rate'] = self.performance_metrics['winning_trades'] / total_closed
            
            logger.info(f"✅ Position closed: PnL ${pnl:.2f}, Balance: ${self.current_balance:.2f}")
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")

    def calculate_performance_stats(self):
        """Calculate comprehensive performance statistics"""
        try:
            metrics = self.performance_metrics
            
            # Profit factor
            total_wins = metrics['avg_win'] * metrics['winning_trades']
            total_losses = metrics['avg_loss'] * metrics['losing_trades']
            metrics['profit_factor'] = total_wins / max(total_losses, 1)
            
            # Sharpe ratio (simplified)
            if len(self.trades) > 0:
                returns = [trade.get('realized_pnl', 0) for trade in self.trades if trade.get('status') == 'CLOSED']
                if len(returns) > 1:
                    avg_return = np.mean(returns)
                    std_return = np.std(returns)
                    metrics['sharpe_ratio'] = avg_return / max(std_return, 0.01) if std_return > 0 else 0
            
            # Execution performance
            if metrics['execution_times']:
                metrics['avg_execution_time'] = np.mean(metrics['execution_times'])
                metrics['max_execution_time'] = max(metrics['execution_times'])
                metrics['min_execution_time'] = min(metrics['execution_times'])
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance stats: {e}")
            return self.performance_metrics

    async def run_trading_campaign(self, duration_minutes: int = 120, trade_frequency_seconds: int = 60):
        """Run the complete trading campaign"""
        logger.info("=" * 80)
        logger.info("🚀 STARTING ENSEMBLE TRADING CAMPAIGN")
        logger.info("=" * 80)
        logger.info(f"Initial Balance: ${self.initial_balance:,.2f} USDT")
        logger.info(f"Campaign Duration: {duration_minutes} minutes")
        logger.info(f"Trade Frequency: Every {trade_frequency_seconds} seconds")
        logger.info(f"Risk per Trade: {self.risk_per_trade*100:.1f}%")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        trade_count = 0
        max_trades = 50  # Safety limit
        
        try:
            while time.time() < end_time and trade_count < max_trades:
                # Generate ensemble signal
                signal, confidence = await self.generate_ensemble_signal()
                
                if signal and signal != 'HOLD' and confidence > 0.6:
                    current_price = self.market_data['indicators']['current_price']
                    trade_info = await self.execute_trade(signal, confidence, current_price)
                    
                    if trade_info:
                        trade_count += 1
                        logger.info(f"Trade #{trade_count} executed: {signal} with {confidence:.2f} confidence")
                
                # Monitor existing positions
                await self.monitor_positions()
                
                # Update performance metrics
                self.calculate_performance_stats()
                
                # Log current status
                if trade_count % 5 == 0 and trade_count > 0:
                    logger.info(f"📊 Status: {trade_count} trades, Balance: ${self.current_balance:.2f}, PnL: ${self.performance_metrics['total_pnl']:.2f}")
                
                # Wait before next iteration
                await asyncio.sleep(trade_frequency_seconds)
            
            # Final position cleanup
            logger.info("🔄 Closing any remaining positions...")
            await self.close_all_positions()
            
            # Final performance calculation
            final_metrics = self.calculate_performance_stats()
            
            logger.info("=" * 80)
            logger.info("🏁 TRADING CAMPAIGN COMPLETED")
            logger.info("=" * 80)
            
            return final_metrics, self.trades
            
        except Exception as e:
            logger.error(f"Error in trading campaign: {e}")
            traceback.print_exc()
            return self.performance_metrics, self.trades

    async def close_all_positions(self):
        """Close all open positions at campaign end"""
        try:
            positions = self.client.futures_position_information(symbol='BTCUSDT')
            
            for position in positions:
                position_amt = float(position['positionAmt'])
                if position_amt != 0:
                    close_side = SIDE_SELL if position_amt > 0 else SIDE_BUY
                    
                    self.client.futures_create_order(
                        symbol='BTCUSDT',
                        side=close_side,
                        type=ORDER_TYPE_MARKET,
                        quantity=abs(position_amt)
                    )
                    
                    logger.info(f"🔄 Closed position: {position_amt} BTC")
            
        except Exception as e:
            logger.error(f"Error closing all positions: {e}")

async def main():
    """Main execution function"""
    try:
        # Load environment and initialize client
        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        client = Client(api_key, api_secret, testnet=True)
        
        # Get initial account balance
        account = client.futures_account()
        initial_balance = float(account['availableBalance'])
        
        logger.info(f"Starting ensemble trading with ${initial_balance:,.2f} USDT")
        
        # Initialize trading strategy
        strategy = EnsembleTradingStrategy(client, initial_balance)
        
        # Run trading campaign (2 hours, trade every minute)
        final_metrics, trades = await strategy.run_trading_campaign(
            duration_minutes=120,
            trade_frequency_seconds=60
        )
        
        # Save results
        results = {
            'campaign_summary': {
                'start_balance': initial_balance,
                'end_balance': strategy.current_balance,
                'total_return': strategy.current_balance - initial_balance,
                'return_percentage': ((strategy.current_balance - initial_balance) / initial_balance) * 100,
                'campaign_duration_minutes': 120
            },
            'performance_metrics': final_metrics,
            'trades': trades,
            'timestamp': datetime.now().isoformat()
        }
        
        # Save to file
        with open('ensemble_trading_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        # Print summary
        print("\n" + "=" * 80)
        print("📊 ENSEMBLE TRADING CAMPAIGN RESULTS")
        print("=" * 80)
        print(f"Initial Balance: ${initial_balance:,.2f} USDT")
        print(f"Final Balance: ${strategy.current_balance:,.2f} USDT")
        print(f"Total Return: ${results['campaign_summary']['total_return']:,.2f} USDT")
        print(f"Return %: {results['campaign_summary']['return_percentage']:.2f}%")
        print(f"Total Trades: {final_metrics['total_trades']}")
        print(f"Win Rate: {final_metrics['win_rate']*100:.1f}%")
        print(f"Profit Factor: {final_metrics['profit_factor']:.2f}")
        print(f"Max Drawdown: {final_metrics['max_drawdown']*100:.2f}%")
        print(f"Sharpe Ratio: {final_metrics['sharpe_ratio']:.2f}")
        if final_metrics.get('avg_execution_time'):
            print(f"Avg Execution Time: {final_metrics['avg_execution_time']:.1f}ms")
        print("=" * 80)
        print("📄 Detailed results saved to: ensemble_trading_results.json")
        
        return True
        
    except Exception as e:
        logger.error(f"Campaign failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)