"""SQLAlchemy model for training sessions."""
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Integer, Boolean, Text
from sqlalchemy.dialects.postgresql import JSONB

from app.models.base import Base

class TrainingSessionDB(Base):
    """SQLAlchemy model for persisting ML training sessions."""
    __tablename__ = "training_sessions"

    # Primary key is the session ID
    id = Column(String, primary_key=True)
    
    # Training parameters
    symbol = Column(String, nullable=False, index=True)
    timeframe = Column(String, nullable=False)
    lookback_days = Column(Integer, nullable=False)
    total_timesteps = Column(Integer, nullable=False)
    optimize = Column(Boolean, nullable=False, default=False)
    n_trials = Column(Integer, nullable=True)
    
    # Training status
    status = Column(String, nullable=False, index=True)  # 'running', 'completed', 'failed'
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=True)
    error = Column(Text, nullable=True)
    
    # Training results
    model_path = Column(String, nullable=True)
    metrics = Column(JSONB, nullable=True)
    hyperparameters = Column(JSONB, nullable=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
