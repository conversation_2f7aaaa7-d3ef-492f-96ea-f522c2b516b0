{"ast": null, "code": "/**\n * Authentication Service\n *\n * This service handles authentication with the backend API.\n */\nimport axios from 'axios';\nimport tokenService from './tokenService';\n\n// API URL from environment variable\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n/**\n * Login with username and password\n *\n * @param username Username\n * @param password Password\n * @returns Promise with login result\n */\nexport const login = async (username, password) => {\n  try {\n    // Create form data for login\n    const formData = new FormData();\n    formData.append('username', username);\n    formData.append('password', password);\n\n    // Send login request\n    const response = await axios.post(`${API_URL}/api/token`, formData);\n\n    // Save tokens to local storage\n    const {\n      access_token,\n      refresh_token,\n      token_type\n    } = response.data;\n    tokenService.saveTokens(access_token, refresh_token, token_type);\n    return true;\n  } catch (error) {\n    console.error('Login error:', error);\n    return false;\n  }\n};\n\n/**\n * Logout the user\n */\nexport const logout = () => {\n  tokenService.clearTokens();\n};\n\n/**\n * Refresh the access token using the refresh token\n *\n * @returns Promise with refresh result\n */\nexport const refreshToken = async () => {\n  try {\n    // Get refresh token from local storage\n    const refreshToken = tokenService.getRefreshToken();\n    if (!refreshToken) {\n      return false;\n    }\n\n    // Check if refresh token is expired\n    if (tokenService.isRefreshTokenExpired()) {\n      tokenService.clearTokens();\n      return false;\n    }\n\n    // Send refresh token request\n    const response = await axios.post(`${API_URL}/api/refresh-token`, {\n      refresh_token: refreshToken\n    });\n\n    // Save new tokens to local storage\n    const {\n      access_token,\n      refresh_token,\n      token_type\n    } = response.data;\n    tokenService.saveTokens(access_token, refresh_token, token_type);\n    return true;\n  } catch (error) {\n    console.error('Token refresh error:', error);\n    tokenService.clearTokens();\n    return false;\n  }\n};\nconst authService = {\n  login,\n  logout,\n  refreshToken\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "tokenService", "API_URL", "process", "env", "REACT_APP_API_URL", "login", "username", "password", "formData", "FormData", "append", "response", "post", "access_token", "refresh_token", "token_type", "data", "saveTokens", "error", "console", "logout", "clearTokens", "refreshToken", "getRefreshToken", "isRefreshTokenExpired", "authService"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/authService.ts"], "sourcesContent": ["/**\n * Authentication Service\n *\n * This service handles authentication with the backend API.\n */\nimport axios from 'axios';\nimport tokenService from './tokenService';\n\n// API URL from environment variable\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n/**\n * Login with username and password\n *\n * @param username Username\n * @param password Password\n * @returns Promise with login result\n */\nexport const login = async (username: string, password: string): Promise<boolean> => {\n  try {\n    // Create form data for login\n    const formData = new FormData();\n    formData.append('username', username);\n    formData.append('password', password);\n\n    // Send login request\n    const response = await axios.post(`${API_URL}/api/token`, formData);\n\n    // Save tokens to local storage\n    const { access_token, refresh_token, token_type } = response.data;\n    tokenService.saveTokens(access_token, refresh_token, token_type);\n\n    return true;\n  } catch (error) {\n    console.error('Login error:', error);\n    return false;\n  }\n};\n\n/**\n * Logout the user\n */\nexport const logout = (): void => {\n  tokenService.clearTokens();\n};\n\n/**\n * Refresh the access token using the refresh token\n *\n * @returns Promise with refresh result\n */\nexport const refreshToken = async (): Promise<boolean> => {\n  try {\n    // Get refresh token from local storage\n    const refreshToken = tokenService.getRefreshToken();\n    if (!refreshToken) {\n      return false;\n    }\n\n    // Check if refresh token is expired\n    if (tokenService.isRefreshTokenExpired()) {\n      tokenService.clearTokens();\n      return false;\n    }\n\n    // Send refresh token request\n    const response = await axios.post(`${API_URL}/api/refresh-token`, { refresh_token: refreshToken });\n\n    // Save new tokens to local storage\n    const { access_token, refresh_token, token_type } = response.data;\n    tokenService.saveTokens(access_token, refresh_token, token_type);\n\n    return true;\n  } catch (error) {\n    console.error('Token refresh error:', error);\n    tokenService.clearTokens();\n    return false;\n  }\n};\n\nconst authService = {\n  login,\n  logout,\n  refreshToken,\n};\n\nexport default authService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;;AAEzC;AACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,KAAK,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,QAAgB,KAAuB;EACnF,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;IACrCE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;;IAErC;IACA,MAAMI,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,GAAGX,OAAO,YAAY,EAAEO,QAAQ,CAAC;;IAEnE;IACA,MAAM;MAAEK,YAAY;MAAEC,aAAa;MAAEC;IAAW,CAAC,GAAGJ,QAAQ,CAACK,IAAI;IACjEhB,YAAY,CAACiB,UAAU,CAACJ,YAAY,EAAEC,aAAa,EAAEC,UAAU,CAAC;IAEhE,OAAO,IAAI;EACb,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,MAAM,GAAGA,CAAA,KAAY;EAChCpB,YAAY,CAACqB,WAAW,CAAC,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAA8B;EACxD,IAAI;IACF;IACA,MAAMA,YAAY,GAAGtB,YAAY,CAACuB,eAAe,CAAC,CAAC;IACnD,IAAI,CAACD,YAAY,EAAE;MACjB,OAAO,KAAK;IACd;;IAEA;IACA,IAAItB,YAAY,CAACwB,qBAAqB,CAAC,CAAC,EAAE;MACxCxB,YAAY,CAACqB,WAAW,CAAC,CAAC;MAC1B,OAAO,KAAK;IACd;;IAEA;IACA,MAAMV,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,GAAGX,OAAO,oBAAoB,EAAE;MAAEa,aAAa,EAAEQ;IAAa,CAAC,CAAC;;IAElG;IACA,MAAM;MAAET,YAAY;MAAEC,aAAa;MAAEC;IAAW,CAAC,GAAGJ,QAAQ,CAACK,IAAI;IACjEhB,YAAY,CAACiB,UAAU,CAACJ,YAAY,EAAEC,aAAa,EAAEC,UAAU,CAAC;IAEhE,OAAO,IAAI;EACb,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5ClB,YAAY,CAACqB,WAAW,CAAC,CAAC;IAC1B,OAAO,KAAK;EACd;AACF,CAAC;AAED,MAAMI,WAAW,GAAG;EAClBpB,KAAK;EACLe,MAAM;EACNE;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}