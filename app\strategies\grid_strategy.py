"""
Grid Trading strategy implementation for the Multi-Strategy Crypto Auto Trader.

This strategy creates a grid of buy and sell orders at predetermined price intervals
to capitalize on market volatility in sideways markets.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple, Any # Removed List
# import talib # No longer needed directly
# from talib import func # No longer needed directly
import logging # Import logging
import app.utils.technical_analysis as ta
from app.config.settings import Settings # Import Settings

from app.strategies.base_strategy import BaseStrategy


class GridStrategy(BaseStrategy):
    """Grid Trading strategy implementation.

    This strategy places a grid of buy and sell orders at predetermined price levels
    to profit from price oscillations in ranging markets.
    """

    def __init__(self, symbol: str, timeframe: str, settings: Settings): # Accept Settings object
        """Initialize the Grid Trading strategy.

        Args:
            symbol: The trading pair symbol (e.g., 'BTCUSDT')
            timeframe: The candlestick timeframe (e.g., '1h', '4h', '1d')
            settings: The application settings object containing strategy parameters.
        """
        # Pass empty dict to BaseStrategy as params are now in settings
        super().__init__(symbol, timeframe, {})
        self.settings = settings # Store settings object
        self.logger = logging.getLogger(__name__) # Initialize logger

    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market data to determine conditions suitable for grid trading.

        Args:
            data: DataFrame containing OHLCV data

        Returns:
            Dictionary containing market analysis results
        """
        result = {}
        df = data.copy()

        # Calculate ADX to determine if market is ranging
        df['ADX'] = ta.calculate_adx(df['high'], df['low'], df['close'], timeperiod=14)
        result['adx'] = df['ADX'].iloc[-1]
        result['is_ranging'] = result['adx'] < 20

        # Calculate ATR for volatility assessment
        df['ATR'] = ta.calculate_atr_volatility(df, window=self.settings.atr_periods)
        result['atr'] = df['ATR'].iloc[-1]
        result['volatility_ratio'] = result['atr'] / df['close'].iloc[-1] if df['close'].iloc[-1] != 0 else 0
        result['is_medium_volatility'] = 0.005 < result['volatility_ratio'] < 0.015

        # Identify support and resistance levels
        support, resistance = self._find_support_resistance(df)
        result['support'] = support
        result['resistance'] = resistance

        # Calculate support/resistance clarity
        price_range = resistance - support
        current_price = df['close'].iloc[-1]

        # Calculate how well current price is contained within S/R range
        if price_range > 0 and support < current_price < resistance:
            containment = min(current_price - support, resistance - current_price) / price_range
            result['sr_containment'] = containment
            result['has_clear_sr'] = containment > 0.2
        else:
            result['sr_containment'] = 0
            result['has_clear_sr'] = False

        # Volume analysis
        if 'volume' in df.columns:
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume_ma'].iloc[-1]
            result['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 0
            result['is_low_volume'] = result['volume_ratio'] < 1.2  # Lower/normal volume is better for grid
        else:
             result['volume_ratio'] = 1.0
             result['is_low_volume'] = True # Assume low if no volume data

        # Overall suitability score for grid trading
        result['is_suitable'] = (
            result['is_ranging'] and
            result['is_medium_volatility'] and
            result['has_clear_sr'] and
            price_range > 0
        )
        self.logger.debug(f"Grid suitability analysis: Ranging={result['is_ranging']}, MedVol={result['is_medium_volatility']}, ClearSR={result['has_clear_sr']}, PriceRange={price_range>0} -> Suitable={result['is_suitable']}")

        return result

    def _find_support_resistance(self, data: pd.DataFrame) -> Tuple[float, float]:
        """Find support and resistance levels.

        Args:
            data: DataFrame containing OHLCV data

        Returns:
            Tuple containing (support_price, resistance_price)
        """
        periods = self.settings.support_resistance_periods # Use settings
        if len(data) < periods:
             self.logger.warning(f"Not enough data ({len(data)}) for S/R calculation (requires {periods}).")
             return data['low'].min(), data['high'].max() # Fallback

        # Use recent highs/lows to establish support/resistance
        recent_highs = data['high'].iloc[-periods:]
        recent_lows = data['low'].iloc[-periods:]

        # Find clusters of highs and lows
        resistance = self._find_price_clusters(recent_highs)
        support = self._find_price_clusters(recent_lows)

        return support, resistance

    def _find_price_clusters(self, prices: pd.Series) -> float:
        """Find clusters of prices to identify support/resistance levels.

        Args:
            prices: Series of price values

        Returns:
            Price level representing the cluster
        """
        # Simple approach: use the median of the top/bottom quartile
        if len(prices) < 4:
            return prices.median() if not prices.empty else 0.0

        # Check if Series has a name attribute and if it contains 'high'
        is_high_series = hasattr(prices, 'name') and isinstance(prices.name, str) and 'high' in prices.name

        if is_high_series:
            # For resistance, use top quartile
            threshold = prices.quantile(0.75)
            cluster = prices[prices >= threshold]
        else:
            # For support, use bottom quartile
            threshold = prices.quantile(0.25)
            cluster = prices[prices <= threshold]

        return cluster.median() if not cluster.empty else prices.median()

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on the grid strategy.

        Args:
            data: DataFrame containing OHLCV data

        Returns:
            DataFrame with signals added
        """
        # Make a copy of the data to avoid modifying the original
        df = data.copy()

        # Initialize signal column
        df['signal'] = 0

        # Analyze market conditions
        market_conditions = self.analyze_market(df)

        # If market conditions are not suitable for grid trading, return without signals
        if not market_conditions.get('is_suitable', False): # Use .get for safety
            self.logger.debug("GridStrategy: Market not suitable, generating no signals.")
            return df

        # Calculate grid levels
        current_price = df['close'].iloc[-1]
        grid_range = self.settings.grid_range # Use settings
        grid_levels = self.settings.grid_levels # Use settings
        min_distance = self.settings.min_level_distance # Use settings

        # Adjust grid range based on support/resistance if available
        if market_conditions.get('has_clear_sr', False): # Use .get for safety
            support = market_conditions.get('support', current_price * (1 - grid_range))
            resistance = market_conditions.get('resistance', current_price * (1 + grid_range))

            # Use support/resistance to define grid boundaries, ensure they make sense
            # Corrected variable names: support -> lower_bound, resistance -> upper_bound
            lower_bound = support
            upper_bound = resistance
            if upper_bound <= lower_bound: # Ensure valid range
                 self.logger.warning(f"GridStrategy: Invalid S/R bounds (S:{support}, R:{resistance}). Falling back to default range.")
                 upper_bound = current_price * (1 + grid_range)
                 lower_bound = current_price * (1 - grid_range)

        else:
            # Use default grid range
            upper_bound = current_price * (1 + grid_range)
            lower_bound = current_price * (1 - grid_range)

        # Ensure minimum range and valid bounds
        min_range_val = current_price * min_distance * 2
        if upper_bound - lower_bound < min_range_val:
             mid_point = (upper_bound + lower_bound) / 2
             upper_bound = mid_point + min_range_val / 2
             lower_bound = mid_point - min_range_val / 2

        if upper_bound <= lower_bound: # Final check for valid range
             self.logger.warning("GridStrategy: Invalid grid bounds calculated after adjustments. Skipping signal generation.")
             return df

        # Calculate grid levels
        # Ensure levels don't exactly equal current price to avoid immediate triggers
        buy_levels = np.linspace(lower_bound, current_price * (1 - min_distance * 0.5), grid_levels)
        sell_levels = np.linspace(current_price * (1 + min_distance * 0.5), upper_bound, grid_levels)
        self.logger.debug(f"Grid Levels: Buy={np.round(buy_levels, 2)}, Sell={np.round(sell_levels, 2)}")

        # Generate signals based on price crossing grid levels
        # Initialize signal based on previous state if available
        if len(df) > 1:
            # Start by carrying over previous signal (important for state)
            df['signal'] = df['signal'].shift(1).fillna(0)

        # Iterate through candles to detect crosses
        for i in range(len(df) - 1):
            price = df['close'].iloc[i]
            next_price = df['close'].iloc[i + 1]
            # Get the signal *before* this candle's potential update
            # If we are at the start (i=0), the previous signal is 0
            prev_signal = df['signal'].iloc[i] if i > 0 else 0

            # Check for buy signals (price crossing downward through buy levels)
            crossed_buy = False
            for level in buy_levels:
                if price > level and next_price <= level:
                    # Set signal for the candle where cross completed
                    df.loc[df.index[i + 1], 'signal'] = 1
                    self.logger.debug(f"Grid Buy Signal at index {i+1}, price {next_price:.2f} crossed level {level:.2f}")
                    crossed_buy = True
                    break # Only one signal per candle

            if crossed_buy: continue # Move to next candle if buy signal generated

            # Check for sell signals (price crossing upward through sell levels)
            crossed_sell = False
            for level in sell_levels:
                if price < level and next_price >= level:
                    # Set signal for the candle where cross completed
                    df.loc[df.index[i + 1], 'signal'] = -1
                    self.logger.debug(f"Grid Sell Signal at index {i+1}, price {next_price:.2f} crossed level {level:.2f}")
                    crossed_sell = True
                    break # Only one signal per candle

            # If no cross occurred, maintain the previous signal for the next candle
            if not crossed_buy and not crossed_sell:
                 # Ensure index i+1 exists before assigning
                 if i + 1 < len(df):
                      # Carry over the signal from candle i to candle i+1
                      df.loc[df.index[i + 1], 'signal'] = prev_signal

        return df

    def calculate_risk_params(self,
                             data: pd.DataFrame,
                             entry_price: float,
                             position_type: str) -> Dict[str, float]:
        """Calculate risk parameters for a grid trade.

        Args:
            data: DataFrame containing OHLCV data
            entry_price: The entry price for the trade
            position_type: 'long' or 'short'

        Returns:
            Dictionary containing stop loss and take profit levels
        """
        # Calculate ATR for dynamic stop loss
        if 'ATR' in data.columns and not data['ATR'].isnull().all():
            atr = data['ATR'].iloc[-1]
            if np.isnan(atr): # Recalculate if last value is NaN
                 # Recalculate using utility function if last value is NaN
                 atr_series = ta.calculate_atr_volatility(data, window=self.settings.atr_periods) # Use settings
                 atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0
        else:
            # Recalculate ATR if not already in dataframe
            # Calculate using utility function if not present in input data
            atr_series = ta.calculate_atr_volatility(data, window=self.settings.atr_periods) # Use settings
            atr = atr_series.iloc[-1] if not atr_series.empty and not np.isnan(atr_series.iloc[-1]) else 0.0

        # Analyze market conditions to adjust risk parameters dynamically
        market_conditions = self.analyze_market(data)

        # Base ATR multiplier from settings
        base_atr_multiplier = self.settings.atr_multiplier

        # Adjust ATR multiplier based on market conditions
        adjusted_atr_multiplier = base_atr_multiplier

        # 1. Adjust based on ADX (trend strength)
        adx = market_conditions.get('adx', 25)
        if adx > 30:  # Strong trend - wider stops
            adjusted_atr_multiplier *= 1.2
        elif adx < 15:  # Very weak trend - tighter stops
            adjusted_atr_multiplier *= 0.8

        # 2. Adjust based on volatility
        volatility_ratio = market_conditions.get('volatility_ratio', 0.01)
        if volatility_ratio > 0.02:  # High volatility - wider stops
            adjusted_atr_multiplier *= 1.3
        elif volatility_ratio < 0.005:  # Low volatility - tighter stops
            adjusted_atr_multiplier *= 0.7

        # 3. Adjust based on support/resistance clarity
        if market_conditions.get('has_clear_sr', False):
            # If we have clear S/R levels, we can use them to optimize SL/TP
            support = market_conditions.get('support', 0)
            resistance = market_conditions.get('resistance', 0)

            # Log the adjustments for debugging
            self.logger.debug(f"Adjusting risk parameters based on market conditions: "
                             f"ADX={adx:.2f}, Volatility={volatility_ratio:.4f}, "
                             f"Base ATR Mult={base_atr_multiplier:.2f}, Adjusted={adjusted_atr_multiplier:.2f}")

        # Calculate dynamic SL/TP levels
        if position_type == 'long':
            # For long positions
            stop_loss = entry_price - (atr * adjusted_atr_multiplier)

            # If we have clear support and it's not too far, use it as a reference
            support = market_conditions.get('support', 0)
            if support > 0 and entry_price - support < entry_price * 0.05:  # Support within 5%
                # Use support as a reference, but ensure minimum distance
                support_based_sl = support * 0.995  # Just below support
                stop_loss = max(stop_loss, support_based_sl)  # Use the higher (safer) of the two

            # Calculate take profit with a dynamic risk-reward ratio
            risk = entry_price - stop_loss
            take_profit = entry_price + (risk * self._calculate_dynamic_rr(market_conditions))
        else:  # short
            # For short positions
            stop_loss = entry_price + (atr * adjusted_atr_multiplier)

            # If we have clear resistance and it's not too far, use it as a reference
            resistance = market_conditions.get('resistance', 0)
            if resistance > 0 and resistance - entry_price < entry_price * 0.05:  # Resistance within 5%
                # Use resistance as a reference, but ensure minimum distance
                resistance_based_sl = resistance * 1.005  # Just above resistance
                stop_loss = min(stop_loss, resistance_based_sl)  # Use the lower (safer) of the two

            # Calculate take profit with a dynamic risk-reward ratio
            risk = stop_loss - entry_price
            take_profit = entry_price - (risk * self._calculate_dynamic_rr(market_conditions))

        # Calculate position size factor based on volatility
        position_size_factor = 1.0 / self.settings.grid_levels
        if volatility_ratio > 0.015:  # Higher volatility - smaller position
            position_size_factor *= 0.8
        elif volatility_ratio < 0.005:  # Lower volatility - larger position
            position_size_factor *= 1.2

        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_reward_ratio': self._calculate_dynamic_rr(market_conditions),
            'position_size_factor': position_size_factor,
            'atr_multiplier_used': adjusted_atr_multiplier  # Store for reference
        }

    def _calculate_dynamic_rr(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate a dynamic risk-reward ratio based on market conditions.

        Args:
            market_conditions: Dictionary containing market analysis results

        Returns:
            Dynamic risk-reward ratio
        """
        # Base risk-reward ratio for grid strategy
        base_rr = 1.0

        # Adjust based on ADX (trend strength)
        adx = market_conditions.get('adx', 25)
        if adx < 15:  # Very ranging market - lower RR
            base_rr *= 0.9
        elif adx > 30:  # Stronger trend - higher RR
            base_rr *= 1.2

        # Adjust based on volatility
        volatility_ratio = market_conditions.get('volatility_ratio', 0.01)
        if volatility_ratio > 0.015:  # Higher volatility - higher potential RR
            base_rr *= 1.1

        # Adjust based on support/resistance clarity
        if market_conditions.get('has_clear_sr', False):
            containment = market_conditions.get('sr_containment', 0)
            if containment > 0.3:  # Well contained - can use higher RR
                base_rr *= 1.1

        # Ensure RR is within reasonable bounds for grid strategy
        return max(0.8, min(1.5, base_rr))

    def calculate_score(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate the strategy score based on current market conditions.

        Args:
            market_conditions: Dictionary containing market conditions

        Returns:
            Strategy score (0-100)
        """
        # Start with base score
        score = 50 # Base score

        # ADX factor: lower is better for grid trading
        adx = market_conditions.get('adx', 25)
        if adx < 20:
            score += 30 * (1 - adx/20)  # Max +30 points when ADX is low
        else:
            score -= min(30, (adx - 20) * 2)  # Penalty for high ADX

        # Volatility factor: medium volatility is ideal
        volatility_ratio = market_conditions.get('volatility_ratio', 0.01)
        if 0.005 < volatility_ratio < 0.015:
            volatility_score = 20 * (1 - abs(volatility_ratio - 0.01) / 0.005)
            score += volatility_score
        else:
            score -= 15 # Penalty for non-ideal volatility

        # Support/Resistance clarity
        if market_conditions.get('has_clear_sr', False):
            score += 15 # Bonus for clear S/R
            # Bonus points for being well-contained between S/R
            containment = market_conditions.get('sr_containment', 0)
            score += containment * 10

        # Volume factor: lower volume is better for grid trading
        volume_ratio = market_conditions.get('volume_ratio', 1.0)
        if volume_ratio < 1.2:
            score += 10 * (1.2 - volume_ratio)

        # Ensure score is between 0 and 100
        return max(0, min(100, score))