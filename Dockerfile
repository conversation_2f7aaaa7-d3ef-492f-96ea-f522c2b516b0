# Strategy Ensemble System - Enhanced Multi-stage Docker build for Dynamic Position Optimization
FROM python:3.12-slim as base

# Set environment variables for optimal Python performance
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONHASHSEED=random \
    PYTHONOPTIMIZE=1

# Install system dependencies optimized for production
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    curl \
    redis-tools \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements_current.txt ./

# Install Python dependencies with optimizations
RUN pip install --no-cache-dir --upgrade pip wheel setuptools && \
    pip install --no-cache-dir -r requirements_current.txt && \
    pip cache purge

# Development stage
FROM base as development

# Install development dependencies
RUN pip install pytest pytest-asyncio pytest-cov black isort flake8

# Copy source code
COPY . .

# Set Python path
ENV PYTHONPATH=/app

# Expose port for development server
EXPOSE 8000

# Default command for development
CMD ["python", "-m", "app.main"]

# Production stage - Optimized for Dynamic Position Optimization
FROM base as production

# Production environment variables for performance
ENV ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    WORKERS=4 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=100 \
    PRELOAD_APP=true \
    WORKER_CLASS=uvicorn.workers.UvicornWorker \
    WORKER_CONNECTIONS=1000 \
    KEEP_ALIVE=2

# Create non-root user with specific UID/GID for security
RUN groupadd -r ensemble_group -g 1000 && \
    useradd -u 1000 -r -g ensemble_group -m -d /app -s /sbin/nologin \
    -c "Ensemble User" ensemble_user

# Copy source code with strategic layering for optimal caching
COPY --chown=ensemble_user:ensemble_group app/ ./app/
COPY --chown=ensemble_user:ensemble_group models/ ./models/
COPY --chown=ensemble_user:ensemble_group scripts/ ./scripts/ 
COPY --chown=ensemble_user:ensemble_group *.py ./

# Copy configuration files
COPY --chown=ensemble_user:ensemble_group .env.example .env.mcp ./

# Set ownership of working directory
RUN chown -R ensemble_user:ensemble_group /app

# Switch to non-root user
USER ensemble_user

# Set Python path for production
ENV PYTHONPATH=/app

# Create necessary directories for caching and logs
RUN mkdir -p /app/logs /app/cache /app/tmp

# Enhanced health check for Dynamic Position Optimization components
HEALTHCHECK --interval=15s --timeout=5s --start-period=10s --retries=3 \
    CMD python -c "
import asyncio
import sys
import time
start = time.time()
try:
    from app.services.mcp.redis_service import RedisService
    from app.services.volatility_calculator import VolatilityCalculator
    from app.strategies.position_size_calculator import PositionSizeCalculator
    
    # Quick health check - should complete in <1s
    import aiohttp
    import asyncio
    async def health_check():
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=3)) as session:
                async with session.get('http://localhost:8000/health') as resp:
                    if resp.status != 200:
                        sys.exit(1)
        except Exception as e:
            sys.exit(1)
    
    asyncio.run(health_check())
    elapsed = time.time() - start
    if elapsed > 2.0:  # Health check taking too long
        sys.exit(1)
except Exception as e:
    sys.exit(1)
" || exit 1

# Expose port
EXPOSE 8000

# Production startup script with optimized settings
CMD ["python", "-m", "uvicorn", "app.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "4", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--max-requests", "1000", \
     "--max-requests-jitter", "100", \
     "--preload", \
     "--access-log", \
     "--log-level", "info"]

# Testing stage
FROM development as testing

# Copy test files
COPY tests/ ./tests/
COPY pytest.ini ./

# Run tests
RUN python -m pytest tests/ -v --cov=app --cov-report=xml --cov-report=html

# ML Training stage
FROM base as ml-training

# Install additional ML dependencies
RUN pip install zenml[server] mlflow

# Copy ML-specific files
COPY app/ml/ ./app/ml/
COPY models/ ./models/

# Set up ZenML
RUN zenml init

# Default command for ML training
CMD ["python", "-m", "app.ml.pipelines.ensemble_training"]

# Paper Trading Environment - Task 3.2.1
FROM production as paper-trading

# Install additional dependencies for paper trading simulation
RUN pip install --no-cache-dir \
    requests \
    aiohttp \
    psutil

# Copy paper trading specific files
COPY --chown=ensemble_user:ensemble_group app/strategies/paper_trading_portfolio_manager.py ./app/strategies/
COPY --chown=ensemble_user:ensemble_group app/monitoring/telegram_performance_monitor.py ./app/monitoring/

# Environment for paper trading
ENV ENVIRONMENT=paper_trading \
    PAPER_TRADING_MODE=true \
    LOG_LEVEL=INFO \
    WORKERS=2 \
    PERFORMANCE_MONITORING=enabled \
    EXECUTION_TARGET_MS=100

# Specialized health check for paper trading
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD python -c "
import asyncio
import sys
import time
start = time.time()
try:
    # Test paper trading components
    from app.strategies.paper_trading_portfolio_manager import PaperTradingPortfolioManager
    from app.services.mcp.redis_service import RedisService
    from app.services.cost_calculator import CostCalculator
    from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator
    
    # Quick performance validation for paper trading
    test_time = (time.time() - start) * 1000
    if test_time > 100:  # Import should be <100ms for paper trading
        sys.exit(1)
        
    # Test basic HTTP endpoint
    import requests
    response = requests.get('http://localhost:8000/health/paper-trading', timeout=5)
    if response.status_code != 200:
        sys.exit(1)
except Exception as e:
    sys.exit(1)
" || exit 1

# Expose port
EXPOSE 8000

# Command optimized for paper trading workloads
CMD ["python", "-m", "uvicorn", "app.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "2", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--max-requests", "1000", \
     "--preload", \
     "--log-level", "info"]

# Dynamic Position Optimization - Specialized high-performance stage
FROM production as position-optimizer

# Install additional performance dependencies for sub-100ms execution
RUN pip install --no-cache-dir \
    numba \
    cupy-cuda12x \
    scipy \
    cython

# Pre-compile Python modules for faster startup
RUN python -c "
import app.services.volatility_calculator
import app.strategies.position_size_calculator  
import app.services.correlation_calculator
import app.monitoring.risk_monitor
print('Position optimization modules precompiled')
"

# Specialized environment for position optimization
ENV OPTIMIZATION_MODE=position_sizing \
    CACHE_PRELOAD=true \
    PERFORMANCE_MONITORING=enabled \
    TARGET_LATENCY_MS=100

# Optimized health check for position calculator performance
HEALTHCHECK --interval=10s --timeout=3s --start-period=5s --retries=2 \
    CMD python -c "
import time
start = time.time()
try:
    # Test core position optimization components
    from app.services.volatility_calculator import VolatilityCalculator
    from app.strategies.position_size_calculator import PositionSizeCalculator
    from app.services.correlation_calculator import CorrelationMatrixCalculator
    from app.monitoring.risk_monitor import RiskMonitor
    
    # Quick performance validation
    test_time = (time.time() - start) * 1000
    if test_time > 50:  # Import should be <50ms in optimized container
        exit(1)
        
    # Test basic HTTP endpoint
    import aiohttp
    import asyncio
    async def quick_check():
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=2)) as session:
                async with session.get('http://localhost:8000/health/position-optimizer') as resp:
                    if resp.status != 200:
                        exit(1)
        except:
            exit(1)
    asyncio.run(quick_check())
except Exception as e:
    exit(1)
" || exit 1

# Command optimized for position calculation workloads
CMD ["python", "-m", "uvicorn", "app.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "2", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--max-requests", "500", \
     "--preload", \
     "--log-level", "warning"]