---
description: 
globs: 
alwaysApply: true
---

# Cascade AI Assistant - Memory Bank & Context Management Protocol
# Version 2.0

## I. Memory Bank Initialization
- **Mandatory Reading**: At the start of EVERY session or task, you MUST read ALL memory bank files. This is not optional.
- **Location**: Check for a `memory-bank` folder in the project root directory (`C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/`).
- **Core Files First**: If the folder exists, read ALL files, prioritizing core files such as:
    1. `projectbrief.md`
    2. `productContext.md` (if exists)
    3. `systemPatterns.md` (if exists)
    4. `techContext.md` (if exists)
    5. `progress.md` (if exists)
    6. `activeContext.md` (if exists)
- **Subdirectories**: Read ALL files in `memory-bank` subdirectories like `history/`, `plans/`, `schema/`, etc.
- **Missing Memory Bank**: If the `memory-bank` folder does NOT exist, NOTIFY the user and recommend creating one.
- **Critical Context**: Memory files provide CRITICAL context. Failure to read them will lead to suboptimal performance and redundant questions.

## II. Memory Creation & Updates (`create_memory` / `mcp0_create-memories`)
- **Proactive Storage**: Use memory creation tools to store all critical information, including but not limited to:
    - Project patterns, architectural decisions, technical choices, and their rationales.
    - User preferences, explicit instructions to remember something, or feedback that should alter future behavior.
    - Important code snippets, configurations, and environment details.
    - Major milestones, feature definitions, and task breakdowns.
    - Successful task completions, their outcomes, and any lessons learned.
    - Summaries of complex discussions or decisions.
- **Memory Bank Document Updates**: After successful tasks or significant findings, always update all relevant documents in the `@memory-bank` folder (`C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/`). This includes, but is not limited to:
    - Task Checklist updates.
    - Documentation of changes made or features implemented.
    - Notes on new insights or important information discovered.
- **Relevance Scoring (RS) for Memories (Conceptual)**:
  - When creating memories, implicitly or explicitly consider their importance.
  - [RS:5]: Critical, must-know information (active tasks, core project goals, critical constraints).
  - [RS:4]: High importance (key decisions, current context, user preferences).
  - [RS:3]: Moderate importance (general background, established patterns).
  - [RS:2]: Background information (historical context, past decisions).
  - [RS:1]: Peripheral information (minor details, dated information).
  - Prioritize higher-scored memories when context space is limited.

## III. Memory Search & Retrieval (`search-memories` / `mcp0_search-memories`)
- **Pre-Task Context Gathering**: ALWAYS use memory search tools to retrieve relevant context BEFORE starting any new task, answering complex questions, or making significant decisions. This helps avoid re-asking for information and ensures actions are based on existing knowledge.
- **Query Formulation**: Formulate search queries effectively to retrieve the most pertinent information for the task at hand.

## IV. Session Continuity
- **End-of-Session Marker**: At the end of each work session, or before a potentially long pause, provide a "CONTINUE_FROM" marker.
- **Summary of State**: Briefly summarize what was being worked on, the current mode, and any pending decisions or questions.
- **Next Steps**: Clearly list the immediate next steps planned for the continuation of the task or the start of the next session.

## V. Memory Bank Structure (Recommended - to be managed by User)
- A well-organized `memory-bank` enhances context retrieval. The following structure is a suggestion:
  - `C:/Users/<USER>/Documents/Programming/Crypto_App_V2/memory-bank/`
    - `core\` (Project Brief, Tech Context, System Patterns, Product Context, Progress, Active Context)
    - `history\` (Chronological log of significant events, past decisions if not in core docs)
    - `activeContext\` (Focused, short-term context for the very immediate task)
    - `docs\` (PRDs, specifications, external documentation copies)
    - `protocols\` (Detailed operational protocols, like full RIPER-5 if separated)
    - `plans\` (Saved implementation checklists from PLAN mode)
    - `schema\` (Database schemas, data model diagrams)

# (This document outlines the protocol for effective memory and context management with Cascade.)
