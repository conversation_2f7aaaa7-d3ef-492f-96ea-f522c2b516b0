# Cascade AI Context Snapshot: [Project Name] - YYYY-MM-DD

## 1. Project Goal
- (Brief summary of the main objective as understood by Cascade)

## 2. Current Task Assigned to Cascade
- (Specific task Cascade is currently working on or has just completed)
- **Status**: (e.g., In Progress, Blocked, Planning, Awaiting Feedback, Completed)

## 3. Key Understandings by <PERSON>
- (Bullet points of critical information Cascade has processed and retained relevant to the project/task)
  - About the codebase structure/logic:
  - About user preferences/intent:
  - About project constraints/requirements:

## 4. Recent Significant Actions by Cascade
- (Last 2-3 major tool uses, analyses, or code modifications performed by Cascade)
  - Action 1:
  - Action 2:

## 5. Pending Questions from Cascade for User
- (Specific questions <PERSON> has for the user to clarify requirements or unblock progress)
  - Question 1:
  - Question 2:

## 6. Next Planned Actions by Cascade
- (Immediate next steps Cascade intends to take based on current understanding and plan)
  - Step 1:
  - Step 2:
